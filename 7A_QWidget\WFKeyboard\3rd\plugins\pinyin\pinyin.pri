#INCLUDEPATH +=  $$PWD/Plugins/pinyin/3rdparty/pinyin/include
#DEPENDPATH  += $$PWD/Plugins/pinyin/3rdparty/pinyin/include

SOURCES += \
    $$PWD/3rdparty/pinyin/share/dictbuilder.cpp \
    $$PWD/3rdparty/pinyin/share/dictlist.cpp \
    $$PWD/3rdparty/pinyin/share/dicttrie.cpp \
    $$PWD/3rdparty/pinyin/share/lpicache.cpp \
    $$PWD/3rdparty/pinyin/share/matrixsearch.cpp \
    $$PWD/3rdparty/pinyin/share/mystdlib.cpp \
    $$PWD/3rdparty/pinyin/share/ngram.cpp \
    $$PWD/3rdparty/pinyin/share/pinyinime.cpp \
    $$PWD/3rdparty/pinyin/share/searchutility.cpp \
    $$PWD/3rdparty/pinyin/share/spellingtable.cpp \
    $$PWD/3rdparty/pinyin/share/spellingtrie.cpp \
    $$PWD/3rdparty/pinyin/share/splparser.cpp \
    $$PWD/3rdparty/pinyin/share/sync.cpp \
    $$PWD/3rdparty/pinyin/share/userdict.cpp \
    $$PWD/3rdparty/pinyin/share/utf16char.cpp \
    $$PWD/3rdparty/pinyin/share/utf16reader.cpp \
    $$PWD/pinyindecoderservice.cpp \
    $$PWD/pinyininputmethod.cpp

HEADERS += \
    $$PWD/3rdparty/pinyin/include/atomdictbase.h \
    $$PWD/3rdparty/pinyin/include/dictbuilder.h \
    $$PWD/3rdparty/pinyin/include/dictdef.h \
    $$PWD/3rdparty/pinyin/include/dictlist.h \
    $$PWD/3rdparty/pinyin/include/dicttrie.h \
    $$PWD/3rdparty/pinyin/include/lpicache.h \
    $$PWD/3rdparty/pinyin/include/matrixsearch.h \
    $$PWD/3rdparty/pinyin/include/mystdlib.h \
    $$PWD/3rdparty/pinyin/include/ngram.h \
    $$PWD/3rdparty/pinyin/include/pinyinime.h \
    $$PWD/3rdparty/pinyin/include/searchutility.h \
    $$PWD/3rdparty/pinyin/include/spellingtable.h \
    $$PWD/3rdparty/pinyin/include/spellingtrie.h \
    $$PWD/3rdparty/pinyin/include/splparser.h \
    $$PWD/3rdparty/pinyin/include/sync.h \
    $$PWD/3rdparty/pinyin/include/userdict.h \
    $$PWD/3rdparty/pinyin/include/utf16char.h \
    $$PWD/3rdparty/pinyin/include/utf16reader.h \
    $$PWD/pinyindecoderservice_p.h \
    $$PWD/pinyininputmethod_p.h

RESOURCES += \
    $$PWD/dictdata.qrc


