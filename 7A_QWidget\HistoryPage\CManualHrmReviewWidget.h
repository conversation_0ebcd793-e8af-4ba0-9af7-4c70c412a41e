#ifndef CMANUALHRMREVIEWWIDGET_H
#define CMANUALHRMREVIEWWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: chenhao
  * Date: 2024-10-14
  * Description: 人工审核窗口
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QComboBox>
#include <QHash>
#include "CLineEdit.h"
#include <QPushButton>
#include <QMessageBox>
#include <QEventLoop>
#include "CHLabelTitleWidget.h"
#include "PublicParams.h"
#include "CLineEditSpinBox.h"
#include "CLineTwoEdit.h"
#include "include/cmeltingcalclib.h"

struct stHrmReviewParam
{
    stHrmReviewParam()
    {
        clear();
    };
    // 拷贝构造函数
    stHrmReviewParam(const stHrmReviewParam& other)
    {
        m_strName = other.m_strName;
        m_strMeltingInfo = other.m_strMeltingInfo;
        m_strMeltingInfo_Review = other.m_strMeltingInfo_Review;
        m_result = other.m_result;
        m_index = other.m_index;
        m_bReview = other.m_bReview;
        m_bNull = other.m_bNull;
        m_fWildTmValue = other.m_fWildTmValue;
        m_fRange = other.m_fRange;
        m_bControl = other.m_bControl;
    }

    // 赋值运算符重载
    stHrmReviewParam& operator=(const stHrmReviewParam& other)
    {
        if (this != &other) // 自我赋值检查
        {
            m_strName = other.m_strName;
            m_strMeltingInfo = other.m_strMeltingInfo;
            m_strMeltingInfo_Review = other.m_strMeltingInfo_Review;
            m_result = other.m_result;
            m_index = other.m_index;
            m_bReview = other.m_bReview;
            m_bNull = other.m_bNull;
            m_fWildTmValue = other.m_fWildTmValue;
            m_fRange = other.m_fRange;
            m_bControl = other.m_bControl;
        }
        return *this; // 返回自身引用
    }

    void clear()
    {
        m_strName.clear();
        m_strMeltingInfo.clear();
        m_strMeltingInfo_Review.clear();
        m_result.clear();
        m_fWildTmValue = 0;
        m_fRange = 0;
        m_index = -1;
        m_bReview = false;
        m_bNull = false;
        m_bControl = false;
    };
    QString m_strName;
    QString m_strMeltingInfo;
    QString m_strMeltingInfo_Review;
    QString m_result;
    float  m_fWildTmValue;
    float  m_fRange;
    int m_index;
    bool m_bReview;
    bool m_bNull;
    bool m_bControl;
};

class CManualHrmReviewWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CManualHrmReviewWidget(QWidget *parent = nullptr);
    void SetManualReviewParam(const SResultInfoStruct& sResultInfo,const SLotInfoStruct& sLotInfo,const SCardInfoStruct &sCardInfo,const QString& strTextName);
    void GetManualReviewParam(SResultInfoStruct& sResultInfo, SLotInfoStruct& sLotInfo);

    QMessageBox::StandardButton GetClickedBtn() const;

signals:
    void SignalReviewConfirm(stHrmReviewParam stReviewParam);

public slots:
    void _SlotOnHoleNameChanged(int index);
    void _SlotAutoThreshouldBtn();
    void _SlotManualCalcBtn();
    void _SlotCancelBtn();
    void _SlotConfirmBtn();


protected:
    void paintEvent(QPaintEvent* pEvent) override;
    void showEvent(QShowEvent *pEvent) override;

private:
    void _InitWidget();
    void _InitLayout();
    void _InitParamValue(const SResultInfoStruct& sRestltInfo,const SLotInfoStruct& sLotInfo);
    void _SetParamResultValue();
    void _ClearData();
    void _SetParamValue(int index);
    void _SetComboBoxValue();
    QString _GetShowResult(const QString& strResult);
private:

    CMeltingCalcLib m_MeltingCalc;
    SResultInfoStruct m_sResultInfo;
    SCardInfoStruct m_sCardInfo;
    SLotInfoStruct m_sLotInfo;
    int m_index{-1};
    QString m_strResult;
    QString m_strMeltingInfo;

    QList<stHrmReviewParam> m_ReviewParamList;

    QEventLoop m_qEventLoop;
    QMessageBox::StandardButton m_eStandardBtn;

    QLabel *m_pBackgroundLabel;
    CHLabelTitleWidget *m_pCHLabelTitleWidget;
    QLabel *m_pHoleNameLabel;
    QComboBox *m_pHoleNameCombo;

    QLabel *m_pThreshouldLabel,*m_pTmRmFirstLabel,*m_pTmRmSecondLabel;
    CLineEditSpinBox * m_pThreshouldEdit;
    // 结果列
    CLineEdit* m_pTmFirstValueEdit,*m_pRmFirstValueEdit;
    CLineEdit* m_pTmSecondValueEdit,*m_pRmSecondValueEdit;

    QLabel *m_pRangeLabel,*m_pResultLabel;
    CLineEdit* m_pRangeEdit,*m_pResultEdit;// 手动计算的有结果，没人工审核的不填上去；

    QPushButton *m_pAutoThreshouldBtn;
    QPushButton *m_pManualCalcBtn;
    const QString m_strPositive, m_strNegative, m_strError, m_strNull;
    QPushButton *m_pCancelBtn, *m_pConfirmBtn;
};

#endif // CHISTORYPAGE_H
