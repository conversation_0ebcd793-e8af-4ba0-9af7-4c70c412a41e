#include "CMotorCalibrate.h"
#include <QBoxLayout>
#include <QGridLayout>
#include <QTimer>
#include "CMessageBox.h"
#include "DBControl/CMotorInfoDB.h"

#define FL_MOTOR_CALIBRATE_RESOLUTION  450
#define FL_MOTOR_HOLE1_BLUE_ANGLE_COUNT     (36000/FL_MOTOR_CALIBRATE_RESOLUTION)
#define FL_MOTOR_HOLE1_GREEN_ANGLE_COUNT    (76000/FL_MOTOR_CALIBRATE_RESOLUTION)
#define FL_MOTOR_HOLE1_YELLOW_ANGLE_COUNT   (116000/FL_MOTOR_CALIBRATE_RESOLUTION)
#define FL_MOTOR_HOLE1_RED_ANGLE_COUNT      (180000/FL_MOTOR_CALIBRATE_RESOLUTION)
#define FL_MOTOR_HOLE2_BLUE_ANGLE_COUNT     (220000/FL_MOTOR_CALIBRATE_RESOLUTION)
#define FL_MOTOR_HOLE2_GREEN_ANGLE_COUNT    (260000/FL_MOTOR_CALIBRATE_RESOLUTION)
#define FL_MOTOR_HOLE2_YELLOW_ANGLE_COUNT   (300000/FL_MOTOR_CALIBRATE_RESOLUTION)
#define FL_MOTOR_HOLE2_RED_ANGLE_COUNT      (340000/FL_MOTOR_CALIBRATE_RESOLUTION)

CMotorCalibrate::CMotorCalibrate(QWidget *parent) : QWidget(parent)
{

    Register2Map(Method_OFSTRST);
    Register2Map(Method_FLLED);
    Register2Map(Method_PCRSTRST);
    Register2Map(Method_PCRPR);
    Register2Map(Method_MOVE);
    Register2Map(Method_FLADC);
    _InitWidget();
    _calibrateState = CALIBRATE_IDLE;

    m_Timer = new QTimer(this);

    connect(m_Timer, &QTimer::timeout, this, &CMotorCalibrate::_FLMotorCalibrateHandle);
    m_Timer->start(20); // 每秒更新一次
}

CMotorCalibrate::~CMotorCalibrate()
{
    UnRegister2Map(Method_OFSTRST);
    UnRegister2Map(Method_PCRSTRST);
    UnRegister2Map(Method_FLLED);
    UnRegister2Map(Method_PCRPR);
    UnRegister2Map(Method_MOVE);
    UnRegister2Map(Method_FLADC);
    m_Timer->stop();
    delete m_Timer;
}

void CMotorCalibrate::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    if (_calibrateState == CALIBRATE_WAIT_JSON_REPLY)
    {
        if (iResult == 1)
        {
            ShowError(this, m_strTipsText, tr("%1#光学电机校准失败").arg(iMachineID + 1));
            _calibrateState = CALIBRATE_IDLE;
            return;
        }

        if(Method_OFSTRST == iMethodID)
        {
            _calibrateState = CALIBRATE_PCR_MOTOR_PRESS;
        }
        else if(Method_PCRPR == iMethodID)
        {
            _calibrateState = CALIBRATE_LED_ON;
        }
        else if(Method_FLLED == iMethodID)
        {
            if (_flCalibrateCtrl.index == 0)
            {
                _calibrateState = CALIBRATE_FL_MOVE;
            }
            else
            {
                _calibrateState = CALIBRATE_PCR_MOTOR_RESET;
            }
        }
        else if(Method_MOVE == iMethodID)
        {
            _calibrateState = CALIBRATE_SEND_FLADC;
        }
        else if(Method_FLADC == iMethodID)
        {
            QVariantList qVarList = qVarData.toList();
            _flCalibrateCtrl.flValueVector.append(qVarList.at(0).toDouble());
            _flCalibrateCtrl.index++;
            if (_flCalibrateCtrl.index == FL_MOTOR_HOLE1_BLUE_ANGLE_COUNT)
            {
                auto max_iter = std::max_element(_flCalibrateCtrl.flValueVector.begin(), _flCalibrateCtrl.flValueVector.end());
                double max_value = *max_iter;
                size_t max_index = std::distance(_flCalibrateCtrl.flValueVector.begin(), max_iter);

                _calibrateState = CALIBRATE_FL_MOVE;
                QString strLog = QString("孔1最大蓝光值: %1, 位置: %2, 步数：%3").arg(max_value).arg(max_index + 1).arg((max_index+1)*FL_MOTOR_CALIBRATE_RESOLUTION);
                m_pTextBrowser->AppendLog(strLog);
                _flCalibrateCtrl.flValueVector.clear();
            }
            else if (_flCalibrateCtrl.index == FL_MOTOR_HOLE1_GREEN_ANGLE_COUNT)
            {
                auto max_iter = std::max_element(_flCalibrateCtrl.flValueVector.begin(), _flCalibrateCtrl.flValueVector.end());
                double max_value = *max_iter;
                size_t max_index = std::distance(_flCalibrateCtrl.flValueVector.begin(), max_iter);

                _calibrateState = CALIBRATE_FL_MOVE;
                QString strLog = QString("孔1最大绿光值: %1, 位置: %2, 步数：%3").arg(max_value).arg(max_index + FL_MOTOR_HOLE1_BLUE_ANGLE_COUNT + 1).arg((max_index + FL_MOTOR_HOLE1_BLUE_ANGLE_COUNT + 1)*FL_MOTOR_CALIBRATE_RESOLUTION);
                m_pTextBrowser->AppendLog(strLog);
                _flCalibrateCtrl.flValueVector.clear();
            }
            else if (_flCalibrateCtrl.index == FL_MOTOR_HOLE1_YELLOW_ANGLE_COUNT)
            {
                auto max_iter = std::max_element(_flCalibrateCtrl.flValueVector.begin(), _flCalibrateCtrl.flValueVector.end());
                double max_value = *max_iter;
                size_t max_index = std::distance(_flCalibrateCtrl.flValueVector.begin(), max_iter);

                _calibrateState = CALIBRATE_FL_MOVE;
                QString strLog = QString("孔1最大黄光值: %1, 位置: %2, 步数：%3").arg(max_value).arg(max_index + FL_MOTOR_HOLE1_GREEN_ANGLE_COUNT + 1).arg((max_index+FL_MOTOR_HOLE1_GREEN_ANGLE_COUNT+1)*FL_MOTOR_CALIBRATE_RESOLUTION);
                m_pTextBrowser->AppendLog(strLog);
                _flCalibrateCtrl.flValueVector.clear();
            }
            else if (_flCalibrateCtrl.index == FL_MOTOR_HOLE1_RED_ANGLE_COUNT)
            {
                auto max_iter = std::max_element(_flCalibrateCtrl.flValueVector.begin(), _flCalibrateCtrl.flValueVector.end());
                double max_value = *max_iter;
                size_t max_index = std::distance(_flCalibrateCtrl.flValueVector.begin(), max_iter);

                _calibrateState = CALIBRATE_FL_MOVE;
                QString strLog = QString("孔1最大红光值: %1, 位置: %2, 步数：%3").arg(max_value).arg(max_index + FL_MOTOR_HOLE1_YELLOW_ANGLE_COUNT+1).arg((max_index+FL_MOTOR_HOLE1_YELLOW_ANGLE_COUNT+1)*FL_MOTOR_CALIBRATE_RESOLUTION);
                m_pTextBrowser->AppendLog(strLog);
                _flCalibrateCtrl.flValueVector.clear();
            }
            else if (_flCalibrateCtrl.index == FL_MOTOR_HOLE2_BLUE_ANGLE_COUNT)
            {
                auto max_iter = std::max_element(_flCalibrateCtrl.flValueVector.begin(), _flCalibrateCtrl.flValueVector.end());
                double max_value = *max_iter;
                size_t max_index = std::distance(_flCalibrateCtrl.flValueVector.begin(), max_iter);

                _calibrateState = CALIBRATE_FL_MOVE;
                QString strLog = QString("孔2最大蓝光值: %1, 位置: %2, 步数：%3").arg(max_value).arg(max_index + FL_MOTOR_HOLE1_RED_ANGLE_COUNT+1).arg((max_index+FL_MOTOR_HOLE1_RED_ANGLE_COUNT+1)*FL_MOTOR_CALIBRATE_RESOLUTION);
                m_pTextBrowser->AppendLog(strLog);
                _flCalibrateCtrl.flValueVector.clear();
            }
            else if (_flCalibrateCtrl.index == FL_MOTOR_HOLE2_GREEN_ANGLE_COUNT)
            {
                auto max_iter = std::max_element(_flCalibrateCtrl.flValueVector.begin(), _flCalibrateCtrl.flValueVector.end());
                double max_value = *max_iter;
                size_t max_index = std::distance(_flCalibrateCtrl.flValueVector.begin(), max_iter);

                _calibrateState = CALIBRATE_FL_MOVE;
                QString strLog = QString("孔2最大绿光值: %1, 位置: %2, 步数：%3").arg(max_value).arg(max_index + FL_MOTOR_HOLE2_BLUE_ANGLE_COUNT+1).arg((max_index+FL_MOTOR_HOLE2_BLUE_ANGLE_COUNT+1)*FL_MOTOR_CALIBRATE_RESOLUTION);
                m_pTextBrowser->AppendLog(strLog);
                _flCalibrateCtrl.flValueVector.clear();
            }
            else if (_flCalibrateCtrl.index == FL_MOTOR_HOLE2_YELLOW_ANGLE_COUNT)
            {
                auto max_iter = std::max_element(_flCalibrateCtrl.flValueVector.begin(), _flCalibrateCtrl.flValueVector.end());
                double max_value = *max_iter;
                size_t max_index = std::distance(_flCalibrateCtrl.flValueVector.begin(), max_iter);

                _calibrateState = CALIBRATE_FL_MOVE;
                QString strLog = QString("孔2最大黄光值: %1, 位置: %2, 步数：%3").arg(max_value).arg(max_index + FL_MOTOR_HOLE2_GREEN_ANGLE_COUNT+1).arg((max_index+FL_MOTOR_HOLE2_GREEN_ANGLE_COUNT+1)*FL_MOTOR_CALIBRATE_RESOLUTION);
                m_pTextBrowser->AppendLog(strLog);
                _flCalibrateCtrl.flValueVector.clear();
            }
            else if (_flCalibrateCtrl.index == FL_MOTOR_HOLE2_RED_ANGLE_COUNT)
            {
                auto max_iter = std::max_element(_flCalibrateCtrl.flValueVector.begin(), _flCalibrateCtrl.flValueVector.end());
                double max_value = *max_iter;
                size_t max_index = std::distance(_flCalibrateCtrl.flValueVector.begin(), max_iter);

                _calibrateState = CALIBRATE_LEN_OFF;
                QString strLog = QString("孔2最大红光值: %1, 位置: %2, 步数：%3").arg(max_value).arg(max_index + FL_MOTOR_HOLE2_YELLOW_ANGLE_COUNT+1).arg((max_index+FL_MOTOR_HOLE2_YELLOW_ANGLE_COUNT+1)*FL_MOTOR_CALIBRATE_RESOLUTION);
                m_pTextBrowser->AppendLog(strLog);
            }
            else
            {
                _calibrateState = CALIBRATE_FL_MOVE;
                QString strLog = QString("MOVE:%1,FLADC:%2").arg(_flCalibrateCtrl.index).arg(qVarList.at(0).toDouble());
                m_pTextBrowser->AppendLog(strLog);
            }

        }
        else if (Method_PCRSTRST == iMethodID) {
            _calibrateState = CALIBRATE_IDLE;
            ShowSuccess(this, m_strTipsText, tr("%1#光学电机校准成功").arg(iMachineID + 1));
        }
    }

}

void CMotorCalibrate::_SlotFLMotorCalibrateBtn()
{
    if (_calibrateState == CALIBRATE_IDLE)
    {
        _calibrateState = CALIBRATE_FL_MOTOR_RESET;
    }
}

void CMotorCalibrate::_FLMotorCalibrateHandle()
{
    QString strCmd;

    switch (_calibrateState) {
        case CALIBRATE_FL_MOTOR_RESET:
            strCmd = GetJsonCmdString(Method_OFSTRST);
            SendJsonCmd(m_pMachineComboBox->GetCurrentIndex(), Method_OFSTRST, strCmd);
            _calibrateState = CALIBRATE_WAIT_JSON_REPLY;
            _flCalibrateCtrl.flValueVector.clear();
            _flCalibrateCtrl.index = 0;
            break;
        case CALIBRATE_PCR_MOTOR_PRESS:
            strCmd = GetJsonCmdString(Method_PCRPR);
            SendJsonCmd(m_pMachineComboBox->GetCurrentIndex(), Method_PCRPR, strCmd);
            _calibrateState = CALIBRATE_WAIT_JSON_REPLY;
            break;
        case CALIBRATE_LED_ON:
        {
            QVariantList varListParam = {3};
            strCmd = GetJsonCmdString(Method_FLLED, varListParam);
            SendJsonCmd(m_pMachineComboBox->GetCurrentIndex(), Method_FLLED, strCmd);
            _calibrateState = CALIBRATE_WAIT_JSON_REPLY;
            break;
        }
        case CALIBRATE_FL_MOVE:
        {
            QVariantList varListParam = {0, 0, FL_MOTOR_CALIBRATE_RESOLUTION};
            strCmd = GetJsonCmdString(Method_MOVE, varListParam);
            SendJsonCmd(m_pMachineComboBox->GetCurrentIndex(), Method_MOVE, strCmd);
            _calibrateState = CALIBRATE_WAIT_JSON_REPLY;
            break;
        }
        case CALIBRATE_SEND_FLADC:
        {
            if (_flCalibrateCtrl.index < 400)
            {
                QVariantList varListParam = {0};
                strCmd = GetJsonCmdString(Method_FLADC, varListParam);
            }
            else
            {
                QVariantList varListParam = {1};
                strCmd = GetJsonCmdString(Method_FLADC, varListParam);
            }
            SendJsonCmd(m_pMachineComboBox->GetCurrentIndex(), Method_FLADC, strCmd);
            _calibrateState = CALIBRATE_WAIT_JSON_REPLY;
            break;
        }
        case CALIBRATE_LEN_OFF:
        {
            QVariantList varListParam = {0};
            strCmd = GetJsonCmdString(Method_FLLED, varListParam);
            SendJsonCmd(m_pMachineComboBox->GetCurrentIndex(), Method_FLLED, strCmd);
            _calibrateState = CALIBRATE_WAIT_JSON_REPLY;
            break;
        }
        case CALIBRATE_PCR_MOTOR_RESET:
        {
            strCmd = GetJsonCmdString(Method_PCRSTRST);
            SendJsonCmd(m_pMachineComboBox->GetCurrentIndex(), Method_PCRSTRST, strCmd);
            _calibrateState = CALIBRATE_WAIT_JSON_REPLY;
            break;
        }
        default:
            break;
    }

}


void CMotorCalibrate::_InitWidget()
{
    m_pMachineComboBox = new CLabelComboBox(tr("机器:"));
    m_pMachineComboBox->SetComboBoxList(gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(80, 50);

    m_pFLMotorCalibrateButton = new QPushButton(tr("光学电机校准"));
    m_pFLMotorCalibrateButton->setFixedSize(220, 50);
    connect(m_pFLMotorCalibrateButton, &QPushButton::clicked, this, &CMotorCalibrate::_SlotFLMotorCalibrateBtn);

    m_pTextBrowser = new CTextBrowser;

    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(20);
    pTopLayout->addWidget(m_pMachineComboBox);
    pTopLayout->addWidget(m_pFLMotorCalibrateButton);
    pTopLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addLayout(pTopLayout);
    pLayout->addWidget(m_pTextBrowser);
    this->setLayout(pLayout);
}
