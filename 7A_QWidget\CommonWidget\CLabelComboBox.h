#ifndef CLABELCOMBOBOX_H
#define CLABELCOMBOBOX_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-10-26
  * Description: QLabel-QComboxBox组合控件
  * -------------------------------------------------------------------------
  * History: 2024-06-28 hxr 添加垂直布局和水平布局
  * 由于早期工厂模式已使用此类，部分接口保留
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QListView>
#include <QComboBox>
#include <QBoxLayout>

class CLabelComboBox : public QWidget
{
    Q_OBJECT
public:
    CLabelComboBox(const QString &strText, const QStringList &strList = QStringList(), int iSpacing = 5,
                   QBoxLayout::Direction eDirection = QBoxLayout::LeftToRight, QWidget *parent = nullptr);
    virtual ~CLabelComboBox();

public:
    void ResetLabelSize(int iWidth, int iHeight);
    void ResetComboBoxSize(int iWidth, int iHeight);

    void SetComboBoxList(const QStringList &strList);
    void SetLabelFixedSize(int iWidth, int iHeight);
    void SetComboBoxMinSize(int iWidth, int iHeight);
    void SetComboBoxFixedSize(int iWidth, int iHeight);
    void SetLineEditEnabled(bool bEdit);
    void SetLineEditQss(const QString &strQss);

    void SetMaxVisibleItems(int iItems);

public:
    int GetCurrentIndex() const;
    void SetCurrentIndex(int index);
    QString GetCurrentText() const;
    void SetCurrentText(QString strText);

signals:
    void SignalCurrentIndexChanged(int index);
    void SignalCurrentTextChanged(const QString &);

private slots:
    void SlotCurrentIndexChanged(int index);
    void SlotCurrentTextChanged(const QString &strText);

private:
    QLabel* m_pLabel;
    QComboBox* m_pComboBox;
};

class CVLabelComboBox : public CLabelComboBox
{
    Q_OBJECT
public:
    CVLabelComboBox(const QString &strText, const QStringList &strList = QStringList(),
                    int iSpacing = 5, QWidget *parent = nullptr);
    virtual ~CVLabelComboBox();
};

class CHLabelComboBox : public CLabelComboBox
{
    Q_OBJECT
public:
    CHLabelComboBox(const QString &strText, const QStringList &strList = QStringList(),
                    int iSpacing = 16, QWidget *parent = nullptr);
    virtual ~CHLabelComboBox();
};

#endif // CLABELCOMBOBOX_H
