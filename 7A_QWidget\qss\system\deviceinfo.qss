QLabel
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    border: 0px solid red;
}

QLabel#LineLabel
{
   background-color: #D6DAEC;
}
QLabel#ImageLabel
{
   border-radius: 24px;
   background-color: #EAEFFD;
}
QLabel#IPCLabel
{
   image: url(:/image/ico/system/IPC.png);
}
QLabel#PLCLabel
{
   image: url(:/image/ico/system/PLC.png);
}

QLabel#BackgroundLabel
{
   border-radius: 32px;
   background-color: #FFF;
}

QLabel#TitleIconLabel
{
   border-radius: 3px;
   background-color: #3D78E5;
}

QLabel#TitleTextLabel
{
   color: #353E4E;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
}

/*一级标题*/
QLabel#SysTitleLabel1
{
    color: #6B788F;
    font-size: 24px;
    font-weight: 400;
    font-family: "Source Han Sans CN";
}

/*一级标题*/
QLabel#SysTitleLabel2
{
    color: #353E4E;
    font-size: 24px;
    font-weight: 500;
    font-family: "Source Han Sans CN";
}

QLabel[edit=false]#DateValueLabel
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    border-radius: 5px;
    border: 0px solid #A1ABBB;
}
QLabel[edit=true]#DateValueLabel
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    border-radius: 5px;
    border: 1px solid #A1ABBB;
}

QLineEdit
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    padding-left: 0px;
    border-radius: 5px;
    border: 1px solid #A1ABBB;
    background-color: transparent;
}
QLineEdit:focus{outline: none;}
QLineEdit:disabled
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    padding-left: 0px;
    border-radius: 5px;
    border: 0px solid #A1ABBB;
    background-color: transparent;
}
QPushButton
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #8490FF, stop: 1 #3D78E5);
}
QPushButton:focus{outline: none;}
QPushButton:pressed
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #6670c7, stop: 1 #3361b8);
}

QPushButton#CancelBtn
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 2px solid #3D78E5;
   background-color: #FFF;
}
QPushButton#CancelBtn:hover
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 3px solid #3D78E5;
   background-color: #FFF;
}
QPushButton#CancelBtn:pressed
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 4px solid #3D78E5;
   background-color: #FFF;
}

/*二级标题*/
QPushButton#SysTitleBtn
{
   color: #6B788F;
   font-size: 26px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-left: 0px solid #3D78E5;
   border-top: 0px solid #3D78E5;
   border-right: 0px solid #3D78E5;
   border-bottom: 0px solid #3D78E5;
   border-radius: 0px;
   background-color: transparent;
}

/*二级标题*/
QPushButton#SysTitleBtn:disabled
{
   color: #3D78E5;
   font-size: 26px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-left: 0px solid #3D78E5;
   border-top: 0px solid #3D78E5;
   border-right: 0px solid #3D78E5;
   border-bottom: 4px solid #3D78E5;
   border-radius: 0px;
   background-color: transparent;
}

QGroupBox
{
   border-radius: 32px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
       stop: 0 #DEE8FB, stop: 0.15 #FFF);
}

QStackedWidget
{
    border: 0px solid red;
}
