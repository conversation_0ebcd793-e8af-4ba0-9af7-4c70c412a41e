#include "italiankey.h"

#include "common/keyboardtoolbutton/keyboardtoolbutton.h"

ItalianKey::<PERSON><PERSON><PERSON>(const QString& name):
    LanguageBase<PERSON>ey(name)
{
    InitButtons();
}

void ItalianKey::InitButtons()
{
    QMap<int, QList<KeyBoardToolButton*> > map;
    QList<KeyBoardToolButton*> firstButtons, secondButtons, thirdButtons;

    firstButtons.append(new KeyBoardToolButton("\x0071","\x0051","",""));
    firstButtons.append(new KeyBoardToolButton("\x0077","\x0057","",""));
    firstButtons.append(new KeyBoardToolButton("\x0065","\x0045","è,é,ê,ë,ę,ė,ē","È,É,Ê,Ë,Ę,Ė,Ē"));
    firstButtons.append(new KeyBoardToolButton("\x0072","\x0052","",""));
    firstButtons.append(new KeyBoardToolButton("\x0074","\x0054","",""));
    firstButtons.append(new KeyBoardToolButton("\x0079","\x0059","",""));
    firstButtons.append(new KeyBoardToolButton("\x0075","\x0055","ū,ü,û,ú,ù","Ū,Ü,Û,Ú,Ù"));
    firstButtons.append(new KeyBoardToolButton("\x0069","\x0049","ī,į,ï,î,í,ì","Ī,Į,Ï,Î,Í,Ì"));
    firstButtons.append(new KeyBoardToolButton("\x006F","\x004F","º,ō,ø,œ,õ,ö,ô,ó,ò","º,Ō,Ø,Œ,Õ,Ö,Ô,Ó,Ò"));
    firstButtons.append(new KeyBoardToolButton("\x0070","\x0050","",""));

    secondButtons.append(new KeyBoardToolButton("\x0061","\x0041","à,â,æ,á,ä,ã,å,ā,ª","À,Â,Æ,Á,Ä,Ã,Å,Ā,ª"));
    secondButtons.append(new KeyBoardToolButton("\x0073","\x0053","ß,ś,š","Ś,Š"));
    secondButtons.append(new KeyBoardToolButton("\x0064","\x0044","",""));
    secondButtons.append(new KeyBoardToolButton("\x0066","\x0046","",""));
    secondButtons.append(new KeyBoardToolButton("\x0067","\x0047","",""));
    secondButtons.append(new KeyBoardToolButton("\x0068","\x0048","",""));
    secondButtons.append(new KeyBoardToolButton("\x006A","\x004A","",""));
    secondButtons.append(new KeyBoardToolButton("\x006B","\x004B","",""));
    secondButtons.append(new KeyBoardToolButton("\x006C","\x004C","",""));

    thirdButtons.append(GetLeftCapsLockBtn());
    thirdButtons.append(new KeyBoardToolButton("\x007A","\x005A","",""));
    thirdButtons.append(new KeyBoardToolButton("\x0078","\x0058","",""));
    thirdButtons.append(new KeyBoardToolButton("\x0063","\x0043","ç,ć,č","Ç,Ć,Č"));
    thirdButtons.append(new KeyBoardToolButton("\x0076","\x0056","",""));
    thirdButtons.append(new KeyBoardToolButton("\x0062","\x0042","",""));
    thirdButtons.append(new KeyBoardToolButton("\x006E","\x004E","",""));
    thirdButtons.append(new KeyBoardToolButton("\x006D","\x004D","",""));
    thirdButtons.append(GetRightCapsLockBtn());

    map.insert(0,firstButtons);
    map.insert(1,secondButtons);
    map.insert(2,thirdButtons);

    SetButtonsMap(map);
    SetTranslate("Cinese","inglese","matematica","spazio");
}
