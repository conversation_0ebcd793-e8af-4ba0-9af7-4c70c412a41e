#include "CPressureInfo.h"
#include <QBoxLayout>
#include <QDateTime>

#include "CSetChartXYRange.h"
#include "PublicParams.h"
#include "qcustomplot.h"
#include "CMessageBox.h"
#include "CRunTest.h"
#include "DBControl/CTimingTecDB.h"
#include "CReadWriteXlsxThread.h"

CPressureInfo::CPressureInfo(QWidget *parent)
    : QWidget(parent)
    , m_bShow(false)
    , m_bRunCheck(false)
    , m_iCheckStep(0)
{        
    Register2Map(Method_pressure_on);
    Register2Map(Method_pressure_stop);
    Register2Map(Method_pressure_info);
    Register2Map(Method_start);
    Register2Map(Method_stop);

    _InitWidget();
    _InitLayout();

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalTimingTestStart, this, &CPressureInfo::SlotClearData);

    m_pCBusyProgressBar = new CBusyProgressBar(m_strTipsText, tr("正在进行气密性检测")); //需要全屏显示
    m_pCBusyProgressBar->SetCancelButtonVisible(true);
    connect(m_pCBusyProgressBar, &CBusyProgressBar::SignalCancel, this, &CPressureInfo::_SlotCancelCheck);

    for(int i=0; i<gk_iMachineCount; i++)
    {
        SGasInfoStruct *pStruct = new SGasInfoStruct;
        m_sGasInfoList.push_back(pStruct);
    }
}

CPressureInfo::~CPressureInfo()
{
    UnRegister2Map(Method_pressure_on);
    UnRegister2Map(Method_pressure_stop);
    UnRegister2Map(Method_pressure_info);
    UnRegister2Map(Method_start);
    UnRegister2Map(Method_stop);
}

void CPressureInfo::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    Q_UNUSED(iResult);
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if(Method_pressure_info == iMethodID)
        _ReceiveData(iMachineID, qVarData);
    else if(Method_start == iMethodID)
        _TestEnd(iMachineID);
}

void CPressureInfo::SlotClearData(int iMachineID)
{
    qDebug()<<QString("时序开始,清空%1#压力曲线").arg(iMachineID + 1);
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    _ClearData(iMachineID);
}

void CPressureInfo::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;

    QTime t1 = QTime::currentTime();
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    SGasInfoStruct *pStruct = m_sGasInfoList.at(iMachineID);
    if(pStruct->bReplot)
    {
        pStruct->bReplot = false;
        m_pCPItemText->setText(pStruct->strCPItemText);
        m_pCustomPlot->graph(0)->setData(pStruct->dTimeVec, pStruct->dP1Vec);
        m_pCustomPlot->graph(1)->setData(pStruct->dTimeVec, pStruct->dP2Vec);
        m_pCustomPlot->replot();
        qDebug()<<"气压曲线画图耗时:"<<t1.msecsTo(QTime::currentTime());
    }

    QWidget::showEvent(pEvent);
}

void CPressureInfo::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;
    QWidget::hideEvent(pEvent);
}

void CPressureInfo::_SlotMachineChanged(int iMachineID)
{
    SGasInfoStruct *pStruct = m_sGasInfoList.at(iMachineID);
    m_pCPItemText->setText(pStruct->strCPItemText);

    QStringList strXYRangList = pStruct->strXYRangeList;
    m_pCSetChartXYRange->SetRange(strXYRangList);
    m_pCustomPlot->xAxis->setRange(strXYRangList.at(0).toDouble(), strXYRangList.at(1).toDouble());
    m_pCustomPlot->yAxis->setRange(strXYRangList.at(2).toDouble(), strXYRangList.at(3).toDouble());

    m_pCustomPlot->graph(0)->setData(pStruct->dTimeVec, pStruct->dP1Vec);
    m_pCustomPlot->graph(1)->setData(pStruct->dTimeVec, pStruct->dP2Vec);
    m_pCustomPlot->replot();
}

void CPressureInfo::_SlotListBtn()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    QPushButton *pBtn = dynamic_cast<QPushButton *>(sender());
    int index = pBtn->property("index").toInt();
    switch (index)
    {
    case 0:
        SendJsonCmd(iMachineID, Method_pressure_on, GetJsonCmdString(Method_pressure_on));
        break;
    case 1:
        SendJsonCmd(iMachineID, Method_pressure_stop, GetJsonCmdString(Method_pressure_stop));
        break;
    case 2:
        _ClearData(iMachineID);
        break;
    case 3:
        _ExportData(iMachineID);
        break;
    case 4:
        _RunCheckFun();
        break;
    default:
        break;
    }
}

void CPressureInfo::_ReceiveData(int iMachineID, QVariant qVarData)
{
    QVariantList qVarList = qVarData.toList();
    if(qVarList.isEmpty())
        return;

    SGasInfoStruct *pStruct = m_sGasInfoList.at(iMachineID);

    //除以10000
    double dP1 = qVarList.at(0).toDouble() / 10000.0;
    dP1 *= 6.89;
    pStruct->dP1Vec.push_back(dP1);

    double dP2 = 0;
    if(qVarList.size() >= 2)
    {
        dP2 = qVarList.at(1).toDouble() / 10000.0;
        dP2 *= 6.89;
    }
    pStruct->dP2Vec.push_back(dP2);

    pStruct->strCPItemText = QString("pressure1:%1\npressure2:%2\np1-p2:%3").arg(dP1).arg(dP2).arg(dP1 - dP2);

    //秒
    double dTime = 0;
    if(pStruct->dTimeVec.isEmpty())
        pStruct->qBeginDateTime = QDateTime::currentDateTime();
    else
        dTime = pStruct->qBeginDateTime.msecsTo(QDateTime::currentDateTime()) / 1000.0;
    pStruct->dTimeVec.push_back(dTime);

    pStruct->bReplot = true;
    if(m_bShow)
    {
        if(m_pMachineComboBox->GetCurrentIndex() == iMachineID)
        {
            m_pCPItemText->setText(pStruct->strCPItemText);
            pStruct->bReplot = false;
            m_pCustomPlot->graph(0)->setData(pStruct->dTimeVec, pStruct->dP1Vec);
            m_pCustomPlot->graph(1)->setData(pStruct->dTimeVec, pStruct->dP2Vec);
            m_pCustomPlot->replot();
        }
    }
}

void CPressureInfo::_ClearData(int iMachineID)
{
    m_sGasInfoList.at(iMachineID)->Clear();

    if(m_pMachineComboBox->GetCurrentIndex() == iMachineID)
    {
        m_pCPItemText->setText("pressure1:\npressure2:\np1-p2:");

        QVector<double> x, y;
        m_pCustomPlot->graph(0)->setData(x, y);
        m_pCustomPlot->graph(1)->setData(x, y);
        m_pCustomPlot->replot();
    }
}

void CPressureInfo::_ExportData(int iMachineID)
{
    QString strExportDir = CPublicConfig::GetInstance()->GetUDiskExportDir();
    if(!UDiskExistAndCreateDir(strExportDir, this))
        return;

    SGasInfoStruct *pStruct = m_sGasInfoList.at(iMachineID);

    QString strCurrentTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    QString strXlsxName = QString("Pressure_%1#_%2.xlsx").arg(iMachineID + 1).arg(strCurrentTime);

    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = strXlsxName;
    pXlsxStruct->strTableName = "pressure";
    pXlsxStruct->strTitleList << "time" << "pressure1" << "pressure2";

    QList<QVariantList> &varDataList = pXlsxStruct->varWriteDataList;
    for(int i=0; i<pStruct->dTimeVec.size(); i++)
    {
        QVariantList oneList;
        oneList << pStruct->dTimeVec.at(i) << pStruct->dP1Vec.at(i) << pStruct->dP2Vec.at(i);
        varDataList<<oneList;
    }

    FunWriteXlsxEndCallBack lambdaFunction = [this](QString strXlsxName, QString strTableName)
    {
        Q_UNUSED(strTableName);
        qDebug()<<strXlsxName<<"文件导出完成";
        QString strDestPath = CPublicConfig::GetInstance()->GetUDiskExportDir();
        MoveQFile(strXlsxName, QDir(strDestPath));
        System("sync");
        ShowInformation(this, tr("提示"), tr("文件导出完成"));
    };

    pXlsxStruct->WriteEndCallBack = lambdaFunction;

    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
}

void CPressureInfo::_WriteData2Xlsx(int iMachineID)
{
    SGasInfoStruct *pStruct = m_sGasInfoList.at(iMachineID);

    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = CPublicConfig::GetInstance()->GetTestXlsxName(iMachineID);;
    pXlsxStruct->strTableName = "pressure";
    pXlsxStruct->bDrawChart = true;
    pXlsxStruct->strTitleList << "time" << "pressure1" << "pressure2";

    int size = pStruct->dTimeVec.size();
    for(int i=0; i<size; i++)
    {
        QVariantList qRowList = {pStruct->dTimeVec.at(i), pStruct->dP1Vec.at(i), pStruct->dP2Vec.at(i)};
        pXlsxStruct->varWriteDataList << qRowList;
    }

    ChartNoteStruct chart;
    chart.iRow = 4;
    chart.iColumn = 5;
    chart.strChartTitle = "pressure data";
    chart.strXTitle = "time (s)";
    chart.strYTitle = "pressure (kpa)";
    chart.strSerialNameList << "pressure1" << "pressure2";
    chart.strXDataRange = QString("%1!$A$2:$A$%2").arg(pXlsxStruct->strTableName).arg(size + 1);
    chart.strNumDataRange = QString("B2:C%1").arg(size + 1);
    chart.bMajorGridlines = false;
    chart.strMarkSymbolList << "none" << "none";

    pXlsxStruct->chartNoteList << chart;
    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
    qDebug()<<QString("%1# pressure data write to result xlsx end").arg(iMachineID + 1);
}

void CPressureInfo::_SlotCancelCheck()
{
    m_bRunCheck = false;
    m_iCheckStep = 0;
    m_dTimeVec1.clear();
    m_dDataVec1.clear();
    m_dDataVec2.clear();
    m_dTimeVec2.clear();
    m_dDataVec3.clear();
    m_dDataVec4.clear();
    m_pCBusyProgressBar->close();

    int iMachineID = m_pMachineComboBox->GetCurrentIndex();

    QString strCmd = GetJsonCmdString(Method_stop);
    SendJsonCmd(iMachineID, Method_stop, strCmd);
}

void CPressureInfo::_SlotSetXYRange(const QStringList &strList)
{
    if(4 != strList.size())
        return;

    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_sGasInfoList.at(iMachineID)->strXYRangeList = strList;
    m_pCustomPlot->xAxis->setRange(strList.at(0).toDouble(), strList.at(1).toDouble());
    m_pCustomPlot->yAxis->setRange(strList.at(2).toDouble(), strList.at(3).toDouble());
    m_pCustomPlot->replot();
}

void CPressureInfo::_SlotPlotClicked(QCPAbstractPlottable *pPlottable, int iDataIndex, QMouseEvent *pEvent)
{
    Q_UNUSED(pEvent);
    QString strName = pPlottable->name();
    double dx=0, dy1=0, dy2=0, dDiff=0;

    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    SGasInfoStruct *pStruct = m_sGasInfoList.at(iMachineID);

    if("pressure1" == strName)
    {
        const QCPGraphData *pGraph = m_pCustomPlot->graph(0)->data()->at(iDataIndex);
        dx = pGraph->key;
        dy1 = pGraph->value;
        if(dx < pStruct->dP2Vec.size())
            dy2 = pStruct->dP2Vec.at(dx);
        dDiff = dy1 - dy2;
    }
    else
    {
        const QCPGraphData *pGraph = m_pCustomPlot->graph(1)->data()->at(iDataIndex);
        dx = pGraph->key;
        dy2 = pGraph->value;
        if(dx < pStruct->dP1Vec.size())
            dy1 = pStruct->dP1Vec.at(dx);
        dDiff = dy1 - dy2;
    }

    QString strText = QString("pressure1:%1\npressure2:%2\np1-p2:%3").arg(dy1).arg(dy2).arg(dDiff);
    m_pCPItemText->setText(strText);
    qDebug()<<Q_FUNC_INFO<<strName<<dx<<dy1<<dy2<<dDiff<<strText;
    m_pCustomPlot->replot();
}

void CPressureInfo::_RunCheckFun()
{
    int iBtnType = ShowQuestion(this, tr("提示"), tr("确定运行气密性检测吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    m_bRunCheck = true;
    m_iCheckStep = 1;
    m_dTimeVec1.clear();
    m_dDataVec1.clear();
    m_dDataVec2.clear();
    m_dTimeVec2.clear();
    m_dDataVec3.clear();
    m_dDataVec4.clear();
    RUN_LOG("开始气密性检测");
    m_pCBusyProgressBar->show();
    CPublicConfig::GetInstance()->SetTestDoneAutoReset(false);

    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    QString strDateTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    QString strSN = CPublicConfig::GetInstance()->GetMachineSN();
    QString strDir = CPublicConfig::GetInstance()->GetXlsxDir();
    m_strXlsxName = QString("pressure_check_%1_%2#_%3.xlsx").arg(strDateTime).arg(iMachineID+1).arg(strSN);
    m_strXlsxName = strDir + m_strXlsxName;

    QString strTecName;
    QString strTimingName = "卡盒总体气密性-腔体";
    _StartTimingTest(iMachineID, strTecName, strTimingName);
}

void CPressureInfo::_TestEnd(int iMachineID)
{
    _WriteData2Xlsx(iMachineID);

    if(!m_bRunCheck)
        return;

    SGasInfoStruct *pStruct = m_sGasInfoList.at(iMachineID);

    if(1 == m_iCheckStep)
    {
        RUN_LOG("气密性检测跑完第1次时序,开始跑第2次时序");
        m_dTimeVec1 = pStruct->dTimeVec;
        m_dDataVec1 = pStruct->dP1Vec;
        m_dDataVec2 = pStruct->dP2Vec;
        m_iCheckStep = 2;
        QString strTecName;
        QString strTimingName = "卡盒总体气密性-PCR";
        _StartTimingTest(iMachineID, strTecName, strTimingName);
        return;
    }

    RUN_LOG("气密性检测跑完第2次时序");
    m_dTimeVec2 = pStruct->dTimeVec;
    m_dDataVec3 = pStruct->dP1Vec;
    m_dDataVec4 = pStruct->dP2Vec;
    if(_HandleCheckData(iMachineID))
        ShowSuccess(this, m_strTipsText, tr("%1#气压检测通过").arg(iMachineID+1));
    else
        ShowWarning(this, m_strTipsText, tr("%1#气压检测不通过:%2").arg(iMachineID+1).arg(m_strXlsxName));

    m_bRunCheck = false;
    m_iCheckStep = 0;
    m_dTimeVec1.clear();
    m_dDataVec1.clear();
    m_dDataVec2.clear();
    m_dTimeVec2.clear();
    m_dDataVec3.clear();
    m_dDataVec4.clear();
    m_pCBusyProgressBar->close();
}

void CPressureInfo::_StartTimingTest(int iMachineID, QString strTecName, QString strTimingName)
{
    QString strTimingData = CTimingTecDB::GetInstance().GetTimingContent(strTimingName);

    QDateTime dateTime = QDateTime::currentDateTime();
    QString strCurrentTime = dateTime.toString("yyyyMMddhhmmss");
    QString strCardID = "C" + strCurrentTime;
    QString strSampleID = "S" + strCurrentTime;
    QString strProject = "2019-nCoV/FluA/FluB/RSV";

    SRunningInfoStruct &sRunInfo = CRunTest::GetInstance()->GetRunInfoStruct(iMachineID);
    sRunInfo.iTecIndex = GetTecIndex(strTecName);
    sRunInfo.bFactroyTest = false;
    sRunInfo.iRunTimes = 1;
    sRunInfo.strTecName = strTecName;
    sRunInfo.strTimingName = strTimingName;
    sRunInfo.strTimingData = strTimingData;
    sRunInfo.sCardInfo.strCardID = strCardID;
    sRunInfo.sCardInfo.strProject = strProject;
    sRunInfo.sSampleInfo.strSampleID = strSampleID;
    sRunInfo.sSampleInfo.strQCTestModel = "T";
    sRunInfo.sSampleInfo.strOperator = CPublicConfig::GetInstance()->GetLoginUser();
    qDebug()<<QString("%1#工厂模式创建气密性工装时序测试,项目:%2,TEC:%3,时序:%4,,次数:%5")
              .arg(iMachineID + 1).arg(strProject).arg(strTecName).arg(strTimingName).arg(sRunInfo.iRunTimes);

    CRunTest::GetInstance()->StartTest(iMachineID);
}

static double GetMaxValue(QVector<double> dVec)
{
    double dMax = dVec.first();
    for(int i=0; i<dVec.size(); i++)
        dMax = qMax(dMax, dVec.at(i));
    return dMax;
}

static double GetUseTime(QVector<double> dTimeVec, QVector<double> dDataVec)
{
    double dUseTime = -1;
    for(int i=0; i<dDataVec.size(); i++)
    {
        if(dDataVec.at(i) - dDataVec.at(0) > 1.5)
        {
            if(i < dTimeVec.size())
                dUseTime = dTimeVec.at(i);
            break;
        }
    }
    return dUseTime;
}

bool CPressureInfo::_HandleCheckData(int iMachineID)
{
    QVector<double> dTimeVec1, dP1Vec, dP2Vec, dP3Vec, dP4Vec, dTimeVec2;
    dTimeVec1 = m_dTimeVec1;
    dTimeVec2 = m_dTimeVec2;
    dP1Vec = m_dDataVec1;
    dP2Vec = m_dDataVec2;
    dP3Vec = m_dDataVec3;
    dP4Vec = m_dDataVec4;

    if(dTimeVec1.isEmpty() || dTimeVec2.isEmpty())
        return false;

    bool bCheckOK = true;
    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = m_strXlsxName;
    pXlsxStruct->strTableName = "pressure_check";
    pXlsxStruct->bDrawChart = true;
    pXlsxStruct->strTitleList<<"时间1"<<"进气口气压值_孔1"<<"出气口气压值_孔1"<<"时间2"<<"进气口气压值_孔2"<<"出气口气压值_孔2"<<""
                            <<"气嘴"<<"参数"<<"孔1"<<""<<""<<"结果"<<"孔2"<<""<<""<<"结果";
    QVariant qEmptyVar;
    int iVecMaxSize = qMax(dTimeVec1.size(), dTimeVec2.size());
    for(int i=0; i<iVecMaxSize; i++)
    {
        QVariantList qRowList;
        if(i < dTimeVec1.size())
            qRowList<<dTimeVec1.at(i)<<dP1Vec.at(i)<<dP2Vec.at(i);
        else
            qRowList<<qEmptyVar<<qEmptyVar<<qEmptyVar;

        if(i < dTimeVec2.size())
            qRowList<<dTimeVec2.at(i)<<dP3Vec.at(i)<<dP4Vec.at(i);
        else
            qRowList<<qEmptyVar<<qEmptyVar<<qEmptyVar;

        if(0 == i)
        {
            qRowList<<""<<""<<""<<"当前值"<<"下限"<<"上限"<<""<<"当前值"<<"下限"<<"上限";
        }
        if(1 == i)
        {
            QString str1 = "不通过";
            double dFirst1 = dP1Vec.at(0);
            if(dFirst1 >= -0.50 && dFirst1 <= 2.00)
                str1 = "通过";
            else
                bCheckOK = false;

            QString str2 = "不通过";
            double dFirst2 = dP3Vec.at(0);
            if(dFirst2 >= -0.50 && dFirst2 <= 2.00)
                str2 = "通过";
            else
                bCheckOK = false;

            qRowList<<""<<"进气口"<<"首点气压"<<dFirst1<<-0.50<<2.00<<str1<<dFirst2<<-0.50<<2.00<<str2;
        }
        if(2 == i)
        {
            QString str1 = "不通过";
            double dLast1 = dP1Vec.last();
            if(dLast1 >= 38.00 && dLast1 <= 48.00)
                str1 = "通过";
            else
                bCheckOK = false;

            QString str2 = "不通过";
            double dLast2 = dP3Vec.last();
            if(dLast2 >= 23.00 && dLast2 <= 32.00)
                str2 = "通过";
            else
                bCheckOK = false;

            qRowList<<""<<"进气口"<<"末点气压"<<dLast1<<38.00<<48.00<<str1<<dLast2<<23.00<<32.00<<str2;
        }
        if(3 == i)
        {
            QString str1 = "不通过";
            double dMax1 = GetMaxValue(dP1Vec);
            if(dMax1 >= 45.00 && dMax1 <= 61.00)
                str1 = "通过";
            else
                bCheckOK = false;

            QString str2 = "不通过";
            double dMax2 = GetMaxValue(dP3Vec);
            if(dMax2 >= 28.00 && dMax2 <= 41.00)
                str2 = "通过";
            else
                bCheckOK = false;

            qRowList<<""<<""<<"峰值"<<dMax1<<45.00<<61.00<<str1<<dMax2<<28.00<<41.00<<str2;
        }
        if(4 == i)
        {
            QString str1 = "不通过";
            double dDiff1 = GetMaxValue(dP1Vec) - dP1Vec.last();
            if(dDiff1 >= 3.00 && dDiff1 <= 16.00)
                str1 = "通过";
            else
                bCheckOK = false;

            QString str2 = "不通过";
            double dDiff2 = GetMaxValue(dP3Vec) - dP3Vec.last();
            if(dDiff2 >= 1.00 && dDiff2 <= 13.00)
                str2 = "通过";
            else
                bCheckOK = false;

            qRowList<<""<<""<<"峰末差值"<<dDiff1<<3.00<<16.00<<str1<<dDiff2<<1.00<<13.00<<str2;
        }
        if(5 == i)
        {
            QString str1 = "不通过";
            double dFirst1 = dP2Vec.at(0);
            if(dFirst1 >= -0.50 && dFirst1 <= 2.00)
                str1 = "通过"; 
            else
                bCheckOK = false;

            QString str2 = "不通过";
            double dFirst2 = dP4Vec.at(0);
            if(dFirst2 >= -0.50 && dFirst2 <= 4.00)
                str2 = "通过";
            else
                bCheckOK = false;

            qRowList<<""<<"出气口"<<"首点气压"<<dFirst1<<-0.50<<2.00<<str1<<dFirst2<<-0.50<<4.00<<str2;
        }
        if(6 == i)
        {
            QString str1 = "不通过";
            double dLast1 = dP2Vec.last();
            if(dLast1 >= 35.00 && dLast1 <= 46.00)
                str1 = "通过";
            else
                bCheckOK = false;

            QString str2 = "不通过";
            double dLast2 = dP4Vec.last();
            if(dLast2 >= 20.00 && dLast2 <= 31.00)
                str2 = "通过";
            else
                bCheckOK = false;

            qRowList<<""<<"出气口"<<"末点气压"<<dLast1<<35.00<<46.00<<str1<<dLast2<<20.00<<31.00<<str2;
        }
        if(7 == i)
        {
            QString str1 = "不通过";
            double dUseTime1 = GetUseTime(dTimeVec1, dP2Vec);
            if(dUseTime1 >= 5.00 && dUseTime1 <= 15.00)
                str1 = "通过";
            else
                bCheckOK = false;

            QString str2 = "不通过";
            double dUseTime2 = GetUseTime(dTimeVec2, dP4Vec);
            if(dUseTime2 >= 5.00 && dUseTime2 <= 16.00)
                str2 = "通过";
            else
                bCheckOK = false;

            qRowList<<""<<""<<"起峰时长"<<dUseTime1<<5.00<<15.00<<str1<<dUseTime2<<5.00<<16.00<<str2;
        }
        if(8 == i)
        {
            QString str1 = "不通过";
            double dSpan1 = dP1Vec.last() - dP2Vec.last();
            if(dSpan1 >= -1.00 && dSpan1 <= 7.00)
                str1 = "通过";
            else
                bCheckOK = false;

            QString str2 = "通过";
            double dSpan2 = dP3Vec.last() - dP4Vec.last();
            if(dSpan2 >= -1.00 && dSpan2 <= 8.00)
                str2 = "通过";
            else
                bCheckOK = false;

            qRowList<<""<<"进出口"<<"末点压差"<<dSpan1<<-1.00<<7.00<<str1<<dSpan2<<-1.00<<8.00<<str2;
        }

        pXlsxStruct->varWriteDataList << qRowList;
    }

    ChartNoteStruct chart1;
    chart1.iRow = 10;
    chart1.iColumn = 6;
    chart1.strChartTitle = "pressure data";
    chart1.strXTitle = "time (s)";
    chart1.strYTitle = "pressure (kpa)";
    chart1.strSerialNameList << "气嘴1" << "气嘴2";
    chart1.strXDataRange = QString("%1!$A$2:$A$%2").arg(pXlsxStruct->strTableName).arg(iVecMaxSize + 1);
    chart1.strNumDataRange = QString("B2:C%1").arg(iVecMaxSize + 1);
    chart1.bMajorGridlines = false;
    chart1.strMarkSymbolList << "none" << "none";

    ChartNoteStruct chart2;
    chart2.iRow = 10;
    chart2.iColumn = 20;
    chart2.strChartTitle = "pressure data";
    chart2.strXTitle = "time (s)";
    chart2.strYTitle = "pressure (kpa)";
    chart2.strSerialNameList << "气嘴1" << "气嘴2";
    chart2.strXDataRange = QString("%1!$D$2:$D$%2").arg(pXlsxStruct->strTableName).arg(iVecMaxSize + 1);
    chart2.strNumDataRange = QString("E2:F%1").arg(iVecMaxSize + 1);
    chart2.bMajorGridlines = false;
    chart2.strMarkSymbolList << "none" << "none";

    pXlsxStruct->chartNoteList << chart1 << chart2;
    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
    qDebug()<<QString("%1# pressure check data write to result xlsx end").arg(iMachineID + 1);
    return bCheckOK;
}

void CPressureInfo::_InitCustomPlot()
{
    m_pCustomPlot = new QCustomPlot;
    m_pCustomPlot->setFixedSize(1494, 665);

    QFont font;
    font.setPointSize(10);
    m_pCustomPlot->legend->setFont(font);
    m_pCustomPlot->legend->setSelectedFont(font);
    m_pCustomPlot->legend->setVisible(true);
    m_pCustomPlot->legend->setSelectableParts(QCPLegend::spItems);
    m_pCustomPlot->legend->setBorderPen(Qt::NoPen);
    m_pCustomPlot->legend->setWrap(1);
    m_pCustomPlot->axisRect()->insetLayout()->setInsetAlignment(0, Qt::AlignTop);

    _AddGraph(m_pCustomPlot, Qt::blue, Qt::blue, 0, "pressure1");
    _AddGraph(m_pCustomPlot, Qt::green, Qt::green, 1, "pressure2");

    m_pCustomPlot->xAxis->setRange(0, 100);
    m_pCustomPlot->yAxis->setRange(0, 40);
    m_pCustomPlot->xAxis->setLabel("time/s");
    m_pCustomPlot->yAxis->setLabel("Kpa");

    m_pCustomPlot->yAxis->setSubTicks(false);
    m_pCustomPlot->yAxis->ticker()->setTickCount(30);

    connect(m_pCustomPlot, &QCustomPlot::plottableClick, this, &CPressureInfo::_SlotPlotClicked);

    QCPItemText *pCPItemText = new QCPItemText(m_pCustomPlot);
    pCPItemText->setTextAlignment(Qt::AlignCenter);

    pCPItemText->setFont(QFont(this->font().family(), 12));
    pCPItemText->setPen(QPen(Qt::black));
    pCPItemText->setBrush(QBrush(QColor("#a1ffa1")));
    pCPItemText->setPadding(QMargins(5, 5, 5, 5));

    QString strText = "pressure1:\npressure2:\np1-p2:";
    pCPItemText->setText(strText);
    pCPItemText->setVisible(true);
    pCPItemText->position->setType(QCPItemPosition::ptAxisRectRatio);
    pCPItemText->position->setCoords(0.8, 0.05);
    m_pCPItemText = pCPItemText;
}

void CPressureInfo::_AddGraph(QCustomPlot *pCustomPlot, QColor penColor, QColor pointColor, int iChart, QString strChartName)
{
    QPen pen;
    pen.setWidth(2);
    pen.setColor(penColor);
    pCustomPlot->addGraph();
    pCustomPlot->graph(iChart)->setPen(pen);
    pCustomPlot->graph(iChart)->setName(strChartName);
    pCustomPlot->graph(iChart)->setAntialiasedFill(true);
    pCustomPlot->graph(iChart)->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssNone,
                                                                QPen(pointColor, 2),
                                                                QBrush(pointColor), 2));
}

void CPressureInfo::_InitWidget()
{
    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(80, 50);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMachineChanged(int)));

    int iBtnWidth = 120;
    if(eLanguage_German == gk_iLanguage)
        iBtnWidth = 140;

    QStringList strBtnTextList = {tr("开启检测"), tr("停止检测"), tr("清空数据"), tr("导出数据"), tr("气密性检测")};
    for(int i=0; i<strBtnTextList.size(); i++)
    {
        QPushButton *pBtn = new QPushButton(strBtnTextList.at(i));
        pBtn->setProperty("index", i);
        pBtn->setFixedSize(iBtnWidth, 50);
        connect(pBtn, &QPushButton::clicked, this, &CPressureInfo::_SlotListBtn);
        m_pBtnList.push_back(pBtn);
    }

    _InitCustomPlot();

    QStringList strRangeList = {"0", "100", "0", "40"};
    m_pCSetChartXYRange = new CSetChartXYRange(strRangeList);
    m_pCSetChartXYRange->SetLineEditTextAlignment();
    connect(m_pCSetChartXYRange, &CSetChartXYRange::SignalSetRange, this, &CPressureInfo::_SlotSetXYRange);
}

void CPressureInfo::_InitLayout()
{
    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(20);
    pTopLayout->addWidget(m_pMachineComboBox);
    pTopLayout->addSpacing(100);
    for(int i=0; i<m_pBtnList.size(); i++)
        pTopLayout->addWidget(m_pBtnList.at(i));
    pTopLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addLayout(pTopLayout);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pCustomPlot);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pCSetChartXYRange, 0, Qt::AlignLeft);
    this->setLayout(pLayout);
}
