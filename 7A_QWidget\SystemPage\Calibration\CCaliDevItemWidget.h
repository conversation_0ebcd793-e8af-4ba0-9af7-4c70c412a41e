#ifndef CCALIDEVITEMWIDGET_H
#define CCALIDEVITEMWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-08-05
  * Description: 校准-设备item
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QTimer>
#include <QLabel>
#include <QGroupBox>
#include <QPushButton>

#include "PublicParams.h"

class CCaliDevItemWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CCaliDevItemWidget(int iMachineID, const SDevParamsStruct &sDevParams, QWidget *parent = nullptr);

    int GetMachineID() const;
    void ReceiveCaliResult(int iResult);
    void UpdateDateLabel(QString strLastDate, QString strNextDate);

public slots:
    void SlotSetDevStatus(int iMachineID, DeviceStatus eStatus);

private slots:
    void _SlotCaliBtn();
    void _SlotRunTimer();
    void _SlotTestReceiveCalibrate();

private:
    void _CaliResult(int iResult);

private:
    QGroupBox *_CreateGroupBox();

private:
    QLabel *m_pTitleLabel;
    QLabel *m_pIndexLabel, *m_pTextLabel;
    QLabel *m_pLastNameLabel, *m_pLastDateLabel;
    QLabel *m_pNextNameLabel, *m_pNextDateLabel;
    QPushButton *m_pCaliBtn;

private:
    int m_iMachineID;
    SDevParamsStruct m_sDevParams;
    DeviceStatus m_eStatus;
    const QString m_strTipsText;
    QTimer *m_pRunTimer;
    QString m_strXlsxPath;
};

#endif // CCALIDEVITEMWIDGET_H
