#ifndef CREGISTERDB_H
#define CREGISTERDB_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-10-23
  * Description: 注册数据库
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QObject>
#include "CSqliteDBBase.h"

class CRegisterDB : public QObject , public CSqliteDBBase
{
    Q_OBJECT
public:
    static CRegisterDB *GetInstance();
    virtual ~CRegisterDB();

    int GetCount();
    bool AddData(const QString &strCardID, const QString &strSampleID, const QString &strCT,
                 const QString &strUplift, const QString &strThreshold, const QString &strFL);
    bool AddData(QString strCardID, QString strSampleID, QStringList strCTList, QStringList strUpLiftList,
                 QStringList strThresholdList, QList<QString> strFLList);
    bool DelAll();
    bool GetFLData(const QString &strCardID, QString &strFL);
    bool GetCTInfo(const QString &strCardID, QStringList &strInfoList);

private:
    CRegisterDB();

private:
    static CRegisterDB *m_spInstance;

    Q_DISABLE_COPY(CRegisterDB)
};

#endif // CREGISTERDB_H
