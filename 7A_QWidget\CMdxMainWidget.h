#ifndef CMDXMAINWIDGET_H
#define CMDXMAINWIDGET_H

#include <QTimer>
#include <QLabel>
#include <QPushButton>
#include <QGroupBox>
#include <QStackedWidget>

#include "CPressLabel.h"
#include "HomePage/CHomePage.h"
#include "HistoryPage/CHistoryPage.h"
#include "QCPage/CQCPage.h"
#include "SystemPage/CSystemPage.h"
#include "PowerPage/CPowerPage.h"

class CMdxMainWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CMdxMainWidget(QWidget *parent = nullptr);
    ~CMdxMainWidget();

    void Set2MainPage();

public slots:
    void SlotSetRunLog(const QString &strLog);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;

signals:
    void SignalLogout();
	void SignalWiFiState(bool bConnect);

private slots:
    void _SlotWiFiState(bool bConnect);
    void _SlotEth1State(bool bConnect);
    void _SlotLisState(bool bConnect);
    void _SlotUpdateCurrentTime();
    void _SlotUpdateUDiskTime();
    void _SlotTitleBtn();
    void _SlotPowerBtn();
    void _SlotLogout();
    void _SlotGotoRunLogPage();
    void _SlotCreateQCTest(int iMachineID,QString strQCModel);

private:
    QPushButton *_CreatePowerBtn();
    QGroupBox *_CreateLeftGroupBox();
    QGroupBox *_CreateRightGroupBox();
    void _InitWidget();
    void _InitLayout();

private:
    QLabel *m_pLogoLabel;
    QList<QPushButton *> m_pTitleBtnList;
    QList<QLabel *> m_pTitleLabelList;

    QPushButton *m_pPowerBtn;

    QLabel *m_pUserIconLabel, *m_pUserNameLabel;
    CPressLabel *m_pRunLogLabel;
    QLabel *m_pUDiskLabel, *m_pEth0Label, *m_pWifiLabel, *m_pLisLabel, *m_pPrinterLabel;
    QLabel *m_pTimerLabel;

    QTimer *m_pCurrentTimer;
    QTimer *m_pUDiskTimer;

    CHomePage *m_pCHomePage;
    CHistoryPage *m_pCHistoryPage;
    CQCPage *m_pCQCPage;
    CSystemPage *m_pCSystemPage;
    QStackedWidget *m_pStackWidget;

    CPowerPage *m_pCPowerPage;

private:
    bool m_bEth1State;
    bool m_bWiFiState;
    bool m_bLisState;
};

#endif // CMDXMAINWIDGET_H
