#ifndef CCALIHISTORYWIDGET_H
#define CCALIHISTORYWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-08-05
  * Description: 校准历史
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QPushButton>
#include <QTableWidget>

#include "CLineEdit.h"

class CCaliHistoryWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CCaliHistoryWidget(QWidget *parent = nullptr);

protected:
    virtual void showEvent(QShowEvent *pEvent);
    virtual void hideEvent(QHideEvent *pEvent);

signals:
    void SignalReturn();

private slots:
    void _SlotGotoPageBtn();
    void _SlotPrePageBtn();
    void _SlotNextPageBtn();
    void _SlotExportBtn();

private:
    void _ShowCurrentPageData();
    void _UpdateTableWidget(QList<QStringList> strDataList);
    void _SetTableWidgetItem(int iRow, int iColu, QString strText);
    void _UpdatePageUI();

private:
    void _InitWidget();
    void _InitLayout();

private:
    bool m_bRefresh;
    bool m_bShow;
    int m_iTotalLines;
    int m_iTotalPages;
    int m_iLeftLines;
    int m_iCurrentPage;
    int m_iOnePageLines;

private:
    QTableWidget *m_pTableWidget;
    QLabel *m_pLinesLabel;
    QLabel *m_pGotoLabel1, *m_pGotoLabel2;
    CLineEdit *m_pGotoLineEdit;
    QPushButton *m_pGotoBtn;
    QPushButton *m_pPrePageBtn;
    QLabel *m_pPageLabel;
    QPushButton *m_pNextPageBtn;
    QPushButton *m_pReturnBtn;
};

#endif // CCALIHISTORYWIDGET_H
