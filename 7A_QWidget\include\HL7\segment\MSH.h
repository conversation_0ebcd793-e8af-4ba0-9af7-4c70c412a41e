﻿#ifndef _MSH_H_
#define  _MSH_H_

#include "../common/HL7Segment.h"
#include "../common/HL7Field.h"
#include "../StringUtil.h"
#include "../interface/IMSH.h"
using namespace Utility;

class MSH : public HL7Segment, public IMSH
{
public:
	MSH();
	virtual ~MSH();
	DECLARE_OBJECTBASE

	BEGIN_IMPL_QUERYIF(MSH)
		IMPL_QUERYIF_BYPATH(IF_OBJECTBASE, IHL7Segment, IObjectBase)
		IMPL_QUERYIF(IF_HL7SEGMENT, IHL7Segment)
		IMPL_QUERYIF(IF_MSH, IMSH)
		END_IMPL_QUERYIF()

	/*
	*	\brief 发送应用程序，index 1
	*/
	virtual void SetSendApp(const char* sendApp);

	virtual void GetSendApp(char** sendApp);

	/*
	*	\brief 设置发送设备名称，index 2
	*/
	virtual void SetSendFacilityName(const char* sendFacilityName);

	virtual void GetSendFacilityName(char** sendFacilityName);

	/*
	*	\brief 设置发送设备唯一标识符，index 2
	*/
	virtual void SetSendFacilityID(const char* sendFacilityID);

	virtual void GetSendFacilityID(char** sendFacilityID);
	/*
	*	\brief 设置发送设备型号，index 2
	*/
	virtual void SetSendFacilityType(const char* sendFacilityType);

	virtual void GetSendFacilityType(char** sendFacilityType);

	/*
	*	\brief 设置接收应用程序，index 3
	*/
	virtual void SetRecvApp(const char* recvApp);

	virtual void GetRecvApp(char** recvApp);
	/*
	*	\brief 设置接收设备名称，index 4
	*/
	virtual void SetRecvFacilityName(const char* recvFacilityName);

	virtual void GetRecvFacilityName(char** recvFacilityName);

	/*
	*	\brief 设置接收设备唯一标识符，index 4
	*/
	virtual void SetRecvFacilityID(const char* recvFacilityID);

	virtual void GetRecvFacilityID(char** recvFacilityID);
	/*
	*	\brief 设置接收设备型号，index 4
	*/
	virtual void SetRecvFacilityType(const char* recvFacilityType);

	virtual void GetRecvFacilityType(char** recvFacilityType);

	/*
	*	\brief 信息的日期时间，index 5
	*/
	virtual void SetDate(const char* date);

	virtual void GetDate(char** date);

	/*
	*	\brief 消息类型， index 7
	*/
	virtual void SetMessageType(const char* msgType);

	virtual void GetMessageType(char** messageType);

	std::string GetMessageType();
	/*
	*	\brief 消息类型，index 7
	*/
	std::string GetTriggerEvent();

	virtual void SetTriggerEvent(const char* triggerEvent);

	virtual void GetTriggerEvent(char** triggerEvent);


	/*
	*	\brief 消息结构, index 7
	*/
	virtual void SetMessageStructure(const char* msgStructure);

	virtual void GetMessageStructure(char** msgStructure);

	/*
	*	\brief 信息控制ID，index 8
	*/
	virtual void SetMsgCtrlID(const char* msgCtrlID);

	virtual void GetMsgCtrlID(char** msgCtrlID);

	/*
	*	\brief 处理ID号,index 9
	*/
	virtual void SetProcessingID(const char* processingID);

	virtual void GetProcessingID(char** processingID);

	/*
	*	\brief 版本ID号，index 10
	*/
	virtual void SetVersionID(const char* versionID);

	virtual void GetVersionID(char** versionID);

	/*
	*	\brief 应用程序确认类型，index 14
	*/
	virtual void SetApplicationAckType(const char* applicationAckType);

	virtual void GetApplicationAckType(char** applicationAckType);

	/*
	*	\brief 国家代码，index 15
	*/
	virtual void SetCountryCode(const char* countryCode);

	virtual void GetCountryCode(char** countryCode);
	/*
	*	\brief 字符集，index 16
	*/
	virtual void SetCharacterSet(const char* charset);

	virtual void GetCharacterSet(char** characterSet);


	virtual void Parse(const char* segmentStr, EncodingCharacters encodingCharacters);

	void ParseMessageType();

	void ParseRecvFacility();

	void ParseSendFacility();

	MSH& operator=(MSH& msh);

private:
	void BuildSendFacility();

	void BuildRecvFacility();

	void BuildMessageType();

private:
	HL7Field m_sendApp;//发送应用程序
	HL7Field m_sendFacility;//发送设备
	HL7Field m_receiveApp;//接收应用程序
	HL7Field m_receiveFacility;//接收设备
	HL7Field m_date;//消息发送时间
	HL7Field m_messageType;//消息类型
	HL7Field m_messageCtrlID;//消息控制ID
	HL7Field m_processingID;//处理ID
	HL7Field m_versionID;//版本ID号
	HL7Field m_countryCode;//国家代码
	HL7Field m_characterSet;//字符集
	HL7Field m_applicationAckType;//应用程序确认代码

	HL7Component m_sendFacilityName;//发送设备名称
	HL7Component m_sendFacilityID;//发送设备ID
	HL7Component m_sendFacilityType;//发送设备型号

	HL7Component m_recvFacilityName;//接收设备名称
	HL7Component m_recvFacilityID;//接收设备ID
	HL7Component m_recvFacilityType;//接收设备型号

	HL7Component m_messageTypeComp;//消息类型组件
	HL7Component m_triggerEvent;//触发事件组件
	HL7Component m_messageStructure;//消息结构组件

};

REGISTER_CLASS(MSH);
#endif
