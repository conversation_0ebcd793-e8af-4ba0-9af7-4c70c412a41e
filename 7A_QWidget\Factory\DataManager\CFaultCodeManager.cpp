#include "CFaultCodeManager.h"
#include <QDir>
#include <QFile>
#include <QDebug>
#include <QBoxLayout>
#include <QHeaderView>
#include <QFileDialog>
#include <QApplication>
#include <QTime>

#include "CMessageBox.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "CShowLogDetail.h"
#include "CReadWriteXlsxThread.h"

CFaultCodeManager::CFaultCodeManager(QWidget *parent) : QWidget(parent) , m_strTipsText(tr("提示"))
{
    this->setFixedSize(1494, 798);
    _InitWidget();
    _InitLayout();

    m_pUpdateBar = new CBusyProgressBar(m_strTipsText, tr("正在更新故障码表"), this);
    m_pUpdateBar->setVisible(false);

    m_iReadType = 0;
    if(eLanguage_Chinese == gk_iLanguage)
        m_strXlsxName = "faultcode.xlsx";
    else if(eLanguage_English == gk_iLanguage)
        m_strXlsxName = "faultcode-english.xlsx";
    else if(eLanguage_Spanish == gk_iLanguage)
        m_strXlsxName = "faultCode_spanish.xlsx";
    else if(eLanguage_German == gk_iLanguage)
        m_strXlsxName = "faultCode_german.xlsx";
    else if(eLanguage_Italian == gk_iLanguage)
        m_strXlsxName = "faultCode_italian.xlsx";
    else
        m_strXlsxName = "faultcode.xlsx";

    m_strXlsxPath = CPublicConfig::GetInstance()->GetResourceDir() + m_strXlsxName;
    QTimer::singleShot(5000, this, &CFaultCodeManager::_SlotDelayReadXlsx);
}

CFaultCodeManager::~CFaultCodeManager()
{

}

void CFaultCodeManager::_SlotDelayReadXlsx()
{
    _ReadXlsx(m_strXlsxPath);
}

void CFaultCodeManager::_ReadXlsx(const QString &strXlsxPath)
{
    if(!QFile::exists(strXlsxPath))
    {
        qDebug()<<"故障码表不存在:"<<m_strXlsxPath;
        return;
    }

    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = strXlsxPath;
    pXlsxStruct->eOpXlsxType = eReadXlsx;

    FunReadXlsxEndCallBack lambdaFunction = [this](QString strXlsxName, const ReadXlsxDataMap &varReadDataMap)
    {
        QMap<int, QStringList> iReadCodeMap;
        for(auto it=varReadDataMap.constBegin(); it!=varReadDataMap.constEnd(); it++)
        {
            QList<QVariantList> oneTabelList = it.value(); //xlsx里的一张表
            for(int i=0; i<oneTabelList.size(); i++)
            {
                QVariantList oneRowList = oneTabelList.at(i); //表里的一行
                if(oneRowList.size() < 6)
                    continue;

                int iCodeID = oneRowList.at(0).toInt();
                if(iCodeID <= 0)
                    continue;

                oneRowList = oneRowList.mid(0, 6);
                oneRowList.push_front("0"); //添加序号,查询定位哪一行用

                QStringList strList;
                for(int i=0; i<oneRowList.size(); i++)
                    strList.push_back(oneRowList.at(i).toString());
                iReadCodeMap.insert(iCodeID, strList);
            }
        }
        qDebug()<<"故障码表读取结束:"<<strXlsxName<<iReadCodeMap.size();

        if(0 == m_iReadType)
        {
            this->m_iCodeInfoMap = iReadCodeMap;
            _ReflashCode();
        }
        else
        {
            Delay_MSec(2000);
            _UpdateEnd(strXlsxName, iReadCodeMap);
        }
    };

    pXlsxStruct->ReadEndCallBack = lambdaFunction;
    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
}

void CFaultCodeManager::_SlotListBtn()
{
    QPushButton *pBtn = dynamic_cast<QPushButton *>(sender());
    if(nullptr == pBtn)
        return;

    int index = pBtn->property("index").toInt();
    switch (index)
    {
    case 0: _QueryCode();  break;
    case 1: _DetailCode(); break;
    case 2: _UpdateCode(); break;
    default: break;
    }
}

void CFaultCodeManager::_QueryCode()
{
    int iCodeID = m_pCodeLineEdit->text().trimmed().toInt();
    if(!m_iCodeInfoMap.contains(iCodeID))
    {
        ShowInformation(this, m_strTipsText, tr("要查询的故障码不存在"));
        return;
    }

    QStringList strList = m_iCodeInfoMap.value(iCodeID);
    int iRow = strList.at(0).toInt() - 1;
    m_pTableWidget->selectRow(iRow);
}

void CFaultCodeManager::_ReflashCode()
{
    m_pTableWidget->clearContents();
    m_pTableWidget->setRowCount(m_iCodeInfoMap.size());

    int iRow = 0;
    for(auto it=m_iCodeInfoMap.begin(); it!= m_iCodeInfoMap.end(); it++)
    {
        QStringList &oneRowList = it.value();
        oneRowList[0] = QString::number(iRow + 1); //更新行row
        for(int iCol=0; iCol<oneRowList.size(); iCol++)
        {
            QTableWidgetItem *pItem = new QTableWidgetItem;
            pItem->setText(oneRowList.at(iCol));
            pItem->setTextAlignment(Qt::AlignCenter);
            m_pTableWidget->setItem(iRow, iCol, pItem);
        }
        iRow++;
    }

    emit SignalUpdateCodeMap(m_iCodeInfoMap);
}

void CFaultCodeManager::_DetailCode()
{
    int iRow = m_pTableWidget->currentRow();
    if(iRow < 0)
    {
        ShowInformation(this, m_strTipsText, tr("请先选择一行"));
        return;
    }

    QTableWidgetItem *pItem = m_pTableWidget->item(iRow, 1);
    int iCodeID = pItem->text().toInt();
    QStringList strList = m_iCodeInfoMap.value(iCodeID);
    QStringList strInfoList;
    int size = qMin(strList.size(), m_strTitleList.size());
    for(int i=0; i<size; i++)
    {
        QString strOneLine = QString("%1: %2").arg(m_strTitleList.at(i)).arg(strList.at(i));
        strInfoList.push_back(strOneLine);
    }
    CShowLogDetail *pShow = new CShowLogDetail(this);
    pShow->ShowTextList(strInfoList);
}

void CFaultCodeManager::_UpdateCode()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定更新故障码表吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    QString strCurrentDir = QApplication::applicationDirPath() + "/";
    QString strFilePath;
#ifdef __aarch64__
    strFilePath = GetUDiskUpdateDir() + m_strXlsxName;
    qDebug()<<strFilePath;
    QDir dir(GetUDiskDir());
    if(!dir.exists())
    {
        ShowInformation(this, m_strTipsText, tr("请先插入U盘"));
        return;
    }

    if(!QFile::exists(strFilePath))
    {
        ShowInformation(this, m_strTipsText, tr("U盘中文件不存在"));
        return;
    }
#else
    strFilePath = QFileDialog::getOpenFileName(this, tr("更新故障码表"), strCurrentDir, "*.xlsx");
    if(strFilePath.isEmpty() || !QFile::exists(strFilePath))
        return;
#endif
    qDebug()<<"更新故障码表:"<<strFilePath;

    QString strLocalPath = strCurrentDir + m_strXlsxName;
    if(!CopyQFile(strFilePath, strLocalPath))
    {
        ShowInformation(this, m_strTipsText, tr("故障码表更新失败,文件拷贝出错"));
        return;
    }

    m_iReadType = 1;
    m_pUpdateBar->setVisible(true);
    _ReadXlsx(strLocalPath);
}

void CFaultCodeManager::_UpdateEnd(const QString &strXlsxPath, const QMap<int, QStringList> &iCodeMap)
{
    if(iCodeMap.isEmpty())
    {
        m_pUpdateBar->setVisible(false);
        ShowInformation(this, m_strTipsText, tr("故障码表更新失败,请检查表格"));
        return;
    }

    m_iCodeInfoMap = iCodeMap;
    _ReflashCode();
    CopyQFile(strXlsxPath, m_strXlsxPath);
    m_pTableWidget->scrollToTop();
    m_pUpdateBar->setVisible(false);
    ShowSuccess(this, m_strTipsText, tr("故障码表更新成功"));
}

void CFaultCodeManager::_InitWidget()
{
    QStringList strTitleList = {tr("序号"), tr("故障码"), tr("故障等级"), tr("故障单元"),
                                tr("故障描述"), tr("工程描述"), tr("故障处理")};
    m_strTitleList = strTitleList;
    m_pTableWidget = new QTableWidget;
    m_pTableWidget->setColumnCount(strTitleList.size());
    m_pTableWidget->setHorizontalHeaderLabels(strTitleList);

    QHeaderView* pVerticalHeader = m_pTableWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(50);
    QHeaderView* pHorizontalHeader = m_pTableWidget->horizontalHeader();
    pHorizontalHeader->resizeSection(0, 70);
    pHorizontalHeader->resizeSection(1, 100);
    pHorizontalHeader->resizeSection(2, 100);
    pHorizontalHeader->resizeSection(3, 100);
    pHorizontalHeader->setSectionResizeMode(4, QHeaderView::Stretch);
    pHorizontalHeader->setSectionResizeMode(5, QHeaderView::Stretch);
    pHorizontalHeader->setSectionResizeMode(6, QHeaderView::Stretch);

    m_pTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pTableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_pTableWidget->setShowGrid(true);
    m_pTableWidget->setFocusPolicy(Qt::NoFocus);

    m_pCodeLineEdit = new CLineEdit;
    m_pCodeLineEdit->setFixedSize(100, 50);
    m_pCodeLineEdit->setPlaceholderText(tr("故障码"));
    m_pCodeLineEdit->setInputMethodHints(Qt::ImhDigitsOnly);

    int iBtnWidth = 100;
    if(eLanguage_Spanish == gk_iLanguage)
        iBtnWidth = 120;
    else if(eLanguage_German == gk_iLanguage)
        iBtnWidth = 170;
    else if(eLanguage_Italian == gk_iLanguage)
        iBtnWidth = 180;

    QStringList strBtnList = {tr("查询"), tr("详情"), tr("更新")};
    for(int i=0; i<strBtnList.size(); i++)
    {
        QPushButton *pBtn = new QPushButton(strBtnList.at(i));
        pBtn->setFixedSize(iBtnWidth, 50);
        pBtn->setProperty("index", i);
        m_pBtnList.push_back(pBtn);
        connect(pBtn, &QPushButton::clicked, this, &CFaultCodeManager::_SlotListBtn);
    }
}

void CFaultCodeManager::_InitLayout()
{
    QHBoxLayout *pBottomLayout = new QHBoxLayout;
    pBottomLayout->setMargin(0);
    pBottomLayout->setSpacing(15);
    pBottomLayout->addWidget(m_pCodeLineEdit);
    for(int i=0; i<m_pBtnList.size(); i++)
        pBottomLayout->addWidget(m_pBtnList.at(i));
    pBottomLayout->addStretch(1);

    QVBoxLayout *pMainLayout = new QVBoxLayout;
    pMainLayout->setMargin(0);
    pMainLayout->addWidget(m_pTableWidget);
    pMainLayout->addSpacing(10);
    pMainLayout->addLayout(pBottomLayout);
    this->setLayout(pMainLayout);
}
