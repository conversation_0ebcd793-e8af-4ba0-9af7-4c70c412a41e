#ifndef CPROJECTDB_H
#define CPROJECTDB_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-08
  * Description: 历史表 样本表 试剂卡表
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QObject>
#include "CSqliteDBBase.h"
#include "PublicParams.h"
#include "HistoryPage/CHistorySearchWidget.h"

class CProjectDB : public QObject , public CSqliteDBBase
{
    Q_OBJECT
public:
    static CProjectDB *GetInstance();
    virtual ~CProjectDB();

signals:
    void SignalAddHistoryID(int iHistoryID);
    void SignalUpdateHistoryID(int iHistoryID);
    void SignalDelHistoryID(int iHistoryID);
    void SignalRefreshHistory();

public:
    /******* history  ********/
    bool GetHistoryIDByTestTime(const QString &strTestTime, int &iHistoryID);
    bool GetAllHistoryID(QList<int> &iIDList);
    int GetHistoryAllDataCount();
    int  AddHistoryData(const SResultInfoStruct &sResult);
    bool DeleteHistoryData(int iHistoryID);
    bool UpdateHistroyTestStatus(int iHistoryID, int iStatus);
    bool UpdateHistroyTestData(const SResultInfoStruct &sResult);
    bool UpdateHistoryReviewData(const SResultInfoStruct &sResult);
    bool UpdateHistroyMeltingTestData(const SResultInfoStruct &sResult);

    bool GetHistoryData(int iHistoryID, SResultInfoStruct &sResult);
    bool GetHistoryData(const QString &strCardID, const QString &strTestTime, SResultInfoStruct &sResult);
    bool GetHistoryPageData(int iPage, int iOffSet, QList<SResultInfoStruct> &sResultList);
    bool IsCardIDUsed(const QString &strCardID);

    bool GetHistorySearchID(const SHistroySearchStruct &sSearchStruct, QList<int> &iIDList);
    int GetHistorySearchCount(const SHistroySearchStruct &sSearchStruct);
    bool GetHistorySearchPageData(const SHistroySearchStruct &sSearchStruct,
                                  int iPage, int iOffSet, QList<SResultInfoStruct> &sResultList);

    /******* sample  ********/
    bool AddSampleData(const SSampleInfoStruct &sSample);
    bool GetSampleData(const QString &strProject, const QString &strSampleID, const QString &strTestTime, SSampleInfoStruct &sSample);

    /******* card  ********/
    bool AddCardData(const SCardInfoStruct &sCard);
    bool GetCardData(const QString &strCardID, const QString &strTestTime, SCardInfoStruct &sCard);
    bool IsCardUsed(const QString &strCardID, const QString &strCardLot);

    bool ClearDB();

private:
    CProjectDB();

    QString _GetSearchCmd(const SHistroySearchStruct &sSearchStruct, const QString &strSearch);

    void _HistoryList2Struct(const QStringList &strList, SResultInfoStruct &sResult);
    void _SampleList2Struct(const QString &strProject, const QStringList &strList, SSampleInfoStruct &sSample);
    void _CardList2Struct(const QStringList &strList, SCardInfoStruct &sCard);

    void _InitHistroyTable();
    void _InitSampleTable();
    void _InitCardTable();

private:
    static CProjectDB *m_spInatance;

    Q_DISABLE_COPY(CProjectDB)
};

#endif // CPROJECTDB_H
