#ifndef CIPLABELLINEEDIT_H
#define CIPLABELLINEEDIT_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-10-31
  * Description: QLabel-QLinedit IP: ***********
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include "CLineEdit.h"

class CIPLabelLineEdit : public QWidget
{
    Q_OBJECT
public:
    CIPLabelLineEdit(const QString& strName, const QStringList &strList = QStringList(), QWidget *parent = nullptr);
    ~CIPLabelLineEdit();

    QStringList GetValueList() const;
    void SetValueList(const QStringList &strList);

    void SetPlaceholderText(const QStringList &strList);
    void SetPointLabelVisable(bool bVisable);

    void ResetLabelSize(int iWidth, int iHeight);
    void ResetLineEditSize(int iWidth, int iHeight);
    void SetLineTextEnable(bool bEnable);
    void ClearEdit();
private:
    void _InitWidget();

private:
    QLabel *m_pLabel;
    QList<QLabel *> m_pPointLabelList;
    QList<CLineEdit *> m_pLineEditList;

    QString m_strName;
    QList<QString> m_strValueList;
};

#endif // CIPLABELLINEEDIT_H
