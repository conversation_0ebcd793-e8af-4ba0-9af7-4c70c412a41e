#include "CLabelComboBox.h"
#include <QListView>
#include <QHBoxLayout>
#include <QLineEdit>
#include <QStyleFactory>

CLabelComboBox::CLabelComboBox(const QString &strText, const QStringList &strList, int iSpacing,
                               QBoxLayout::Direction eDirection, QWidget *parent)
    : QWidget(parent)
{
    m_pLabel = new QLabel(strText);

    m_pComboBox = new QComboBox;
    m_pComboBox->setFixedSize(303, 56);
    m_pComboBox->setView(new QListView);
    m_pComboBox->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_pComboBox->setMaxVisibleItems(10);
    m_pComboBox->setStyle(QStyleFactory::create("Windows"));

    m_pComboBox->addItems(strList);

    connect(m_pComboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(SlotCurrentIndexChanged(int)));
    connect(m_pComboBox, SIGNAL(currentTextChanged(const QString &)), this, SLOT(SlotCurrentTextChanged(const QString &)));

    QBoxLayout* pLayout = nullptr;
    if(QBoxLayout::Direction::LeftToRight == eDirection)
        pLayout = new QHBoxLayout;
    else
        pLayout = new QVBoxLayout;

    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    //pLayout->addStretch(1);
    pLayout->addWidget(m_pLabel);
    pLayout->addSpacing(iSpacing);
    pLayout->addWidget(m_pComboBox);
    //pLayout->addStretch(1);
    this->setLayout(pLayout);
}

CLabelComboBox::~CLabelComboBox()
{

}

void CLabelComboBox::ResetLabelSize(int iWidth, int iHeight)
{
    m_pLabel->setFixedSize(iWidth, iHeight);
}

void CLabelComboBox::ResetComboBoxSize(int iWidth, int iHeight)
{
    m_pComboBox->setFixedSize(iWidth, iHeight);
}

void CLabelComboBox::SetComboBoxList(const QStringList &strList)
{
    m_pComboBox->clear();
    m_pComboBox->addItems(strList);
}

void CLabelComboBox::SetLabelFixedSize(int iWidth, int iHeight)
{
    m_pLabel->setFixedSize(iWidth, iHeight);
}

void CLabelComboBox::SetComboBoxMinSize(int iWidth, int iHeight)
{
    m_pComboBox->setMinimumSize(iWidth, iHeight);
}

void CLabelComboBox::SetComboBoxFixedSize(int iWidth, int iHeight)
{
    m_pComboBox->setFixedSize(iWidth, iHeight);
}

void CLabelComboBox::SetLineEditEnabled(bool bEdit)
{
    m_pComboBox->setEditable(bEdit);
}

void CLabelComboBox::SetLineEditQss(const QString &strQss)
{
    if(m_pComboBox->isEditable())
        m_pComboBox->lineEdit()->setStyleSheet(strQss);
}

void CLabelComboBox::SetMaxVisibleItems(int iItems)
{
    m_pComboBox->setMaxVisibleItems(iItems);
}

int CLabelComboBox::GetCurrentIndex() const
{
    return m_pComboBox->currentIndex();
}

void CLabelComboBox::SetCurrentIndex(int index)
{
    m_pComboBox->setCurrentIndex(index);
}

QString CLabelComboBox::GetCurrentText() const
{
    return m_pComboBox->currentText();
}

void CLabelComboBox::SetCurrentText(QString strText)
{
    m_pComboBox->setCurrentText(strText);
}

void CLabelComboBox::SlotCurrentIndexChanged(int index)
{
    emit SignalCurrentIndexChanged(index);
}

void CLabelComboBox::SlotCurrentTextChanged(const QString &strText)
{
    emit SignalCurrentTextChanged(strText);
}

CVLabelComboBox::CVLabelComboBox(const QString &strText, const QStringList &strList,
                                 int iSpacing, QWidget *parent)
    : CLabelComboBox(strText, strList, iSpacing, QBoxLayout::Direction::TopToBottom, parent)
{

}

CVLabelComboBox::~CVLabelComboBox()
{

}

CHLabelComboBox::CHLabelComboBox(const QString &strText, const QStringList &strList,
                                 int iSpacing, QWidget *parent)
    : CLabelComboBox(strText, strList, iSpacing, QBoxLayout::Direction::LeftToRight, parent)
{

}

CHLabelComboBox::~CHLabelComboBox()
{

}
