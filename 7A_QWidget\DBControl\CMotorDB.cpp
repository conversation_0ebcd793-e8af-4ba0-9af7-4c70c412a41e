#include "CMotorDB.h"
#include "PublicConfig.h"

CMotorDB &CMotorDB::GetInstance()
{
    static CMotorDB motorDB;
    return motorDB;
}

CMotorDB::CMotorDB()
    : CSqliteDBBase(CPublicConfig::GetInstance()->GetMotorDBPath(), gk_strMotorDBConnect)
{
    _InitMotorCmdTable();
    _InitMotorCompensateTable();
    _InitMotorParamTable();
}

CMotorDB::~CMotorDB()
{

}

bool CMotorDB::AddOneCmd(const QStringList &strDataList)
{
    if(5 != strDataList.size())
        return false;

    return AddOneCmd(strDataList.at(0), strDataList.at(1), strDataList.at(2), strDataList.at(3), strDataList.at(4));
}

bool CMotorDB::AddOneCmd(const QString &strCmdID, const QString &strCmdName, const QString &strCmdText,
                         const QString &strCmdParam, const QString &strCmdContent)
{
    if(strCmdID.isEmpty())
        return false;

    QString strSql = QString("select * from cmd_table where cmd_id = '%1'").arg(strCmdID);
    QList<QStringList> strList;
    if(!_QueryDB(strSql, strList))
        return false;

    if(strList.isEmpty())
    {
        strSql = QString("insert into cmd_table (cmd_id,cmd_name,cmd_text,cmd_param,cmd_content) "
                         "values ('%1','%2','%3','%4','%5')")
                .arg(strCmdID).arg(strCmdName).arg(strCmdText).arg(strCmdParam).arg(strCmdContent);
        return _ExecuteDB(strSql);
    }

    strSql = QString("update cmd_table set cmd_name = '%1',cmd_text = '%2',cmd_param = '%3',cmd_content = '%4' "
                     "where cmd_id = '%5'")
            .arg(strCmdName).arg(strCmdText).arg(strCmdParam).arg(strCmdContent).arg(strCmdID);
    return _ExecuteDB(strSql);
}

bool CMotorDB::DeleteOneCmd(const QString &strCmdID)
{
    QString strSql = QString("delete from cmd_table where cmd_id = '%1'").arg(strCmdID);
    return _ExecuteDB(strSql);
}

bool CMotorDB::DeleteAllCmd()
{
    QStringList strSqlList = {"delete from cmd_table", "delete from sqlite_sequence where name ='cmd_table'"};
    return _ExecuteDB(strSqlList);
}

QList<QStringList> CMotorDB::GetCmdIDNameList()
{
    QString strSql = QString("select cmd_id,cmd_name from cmd_table order by cmd_id");
    QList<QStringList> strList;
    _QueryDB(strSql, strList);
    return strList;
}

QMap<QString, QString> CMotorDB::GetCmdIDNameMap()
{
    QMap<QString,QString> strMap;
    QString strCmd = QString("select cmd_id,cmd_text from cmd_table");
    QList<QStringList> strList;
    if(_QueryDB(strCmd,strList))
    {
        for(int i=0; i<strList.size(); i++)
        {
            QStringList one = strList.at(i);
            if(one.size() < 2)
                continue;

            QString id   = one.at(0);
            QString text = one.at(1);
            if(text.isEmpty())
                strMap.insert(id, id);
            else
                strMap.insert(id, text + "_" + id);
        }
    }
    return strMap;
}

QStringList CMotorDB::GetCmdInfoByCmdID(const QString &strCmdID)
{
    QString strSql = QString("select * from cmd_table where cmd_id = '%1'").arg(strCmdID);
    QList<QStringList> strList;
    _QueryDB(strSql, strList);
    return _GetRowValueList(strList);
}

QString CMotorDB::GetCmdIDByCmdName(const QString &strCmdName)
{
    QString strSql = QString("select cmd_id from cmd_table where cmd_name = '%1'").arg(strCmdName);
    QList<QStringList> strList;
    _QueryDB(strSql, strList);
    return _GetFirstValue(strList);
}

QString CMotorDB::GetCmdNameByCmdID(const QString &strCmdID)
{
    QString strSql = QString("select cmd_name from cmd_table where cmd_id = '%1'").arg(strCmdID);
    QList<QStringList> strList;
    _QueryDB(strSql, strList);
    return _GetFirstValue(strList);
}

QString CMotorDB::GetCmdTextByCmdID(const QString &strCmdID)
{
    QString strSql = QString("select cmd_text from cmd_table where cmd_id = '%1'").arg(strCmdID);
    QList<QStringList> strList;
    _QueryDB(strSql, strList);
    return _GetFirstValue(strList);
}

bool CMotorDB::DeleteMotorCompensate(int iMotorIndex)
{
    QString strSql = QString("delete from compensate_table where motor_index = '%1'").arg(iMotorIndex);
    return _ExecuteDB(strSql);
}

bool CMotorDB::AddOneCompensate(int iMotorIndex, int iCompensateIndex, const QString &strName, const QString &strValue)
{
    if(iMotorIndex < 0)
        return false;

    QString strSql = QString("insert into compensate_table (motor_index,compensate_index,name,value) "
                             "values ('%1','%2','%3','%4')")
            .arg(iMotorIndex).arg(iCompensateIndex).arg(strName).arg(strValue);
    //return _ExecuteDB(strSql);

#if 1
    strSql = QString("select * from compensate_table where motor_index = '%1'"
                     " and compensate_index = '%2'").arg(iMotorIndex).arg(iCompensateIndex);
    QList<QStringList> strList;
    if(!_QueryDB(strSql, strList))
        return false;

    if(strList.isEmpty())
    {
        strSql = QString("insert into compensate_table (motor_index,compensate_index,name,value) "
                         "values ('%1','%2','%3','%4')")
                .arg(iMotorIndex).arg(iCompensateIndex).arg(strName).arg(strValue);
        return _ExecuteDB(strSql);
    }

    strSql = QString("update compensate_table set name = '%1',value = '%2' "
                     "where motor_index = '%3' and compensate_index = '%4'")
            .arg(strName).arg(strValue).arg(iMotorIndex).arg(iCompensateIndex);
    return _ExecuteDB(strSql);
#endif
}

bool CMotorDB::AddOneCompensate(QStringList strInfoList)
{
    if(4 != strInfoList.size())
        return false;

    int iMotor = strInfoList.at(0).toInt();
    int iCompen = strInfoList.at(1).toInt();
    QString strName = strInfoList.at(2);
    QString strValue = strInfoList.at(3);

    return AddOneCompensate(iMotor, iCompen, strName, strValue);
}

QStringList CMotorDB::GetCompensateNameListByMotorIndex(int iMotorIndex)
{
    QString strSql = QString("select name from compensate_table where motor_index = '%1'").arg(iMotorIndex);
    QList<QStringList> strList;
    _QueryDB(strSql, strList);
    return _GetColumnValueList(strList);
}

QList<QStringList> CMotorDB::GetCompensateInfoByMotorIndex(int iMotorIndex)
{
    QString strSql = QString("select compensate_index,name,value from compensate_table where motor_index = '%1'").arg(iMotorIndex);
    QList<QStringList> strList;
    _QueryDB(strSql, strList);
    return strList;
}

QString CMotorDB::GetParamValueByMotorIndexAndType(int iMotorIndex, int iType)
{
    QString strSql = QString("select value from param_table where motor_index = '%1' and type = '%2'")
            .arg(iMotorIndex).arg(iType);
    QList<QStringList> strList;
    _QueryDB(strSql, strList);
    return _GetFirstValue(strList);
}

QString CMotorDB::GetParamValueByMotorIndexAndParam8(int iMotorIndex, int iParam8)
{
    QString strSql = QString("select value from param_table where motor_index = '%1' and param8 = '%2'")
            .arg(iMotorIndex).arg(iParam8);
    QList<QStringList> strList;
    _QueryDB(strSql, strList);
    return _GetFirstValue(strList);
}

bool CMotorDB::AddOneParam(const QStringList &strDataList)
{
    if(4 != strDataList.count())
        return false;

    QString index = strDataList.at(0);
    QString type = strDataList.at(1);
    QString param = strDataList.at(2);
    QString value = strDataList.at(3);

    QString strSql = QString("select * from param_table where motor_index = '%1'"
                             " and type = '%2' and param8 = '%3'").arg(index).arg(type).arg(param);
    QList<QStringList> strList;
    if(!_QueryDB(strSql,strList))
        return false;

    if(strList.isEmpty())
    {
        strSql = QString("insert into param_table (motor_index,type,param8,value) "
                         "values ('%1','%2','%3','%4')").arg(index).arg(type).arg(param).arg(value);
        return _ExecuteDB(strSql);
    }

    strSql = QString("update param_table set value = '%1' where motor_index = '%2'"
                     " and type = '%3' and param8 = '%4'").arg(value).arg(index).arg(type).arg(param);
    return _ExecuteDB(strSql);
}

void CMotorDB::_InitMotorCmdTable()
{
    QString strCreateTable = "create table if not exists cmd_table ("
                             "id integer not null primary key autoincrement,"
                             "cmd_id varchar,"
                             "cmd_name varchar,"
                             "cmd_text varchar,"
                             "cmd_param varchar,"
                             "cmd_content varchar)";
    _ExecuteDB(strCreateTable);
}

void CMotorDB::_InitMotorCompensateTable()
{
    QString strCreateTable = "create table if not exists compensate_table ("
                             "id integer not null primary key autoincrement,"
                             "motor_index varchar,"
                             "compensate_index varchar,"
                             "name varchar,"
                             "value varchar)";
    _ExecuteDB(strCreateTable);
}

void CMotorDB::_InitMotorParamTable()
{
    QString strCreateTable = "create table if not exists param_table ("
                             "id integer not null primary key autoincrement,"
                             "motor_index varchar,"
                             "type varchar,"
                             "param8 varchar,"
                             "value varchar)";
    _ExecuteDB(strCreateTable);
}
