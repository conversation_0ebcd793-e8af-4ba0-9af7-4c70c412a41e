#ifndef CMYLOG_H
#define CMYLOG_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-05-06
  * Description: 日志模块
  * -------------------------------------------------------------------------
  * History: 1.使用新的压缩库
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QDate>
#include <QMutex>
#include <QFile>
#include <QMap>
#include <QTimer>
#include <QThread>

class CMylog : public QObject
{
    Q_OBJECT
public:
    static CMylog &GetInstance();
    ~CMylog();

public:
    enum LogLevel {eLog_Debug = 0, eLog_Info, eLog_Warning, eLog_Error, eLog_Fatal, eLog_LowMachine};

    struct LogStruct
    {
        LogLevel level;
        QString strLog;
    };

    void AddLog(const LogStruct &sLogStruct);

signals:
    void SignalWriteLog();
    void SignalInit();

private slots:
    void _SlotInit();
    void _SlotExit();
    void _SlotWriteLog();
    void _SlotWriteTimeout();
    void _SlotRunTimeout();

private:
    CMylog();
    void _ZipLog();
    void _ZipFile(QString strFilePath);
    void _PrintfLog(const QString &strLog);
    void _CheckFileSize(QFile *pFile);

private:
    bool m_bPrint;
    int m_iKeepDays;
    int m_iMaxSize;
    QString m_strLogDir;
    QStringList m_strFileDirList;
    QList<LogStruct> m_sLogStructList;

    QDate m_qCurrentDate;
    QMutex m_qMutex;
    QMap<LogLevel, QFile *> m_filePtrMap;

    QTimer *m_pWriteTimer;
    QTimer *m_pRunTimer;
    QThread m_qThread;
};

#endif // CMYLOG_H
