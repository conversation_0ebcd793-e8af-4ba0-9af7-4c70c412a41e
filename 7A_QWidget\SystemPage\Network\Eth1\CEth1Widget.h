﻿#ifndef CETH1WIDGET_H
#define CETH1WIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-01-16
  * Description: Eth1 UI
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QTimer>
#include <QWidget>
#include <QPushButton>
#include <QRadioButton>

#include "CLabelLineEdit.h"
#include "CIPLabelLineEdit.h"
#include "CHLabelTitleWidget.h"

class CEth1Widget : public QWidget
{
    Q_OBJECT
public:
    explicit CEth1Widget(QWidget *parent = nullptr);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;

public slots:
    void SlotWiFiOpen(bool bWiFiOpen);
    void SlotWlanIPInfoList(QStringList strWlanList);

private slots:
    void _SlotAutoBtn();
    void _SlotManualBtn();
    void _SlotSaveBtn();
    void _SlotGetAutoIPTimer();

private:
    void _ReadCfg();
    void _ResetUI();

private:
    void _InitWidget();
    void _InitLayout();

private:
    CHLabelTitleWidget *m_pTitleWidget;
    CIPLabelLineEdit *m_pIPWidget;
    CIPLabelLineEdit *m_pNetmaskWidget;
    CIPLabelLineEdit *m_pGatewayWidget;
    QLabel *m_pConnectLabel;
    QRadioButton *m_pAutoRadioBtn,*m_pManualRadioBtn;
    QPushButton *m_pSaveBtn;

    bool m_bWiFiOpen;
    QString m_strTipsText;

    QTimer *m_pGetAutoIPTimer;
};

#endif // CETH1WIDGET_H
