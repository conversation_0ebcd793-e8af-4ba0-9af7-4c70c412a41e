#ifndef CHRMWIDGET_H
#define CHRMWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-11-14
  * Description: HRM
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QStackedWidget>
#include "CHBtnTitleWidget.h"

class CHrmCurve;
class CHrmTiming;
class CHrmCalibrate;

class CHrmWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CHrmWidget(QWidget *parent = nullptr);
    ~CHrmWidget();

private slots:
    void _SlotTitleChanged(int);

private:
    void _InitWidget();

private:
    CHrmCurve *m_pHrmCurve;
    CHrmTiming *m_pHrmTiming;
    CHrmCalibrate *m_pHrmCalibrate;
    CHBtnTitleWidget *m_pHBtnTitle;
    QStackedWidget *m_pStackedWidget;
};

#endif // CHRMWIDGET_H
