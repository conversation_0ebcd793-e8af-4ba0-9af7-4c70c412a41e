#ifndef CHOMESELECTPROJECTWIDGET_H
#define CHOMESELECTPROJECTWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-01
  * Description: 主页创建测试 选择项目
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QCheckBox>
#include <QComboBox>
#include <QListWidget>
#include <QPushButton>

#include "CHLabelTitleWidget.h"

class CProjectItemWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CProjectItemWidget(int index, const QString &strProject, const QStringList &strTimingList,
                                const QStringList &strTecList, QWidget *parent = nullptr);

public:
    void SetTimingTecIndex(int iTimingIndex, int iTecIndex);
    void SetChecked(bool bChecked);
    bool GetChecked() const;
    int GetIndex() const;
    void SetShowIndex(int index);
    QString GetProjectName() const;
    QString GetTimingName() const;
    QString GetTecName() const;

protected:
    void showEvent(QShowEvent *pEvent) override;
    void hideEvent(QHideEvent *pEvent) override;

signals:
    void SignalItemChecked(int index);

private slots:
    void _SlotCheckBox();

private:
    QLabel *m_pIndexLabel;
    QCheckBox *m_pCheckBox;
    QLabel *m_pProjectLabel;
    QComboBox *m_pTimingComboBox;
    QComboBox *m_pPCRComboBox;

    int m_index;

    int m_iRawTimingIndex, m_iRawTecIndex;
};

class CHomeSelectProjectWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CHomeSelectProjectWidget(QWidget *parent = nullptr);

protected:
    void paintEvent(QPaintEvent* pEvent) override;
    void showEvent(QShowEvent *pEvent) override;
    void hideEvent(QHideEvent *pEvent) override;

signals:
    void SignalSelectProject(const QString &strProjectName, const QString &strTimingName, const QString &strTecName);

private slots:
    void _SlotConfirmBtn();
    void _SlotCancelBtn();
    void _SlotCurrentRowChanged(int iRow);
    void _SlotItemChecked(int iItemIndex);

private:
    void _InitWidget();
    void _InitLayout();

private:
    QLabel *m_pBackgroundLabel;
    CHLabelTitleWidget *m_pCHLabelTitleWidget;
    QListWidget *m_pListWidget;
    QPushButton *m_pConfirmBtn, *m_pCancelBtn;
};

#endif // CHOMESELECTPROJECTWIDGET_H
