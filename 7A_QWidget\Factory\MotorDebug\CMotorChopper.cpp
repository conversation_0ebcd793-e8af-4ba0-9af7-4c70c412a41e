#include "CMotorChopper.h"
#include <QBoxLayout>
#include <QGridLayout>
#include <QtMath>
#include "CMessageBox.h"

CMotorChopper::CMotorChopper(QWidget *parent) : QWidget(parent)
{
    Register2Map(Method_RRCHOP);
    Register2Map(Method_SRCHOP);
    Register2Map(Method_CRCHOP);

    _InitWidget();

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalSoftTypeChanged, this, &CMotorChopper::SlotSoftTypeChanged);
}

CMotorChopper::~CMotorChopper()
{
    UnRegister2Map(Method_RRCHOP);
    UnRegister2Map(Method_SRCHOP);
    UnRegister2Map(Method_CRCHOP);
}

void CMotorChopper::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    qDebug()<<QString("%1#斩波器").arg(iMachineID + 1)<<iMethodID<<iResult;
    if(Method_RRCHOP == iMethodID)
    {
        QStringList strList = qVarData.toString().split(SPLIT_IN_CMD);
        if(strList.size() < 2)
            return;

        m_pComboBoxList.at(0)->SetCurrentIndex(strList.at(0).toInt());
        QList<int> iValueList = _GetValueFromData(strList.at(1).toUInt());
        iValueList.push_front(strList.at(0).toInt()); // add motor id
        iValueList.insert(4, 0); // fd3 disfdcc 用不到
        iValueList.insert(5, 0);

        for(int i=0; i<iValueList.size(); i++)
        {
           m_pComboBoxList.at(i)->SetCurrentText(QString::number(iValueList.at(i)));
        }
    }
}

void CMotorChopper::SlotSoftTypeChanged(int iSoftType)
{
    Q_UNUSED(iSoftType);
    m_pComboBoxList.at(0)->SetComboBoxList(CPublicConfig::GetInstance()->GetMotorNameList());
}

void CMotorChopper::showEvent(QShowEvent *pEvent)
{
    QWidget::showEvent(pEvent);
}

QList<int> CMotorChopper::_GetValueFromData(uint iData)
{
    QList<int> iReturn;
    quint32 uiValue = iData;
    quint32 qu8Value = uiValue  << 28 >> 28;
    iReturn.push_back(qu8Value);
    qu8Value = uiValue  << 25 >> 29;
    iReturn.push_back(qu8Value);
    qu8Value = uiValue  << 21 >> 28;
    iReturn.push_back(qu8Value);
    qu8Value = uiValue  << 17 >> 31;
    iReturn.push_back(qu8Value);
    qu8Value = uiValue  << 15 >> 30;
    iReturn.push_back(qu8Value);
    qu8Value = uiValue  << 13 >> 31;
    iReturn.push_back(qu8Value);
    qu8Value = uiValue  << 12 >> 31;
    iReturn.push_back(qu8Value);
    qu8Value = uiValue  << 8 >> 28;
    iReturn.push_back(qu8Value);
    qu8Value = uiValue  << 4 >> 28;
    iReturn.push_back(qu8Value);
    qu8Value = uiValue  << 3 >> 31;
    iReturn.push_back(qu8Value);
    qu8Value = uiValue  << 2 >> 31;
    iReturn.push_back(qu8Value);
    qu8Value = uiValue  << 1 >> 31;
    iReturn.push_back(qu8Value);
    qu8Value = uiValue  >> 31;
    iReturn.push_back(qu8Value);

    return iReturn;
}

void CMotorChopper::_SlotBtnClicked()
{
    QPushButton *pBtn = dynamic_cast<QPushButton *>(sender());
    if(nullptr == pBtn)
        return;

    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    int iMotorID = m_pComboBoxList.at(0)->GetCurrentIndex();
    int index = pBtn->property("index").toInt();
    switch (index)
    {
    case 0:
    {
        QVariant qVarData = QString::number(iMotorID);
        QString strCmd = GetJsonCmdString(Method_RRCHOP, qVarData);
        qDebug()<<QString("%1#读取斩波器参数:%2").arg(iMachineID + 1).arg(strCmd);
        SendJsonCmd(iMachineID, Method_RRCHOP, strCmd);
        break;
    }
    case 1:
    {
        QList<int> q32ValueList;
        for(int i=1; i<m_pComboBoxList.size(); i++)
        {
            if(4 != i && 5 != i) // fd3 disfdcc 用不到
                q32ValueList.push_back(m_pComboBoxList.at(i)->GetCurrentText().toInt());
        }
        qDebug() << Q_FUNC_INFO << q32ValueList;

        quint32 qu32Value = q32ValueList[0]  | (q32ValueList[1] << 4) | (q32ValueList[2] << 7)
                | (q32ValueList[3] << 14) | (q32ValueList[4] << 15) | (q32ValueList[5] << 18)
                | (q32ValueList[6] << 19) | (q32ValueList[7] << 20) | (q32ValueList[8] << 24)
                | (q32ValueList[9] << 28) | (q32ValueList[10] << 29)| (q32ValueList[11] << 30)
                | (q32ValueList[12] << 31);
        QVariant qVarString = QString("%1,%2").arg(m_pComboBoxList.at(0)->GetCurrentIndex()).arg(qu32Value);
        QString strCmd = GetJsonCmdString(Method_SRCHOP, qVarString);
        qDebug()<<QString("%1#设置斩波器参数:%2").arg(iMachineID + 1).arg(strCmd);
        SendJsonCmd(iMachineID, Method_SRCHOP, strCmd);
        break;
    }
    case 2:
    {
        //QVariant qVar = QString("2,1083181041");
        //receiveMachineCmdReplay(0, Method_RRCHOP ,true ,qVar);
        break;
    }
    case 3:
        break;
    case 4:
    {
        int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定要清除斩波器参数吗"));
        if(QMessageBox::Yes != iBtnType)
            return;

        for(int i=0; i<m_pComboBoxList.size(); i++)
            m_pComboBoxList.at(i)->SetCurrentIndex(0);

        QVariant qVarData = QString::number(iMotorID);
        QString strCmd = GetJsonCmdString(Method_CRCHOP, qVarData);
        qDebug()<<QString("%1#清除斩波器参数:%2").arg(iMachineID + 1).arg(strCmd);
        SendJsonCmd(iMachineID, Method_CRCHOP, strCmd);
        break;
    }
    default: break;
    }
}

void CMotorChopper::_InitWidget()
{
    QStringList strNameList = {tr("电机"),
                               "toff", "hstrt", "hend",
                               "fd3", "disfdcc","chm",
                               "tbl", "vhighs", "vhighms",
                               "tpdf", "MERS", "intpol",
                               "dedge", "diss2g", "diss2vs"};
    QStringList str15List;
    for(int i=0; i<=15; i++)
        str15List.push_back(QString::number(i));
    QStringList strMERS2List = {"0"};
    for(int i=0; i<10; i++)
        strMERS2List.push_back(QString::number(qPow(2, i)));

    QList<QStringList> strValueList;
    strValueList<<CPublicConfig::GetInstance()->GetMotorNameList()
               <<str15List.mid(1, 15)<<str15List.mid(0, 8)<<str15List
              <<str15List.mid(0, 1)<<str15List.mid(0, 1)<<str15List.mid(0, 2)
             <<str15List.mid(0, 4)<<str15List.mid(0, 2)<<str15List.mid(0, 2)
            <<str15List<<strMERS2List<<str15List.mid(0, 2)
           <<str15List.mid(0, 2)<<str15List.mid(0, 2)<<str15List.mid(0, 2);

    QGridLayout *pGridLayout = new QGridLayout;
    pGridLayout->setContentsMargins(20, 0, 20, 0);
    pGridLayout->setHorizontalSpacing(10);
    pGridLayout->setVerticalSpacing(20);

    for(int i=0; i<16; i++)
    {
        CLabelComboBox *pComboBox = new CLabelComboBox(strNameList.at(i), strValueList.at(i));
        pComboBox->SetComboBoxMinSize(170, 50);
        pComboBox->SetLabelFixedSize(90, 50);
        m_pComboBoxList.push_back(pComboBox);

        if(0 == i)
            pGridLayout->addWidget(pComboBox, 0, 0);
        else
            pGridLayout->addWidget(pComboBox, (i - 1) / 3 + 1, (i  - 1) % 3);
    }

    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxMinSize(110, 50);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(20);
    pBtnLayout->addSpacing(60);
    pBtnLayout->addWidget(m_pMachineComboBox);

    QStringList strBtnNameList = {tr("读取"), tr("设置"), tr("导入"), tr("导出"), tr("清除参数")};
    for(int i=0; i<strBtnNameList.size(); i++)
    {
        QPushButton *pBtn = new QPushButton(strBtnNameList.at(i));
        pBtn->setMinimumSize(110, 50);
        pBtn->setProperty("index", i);
        connect(pBtn, &QPushButton::clicked, this, &CMotorChopper::_SlotBtnClicked);

        pBtnLayout->addWidget(pBtn);
    }
    pBtnLayout->addSpacing(60);

    QVBoxLayout *pMainLayout = new QVBoxLayout;
    pMainLayout->setMargin(0);
    pMainLayout->addLayout(pGridLayout);
    pMainLayout->addSpacing(30);
    pMainLayout->addLayout(pBtnLayout);
    pMainLayout->addSpacing(30);
    pMainLayout->addStretch(1);
    this->setLayout(pMainLayout);
}
