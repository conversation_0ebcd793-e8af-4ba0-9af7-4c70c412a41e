#ifndef CUSERDB_H
#define CUSERDB_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-14
  * Description: 登录用户数据库管理
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QObject>
#include "CSqliteDBBase.h"

class CUserDB : public QObject , public CSqliteDBBase
{
    Q_OBJECT
public:
    static CUserDB *GetInstance();
    virtual ~CUserDB();

    bool AddUserData(const QStringList &strUserList);
    QStringList GetAllUserNameList();
    QStringList GetUserDataListByName(const QString &strUserName);
    QList<QStringList> GetAllUserDataList();
    QString FindUserPasswordByName(const QString &strUserName);
    bool UpdateUserPasswordNoteByName(const QString &strUserName, const QString &strPassword, const QString &strNote);
    bool DeleteUserByName(const QString &strUserName);
    bool DeleteAllUser();
    bool IsUserExistInDB(const QString &strUserName);
    bool IsUserEnabled(const QString &strUserName);

private:
    CUserDB();

private:
    static CUserDB *m_spInstance;

    Q_DISABLE_COPY(CUserDB)
};

#endif // CUSERDB_H
