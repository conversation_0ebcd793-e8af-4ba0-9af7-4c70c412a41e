#include "CUserNameComboBox.h"
#include <QListView>
#include <QLineEdit>
#include <QBoxLayout>
#include <QStyleFactory>

CUserNameComboBox::CUserNameComboBox(QWidget *parent) : QComboBox(parent)
{
    this->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    this->setMaxVisibleItems(10);
    this->setStyle(QStyleFactory::create("Windows"));

    m_pIconLabel = new QLabel;
    m_pIconLabel->setFixedSize(32, 32);
    m_pIconLabel->setCursor(QCursor(Qt::ArrowCursor));
    m_pIconLabel->setPixmap(QPixmap(":/image/ico/login/user.png"));

    this->setEditable(true);
    this->lineEdit()->setStyleSheet("QLineEdit{padding-left:70px;} QLineEdit:focus{padding-left:70px;}");

    QHBoxLayout *pIconLayout = new QHBoxLayout;
    pIconLayout->setMargin(0);
    pIconLayout->setSpacing(0);
    pIconLayout->addSpacing(24);
    pIconLayout->addWidget(m_pIconLabel);
    pIconLayout->addStretch(1);
    this->setLayout(pIconLayout);
}

CUserNameLineEdit::CUserNameLineEdit(QWidget *parent) : QLineEdit(parent)
{
    m_pIconLabel = new QLabel;
    m_pIconLabel->setFixedSize(32, 32);
    m_pIconLabel->setCursor(QCursor(Qt::ArrowCursor));
    m_pIconLabel->setPixmap(QPixmap(":/image/ico/login/user.png"));

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addSpacing(24);
    pLayout->addWidget(m_pIconLabel);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}
