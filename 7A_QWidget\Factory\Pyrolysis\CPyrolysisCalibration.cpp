#include "CPyrolysisCalibration.h"
#include <QBoxLayout>
#include <QJsonArray>
#include <QJsonObject>
#include <QJsonDocument>
#include "CMessageBox.h"

CPyrolysisCalibration::CPyrolysisCalibration(QWidget *parent) : QWidget(parent)
{
    QTime t1 = QTime::currentTime();

    Register2Map(Method_HTCALC);

    _InitWidget();

    qDebug()<<"HT校准页面构造时间:"<<t1.msecsTo(QTime::currentTime());
}

CPyrolysisCalibration::~CPyrolysisCalibration()
{
    UnRegister2Map(Method_HTCALC);
}

void CPyrolysisCalibration::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    Q_UNUSED(qVarData);
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if(Method_HTCALC == iMethodID)
    {
        if(0 == iResult)
            ShowSuccess(this, m_strTipsText, tr("%1#HT温度校准成功").arg(iMachineID + 1));
        else
            ShowError(this, m_strTipsText, tr("%1#HT温度校准失败").arg(iMachineID + 1));
    }
}

void CPyrolysisCalibration::_Slot2RouteBtn()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    QStringList strSrcDstList1 = m_pLysisOneCalibrate1->GetSrcDstList();
    QStringList strSrcDstList2 = m_pLysisOneCalibrate2->GetSrcDstList();

    if(strSrcDstList1.isEmpty() || strSrcDstList2.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("校准数据不能为空"));
        return;
    }

    QString strJson = QString("{\"method\":\"HTCALC\",\"id\":1029,"
                              "\"src1\":\"%1\",\"dst1\":\"%2\","
                              "\"src2\":\"%3\",\"dst2\":\"%4\"}")
            .arg(strSrcDstList1.at(0)).arg(strSrcDstList1.at(1))
            .arg(strSrcDstList2.at(0)).arg(strSrcDstList2.at(1));
    qDebug()<<QString("%1#HT 2路温度校准:").arg(iMachineID + 1)<<strJson;
    SendJsonCmd(iMachineID, Method_HTCALC, strJson);
}

void CPyrolysisCalibration::_Slot4RouteBtn()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    QStringList strSrcDstList1 = m_pLysisOneCalibrate1->GetSrcDstList();
    QStringList strSrcDstList2 = m_pLysisOneCalibrate2->GetSrcDstList();
    QStringList strSrcDstList3 = m_pLysisOneCalibrate3->GetSrcDstList();
    QStringList strSrcDstList4 = m_pLysisOneCalibrate4->GetSrcDstList();

    if(strSrcDstList1.isEmpty() || strSrcDstList2.isEmpty()
            || strSrcDstList3.isEmpty() || strSrcDstList4.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("校准数据不能为空"));
        return;
    }

    QString strJson = QString("{\"method\":\"HTCALC\",\"id\":1029,"
                              "\"src1\":\"%1\",\"dst1\":\"%2\","
                              "\"src2\":\"%3\",\"dst2\":\"%4\","
                              "\"src3\":\"%5\",\"dst3\":\"%6\","
                              "\"src4\":\"%7\",\"dst4\":\"%8\""
                              "}")
            .arg(strSrcDstList1.at(0)).arg(strSrcDstList1.at(1))
            .arg(strSrcDstList2.at(0)).arg(strSrcDstList2.at(1))
            .arg(strSrcDstList3.at(0)).arg(strSrcDstList3.at(1))
            .arg(strSrcDstList4.at(0)).arg(strSrcDstList4.at(1));
    qDebug()<<QString("%1#HT 4路温度校准:").arg(iMachineID + 1)<<strJson;
    SendJsonCmd(iMachineID, Method_HTCALC, strJson);
}

void CPyrolysisCalibration::_InitWidget()
{
    m_p2RouteBtn = new QPushButton(tr("2路校准"));
    m_p2RouteBtn->setFixedSize(120, 50);
    connect(m_p2RouteBtn, &QPushButton::clicked, this, &CPyrolysisCalibration::_Slot2RouteBtn);

    m_p4RouteBtn = new QPushButton(tr("4路校准"));
    m_p4RouteBtn->setFixedSize(120, 50);
    connect(m_p4RouteBtn, &QPushButton::clicked, this, &CPyrolysisCalibration::_Slot4RouteBtn);

    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(120, 50);

    m_pLysisOneCalibrate1 = new CLysisOneCalibrate(tr("第一路"));
    m_pLysisOneCalibrate2 = new CLysisOneCalibrate(tr("第二路"));
    m_pLysisOneCalibrate3 = new CLysisOneCalibrate(tr("第三路"));
    m_pLysisOneCalibrate4 = new CLysisOneCalibrate(tr("第四路"));

    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(0);
    pTopLayout->addStretch(1);
    pTopLayout->addWidget(m_pLysisOneCalibrate1);
    pTopLayout->addWidget(m_pLysisOneCalibrate2);
    pTopLayout->addWidget(m_pLysisOneCalibrate3);
    pTopLayout->addWidget(m_pLysisOneCalibrate4);
    pTopLayout->addStretch(1);

    QHBoxLayout *pBottomLayout = new QHBoxLayout;
    pBottomLayout->setSpacing(20);
    pBottomLayout->addStretch(1);
    pBottomLayout->addWidget(m_pMachineComboBox);
    pBottomLayout->addWidget(m_p2RouteBtn);
    pBottomLayout->addWidget(m_p4RouteBtn);
    pBottomLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addLayout(pTopLayout);
    pLayout->addSpacing(20);
    pLayout->addLayout(pBottomLayout);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}
