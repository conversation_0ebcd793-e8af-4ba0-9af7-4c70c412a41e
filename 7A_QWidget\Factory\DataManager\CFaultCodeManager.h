#ifndef CFAULTCODEMANAGER_H
#define CFAULTCODEMANAGER_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-01-22
  * Description: 故障码管理
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QPushButton>
#include <QTableWidget>

#include "CLineEdit.h"
#include "CBusyProgressBar.h"

class CFaultCodeManager : public QWidget
{
    Q_OBJECT
public:
    explicit CFaultCodeManager(QWidget *parent = nullptr);
    ~CFaultCodeManager();

signals:
    void SignalUpdateCodeMap(const QMap<int, QStringList> &map);

private slots:
    void _SlotDelayReadXlsx();
    void _SlotListBtn();

private:
    void _ReadXlsx(const QString &strXlsxPath);
    void _QueryCode();
    void _ReflashCode();
    void _DetailCode();
    void _UpdateCode();
    void _UpdateEnd(const QString &strXlsxPath, const QMap<int, QStringList> &iCodeMap);

private:
    void _InitWidget();
    void _InitLayout();

private:
    QStringList m_strTitleList;
    QTableWidget *m_pTableWidget;
    CLineEdit *m_pCodeLineEdit;
    QList<QPushButton *> m_pBtnList;
    CBusyProgressBar *m_pUpdateBar;

    int m_iReadType;
    QString m_strXlsxName;
    QString m_strXlsxPath;
    QMap<int, QStringList> m_iCodeInfoMap;

    QString m_strTipsText;
};

#endif // CFAULTCODEMANAGER_H
