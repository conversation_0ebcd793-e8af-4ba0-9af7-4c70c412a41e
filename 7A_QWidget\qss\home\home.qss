QLabel
{    
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    border: 0px solid red;
}
QLabel#BackgroundLabel
{
   border-radius: 32px;
   background-color: #FFF;
}

QLabel[step="yes"]#RectBackLabel
{
   border-radius: 20px;
   border: 0px solid red;
   background-color: transparent;
}

QLabel[step="no"]#RectBackLabel
{
   border-radius: 20px;
   border: 0px solid red;
   background-color: rgba(161, 171, 187, 50);
}

QLabel#TitleIconLabel
{
   border-radius: 3px;
   background-color: #3D78E5;
}
QLabel#TitleTextLabel
{
    color: #353E4E;
    font-size: 24px;
    font-weight: 500;
    font-family: "Source Han Sans CN";
}
QLabel#MovieLabel
{
    border-radius: 20px;
    background-color: #F3F8FF;
}
QLabel#DateLabel
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    padding-left: 20px;
    border-radius: 28px;
    border: 0px solid #A1ABBB;
    background-color: #F3F8FF;
}

QLabel#InfoLabel
{
    color: #3D78E5;
    font-size: 28px;
    font-family: "Source Han Sans CN";
    background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
        stop: 0 #FFF, stop: 0.5 rgba(89,144,255,122),stop: 1 #FFF);
}
QLabel#SepLabel
{
    background-color: #A1ABBB;
}
QLabel[step="yes"]#SpacingLabel
{
    color: #3D78E5;
    font-size: 32px;
    font-weight: bold;
    font-family: "Microsoft YaHei";
    border: 0px solid red;
}
QLabel[step="no"]#SpacingLabel
{
    color: #A1ABBB;
    font-size: 32px;
    font-weight: bold;
    font-family: "Microsoft YaHei";
    border: 0px solid red;
}
QLabel[step="yes"]#VIndexLabel
{
    color: #FFF;
    font-size: 24px;
    font-weight: bold;
    font-family: "Source Han Sans CN";
    border-radius: 20px;
    border: 0px solid #A1ABBB;
    background-color: #3D78E5;
}
QLabel[step="no"]#VIndexLabel
{
    color: #A1ABBB;
    font-size: 24px;
    font-weight: bold;
    font-family: "Source Han Sans CN";
    border-radius: 20px;
    border: 2px solid #A1ABBB;
    background-color: transparent;
}
QLabel[step="yes"]#VTextLabel
{
    color: #3D78E5;
    font-size: 24px;
    font-weight: bold;
    font-family: "Source Han Sans CN";
    border-radius: 0px;
    border: 0px solid #A1ABBB;
    background-color: transparent;
}
QLabel[step="no"]#VTextLabel
{
    color: #A1ABBB;
    font-size: 24px;
    font-weight: bold;
    font-family: "Source Han Sans CN";
    border-radius: 0px;
    border: 0px solid #A1ABBB;
    background-color: transparent;
}

QLineEdit
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    padding-left: 20px;
    padding-right: 20px;
    border-radius: 28px;
    border: 0px solid #A1ABBB;
    background-color: #F3F8FF;
}

QComboBox
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    padding-left: 20px;
    border-radius: 28px;
    background-color: #F3F8FF;
}
QCheckBox:focus{outline: none;}

QComboBox::drop-down
{
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 60px;
    font-weight: 500;
    border-left: 0px solid red;
}
QComboBox::down-arrow
{
    width: 32px;
    height: 32px;
    image: url(:/image/ico/login/commod.png);
    padding: 0px 20px 0px 0px;
}
QComboBox QAbstractItemView
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    selection-background-color: #248CEB;
}
QComboBox QAbstractItemView::item
{
    min-height: 56px;/*下拉列表的行高，也可以看做行距*/
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
}

QGroupBox
{
   border-radius: 32px;
   background-color: #FFF;
   border: 0px solid red;
}

QPushButton
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #8490FF, stop: 1 #3D78E5);
}
QPushButton:pressed
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #6670c7, stop: 1 #3361b8);
}
QPushButton:focus{outline: none;}

QPushButton#SelectBtn:disabled
{
   background-color: rgba(0, 0, 0, 50);
}

QPushButton#SelectBtn-German
{
   font-size: 18px;
}
QPushButton#SelectBtn-German:pressed
{
   font-size: 18px;
}
QPushButton#SelectBtn-German:disabled
{
   background-color: rgba(0, 0, 0, 50);
}
QPushButton#SelectBtn-Spanlish
{
   font-size: 16px;
}
QPushButton#SelectBtn-Spanlish:pressed
{
   font-size: 16px;
}
QPushButton#SelectBtn-Spanlish:disabled
{
   background-color: rgba(0, 0, 0, 50);
}

QPushButton#CancelBtn
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 2px solid #3D78E5;
   background-color: #FFF;
}

QPushButton#CancelBtn:hover
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 3px solid #3D78E5;
   background-color: #FFF;
}

QPushButton#CancelBtn:pressed
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 4px solid #3D78E5;
   background-color: #FFF;
}

QPushButton#StartTest
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: #E86C79;
}
QPushButton:pressed#StartTest
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: #BA5661;
}

QCheckBox
{
   color: #6B788F;
   font-size: 24px;
   font-family: "Source Han Sans CN";
   border: 0px solid red;
}
QCheckBox::indicator
{
   width: 28px;
   height: 28px;
   /* subcontrol-position:right  right;*/
}
QCheckBox::indicator::unchecked
{
   image: url(:/image/ico/history/uncheck.png);
}
QCheckBox::indicator::checked
{
   image: url(:/image/ico/history/check.png);
}

QListWidget
{
    color: #333;
    font-family: "Source Han Sans CN";
    font-size: 20px;
    outline: none;
    border: 1px solid #D6DFE9;
    border-radius: 0px;
    background-color: #FFF;
}
QListWidget::item
{
    margin: 0px;
    color: #333; /* text color */
    font-family: "Source Han Sans CN";
    font-size: 20px;
    outline: none;
    border-radius: 0px;
    border: 1px solid #D6DFE9;
    background-color: #FFF;
}
QListWidget::Item:hover,
QListWidget::Item:selected {
    border: 2px solid #498CE5;
    border-radius: 0px;
    background-color: #FFF;
}
QListWidget::Item:selected:!active {
    border: 1px solid #D6DFE9;
    border-radius: 0px;
    background-color: #FFF;
}

QStackedWidget
{
   border: 0px solid red;
}

QScrollBar:vertical
{
    width: 30px;
    background: #F0F0F0;
    padding-top: 30px;
    padding-bottom: 30px;
}

QScrollBar::handle:vertical
{
    width: 30px;
    background: #B6B6B6;
    min-height: 35px;
}
