#include "CPdfHelper.h"
#include <QFile>
#include <QDebug>
#include <QPixmap>
#include <QPainter>
#include <QPrinter>
#include <QPdfWriter>
#include <QDateTime>
/*
 * A2: 1562*2219
 * A3: 1097*1562
 * A4: 767*1097
*/
CPdfHelper::CPdfHelper()
{
    m_printer = new QPrinter;
    m_painter = new QPainter;
    m_x = 0;
    m_y = 0;
    m_iWidthPX = 767;
    m_iHeightPX = 1097;

    m_font.setPixelSize(24);
}

CPdfHelper::~CPdfHelper()
{
    if(m_printer)
    {
        delete m_printer;
        m_printer = nullptr;
    }
    if(m_painter)
    {
        delete m_painter;
        m_painter = nullptr;
    }
}

void CPdfHelper::SetPdfName(const QString &strFileName)
{
    m_printer->setPageSize(QPrinter::A2);
    m_printer->setOutputFormat(QPrinter::PdfFormat);
    m_printer->setOutputFileName(strFileName);

    m_painter->begin(m_printer);
    m_painter->setFont(m_font);

    m_iWidthPX = m_painter->viewport().width();
    m_iHeightPX = m_painter->viewport().height();
    qDebug()<<Q_FUNC_INFO<<m_iWidthPX<<m_iHeightPX;
}

void CPdfHelper::WriteTextToPdf(const QString &strText)
{
    if(nullptr == m_printer)
        return;

    QString strRawText = strText;
    int iLen = strRawText.length();

    while(iLen > 0)
    {
        QString strData;
        if(iLen <= 124)
        {
            strData = strRawText;
            strRawText.clear();
        }
        else
        {
            strData = strRawText.left(124);
            strRawText = strRawText.right(iLen - 124);
        }
        iLen = strRawText.length();

        int iMax = m_y + 50;
        if(iMax > m_iHeightPX)
        {
            m_y = 0;
            m_printer->newPage();
        }

        m_painter->drawText(m_x, m_y, m_iWidthPX, m_iHeightPX, 0, strData);
        m_y += 50;
    }
}

void CPdfHelper::InsertPictureToPdf(const QPixmap &pixmap)
{
    if(nullptr == m_printer)
        return;

    _WritePixmap(pixmap);
}

void CPdfHelper::InsertPictureToPdf(const QString &strPictruePath)
{
    if(nullptr == m_printer || !QFile::exists(strPictruePath))
        return;

    QPixmap pixmap(strPictruePath);
    _WritePixmap(pixmap);
}

void CPdfHelper::EndPainter()
{
    m_painter->end();
}

void CPdfHelper::_WritePixmap(QPixmap pixmap)
{
    if(pixmap.isNull())
        return;

    double dScale = m_iWidthPX * 1.0 / pixmap.width();
    QPixmap newPixMap = pixmap.scaled(pixmap.width() * dScale, pixmap.height() * dScale, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    int iMax = m_y + newPixMap.height();
    if(iMax > m_iHeightPX)
    {
        m_y = 0;
        m_printer->newPage();
    }

    m_painter->drawPixmap(m_x, m_y, newPixMap.width(), newPixMap.height(), newPixMap);
    m_y += newPixMap.height();
}
