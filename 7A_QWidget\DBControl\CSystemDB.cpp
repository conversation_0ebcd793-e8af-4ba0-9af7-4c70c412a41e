﻿#include "CSystemDB.h"
#include "PublicConfig.h"
#include "PublicParams.h"
#include <QDebug>

CSystemDB *CSystemDB::m_spInstance = nullptr;

CSystemDB::CSystemDB()
    : CSqliteDBBase(CPublicConfig::GetInstance()->GetSystemDBPath(), gk_strSystemDBConnect)

{

}

CSystemDB::~CSystemDB()
{

}

CSystemDB *CSystemDB::GetInstance()
{
    if(nullptr == m_spInstance)
        m_spInstance = new CSystemDB;
    return m_spInstance;
}

QVariant CSystemDB::getValueFromKey(QVariant qKey)
{
    QString strCmd = QString("SELECT value FROM config WHERE key = '%1'").arg(qKey.toString());
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return _GetFirstValue(strList);
}

int CSystemDB::getIntValueFromKey(QVariant qKey)
{
    int iValue = getValueFromKey(qKey).toInt();
    return iValue;
}

float CSystemDB::getFloatValueFromKey(QVariant qKey)
{
    float fValue = getValueFromKey(qKey).toFloat();
    return fValue;
}

QString CSystemDB::getQStringValueFromKey(QVariant qKey)
{
    QString strValue = getValueFromKey(qKey).toString();
    return strValue;
}

bool CSystemDB::addKeyValue(QString strKey, QVariant strValue)
{
    QString strCmd = QString("select * from config where key = '%1'").arg(strKey);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    if(strList.isEmpty())
    {
        strCmd = QString("insert into config (key,value) values('%1','%2')").arg(strKey).arg(strValue.toString());
        return _ExecuteDB(strCmd);
    }
    else
    {
        strCmd = QString("update config set value = '%1' where key = '%2'").arg(strValue.toString()).arg(strKey);
        return _ExecuteDB(strCmd);
    }
}
