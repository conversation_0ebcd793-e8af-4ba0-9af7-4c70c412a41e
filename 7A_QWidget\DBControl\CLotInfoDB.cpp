#include "CLotInfoDB.h"
#include "PublicConfig.h"
#include "PublicParams.h"
#include "QDebug"
CLotInfoDB *CLotInfoDB::m_spInstance = nullptr;

CLotInfoDB *CLotInfoDB::GetInstance()
{
    if(nullptr == m_spInstance)
        m_spInstance = new CLotInfoDB;
    return m_spInstance;
}

CLotInfoDB::CLotInfoDB(): CSqliteDBBase(CPublicConfig::GetInstance()->GetLotInfoDBPath(), gk_strLotInfoDBConnect)
{
    QString strCmd = "create table if not exists CodeName ("
                     "id integer not null primary key autoincrement,"
                     "ProjectCode varchar,"
                     "ProjectShowName varchar,"
                     "ProjectResultName varchar,"
                     "ThresholdValue varchar,"
                     "CurveName varchar,"
                     "CurveBRGYLine varchar,"
                     "SampleType varchar,"
                     "TestTime varchar,"
                     "HrmTime varchar,"
                     "MinCycleCount varchar,"
                     "upliftThreshould varchar,"//
            "FlThreshould varchar,"
            "FirstDevThreshould varchar,"
            "PQcCtCutoff varchar,"
            "FlInterfereKPCR varchar,"
            "FlInterfereBPCR varchar,"
            "StandardizationPCR varchar,"
            "FlInterfereKHRM varchar,"
            "FlInterfereBHRM varchar,"
            "StandardizationHRM varchar,"
            "wildTypeTm varchar,"
            "TempRangeValue varchar,"
            "TmThreshould varchar,"
            "YmThreshold varchar,"
            "RmThreshold varchar,"
            "AmplitudeThreshould varchar,"
            "TmRange varchar,"
            "Rmmark1 varchar,"
            "Rmmark2 varchar,"
            "Forward2CT varchar,"
            "ForwardBeginNumber varchar)";
    _ExecuteDB(strCmd);


    // 第一版
    QString strTemp = "select * from sqlite_master where type = 'table' and name = 'CodeName' and sql like '%PQcCtCutoff%'";
    QList<QStringList> strList;

    if(_QueryDB(strTemp, strList))
    {
        if(strList.isEmpty())
        {
            _ExecuteDB("BEGIN TRANSACTION");
            QString strCmd = "create table if not exists CodeNameTemp ("
                             "id integer not null primary key autoincrement,"
                             "ProjectCode varchar,"
                             "ProjectShowName varchar,"
                             "ProjectResultName varchar,"
                             "ThresholdValue varchar,"
                             "CurveName varchar,"
                             "CurveBRGYLine varchar,"
                             "SampleType varchar,"
                             "TestTime varchar,"
                             "HrmTime varchar,"
                             "MinCycleCount varchar,"
                             "upliftThreshould varchar,"//
                    "FlThreshould varchar,"
                    "FirstDevThreshould varchar,"
                    "PQcCtCutoff varchar,"
                    "FlInterfereKPCR varchar,"
                    "FlInterfereBPCR varchar,"
                    "StandardizationPCR varchar,"
                    "FlInterfereKHRM varchar,"
                    "FlInterfereBHRM varchar,"
                    "StandardizationHRM varchar,"
                    "wildTypeTm varchar,"
                    "TempRangeValue varchar,"
                    "TmThreshould varchar,"
                    "YmThreshold varchar,"
                    "RmThreshold varchar,"
                    "AmplitudeThreshould varchar,"
                    "TmRange varchar,"
                    "Rmmark1 varchar,"
                    "Rmmark2 varchar,"
                    "Forward2CT varchar,"
                    "ForwardBeginNumber varchar)";
            if(_ExecuteDB(strCmd))
            {
                strCmd = "INSERT INTO CodeNameTemp SELECT T.id, T.ProjectCode, T.ProjectShowName, T.ProjectResultName,T.ThresholdValue,T.CurveName,T.CurveBRGYLine,T.SampleType,T.TestTime,T.HrmTime,T.MinCycleCount,T.upliftThreshould,T.FlThreshould,T.FirstDevThreshould,'',T.FlInterfereKPCR,'','' ,'' ,'' ,'',T.wildTypeTm,T.TempRangeValue ,T.TmThreshould,'','',T.AmplitudeThreshould,'','','',T.Forward2CT,T.ForwardBeginNumber FROM CodeName T";

                if(!_ExecuteDB(strCmd))
                {
                    _ExecuteDB("DROP TABLE CodeNameTemp");
                    _ExecuteDB("ROLLBACK");
                    return;
                }
                strCmd = "DROP TABLE CodeName";
                if(!_ExecuteDB(strCmd))
                {
                    _ExecuteDB("ROLLBACK");
                    return;
                }
                strCmd = "ALTER TABLE CodeNameTemp RENAME TO CodeName";
                if(!_ExecuteDB(strCmd))
                {
                    _ExecuteDB("ROLLBACK");
                    return;
                }
                qDebug()<<"CodeName Add new column success";
            }
            // 提交事务
            _ExecuteDB("COMMIT");
        }

    }

    // 第二版 存在PQcCtCutoff
    QString strTemp2 = "select * from sqlite_master where type = 'table' and name = 'CodeName' and sql like '%YmThreshold%'";
    QList<QStringList> strList2;

    if(_QueryDB(strTemp2, strList2))
    {
        if(strList2.isEmpty())
        {
            _ExecuteDB("BEGIN TRANSACTION");
            QString strCmd = "create table if not exists CodeNameTemp ("
                             "id integer not null primary key autoincrement,"
                             "ProjectCode varchar,"
                             "ProjectShowName varchar,"
                             "ProjectResultName varchar,"
                             "ThresholdValue varchar,"
                             "CurveName varchar,"
                             "CurveBRGYLine varchar,"
                             "SampleType varchar,"
                             "TestTime varchar,"
                             "HrmTime varchar,"
                             "MinCycleCount varchar,"
                             "upliftThreshould varchar,"//
                    "FlThreshould varchar,"
                    "FirstDevThreshould varchar,"
                    "PQcCtCutoff varchar,"
                    "FlInterfereKPCR varchar,"
                    "FlInterfereBPCR varchar,"
                    "StandardizationPCR varchar,"
                    "FlInterfereKHRM varchar,"
                    "FlInterfereBHRM varchar,"
                    "StandardizationHRM varchar,"
                    "wildTypeTm varchar,"
                    "TempRangeValue varchar,"
                    "TmThreshould varchar,"
                    "YmThreshold varchar,"
                    "RmThreshold varchar,"
                    "AmplitudeThreshould varchar,"
                    "TmRange varchar,"
                    "Rmmark1 varchar,"
                    "Rmmark2 varchar,"
                    "Forward2CT varchar,"
                    "ForwardBeginNumber varchar)";
            if(_ExecuteDB(strCmd))
            {
                strCmd = "INSERT INTO CodeNameTemp SELECT T.id, T.ProjectCode, T.ProjectShowName, T.ProjectResultName,T.ThresholdValue,T.CurveName,T.CurveBRGYLine,T.SampleType,T.TestTime,T.HrmTime,T.MinCycleCount,T.upliftThreshould,T.FlThreshould,T.FirstDevThreshould,T.PQcCtCutoff,T.FlInterfereKPCR,T.FlInterfereBPCR,T.StandardizationPCR,T.FlInterfereKHRM ,T.FlInterfereBHRM ,T.StandardizationHRM,T.wildTypeTm,T.TempRangeValue ,T.TmThreshould,'','',T.AmplitudeThreshould,T.TmRange,'','',T.Forward2CT,T.ForwardBeginNumber FROM CodeName T";
                if(!_ExecuteDB(strCmd))
                {
                    _ExecuteDB("DROP TABLE CodeNameTemp");
                    _ExecuteDB("ROLLBACK");
                    return;
                }
                strCmd = "DROP TABLE CodeName";
                if(!_ExecuteDB(strCmd))
                {
                    _ExecuteDB("ROLLBACK");
                    return;
                }
                strCmd = "ALTER TABLE CodeNameTemp RENAME TO CodeName";
                if(!_ExecuteDB(strCmd))
                {
                    _ExecuteDB("ROLLBACK");
                    return;
                }
                qDebug()<<"CodeName Add new column success";
            }
            // 提交事务
            _ExecuteDB("COMMIT");
        }


    }


}

CLotInfoDB::~CLotInfoDB()
{

}

QStringList CLotInfoDB::GetAllProjectShowName()
{
    QString strCmd = "select ProjectShowName from CodeName";
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);

    return _GetColumnValueList(strList);
}

bool CLotInfoDB::GetLotInfoByShowName(const QString &strProjectShowName, SLotInfoStruct &sLotInfo)
{
    QString strCmd = QString("select * from CodeName where ProjectShowName = '%1'").arg(strProjectShowName);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    QStringList strRowList = _GetRowValueList(strList);
    if(strRowList.size() >= 32)
    {
        sLotInfo.strProjectCode = strRowList.at(1);
        sLotInfo.strProjectShowName = strRowList.at(2);
        sLotInfo.strProjectResultName = strRowList.at(3);
        sLotInfo.strThresholdValue = strRowList.at(4);

        sLotInfo.strCurveName = strRowList.at(5);
        sLotInfo.strCurveBRGYLine = strRowList.at(6);

        // 判断是否以 ";" 结尾
        if (sLotInfo.strCurveName.endsWith(";")) {
            // 去掉最后一个字符
            sLotInfo.strCurveName.chop(1);
        }
        if (sLotInfo.strCurveBRGYLine.endsWith(";")) {
            // 去掉最后一个字符
            sLotInfo.strCurveBRGYLine.chop(1);
        }

        sLotInfo.strSampleType = strRowList.at(7);

        QStringList strTimeList = strRowList.at(8).split(",");
        if(strTimeList.size() >= 1)
            sLotInfo.iTestTime = strTimeList.at(0).toInt();
        if(strTimeList.size() >= 2)
            sLotInfo.iAddTime = strTimeList.at(1).toInt();

        sLotInfo.iHrmTime = strRowList.at(9).toInt();
        sLotInfo.iMinCycleCount = strRowList.at(10).toInt();

        sLotInfo.strUpliftThresholdValue = strRowList.at(11);
        sLotInfo.strFlThreshouldValue = strRowList.at(12);
        sLotInfo.strFirstDevThreshouldValue = strRowList.at(13);
        sLotInfo.strPQCCutoffValue = strRowList.at(14);
        sLotInfo.FlInterfereKPCR = strRowList.at(15);
        sLotInfo.FlInterfereBPCR = strRowList.at(16);
        sLotInfo.strStandardizationPCR = strRowList.at(17);
        sLotInfo.FlInterfereKHRM = strRowList.at(18);
        sLotInfo.FlInterfereBHRM = strRowList.at(19);
        sLotInfo.strStandardizationHRM = strRowList.at(20);

        sLotInfo.strWildTypeTmValue = strRowList.at(21);
        sLotInfo.strTempRangeValue = strRowList.at(22);
        sLotInfo.strTmThresholdValue = strRowList.at(23);

        sLotInfo.strYmThreshold = strRowList.at(24);
        sLotInfo.strRmThreshold = strRowList.at(25);

        sLotInfo.strAmplitudeThresholdValue = strRowList.at(26);
        sLotInfo.strTmRange = strRowList.at(27);

        sLotInfo.bForward2CT = (strRowList.at(30) == "0") ? false : true;
        sLotInfo.iForwardBeginNumber = strRowList.at(31).toInt();
    }
    return true;
}

bool CLotInfoDB::SetLotInfoByShowName(const QString &strProjectShowName, SLotInfoStruct &sLotInfo)
{
    QString strCmd = QString("UPDATE CodeName SET "
                             "ThresholdValue = '%1', "
                             "TestTime = '%2', " // 测试时间+AddTime
                             "HrmTime = '%3', "
                             "MinCycleCount = '%4', "
                             "upliftThreshould = '%5', "
                             "FlThreshould = '%6', "
                             "FirstDevThreshould = '%7', "
                             "PQcCtCutoff = '%8', "
                             "FlInterfereKPCR = '%9', "
                             "StandardizationPCR = '%10', "
                             "FlInterfereKHRM = '%11', "
                             "StandardizationHRM = '%12', "
                             "wildTypeTm = '%13', "
                             "TempRangeValue = '%14', "
                             "TmThreshould = '%15', "
                             "AmplitudeThreshould = '%16', "
                             "TmRange = '%17'"
                             "WHERE ProjectShowName = '%18'")
            .arg(sLotInfo.strThresholdValue)
            .arg(sLotInfo.iTestTime)
            .arg(sLotInfo.iHrmTime)
            .arg(sLotInfo.iMinCycleCount)
            .arg(sLotInfo.strUpliftThresholdValue)
            .arg(sLotInfo.strFlThreshouldValue)
            .arg(sLotInfo.strFirstDevThreshouldValue)
            .arg(sLotInfo.strPQCCutoffValue)
            .arg(sLotInfo.FlInterfereKPCR)
            .arg(sLotInfo.strStandardizationPCR)
            .arg(sLotInfo.FlInterfereKHRM)
            .arg(sLotInfo.strStandardizationHRM)
            .arg(sLotInfo.strWildTypeTmValue)
            .arg(sLotInfo.strTempRangeValue)
            .arg(sLotInfo.strTmThresholdValue)
            .arg(sLotInfo.strAmplitudeThresholdValue)
            .arg(sLotInfo.strTmRange)
            .arg(strProjectShowName);

    return _ExecuteDB(strCmd);
}

QString CLotInfoDB::GetProjectNameByCode(const QString &strProjectCode)
{
    QString strCmd = QString("select ProjectShowName from CodeName where ProjectCode = '%1'").arg(strProjectCode);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return _GetFirstValue(strList);
}

QList<QStringList> CLotInfoDB::GetAllCodeNameTarget()
{
    QString strCmd = QString("select ProjectCode, ProjectShowName, CurveName from CodeName");
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return strList;
}

QString CLotInfoDB::GetCurveNameByProjectName(const QString &strProjectName)
{
    QString strCmd = QString("select CurveName from CodeName where ProjectShowName = '%1'").arg(strProjectName);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return _GetFirstValue(strList);
}

bool CLotInfoDB::IsProjectNameExist(QString strProjectName)
{
    QString strCmd = QString("select * from CodeName where ProjectShowName = '%1'").arg(strProjectName);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return (strList.isEmpty() ? false : true);
}

QMap<QString, QStringList> CLotInfoDB::GetProjectSampleTypeMap()
{
    QString strCmd = QString("select ProjectShowName, SampleType from CodeName");
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);

    QMap<QString, QStringList> strMap;
    for(int i=0; i<strList.size(); i++)
    {
        QStringList oneList = strList.at(i);
        if(oneList.size() < 2)
            continue;

        strMap.insert(oneList.at(0), oneList.at(1).split(";"));
    }
    return strMap;
}
