#include "CLightOneCurve.h"
#include "CSetChartXYRange.h"
#include "PublicParams.h"
#include "qcustomplot.h"
#include <QBoxLayout>

CLightOneCurve::CLightOneCurve(QWidget *parent)
    : QWidget(parent)
    , m_bShow(false)
    , m_iPointCount(0)
    , m_iReadLightIndex(1)
{
    _InitWidget();
}

CLightOneCurve::~CLightOneCurve()
{

}

void CLightOneCurve::ClearData()
{
    m_iPointCount = 0;
    m_iReadLightIndex = 1;
    QVector<double> x, y;
    m_pCustomPlot->graph(0)->setData(x, y);
    m_pCustomPlot->replot();
}

void CLightOneCurve::ReceiveData(const QVariant &qVarData)
{
    QVariantList qVarList = qVarData.toList();
    if(qVarList.size() < 5)
        return;

    if(qVarList.at(0).toInt() == m_iReadLightIndex)
    {
        for(int i=0; i<4; i++)
        {
            m_pCustomPlot->graph(0)->addData(m_iPointCount + i, qVarList.at(i + 1).toDouble());
        }
        m_iReadLightIndex += 4;
    }
    else
    {
        for(int i=0; i<4; i++)
        {
            m_pCustomPlot->graph(0)->addData(m_iPointCount + i, 0);
        }
        m_iReadLightIndex += qVarList.at(0).toInt() + 4;
    }
    m_iPointCount += 4;
    m_pCustomPlot->replot();
}

void CLightOneCurve::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;
    QWidget::showEvent(pEvent);
}

void CLightOneCurve::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;
    QWidget::hideEvent(pEvent);
}

void CLightOneCurve::_SlotSetXYRange(const QStringList &strList)
{
    if(4 != strList.size())
        return;

    m_pCustomPlot->xAxis->setRange(strList.at(0).toDouble(), strList.at(1).toDouble());
    m_pCustomPlot->yAxis->setRange(strList.at(2).toDouble(), strList.at(3).toDouble());
    m_pCustomPlot->replot();
}

void CLightOneCurve::_InitWidget()
{
    _InitCustomPlot();

    QStringList strRangeList = {"0", "2000", "0", "2000"};
    m_pCSetChartXYRange = new CSetChartXYRange(strRangeList);
    m_pCSetChartXYRange->SetLineEditTextAlignment();
    connect(m_pCSetChartXYRange, &CSetChartXYRange::SignalSetRange, this, &CLightOneCurve::_SlotSetXYRange);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addWidget(m_pCustomPlot);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pCSetChartXYRange, 0, Qt::AlignLeft);
    this->setLayout(pLayout);
}

void CLightOneCurve::_InitCustomPlot()
{
    m_pCustomPlot = new QCustomPlot;

    QFont font;
    font.setPointSize(10);
    m_pCustomPlot->legend->setFont(font);
    m_pCustomPlot->legend->setSelectedFont(font);
    m_pCustomPlot->legend->setVisible(true);
    m_pCustomPlot->legend->setSelectableParts(QCPLegend::spItems);
    m_pCustomPlot->legend->setBorderPen(Qt::NoPen);
    m_pCustomPlot->legend->setWrap(1);
    m_pCustomPlot->axisRect()->insetLayout()->setInsetAlignment(0, Qt::AlignTop);

    _AddGraph(m_pCustomPlot, Qt::black, Qt::black, 0, "LineSeries");

    m_pCustomPlot->xAxis->setRange(0, 2000);
    m_pCustomPlot->yAxis->setRange(0, 2000);
}

void CLightOneCurve::_AddGraph(QCustomPlot *pCustomPlot, QColor penColor, QColor pointColor, int iChart, QString strChartName)
{
    QPen pen;
    pen.setWidth(2);
    pen.setColor(penColor);
    pCustomPlot->addGraph();
    pCustomPlot->graph(iChart)->setPen(pen);
    pCustomPlot->graph(iChart)->setName(strChartName);
    pCustomPlot->graph(iChart)->setAntialiasedFill(true);
    pCustomPlot->graph(iChart)->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssNone,
                                                                QPen(pointColor, 1),
                                                                QBrush(pointColor), 3));
}

void CLightOneCurve::_UpdatePlot(bool bResetRange)
{
    if(bResetRange)
    {

    }

    m_pCustomPlot->replot();
}
