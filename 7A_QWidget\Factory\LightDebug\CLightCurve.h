#ifndef CLIGHTCURVE_H
#define CLIGHTCURVE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-11-14
  * Description: 光学曲线-曲线
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QCheckBox>
#include <QComboBox>
#include <QPushButton>
#include <QStackedWidget>

#include "CCmdBase.h"
#include "CLabelComboBox.h"
#include "CLabelLineEdit.h"
#include "CLightOneCurve.h"

class CLightCurve : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CLightCurve(QWidget *parent = nullptr);
    ~CLightCurve();

    void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

private slots:
    void _SlotMachineChanged(int iMachineID);
    void _SlotReqChanged(const QString &strReq);
    void _SlotReqBtn();
    void _SlotLed1Checked(bool bChecked);
    void _SlotLed2Checked(bool bChecked);
    void _SlotLedBtn();
    void _SlotPDChanged(int index);
    void _SlotCSTBtn();
    void _SlotCSPBtn();

private:
    void _InitWidget();

private:
    typedef struct _SLightCurveStruct
    {
        _SLightCurveStruct()
        {
            bLed1 = false;
            bLed2 = false;
            pCLightOneCurve = nullptr;
            iPDIndex = 0;
        }
        bool bLed1;
        bool bLed2;
        int iPDIndex;
        CLightOneCurve *pCLightOneCurve;
        QString strReq;
    }SLightCurveStruct;

private:
    CLabelComboBox *m_pMachineComboBox;
    CLabelLineEdit *m_pReqLineEdit;
    QPushButton *m_pReqBtn;
    QCheckBox *m_pLed1CheckBox, *m_pLed2CheckBox;
    QPushButton *m_pLedBtn;
    QComboBox *m_pPDComboBox;
    QPushButton *m_pCSTBtn, *m_pCSPBtn;
    QStackedWidget *m_pStackedWidget;
    QList<SLightCurveStruct*> m_sCurveUiList;
};

#endif // CLIGHTCURVE_H
