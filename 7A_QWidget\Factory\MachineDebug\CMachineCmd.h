#ifndef CMACHINECMD_H
#define CMACHINECMD_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-04-28
  * Description: 单指令
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QCheckBox>
#include <QPushButton>
#include <QTableWidget>

#include "CCmdBase.h"
#include "CLineEdit.h"
#include "CLabelComboBox.h"
#include "CTextBrowser.h"

class CMachineCmd : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CMachineCmd(QWidget *parent = nullptr);
    ~CMachineCmd();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData);

private slots:
    void _SlotSendButton();
    void _SlotCmdComboBoxChanged(int index);
    void _SlotReadEnvTempBtn();

    void _SlotMachineChanged(int iMachineID);
    void _SlotIntervalValueChanged(const QString &strValue);
    void _SlotPCRIntevalBtn();

    void _SlotVMCheckBox(bool bClicked);
    void _SlotFLCheckBox(bool bClicked);
    void _SlotHTCheckBox(bool bClicked);
    void _SlotPressureCheckBox(bool bClicked);
    void _SlotPCRCheckBox(bool bClicked);
    void _SlotNotifyBtn();

private:
    void _InitCmdInfo();

private:
    void _InitWidget();
    void _InitLayout();

private:
    struct _SUiInfoStruct
    {
        bool bVM = true;
        bool bFL = true;
        bool bHT = true;
        bool bPressure = true;
        bool bPCR = true;
        QString strInterval;
    };

    QList<_SUiInfoStruct> m_sUiInfoStructList;

private:
    CLabelComboBox *m_pMachineComboBox;
    QTableWidget *m_pCmdTableWidget;
    QPushButton *m_pSendBtn;
    CTextBrowser *m_pTextBrowser;

    QPushButton *m_pReadTempBtn;

    CLineEdit *m_pPCRIntevalLineEdit;
    QPushButton *m_pPCRIntevalBtn;

    QCheckBox *m_pVMCheckBox, *m_pFLCheckBox, *m_pHTCheckBox, *m_pPressureCheckBox, *m_pPCRCheckBox;
    QPushButton *m_pNotifyBtn;

    QStringList m_strCmdTextList;
    QStringList m_strParams2List;
    QStringList m_strParams3List;
    QStringList m_strParams4List;
    QStringList m_strParams6List;
    QStringList m_strLightCoverList;
    QStringList m_strHomeEndList;
    QStringList m_strNumber10List;
    QStringList m_strNumber4List;
    QStringList m_strLightList;
    QStringList m_strPDList;
    QStringList m_strHTOpenList;
    QStringList m_strHTCloseList;
    QStringList m_strRelList;
    QStringList m_strStopList;
    QStringList m_strMotorNameList;
    QMap<int, int> m_iCmdIndexIDMap;
};

#endif // CMACHINECMD_H
