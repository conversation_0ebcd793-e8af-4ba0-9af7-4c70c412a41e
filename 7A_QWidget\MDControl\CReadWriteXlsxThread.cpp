#include "CReadWriteXlsxThread.h"
#include <QDebug>
#include <unistd.h>

CReadWriteXlsxThread* CReadWriteXlsxThread::m_spInstance = NULL;

CReadWriteXlsxThread::CReadWriteXlsxThread()
{
    m_bRunning = true;
    connect(this,&CReadWriteXlsxThread::SignalReadWriteEnd,this,&CReadWriteXlsxThread::_SlotReadWriteEnd,Qt::BlockingQueuedConnection);

    pthread_t tid;
    pthread_create(&tid, NULL, _CreateThread2WriteXlsx, this);
}

CReadWriteXlsxThread::~CReadWriteXlsxThread()
{
    m_bRunning = false;
    printf("CReadWriteXlsxThread::~CReadWriteXlsxThread()\n");
}

CReadWriteXlsxThread* CReadWriteXlsxThread::GetInstance()
{
    if(NULL == m_spInstance)
        m_spInstance = new CReadWriteXlsxThread;
    return m_spInstance;
}

void CReadWriteXlsxThread::FreeInstance()
{
    if(NULL != m_spInstance)
    {
        delete m_spInstance;
        m_spInstance = NULL;
    }
}

void CReadWriteXlsxThread::AddXlsxParamsStruct(STXlsxParmasStruct *pXlsxStruct)
{
    QMutexLocker locker(&m_mutex);
    m_stXlsxParamsList.push_back(pXlsxStruct);
}

void* CReadWriteXlsxThread::_CreateThread2WriteXlsx(void *arg)
{
    qDebug()<<"[CReadWriteXlsxThread],读写xlsx线程ID:"<<QThread::currentThreadId();

    CReadWriteXlsxThread* pClass = (CReadWriteXlsxThread*)arg;
    while(pClass->m_bRunning)
    {
        if(!pClass->m_stXlsxParamsList.isEmpty())
        {
            pClass->m_mutex.lock();
            STXlsxParmasStruct* pXlsxStruct = pClass->m_stXlsxParamsList.takeFirst();
            pClass->m_mutex.unlock();

            if(eWriteXlsx == pXlsxStruct->eOpXlsxType)
                pClass->_WriteXlsx(pXlsxStruct);
            else if(eReadXlsx == pXlsxStruct->eOpXlsxType)
                pClass->_ReadXlsx(pXlsxStruct);
        }
        else
        {
            ::usleep(500*1000);
        }
    }

    return NULL;
}

/*******************************************************************
* @brief: _ReadXlsx
* @param: STXlsxParmasStruct
* @return: void
* @description: 如果strTableName为空则读整张xlsx，不为空则只读那张表
* @author: hxirong
* @date:   2021-12-30
*******************************************************************/
void CReadWriteXlsxThread::_ReadXlsx(STXlsxParmasStruct *pXlsxStruct)
{
    QString strXlsxName = pXlsxStruct->strXlsxName;
    QString strTableName = pXlsxStruct->strTableName;
    qDebug()<<"[CReadWriteXlsxThread::_ReadXlsx],start read:"<<strXlsxName<<strTableName;

    if(!QFile::exists(strXlsxName))
        return;

    QXlsx::Document qXlsxDoc(strXlsxName);
    QStringList strTableList = qXlsxDoc.sheetNames();

    for(int index=0;index<strTableList.size();index++)
    {
        if(!strTableName.isEmpty())
        {
            if(strTableName != strTableList[index])
                continue;
        }

        QXlsx::Workbook *workBook = qXlsxDoc.workbook();
        QXlsx::Worksheet *workSheet = static_cast<QXlsx::Worksheet*>(workBook->sheet(index));

        int iRowCount = workSheet->dimension().rowCount();
        int iColumnCount = workSheet->dimension().columnCount();
        qDebug()<<QString("read xlsx %1 行数 %2 列数 %3").arg(strTableList[index]).arg(iRowCount).arg(iColumnCount);

        QList<QVariantList> oneTableDataList;

        for(int i=0;i<=iRowCount;i++)
        {
            QVariantList varDataList;
            for(int j=0;j<=iColumnCount;j++)
            {
                QXlsx::Cell *pCell = workSheet->cellAt(i, j);
                if(NULL == pCell)
                    continue;

                varDataList.push_back(pCell->value());
            }

            oneTableDataList.push_back(varDataList);
        }

        pXlsxStruct->varReadDataMap.insert(strTableList[index],oneTableDataList);
    }

    qDebug()<<"[CReadWriteXlsxThread::_ReadXlsx],read end:"<<strXlsxName<<strTableName;
    emit SignalReadWriteEnd(pXlsxStruct);

    delete pXlsxStruct;
    pXlsxStruct = NULL;
}

/*******************************************************************
* @brief:   _WriteXlsx
* @param:   STXlsxParmasStruct
* @return:  void
* @description: 写xlsx
* @author: hxirong
* @date:   2021-12-30
*******************************************************************/
void CReadWriteXlsxThread::_WriteXlsx(STXlsxParmasStruct* pXlsxStruct)
{
    QString strXlsxName = pXlsxStruct->strXlsxName;
    QString strTableName = pXlsxStruct->strTableName;
    qDebug()<<"[CReadWriteXlsxThread::_WriteXlsx],start write:"<<strXlsxName<<strTableName;
    _WriteData(pXlsxStruct);
    qDebug()<<"[CReadWriteXlsxThread::_WriteXlsx],write end:"<<strXlsxName<<strTableName;
    emit SignalReadWriteEnd(pXlsxStruct);

    delete pXlsxStruct;
    pXlsxStruct = NULL;
}
/*******************************************************************
* @brief:   _SlotReadWriteEnd
* @param:
* @param:
* @return:  void
* @description: 读写结束回调通知
* @author: hxirong
* @date:   2022-01-05
*******************************************************************/
void CReadWriteXlsxThread::_SlotReadWriteEnd(STXlsxParmasStruct* pXlsxStruct)
{
    if(eWriteXlsx == pXlsxStruct->eOpXlsxType)
    {
        if(pXlsxStruct->WriteEndCallBack)
        {
            qDebug()<<"pXlsxStruct->WriteEndCallBack:"<<pXlsxStruct->strXlsxName<<pXlsxStruct->strTableName;
            pXlsxStruct->WriteEndCallBack(pXlsxStruct->strXlsxName,pXlsxStruct->strTableName);
        }
    }
    else if(eReadXlsx == pXlsxStruct->eOpXlsxType)
    {
        if(pXlsxStruct->ReadEndCallBack)
        {
            qDebug()<<"pXlsxStruct->ReadEndCallBack:"<<pXlsxStruct->strXlsxName;
            pXlsxStruct->ReadEndCallBack(pXlsxStruct->strXlsxName,pXlsxStruct->varReadDataMap);
        }
    }
}

/*******************************************************************
* @brief:   _WriteData
* @param:   pXlsxStruct
* @param:
* @return:  bool
* @description: 写数据
* @author: hxirong
* @date:   2022-01-05
* 2022-02-11 修改填写数据和画图
*******************************************************************/
bool CReadWriteXlsxThread::_WriteData(STXlsxParmasStruct* pXlsxStruct)
{
    QString& strXlsxName = pXlsxStruct->strXlsxName;
    QString& strTableName = pXlsxStruct->strTableName;
    QStringList& strTitleList = pXlsxStruct->strTitleList;
    QList<QVariantList>& varDataList = pXlsxStruct->varWriteDataList;

    QXlsx::Document qXlsxDoc(strXlsxName);
    if(!qXlsxDoc.selectSheet(strTableName))
    {
        qDebug()<<"[CReadWriteXlsxThread],addSheet:"<<strTableName;
        qXlsxDoc.addSheet(strTableName);
    }

#if 1
    QXlsx::Format format;
    format.setFontSize(12);
    format.setHorizontalAlignment(QXlsx::Format::AlignHCenter);

    for(int i=0;i<strTitleList.size();i++)       
        qXlsxDoc.write(1,i+1,strTitleList[i],format);

    for(int i=0;i<varDataList.size();i++)
    {
        QVariantList varValueList = varDataList[i];

        for(int j=1;j<=varValueList.size();j++)
            qXlsxDoc.write(i+2,j,varValueList[j-1],format);
    }
#endif

#if 0
    if(pXlsxStruct->bDrawChart && !pXlsxStruct->varWriteDataList.isEmpty())
    {
        int iRow = 3;
        int iColumn = strTitleList.size() + 1;
        QXlsx::Chart *pLineChart = qXlsxDoc.insertChart(iRow, iColumn, pXlsxStruct->qSize);
        pLineChart->setChartType(pXlsxStruct->eChartType);

        QString strDataRange,strXRange;
        _GetDataAndXRange(pXlsxStruct,strDataRange,strXRange);
        qDebug()<<"xxxxxxxxxxxxxxxxxxxxxxx表格范围:"<<strTableName<<strXRange<<strDataRange;

        pLineChart->addSeries(QXlsx::CellRange(strDataRange),strXRange);
    }
#endif

#if 1

    //调整行高
    for(int i=0; i<pXlsxStruct->mdyHeightList.size(); i++)
    {
        MdyHeightStruct mdy = pXlsxStruct->mdyHeightList[i];
        qXlsxDoc.setRowHeight(mdy.iRow,mdy.dHeight);
    }

    //调整列宽
    for(int i=0; i<pXlsxStruct->mdyWidthList.size(); i++)
    {
        MdyWidthStruct mdy = pXlsxStruct->mdyWidthList[i];
        qXlsxDoc.setColumnWidth(mdy.iColumn,mdy.dWidth);
    }

    //按行、列写入元素
    for(int i=0; i<pXlsxStruct->dataStructList.size(); i++)
    {
        XlsxDataStruct& data = pXlsxStruct->dataStructList[i];
        qXlsxDoc.write(data.iRow,data.iColumn,data.varValue,data.format);
    }

    //合并单元格
    for(int i=0; i<pXlsxStruct->mergeRangeList.size(); i++)
    {
        QString strMerge = pXlsxStruct->mergeRangeList[i];
        qXlsxDoc.mergeCells(strMerge);
    }

    //自动调整列宽
    if(pXlsxStruct->bAutoAdjustCol)
        qXlsxDoc.autosizeColumnWidth();

    //画图,部分表有多张图
    if(!pXlsxStruct->varWriteDataList.isEmpty() || !pXlsxStruct->dataStructList.isEmpty())
    {
        for(int i=0; i<pXlsxStruct->chartNoteList.size(); i++)
        {
            ChartNoteStruct& note = pXlsxStruct->chartNoteList[i];

            qDebug()<<"[Xlsx线程],画图参数:"<<strXlsxName<<strTableName
                   <<note.strChartTitle<<note.strXTitle<<note.strYTitle
                  <<note.strSerialNameList<<note.strSerialColorList
                 <<note.strXDataRange<<note.strNumDataRange;

            QXlsx::Chart *pLineChart = qXlsxDoc.insertChart(note.iRow, note.iColumn, note.qSize);

            pLineChart->setChartType(note.eChartType);
            pLineChart->addSeries(QXlsx::CellRange(note.strNumDataRange),note.strXDataRange);
            pLineChart->setSeriesNameList(note.strSerialNameList);
            pLineChart->setSeriesColor(note.strSerialColorList);
            pLineChart->setSeriesMarkSymbol(note.strMarkSymbolList);

            pLineChart->setChartTitle(note.strChartTitle);
            pLineChart->setChartLegend(note.legendPos,false);
            pLineChart->setAxisTitle(Chart::ChartAxisPos::Bottom, note.strXTitle);
            pLineChart->setAxisTitle(Chart::ChartAxisPos::Left,note.strYTitle);
            pLineChart->setGridlinesEnable(note.bMajorGridlines,note.bMinorGridlines);
        }
    }
#endif

    qXlsxDoc.save();

    return true;
}

/*******************************************************************
* @brief:   _GetDataAndXRange
* @param:   pXlsxStruct
* @param:
* @return:  void
* @description: 获取画图的数据范围和X轴范围
* @author: hxirong
* @date:   2022-01-05
*******************************************************************/
void CReadWriteXlsxThread::_GetDataAndXRange(STXlsxParmasStruct* pXlsxStruct, QString &strDataRange, QString &strXRange)
{
    if(pXlsxStruct->varWriteDataList.isEmpty())
        return;

    int iDataSize = pXlsxStruct->varWriteDataList.size();
    strXRange = QString("%1!$A$2:$A$%2").arg(pXlsxStruct->strTableName).arg(iDataSize+1);
    char chEnd = 'B' + pXlsxStruct->varWriteDataList[0].size() - 2;
    strDataRange = QString("B2:%1%2").arg(chEnd).arg(iDataSize+1);
}
