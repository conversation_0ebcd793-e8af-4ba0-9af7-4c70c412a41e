#include "CHL7MsgThread.h"
#include <QDebug>

#include "CLisTcpClient.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "DBControl/CProjectDB.h"
#include "DBControl/CLotInfoDB.h"
#include "include/HL7/interface/IMSH.h"
#include "include/HL7/interface/IHL7Message.h"
#include "include/HL7/interface/IHL7Segment.h"
#include "include/HL7/interface/helper.h"
#include "include/HL7/interface/base.h"
#include "include/HL7/interface/HL7Parser.h"

CHL7MsgThread &CHL7MsgThread::GetInstace()
{
    static CHL7MsgThread hl7;
    return hl7;
}

CHL7MsgThread::CHL7MsgThread()
{
    connect(this, &CHL7MsgThread::SignalInitThread, this, &CHL7MsgThread::_SlotInitThread);
    connect(&m_qThread, &QThread::finished, this, &CHL7MsgThread::_SlotExitThread);
    connect(&CLisTcpClient::GetInstace(), &CLisTcpClient::SignalReadData, this, &CHL7MsgThread::SlotReadServer);

    this->moveToThread(&m_qThread);
    m_qThread.start();
    emit SignalInitThread();
}

CHL7MsgThread::~CHL7MsgThread()
{
    m_qThread.quit();
    m_qThread.wait();
}

void CHL7MsgThread::_SlotExitThread()
{
    if(m_pSendTimer)
    {
        m_pSendTimer->stop();
        m_pSendTimer->deleteLater();
    }
    printf("%s\n", Q_FUNC_INFO);
}

void CHL7MsgThread::AddHistoryID(int iHistoryID)
{
    QMutexLocker locker(&m_qMutex);
    m_iHistoryIDList.push_back(iHistoryID);
}

void CHL7MsgThread::AddHistoryIDList(QList<int> iIDList)
{
    QMutexLocker locker(&m_qMutex);
    m_iHistoryIDList.append(iIDList);
}

void CHL7MsgThread::AddSampleID(QString strSampleID)
{
    QMutexLocker locker(&m_qMutex);
    m_strSampleIDList.append(strSampleID);
}

void CHL7MsgThread::CancelUpload()
{
    QMutexLocker locker(&m_qMutex);

    m_bWaitAck = false;
    m_iReSendTimes = 0;
    m_iHistoryIDList.clear();
}

void CHL7MsgThread::SlotReadServer(QByteArray qReadByte)
{
    qDebug()<<"Lis server reply:"<<qReadByte.data();
    m_qReceiveByte += qReadByte;

    QByteArray qEndByte;
    qEndByte[0] = 0x1C;
    qEndByte[1] = 0x0D;

    int iBegin = 0;
    int iEnd = 0;
    while(m_qReceiveByte.contains("MSH") && m_qReceiveByte.contains(qEndByte))
    {
        iBegin = m_qReceiveByte.indexOf("MSH");
        iEnd = m_qReceiveByte.indexOf(qEndByte);
        if(iEnd > iBegin)
        {
            QByteArray qFrameByte = m_qReceiveByte.mid(iBegin, iEnd - iBegin);
            if(qFrameByte.contains("Message accepted") || qFrameByte.contains("ACK^R01"))
            {
                qDebug()<<"Lis上传成功,HistoryID:"<<m_iLastHistoryID;
                m_bWaitAck = false;
                m_iReSendTimes = 0;
                if(m_iHistoryIDList.isEmpty())
                    emit SignalUploadLeftNum(-1);
            }
            _ParseSampleInfo(qFrameByte);
        }
        m_qReceiveByte = m_qReceiveByte.remove(0, iEnd + 2);
    }
}

void CHL7MsgThread::_SlotInitThread()
{
    qDebug()<<"CHL7MsgThread ID:"<<QThread::currentThreadId();

    m_bWaitAck = false;
    m_iReSendTimes = 0;
    m_iMsgCtrID = 0;

    m_pSendTimer = new QTimer(this);
    connect(m_pSendTimer, &QTimer::timeout, this, &CHL7MsgThread::_SlotSendTimer);
    m_pSendTimer->start(200);
}

void CHL7MsgThread::_SendSampleID()
{
    if(m_strSampleIDList.isEmpty())
        return;

    m_qMutex.lock();
    QString strSampleID = m_strSampleIDList.takeFirst();
    m_qMutex.unlock();

    QByteArray qSendByte = _GetQuerySampleByte(strSampleID);
    CLisTcpClient::GetInstace().WriteData(qSendByte);
}

void CHL7MsgThread::_SlotSendTimer()
{
    //查询样本信息,没有重发
    _SendSampleID();

    //测试结果上传,有重发
    if(m_iReSendTimes >= 3)
    {
        qDebug()<<"Lis重发3次结束,HistoryID:"<<m_iLastHistoryID;
        CancelUpload();
        emit SignalUploadError(tr("LIS上传超时"));
        return;
    }

    if(m_bWaitAck)
    {
        int iWaitSecs = m_qSendDateTime.secsTo(QDateTime::currentDateTime());
        if(iWaitSecs >= 30)
        {
            m_iReSendTimes++;
            m_qSendDateTime = QDateTime::currentDateTime();
            CLisTcpClient::GetInstace().WriteData(m_qLastSendByte);
            qDebug()<<"Lis超时重发,次数:"<<m_iReSendTimes<<"HistoryID:"<<m_iLastHistoryID<<m_qLastSendByte;
        }
        return;
    }

    if(m_iHistoryIDList.isEmpty())
        return;

    m_qMutex.lock();
    int iHistoryID = m_iHistoryIDList.takeFirst();
    m_qMutex.unlock();

    m_bWaitAck = true;
    m_iReSendTimes = 1;
    m_iLastHistoryID = iHistoryID;
    qDebug()<<"LIS开始上传HistoryID:"<<m_iLastHistoryID;
    m_qLastSendByte = _GetSendByte(iHistoryID);
    m_qSendDateTime = QDateTime::currentDateTime();
    CLisTcpClient::GetInstace().WriteData(m_qLastSendByte);
    emit SignalUploadLeftNum(m_iHistoryIDList.size());
}

void PrintMessageStr(IHL7Message *message, std::string &messageStr)
{
    if(message)
    {
        char *str = nullptr;
        message->GetMessageString(&str);
        messageStr = str;
        FreeMemory(str);//由DLL内部申请的内存记得调用FreeMemory释放
    }
}

QByteArray CHL7MsgThread::_GetSendByte(int iHistoryID)
{
    SResultInfoStruct sResultInfo;
    CProjectDB::GetInstance()->GetHistoryData(iHistoryID, sResultInfo);

    SSampleInfoStruct sSampleInfo;
    CProjectDB::GetInstance()->GetSampleData(sResultInfo.strProjectName, sResultInfo.strSampleID, sResultInfo.strTestTime, sSampleInfo);

    SCardInfoStruct sCardInfo;
    CProjectDB::GetInstance()->GetCardData(sResultInfo.strCardID, sResultInfo.strTestTime, sCardInfo);

    SLotInfoStruct sLotInfo;
    CLotInfoDB::GetInstance()->GetLotInfoByShowName(sResultInfo.strProjectName, sLotInfo);

    QString strTestDateTime = sResultInfo.strTestTime;
    strTestDateTime = strTestDateTime.remove("-").remove(":").remove(" ");

    QString strBirthday = sSampleInfo.strBirthday;
    strBirthday = strBirthday.remove("-");
    qDebug()<<"Lis生日,年龄,性别:"<<strBirthday<<sSampleInfo.strAge<<sSampleInfo.strGender;
    if(strBirthday.isEmpty())
    {
        QStringList strAgeList = sSampleInfo.strAge.split(" ");
        if(strAgeList.size() >= 2)
        {
            if(tr("岁") == strAgeList.at(1))
            {
                int iYear = strAgeList.at(0).toInt();
                strBirthday = QDate::currentDate().addYears(-iYear).toString("yyyyMMdd");
            }
            else if(tr("月") == strAgeList.at(1))
            {
                int iMonths = strAgeList.at(0).toInt();
                strBirthday = QDate::currentDate().addMonths(-iMonths).toString("yyyyMMdd");
            }
            else if(tr("天") == strAgeList.at(1))
            {
                int iDays = strAgeList.at(0).toInt();
                strBirthday = QDate::currentDate().addDays(-iDays).toString("yyyyMMdd");
            }
        }
    }

    // 创建信息头数据
    ORUR01ScopedPtr oruR01ScopedPtr;
    oruR01ScopedPtr.Reset(CreateOruR01());

    MSHScopedPtr mshPtr;
    mshPtr.Reset(CreateMSH());

    PIDScopedPtr pidPtr;
    pidPtr.Reset(CreatePID());

    OBRScopedPtr obrScopedPtr;
    obrScopedPtr.Reset(CreateOBR());

    ORUR01PatientResultScopedPtr patientResultScopedPtr;
    patientResultScopedPtr.Reset(CreateOruR01PatientResult());

    ORUR01OrderObrScopedPtr orderObrScopedPtr;
    orderObrScopedPtr.Reset(CreateOruR01OrderObservation());
    //////////////////////////各字段数据填充/////////////////////////////
    mshPtr.Get()->SetSendApp("WonDx-1000");
    //QString strFacilityID = "全自动快速核酸扩增分析系统^" + GetHostMacAddress() + "^MDx-301";
    QString strFacilityID = CPublicConfig::GetInstance()->GetMachineSN();

    mshPtr.Get()->SetSendFacilityID(strFacilityID.toUtf8());//
    mshPtr.Get()->SetSendFacilityName("全自动核酸扩增分析仪");
    mshPtr.Get()->SetSendFacilityType("WonDx-1000");
    mshPtr.Get()->SetRecvApp("");
    mshPtr.Get()->SetRecvFacilityID("");
    mshPtr.Get()->SetRecvFacilityName("");
    mshPtr.Get()->SetRecvFacilityType("");
    mshPtr.Get()->SetDate(QDateTime::currentDateTime().toString("yyyyMMddhhmmss").toUtf8());//信息的日期时间
    mshPtr.Get()->SetMessageType("ORU");
    mshPtr.Get()->SetTriggerEvent("R01");
    mshPtr.Get()->SetMessageStructure("ORU_R01");
    mshPtr.Get()->SetMsgCtrlID(_GetMsgCtrlID().toUtf8());
    mshPtr.Get()->SetProcessingID("P");
    mshPtr.Get()->SetVersionID("2.4");
    mshPtr.Get()->SetApplicationAckType("0");
    mshPtr.Get()->SetCountryCode("CHN");
    mshPtr.Get()->SetCharacterSet("Unicode");

    QString strGender;
    if(tr("男") == sSampleInfo.strGender)
        strGender = "M";
    else if(tr("女") == sSampleInfo.strGender)
        strGender = "F";
    else if(tr("其他") == sSampleInfo.strGender)
        strGender = "O";

    // PID用户标识信息
    pidPtr->SetID("");// 身份证
    pidPtr->SetName(sSampleInfo.strName.toUtf8());
    pidPtr->SetBirthDate(strBirthday.toUtf8());
    pidPtr->SetSex(strGender.toUtf8());
    pidPtr->SetTelephoneNum(sSampleInfo.strTelephone.toUtf8());

    // OBR样本信息
    // 使用donorID作为样本编号
    QString strObrIndexStr = strFacilityID + "_" + sSampleInfo.strSampleID + "_" + strTestDateTime;
    obrScopedPtr->SetOBRIndex(strObrIndexStr.toUtf8());
    obrScopedPtr->SetProjectID(sCardInfo.strCardID.toUtf8());// 试剂批号/项目编号/卡盒ID
    obrScopedPtr->SetSampleID(sSampleInfo.strSampleID.toUtf8());// 样本编号,使用DonorID
    obrScopedPtr->SetTestTime(strTestDateTime.toUtf8());
    obrScopedPtr->SetDiagnosticMessage("");
    obrScopedPtr->SetSubmitSampleTime(sSampleInfo.strSamplingDate.replace("-", "").toUtf8());
    obrScopedPtr->SetSampleType(sSampleInfo.strSampleType.toUtf8());
    obrScopedPtr->SetSubmittingPhysician("");
    obrScopedPtr->SetSubmittingDepartment("");
    obrScopedPtr->SetAttendingPhysician("");
    obrScopedPtr->SetTreatDepartment("");

    // OBX 结果信息
    //QStringList strTargetList = sLotInfo.strCurveName.split(";");
    //QStringList strResultList = sResultInfo.strResult.split(";");
    QStringList strInfoList = GetTargetOutputInfoList(sResultInfo, sLotInfo);
    for(int i=0; i<strInfoList.size(); i++)
    {
        QString strTarget, strResult;
        QString strOneInfo = strInfoList.at(i);
        if(strOneInfo.contains("(")) //含有ct
        {
            int index = strOneInfo.indexOf("(");
            strOneInfo = strOneInfo.mid(0, index);
            strOneInfo = strOneInfo.remove(" ");
            QStringList strList = strOneInfo.split(":");
            if(strList.size() >= 2)
            {
                strTarget = strList.at(0);
                strResult = strList.at(1);
            }
        }
        else
        {
            QStringList strList = strOneInfo.split(":");
            if(strList.size() >= 2)
            {
                strTarget = strList.at(0);
                strResult = strList.at(1);
            }
        }

        if("" == strTarget || "0" == strTarget || IsCtrlTarget(strTarget))
            continue;

        OBXScopedPtr obxScopedPtr;
        obxScopedPtr.Reset(CreateOBX());
        QString strObxIndexTmp = strObrIndexStr + "_" + QString::number(i);
        obxScopedPtr->SetOBXIndex(strObxIndexTmp.toUtf8());
        obxScopedPtr->SetValueType("ST");
        obxScopedPtr->SetItemName(strTarget.toUtf8());
        obxScopedPtr->SetItemResult(strResult.toUtf8());
        obxScopedPtr->SetTestTime(strTestDateTime.toUtf8());
        orderObrScopedPtr->AddOBX(obxScopedPtr.Get());
    }

    ////////////////////////////////////填充消息内容///////////////////////
    orderObrScopedPtr->SetOBR(obrScopedPtr.Get());

    patientResultScopedPtr->SetPatient(pidPtr.Get());
    patientResultScopedPtr->Add_ORU_R01_ORDER_OBSERVATION(orderObrScopedPtr.Get());
    oruR01ScopedPtr->SetMSH(mshPtr.Get());
    oruR01ScopedPtr->Add_Patient_Result(patientResultScopedPtr.Get());
    /////////////////////////////////////查询oruR01ScopedPtr指针是否是实现了IHLMessage接口////////////////
    IHL7Message* pIHL7Message = QueryInterface<IHL7Message, IObjectBase>(oruR01ScopedPtr.Get(), IF_HL7MESSAGE);
    std::string strMessage = "";
    PrintMessageStr(pIHL7Message, strMessage);
    oruR01ScopedPtr->Release();//调用QueryInterface 会调用oruR01ScopedPtr的AddRef,这里需要释放

    QString strTmp = QString::fromStdString(strMessage);
    qDebug()<<"Lis send message string:"<<strTmp;

    QByteArray byteMsg;
    byteMsg.append(0x0B);
    byteMsg.append(strTmp);
    byteMsg.append(0x1C);
    byteMsg.append(0x0D);

    return byteMsg;
}

QByteArray CHL7MsgThread::_GetQuerySampleByte(QString strSampleID)
{
    if(strSampleID.isEmpty())
        return "";

    QRYQ02ScopedPtr qryQ02ScopedPtr;
    qryQ02ScopedPtr.Reset(CreateQryQ02());

    MSHScopedPtr mshScopedPtr;
    mshScopedPtr.Reset(CreateMSH());

    QRDScopedPtr qrdScopedPtr;
    qrdScopedPtr.Reset(CreateQRD());

    //QRFScopedPtr qrfScopedPtr;
    //qrfScopedPtr.Reset(CreateQRF());

    mshScopedPtr.Get()->SetSendApp("WonDx-1000");
    QString strFacilityID = CPublicConfig::GetInstance()->GetMachineSN();
    mshScopedPtr.Get()->SetSendFacilityID(strFacilityID.toUtf8());//
    mshScopedPtr.Get()->SetSendFacilityName("全自动核酸扩增分析仪");
    mshScopedPtr.Get()->SetSendFacilityType("WonDx-1000");
    mshScopedPtr.Get()->SetRecvApp("");
    mshScopedPtr.Get()->SetRecvFacilityID("");
    mshScopedPtr.Get()->SetRecvFacilityName("");
    mshScopedPtr.Get()->SetRecvFacilityType("");
    mshScopedPtr.Get()->SetDate(QDateTime::currentDateTime().toString("yyyyMMddhhmmss").toUtf8());//信息的日期时间
    mshScopedPtr.Get()->SetMessageType("QRY");
    mshScopedPtr.Get()->SetTriggerEvent("Q02");
    mshScopedPtr.Get()->SetMessageStructure("QRY_Q02");
    mshScopedPtr.Get()->SetMsgCtrlID(_GetMsgCtrlID().toUtf8());
    mshScopedPtr.Get()->SetProcessingID("P");
    mshScopedPtr.Get()->SetVersionID("2.4");
    mshScopedPtr.Get()->SetApplicationAckType("0");
    mshScopedPtr.Get()->SetCountryCode("CHN");
    mshScopedPtr.Get()->SetCharacterSet("Unicode");

    //QRD填充
    qrdScopedPtr->SetQueryDateTime(QDateTime::currentDateTime().toString("yyyyMMddhhmmss").toUtf8());
    qrdScopedPtr->SetQueryFormatCode("R");//R
    qrdScopedPtr->SetQueryPriority("D");//D
    qrdScopedPtr->SetQueryID("1");
    qrdScopedPtr->SetQuantityLimitedRequest_Quantity("");
    qrdScopedPtr->SetQuantityLimitedRequest_Units("");
    qrdScopedPtr->SetWhatSubjectFilter("OTH");
    qrdScopedPtr->SetSampleBarcode(strSampleID.toStdString().c_str());
    qrdScopedPtr->SetQueryResultsLevel("T");

    ////QRF填充
    //qrfScopedPtr->SetWhereSubjectFilter("查询系统/设备");
    //qrfScopedPtr->SetWhenDataStartDateTime("开始时间");
    //qrfScopedPtr->SetWhenDataEndDateTime("结束时间");
    //qrfScopedPtr->SetWhichDateTimeQualifier("RC");//默认值
    //qrfScopedPtr->SetWhichDateTimeStatusQualifier("COR");//默认值
    //qrfScopedPtr->SetDateTimeSelectionQualifier("ALL");//默认值

    ////////////////////////////////////填充消息内容//////////////////////

    qryQ02ScopedPtr->SetMSH(mshScopedPtr.Get());
    qryQ02ScopedPtr->SetQRD(qrdScopedPtr.Get());
    //qryQ02ScopedPtr->SetQRF(qrfScopedPtr.Get());

    ////////////////////////查询qryQ02ScopedPtr指针是否是实现了IHLMessage接口，获取消息文本////////////////
    IHL7Message* message = QueryInterface<IHL7Message, IObjectBase>(qryQ02ScopedPtr.Get(), IF_HL7MESSAGE);
    std::string strMessage = "";
    PrintMessageStr(message, strMessage);
    qryQ02ScopedPtr->Release();//调用QueryInterface 会调用qryQ02ScopedPtr的AddRef,这里需要释放

    QString strTmp = QString::fromStdString(strMessage);
    qDebug()<<"Lis query sample info:"<<strSampleID<<strTmp;

    QByteArray byteMsg;
    byteMsg.append(0x0B);
    byteMsg.append(strTmp);
    byteMsg.append(0x1C);
    byteMsg.append(0x0D);

    return byteMsg;
}

QString CHL7MsgThread::_GetMsgCtrlID()
{
    QString strMsgID = QString::number(m_iMsgCtrID);
    m_iMsgCtrID++;
    return strMsgID;
}

void CHL7MsgThread::_ParseSampleInfo(QByteArray qByteData)
{
    IHL7Message *pMessagePtr = Parse(qByteData.toStdString());
    if(nullptr == pMessagePtr)
        return;

    qDebug()<<"parse lis sample info";
    IDSR_Q03* dsr_q03 = QueryInterface<IDSR_Q03, IHL7Message>(pMessagePtr, IF_DSR_Q03);
    if(dsr_q03)
    {
        bool isExist = true;

        DSRQ03ScopedPtr tmp;
        tmp.Reset(dsr_q03);

        MSHScopedPtr mshSP;
        mshSP.Reset(CreateMSH());
        tmp->GetMSH(mshSP.Get());

        MSAScopedPtr msaSP;
        msaSP.Reset(CreateMSA());
        isExist = tmp->GetMSA(msaSP.Get());
        qDebug() << "GetMSA isExist" << isExist;

        QRDScopedPtr qrdSP;
        qrdSP.Reset(CreateQRD());
        isExist = tmp->GetQRD(qrdSP.Get());
        qDebug() << "GetQRD isExist" << isExist;

        QRFScopedPtr qrfSP;
        qrfSP.Reset(CreateQRF());
        isExist = tmp->GetQRF(qrfSP.Get());
        qDebug() << "GetQRF isExist" << isExist;
        qrfSP.Release();

        ERRScopedPtr errSP;
        errSP.Reset(CreateERR());
        isExist = tmp->GetERR(errSP.Get());
        qDebug() << "GetERR isExist" << isExist;

        QAKScopedPtr qakSP;
        qakSP.Reset(CreateQAK());
        isExist = tmp->GetQAK(qakSP.Get());
        qDebug() << "GetQAK isExist" << isExist;

        DSCScopedPtr dscSP;
        dscSP.Reset(CreateDSC());
        isExist = tmp->GetDSC(dscSP.Get());
        qDebug() << "GetDSC isExist" << isExist;

        DSPScopedPtr dspSP;
        dspSP.Reset(CreateDSP());
        int dspSize = tmp->GetDSPSize();
        QMap<int, QString> strDSPMap;
        for(int i = 0; i != dspSize; ++i)
        {
            isExist = tmp->GetDSP(dspSP.Get(), i);
            char *pDspID = NULL;
            char *pDspLine = NULL;
            dspSP.Get()->GetDSPID(&pDspID);
            dspSP.Get()->GetDataLine(&pDspLine);
            qDebug() << "GetDSP isExist" << dspSize << isExist << pDspID << pDspLine;
            strDSPMap.insert(QString::fromLocal8Bit(pDspID).toInt(), QString::fromLocal8Bit(pDspLine));
            FreeMemory(pDspID);
            FreeMemory(pDspLine);
        }
        emit SignalSampleInfoMap(strDSPMap);
        qDebug()<<"Lis reply sample info:"<<strDSPMap;

        mshSP.Release();
        msaSP.Release();
        qrdSP.Release();
        qrfSP.Release();
        errSP.Release();
        qakSP.Release();
        dscSP.Release();
        dspSP.Release();
    }
    pMessagePtr->Release();
}
