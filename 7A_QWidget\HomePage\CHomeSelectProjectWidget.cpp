#include "CHomeSelectProjectWidget.h"
#include <QPainter>
#include <QListView>
#include <QBoxLayout>
#include <QStyleFactory>

#include "PublicParams.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "CLotInfoDB.h"
#include "CTimingTecDB.h"

CProjectItemWidget::CProjectItemWidget(int index, const QString &strProject, const QStringList &strTimingList,
                                       const QStringList &strTecList, QWidget *parent)
    : QWidget(parent)
    , m_index(index)
{
    m_pIndexLabel = new QLabel(QString::number(index + 1));
    m_pIndexLabel->setFixedSize(30, 50);

    m_pCheckBox = new QCheckBox;
    m_pCheckBox->setFixedSize(28, 28);
    connect(m_pCheckBox, &QCheckBox::clicked, this, &CProjectItemWidget::_SlotCheckBox);

    m_pProjectLabel = new QLabel(strProject);
    m_pProjectLabel->setFixedSize(450, 50);

    m_pTimingComboBox = new QComboBox;
    m_pTimingComboBox->setView(new QListView);
    m_pTimingComboBox->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_pTimingComboBox->setMaxVisibleItems(7);
    m_pTimingComboBox->setStyle(QStyleFactory::create("Windows"));
    m_pTimingComboBox->addItems(strTimingList);
    m_pTimingComboBox->setFixedSize(300, 50);

    m_pPCRComboBox = new QComboBox;
    m_pPCRComboBox->setView(new QListView);
    m_pPCRComboBox->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_pPCRComboBox->setMaxVisibleItems(7);
    m_pPCRComboBox->setStyle(QStyleFactory::create("Windows"));
    m_pPCRComboBox->addItems(strTecList);
    m_pPCRComboBox->setFixedSize(300, 50);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(20);
    pLayout->addSpacing(30);
    pLayout->addWidget(m_pIndexLabel);
    pLayout->addWidget(m_pCheckBox);
    pLayout->addWidget(m_pProjectLabel);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pTimingComboBox);
    pLayout->addWidget(m_pPCRComboBox);
    pLayout->addSpacing(10);
    this->setLayout(pLayout);
}

void CProjectItemWidget::SetTimingTecIndex(int iTimingIndex, int iTecIndex)
{
    m_iRawTimingIndex = iTimingIndex;
    m_iRawTecIndex = iTecIndex;

    m_pTimingComboBox->setCurrentIndex(iTimingIndex);
    m_pPCRComboBox->setCurrentIndex(iTecIndex);
}

void CProjectItemWidget::SetChecked(bool bChecked)
{
    m_pCheckBox->setChecked(bChecked);
}

bool CProjectItemWidget::GetChecked() const
{
    return m_pCheckBox->isChecked();
}

int CProjectItemWidget::GetIndex() const
{
    return m_index;
}

void CProjectItemWidget::SetShowIndex(int index)
{
    m_pIndexLabel->setText(QString::number(index));
}

QString CProjectItemWidget::GetProjectName() const
{
    return m_pProjectLabel->text();
}

QString CProjectItemWidget::GetTimingName() const
{
    return m_pTimingComboBox->currentText();
}

QString CProjectItemWidget::GetTecName() const
{
    return m_pPCRComboBox->currentText();
}

void CProjectItemWidget::showEvent(QShowEvent *pEvent)
{
    if(CPublicConfig::GetInstance()->GetLoginLevel() < eUser_Factory)
    {
        m_pTimingComboBox->setVisible(false);
        m_pPCRComboBox->setVisible(false);
    }
    else
    {
        m_pTimingComboBox->setVisible(true);
        m_pPCRComboBox->setVisible(true);
    }

    QWidget::showEvent(pEvent);
}

void CProjectItemWidget::hideEvent(QHideEvent *pEvent)
{
    m_pTimingComboBox->setCurrentIndex(m_iRawTimingIndex);
    m_pPCRComboBox->setCurrentIndex(m_iRawTecIndex);

    QWidget::hideEvent(pEvent);
}

void CProjectItemWidget::_SlotCheckBox()
{
    if(m_pCheckBox->isChecked())
        emit SignalItemChecked(m_index);
}

CHomeSelectProjectWidget::CHomeSelectProjectWidget(QWidget *parent) : QWidget(parent)
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setFixedSize(G_QRootSize);
    this->move(G_QRootPoint);
    this->setAttribute(Qt::WA_TranslucentBackground);

    _InitWidget();
    _InitLayout();

    LoadQSS(this, ":/qss/qss/home/<USER>");
}

void CHomeSelectProjectWidget::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, 30));
    QWidget::paintEvent(pEvent);
}

void CHomeSelectProjectWidget::showEvent(QShowEvent *pEvent)
{
    if(CPublicConfig::GetInstance()->GetLoginLevel() < eUser_Factory)
    {
        m_pBackgroundLabel->setFixedSize(1000, 540);
        m_pListWidget->setFixedSize(850, 350);

        int index = 1;
        for(int iRow=0; iRow<m_pListWidget->count(); iRow++)
        {
            QListWidgetItem *pItem = m_pListWidget->item(iRow);
            CProjectItemWidget *pWidget = dynamic_cast<CProjectItemWidget *>(m_pListWidget->itemWidget(pItem));
            if(nullptr == pWidget)
                continue;
            if("TEST" == pWidget->GetProjectName() || "HRM/TEST" == pWidget->GetProjectName())
            {
                pItem->setHidden(true);
            }
            else
            {
                pWidget->SetShowIndex(index);
                index++;
            }
        }
    }
    else
    {
        m_pBackgroundLabel->setFixedSize(1400, 660);
        m_pListWidget->setFixedSize(1250, 470);

        for(int iRow=0; iRow<m_pListWidget->count(); iRow++)
        {
            QListWidgetItem *pItem = m_pListWidget->item(iRow);
            CProjectItemWidget *pWidget = dynamic_cast<CProjectItemWidget *>(m_pListWidget->itemWidget(pItem));
            if(nullptr == pWidget)
                continue;
            if("TEST" == pWidget->GetProjectName() || "HRM/TEST" == pWidget->GetProjectName())
                pItem->setHidden(false);
            pWidget->SetShowIndex(iRow + 1);
        }
    }

    _SlotCurrentRowChanged(0);
    m_pListWidget->setCurrentRow(0);

    QWidget::showEvent(pEvent);
}

void CHomeSelectProjectWidget::hideEvent(QHideEvent *pEvent)
{
    QWidget::hideEvent(pEvent);
}

void CHomeSelectProjectWidget::_SlotConfirmBtn()
{
    for(int i=0; i<m_pListWidget->count(); i++)
    {
        QListWidgetItem *pItem = m_pListWidget->item(i);
        CProjectItemWidget *pWidget = dynamic_cast<CProjectItemWidget *>(m_pListWidget->itemWidget(pItem));
        if(pWidget)
        {
            if(pWidget->GetChecked())
            {
                QString strProjectName = pWidget->GetProjectName();
                QString strTimingName = pWidget->GetTimingName();
                QString strTecName = pWidget->GetTecName();
                emit SignalSelectProject(strProjectName, strTimingName, strTecName);
                break;
            }
        }
    }

    this->close();
}

void CHomeSelectProjectWidget::_SlotCancelBtn()
{
    this->close();
}

void CHomeSelectProjectWidget::_SlotCurrentRowChanged(int iRow)
{
    for(int i=0; i<m_pListWidget->count(); i++)
    {
        QListWidgetItem *pItem = m_pListWidget->item(i);
        CProjectItemWidget *pWidget = dynamic_cast<CProjectItemWidget *>(m_pListWidget->itemWidget(pItem));
        if(pWidget)
        {
            if(iRow == pWidget->GetIndex())
                pWidget->SetChecked(true);
            else
                pWidget->SetChecked(false);
        }
    }
}

void CHomeSelectProjectWidget::_SlotItemChecked(int iItemIndex)
{
    for(int i=0; i<m_pListWidget->count(); i++)
    {
        QListWidgetItem *pItem = m_pListWidget->item(i);
        CProjectItemWidget *pWidget = dynamic_cast<CProjectItemWidget *>(m_pListWidget->itemWidget(pItem));
        if(pWidget)
        {
            if(iItemIndex != pWidget->GetIndex())
                pWidget->SetChecked(false);
        }
    }
}

void CHomeSelectProjectWidget::_InitWidget()
{  
    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(1250, 630);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");

    m_pCHLabelTitleWidget = new CHLabelTitleWidget(tr("选择测试项目"));

    QStringList strTimingList = CTimingTecDB::GetInstance().GetTimingNameList();
    QStringList strTecList = CTimingTecDB::GetInstance().GetTecNameList();
    strTecList.push_front("");
    QStringList strProjectList = CLotInfoDB::GetInstance()->GetAllProjectShowName();

    m_pListWidget = new QListWidget;
    m_pListWidget->setFixedSize(1100, 420);
    m_pListWidget->setFocusPolicy(Qt::NoFocus);
    m_pListWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    for(int i=0; i<strProjectList.size(); i++)
    {
        QString strShowProject = strProjectList.at(i);
        QStringList strDBTimingTecList = CTimingTecDB::GetInstance().GetProjectRunTimingTec(strShowProject);
        int iTimingIndex = 0;
        int iTecIndex= -1;
        if(strDBTimingTecList.size() >= 2)
        {
            iTimingIndex = strTimingList.indexOf(strDBTimingTecList.at(0));
            iTecIndex = strTecList.indexOf(strDBTimingTecList.at(1));
        }

        QListWidgetItem *pItem = new QListWidgetItem(m_pListWidget);
        pItem->setSizeHint(QSize(1100, 60));
        CProjectItemWidget *pWidget = new CProjectItemWidget(i, strShowProject, strTimingList, strTecList);
        pWidget->SetTimingTecIndex(iTimingIndex, iTecIndex);
        connect(pWidget, &CProjectItemWidget::SignalItemChecked, this, &CHomeSelectProjectWidget::_SlotItemChecked);
        m_pListWidget->setItemWidget(pItem, pWidget);
    }
    connect(m_pListWidget, &QListWidget::currentRowChanged, this, &CHomeSelectProjectWidget::_SlotCurrentRowChanged);

    m_pConfirmBtn = new QPushButton(tr("确认"));
    m_pConfirmBtn->setFixedSize(150, 56);
    connect(m_pConfirmBtn, &QPushButton::clicked, this, &CHomeSelectProjectWidget::_SlotConfirmBtn);

    m_pCancelBtn = new QPushButton(tr("取消"));
    m_pCancelBtn->setFixedSize(150, 56);
    m_pCancelBtn->setObjectName("CancelBtn");
    connect(m_pCancelBtn, &QPushButton::clicked, this, &CHomeSelectProjectWidget::_SlotCancelBtn);
}

void CHomeSelectProjectWidget::_InitLayout()
{
    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(50);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pCancelBtn);
    pBtnLayout->addWidget(m_pConfirmBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pBackLayout = new QVBoxLayout;
    pBackLayout->setContentsMargins(24, 12, 24, 24);
    pBackLayout->setSpacing(0);
    pBackLayout->addWidget(m_pCHLabelTitleWidget, 0, Qt::AlignLeft);
    pBackLayout->addSpacing(15);
    pBackLayout->addWidget(m_pListWidget, 0, Qt::AlignHCenter);
    pBackLayout->addStretch(1);
    pBackLayout->addLayout(pBtnLayout);
    m_pBackgroundLabel->setLayout(pBackLayout);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pBackgroundLabel, 0, Qt::AlignVCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}
