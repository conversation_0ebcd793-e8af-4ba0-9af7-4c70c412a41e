#ifndef CLIGHTONECURVE_H
#define CLIGHTONECURVE_H

#include <QWidget>

class QCustomPlot;
class CSetChartXYRange;

class CLightOneCurve : public QWidget
{
    Q_OBJECT
public:
    explicit CLightOneCurve(QWidget *parent = nullptr);
    ~CLightOneCurve();

    void ClearData();
    void ReceiveData(const QVariant &qVarData);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual void hideEvent(QHideEvent *pEvent) override;

private slots:
    void _SlotSetXYRange(const QStringList &strList);

private:
    void _InitWidget();
    void _InitCustomPlot();
    void _AddGraph(QCustomPlot* pCustomPlot, QColor penColor, QColor pointColor,
                  int iChart, QString strChartName);
    void _UpdatePlot(bool bResetRange = false);

private:
    bool m_bShow;
    QCustomPlot *m_pCustomPlot;
    CSetChartXYRange *m_pCSetChartXYRange;
    int m_iPointCount;
    int m_iReadLightIndex;
};

#endif // CLIGHTONECURVE_H
