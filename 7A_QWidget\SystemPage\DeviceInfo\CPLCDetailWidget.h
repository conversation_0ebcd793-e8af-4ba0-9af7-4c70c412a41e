#ifndef CPLCDETAILWIDGET_H
#define CPLCDETAILWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-08-05
  * Description: 下位机详情
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QGroupBox>
#include <QPushButton>

#include "CCmdBase.h"
#include "CLineEdit.h"
#include "CPressLabel.h"
#include "CDateTimeWidget.h"
#include "CHLabelTitleWidget.h"
#include "PublicParams.h"

struct SDetailInfoStruct
{
    SDetailInfoStruct()
    {
        iMachineID = -1;
        eStatus = eDeviceDisconnect;
        strSN.clear();
        strVersion.clear();
        strMADE.clear();
    }

    int iMachineID;
    DeviceStatus eStatus;
    QString strSN;
    QString strVersion;
    QString strMADE;
};

class CPLCDetailWidget : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    CPLCDetailWidget(QWidget *parent = nullptr);
    ~CPLCDetailWidget();

    void PageShow(int iMachineID);

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData);

public slots:
    void SlotSetDevStatus(int iMachineID, DeviceStatus eStatus);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual void hideEvent(QHideEvent *pEvent) override;
    virtual void paintEvent(QPaintEvent* pEvent) override;

private slots:
    void _SlotEditBtn();
    void _SlotScanBtn();
    void _SlotResetBtn();
    void _SlotIdentifyBtn();
    void _SlotShowDateTime();
    void _SlotConfirmDateTime(const QString &strDateTime);
    void _SlotScanData(QByteArray qScanData);

private:
    QGroupBox *_CreateGroupBox();

private:
    CHLabelTitleWidget *m_pCHLabelTitleWidget;

    QLabel *m_pImageLabel;

    QLabel *m_pIDLabel;
    QLabel *m_pSNLabel;
    CLineEdit *m_pSNLineEdit;
    QLabel *m_pFirmVerLabel;
    QLabel *m_pDateNameLabel;
    CPressLabel *m_pDateValueLabel;
    CDateTimeWidget *m_pCDateTimeWidget;
    QLabel *m_pManufacturerLabel;
    QLabel *m_pAddressNameLabel;
    QLabel *m_pAddressValueLabel;
    QLabel *m_pEffectiveDateLabel;
    QPushButton *m_pCloseBtn, *m_pEditBtn, *m_pScanBtn, *m_pResetBtn, *m_pIdentifyBtn;

private:
    bool m_bShow;
    bool m_bEdit;
    int m_iCurrentMachineID;
    QList<SDetailInfoStruct> m_sDetailInfoList;
};

#endif // CPLCDETAILWIDGET_H
