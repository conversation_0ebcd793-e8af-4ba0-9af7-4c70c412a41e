#ifndef CMESSAGEBOX_H
#define CMESSAGEBOX_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-27
  * Description: 新的弹窗
  * -------------------------------------------------------------------------
  * History: 结尾加上?或者.
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

enum POPMSG_TYPE
{
    ePopMsg_Info     = 0,  //提示弹窗
    ePopMsg_Error    = 1,  //错误弹窗
    ePopMsg_Success  = 2,  //成功弹窗
    ePopMsg_Question = 3,  //提问弹窗
    ePopMsg_Warning  = 4,  //警告弹窗
    ePopMsg_Crital   = 5,  //故障弹窗
};

#include <QLabel>
#include <QDialog>
#include <QMessageBox>
#include <QPushButton>

#include "CHLabelTitleWidget.h"

class CNewMessageBox : public QDialog
{
    Q_OBJECT
public:
    CNewMessageBox(const QString &strTitle, const QString &strText, POPMSG_TYPE eMsgType, QWidget *parent = nullptr);
    ~CNewMessageBox();

    QMessageBox::StandardButton GetClickedBtn() const;

protected:
    void paintEvent(QPaintEvent* pEvent) override;

signals:
    void SignalCancel();
    void SignalConfirm();

private slots:
    void _SlotCancelBtn();
    void _SlotConfirmBtn();

private:
    QString _GetIconPath(POPMSG_TYPE eMsgType);
    void _InitWidget();
    void _InitLayout();

private:
    CHLabelTitleWidget *m_pCHLabelTitleWidget;
    QLabel *m_pIconLabel;
    QLabel *m_pTextLabel;
    QLabel *m_pBackgroundLabel;

    QPushButton *m_pCancalBtn;
    QPushButton *m_pConfirmBtn;

    QString m_strTitle;
    QString m_strText;
    POPMSG_TYPE m_eMsgType;
    QMessageBox::StandardButton m_eStandardBtn;
};

QMessageBox::StandardButton ShowInformation(QWidget *parent, const QString &title, const QString &text);

QMessageBox::StandardButton ShowError(QWidget *parent, const QString &title, const QString &text);

QMessageBox::StandardButton ShowSuccess(QWidget *parent, const QString &title, const QString &text);

QMessageBox::StandardButton ShowQuestion(QWidget *parent, const QString &title, const QString &text);

QMessageBox::StandardButton ShowWarning(QWidget *parent, const QString &title, const QString &text);

QMessageBox::StandardButton ShowCritical(QWidget *parent, const QString &title, const QString &text);

#endif // CMESSAGEBOX_H
