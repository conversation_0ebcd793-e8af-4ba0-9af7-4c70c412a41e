#include "CHistoryDetailWidget.h"
#include <QBoxLayout>

#include "CProjectDB.h"
#include "CHistoryDB.h"
#include "CLotInfoDB.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "CRunTest.h"
#include "CCreateImage.h"

CHistoryDetailWidget::CHistoryDetailWidget(const QString &strTitle, bool bHistoryMode, int iMachineID, QWidget *parent)
    : QWidget(parent)
    , m_strTitle(strTitle)
    , m_bHistoryMode(bHistoryMode)
    , m_iMachineID(iMachineID)
{

    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setFixedSize(G_QRootSize);
    this->move(G_QRootPoint);
    this->setAttribute(Qt::WA_TranslucentBackground);

    _InitWidget();
    _InitLayout();

    {
        connect(CRunTest::GetInstance(), &CRunTest::SignalUpdateItemCalcResult,
                this, &CHistoryDetailWidget::_SlotUpdateCalcResult);
    }
    LoadQSS(this, ":/qss/qss/history/detail.qss");
}

void CHistoryDetailWidget::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, gk_iBackTransparency + 10));
    QWidget::paintEvent(pEvent);
}


void CHistoryDetailWidget::ClearData()
{
    qDebug()<<Q_FUNC_INFO;

    //更新UI前先清除旧数据,不包含荧光数据
    m_sResultInfo.Clear();
    m_sSampleInfo.Clear();
    m_sCardInfo.Clear();
    m_sLotInfo.Clear();

    m_AmpCurve->ClearData();
    m_HrmCurve->ClearData();

    m_pDevItemLabel->setText(tr("检测模块："));
    m_pDevSNLabel->setText(tr("模块SN："));
    m_pVersionLabel->setText(tr("版本："));
    m_pDevItemStateLabel->setText(tr("测试状态："));
    m_pOperatorLabel->setText(tr("操作者："));
    m_pTestTypeLabel->setText(tr("测试类型："));
    //m_pResultLabel->setText(tr("测试结果："));

    m_pSampleIDLabel->setText(tr("样本编号："));
    m_pSampleTypeLabel->setText(tr("样本类型："));
    m_pSamplingDateLabel->setText(tr("采样日期："));
    m_pNameLabel->setText(tr("姓名："));
    m_pGenderLabel->setText(tr("性别："));
    m_pAgeLabel->setText(tr("年龄："));
    m_pBirthdayLabel->setText(tr("生日："));
    m_pTelLabel->setText(tr("电话："));


    m_pProjectLabel->setText(tr("测试项目："));
    m_pCardIDLabel->setText(tr("试剂卡编号："));
    m_pCardSNLabel->setText(tr("试剂卡SN码："));
    m_pCardMFGLabel->setText(tr("试剂卡生产日期："));
    m_pCardEXPLabel->setText(tr("试剂卡有效日期："));
}

void CHistoryDetailWidget::ClearFL()
{
    //一起清除荧光数据
    m_AmpCurve->ClearFL();
    m_HrmCurve->ClearFL();
}

void CHistoryDetailWidget::SetHistoryID(int iHistoryID)
{
    qDebug()<<Q_FUNC_INFO<<"iHistoryID:"<<iHistoryID;

    ClearData();

    if(!CProjectDB::GetInstance()->GetHistoryData(iHistoryID, m_sResultInfo))
        return;

    CProjectDB::GetInstance()->GetSampleData(m_sResultInfo.strProjectName, m_sResultInfo.strSampleID, m_sResultInfo.strTestTime, m_sSampleInfo);
    CProjectDB::GetInstance()->GetCardData(m_sResultInfo.strCardID, m_sResultInfo.strTestTime, m_sCardInfo);
    CLotInfoDB::GetInstance()->GetLotInfoByShowName(m_sResultInfo.strProjectName, m_sLotInfo);
    qDebug()<<Q_FUNC_INFO<<"sample info"<<m_sSampleInfo.strSampleID<<m_sSampleInfo.strTestTime;
    qDebug()<<Q_FUNC_INFO<<"card info"<<m_sCardInfo.strCardID<<m_sCardInfo.strTestTime;
    SetHistoryIdHandle(m_sResultInfo,m_sSampleInfo,m_sCardInfo,m_sLotInfo,false);
}

void CHistoryDetailWidget::SetHistoryIdHandle(const SResultInfoStruct& sResultInfo,const SSampleInfoStruct& sSampleInfo,const SCardInfoStruct& sCardInfo,const SLotInfoStruct& sLotInfo,bool bPdfImage)
{
    QStringList strSNList = sResultInfo.strTmValue.split("&");
    QString strDevSN;
    if(strSNList.size() >= 2)
    {
        strDevSN = strSNList.at(1);
    }
    if(strDevSN.isEmpty())
    {
        //之前数据库没保存SN的记录,以当前设备的SN为准
        strDevSN = CPublicConfig::GetInstance()->GetPLCVersionStruct(sResultInfo.iMachineID).strSN;
    }
    //strDevSN = "WD100125042B0002";
    m_pDevSNLabel->setText(tr("模块SN：") + strDevSN);

    QString strVersion;
    if(strSNList.size() >= 3)
    {
        strVersion = strSNList.at(2);
    }
    if(strVersion.isEmpty())
    {
        strVersion = CPublicConfig::GetInstance()->GetFullVersion();
    }
    m_pVersionLabel->setText(tr("版本：") + strVersion);

    m_pDevItemLabel->setText(tr("检测模块：") + QString("%1#").arg(sResultInfo.iMachineID + 1));
    m_pDevItemStateLabel->setText(tr("测试状态：") + QString("%1").arg(CPublicConfig::GetInstance()->GetStatusShowString(sResultInfo.iStatus)));

    //m_pResultLabel->setText(_GetResultText(sResultInfo,sLotInfo.strCurveName));
    m_pTestTypeLabel->setText(tr("测试类型：") + CPublicConfig::GetInstance()->GetTestTypeShowString(sResultInfo.strMode));
    m_pOperatorLabel->setText(tr("操作者：") + sResultInfo.strOperator);
    m_pSampleIDLabel->setText(tr("样本编号：") + sSampleInfo.strSampleID);
    m_pSampleTypeLabel->setText(tr("样本类型：") + sSampleInfo.strSampleType);
    m_pSamplingDateLabel->setText(tr("采样日期：") + sSampleInfo.strSamplingDate);
    m_pNameLabel->setText(tr("姓名：") + sSampleInfo.strName);
    m_pGenderLabel->setText(tr("性别：") + sSampleInfo.strGender);
    m_pAgeLabel->setText(tr("年龄：") + sSampleInfo.strAge);
    m_pBirthdayLabel->setText(tr("生日：") + sSampleInfo.strBirthday);
    m_pTelLabel->setText(tr("电话：") + sSampleInfo.strTelephone);


    m_pProjectLabel->setText(tr("测试项目：") + sResultInfo.strProjectName);
    m_pCardIDLabel->setText(tr("试剂卡编号：") + sCardInfo.strCardID);
    m_pCardSNLabel->setText(tr("试剂卡批次：") + sCardInfo.strCardLot);
    m_pCardMFGLabel->setText(tr("试剂卡生产日期：") + sCardInfo.strCardMFG);
    m_pCardEXPLabel->setText(tr("试剂卡有效日期：") + sCardInfo.strCardEXP);

    // 这里直接调用更新结果
    //  这里可以判断是否hrm曲线

    if(bPCRTecType(sResultInfo.iTestProject))
    {
        m_pStackWidget->setCurrentIndex(0);
        if(bPdfImage)
        {
            m_AmpCurve->PdfSpecialRequirement(bPdfImage);
        }
        m_AmpCurve->SetHistoryIDHandle(sCardInfo,sResultInfo,sLotInfo);
    }
    else if(bHrmTecType(sResultInfo.iTestProject))
    {
        m_pStackWidget->setCurrentIndex(1);
        m_HrmCurve->SetHistoryIDHandle(sCardInfo,sResultInfo,sLotInfo);
    }
}

void CHistoryDetailWidget::SetFLDataMap(int iMachineID,const QList<QMap<double, double> > &dFLMap)
{
    if(nullptr == m_AmpCurve || nullptr == m_HrmCurve)
    {
        return;
    }

    if(bHrmTecType(CRunTest::GetInstance()->GetRunInfoStruct(iMachineID).iTecIndex))
    {
        m_HrmCurve->SetFLDataMap(dFLMap);
    }
    else
    {
        m_AmpCurve->SetFLDataMap(dFLMap);
    }

}

void CHistoryDetailWidget::SetMeltingFLDataMap(int iMachineID, const QList<double> &dTempList, const QList<QMap<double, double> > &dFLMap)
{
    if(nullptr == m_HrmCurve)
    {
        return;
    }

    if(bHrmTecType(CRunTest::GetInstance()->GetRunInfoStruct(iMachineID).iTecIndex))
    {
        m_HrmCurve->SetFLMeltingDataMap(dTempList,dFLMap);
    }
}

void CHistoryDetailWidget::UpdateInfo(int iHistoryID)
{
    if(!CProjectDB::GetInstance()->GetHistoryData(iHistoryID, m_sResultInfo))
        return;

    // 这里可以判断是否hrm测试
    m_pDevItemStateLabel->setText(tr("测试状态：") + QString("%1").arg(CPublicConfig::GetInstance()->GetStatusShowString(m_sResultInfo.iStatus)));
    //m_pDevItemStateLabel->setText(CPublicConfig::GetInstance()->GetStatusShowString(m_sResultInfo.iStatus));
    if(bPCRTecType(m_sResultInfo.iTestProject))
    {
        m_pStackWidget->setCurrentIndex(0);
        m_AmpCurve->UpdateInfo(m_sResultInfo);

    }
    else if(bHrmTecType(m_sResultInfo.iTestProject))
    {
        m_pStackWidget->setCurrentIndex(1);
        m_HrmCurve->UpdateInfo(m_sResultInfo);
    }

}


void CHistoryDetailWidget::_SlotCloseBtn()
{
    this->close();

    if(bPCRTecType(m_sResultInfo.iTestProject))
    {
        m_AmpCurve->closeBtnHandle();
    }
    else if(bHrmTecType(m_sResultInfo.iTestProject))
    {
        m_HrmCurve->closeBtnHandle();
    }
}

static void ThreadSaveImage(QStringList strNameList, QList<QImage> qImageList)
{
    qDebug()<<Q_FUNC_INFO<<QThread::currentThreadId()<<strNameList<<qImageList.size();

    int iMin = qMin(strNameList.size(), qImageList.size());
    for(int i=0; i<iMin; i++)
    {
       bool ok = qImageList.at(i).save(strNameList.at(i), "png");
       qDebug()<<strNameList.at(i)<<"save"<<ok;
    }
}

void CHistoryDetailWidget::_SlotUpdateCalcResult(int iMachineID)
{
    if(m_sResultInfo.iMachineID != iMachineID)
         return;
    SRunningInfoStruct &sRunInfo = CRunTest::GetInstance()->GetRunInfoStruct(iMachineID);
    qDebug()<<Q_FUNC_INFO<<"iMachineID:"<<iMachineID<<"iHistoryID:"<<sRunInfo.sResultInfo.iHistoryID
           <<"m_bHistoryMode"<<m_bHistoryMode;

    if(this->isVisible() && sRunInfo.sResultInfo.iHistoryID == m_sResultInfo.iHistoryID)
    {
        SetHistoryID(sRunInfo.sResultInfo.iHistoryID);
    }    
}

void CHistoryDetailWidget::SavePdfImage(const SRunningInfoStruct &sRunInfo, bool bReview)
{
    m_PngPathStrList.clear();
    QTime t1 = QTime::currentTime();
    SaveImageHandle(t1,sRunInfo,m_PngPathStrList,bReview);
    if(bReview)
    {
        QString strProject = sRunInfo.sResultInfo.strProjectName;
        strProject.replace("/", "+");

        QString strTestTime = sRunInfo.sResultInfo.strTestTime;
        QString strBaseName = QString("%1_%2#_%3_%4_%5")
                .arg(strTestTime.remove(" ").remove("-").remove(":"))
                .arg(sRunInfo.sResultInfo.iMachineID + 1)
                .arg(DeleteSpecialCharacters(sRunInfo.sSampleInfo.strSampleID))
                .arg(DeleteSpecialCharacters(sRunInfo.sCardInfo.strCardID))
                .arg(DeleteSpecialCharacters(strProject));
        strBaseName.remove("\\").remove("/").remove(":").remove("*").remove("?");
        strBaseName.remove("\"").remove("<").remove(">").remove("|").remove(" ");
        QString strPdfName = CPublicConfig::GetInstance()->GetPdfDir() + strBaseName + ".pdf";
        CRunTest::GetInstance()->WritePdf(sRunInfo.sResultInfo.iMachineID,strPdfName,sRunInfo,m_PngPathStrList);
    }
    else
    {
        CRunTest::GetInstance()->AddPngPath(sRunInfo.sResultInfo.iMachineID, m_PngPathStrList);
    }
}

void CHistoryDetailWidget::_SlotReviewConfirm(SResultInfoStruct sResultInfo)
{
    // 审核之后，我们这边保存图片；    
    SRunningInfoStruct sRunInfo;
    sRunInfo.sCardInfo = m_sCardInfo;
    sRunInfo.sSampleInfo = m_sSampleInfo;
    sRunInfo.sResultInfo = sResultInfo;
    sRunInfo.iTecIndex =  sResultInfo.iTestProject;
    CCreateImage::GetInstance()->SaveHistoryDetailImage(sRunInfo,true);

}

void CHistoryDetailWidget::SaveImageHandle(const QTime& t1,const SRunningInfoStruct& sRunInfo,QStringList& strPngPathList,bool bReviewConfirm)
{

    //增加详情归一化的截图 20250213
    QTime t2 = QTime::currentTime();
    QString strCurveName = CLotInfoDB::GetInstance()->GetCurveNameByProjectName(sRunInfo.sResultInfo.strProjectName);

    bool bTwoHole = (strCurveName.split(";").size() >= 8);
    QString strAppPath = QApplication::applicationDirPath() + "/";

    QList<QImage> qImageList;

    if(bPCRTecType(sRunInfo.sResultInfo.iTestProject))
    {
        m_AmpCurve->SetReviewMode(bReviewConfirm);

        //m_AmpCurve->ShowRawData(); //显示原始数据
        m_AmpCurve->ShowNmzaData(); //显示归一化数据
        m_AmpCurve->ThresholdLlineShow(false);

        m_AmpCurve->SwitchHole(0);
        m_AmpCurve->update();
        qImageList << this->grab(this->rect()).toImage();
        strPngPathList << strAppPath + "a" + t1.toString("hhmmsszzz") + ".png";

        if(bTwoHole)
        {
            m_AmpCurve->SwitchHole(1);
            m_AmpCurve->update();
            qImageList << this->grab(this->rect()).toImage();
            strPngPathList << strAppPath + "b" + t1.toString("hhmmsszzz") + ".png";
        }

        m_AmpCurve->SwitchHole(-1);
        m_AmpCurve->ShowRawData();
    }
    if(bHrmTecType(sRunInfo.sResultInfo.iTestProject))
    {
        m_HrmCurve->SetReviewMode(bReviewConfirm);

        //m_HrmCurve->ShowRawData(); //显示原始数据
        m_HrmCurve->ShowPCRNmzaData(); //显示扩增归一化数据

        //PCR扩增
        m_HrmCurve->SwitchTab(0);

        m_HrmCurve->SwitchHole(0); //孔1
        m_HrmCurve->update();
        qImageList << this->grab(this->rect()).toImage();
        strPngPathList << strAppPath + "a" + t1.toString("hhmmsszzz") + ".png";

        if(bTwoHole)
        {
            m_HrmCurve->SwitchHole(1); //孔2
            m_HrmCurve->update();
            qImageList << this->grab(this->rect()).toImage();
            strPngPathList << strAppPath + "b" + t1.toString("hhmmsszzz") + ".png";
        }

        //熔解
        m_HrmCurve->SwitchTab(1);
        m_HrmCurve->ShowHRMPeakData();
        m_HrmCurve->SwitchHole(0); //孔1
        qImageList << this->grab(this->rect()).toImage();
        strPngPathList << strAppPath + "c" + t1.toString("hhmmsszzz") + ".png";

        if(bTwoHole)
        {
            m_HrmCurve->SwitchHole(1); //孔2
            qImageList << this->grab(this->rect()).toImage();
            strPngPathList << strAppPath + "d" + t1.toString("hhmmsszzz") + ".png";
        }

        m_HrmCurve->SwitchHole(-1); //孔1+孔2
        m_HrmCurve->Reset2RawData();
    }

    if(!bReviewConfirm)
    {
        std::thread mythread(ThreadSaveImage, strPngPathList, qImageList);
        mythread.detach();
    }
    else
    {
        ThreadSaveImage(strPngPathList,qImageList);
    }

    qDebug()<<Q_FUNC_INFO<<"测试详情截图耗时:"<<t1.msecsTo(QTime::currentTime())<<t2.msecsTo(QTime::currentTime());
}

QString CHistoryDetailWidget::_GetResultText(const SResultInfoStruct& sResultInfo,const QString& strHoleName)
{
    // 这里返回测试结果
    // 如果是HRM测试
    // 如果是pcr测试
    // 如果是质控测试
    if(sResultInfo.iStatus == ETestStatus::eTestRunning)
    {
        return " ";
    }
    bool bQCResult;
    QString strTestResult  = QObject::tr("测试结果：") + GetTestResultText(sResultInfo.strMode,sResultInfo.strResult,strHoleName,bQCResult);
    return strTestResult;
}

void CHistoryDetailWidget::_InitLeftLabel()
{
    /*
    m_pResultLabel = new QLabel(tr("测试结果："));
    m_pResultLabel->setWordWrap(true);
    */
    m_pDevTitleLabel = new QLabel(tr("检测信息"));
    m_pDevTitleLabel->setObjectName("TitleTextLabel");
    m_pDevItemLabel = new QLabel(tr("检测模块："));
    m_pDevSNLabel = new QLabel(tr("模块SN："));
    m_pVersionLabel = new QLabel(tr("版本："));
    m_pOperatorLabel = new QLabel(tr("操作者："));
    m_pDevItemStateLabel = new QLabel(tr("测试状态："));

    m_pSampleTitleLabel = new QLabel(tr("样本信息"));
    m_pSampleTitleLabel->setObjectName("TitleTextLabel");
    m_pSampleIDLabel = new QLabel(tr("样本编号："));
    m_pSampleTypeLabel = new QLabel(tr("样本类型："));
    m_pSamplingDateLabel = new QLabel(tr("采样日期："));
    m_pNameLabel = new QLabel(tr("姓名："));
    m_pGenderLabel = new QLabel(tr("性别："));
    m_pAgeLabel = new QLabel(tr("年龄："));
    m_pBirthdayLabel = new QLabel(tr("生日："));
    m_pTelLabel = new QLabel(tr("电话："));
    m_pTestTypeLabel = new QLabel(tr("测试类型："));

    m_pCardBoxTitleLabel = new QLabel(tr("试剂卡信息"));
    m_pCardBoxTitleLabel->setObjectName("TitleTextLabel");
    m_pProjectLabel = new QLabel(tr("测试项目："));
    m_pCardIDLabel = new QLabel(tr("试剂卡编号："));
    m_pCardSNLabel = new QLabel(tr("试剂卡SN码： "));
    m_pCardMFGLabel = new QLabel(tr("试剂卡生产日期："));
    m_pCardEXPLabel = new QLabel(tr("试剂卡有效日期："));

    QVBoxLayout *pDevLayout = new QVBoxLayout;
    pDevLayout->setContentsMargins(8, 0, 0, 0);
    pDevLayout->setSpacing(10);
    pDevLayout->addWidget(m_pDevItemLabel);
    pDevLayout->addWidget(m_pDevSNLabel);
    pDevLayout->addWidget(m_pVersionLabel);
    pDevLayout->addWidget(m_pOperatorLabel);
    pDevLayout->addWidget(m_pTestTypeLabel);
    pDevLayout->addWidget(m_pDevItemStateLabel);
    //pDevLayout->addWidget(m_pResultLabel);


    QVBoxLayout *pSampleLayout = new QVBoxLayout;
    pSampleLayout->setContentsMargins(8, 0, 0, 0);
    pSampleLayout->setSpacing(10);
    pSampleLayout->addWidget(m_pSampleIDLabel);
    pSampleLayout->addWidget(m_pSampleTypeLabel);
    pSampleLayout->addWidget(m_pSamplingDateLabel);
    pSampleLayout->addWidget(m_pNameLabel);
    pSampleLayout->addWidget(m_pGenderLabel);
    pSampleLayout->addWidget(m_pAgeLabel);
    //pSampleLayout->addWidget(m_pBirthdayLabel);
    //pSampleLayout->addWidget(m_pTelLabel);


    QVBoxLayout *pCardLayout = new QVBoxLayout;
    pCardLayout->setContentsMargins(8, 0, 0, 0);
    pCardLayout->setSpacing(10);
    pCardLayout->addWidget(m_pProjectLabel);
    pCardLayout->addWidget(m_pCardIDLabel);
    pCardLayout->addWidget(m_pCardSNLabel);
    pCardLayout->addWidget(m_pCardMFGLabel);
    pCardLayout->addWidget(m_pCardEXPLabel);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setContentsMargins(12, 0, 0, 0);
    pLayout->setSpacing(0);
    pLayout->addSpacing(5);
    pLayout->addWidget(m_pDevTitleLabel, 0, Qt::AlignLeft);
    pLayout->addSpacing(12);
    pLayout->addLayout(pDevLayout);
    pLayout->addSpacing(20);
    pLayout->addWidget(m_pSampleTitleLabel, 0, Qt::AlignLeft);
    pLayout->addSpacing(12);
    pLayout->addLayout(pSampleLayout);
    pLayout->addSpacing(20);
    pLayout->addWidget(m_pCardBoxTitleLabel, 0, Qt::AlignLeft);
    pLayout->addSpacing(12);
    pLayout->addLayout(pCardLayout);
    pLayout->addSpacing(5);

    m_pLeftBackgroundLabel = new QLabel;
    m_pLeftBackgroundLabel->setFixedSize(370, 860);
    m_pLeftBackgroundLabel->setObjectName("LeftBackgroundLabel");

    m_pLeftBackgroundLabel->setLayout(pLayout);

    m_pBirthdayLabel->setVisible(false);
    m_pTelLabel->setVisible(false);
}

void CHistoryDetailWidget::_InitWidget()
{
    _InitLeftLabel();
    m_pDetailTitleWidget = new CHLabelTitleWidget(m_strTitle);


    m_AmpCurve = new CHistoryDetailAmpCurve(m_bHistoryMode);
    m_HrmCurve = new CHistoryDetailHrmCurve(m_bHistoryMode);//(bHistoryMode);

    connect(m_AmpCurve, &CHistoryDetailAmpCurve::SignalReviewConfirmBtn, this, &CHistoryDetailWidget::_SlotReviewConfirm);


    m_pStackWidget = new QStackedWidget;
    m_pStackWidget->setFixedSize(1470, 846);
    m_pStackWidget->addWidget(m_AmpCurve);
    m_pStackWidget->addWidget(m_HrmCurve);

    m_pCloseBtn = new QPushButton(tr("关闭"));
    m_pCloseBtn->setFixedSize(150, 50);
    m_pCloseBtn->setObjectName("CancelBtn");
    connect(m_pCloseBtn, &QPushButton::clicked, this, &CHistoryDetailWidget::_SlotCloseBtn);

    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(1890, 1010);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");
}

void CHistoryDetailWidget::_InitLayout()
{
    QVBoxLayout *pRightLayout = new QVBoxLayout;
    pRightLayout->setMargin(0);
    pRightLayout->setSpacing(12);
    pRightLayout->addWidget(m_pStackWidget);
    pRightLayout->addStretch(1);


    QHBoxLayout *pContentLayout = new QHBoxLayout;
    pContentLayout->setMargin(0);
    pContentLayout->setSpacing(0);
    pContentLayout->addWidget(m_pLeftBackgroundLabel, 0, Qt::AlignTop);
    pContentLayout->addSpacing(20);
    pContentLayout->addLayout(pRightLayout);

    QVBoxLayout *pBackLayout = new QVBoxLayout;
    pBackLayout->setContentsMargins(15, 12, 15, 12);
    pBackLayout->addWidget(m_pDetailTitleWidget, 0, Qt::AlignLeft);
    pBackLayout->addSpacing(12);
    pBackLayout->addLayout(pContentLayout);
    pBackLayout->addWidget(m_pCloseBtn, 0, Qt::AlignHCenter);
    m_pBackgroundLabel->setLayout(pBackLayout);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pBackgroundLabel, 0, Qt::AlignCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}
