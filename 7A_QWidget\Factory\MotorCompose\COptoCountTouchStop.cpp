#include "COptoCountTouchStop.h"
#include <QPainter>
#include <QBoxLayout>
#include <QGridLayout>

#include "PublicParams.h"
#include "PublicFunction.h"
#include "CMessageBox.h"

COptoCountTouchStop::COptoCountTouchStop(QWidget *parent) : QWidget(parent), m_iRow(-1)
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setFixedSize(1494, 884);
    this->setAttribute(Qt::WA_TranslucentBackground);
    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroup(), 0, Qt::AlignCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
    LoadQSS(this,":/qss/qss/default.qss");
}

void COptoCountTouchStop::Show(int iRow, const QString &strRawParams)
{
    m_iRow = iRow;
    QStringList strList = strRawParams.split(SPLIT_IN_CMD);
    if(strList.size() >= 6)
    {
        m_pCountType->SetCurrentIndex(strList.at(0).toInt());
        m_pCountParam->SetLineEditText(strList.at(1));
        m_pMoveDir->SetLineEditText(strList.at(2));
        m_pMoveParam->SetCurrentIndex(strList.at(3).toInt());
        m_pNSTEP->SetLineEditText(strList.at(4));
        m_pStopParaType->SetCurrentIndex(strList.at(5).toInt());

        if(strList.size() > 6)
        {
            m_pCompensate->setVisible(true);
            m_pCompensate->SetCurrentIndex(strList.at(6).toInt());
        }
    }
    else
    {
        m_pCountType->SetCurrentIndex(0);
        m_pCountParam->SetLineEditText("0");
        m_pMoveDir->SetLineEditText("0");
        m_pMoveParam->SetCurrentIndex(0);
        m_pNSTEP->SetLineEditText("0");
        m_pStopParaType->SetCurrentIndex(0);
    }

    //this->show();
}

void COptoCountTouchStop::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, gk_iBackTransparency));
    QWidget::paintEvent(pEvent);
}

void COptoCountTouchStop::_SlotConfirmBtn()
{
    QStringList strList;
    strList<<QString::number(m_pCountType->GetCurrentIndex())<<m_pCountParam->GetLineEditText()
          <<m_pMoveDir->GetLineEditText()<<QString::number(m_pMoveParam->GetCurrentIndex())
         <<m_pNSTEP->GetLineEditText()<<QString::number(m_pStopParaType->GetCurrentIndex());

    if(m_pCompensate->isVisible())
        strList<<QString::number(m_pCompensate->GetCurrentIndex());

    emit SignalParamsConfirm(m_iRow, strList.join(SPLIT_IN_CMD));
    this->close();
}

void COptoCountTouchStop::_SlotResetBtn()
{
    m_pCountType->SetCurrentIndex(0);
    m_pCountParam->SetLineEditText("0");
    m_pMoveDir->SetLineEditText("0");
    m_pMoveParam->SetCurrentIndex(0);
    m_pNSTEP->SetLineEditText("0");
    m_pStopParaType->SetCurrentIndex(0);
}

void COptoCountTouchStop::_SlotCancelBtn()
{
    this->close();
}

void COptoCountTouchStop::_SlotStopParaChanged(int index)
{
    if(4 == index || 5 == index)
        m_pCompensate->setVisible(true);
    else
        m_pCompensate->setVisible(false);
}

QGroupBox *COptoCountTouchStop::_CreateGroup()
{
    QStringList strList = {tr("自定义"), tr("可替换")};
    m_pCountType = new CLabelComboBox(tr("计数类型:"), strList);
    m_pCountType->SetComboBoxFixedSize(100, 50);

    m_pCountParam = new CLabelLineEdit(tr("计数参数:"), "0");
    m_pCountParam->SetLineEditFixedSize(100, 50);

    m_pMoveDir = new CLabelLineEdit(tr("运动方向:"), "0");
    m_pMoveDir->SetLineEditFixedSize(100, 50);

    strList<<tr("补偿替换");
    m_pMoveParam = new CLabelComboBox(tr("运动参数:"), strList);
    m_pMoveParam->SetComboBoxFixedSize(100, 50);

    m_pNSTEP = new CLabelLineEdit(tr("N_STEP:"), "0");
    m_pNSTEP->SetLineEditFixedSize(100, 50);

    strList.clear();
    strList<<tr("硬停")<<tr("软停")<<tr("硬停补偿")<<tr("软停补偿")<<tr("硬停偏移补偿")<<tr("软停偏移补偿");
    m_pStopParaType = new CLabelComboBox(tr("停止类型"), strList);
    m_pStopParaType->SetComboBoxFixedSize(100, 50);
    connect(m_pStopParaType, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotStopParaChanged(int)));

    //电机补偿
    m_pCompensate = new CLabelComboBox(tr("补偿索引"));
    m_pCompensate->SetComboBoxFixedSize(100, 50);

    m_pConfirmBtn = new QPushButton(tr("确认"));
    m_pConfirmBtn->setFixedSize(120, 50);
    connect(m_pConfirmBtn, &QPushButton::clicked, this, &COptoCountTouchStop::_SlotConfirmBtn);

    m_pResetBtn = new QPushButton(tr("重置"));
    m_pResetBtn->setFixedSize(120, 50);
    connect(m_pResetBtn, &QPushButton::clicked, this, &COptoCountTouchStop::_SlotResetBtn);

    m_pCancelBtn = new QPushButton(tr("取消"));
    m_pCancelBtn->setFixedSize(120, 50);
    connect(m_pCancelBtn, &QPushButton::clicked, this, &COptoCountTouchStop::_SlotCancelBtn);

    QGridLayout *pGridLayout = new QGridLayout;
    pGridLayout->setMargin(30);
    pGridLayout->setVerticalSpacing(20);
    pGridLayout->setHorizontalSpacing(10);
    pGridLayout->addWidget(m_pCountType, 0, 0);
    pGridLayout->addWidget(m_pCountParam, 0, 1);
    pGridLayout->addWidget(m_pMoveDir, 0, 2);
    pGridLayout->addWidget(m_pMoveParam, 1, 0);
    pGridLayout->addWidget(m_pNSTEP, 1, 1);
    pGridLayout->addWidget(m_pStopParaType, 1, 2);
    pGridLayout->addWidget(m_pCompensate, 2, 0);
    m_pCompensate->setVisible(false);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->addStretch(1);
    pBtnLayout->setSpacing(20);
    pBtnLayout->addWidget(m_pConfirmBtn);
    pBtnLayout->addWidget(m_pResetBtn);
    pBtnLayout->addWidget(m_pCancelBtn);
    pBtnLayout->addStretch(1);

    QGroupBox *pGroupBox = new QGroupBox(this);
    pGroupBox->setWindowOpacity(1);
    pGroupBox->setFixedSize(700, 400);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addStretch(1);
    pLayout->addLayout(pGridLayout);
    pLayout->addStretch(1);
    pLayout->addLayout(pBtnLayout);
    pLayout->addSpacing(40);
    pGroupBox->setLayout(pLayout);

    return pGroupBox;
}
