#include "CLabelLabel.h"
#include <QHBoxLayout>

CLabelLabel::CLabelLabel(const QString &strNameText, const QString &strValueText, int iSpacing, QWidget *parent)
    : QWidget(parent)
    , m_iSpacing(iSpacing)
    , m_strNameText(strNameText)
    , m_strValueText(strValueText)
{
    _InitWidget();
}

CLabelLabel::~CLabelLabel()
{

}

void CLabelLabel::SetNameLabelAlignment(Qt::Alignment qAlig)
{
    m_pNameLabel->setAlignment(qAlig);
}

void CLabelLabel::SetValueLabelAlignment(Qt::Alignment qAlig)
{
    m_pValueLabel->setAlignment(qAlig);
}

void CLabelLabel::SetValueLabelText(const QString &strText)
{
    m_strValueText = strText;
    m_pValueLabel->setText(strText);
}

void CLabelLabel::SetNameLabelMinSize(int iWidth, int iHeight)
{
    m_pNameLabel->setMinimumSize(iWidth, iHeight);
}

void CLabelLabel::SetNameLabelFixedSize(int iWidth, int iHeight)
{
    m_pNameLabel->setFixedSize(iWidth, iHeight);
}

void CLabelLabel::SetValueLabelMinSize(int iWidth, int iHeight)
{
    m_pValueLabel->setMinimumSize(iWidth, iHeight);
}

void CLabelLabel::SetValueLabelFixedSize(int iWidth, int iHeight)
{
    m_pValueLabel->setFixedSize(iWidth, iHeight);
}

void CLabelLabel::SetNameLabelObjectName(const QString &strObjName)
{
    m_pNameLabel->setObjectName(strObjName);
}

void CLabelLabel::SetValueLabelObjectName(const QString &strObjName)
{
    m_pValueLabel->setObjectName(strObjName);
}

QString CLabelLabel::GetValueText() const
{
    return m_pValueLabel->text();
}

void CLabelLabel::_InitWidget()
{
    m_pNameLabel = new QLabel(m_strNameText, this);
    m_pValueLabel = new QLabel(m_strValueText, this);

    QHBoxLayout* pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pNameLabel);
    pLayout->addSpacing(m_iSpacing);
    pLayout->addWidget(m_pValueLabel);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}
