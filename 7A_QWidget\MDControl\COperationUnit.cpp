#include "COperationUnit.h"
#include "PublicParams.h"
#include "CSerialThread.h"
#include "CCanBusThread.h"

COperationUnit *COperationUnit::m_spInstance = nullptr;

COperationUnit *COperationUnit::GetInstance()
{
    if(nullptr == m_spInstance)
        m_spInstance = new COperationUnit;
    return m_spInstance;
}

void COperationUnit::SendJsonText(int iMachineID, int iMethodID, const QString &strText)
{
    SCanBusDataStruct sSCanBusDataStruct;
    sSCanBusDataStruct.quMachineID = iMachineID;
    sSCanBusDataStruct.quCmdID = 0x00;
    sSCanBusDataStruct.quObjectID = _GetDestinationID(iMethodID);
    sSCanBusDataStruct.quFormat = 0x00;
    sSCanBusDataStruct.quMethonID = iMethodID;
    sSCanBusDataStruct.qbPayload = strText.toUtf8();

#ifdef __aarch64__
    CCanBusThread::GetInstance()->WriteCan(sSCanBusDataStruct);
#else
    CSerialThread::GetInstance()->WriteSerial(sSCanBusDataStruct);
#endif
}

void COperationUnit::MedianUpdate(int iMachineID, const QByteArray &qPayload)
{
    SCanBusDataStruct sSCanBusDataStruct;
    sSCanBusDataStruct.quMachineID = iMachineID;
    sSCanBusDataStruct.quCmdID = 0x01;
    sSCanBusDataStruct.quObjectID = 0x00; //中位机
    sSCanBusDataStruct.quFormat = 0x02;
    sSCanBusDataStruct.quMethonID = Method_upgrade_data;
    sSCanBusDataStruct.qbPayload = qPayload;
    sSCanBusDataStruct.quReserve = 0x01;

#ifdef __aarch64__
    CCanBusThread::GetInstance()->WriteCan(sSCanBusDataStruct);
#else
    CSerialThread::GetInstance()->WriteSerial(sSCanBusDataStruct);
#endif
}

void COperationUnit::PCRUpdate(int iMachineID, const QByteArray &qPayload)
{
    SCanBusDataStruct sSCanBusDataStruct;
    sSCanBusDataStruct.quMachineID = iMachineID;
    sSCanBusDataStruct.quCmdID = 0x01;
    sSCanBusDataStruct.quObjectID = 0x02; //PCR
    sSCanBusDataStruct.quFormat = 0x02;
    sSCanBusDataStruct.quMethonID = Method_pcr_upgrade_data;
    sSCanBusDataStruct.qbPayload = qPayload;
    sSCanBusDataStruct.quReserve = 0x01;

#ifdef __aarch64__
    CCanBusThread::GetInstance()->WriteCan(sSCanBusDataStruct);
#else
    CSerialThread::GetInstance()->WriteSerial(sSCanBusDataStruct);
#endif
}

void COperationUnit::FLUpdate(int iMachineID, const QByteArray &qPayload)
{
    SCanBusDataStruct sSCanBusDataStruct;
    sSCanBusDataStruct.quMachineID = iMachineID;
    sSCanBusDataStruct.quCmdID = 0x01;
    sSCanBusDataStruct.quObjectID = 0x03; //FL
    sSCanBusDataStruct.quFormat = 0x02;
    sSCanBusDataStruct.quMethonID = Method_fl_upgrade_data;
    sSCanBusDataStruct.qbPayload = qPayload;
    sSCanBusDataStruct.quReserve = 0x01;

#ifdef __aarch64__
    CCanBusThread::GetInstance()->WriteCan(sSCanBusDataStruct);
#else
    CSerialThread::GetInstance()->WriteSerial(sSCanBusDataStruct);
#endif
}

COperationUnit::COperationUnit()
{

}

COperationUnit::~COperationUnit()
{

}

quint8 COperationUnit::_GetDestinationID(int iMethodID)
{
    quint8 quDestinationID = 0x00;
    if(iMethodID < 0x0100)
    {
        quDestinationID = 0x00;
    }
    else if(iMethodID >= 0x0100 && iMethodID < 0x0200)
    {
        quDestinationID = 0x01;
    }
    else if(iMethodID >= 0x0200 && iMethodID < 0x0300)
    {
        quDestinationID = 0x02;
    }
    else if(iMethodID >= 0x0300 && iMethodID < 0x0400)
    {
        quDestinationID = 0x03;
    }
    else if(iMethodID >= 0x0400 && iMethodID < 0x0500)
    {
        quDestinationID = 0x04;
    }
    else if(iMethodID >= 0x0700 && iMethodID < 0x0800)
    {
        quDestinationID = 0x07;
    }
    return quDestinationID;
}
