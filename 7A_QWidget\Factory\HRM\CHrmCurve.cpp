#include "CHrmCurve.h"
#include <QBoxLayout>

#include "CRunTest.h"
#include "CMessageBox.h"
#include "qcustomplot.h"
#include "PublicFunction.h"
#include "CReadWriteXlsxThread.h"

#define HRM_SHOW_SIZE 100

CHrmCurve::CHrmCurve(QWidget *parent)
    : QWidget(parent)
    , m_bShow(false)
{
    //Register2Map(Method_FLCDT);
    Register2Map(Method_pcr_info);
    Register2Map(Method_start);

    for(int i=0; i<gk_iMachineCount; i++)
    {
        m_sCurveUiList.push_back(new SHrmCurveStruct);
    }

    _InitWidget();

#if 0
    for(int i=0; i<100; i++)
        m_sCurveUiList.at(0)->pCustomPlot->graph(2)->addData(i*10,20);
    m_sCurveUiList.at(0)->pCustomPlot->replot();
#endif

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalTimingTestStart, this, &CHrmCurve::SlotClearData);
}

CHrmCurve::~CHrmCurve()
{
    //UnRegister2Map(Method_FLCDT);
    UnRegister2Map(Method_pcr_info);
    UnRegister2Map(Method_start);

    for(int i=0; i<m_sCurveUiList.size(); i++)
    {
        SHrmCurveStruct *pStruct = m_sCurveUiList.at(i);
        delete pStruct;
        pStruct = nullptr;
    }
    m_sCurveUiList.clear();
}

void CHrmCurve::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    Q_UNUSED(iResult);
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if(Method_pcr_info == iMethodID)
    {
        _HandlePCRInfo(iMachineID, qVarData);
    }
    else if(Method_FLCDT == iMethodID)
    {
        _HandelFLCDTData(iMachineID, qVarData);
    }
    else if(Method_start == iMethodID)
    {
        _TestEnd(iMachineID);
    }
}

void CHrmCurve::SlotClearData(int iMachineID)
{
    qDebug()<<QString("时序开始,清空%1#HRM曲线").arg(iMachineID + 1);
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    SHrmCurveStruct *pStruct = m_sCurveUiList[iMachineID];
    pStruct->dFLVec.clear();
    pStruct->dTimeVec.clear();
    pStruct->dM1TempVec.clear();
    pStruct->dM2TempVec.clear();
    pStruct->dY1Vec.clear();
    pStruct->dY2Vec.clear();

    pStruct->pCPItemText->setText("Module1Temp:\nModule2Temp:");
    QVector<double> x, y;
    pStruct->pCustomPlot->graph(0)->setData(x, y);
    pStruct->pCustomPlot->graph(1)->setData(x, y);
    pStruct->pCustomPlot->graph(2)->setData(x, y);
    pStruct->pCustomPlot->replot();
}

void CHrmCurve::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;
    _UpdateMachinePlot(m_pMachineComboBox->GetCurrentIndex());

    QWidget::showEvent(pEvent);
}

void CHrmCurve::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;
    QWidget::hideEvent(pEvent);
}

void CHrmCurve::_SlotExportBtn()
{
    QString strExportDir = CPublicConfig::GetInstance()->GetUDiskExportDir();
    if(!UDiskExistAndCreateDir(strExportDir, this))
        return;

    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    QString strCurrentTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    QString strXlsxName = QString("HRM_%1#_%2.xlsx").arg(iMachineID + 1).arg(strCurrentTime);

    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = strXlsxName;
    pXlsxStruct->strTableName = "HRM";

    QStringList &strTitleList = pXlsxStruct->strTitleList;
    strTitleList << "x" << "Module1Temp" << "Module2Temp";

    SHrmCurveStruct *pStruct = m_sCurveUiList[iMachineID];

    QVector<double> dXVec;
    for(int i=0; i<pStruct->dM1TempVec.size(); i++)
        dXVec.push_back(i);

    QList<QVariantList> &varDataList = pXlsxStruct->varWriteDataList;
    for(int i=0; i<dXVec.size(); i++)
    {
        QVariantList oneList;
        oneList << dXVec.at(i) << pStruct->dM1TempVec.at(i) << pStruct->dM2TempVec.at(i);
        varDataList<<oneList;
    }

    int iSize = pStruct->dTimeVec.size();
    ChartNoteStruct hrmChart;
    hrmChart.iRow = 5;
    hrmChart.iColumn = 6;
    hrmChart.strChartTitle = "HRM";
    hrmChart.strXTitle = "x";
    hrmChart.strYTitle = "温度 (℃)";
    hrmChart.strSerialNameList<<"Module1Temp"<<"Module2Temp";
    hrmChart.strXDataRange = QString("%1!$A$2:$A$%2").arg(pXlsxStruct->strTableName).arg(iSize+1);
    hrmChart.strNumDataRange = QString("B2:E%1").arg(iSize+1);
    hrmChart.bMajorGridlines = false;
    hrmChart.strMarkSymbolList<<"none"<<"none"<<"none"<<"none";
    hrmChart.strSerialColorList<<HEX_COLOR_B<<HEX_COLOR_R<<HEX_COLOR_G<<HEX_COLOR_Y;

    // pXlsxStruct->chartNoteList<<hrmChart;

    FunWriteXlsxEndCallBack lambdaFunction = [this](QString strXlsxName, QString strTableName)
    {
        Q_UNUSED(strTableName);
        qDebug()<<strXlsxName<<"文件导出完成";
        QString strDestPath = CPublicConfig::GetInstance()->GetUDiskExportDir();
        CopyQFileDir(strXlsxName, QDir(strDestPath));
        ExportEndUmountUSB();
        ShowInformation(this, m_strTipsText, tr("文件导出完成"));
    };

    pXlsxStruct->WriteEndCallBack = lambdaFunction;

    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
}

void CHrmCurve::_SlotClearBtn()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    SHrmCurveStruct *pStruct = m_sCurveUiList[iMachineID];
    pStruct->dFLVec.clear();
    pStruct->dTimeVec.clear();
    pStruct->dM1TempVec.clear();
    pStruct->dM2TempVec.clear();
    pStruct->dY1Vec.clear();
    pStruct->dY2Vec.clear();

    QVector<double> x, y;
    pStruct->pCustomPlot->graph(0)->setData(x, y);
    pStruct->pCustomPlot->graph(1)->setData(x, y);
    pStruct->pCustomPlot->graph(2)->setData(x, y);
    pStruct->pCustomPlot->replot();
}

void CHrmCurve::_SlotSetXYRange(const QStringList &strRangeList)
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    SHrmCurveStruct *pStruct = m_sCurveUiList[iMachineID];
    pStruct->strRangeList = strRangeList;
    pStruct->pCustomPlot->xAxis->setRange(strRangeList.at(0).toDouble(), strRangeList.at(1).toDouble());
    pStruct->pCustomPlot->yAxis->setRange(strRangeList.at(2).toDouble(), strRangeList.at(3).toDouble());

    if(false == CRunTest::GetInstance()->GetRunInfoStruct(iMachineID).bRunning)
    {                
        SHrmCurveStruct *pStruct = m_sCurveUiList.at(iMachineID);
        QVector<double> dXVec;
        for(int i=0; i<pStruct->dM1TempVec.size(); i++)
            dXVec.push_back(i);

        pStruct->pCustomPlot->graph(0)->setData(dXVec, pStruct->dM1TempVec);
        pStruct->pCustomPlot->graph(1)->setData(dXVec, pStruct->dM2TempVec);
    }
    pStruct->pCustomPlot->replot();
}

void CHrmCurve::_SlotFLCheckBox(bool bChecked)
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    SHrmCurveStruct *pStruct = m_sCurveUiList[iMachineID];
    pStruct->bFLChecked = bChecked;
    pStruct->pCustomPlot->graph(2)->setVisible(bChecked);
    pStruct->pCustomPlot->legend->item(2)->setVisible(bChecked);
    pStruct->pCustomPlot->replot();
}

void CHrmCurve::_SlotMachineChange(int iMachineID)
{
    m_pStackWidget->setCurrentIndex(iMachineID);
    m_pFLCheckBox->setChecked(m_sCurveUiList[iMachineID]->bFLChecked);
    m_pCSetChartXYRange->SetRange(m_sCurveUiList[iMachineID]->strRangeList);

    _UpdateMachinePlot(iMachineID);
}

void CHrmCurve::_HandlePCRInfo(int iMachineID, const QVariant &qVarData)
{
    QVariantMap qVarMap = qVarData.toMap();

    SHrmCurveStruct *pStruct = m_sCurveUiList[iMachineID];
    if(pStruct->dTimeVec.isEmpty())
    {
        pStruct->qBeginTime = QDateTime::currentDateTime();
        pStruct->dTimeVec.push_back(0);
        pStruct->dFLVec.clear();
        pStruct->dM1TempVec.clear();
        pStruct->dM2TempVec.clear();
        pStruct->dY1Vec.clear();
        pStruct->dY2Vec.clear();
    }
    else
    {
        int iTime = pStruct->qBeginTime.msecsTo(QDateTime::currentDateTime());
        pStruct->dTimeVec.push_back(iTime);

        int size = pStruct->dFLVec.size();
        if(size > 0)
        {
            int iLastTime = pStruct->dTimeVec.last();
            double inter = (iTime - iLastTime) / size;
            QVector<double> dTimeList;
            for(int i=0; i<size; i++)
                dTimeList.push_back(iLastTime + i * inter);
            pStruct->pCustomPlot->graph(2)->addData(dTimeList, pStruct->dFLVec);
            pStruct->dFLVec.clear();
        }
    }

    double dM1 = qVarMap.value("Module1Temp").toDouble() / 100.0;
    double dM2 = qVarMap.value("Module2Temp").toDouble() / 100.0;

    double dMax = qMax(dM1, dM2);
    if(dMax >= pStruct->dMaxValue)
    {
        pStruct->dMaxValue = dMax * 1.2;
        pStruct->pCustomPlot->yAxis->setRange(0, pStruct->dMaxValue);
    }

    QString strInfo = QString("Module1Temp:%1\nModule2Temp:%2").arg(dM1).arg(dM2);
    pStruct->pCPItemText->setText(strInfo);

    if(pStruct->dY1Vec.size() > HRM_SHOW_SIZE)
        pStruct->dY1Vec.pop_front();
    if(pStruct->dY2Vec.size() > HRM_SHOW_SIZE)
        pStruct->dY2Vec.pop_front();

    pStruct->dM1TempVec.push_back(dM1);
    pStruct->dM2TempVec.push_back(dM2);
    pStruct->dY1Vec.push_back(dM1);
    pStruct->dY2Vec.push_back(dM2);

    pStruct->bReplot = true;
    if(m_bShow)
    {
        _UpdateMachinePlot(iMachineID);
    }
}

void CHrmCurve::_UpdateMachinePlot(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    SHrmCurveStruct *pStruct = m_sCurveUiList.at(iMachineID);
    if(!pStruct->bReplot)
        return;

    int iDataSize = pStruct->dY1Vec.size();
    QVector<double> dXVec;
    for(int i=0; i<iDataSize; i++)
        dXVec.push_back(i);

    pStruct->pCustomPlot->graph(0)->setData(dXVec, pStruct->dY1Vec);
    pStruct->pCustomPlot->graph(1)->setData(dXVec, pStruct->dY2Vec);
    pStruct->pCustomPlot->replot();

    pStruct->bReplot = false;
}

void CHrmCurve::_HandelFLCDTData(int iMachineID, const QVariant &qVarData)
{
    QVariantList qVarList = qVarData.toList();
    if(qVarList.size() < 5)
        return;

    for(int i=1; i<qVarList.size(); i++)
        m_sCurveUiList[iMachineID]->dFLVec.push_back(qVarList.at(i).toDouble());
}

void CHrmCurve::_TestEnd(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    SHrmCurveStruct *pDataStruct = m_sCurveUiList.at(iMachineID);

    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = CPublicConfig::GetInstance()->GetTestXlsxName(iMachineID);;
    pXlsxStruct->strTableName = "hrm";
    pXlsxStruct->bDrawChart = true;
    pXlsxStruct->strTitleList << "x" << "Module1Temp" << "Module2Temp";

    QVector<double> dXVec;
    for(int i=0; i<pDataStruct->dM1TempVec.size(); i++)
        dXVec.push_back(i);

    int size = dXVec.size();
    for(int i=0; i<size; i++)
    {
        QVariantList qRowList;
        qRowList << dXVec.at(i);
        qRowList << pDataStruct->dM1TempVec.at(i);
        qRowList << pDataStruct->dM2TempVec.at(i);

        pXlsxStruct->varWriteDataList << qRowList;
    }

    ChartNoteStruct chart;
    chart.iRow = 4;
    chart.iColumn = 5;
    chart.strChartTitle = "hrm data";
    chart.strXTitle = "time (ms)";
    chart.strYTitle = "temp (℃)";
    chart.strSerialNameList << "Module1Temp" << "Module2Temp";
    chart.strSerialColorList<<HEX_COLOR_B<<HEX_COLOR_G;
    chart.strXDataRange = QString("%1!$A$2:$A$%2").arg(pXlsxStruct->strTableName).arg(size + 1);
    chart.strNumDataRange = QString("B2:C%1").arg(size + 1);
    chart.bMajorGridlines = false;
    chart.strMarkSymbolList << "none" << "none";

    pXlsxStruct->chartNoteList << chart;
    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);

    qDebug()<<QString("%1# hrm data write to result xlsx end").arg(iMachineID + 1);
    return;

    QString strX = DoubleVector2StringList(dXVec).join(",");
    QString strY1 = DoubleVector2StringList(pDataStruct->dM1TempVec).join(",");
    QString strY2 = DoubleVector2StringList(pDataStruct->dM2TempVec).join(",");
    QJsonObject qHrmObj;
    qHrmObj.insert("x", strX);
    qHrmObj.insert("y1", strY1);
    qHrmObj.insert("y2", strY2);
    CRunTest::GetInstance()->AddJsonObj(iMachineID, "HRM", qHrmObj);
}

void CHrmCurve::_InitWidget()
{
    m_pStackWidget = new QStackedWidget;
    for(int i=0; i<gk_iMachineCount; i++)
    {
        QCustomPlot *pCustomplot = _InitCustomPlot();
        pCustomplot->legend->item(2)->setVisible(false);
        QCPItemText *pCPItemText = InitCPItemText(pCustomplot);
        m_sCurveUiList.at(i)->pCustomPlot = pCustomplot;
        m_sCurveUiList.at(i)->pCPItemText = pCPItemText;
        m_pStackWidget->addWidget((QWidget*)pCustomplot);
    }

    int iHeight = 50;
    int iWidth = 100;
    if(eLanguage_German == gk_iLanguage)
        iWidth = 140;
    m_pExportBtn = new QPushButton(tr("导出"));
    m_pExportBtn->setFixedSize(iWidth, iHeight);
    connect(m_pExportBtn, &QPushButton::clicked, this, &CHrmCurve::_SlotExportBtn);

    m_pClearBtn = new QPushButton(tr("清空"));
    m_pClearBtn->setFixedSize(iWidth, iHeight);
    connect(m_pClearBtn, &QPushButton::clicked, this, &CHrmCurve::_SlotClearBtn);

    m_pCSetChartXYRange = new CSetChartXYRange(m_sCurveUiList.at(0)->strRangeList);
    m_pCSetChartXYRange->SetLineEditTextAlignment();
    connect(m_pCSetChartXYRange, &CSetChartXYRange::SignalSetRange, this, &CHrmCurve::_SlotSetXYRange);

    m_pFLCheckBox = new QCheckBox("FL");
    m_pFLCheckBox->setFixedHeight(iHeight);
    m_pFLCheckBox->setLayoutDirection(Qt::RightToLeft);
    connect(m_pFLCheckBox, &QCheckBox::clicked, this, &CHrmCurve::_SlotFLCheckBox);
    m_pFLCheckBox->setVisible(false);

    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(70, iHeight);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMachineChange(int)));

    QHBoxLayout *pHLayout = new QHBoxLayout;
    pHLayout->setMargin(0);
    pHLayout->setSpacing(10);
    pHLayout->addWidget(m_pExportBtn);
    pHLayout->addWidget(m_pClearBtn);
    pHLayout->addSpacing(10);
    pHLayout->addWidget(m_pCSetChartXYRange);
    pHLayout->addWidget(m_pFLCheckBox);
    pHLayout->addStretch(1);
    pHLayout->addWidget(m_pMachineComboBox);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addWidget(m_pStackWidget);
    pLayout->addSpacing(10);
    pLayout->addLayout(pHLayout);
    this->setLayout(pLayout);
}

QCustomPlot *CHrmCurve::_InitCustomPlot()
{
    QCustomPlot *pCustomPlot = new QCustomPlot;

    QFont font;
    font.setPointSize(12);
    pCustomPlot->legend->setFont(font);
    pCustomPlot->legend->setSelectedFont(font);
    pCustomPlot->legend->setVisible(true);
    pCustomPlot->legend->setSelectableParts(QCPLegend::spItems);
    pCustomPlot->legend->setBorderPen(Qt::NoPen);
    pCustomPlot->legend->setWrap(1);
    pCustomPlot->axisRect()->insetLayout()->setInsetAlignment(0, Qt::AlignTop);

    _AddGraph(pCustomPlot, Qt::blue,  Qt::blue,  0, "Module1Temp");
    _AddGraph(pCustomPlot, Qt::red,   Qt::red,   1, "Module2Temp");
    _AddGraph(pCustomPlot, Qt::black, Qt::black, 2, "FL");

    pCustomPlot->xAxis->setLabel(tr("时间 (ms)"));
    pCustomPlot->xAxis->setLabel("x");
    pCustomPlot->yAxis->setLabel(tr("温度 (℃)"));
    pCustomPlot->xAxis->setLabelFont(font);
    pCustomPlot->yAxis->setLabelFont(font);

    pCustomPlot->xAxis->setRange(0, 100);
    pCustomPlot->yAxis->setRange(0, 110);

    return pCustomPlot;
}

QCPItemText *CHrmCurve::InitCPItemText(QCustomPlot *pCustomPlot)
{
    QCPItemText *pCPItemText = new QCPItemText(pCustomPlot);
    pCPItemText->setTextAlignment(Qt::AlignCenter);

    pCPItemText->setFont(QFont(font().family(), 12));
    pCPItemText->setPen(QPen(Qt::black));
    pCPItemText->setBrush(QBrush(QColor("#a1ffa1")));
    pCPItemText->setPadding(QMargins(5, 5, 5, 5));

    QString strText = "Module1Temp:\nModule2Temp:";
    pCPItemText->setText(strText);
    pCPItemText->setVisible(true);
    pCPItemText->position->setType(QCPItemPosition::ptAxisRectRatio);
    pCPItemText->position->setCoords(0.8, 0.05);

    return pCPItemText;
}

void CHrmCurve::_AddGraph(QCustomPlot *pCustomPlot, QColor penColor, QColor pointColor, int iChart, QString strChartName)
{
    QPen pen;
    pen.setWidth(2);
    pen.setColor(penColor);
    pCustomPlot->addGraph();
    pCustomPlot->graph(iChart)->setPen(pen);
    pCustomPlot->graph(iChart)->setName(strChartName);
    pCustomPlot->graph(iChart)->setAntialiasedFill(true);
    pCustomPlot->graph(iChart)->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssNone,
                                                                QPen(pointColor, 1),
                                                                QBrush(pointColor), 1));
}
