﻿#ifndef CFTPDB_H
#define CFTPDB_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-12-31
  * Description: ftp upload file
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QObject>
#include "CSqliteDBBase.h"

class CFtpDB : public QObject , public CSqliteDBBase
{
    Q_OBJECT
public:
    static CFtpDB *GetInstance();
    virtual ~CFtpDB();

public:
    void AddFtpUploadFile(QString strFileName);
    void DeleteFtpUploadFile(QString strFileName);
    void ReadFtpUploadFile(QStringList &strFileList);

private:
    CFtpDB();

private:
    static CFtpDB *m_spInstance;

    Q_DISABLE_COPY(CFtpDB)
};

#endif // CFTPDB_H
