#ifndef CDATETIMEWIDGET_H
#define CDATETIMEWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-05-29
  * Description: 新的日期时间控件
  * -------------------------------------------------------------------------
  * History: 2024-06-28 增加接口只显示日期 年-月-日
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QGroupBox>
#include <QPushButton>
#include <QListWidget>
#include <QDateTime>

class CTimeWidget : public QWidget
{
    Q_OBJECT
public:
    CTimeWidget(const QString &strTitle, const QList<int> &iDataList, QWidget *parent = nullptr);

public:
    void SetDataList(const QList<int> &iDataList);
    void SetCurrentData(int iData);
    void SetObjName(const QString &strLabelName, const QString &strListWidgetName);
    int GetCurrentData() const;

signals:
    void SignalDataChanged(int iData);

private slots:
    void _SlotListItemClicked(QListWidgetItem *pItem);

private:
    QLabel *m_pTitleLabel;
    QListWidget *m_pListWidget;

    QList<int> m_iDataList;
};

class CDateTimeWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CDateTimeWidget(QWidget *parent = nullptr);
    CDateTimeWidget(const QString &strDateTime, QWidget *parent = nullptr);
    CDateTimeWidget(const QString &strDate, const QString &strTime, QWidget *parent = nullptr);
    CDateTimeWidget(const QDate &qDate, const QTime &qTime, QWidget *parent = nullptr);
    CDateTimeWidget(const QDateTime &qDateTime, QWidget *parent = nullptr);

public:
    void SetDateTime(const QString &strDateTime);
    void SetDateTime(const QDateTime &qDateTime);
    void GetDateTime(QString &strDateTime) const;
    void GetDateTime(QDateTime &qDateTime) const;

    void SetOnlyDateModel();

protected:
    virtual bool eventFilter(QObject *pWatched, QEvent *pEvent);
    void paintEvent(QPaintEvent *pEvent) override;

signals:
    void SignalDateTime(const QString &strDateTime);

private slots:
    void _SlotYearChanged(int iYear);
    void _SlotMonthChanged(int iMonth);
    void _SlotUpdateDateText();
    void _SlotUpdateTimeText();
    void _SlotComfirmBtn();
    void _SlotCancleBtn();
    void _SlotClearBtn();

private:
    void _UpdateDayWidget(int iMinDay, int iMaxDay);
    void _ShowDateOrTime(bool bDate);
    QGroupBox *_CreateGroupBox();

private:
    QGroupBox *m_pGroupBox;

    QLabel *m_pTopLabel;
    QLabel *m_pDateLabel, *m_pTimeLabel;
    CTimeWidget *m_pYearWidget, *m_pMonthWidget, *m_pDayWidget;
    CTimeWidget *m_pHourWidget, *m_pMinuteWidget, *m_pSecondWidget;
    QLabel *m_pTipLabel;
    QPushButton *m_pConfirmBtn, *m_pCancleBtn, *m_pClearBtn;

    bool m_bOnlyDateModel;
    QDateTime m_qDateTime;
    QList<int> m_iBigMonthList, m_iSmallMonthList;
};

#endif // CDATETIMEWIDGET_H
