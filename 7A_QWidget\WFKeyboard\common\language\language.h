#ifndef LANGUAGE_H
#define LANGUAGE_H
#include <QObject>
#include <QString>

namespace WfKeyboard
{

enum LANGUAGE_TYPE{
    chinese = 0,
    english,
    russia,
    greek,
    german,
    arabic,
    french,
    italian,
    spanish,
    polski,
    romania,
    croatia,
    bulgaria,
    turkey,
    portugal,
    finnish,
    ukrainian,
    kazakh,
    language_count
};


class LanguageFunction:public QObject
{
    Q_OBJECT

public:
    static LanguageFunction* GetInstance();

    void SetCurrentLanguage(QString tmpStr);

    LANGUAGE_TYPE GetCurrentLanguageType();

    QStringList  GetSupportLanguages();

private:
    LanguageFunction();

private:
    static LanguageFunction *m_Instance;

    class PrivateData;
    PrivateData *const md;
};
}
#endif // LANGUAGE_H
