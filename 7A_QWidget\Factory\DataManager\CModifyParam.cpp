#include "CModifyParam.h"
#include "CLotInfoDB.h"


#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include "CMessageBox.h"

CModifyParam::CModifyParam(QWidget* parent)
    : QWidget(parent)
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setFixedSize(G_QRootSize);
    this->setAttribute(Qt::WA_TranslucentBackground);
    setWindowTitle(tr("参数修改"));
    initUI();
    initData();
    this->move(G_QRootPoint);
    LoadQSS(this, ":/qss/qss/factory/flsetparamwidget.qss");
}
void CModifyParam::SetProjectName(const QString& strProjectName)
{
    m_strProjectName = strProjectName;
    m_pTitleLabel->setText(tr("当前项目：%1").arg(m_strProjectName));
    CLotInfoDB::GetInstance()->GetLotInfoByShowName(m_strProjectName, m_sLotInfo);
    qDebug()<<Q_FUNC_INFO<<"更新参数："<<m_strProjectName;
}
void CModifyParam::initUI()
{

    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(680, 580);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");

    // 项目名称显示
    m_pTitleLabel = new QLabel(tr("当前项目：%1").arg(m_strProjectName));

    // 数值输入
    m_pValueEdit = new QLineEdit;
    m_pValueEdit->setPlaceholderText(tr("请输入新数值"));
    m_pValueEdit->setFixedSize(380,38);

    // 字段选择
    m_pFieldCombo = new QComboBox;
    //m_pFieldCombo->setPlaceholderText(tr("请选择要修改的字段"));
    m_pFieldCombo->setFixedSize(380,38);

    connect(m_pFieldCombo, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotHoleComboBoxChanged(int)));


    // 按钮组
    m_pConfirmBtn = new QPushButton(tr("确认"));
    m_pConfirmBtn->setFixedSize(120, 38);
    connect(m_pConfirmBtn, &QPushButton::clicked, this, &CModifyParam::_SlotOnConfirm);

    m_pCancelBtn = new QPushButton(tr("取消"));
    m_pCancelBtn->setFixedSize(120, 38);
    m_pCancelBtn->setObjectName("CancelBtn");
    connect(m_pCancelBtn, &QPushButton::clicked, this, &CModifyParam::_SlotCancelBtn);



    QHBoxLayout* pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(60);

    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pConfirmBtn);
    pBtnLayout->addWidget(m_pCancelBtn);
    pBtnLayout->addStretch(1);

    // 主布局
    QVBoxLayout* pBackLayout = new QVBoxLayout(this);
    pBackLayout->setContentsMargins(24, 12, 24, 24);
    pBackLayout->addSpacing(10);

    pBackLayout->addWidget(m_pTitleLabel, 0, Qt::AlignLeft);
    pBackLayout->addSpacing(20);

    QFormLayout* pFormLayout = new QFormLayout;
    pFormLayout->addRow(tr("修改字段:"), m_pFieldCombo);
    pFormLayout->addRow(tr("新数值:"), m_pValueEdit);

    pBackLayout->addLayout(pFormLayout);
    pBackLayout->addSpacing(30);
    pBackLayout->addLayout(pBtnLayout);

    m_pBackgroundLabel->setLayout(pBackLayout);
    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pBackgroundLabel, 0, Qt::AlignCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);

}

void CModifyParam::initData()
{
    // 初始化字段映射
    m_fieldMap.insert(tr("抬升阈值比例"), "upliftThreshould");
    m_fieldMap.insert(tr("一阶导数阈值"), "FirstDevThreshould");
    m_fieldMap.insert(tr("荧光串扰系数"), "FlInterfereKPCR");
    m_fieldMap.insert(tr("质控Ct范围"), "PQcCtCutoff");
    m_fieldMap.insert(tr("规范化参数"), "strStandardizationPCR");

    m_fieldMap.insert(tr("Rm阈值参数"), "RmThreshold");
    m_fieldMap.insert(tr("Tm范围参数"), "TmRange");
    m_fieldMap.insert(tr("Tm参考值"), "wildTypeTm");
    m_fieldMap.insert(tr("Tm参考值区间"), "TempRangeValue");

    m_pFieldCombo->addItems(m_fieldMap.keys());
}

void CModifyParam::_SlotCancelBtn()
{
    this->close();
}

void CModifyParam::_SlotHoleComboBoxChanged(int index)
{
    Q_UNUSED(index);

    QString selectedField = m_pFieldCombo->currentText(); // 获取当前选择的字段名称
    QString actualField = m_fieldMap.value(selectedField); // 根据字段名称获取实际列名
    if(actualField == "FirstDevThreshould"){
        m_pValueEdit->setText(m_sLotInfo.strFirstDevThreshouldValue);
    }
    else if(actualField == "upliftThreshould"){
        m_pValueEdit->setText(m_sLotInfo.strUpliftThresholdValue);
    }
    else if(actualField == "FlInterfereKPCR"){
        m_pValueEdit->setText(m_sLotInfo.FlInterfereKPCR);
    }
    else if(actualField == "PQcCtCutoff"){
        m_pValueEdit->setText(m_sLotInfo.strPQCCutoffValue);
    }
    else if(actualField == "strStandardizationPCR"){
        m_pValueEdit->setText(m_sLotInfo.strStandardizationPCR);
    }
    else if(actualField == "RmThreshold"){
        m_pValueEdit->setText(m_sLotInfo.strRmThreshold);
    }
    else if(actualField == "TmRange"){
        m_pValueEdit->setText(m_sLotInfo.strTmRange);
    }
    else if(actualField == "wildTypeTm"){
        m_pValueEdit->setText(m_sLotInfo.strWildTypeTmValue);
    }
    else if(actualField == "TempRangeValue"){
        m_pValueEdit->setText(m_sLotInfo.strTempRangeValue);
    }
    else{
        return;
    }

}

void CModifyParam::_SlotOnConfirm()
{
    this->close();
    QString strDebug = QString("%1 : %2 update %3").arg(m_strProjectName).arg(m_pFieldCombo->currentText()).arg(m_pValueEdit->text());
    if(m_pValueEdit->text().isEmpty()){
        qDebug()<<Q_FUNC_INFO<<strDebug<<"参数更新失败";
    }

    //QString strField = m_fieldMap.value(m_pFieldCombo->currentText());

    QString strField = m_fieldMap.value(m_pFieldCombo->currentText());
    QString strValue = m_pValueEdit->text();

    SLotInfoStruct sLotInfo;
    CLotInfoDB::GetInstance()->GetLotInfoByShowName(m_strProjectName, sLotInfo);

    if(strField == "FirstDevThreshould"){
        sLotInfo.strFirstDevThreshouldValue = strValue;
    }
    else if(strField == "upliftThreshould"){
        sLotInfo.strUpliftThresholdValue = strValue;
    }
    else if(strField == "FlInterfereKPCR"){
        sLotInfo.FlInterfereKPCR = strValue;
    }
    else if(strField == "PQcCtCutoff"){
        sLotInfo.strPQCCutoffValue = strValue;
    }
    else if(strField == "strStandardizationPCR"){
        sLotInfo.strStandardizationPCR = strValue;
    }
    else if(strField == "RmThreshold"){
        sLotInfo.strRmThreshold = strValue;
    }
    else if(strField == "TmRange"){
        sLotInfo.strTmRange = strValue;
    }
    else if(strField == "wildTypeTm"){
        sLotInfo.strWildTypeTmValue = strValue;
    }
    else if(strField == "TempRangeValue"){
        sLotInfo.strTempRangeValue = strValue;
    }
    else{
        return;
    }
    if(CLotInfoDB::GetInstance()->SetLotInfoByShowName(m_strProjectName,sLotInfo))
    {
        qDebug()<<Q_FUNC_INFO<<strDebug<<"参数更新成功";
        m_sLotInfo = sLotInfo;
        return;
    }
    qDebug()<<Q_FUNC_INFO<<strDebug<<"参数更新失败";
}
