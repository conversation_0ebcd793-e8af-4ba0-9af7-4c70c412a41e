#ifndef CREADWRITEXLSXTHREAD_H
#define CREADWRITEXLSXTHREAD_H

/*******************************************************************
* @projectName: MDx-5D
* @brief:       XXX
* @author:      hxirong
* @date:        2021-12-23
* @note:        qtxlsx不支持多线程,任何涉及到xlsx的读写必须在此线程
*  20220210:替换xlsx库,增加额外功能
*******************************************************************/

#include <QSize>
#include <QTimer>
#include <QMutex>
#include <QThread>
#include <QObject>
#include <QVariant>

//#include "xlsx/xlsxdocument.h"
//#include "xlsx/xlsxcellrange.h"
//#include "xlsx/xlsxformat.h"
//#include "xlsx/xlsxchart.h"
//#include "xlsx/xlsxworkbook.h"

#include "QXlsx/header/xlsxdocument.h"
#include "QXlsx/header/xlsxcellrange.h"
#include "QXlsx/header/xlsxchart.h"
#include "QXlsx/header/xlsxworkbook.h"

//#define HEX_COLOR_B "0000FF"
//#define HEX_COLOR_R "FF0000"
//#define HEX_COLOR_G "00FF00"
//#define HEX_COLOR_Y "FFFF00"

#define HEX_COLOR_B "2826FB"
#define HEX_COLOR_R "FE0404"
#define HEX_COLOR_G "9BBB59"
#define HEX_COLOR_Y "FF9900"

typedef QMap<QString,QList<QVariantList>> ReadXlsxDataMap;

typedef std::function<void(QString strXlsxName,QString strTableName)> FunWriteXlsxEndCallBack;
typedef std::function<void(QString strXlsxName,const ReadXlsxDataMap& dataMap)> FunReadXlsxEndCallBack;

enum OPXlsxType
{
    eWriteXlsx=0,
    eReadXlsx=1,
};

QXLSX_USE_NAMESPACE

//表格元素
typedef struct _XlsxDataStruct
{
    _XlsxDataStruct()
    {
        iRow = iColumn = 0;
        format.setFontSize(12);
        format.setHorizontalAlignment(Format::AlignHCenter);
    }

    int iRow = 0;      //行
    int iColumn = 0;   //列
    QVariant varValue; //内容
    Format format;     //格式
}XlsxDataStruct;

//设置列宽 unused
typedef struct _MdyWidthStruct
{
    _MdyWidthStruct()
    {
        iColumn = 1;
        dWidth = 8.0;
    }

    int iColumn;    //列
    double dWidth;  //宽度
}MdyWidthStruct;

//设置行高 unused
typedef struct _MdyHeightStruct
{
    _MdyHeightStruct()
    {
        iRow = 1;
        dHeight = 15.0;
    }

    int iRow;       //行
    double dHeight; //高度
}MdyHeightStruct;

//图参数
typedef struct _ChartNoteStruct
{
    _ChartNoteStruct()
    {
        bMajorGridlines = true;
        bMinorGridlines = false;
        iRow = iColumn = 3;
        legendPos = Chart::ChartAxisPos::Bottom;
        eChartType = Chart::CT_LineChart;
        qSize.setWidth(800);
        qSize.setHeight(500);
        iMarkSize = 6;
    }

    bool bMajorGridlines;           //主网格线
    bool bMinorGridlines;           //次网格线
    int iRow;                       //图起始行
    int iColumn;                    //图起始列
    int iMarkSize;                  //mark大小
    Chart::ChartAxisPos legendPos;  //图例位置
    Chart::ChartType eChartType;    //图类型
    QSize qSize;                    //图大小
    QString strChartTitle;          //图标题
    QString strXTitle;              //X轴名称
    QString strYTitle;              //Y轴名称
    QString strXDataRange;          //图的横坐标取值范围
    QString strNumDataRange;        //图的纵坐标数据范围
    QStringList strSerialNameList;  //曲线名称
    QStringList strSerialColorList; //曲线颜色
    QStringList strMarkSymbolList;  //mark样式
}ChartNoteStruct;

//读写xlsx参数结构体
typedef struct _STXlsxParmasStruct
{
    _STXlsxParmasStruct()
    {
        bDrawChart = false;
        bAutoAdjustCol = false;
        eChartType = QXlsx::Chart::CT_LineChart;

        eOpXlsxType = eWriteXlsx;
        strXlsxName.clear();
        strTableName.clear();
        strTitleList.clear();
        varWriteDataList.clear();
        varReadDataMap.clear();

        WriteEndCallBack = NULL;
        ReadEndCallBack = NULL;
    }
    bool bAutoAdjustCol;
    bool bDrawChart;                    //是否画图
    QXlsx::Chart::ChartType eChartType; //图类型

    OPXlsxType eOpXlsxType;
    QString strXlsxName;
    QString strTableName;
    QStringList strTitleList;
    QList<QVariantList> varWriteDataList;
    QList<QString> mergeRangeList;            //合并单元格
    QList<MdyHeightStruct> mdyHeightList;     //调整行高
    QList<MdyWidthStruct>  mdyWidthList;      //调整列宽
    QList<XlsxDataStruct> dataStructList;     //元素内容
    QList<ChartNoteStruct> chartNoteList;     //图List
    ReadXlsxDataMap varReadDataMap;           //读到的内容
    FunWriteXlsxEndCallBack WriteEndCallBack; //写xlsx结束回调
    FunReadXlsxEndCallBack ReadEndCallBack;   //读xlsx结束回调
}STXlsxParmasStruct;

class CReadWriteXlsxThread:public QObject
{
    Q_OBJECT
public:
    static CReadWriteXlsxThread* GetInstance();
    static void FreeInstance();

    void AddXlsxParamsStruct(STXlsxParmasStruct* pXlsxStruct);

signals:
    void SignalReadWriteEnd(STXlsxParmasStruct* pXlsxStruct);

private slots:   
    void _SlotReadWriteEnd(STXlsxParmasStruct* pXlsxStruct);

private:
    CReadWriteXlsxThread();
    ~CReadWriteXlsxThread();

    void _WriteXlsx(STXlsxParmasStruct* pXlsxStruct);
    void _ReadXlsx(STXlsxParmasStruct* pXlsxStruct);
    static void* _CreateThread2WriteXlsx(void* arg);
    bool _WriteData(STXlsxParmasStruct* pXlsxStruct);
    void _GetDataAndXRange(STXlsxParmasStruct* pXlsxStruct,QString& strDataRange,QString& strXRange);

private:
    static CReadWriteXlsxThread* m_spInstance;

    QMutex m_mutex;
    bool m_bRunning;
    QList<STXlsxParmasStruct*> m_stXlsxParamsList;
};

#endif // CREADWRITEXLSXTHREAD_H
