#include "CThreeParams.h"
#include <QPainter>
#include <QBoxLayout>

#include "PublicParams.h"
#include "PublicFunction.h"
#include "CMessageBox.h"

CThreeParams::CThreeParams(QWidget *parent) : QWidget(parent), m_iRow(-1)
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setFixedSize(1494, 884);
    this->setAttribute(Qt::WA_TranslucentBackground);
    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroup(), 0, Qt::AlignCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
    LoadQSS(this,":/qss/qss/default.qss");
}

CThreeParams::~CThreeParams()
{

}

void CThreeParams::Show(int iRow, const QString &strRawParams)
{
    m_iRow = iRow;
    QStringList strList = strRawParams.split(",");
    if(3 == strList.size())
    {
        m_pTimeLineEidt->SetLineEditText(strList.at(0));
        m_pInLineEdit->SetLineEditText(strList.at(1));
        m_pDeLineEdit->SetLineEditText(strList.at(2));
    }
    else
    {
        m_pTimeLineEidt->SetLineEditText("");
        m_pInLineEdit->SetLineEditText("");
        m_pDeLineEdit->SetLineEditText("");
    }

    //this->show();
}

void CThreeParams::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, gk_iBackTransparency));
    QWidget::paintEvent(pEvent);
}

void CThreeParams::_SlotConfirmBtn()
{
    double dTime = m_pTimeLineEidt->GetLineEditText().toDouble();
    if(dTime <= 0)
    {
        ShowInformation(this, tr("提示"), tr("时间不能为0"));
        return;
    }

    double dIn = m_pInLineEdit->GetLineEditText().toDouble();
    double dDe = m_pDeLineEdit->GetLineEditText().toDouble();
    if(dIn < 0 || dIn > 100 || dDe < 0 || dDe > 100)
    {
        ShowInformation(this, tr("提示"), tr("加速/减速行程占比范围只能在0~100"));
        return;
    }

    QString strParams = QString("%1,%2,%3").arg(dTime).arg(dIn).arg(dDe);
    emit SignalParamsConfirm(m_iRow, strParams);
    this->close();
}

void CThreeParams::_SlotCancelBtn()
{
    this->close();
}

QGroupBox *CThreeParams::_CreateGroup()
{
    QGroupBox *pGroupBox = new QGroupBox(this);
    pGroupBox->setWindowOpacity(1);
    pGroupBox->setFixedSize(600, 400);

    m_pTimeLineEidt = new CLabelLineEdit(tr("运行时间:"));
    m_pTimeLineEidt->SetLabelFixedSize(150, 50);
    m_pTimeLineEidt->SetLineEditFixedSize(150, 50);

    QLabel *pLabel1 = new QLabel(tr("ms"));
    pLabel1->setFixedHeight(50);

    m_pInLineEdit = new CLabelLineEdit(tr("加速行程占比:"));
    m_pInLineEdit->SetLabelFixedSize(150, 50);
    m_pInLineEdit->SetLineEditFixedSize(150, 50);

    QLabel *pLabel2 = new QLabel(tr("n%"));
    pLabel2->setFixedHeight(50);

    m_pDeLineEdit = new CLabelLineEdit(tr("减速行程占比:"));
    m_pDeLineEdit->SetLabelFixedSize(150, 50);
    m_pDeLineEdit->SetLineEditFixedSize(150, 50);

    QLabel *pLabel3 = new QLabel(tr("n%"));
    pLabel3->setFixedHeight(50);

    m_pConfirmBtn = new QPushButton(tr("确认"));
    m_pConfirmBtn->setFixedSize(130, 50);
    connect(m_pConfirmBtn, &QPushButton::clicked, this, &CThreeParams::_SlotConfirmBtn);

    m_pCancelBtn = new QPushButton(tr("取消"));
    m_pCancelBtn->setFixedSize(130, 50);
    connect(m_pCancelBtn, &QPushButton::clicked, this, &CThreeParams::_SlotCancelBtn);

    QHBoxLayout *pHLayout1 = new QHBoxLayout;
    pHLayout1->setMargin(0);
    pHLayout1->addStretch(1);
    pHLayout1->addWidget(m_pTimeLineEidt);
    pHLayout1->addSpacing(10);
    pHLayout1->addWidget(pLabel1);
    pHLayout1->addStretch(1);

    QHBoxLayout *pHLayout2 = new QHBoxLayout;
    pHLayout2->setMargin(0);
    pHLayout2->addStretch(1);
    pHLayout2->addWidget(m_pInLineEdit);
    pHLayout2->addSpacing(10);
    pHLayout2->addWidget(pLabel2);
    pHLayout2->addStretch(1);

    QHBoxLayout *pHLayout3 = new QHBoxLayout;
    pHLayout3->setMargin(0);
    pHLayout3->addStretch(1);
    pHLayout3->addWidget(m_pDeLineEdit);
    pHLayout3->addSpacing(10);
    pHLayout3->addWidget(pLabel3);
    pHLayout3->addStretch(1);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pConfirmBtn);
    pBtnLayout->addSpacing(30);
    pBtnLayout->addWidget(m_pCancelBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pVLayout = new QVBoxLayout;
    pVLayout->setMargin(0);
    pVLayout->addStretch(1);
    pVLayout->setSpacing(10);
    pVLayout->addLayout(pHLayout1);
    pVLayout->addLayout(pHLayout2);
    pVLayout->addLayout(pHLayout3);
    pVLayout->addSpacing(10);
    pVLayout->addLayout(pBtnLayout);
    pVLayout->addStretch(1);
    pGroupBox->setLayout(pVLayout);

    return pGroupBox;
}
