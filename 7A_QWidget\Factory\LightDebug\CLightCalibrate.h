#ifndef CLIGHTCALIBRATE_H
#define CLIGHTCALIBRATE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-11-09
  * Description: 光学校准
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QPushButton>

#include "CCmdBase.h"
#include "CLabelComboBox.h"
#include "CIPLabelLineEdit.h"
#include "CLabelLineEdit.h"

class CLightCalibrate : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CLightCalibrate(QWidget *parent = nullptr);
    ~CLightCalibrate();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

private slots:
    void _SlotReadBtn();
    void _SlotSetBtn();
    void _SlotMachineChanged(int);
    void _SlotReadLedBtn();
    void _SlotSetLedBtn();
    void _SlotSetMppcVBtn();
    void _SlotGetMppcVBtn();

private:
    void _InitWidget();

private:
    CLabelComboBox *m_pMachineComboBox;
    QPushButton *m_pReadBtn, *m_pSetBtn;
    CIPLabelLineEdit *m_pLineEditList1, *m_pLineEditList2;
    
    // 操作标志，用于判断是否显示弹框
    bool m_bWaitingForGainResult;
    bool m_bWaitingForLedResult;
    bool m_bWaitingForMppcResult;
    QVector<QStringList> m_strValueList;

    CLabelComboBox *m_pLedIComboBox;
    QPushButton *m_pReadLedBtn, *m_pSetLedBtn;

    CLabelLineEdit *m_pMppcVLineEdit;
    QPushButton *m_pReadMppcVBtn, *m_pSetMppcVBtn;
};

#endif // CLIGHTCALIBRATE_H
