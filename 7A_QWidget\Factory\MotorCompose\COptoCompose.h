#ifndef COPTOCOMPOSE_H
#define COPTOCOMPOSE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-11-28
  * Description: 组合光耦
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QGroupBox>
#include <QPushButton>
#include "CLabelComboBox.h"
#include "CLabelLineEdit.h"

class COptoCompose : public QWidget
{
    Q_OBJECT
public:
    explicit COptoCompose(QWidget *parent = nullptr);

    //iType 0 :自定义; 1 : 可替换
    void Show(int iRow, int iType, const QString &strRawParams);

protected:
    void paintEvent(QPaintEvent* pEvent) override;

signals:
    void SignalParamsConfirm(int iRow, const QString &strParams);

private slots:
    void _SlotConfirmBtn();
    void _SlotResetBtn();
    void _SlotCancelBtn();

private:
    QGroupBox *_CreateGroup();

private:
    QList<CLabelLineEdit *> m_pLineEditList;
    QList<CLabelComboBox *> m_pComboBoxList;
    QList<CLabelLineEdit *> m_pIndexLineEditList;
    QPushButton *m_pConfirmBtn, *m_pResetBtn, *m_pCancelBtn;
    int m_iRow;
    int m_iType;
};

#endif // COPTOCOMPOSE_H
