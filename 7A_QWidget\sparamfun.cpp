﻿#include "sparamfun.h"

#include <algorithm>
#include <cmath>
#include <cassert>
#include <deque>
#include <numeric>
// 中位绝对偏差-尖刺
// Helper function to calculate the median of a vector
double findMedian(std::vector<double> data) {
    size_t size = data.size();
    std::sort(data.begin(), data.end());
    return size % 2 == 0 ? (data[size / 2 - 1] + data[size / 2]) / 2 : data[size / 2];
    }

    // Function to correct outliers in a vector using MAD
    std::vector<double> correctOutliersWithMAD(std::vector<double> data, double multiplier ) {
    double median = findMedian(data);
    std::vector<double> deviations(data.size());

    // Calculate absolute deviations from median
    for(size_t i = 0; i < data.size(); ++i) {
        deviations[i] = std::abs(data[i] - median);
    }

    // Calculate the median of the absolute deviations
    double mad = findMedian(deviations);

    // Identify and correct outliers
    for(size_t i = 0; i < data.size(); ++i) {
        if(std::abs(data[i] - median) / mad > multiplier) {
            // Outlier found, replace it with the average of its nearest non-outlier neighbours
            size_t left = i, right = i;
            while(left > 0 && std::abs(data[left - 1] - median) / mad > multiplier) --left;
            while(right < data.size() - 1 && std::abs(data[right + 1] - median) / mad > multiplier) ++right;
            if (left > 0 && right < data.size() - 1) {
                data[i] = (data[left - 1] + data[right + 1]) / 2;
            } else if (left > 0) {
                data[i] = data[left - 1];
            } else if (right < data.size() - 1) {
                data[i] = data[right + 1];
            } else {
                // If there is no non-outlier neighbour, keep the outlier as it is
                continue;
            }
        }
    }
    return data;
}
//滑动窗口z-score算法 - 尖刺
const double Z_THRESHOLD = 3.0;  // Z-score threshold for detecting outliers

// Function to calculate the mean of a vector
double mean(const std::deque<double>& data) {
    return std::accumulate(data.begin(), data.end(), 0.0) / data.size();
}

// Function to calculate the standard deviation of a vector
double stddev(const std::deque<double>& data, double mean) {
    double sum = 0.0;
    for (double value : data) {
        sum += std::pow(value - mean, 2);
    }
    return std::sqrt(sum / data.size());
}

// Function to apply sliding window Z-Score algorithm to a vector
std::vector<double> correctOutliersWithZScore(std::vector<double> data,
                                              int windowSize, double bandwidth) {
    std::deque<double> window(data.begin(), data.begin() + windowSize);

    for (size_t i = windowSize; i < data.size(); ++i) {
        double m = mean(window);
        double sd = stddev(window, m);
        double zScore = (data[i] - m) / sd;

        // Check if data[i] is an outlier
        if (std::abs(zScore) > bandwidth) {
            // If it is an outlier, replace it with the mean of the sliding window
            data[i] = m;
        }

        window.pop_front();
        window.push_back(data[i]);
    }

    return data;
}
// Hampel标识符方法-尖峰
double median(std::vector<double> v) {
    size_t n = v.size() / 2;
    nth_element(v.begin(), v.begin()+n, v.end());
    return v[n];
}

void hampelFilter(std::vector<double>& data, int windowSize, double n_sigma) {
    int halfWindowSize = windowSize / 2;
    for (int i = 0; i < data.size(); ++i) {
        int start = std::max(0, i - halfWindowSize);
        int end = std::min((int)data.size() - 1, i + halfWindowSize);
        std::vector<double> windowData(data.begin() + start, data.begin() + end + 1);
        double med = median(windowData);

        std::vector<double> deviations(windowData.size());
        std::transform(windowData.begin(), windowData.end(), deviations.begin(), [med](double x) { return std::abs(x - med); });
        double mad = median(deviations);

        if (std::abs(data[i] - med) > n_sigma * mad) {
            // Replace with median if it's an outlier
            data[i] = med;
        }
    }
}
//////////////////////////////////////////////////////////////////
// Function to calculate the Gaussian kernel
double GaussianKernel(double distance, double bandwidth) {
    double exponent = -(distance * distance) / (2 * bandwidth * bandwidth);
    return exp(exponent);
}

// Function to calculate LOESS for a single point
double LoessSinglePoint(const std::vector<double>& xs, const std::vector<double>& ys, double x, double bandwidth) {
    assert(xs.size() == ys.size() && xs.size() > 0);

    int n = xs.size();
    double weights_sum = 0.0;
    double weighted_ys_sum = 0.0;

    for (int i = 0; i < n; ++i) {
        double weight = GaussianKernel(fabs(x - xs[i]), bandwidth);
        weights_sum += weight;
        weighted_ys_sum += weight * ys[i];
    }

    return weighted_ys_sum / weights_sum;
}

// Function to calculate LOESS for a series of points局部加权回归平滑算法
std::vector<double> Loess(const std::vector<double>& xs, const std::vector<double>& ys,
                          const std::vector<double>& smoothing_xs, double bandwidth) {
    assert(bandwidth > 0);
    std::vector<double> smoothing_ys(smoothing_xs.size());

    for (size_t i = 0; i < smoothing_xs.size(); ++i) {
        smoothing_ys[i] = LoessSinglePoint(xs, ys, smoothing_xs[i], bandwidth);
    }

    return smoothing_ys;
}
