#ifndef CNORMALMOTOR_H
#define CNORMALMOTOR_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-11-30
  * Description: 通用电机
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QObject>
#include <QWidget>
#include <QPushButton>

#include "CCmdBase.h"
#include "CLabelComboBox.h"
#include "CLabelLineEdit.h"

class CNormalMotor : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CNormalMotor(QWidget *parent = nullptr);
    ~CNormalMotor();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

public slots:
    void SlotSoftTypeChanged(int iSoftType);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;

private slots:
    void _SlotMachineComboBoxChanged(int iMachineID);
    void _SlotMotorComboBoxChanged(int index);
    void _SlotParamComboBoxChanged(int index);
    void _SlotDirReadBtn();
    void _SlotDirSaveBtn();
    void _SlotDirDBBtn();
    void _SlotParamReadBtn();
    void _SlotParamSaveBtn();
    void _SlotParamDBBtn();
    void _SlotStepReadBtn();
    void _SlotStepSaveBtn();
    void _SlotStepDBBtn();
    void _SlotHzReadBtn();
    void _SlotHzSaveBtn();
    void _SlotHzDBBtn();
    void _SlotResetParamBtn();
    void _SlotResetConfigBtn();
    void _SlotMoveBtn();

private:
    void _InitWidget();
    void _InitLayout();

private:
    CLabelComboBox *m_pMotorComboBox;
    CLabelComboBox *m_pDirComboBox;
    QPushButton *m_pDirReadBtn, *m_pDirSaveBtn, *m_pDirDBBtn;
    CLabelComboBox *m_pParamComboBox;
    QPushButton *m_pParamReadBtn, *m_pParamSaveBtn, *m_pParamDBBtn;
    CLabelLineEdit *m_pStepLineEdit;
    QPushButton *m_pStepReadBtn, *m_pStepSaveBtn, *m_pStepDBBtn;
    CLabelLineEdit *m_pHzLineEdit;
    QPushButton *m_pHzReadBtn, *m_pHzSaveBtn, *m_pHzDBBtn;
    QPushButton *m_pResetParamBtn, *m_pResetConfigBtn;
    CLabelComboBox *m_pMoveComboBox;
    CLabelLineEdit *m_pMoveLineEdit;
    QPushButton *m_pMoveBtn;
    QList<CLabelLineEdit *> m_pLineEditList;
    CLabelComboBox *m_pMachineComboBox;

    int m_iMotorIndex;
    int m_iMachineID;
    QStringList m_strMotorNameList;
    QList<QList<QList<double>>> m_dAllMotorParamList;
};

#endif // CNORMALMOTOR_H
