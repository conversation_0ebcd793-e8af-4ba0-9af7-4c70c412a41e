﻿#ifndef CNETWORKWIDGET_H
#define CNETWORKWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-03
  * Description: 网络设置 LIS 有线 WIFI
  * -------------------------------------------------------------------------
  * History:
  * 1.chenhao 11.8日 简化版有线网络模块
  * 2.hongxirong 20250116 重构网络模块
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QPushButton>

#include "Eth1/CEth1Widget.h"
#include "Lis/CLisWidget.h"
#include "WiFi/CWiFiWidget.h"
#include "SystemPage/CSysFirstTitleWidget.h"

class CNetworkWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CNetworkWidget(QWidget *parent = nullptr);

signals:
    void SignalReturn();

private:
    void _InitWidget();
    void _InitLayout();

private:
    CSysFirstTitleWidget *m_pCSysTtileLabelWidget;
    QLabel *m_pBackgroundLabel;

    CEth1Widget *m_pEth1Widget;
    CLisWidget *m_pLisWidget;
    CWiFiWidget *m_pWiFiWidget;
    QLabel *m_pVLabel;
    QPushButton *m_pReturnBtn;
};

#endif // CNETWORKWIDGET_H
