﻿#ifndef _ORU_R01_H_
#define _ORU_R01_H_

#include <vector>
#include "../group/ORU_R01_PATIENT_RESULT.h"
#include "../interface/IMSH.h"
#include "../interface/IDSC.h"
#include "../common/HL7Message.h"
#include "../interface/IORU_R01.h"
#include "../segment/DSC.h"
#include "../segment/MSH.h"
/**
* ORU_R01消息结构包含如下内容：
*  MSH
* {
*  [PID]
*  {
*	OBR
*   {[OBX]}
*   }
*  }
* [DSC]
*
**/
class ORU_R01 : public HL7Message, public IORU_R01
{
public:
	ORU_R01();
	~ORU_R01();
	DECLARE_OBJECTBASE

	BEGIN_IMPL_QUERYIF(ORU_R01)
		IMPL_QUERYIF_BYPATH(IF_OBJECTBASE, IHL7Message, IObjectBase)
		IMPL_QUERYIF(IF_HL7MESSAGE, IHL7Message)
		IMPL_QUERYIF(IF_ORU_R01, IORU_R01)
		END_IMPL_QUERYIF()

	void SetDSC(IDSC* dsc);

	void SetMSH(IMSH* msh);

	void Add_Patient_Result(IORU_R01_PATIENT_RESULT* patientResult);

	bool GetMSH(IMSH* msh);

	bool GetDSC(IDSC* dsc);

	NRET GetPatientResultAll(IObjectList* objectList);

	virtual void Build();

	virtual void Parse(const char* messageStr);

	void Free();

	virtual int GetPatientResultSize();

	virtual int GetOrderObservationSize(std::size_t patientResultIndex);

	virtual int GetOBXSize(std::size_t patientResultIndex, std::size_t orderOBRIndex);

	virtual bool GetPID(IPID* pid, std::size_t index);

	virtual bool GetOBR(IOBR* obr, std::size_t patientResultIndex,
		std::size_t orderOBRIndex);

	virtual bool GetOBX(IOBX* obx, std::size_t patientResultIndex,
		std::size_t orderOBRIndex, std::size_t obxIndex);

private:
	std::vector<IORU_R01_PATIENT_RESULT*> m_patientResultVect;
	MSH* m_msh;
	DSC* m_dsc;
};

#endif
