#ifndef CHISTORYPAGE_H
#define CHISTORYPAGE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-26
  * Description: 历史
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QCheckBox>
#include <QListWidget>
#include <QPushButton>

#include "CLineEdit.h"
#include "CHLabelTitleWidget.h"
#include "CHistoryItemWidget.h"
#include "CHistorySearchWidget.h"
#include "CHistoryDetailWidget.h"
#include "CProgressBar.h"

class CHistoryPage : public QWidget
{
    Q_OBJECT
public:
    explicit CHistoryPage(QWidget *parent = nullptr);

public slots:
    void SlotAppStartEnd();
    void SlotRefreshHistory();
    void SlotUpdateItemByID(int iHistoryID);
    void SlotAddHistoryID(int iHistoryID);
    void SlotDelHistoryID(int iHistoryID);
    void SlotSearchConfirm(const SHistroySearchStruct &sSearchStruct);
    void SlotPrintLeftNum(int iLeftNum);
    void SlotLisUploadLeftNum(int iLeftNum);
    void SlotLisUploadError(QString strError);

protected:
    void showEvent(QShowEvent *pEvent) override;
    void hideEvent(QHideEvent *pEvent) override;

private slots:
    void _SlotSelectAllCheckedBox();
    void _SlotItemChecked(int iHistoryID, bool bChecked);
    void _SlotListWidgetItemClicked(QListWidgetItem *pItem);
    void _SlotListWidgetItemDoubleClicked(QListWidgetItem *pItem);
    void _SlotGotoPageBtn();
    void _SlotPrePageBtn();
    void _SlotNextPageBtn();
    void _SlotDetailBtn();
    void _SlotQueryBtn();
    void _SlotShowAllBtn();
    void _SlotLisBtn();
    void _SlotExportBtn();
    void _SlotPrintBtn();
    void _SlotExportProgress(QString strProgress);
    void _SlotExportError();
    void _SlotExportEnd();

    void _SlotDeleteBtn();
    void _SlotDeleteProgress(int index);
    void _SlotDeleteEnd();

signals:
    void SignalExportProgress(QString strProgress);
    void SignalExportError();
    void SignalExportEnd();

    void SignalDeleteProgress(int index);
    void SignalDeleteEnd();

private:
    void _ShowAll();
    void _ReGetDataPageLines();
    void _ShowCurrentPageAllData();   //显示当前页所有数据
    void _ShowCurrentPageQueryData(); //显示当前页查询数据
    void _UpdateListWidgetData(const QList<SResultInfoStruct> &sResultList);
    void _UpdatePageBtn();
    void _UpdateSelectedInfo();
    QList<int> _GetSelectedHistoryID();
    void _Thread2Export(QList<int> iHistoryIDList);
    void _Fun2Delete(QList<int> iHistoryIDList);
    void _RefreshHistoryHandle();

private:
    void _InitWidget();
    void _InitLayout();

private:
    bool m_bAutoReflash;
    QString m_strCurrentUser;

    bool m_bLising;
    bool m_bExporting;
    bool m_bPrinting;

    bool m_bHasQuery;
    SHistroySearchStruct m_sSearchStruct;

    int m_iTotalLines;
    int m_iTotalPages;
    int m_iCurrentPage;
    int m_iLeftLines;
    const int m_iOnePageLines;

    int m_iSelectedSize;
    QMap<int, bool> m_iHistoryIDMap;
    const QString m_strTipText;

private:
    int m_iBtnWidth, m_iCancalBtnWidth;
    int m_iGotoBtnWidth;

private:
    CHLabelTitleWidget *m_pCHLabelTitleWidget;
    QCheckBox *m_pSelectAllCheckBox;
    QLabel *m_pSelectInfoLabel;
    QLabel *m_pGotoLabel1, *m_pGotoLabel2;
    CLineEdit *m_pGotoLineEdit;
    QPushButton *m_pGotoBtn;
    QPushButton *m_pPrePageBtn;
    QLabel *m_pPageLabel;
    QPushButton *m_pNextPageBtn;

    QListWidget *m_pListWidget;

    QLabel *m_pLisInfoLabel;
    QLabel *m_pExportInfoLabel;
    QLabel *m_pPrintInfoLabel;

    QPushButton *m_pDetailBtn, *m_pQueryBtn, *m_pShowAllBtn, *m_pLisBtn, *m_pExportBtn, *m_pPrintBtn;
    QPushButton *m_pDeleteBtn;
    CProgressBar *m_pProgressBar;
    QLabel *m_pBackgroundLabel;

    CHistorySearchWidget *m_pCHistorySearchWidget;
    CHistoryDetailWidget *m_pCHistoryDetailWidget;
};

#endif // CHISTORYPAGE_H
