#
# These things are run when an Openbox X Session is started.
# You may place a similar script in $HOME/.config/openbox/autostart
# to run user-specific things.
#

# If you want to use GNOME config tools...
#
#if test -x /usr/lib/aarch64-linux-gnu/gnome-settings-daemon >/dev/null; then
#  /usr/lib/aarch64-linux-gnu/gnome-settings-daemon &
#elif which gnome-settings-daemon >/dev/null 2>&1; then
#  gnome-settings-daemon &
#fi

# If you want to use XFCE config tools...
#
#xfce-mcs-manager &

export QT_QPA_FONTDIR=/usr/share/fonts/
export XDG_RUNTIME_DIR=${XDG_RUNTIME_DIR:-/var/run}
export QT_QPA_PLATFORM=${QT_QPA_PLATFORM:-wayland}

function start_7c()
{
	cp /root/lib/lib* /usr/lib
        cp /root/app/* /root/
        sync

	ip link set can0 down
	ip link set can0 type can bitrate 250000
	ip link set can0 up

	ifconfig can0 txqueuelen 8192

	echo 1 > /proc/sys/kernel/core_uses_pid
	echo /root/coredump > /proc/sys/kernel/core_pattern
	ulimit -c unlimited

	cd /root
	killall 7CAPP
	/root/7CAPP &
}


case "$1" in
        start)
                echo -n "starting weston... "
                start_7c
                echo "done."
                ;;
        stop)
                echo -n "stoping weston... "
                
                echo "done."
                ;;
        restart|reload)
                echo -n "stoping weston... "
                echo "done."
                ;;
        *)
                echo "Usage: $0 {start|stop|restart}"
                exit 1
esac

exit 0

