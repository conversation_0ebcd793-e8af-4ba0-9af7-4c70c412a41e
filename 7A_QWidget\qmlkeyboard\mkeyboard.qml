
import QtQuick 2.12
import QtQuick.Window 2.12
import QtQuick.VirtualKeyboard 2.4
import QtQuick.VirtualKeyboard.Settings 2.2
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12
import Qt.labs.qmlmodels 1.0

import CQmlKeybaord 1.0

Rectangle {
    id: window
    height: 370
    width: 1024
    color: "#ECF5FC"

    property var m_activeLocales : ["en_GB","zh_CN"]
    property var m_locale: "en_GB"

    function slotSetWidth(iWidth) {
        //window.width = iWidth
        //console.log("qml keybaord width",window.width,"inputPanel width:",inputPanel.width)
    }

    function showQuickWidget() {
        CQmlKeybaord.showQuickWidget()
    }

    InputPanel {
        id: inputPanel
        z: 99
        x: 0
        y: window.height
        width: window.width

        Component.onCompleted: {
            var lan = CQmlKeybaord.getCurrentLanguage()
            VirtualKeyboardSettings.activeLocales = m_activeLocales
            VirtualKeyboardSettings.locale = m_locale

            CQmlKeybaord.onSignalSetKeyboardWidth.connect(slotSetWidth)

            inputPanel.setKeyboardBackgroundColor("#D2ECF5FC")
            inputPanel.setKeysBackgroundColor("white")
            inputPanel.setKeysTextColor("black")

            var strVer = inputPanel.getKeyboardVersion()
            console.log("qml keyboard version:",strVer)
            CQmlKeybaord.setVersion(strVer)
        }

        onYChanged: {
            if(y >= window.height)
                CQmlKeybaord.hideKeyboard()
        }

        states: State {
            name: "visible"
            when: inputPanel.active
            PropertyChanges {
                target: inputPanel
                y: 70
            }
        }
        transitions: Transition {
            from: ""
            to: "visible"
            reversible: true
            ParallelAnimation {
                NumberAnimation {
                    properties: "y"
                    duration: 200
                    easing.type: Easing.InOutQuad
                }
            }
        }
    }
}
