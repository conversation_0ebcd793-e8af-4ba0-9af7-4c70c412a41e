#include "greekkey.h"

#include "common/keyboardtoolbutton/keyboardtoolbutton.h"

GreekKey::<PERSON><PERSON><PERSON>(const QString& name):
    LanguageBase<PERSON><PERSON>(name)
{
    InitButtons();
}

void GreekKey::InitButtons()
{
    QMap<int, QList<KeyBoardToolButton*> > map;
    QList<KeyBoardToolButton*> firstButtons, secondButtons, thirdButtons;

    firstButtons.append(new KeyBoardToolButton(":",":","",""));
    firstButtons.append(new KeyBoardToolButton("\u03C2","\u03A3","",""));
    firstButtons.append(new KeyBoardToolButton("\u03B5","\u0395","\u03AD","\u0388"));
    firstButtons.append(new KeyBoardToolButton("\u03C1","\u03A1","",""));
    firstButtons.append(new KeyBoardToolButton("\u03C4","\u03A4","",""));
    firstButtons.append(new KeyBoardToolButton("\u03C5","\u03A5","\u03CB,\u03CD,\u03B0","\u03AB,\u038E,\u03B0"));
    firstButtons.append(new KeyBoardToolButton("\u03B8","\u0398","",""));
    firstButtons.append(new KeyBoardToolButton("\u03B9","\u0399","\u03AF,\u03CA,\u0390","\u038A,\u03AA,\u0390"));
    firstButtons.append(new KeyBoardToolButton("\u03BF","\u039F","\u03CC","\u038C"));
    firstButtons.append(new KeyBoardToolButton("\u03C0","\u03A0","",""));

    secondButtons.append(new KeyBoardToolButton("\u03B1","\u0391","\u03AC","\u0386"));
    secondButtons.append(new KeyBoardToolButton("\u03C3","\u03A3","",""));
    secondButtons.append(new KeyBoardToolButton("\u03B4","\u0394","",""));
    secondButtons.append(new KeyBoardToolButton("\u03C6","\u03A6","",""));
    secondButtons.append(new KeyBoardToolButton("\u03B3","\u0393","",""));
    secondButtons.append(new KeyBoardToolButton("\u03B7","\u0397","\u03AE","\u0389"));
    secondButtons.append(new KeyBoardToolButton("\u03BE","\u039E","",""));
    secondButtons.append(new KeyBoardToolButton("\u03BA","\u039A","",""));
    secondButtons.append(new KeyBoardToolButton("\u03BB","\u039B","",""));

    thirdButtons.append(GetLeftCapsLockBtn());
    thirdButtons.append(new KeyBoardToolButton("\u03B6","\u0396","",""));
    thirdButtons.append(new KeyBoardToolButton("\u03C7","\u03A7","",""));
    thirdButtons.append(new KeyBoardToolButton("\u03C8","\u03A8","",""));
    thirdButtons.append(new KeyBoardToolButton("\u03C9","\u03A9","\u03CE","\u038F"));
    thirdButtons.append(new KeyBoardToolButton("\u03B2","\u0392","",""));
    thirdButtons.append(new KeyBoardToolButton("\u03BD","\u039D","",""));
    thirdButtons.append(new KeyBoardToolButton("\u03BC","\u039C","",""));
    thirdButtons.append(GetRightCapsLockBtn());

    map.insert(0,firstButtons);
    map.insert(1,secondButtons);
    map.insert(2,thirdButtons);

    SetButtonsMap(map);
    SetTranslate("κινέζικα","Αγγλικά","Μαθηματικά","Χώρος");
}
