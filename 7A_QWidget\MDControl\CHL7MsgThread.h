#ifndef CHL7MSGTHREAD_H
#define CHL7MSGTHREAD_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-03-27
  * Description: LIS发送和解析线程
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QMap>
#include <QMutex>
#include <QTimer>
#include <QThread>
#include <QDateTime>

class CHL7MsgThread : public QObject
{
    Q_OBJECT
public:
    static CHL7MsgThread &GetInstace();
    ~CHL7MsgThread();

    void AddHistoryID(int iHistoryID);
    void AddHistoryIDList(QList<int> iIDList);
    void AddSampleID(QString strSampleID);
    void CancelUpload();

public slots:
    void SlotReadServer(QByteArray qReadByte);

signals:
    void SignalInitThread();
    void SignalUploadLeftNum(int iLeftNum);
    void SignalUploadError(QString strError);
    void SignalSampleInfoMap(QMap<int, QString> strDSPMap);

private slots:
    void _SlotInitThread();
    void _SlotExitThread();
    void _SlotSendTimer();
    void _SendSampleID();

private:
    CHL7MsgThread();
    QByteArray _GetSendByte(int iHistoryID);
    QByteArray _GetQuerySampleByte(QString strSampleID);
    QString _GetMsgCtrlID();
    void _ParseSampleInfo(QByteArray qByteData);

private:
    QThread m_qThread;

    QMutex m_qMutex;
    QTimer *m_pSendTimer;
    QList<int> m_iHistoryIDList;
    QStringList m_strSampleIDList;

    bool m_bWaitAck;
    int m_iReSendTimes;
    int m_iMsgCtrID;
    int m_iLastHistoryID;
    QByteArray m_qLastSendByte;
    QDateTime m_qSendDateTime;

    QByteArray m_qReceiveByte;

    Q_DISABLE_COPY(CHL7MsgThread)
};

#endif // CHL7MSGTHREAD_H
