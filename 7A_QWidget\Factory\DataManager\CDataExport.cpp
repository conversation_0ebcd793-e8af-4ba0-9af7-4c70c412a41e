#include "CDataExport.h"
#include <thread>
#include <QDir>
#include <QMultiMap>
#include <QDebug>
#include <QThread>
#include <QDateTime>
#include <QBoxLayout>
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "CMessageBox.h"

CDataExport::CDataExport(QWidget *parent)
    : QWidget(parent)
    , m_bGuard(true)
    , m_bLogExporting(false)
    , m_bPdfExporting(false)
    , m_bXlsxExporting(false)
    , m_eDateType(eStartDateLog)
    , m_strTipsText(tr("提示"))
{  
    _InitWidget();
    _InitLayout();

    connect(this, &CDataExport::SignalNoFiles, this, &CDataExport::_SlotNoFiles);
    connect(this, &CDataExport::SignalExportProgress, this, &CDataExport::_SlotExportProgress);
    connect(this, &CDataExport::SignalExportError, this, &CDataExport::_SlotExportError);
    connect(this, &CDataExport::SignalExportEnd, this, &CDataExport::_SlotExportEnd);

    std::thread guardthread(&CDataExport::_Thread2GuardDirSize, this);
    guardthread.detach();
}

CDataExport::~CDataExport()
{
    m_bGuard = false;
}

void CDataExport::_SlotShowDateWidget()
{
    CLabelDate *pCLabelDate = dynamic_cast<CLabelDate *>(sender());
    if(nullptr == pCLabelDate)
        return;
    m_eDateType = (EnumDateType)pCLabelDate->property("DateType").toInt();
    qDebug()<<Q_FUNC_INFO<<m_eDateType;
    m_pCDateWidget->SetDate(pCLabelDate->GetDateString());
    m_pCDateWidget->show();
}

void CDataExport::_SlotConfirmDate(const QString &strDate)
{
    switch (m_eDateType)
    {
    case eStartDateLog:
        m_pLogBeginDate->SetDateString(strDate);
        break;
    case eEndDateLog:
        m_pLogEndDate->SetDateString(strDate);
        break;
    case eStartDatePdf:
        m_pPdfBeginDate->SetDateString(strDate);
        break;
    case eEndDatePdf:
        m_pPdfEndDate->SetDateString(strDate);
        break;
    case eStartDateXlsx:
        m_pXlsxBeginDate->SetDateString(strDate);
        break;
    case eEndDateXlsx:
        m_pXlsxEndDate->SetDateString(strDate);
        break;
    default:
        break;
    }
}

void CDataExport::_SlotExportLogBtn()
{
    if(!UDiskExist(this))
        return;

    m_pLogLabel->setText("0/0");
    m_pLogExportBtn->setEnabled(false);
    QString strBeginDate = m_pLogBeginDate->GetDateString();
    QString strEndDate = m_pLogEndDate->GetDateString();
    std::thread mythread(&CDataExport::_Thread2ExortLog, this, strBeginDate, strEndDate);
    mythread.detach();
}

void CDataExport::_SlotExportPdfBtn()
{
    if(!UDiskExist(this))
        return;

    m_pPdfLabel->setText("0/0");
    m_pPdfExportBtn->setEnabled(false);
    QString strBeginDate = m_pPdfBeginDate->GetDateString();
    QString strEndDate = m_pPdfEndDate->GetDateString();
    std::thread mythread(&CDataExport::_Thread2ExortPdf, this, strBeginDate, strEndDate);
    mythread.detach();
}

void CDataExport::_SlotExportXlsxBtn()
{
    if(!UDiskExist(this))
        return;

    m_pXlsxLabel->setText("0/0");
    m_pXlsxExportBtn->setEnabled(false);
    QString strBeginDate = m_pXlsxBeginDate->GetDateString();
    QString strEndDate = m_pXlsxEndDate->GetDateString();
    std::thread mythread(&CDataExport::_Thread2ExortXlsx, this, strBeginDate, strEndDate);
    mythread.detach();
}

void CDataExport::_SlotNoFiles(int iFileType)
{
    switch (iFileType)
    {
    case eLog:
        m_pLogExportBtn->setEnabled(true);
        ShowInformation(this, m_strTipsText, tr("没有此日期的日志"));
        break;
    case ePdf:
        m_pPdfExportBtn->setEnabled(true);
        ShowInformation(this, m_strTipsText, tr("没有此日期的PDF"));
        break;
    case eXlsx:
        m_pXlsxExportBtn->setEnabled(true);
        ShowInformation(this, m_strTipsText, tr("没有此日期的Xlsx"));
        break;
    default:
        break;
    }
}

void CDataExport::_SlotExportProgress(int iFileType, QString strProgress)
{
    switch (iFileType)
    {
    case eLog:
        m_pLogLabel->setText(strProgress);
        break;
    case ePdf:
        m_pPdfLabel->setText(strProgress);
        break;
    case eXlsx:
        m_pXlsxLabel->setText(strProgress);
        break;
    default:
        break;
    }
}

void CDataExport::_SlotExportError(int iFileType)
{
    switch (iFileType)
    {
    case eLog:
        m_pLogExportBtn->setEnabled(true);
        ShowError(this, m_strTipsText, tr("日志导出失败，请重试"));
        break;
    case ePdf:
        m_pPdfExportBtn->setEnabled(true);
        ShowError(this, m_strTipsText, tr("PDF导出失败，请重试"));
        break;
    case eXlsx:
        m_pXlsxExportBtn->setEnabled(true);
        ShowError(this, m_strTipsText, tr("Xlsx导出失败，请重试"));
        break;
    default:
        break;
    }
}

void CDataExport::_SlotExportEnd(int iFileType)
{
    if(!m_bLogExporting && !m_bPdfExporting && !m_bXlsxExporting)
        ExportEndUmountUSB();

    switch (iFileType)
    {
    case eLog:
        m_pLogExportBtn->setEnabled(true);
        ShowSuccess(this, m_strTipsText, tr("日志导出完成"));
        break;
    case ePdf:
        m_pPdfExportBtn->setEnabled(true);
        ShowSuccess(this, m_strTipsText, tr("PDF导出完成"));
        break;
    case eXlsx:
        m_pXlsxExportBtn->setEnabled(true);
        ShowSuccess(this, m_strTipsText, tr("Xlsx导出完成"));
        break;
    default:
        break;
    }
}

void CDataExport::_Thread2ExortLog(const QString &strBeginDate, const QString &strEndDate)
{
    qDebug()<<"导出日志:"<<strBeginDate<<strEndDate<<QThread::currentThread();

    QStringList strExportLogList;
    QStringList strDirList = CPublicConfig::GetInstance()->GetLogDirList();
    for(int i=0; i<strDirList.size(); i++)
        strExportLogList.append(_GetMacthDateFilesFromDir(eLog, strDirList.at(i), strBeginDate, strEndDate));
    int iLogNums = strExportLogList.size();
    if(0 == iLogNums)
    {
        emit SignalNoFiles(eLog);
        return;
    }

    m_bLogExporting = true;
    QDir qExportDir = GetUDiskDir() + "7C_Log_" + QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    CreateDir(qExportDir.path());
    for(int i=0; i<iLogNums; i++)
    {
        bool bCopy = CopyQFileDir(strExportLogList.at(i), qExportDir);
        emit SignalExportProgress(eLog, QString("%1/%2").arg(i+1).arg(iLogNums));
        if(false == bCopy)
        {
            m_bLogExporting = false;
            emit SignalExportError(eLog);
            return;
        }
    }
    m_bLogExporting = false;
    emit SignalExportEnd(eLog);
}

void CDataExport::_Thread2ExortPdf(const QString &strBeginDate, const QString &strEndDate)
{
    qDebug()<<"导出PDF:"<<strBeginDate<<strEndDate<<QThread::currentThread();

    QString strPdfDir = CPublicConfig::GetInstance()->GetPdfDir();
    QStringList strExportPdfList = _GetMacthDateFilesFromDir(ePdf, strPdfDir, strBeginDate, strEndDate);
    int iPdfNums = strExportPdfList.size();
    if(0 == iPdfNums)
    {
        emit SignalNoFiles(ePdf);
        return;
    }

    m_bPdfExporting = true;
    QDir qExportDir = GetUDiskDir() + "7C_PDF_" + QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    CreateDir(qExportDir.path());
    for(int i=0; i<iPdfNums; i++)
    {
        bool bCopy = CopyQFileDir(strExportPdfList.at(i), qExportDir);
        emit SignalExportProgress(ePdf, QString("%1/%2").arg(i+1).arg(iPdfNums));       
        if(false == bCopy)
        {
            m_bPdfExporting = false;
            emit SignalExportError(ePdf);
            return;
        }
    }
    m_bPdfExporting = false;
    emit SignalExportEnd(ePdf);
}

void CDataExport::_Thread2ExortXlsx(const QString &strBeginDate, const QString &strEndDate)
{
    qDebug()<<"导出Xlsx:"<<strBeginDate<<strEndDate<<QThread::currentThread();

    QString strXlsxDir = CPublicConfig::GetInstance()->GetXlsxDir();
    QStringList strExportXlsxList = _GetMacthDateFilesFromDir(eXlsx, strXlsxDir, strBeginDate, strEndDate);
    int iXlsxNums = strExportXlsxList.size();
    if(0 == iXlsxNums)
    {
        emit SignalNoFiles(eXlsx);
        return;
    }

    m_bXlsxExporting = true;
    QDir qExportDir = GetUDiskDir() + "7C_Xlsx_" + QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    CreateDir(qExportDir.path());
    for(int i=0; i<iXlsxNums; i++)
    {
        bool bCopy = CopyQFileDir(strExportXlsxList.at(i), qExportDir);
        emit SignalExportProgress(eXlsx, QString("%1/%2").arg(i+1).arg(iXlsxNums));
        if(false == bCopy)
        {
            m_bXlsxExporting = false;
            emit SignalExportError(eXlsx);
            return;
        }
    }
    m_bXlsxExporting = false;
    emit SignalExportEnd(eXlsx);
}

QStringList CDataExport::_GetMacthDateFilesFromDir(int iFileType, const QDir &qDir, const QString &strBeginDate, const QString &strEndDate)
{    
    QStringList strFileList;
    QDate qBeginDate = QDate::fromString(strBeginDate, "yyyy-MM-dd");
    QDate qEndDate = QDate::fromString(strEndDate, "yyyy-MM-dd");
    if(qBeginDate > qEndDate)
    {
        qBeginDate = QDate::fromString(strEndDate, "yyyy-MM-dd");
        qEndDate = QDate::fromString(strBeginDate, "yyyy-MM-dd");
    }

    QFileInfoList fileList = qDir.entryInfoList(QDir::Files);
    for(int i=0; i<fileList.size(); i++)
    {
        QStringList strNameList = fileList.at(i).baseName().split("_");
        if(strNameList.size() < 2)
            continue;
        QDate qFileDate;
        if(eLog == iFileType)
            qFileDate = QDate::fromString(strNameList.last(), "yyyyMMdd");
        else if(ePdf == iFileType || eXlsx == iFileType)
            qFileDate = QDate::fromString(strNameList.first().mid(0, 8), "yyyyMMdd");
        else
            qFileDate = fileList.at(i).metadataChangeTime().date();

        if(qFileDate >= qBeginDate && qFileDate <= qEndDate)
            strFileList.push_back(fileList.at(i).absoluteFilePath());
    }

    return strFileList;
}

void CDataExport::_GuardDirSize(QString strPath, qint64 iMaxSize)
{
    QDir qDir(strPath);
    if(!qDir.exists())
        return;

    qint64 iDirSize = 0;
    QMap<QString, QString> qFilePathMap;
    QMultiMap<QString, QString> strFileMap;

    QFileInfoList qInfoList = qDir.entryInfoList(QDir::Files);
    for(int i=0; i<qInfoList.size(); i++)
    {
        QFileInfo qFileInfo = qInfoList.at(i);
        iDirSize += qFileInfo.size();

        qFilePathMap.insert(qFileInfo.absoluteFilePath(), "");
        continue;

//        QString strFileTime = qFileInfo.birthTime().toString("yyyyMMddhhmmssz");
//        QString strFileName = qFileInfo.fileName();
//        if(strFileName.contains(".pdf") || strFileName.contains(".xlsx"))
//        {
//            QStringList strCharList = qFileInfo.baseName().split("_");
//            if(strCharList.size() > 0)
//                strFileTime = strCharList.first();
//        }
//        if(strFileName.contains(".log") || strFileName.contains(".zip"))
//        {
//            QStringList strCharList = qFileInfo.baseName().split("_");
//            if(strCharList.size() > 1)
//                strFileTime = strCharList.at(1);
//        }

//        QString strFilePath = qFileInfo.absoluteFilePath();
//        strFileMap.insert(strFileTime, strFilePath);
    }

    double dK = 1.0 * iDirSize / 1024;
    double dM = 1.0 * iDirSize / 1024 / 1024;
    double dMax = iMaxSize / 1024 / 1024;
    qDebug()<<"文件夹大小:"<<qDir.path()<<dK<<"K,"<<dM<<"M"<<",预设大小:"<<dMax<<"M";
    //qDebug()<<strPath<<qFilePathMap.keys();

    if(iDirSize < iMaxSize)
        return;

    {
        QStringList strAllPathList = qFilePathMap.keys();
        qint64 iDelSize = 0;
        qint64 iNeedSize = iDirSize - iMaxSize + iMaxSize / 4.0;

        for(int i=0; i<strAllPathList.size(); i++)
        {
            if(iDelSize >= iNeedSize)
                break;

            QString strFilePath = strAllPathList.at(i);
            QFileInfo qFileInfo(strFilePath);
            iDelSize += qFileInfo.size();
            qDebug()<<"delete"<<strFilePath;
            QFile::remove(strFilePath);
        }

        System("sync");
        return;
    }
    return;


    //删除十分之一文件
    int index = 0;
    int del = strFileMap.size() / 10;    
    for(auto it=strFileMap.begin(); it!=strFileMap.end(); it++)
    {
        if(index >= del)
            break;

        qDebug()<<"delete:"<<it.value();
        QFile::remove(it.value());
        index++;
    }
    System("sync");
    return;

    {
        //删除十分之一空间
        qint64 iDelSize = 0;
        qint64 iNeedSize = iDirSize - iMaxSize + iMaxSize / 10.0;
        for(auto it=strFileMap.begin(); it!=strFileMap.end(); it++)
        {
            if(iDelSize >= iNeedSize)
                break;

            QString strFilePath = it.value();
            QFileInfo qFileInfo(strFilePath);
            iDelSize += qFileInfo.size();
            qDebug()<<"delete"<<strFilePath;
            QFile::remove(strFilePath);
        }
        System("sync");
        return;
    }
}

/* 每个xlsx大小30k左右
 * 每个PDF大小1M左右
*/
void CDataExport::_Thread2GuardDirSize()
{
    while(m_bGuard)
    {
        static int times = 1;
        if(0 == times % 6)
        {
            //qDebug()<<"定期释放缓存:";
            //System("sync");
            //System("echo 3 > /proc/sys/vm/drop_caches");
        }
        times++;

        qint64 iMSize = 1024 * 1024 * 1024;

        _GuardDirSize(CPublicConfig::GetInstance()->GetLogDirList().first(), 1 * iMSize);
        _GuardDirSize(CPublicConfig::GetInstance()->GetXlsxDir(), 0.5 * iMSize);
        _GuardDirSize(CPublicConfig::GetInstance()->GetPdfDir(), 2 * iMSize);

        QThread::sleep(300);
    }
}

void CDataExport::_InitWidget()
{
    int iBtnWidth = 130;
    if(eLanguage_Spanish == gk_iLanguage)
        iBtnWidth = 150;
    else if(eLanguage_German == gk_iLanguage)
        iBtnWidth = 180;
    else if(eLanguage_Italian == gk_iLanguage)
        iBtnWidth = 245;

    int iHeight = 50;
    QString strDate = QDateTime::currentDateTime().toString("yyyy-MM-dd");

    m_pLogBeginDate = new CLabelDate(tr("开始日期:"), strDate);
    m_pLogBeginDate->SetDateFixedSize(120, iHeight);
    m_pLogBeginDate->setProperty("DateType", eStartDateLog);
    connect(m_pLogBeginDate, &CLabelDate::SignalPressEvent, this, &CDataExport::_SlotShowDateWidget);

    m_pLogEndDate = new CLabelDate(tr("结束日期:"), strDate);
    m_pLogEndDate->SetDateFixedSize(120, iHeight);
    m_pLogEndDate->setProperty("DateType", eEndDateLog);
    connect(m_pLogEndDate, &CLabelDate::SignalPressEvent, this, &CDataExport::_SlotShowDateWidget);

    m_pLogExportBtn = new QPushButton(tr("导出日志"));
    m_pLogExportBtn->setFixedSize(iBtnWidth, iHeight);
    connect(m_pLogExportBtn, &QPushButton::clicked, this, &CDataExport::_SlotExportLogBtn);

    m_pLogLabel = new QLabel("0/0");
    m_pLogLabel->setAlignment(Qt::AlignCenter);
    m_pLogLabel->setFixedHeight(iHeight);

    m_pPdfBeginDate = new CLabelDate(tr("开始日期:"), strDate);
    m_pPdfBeginDate->SetDateFixedSize(120, iHeight);
    m_pPdfBeginDate->setProperty("DateType", eStartDatePdf);
    connect(m_pPdfBeginDate, &CLabelDate::SignalPressEvent, this, &CDataExport::_SlotShowDateWidget);

    m_pPdfEndDate = new CLabelDate(tr("结束日期:"), strDate);
    m_pPdfEndDate->SetDateFixedSize(120, iHeight);
    m_pPdfEndDate->setProperty("DateType", eEndDatePdf);
    connect(m_pPdfEndDate, &CLabelDate::SignalPressEvent, this, &CDataExport::_SlotShowDateWidget);

    m_pPdfExportBtn = new QPushButton(tr("导出PDF"));
    m_pPdfExportBtn->setFixedSize(iBtnWidth, iHeight);
    connect(m_pPdfExportBtn, &QPushButton::clicked, this, &CDataExport::_SlotExportPdfBtn);

    m_pPdfLabel = new QLabel("0/0");
    m_pPdfLabel->setAlignment(Qt::AlignCenter);
    m_pPdfLabel->setFixedHeight(iHeight);

    m_pXlsxBeginDate = new CLabelDate(tr("开始日期:"), strDate);
    m_pXlsxBeginDate->SetDateFixedSize(120, iHeight);
    m_pXlsxBeginDate->setProperty("DateType", eStartDateXlsx);
    connect(m_pXlsxBeginDate, &CLabelDate::SignalPressEvent, this, &CDataExport::_SlotShowDateWidget);

    m_pXlsxEndDate = new CLabelDate(tr("结束日期:"), strDate);
    m_pXlsxEndDate->SetDateFixedSize(120, iHeight);
    m_pXlsxEndDate->setProperty("DateType", eEndDateXlsx);
    connect(m_pXlsxEndDate, &CLabelDate::SignalPressEvent, this, &CDataExport::_SlotShowDateWidget);

    m_pXlsxExportBtn = new QPushButton(tr("导出Xlsx"));
    m_pXlsxExportBtn->setFixedSize(iBtnWidth, iHeight);
    connect(m_pXlsxExportBtn, &QPushButton::clicked, this, &CDataExport::_SlotExportXlsxBtn);

    m_pXlsxLabel = new QLabel("0/0");
    m_pXlsxLabel->setAlignment(Qt::AlignCenter);
    m_pXlsxLabel->setFixedHeight(iHeight);

    m_pCDateWidget = new CDateTime(this);
    connect(m_pCDateWidget, &CDateTime::SignalConfirmDate, this, &CDataExport::_SlotConfirmDate);
    m_pCDateWidget->setVisible(false);
}

void CDataExport::_InitLayout()
{
    QHBoxLayout *pLogLayout = new QHBoxLayout;
    pLogLayout->setMargin(0);
    pLogLayout->setSpacing(20);
    pLogLayout->addWidget(m_pLogBeginDate);
    pLogLayout->addWidget(m_pLogEndDate);
    pLogLayout->addWidget(m_pLogExportBtn);
    pLogLayout->addWidget(m_pLogLabel);
    pLogLayout->addStretch(1);

    QHBoxLayout *pPdfLayout = new QHBoxLayout;
    pPdfLayout->setMargin(0);
    pPdfLayout->setSpacing(20);
    pPdfLayout->addWidget(m_pPdfBeginDate);
    pPdfLayout->addWidget(m_pPdfEndDate);
    pPdfLayout->addWidget(m_pPdfExportBtn);
    pPdfLayout->addWidget(m_pPdfLabel);
    pPdfLayout->addStretch(1);

    QHBoxLayout *pXlsxLayout = new QHBoxLayout;
    pXlsxLayout->setMargin(0);
    pXlsxLayout->setSpacing(20);
    pXlsxLayout->addWidget(m_pXlsxBeginDate);
    pXlsxLayout->addWidget(m_pXlsxEndDate);
    pXlsxLayout->addWidget(m_pXlsxExportBtn);
    pXlsxLayout->addWidget(m_pXlsxLabel);
    pXlsxLayout->addStretch(1);

    QVBoxLayout *pMainLayout = new QVBoxLayout;
    pMainLayout->setMargin(10);
    pMainLayout->setSpacing(15);
    pMainLayout->addLayout(pLogLayout);
    pMainLayout->addLayout(pPdfLayout);
    pMainLayout->addLayout(pXlsxLayout);
    pMainLayout->addStretch(1);
    this->setLayout(pMainLayout);
}
