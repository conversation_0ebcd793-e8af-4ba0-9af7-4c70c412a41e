﻿#include "CNetworkWidget.h"
#include <QBoxLayout>
#include "PublicFunction.h"

CNetworkWidget::CNetworkWidget(QWidget *parent) : QWidget(parent)
{
    _InitWidget();
    _InitLayout();

    LoadQSS(this, ":/qss/qss/system/network.qss");
}

void CNetworkWidget::_InitWidget()
{
    m_pCSysTtileLabelWidget = new CSysFirstTitleWidget(tr("系统设置"), tr("网络设置"));
    connect(m_pCSysTtileLabelWidget, &CSysFirstTitleWidget::SignalTitlePress, this, &CNetworkWidget::SignalReturn);

    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(1684, 904);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");

    m_pEth1Widget = new CEth1Widget;
    m_pLisWidget = new CLisWidget;
    m_pWiFiWidget = new CWiFiWidget(this);

    connect(m_pWiFiWidget, &CWiFiWidget::SignalWiFiOpen, m_pEth1Widget, &CEth1Widget::SlotWiFiOpen);
    connect(m_pWiFiWidget, &CWiFiWidget::SignalWiFiIPInfoList, m_pEth1Widget, &CEth1Widget::SlotWlanIPInfoList);

    m_pVLabel = new QLabel;
    m_pVLabel->setFixedSize(1, 784);
    m_pVLabel->setObjectName("LineLabel");

    m_pReturnBtn = new QPushButton(tr("返回"));
    m_pReturnBtn->setFixedSize(150, 56);
    m_pReturnBtn->setObjectName("CancelBtn");
    connect(m_pReturnBtn, &QPushButton::clicked, this, &CNetworkWidget::SignalReturn);
}

void CNetworkWidget::_InitLayout()
{
    QVBoxLayout *pLeftLayout = new QVBoxLayout;
    pLeftLayout->setMargin(0);
    pLeftLayout->setSpacing(0);
    pLeftLayout->addWidget(m_pEth1Widget);
    pLeftLayout->addSpacing(45);
    pLeftLayout->addWidget(m_pLisWidget);

    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setContentsMargins(24, 16, 24, 0);
    pTopLayout->addLayout(pLeftLayout);
    pTopLayout->addStretch(1);
    pTopLayout->addWidget(m_pVLabel);
    pTopLayout->addSpacing(24);
    pTopLayout->addWidget(m_pWiFiWidget);

    QVBoxLayout *pBackLayout = new QVBoxLayout;
    pBackLayout->setMargin(0);
    pBackLayout->setSpacing(0);
    pBackLayout->addLayout(pTopLayout);
    pBackLayout->addStretch(1);
    pBackLayout->addWidget(m_pReturnBtn, 0, Qt::AlignHCenter);
    pBackLayout->addSpacing(20);
    m_pBackgroundLabel->setLayout(pBackLayout);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pCSysTtileLabelWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pBackgroundLabel);
    this->setLayout(pLayout);
}
