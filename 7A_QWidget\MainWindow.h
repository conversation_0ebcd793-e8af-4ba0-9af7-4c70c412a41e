#ifndef MAINWINDOW_H
#define MAINWINDOW_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-10-26
  * Description: 主窗口
  * -------------------------------------------------------------------------
  * History: 由调试软件改为功能机软件主界面 20240614
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QMainWindow>
#include <QQuickWidget>
#include <QStackedWidget>

#include "CMdxMainWidget.h"
#include "LoginPage/CLoginWidget.h"

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    //virtual void resizeEvent(QResizeEvent *pEvent) override;
    //virtual void moveEvent(QMoveEvent *pEvent) override;
    virtual void closeEvent(QCloseEvent *pEvent) override;
    virtual void showEvent(QShowEvent *pEvent) override;

private slots:
    void _SlotLoginOK();
    void _SlotLogout();

private slots:
    void _SlotHideKeyboard();
    void _SlotShowQuickWidget();
    void _SlotSetKeyboardPosition(int x, int y, int height, QLineEdit* pLineEdit);
    void _SlotSetKeyboardParentWidget(QWidget* pWidget);

private:
    void _InitControl();
    void _InitWidget();
    void _InitKeyboard();

private:
    Ui::MainWindow *ui;

    QQuickWidget *m_qmlInputPanel;
    CLoginWidget *m_pCLoginWidget;
    CMdxMainWidget *m_pCMdxMainWidget;
    QStackedWidget *m_pStackedWidget;
};
#endif // MAINWINDOW_H
