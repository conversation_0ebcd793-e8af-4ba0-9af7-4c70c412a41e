#ifndef CRUNLOG_H
#define CRUNLOG_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-24
  * Description: 运行日志
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QPushButton>
#include <QTextBrowser>

class CRunLog : public QWidget
{
    Q_OBJECT
public:
    explicit CRunLog(QWidget *parent = nullptr);

public slots:
    void SlotSaveRunLog(const QString &strLog);

private:
    void _InitWidget();
    void _InitLayout();

private:
    QTextBrowser *m_pTextBrowser;
    QPushButton *m_pClearBtn;
};

#endif // CRUNLOG_H
