#include "CPyrolysisCurve.h"
#include <QBoxLayout>

#include "CMessageBox.h"
#include "qcustomplot.h"
#include "PublicFunction.h"
#include "CReadWriteXlsxThread.h"
#include "CRunTest.h"

CPyrolysisCurve::CPyrolysisCurve(QWidget *parent)
    : QWidget(parent)
    , m_bShow(false)
{
    //启动加热 重新绘制温度曲线
    // ht_info 接受ht温度曲线
    Register2Map(Method_ht_info);
    Register2Map(Method_HTST);
    Register2Map(Method_start);

    for(int i=0; i<gk_iMachineCount; i++)
    {
        m_sCurveUiList.push_back(new SHrmCurveStruct);
    }

    _InitWidget();

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalTimingTestStart, this, &CPyrolysisCurve::SlotClearData);
}

CPyrolysisCurve::~CPyrolysisCurve()
{
    UnRegister2Map(Method_ht_info);
    UnRegister2Map(Method_HTST);
    UnRegister2Map(Method_start);

    for(int i=0; i<m_sCurveUiList.size(); i++)
    {
        SHrmCurveStruct *pStruct = m_sCurveUiList.at(i);
        delete pStruct;
        pStruct = nullptr;
    }
    m_sCurveUiList.clear();
}

void CPyrolysisCurve::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    Q_UNUSED(iResult);
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if(Method_ht_info == iMethodID)
    {
        _HandleHTInfo(iMachineID, qVarData);
    }
    else if(Method_HTST == iMethodID)
    {
        _HandelHTSTResult(iMachineID);
    }
    else if(Method_start == iMethodID)
    {
        _TestEnd(iMachineID);
    }
}

void CPyrolysisCurve::SlotClearData(int iMachineID)
{
    qDebug()<<QString("时序开始,清空%1#Pyrolysis曲线").arg(iMachineID + 1);
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    SHrmCurveStruct *pStruct = m_sCurveUiList[iMachineID];
    pStruct->Clear();

    if(m_pMachineComboBox->GetCurrentIndex() == iMachineID)
    {
        m_pCPItemText->setText("Module1Temp:\nModule2Temp:\nModule3Temp:\nModule4Temp:");
        QVector<double> x, y;
        m_pCustomPlot->graph(0)->setData(x, y);
        m_pCustomPlot->graph(1)->setData(x, y);
        m_pCustomPlot->graph(2)->setData(x, y);
        m_pCustomPlot->graph(3)->setData(x, y);
        m_pCustomPlot->replot();
    }
}

void CPyrolysisCurve::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;

    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    SHrmCurveStruct *pStruct = m_sCurveUiList.at(iMachineID);
    if(pStruct->bReplot)
    {
        pStruct->bReplot = false;
        _UpdateMachinePlot(iMachineID);
    }

    QWidget::showEvent(pEvent);
}

void CPyrolysisCurve::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;
    QWidget::hideEvent(pEvent);
}

void CPyrolysisCurve::_SlotExportBtn()
{
    QString strExportDir = CPublicConfig::GetInstance()->GetUDiskExportDir();
    if(!UDiskExistAndCreateDir(strExportDir, this))
        return;

    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    QString strCurrentTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    QString strXlsxName = QString("LYSIS_%1#_%2.xlsx").arg(iMachineID + 1).arg(strCurrentTime);

    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = strXlsxName;
    pXlsxStruct->strTableName = "LYSIS";

    QStringList &strTitleList = pXlsxStruct->strTitleList;
    strTitleList << "time" << "Module1Temp" << "Module2Temp"<< "Module3Temp" << "Module4Temp" ;

    SHrmCurveStruct *pStruct = m_sCurveUiList[iMachineID];
    if(nullptr == pStruct)
    {
        delete pXlsxStruct;
        pXlsxStruct = nullptr;
        return;
    }
    QList<QVariantList> &varDataList = pXlsxStruct->varWriteDataList;
    for(int i=0; i<pStruct->dTimeVec.size(); i++)
    {
        QVariantList oneList;
        oneList << pStruct->dTimeVec.at(i);
        for(const auto& item : pStruct->TempVecMap)
        {
            oneList << item.at(i);
        }
        varDataList<<oneList;
    }

    int iSize = pStruct->dTimeVec.size();
    ChartNoteStruct hrmChart;
    hrmChart.iRow = 5;
    hrmChart.iColumn = 6;
    hrmChart.strChartTitle = "LYSIS";
    hrmChart.strXTitle = "x";
    hrmChart.strYTitle = "温度 (℃)";
    hrmChart.strSerialNameList<<"Module1Temp"<<"Module2Temp"<< "Module3Temp" << "Module4Temp";
    hrmChart.strXDataRange = QString("%1!$A$2:$A$%2").arg(pXlsxStruct->strTableName).arg(iSize+1);
    hrmChart.strNumDataRange = QString("B2:E%1").arg(iSize+1);
    hrmChart.bMajorGridlines = false;
    hrmChart.strMarkSymbolList<<"none"<<"none"<<"none"<<"none";
    hrmChart.strSerialColorList<<HEX_COLOR_B<<HEX_COLOR_R<<HEX_COLOR_G<<HEX_COLOR_Y;

    // pXlsxStruct->chartNoteList<<hrmChart;

    FunWriteXlsxEndCallBack lambdaFunction = [this](QString strXlsxName,QString strTableName)
    {
        Q_UNUSED(strTableName);
        qDebug()<<strXlsxName<<"文件导出完成";
        QString strDestPath = CPublicConfig::GetInstance()->GetUDiskExportDir() + strXlsxName;
        CopyQFileDir(strXlsxName, QDir(strDestPath));
        System("sync");
        ShowInformation(this, tr("提示"), tr("文件导出完成"));
    };

    pXlsxStruct->WriteEndCallBack = lambdaFunction;

    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
}

void CPyrolysisCurve::_SlotClearBtn()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    SlotClearData(iMachineID);
}

void CPyrolysisCurve::_SlotSetXYRange(const QStringList &strRangeList)
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    SHrmCurveStruct *pStruct = m_sCurveUiList[iMachineID];
    pStruct->strRangeList = strRangeList;
    m_pCustomPlot->xAxis->setRange(strRangeList.at(0).toDouble(), strRangeList.at(1).toDouble());
    m_pCustomPlot->yAxis->setRange(strRangeList.at(2).toDouble(), strRangeList.at(3).toDouble());
    m_pCustomPlot->replot();
}

void CPyrolysisCurve::_SlotMachineChange(int iMachineID)
{
    QStringList strXYRangList = m_sCurveUiList.at(iMachineID)->strRangeList;
    m_pCSetChartXYRange->SetRange(strXYRangList);
    m_pCustomPlot->xAxis->setRange(strXYRangList.at(0).toDouble(), strXYRangList.at(1).toDouble());
    m_pCustomPlot->yAxis->setRange(strXYRangList.at(2).toDouble(), strXYRangList.at(3).toDouble());

    _UpdateMachinePlot(iMachineID);
}

void CPyrolysisCurve::_HandleHTInfo(int iMachineID, const QVariant &qVarData)
{
    QString strHtInfoString = qVarData.toString();
    QStringList strHtInfoList = strHtInfoString.split(SPLIT_BETWEEN_CMD);
    int size = strHtInfoList.size();
    qDebug()<<QString("测试日志,%1#Pyrolysis曲线数据").arg(iMachineID + 1)<<strHtInfoString;
    if(size < 4)
    {
        return;
    }

    SHrmCurveStruct *pStruct = m_sCurveUiList[iMachineID];
    if(nullptr == pStruct)
    {
        return;
    }
    if(pStruct->dTimeVec.isEmpty())
    {
        pStruct->Clear();
        pStruct->qBeginTime = QDateTime::currentDateTime();
        pStruct->dTimeVec.push_back(0);
    }
    else
    {
        int iTime = pStruct->qBeginTime.msecsTo(QDateTime::currentDateTime());
        pStruct->dTimeVec.push_back(iTime);
    }

    // 获取第一个模块温度
    QString strCPItemText;
    for(int i = 0 ; i < size ;i++)
    {
        QStringList strModelTempList = strHtInfoList.at(i).split(SPLIT_IN_CMD);
        double dMTemp = strModelTempList.at(1).toDouble() / 100.0;
        pStruct->TempVecMap[i].push_back(dMTemp);

        if(i == size -1)
        {
            strCPItemText.append( QString("Module%1Temp:%2").arg(i + 1).arg(dMTemp));
        }
        else
        {
            strCPItemText.append( QString("Module%1Temp:%2\n").arg(i + 1).arg(dMTemp));
        }
    }

    pStruct->strCPItemText = strCPItemText;

    pStruct->bReplot = true;
    if(m_bShow)
    {
        if(m_pMachineComboBox->GetCurrentIndex() == iMachineID)
        {
            pStruct->bReplot = false;
            _UpdateMachinePlot(iMachineID);
        }
    }
}

void CPyrolysisCurve::_UpdateMachinePlot(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    SHrmCurveStruct *pStruct = m_sCurveUiList.at(iMachineID);
    for(auto it=pStruct->TempVecMap.begin(); it!=pStruct->TempVecMap.end(); it++)
    {
        int index = it.key();
        QVector<double> dTempVec = it.value();
        QVector<double> dXVec;
        for(int i=0; i<dTempVec.size(); i++)
            dXVec.push_back(i);

        if(m_pCustomPlot->graph(index))
            m_pCustomPlot->graph(index)->setData(dXVec, dTempVec);
    }
    m_pCPItemText->setText(pStruct->strCPItemText);
    m_pCustomPlot->replot();
}

void CPyrolysisCurve::_HandelHTSTResult(int iMachineID)
{
    SlotClearData(iMachineID);
}

void CPyrolysisCurve::_TestEnd(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    SHrmCurveStruct *pDataStruct = m_sCurveUiList.at(iMachineID);

    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = CPublicConfig::GetInstance()->GetTestXlsxName(iMachineID);;
    pXlsxStruct->strTableName = "lysis";
    pXlsxStruct->bDrawChart = true;
    pXlsxStruct->strTitleList << "time" << "Module1Temp" << "Module2Temp" << "Module3Temp" << "Module4Temp";

    QVector<double> dTemp0Vec = pDataStruct->TempVecMap.value(0);
    QVector<double> dTemp1Vec = pDataStruct->TempVecMap.value(1);
    QVector<double> dTemp2Vec = pDataStruct->TempVecMap.value(2);
    QVector<double> dTemp3Vec = pDataStruct->TempVecMap.value(3);

    int iDataSize = dTemp0Vec.size();
    QVector<double> dXVec;
    for(int i=0; i<iDataSize; i++)
        dXVec.push_back(i);

    for(int i=0; i<iDataSize; i++)
    {
        QVariantList qRowList;
        qRowList << dXVec.at(i);

        qRowList << (i < dTemp0Vec.size() ? dTemp0Vec.at(i) : 0);
        qRowList << (i < dTemp1Vec.size() ? dTemp1Vec.at(i) : 0);
        qRowList << (i < dTemp2Vec.size() ? dTemp2Vec.at(i) : 0);
        qRowList << (i < dTemp3Vec.size() ? dTemp3Vec.at(i) : 0);

        pXlsxStruct->varWriteDataList << qRowList;
    }

    ChartNoteStruct chart;
    chart.iRow = 4;
    chart.iColumn = 7;
    chart.strChartTitle = "lysis data";
    chart.strXTitle = "time (s)";
    chart.strYTitle = "temp (℃)";
    chart.strSerialNameList << "Module1Temp" << "Module2Temp" << "Module3Temp" << "Module4Temp";
    chart.strSerialColorList<<HEX_COLOR_B<<HEX_COLOR_G<<HEX_COLOR_Y<<HEX_COLOR_R;
    chart.strXDataRange = QString("%1!$A$2:$A$%2").arg(pXlsxStruct->strTableName).arg(iDataSize + 1);
    chart.strNumDataRange = QString("B2:E%1").arg(iDataSize + 1);
    chart.bMajorGridlines = false;
    chart.strMarkSymbolList << "none" << "none" << "none" << "none";

    pXlsxStruct->chartNoteList << chart;
    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
    qDebug()<<QString("%1# lysis data write to result xlsx end").arg(iMachineID + 1);
}

void CPyrolysisCurve::_InitWidget()
{
    m_pCustomPlot = _InitCustomPlot();
    m_pCPItemText = _InitCPItemText(m_pCustomPlot);

    int iBtnWidth = 100;
    if(eLanguage_German == gk_iLanguage)
        iBtnWidth = 140;

    int iHeight = 50;
    m_pExportBtn = new QPushButton(tr("导出"));
    m_pExportBtn->setFixedSize(iBtnWidth, iHeight);
    connect(m_pExportBtn, &QPushButton::clicked, this, &CPyrolysisCurve::_SlotExportBtn);

    m_pClearBtn = new QPushButton(tr("清空"));
    m_pClearBtn->setFixedSize(iBtnWidth, iHeight);
    connect(m_pClearBtn, &QPushButton::clicked, this, &CPyrolysisCurve::_SlotClearBtn);

    m_pCSetChartXYRange = new CSetChartXYRange(m_sCurveUiList.at(0)->strRangeList);
    m_pCSetChartXYRange->SetLineEditTextAlignment();
    connect(m_pCSetChartXYRange, &CSetChartXYRange::SignalSetRange, this, &CPyrolysisCurve::_SlotSetXYRange);

    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(70, iHeight);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMachineChange(int)));

    QHBoxLayout *pHLayout = new QHBoxLayout;
    pHLayout->setMargin(0);
    pHLayout->setSpacing(10);
    pHLayout->addWidget(m_pExportBtn);
    pHLayout->addWidget(m_pClearBtn);
    pHLayout->addSpacing(10);
    pHLayout->addWidget(m_pCSetChartXYRange);
    pHLayout->addStretch(1);
    pHLayout->addWidget(m_pMachineComboBox);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addWidget(m_pCustomPlot);
    pLayout->addSpacing(10);
    pLayout->addLayout(pHLayout);
    this->setLayout(pLayout);
}

QCustomPlot *CPyrolysisCurve::_InitCustomPlot()
{
    QCustomPlot *pCustomPlot = new QCustomPlot(this);
    pCustomPlot->setFixedSize(1494, 740);

    QFont font;
    font.setPointSize(12);
    pCustomPlot->legend->setFont(font);
    pCustomPlot->legend->setSelectedFont(font);
    pCustomPlot->legend->setVisible(true);
    pCustomPlot->legend->setSelectableParts(QCPLegend::spItems);
    pCustomPlot->legend->setBorderPen(Qt::NoPen);
    pCustomPlot->legend->setWrap(1);
    pCustomPlot->axisRect()->insetLayout()->setInsetAlignment(0, Qt::AlignTop);

    _AddGraph(pCustomPlot, Qt::blue,  Qt::blue,  0, "Module1Temp");
    _AddGraph(pCustomPlot, Qt::red,   Qt::red,   1, "Module2Temp");
    _AddGraph(pCustomPlot, Qt::green,   Qt::green,   2, "Module3Temp");
    _AddGraph(pCustomPlot, Qt::yellow,   Qt::yellow,   3, "Module4Temp");

    pCustomPlot->xAxis->setLabel("X");
    pCustomPlot->yAxis->setLabel(tr("温度 (℃)"));
    pCustomPlot->xAxis->setLabelFont(font);
    pCustomPlot->yAxis->setLabelFont(font);

    pCustomPlot->xAxis->setRange(0, 150);
    pCustomPlot->yAxis->setRange(0, 110);

    return pCustomPlot;
}

QCPItemText *CPyrolysisCurve::_InitCPItemText(QCustomPlot *pCustomPlot)
{
    QCPItemText *pCPItemText = new QCPItemText(pCustomPlot);
    pCPItemText->setTextAlignment(Qt::AlignCenter);

    pCPItemText->setFont(QFont(font().family(), 12));
    pCPItemText->setPen(QPen(Qt::black));
    pCPItemText->setBrush(QBrush(QColor("#a1ffa1")));
    pCPItemText->setPadding(QMargins(5, 5, 5, 5));

    QString strText = "Module1Temp:\nModule2Temp:\nModule3Temp:\nModule4Temp:";
    pCPItemText->setText(strText);
    pCPItemText->setVisible(true);
    pCPItemText->position->setType(QCPItemPosition::ptAxisRectRatio);
    pCPItemText->position->setCoords(0.9, 0.1);

    return pCPItemText;
}

void CPyrolysisCurve::_AddGraph(QCustomPlot *pCustomPlot, QColor penColor, QColor pointColor, int iChart, QString strChartName)
{
    QPen pen;
    pen.setWidth(2);
    pen.setColor(penColor);
    pCustomPlot->addGraph();
    pCustomPlot->graph(iChart)->setPen(pen);
    pCustomPlot->graph(iChart)->setName(strChartName);
    pCustomPlot->graph(iChart)->setAntialiasedFill(true);
    pCustomPlot->graph(iChart)->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssNone,
                                                                QPen(pointColor, 1),
                                                                QBrush(pointColor), 1));
}
