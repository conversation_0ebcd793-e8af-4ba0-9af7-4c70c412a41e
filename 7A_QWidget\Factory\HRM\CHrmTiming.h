#ifndef CHRMTIMING_H
#define CHRMTIMING_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-11-14
  * Description: HRM-时序
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QPushButton>
#include <QTableWidget>
#include <QStackedWidget>

#include "CCmdBase.h"
#include "CLineEdit.h"
#include "CLabelComboBox.h"

class CHrmTiming : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CHrmTiming(QWidget *parent = nullptr);
    ~CHrmTiming();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

public slots:
    void SlotSendTecByName(int iMachineID, const QString &strName);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual void hideEvent(QHideEvent *pEvent) override;
    virtual bool eventFilter(QObject *pObject, QEvent *pEvent) override;

signals:
    void SignalStartPCR(int);

private slots:
    void _SlotAddRowBtn();
    void _SlotSubRowBtn();
    void _SlotMachineChange(int iMachineID);
    void _SlotSaveBtn();
    void _SlotLoadBtn();
    void _SlotDeleteBtn();
    void _SlotClearBtn();
    void _SlotSendBtn();
    void _SlotStartBtn();
    void _SlotStopBtn();
    void _SlotComboBoxChanged(int index);

private:
    void _LoadName2Table();
    void _LoadData2Table(int iBegin, int iEnd);
    void _AddOneCmdLine(QTableWidget *pTableWidget, int iRow, const QStringList &strOneList);
    void _DeleteCmdTableWidget(int iMachineID);    
    QStringList _GetCmdTableDataList(int iMachineID);
    void _StartSendTec(int iMachineID, const QString &strTecData);
    void _SendTecData2LowMachine(int iMachineID);

private:
    void _InitWidget();
    QTableWidget *_CreateCmdTableWidget();
    void _InitTableWidget();

private:
    typedef struct _STecTimingStruct
    {
        _STecTimingStruct()
        {
            pCmdTable = nullptr;
            iPageCount = 12;
        }
        QTableWidget *pCmdTable;
        int iPageCount;
        QStringList strTecDataList; // tec下发数据, list[0]是下拉index+1,db保存的是下拉string text
    }STecTimingStruct;

    QStackedWidget *m_pStackedWidget;
    QList<STecTimingStruct *> m_sTimingUiList;
    QPushButton *m_pAddRowBtn, *m_pSubRowBtn;
    CLabelComboBox *m_pMachineComboBox;

    QTableWidget *m_pFileTableWidget;
    CLineEdit *m_pSaveLineEdit;
    QPushButton *m_pSaveBtn;
    QPushButton *m_pLoadBtn, *m_pDeleteBtn;
    QPushButton *m_pClearBtn, *m_pSendBtn;
    QPushButton *m_pStartBtn, *m_pStopBtn;

    QStringList m_strTecNameList;
    QStringList m_strCmdTextList;

    bool m_bShow;
    int m_iUiMachineID;    
};

#endif // CHRMTIMING_H
