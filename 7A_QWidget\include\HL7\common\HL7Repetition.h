﻿#ifndef _HL7_REPETITION_H_
#define _HL7_REPETITION_H_
#include "HL7ComponentCollection.h"
#include <string>
#include "../interface/IObjectBase.h"
class HL7Repetition
{
public:
	HL7Repetition();

	~HL7Repetition();

	/*
	*@brief 返回成分集合
	*/
	HL7ComponentCollection GetHL7ComponentCollection();

	/*
	*@brief 返回重复段字符串
	*/
	std::string GetRepetitionString();

	/*
	*@brief 构建重复段，输入参数为按顺序排列的成分集合
	*/
	void Build(HL7ComponentCollection componentCollection);

	/*
	*@brief 分解重复段为成分
	*/
	void Parse(const char* repetitionStr, EncodingCharacters encodingChars);

private:
	HL7ComponentCollection m_componentCollection;

	std::string m_repetitionStr;

	std::string m_componentSeparator;
};

#endif
