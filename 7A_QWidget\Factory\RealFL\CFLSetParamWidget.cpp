#include "CFLSetParamWidget.h"
#include <QDebug>
#include <QPainter>
#include <QBoxLayout>
#include <QGridLayout>
#include <QHeaderView>

#include "CLotInfoDB.h"
#include "PublicParams.h"
#include "PublicConfig.h"
#include "PublicFunction.h"

CFLSetParamWidget::CFLSetParamWidget(QWidget *parent) : QWidget(parent)
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setFixedSize(G_QRootSize);
    this->move(G_QRootPoint);
    this->setAttribute(Qt::WA_TranslucentBackground);


    _InitWidget();
    _InitLayout();
    _SlotResetBtn();
    LoadQSS(this, ":/qss/qss/factory/flsetparamwidget.qss");
}

void CFLSetParamWidget::setParamText(const QList<float> &qFList)
{
    if( qFList.size() < 8 )
    {
        return;
    }
    m_pHole1B->SetLineEditText(QString("%1").arg(qFList.at(0)));
    m_pHole2B->SetLineEditText(QString("%1").arg(qFList.at(4)));

    m_pHole1G->SetLineEditText(QString("%1").arg(qFList.at(1)));
    m_pHole2G->SetLineEditText(QString("%1").arg(qFList.at(5)));

    m_pHole1Y->SetLineEditText(QString("%1").arg(qFList.at(2)));
    m_pHole2Y->SetLineEditText(QString("%1").arg(qFList.at(6)));

    m_pHole1R->SetLineEditText(QString("%1").arg(qFList.at(3)));
    m_pHole2R->SetLineEditText(QString("%1").arg(qFList.at(7)));
}


void CFLSetParamWidget::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, 30));
    QWidget::paintEvent(pEvent);
}

void CFLSetParamWidget::showEvent(QShowEvent *pEvent)
{
    //QStringList strProjectList = CLotInfoDB::GetInstance()->GetAllProjectShowName();
    //strProjectList.push_front("");
    //m_pProjetcWidget->SetComboBoxList(strProjectList);

    QWidget::showEvent(pEvent);
}

void CFLSetParamWidget::_SlotCancelBtn()
{
    this->close();
}

void CFLSetParamWidget::_SlotConfirmBtn()
{
    this->close();
    _ClearData();

    // 获取矩阵输出
    if(0 == m_pStackWidget->currentIndex())
    {
        for (int row = 0; row < m_FirstTableWidget->rowCount(); ++row) {
            for (int col = 0; col < m_FirstTableWidget->columnCount(); ++col) {
                QLineEdit *lineEdit = qobject_cast<QLineEdit*>(m_FirstTableWidget->cellWidget(row, col));
                if (lineEdit) {
                    m_pfList.append(lineEdit->text().toFloat());
                }
            }
        }
        for (int row = 0; row < m_SecondTableWidget->rowCount(); ++row) {
            for (int col = 0; col < m_SecondTableWidget->columnCount(); ++col) {
                QLineEdit *lineEdit = qobject_cast<QLineEdit*>(m_SecondTableWidget->cellWidget(row, col));
                if (lineEdit) {
                    m_pfList.append(lineEdit->text().toFloat());
                }
            }
        }
        emit SignalFLCrossSetParamConfirm(m_pfList);
    }
    else if(1 == m_pStackWidget->currentIndex())
    {

        m_pfStandardList.push_back(m_pHole1B->GetLineEditText().toFloat());
        m_pfStandardList.push_back(m_pHole1G->GetLineEditText().toFloat());
        m_pfStandardList.push_back(m_pHole1Y->GetLineEditText().toFloat());
        m_pfStandardList.push_back(m_pHole1R->GetLineEditText().toFloat());

        m_pfStandardList.push_back(m_pHole2B->GetLineEditText().toFloat());
        m_pfStandardList.push_back(m_pHole2G->GetLineEditText().toFloat());
        m_pfStandardList.push_back(m_pHole2Y->GetLineEditText().toFloat());
        m_pfStandardList.push_back(m_pHole2R->GetLineEditText().toFloat());
        emit SignalStandardSetParamConfirm(m_pfStandardList);
    }



}

void CFLSetParamWidget::_SlotResetBtn()
{
    // 矩阵参数设置
    if(0 == m_pStackWidget->currentIndex())
    {
        for (int row = 0; row < m_FirstTableWidget->rowCount(); ++row) {
            for (int col = 0; col < m_FirstTableWidget->columnCount(); ++col) {
                QLineEdit *lineEdit = qobject_cast<QLineEdit*>(m_FirstTableWidget->cellWidget(row, col));
                if (lineEdit) {
                    lineEdit->setText("0.00");
                }
            }
        }
        for (int row = 0; row < m_SecondTableWidget->rowCount(); ++row) {
            for (int col = 0; col < m_SecondTableWidget->columnCount(); ++col) {
                QLineEdit *lineEdit = qobject_cast<QLineEdit*>(m_SecondTableWidget->cellWidget(row, col));
                if (lineEdit) {
                    lineEdit->setText("0.00");
                }
            }
        }
    }
    if(1 == m_pStackWidget->currentIndex())
    {
        m_pHole1B->SetLineEditText("0");
        m_pHole1G->SetLineEditText("0");
        m_pHole1Y->SetLineEditText("0");
        m_pHole1R->SetLineEditText("0");

        m_pHole2B->SetLineEditText("0");
        m_pHole2G->SetLineEditText("0");
        m_pHole2Y->SetLineEditText("0");
        m_pHole2R->SetLineEditText("0");

    }
}

void CFLSetParamWidget::_SlotFlCrossBtn()
{
    m_pStackWidget->setCurrentIndex(0);
}

void CFLSetParamWidget::_SlotFlStadardBtn()
{
    m_pStackWidget->setCurrentIndex(1);
}

void CFLSetParamWidget::_ClearData()
{
    m_pfList.clear();
    m_pfStandardList.clear();
}

void CFLSetParamWidget::_InitTabBtnWidget()
{
    m_pTopTabBackgroundLabel = new QLabel;
    m_pTopTabBackgroundLabel->setFixedSize(330, 40);
    m_pTopTabBackgroundLabel->setObjectName("TopBackgroundLabel");

    m_pFLCrossBtn = new QPushButton(tr("交叉干扰系数"));
    m_pFLCrossBtn->setFixedSize(156,40);
    m_pFLCrossBtn->setObjectName("TopBtn");

    m_pFLStandardBtn = new QPushButton(tr("标准化系数"));
    m_pFLStandardBtn->setFixedSize(156,40);
    m_pFLStandardBtn->setObjectName("TopBtn");

    connect(m_pFLCrossBtn,&QPushButton::clicked,this,&CFLSetParamWidget::_SlotFlCrossBtn);
    connect(m_pFLStandardBtn,&QPushButton::clicked,this,&CFLSetParamWidget::_SlotFlStadardBtn);


    QHBoxLayout *pTabButtonLayout = new QHBoxLayout;
    pTabButtonLayout->setMargin(0);
    pTabButtonLayout->setSpacing(0);
    pTabButtonLayout->addStretch(1);
    pTabButtonLayout->addWidget(m_pFLCrossBtn);
    pTabButtonLayout->addWidget(m_pFLStandardBtn);
    pTabButtonLayout->addStretch(1);
    m_pTopTabBackgroundLabel->setLayout(pTabButtonLayout);

}

void CFLSetParamWidget::_InitTableWidget(QTableWidget*& tableWidget,const QStringList& RowHeaders,const  QStringList& ColumnHeaders)
{
    tableWidget = new QTableWidget(4, 4, this);
    tableWidget->setFixedSize(480, 320);
    // 设置表头标签
    tableWidget->setHorizontalHeaderLabels(ColumnHeaders);
    tableWidget->setVerticalHeaderLabels(RowHeaders);
    // 设置表头不可编辑
    QHeaderView* pVerticalHeader = tableWidget->verticalHeader();
    QHeaderView* pHorizontalHeader = tableWidget->horizontalHeader();
    pHorizontalHeader->setSectionsClickable(false);
    pVerticalHeader->setSectionsClickable(false);

    pHorizontalHeader->setSectionResizeMode(QHeaderView::Stretch);
    pVerticalHeader->setSectionResizeMode(QHeaderView::Stretch);

    tableWidget->setShowGrid(false);
    // 定义样式表
#if   1
    QString styleSheet = R"(
                         QTableWidget
                         {
                         color: #353E4E;
                         font-size: 20px;
                         font-family: "Source Han Sans CN";
                         selection-background-color: #60C8FF;
                         border: 1px solid #D6DFE9;
                         border-radius: 0px;
                         background-color: #FFFF00;
                         }
                         QTableWidget::item
                         {
                         height: 56px; /* 表格单元格高度 */
                         width: 100px; /* 表格单元格宽度 */
                         }
                         QHeaderView::section
                         {
                         color: #353E4E;
                         font-size: 20px;
                         font-weight: 500;
                         font-family: "Source Han Sans CN";
                         border: 0px solid #D6DFE9;
                         border-radius: 0px;
                         height: 56px;
                         }

                         QHeaderView::horizontal::section
                         {
                         background-color: #FFFF00;
                         max-width: 480px; /* 水平表头最大宽度 */
                         height: 56px; /* 水平表头高度 */
                         }
                         QHeaderView::vertical::section
                         {
                         background-color: #FFFF00;
                         min-width: 56px;  /* 竖直表头最小宽度 */
                         max-width: 480px; /* 竖直表头最大宽度 */
                         height: 56px; /* 竖直表头高度 */
                         }
                         )";
    // 应用样式表到表格
    tableWidget->setStyleSheet(styleSheet);
#endif 1
    // 插入 QLineEdit 到单元格
    for (int row = 0; row < tableWidget->rowCount(); ++row) {
        for (int col = 0; col < tableWidget->columnCount(); ++col) {
            QLineEdit *lineEdit = new QLineEdit(tableWidget);
            if(row == col)
            {
                lineEdit->setText("0.00");
                lineEdit->setReadOnly(true);
                lineEdit->setStyleSheet("background-color: #f0f0f0;");
            }
            // 定义正则表达式
            QRegularExpression regex("^-?[0-9]+(\\.[0-9]{0,3})?$");
            // 创建正则表达式验证器
            QRegularExpressionValidator *validator = new QRegularExpressionValidator(regex, lineEdit);
            // 将验证器设置到 QLineEdit 上
            lineEdit->setValidator(validator);
            tableWidget->setCellWidget(row, col, lineEdit);
        }
    }
}

void CFLSetParamWidget::_InitStandardWidget()
{
    int iNameWidth = 80;
    int iValueWidth = 120;
    if(eLanguage_English == gk_iLanguage)
    {
        iNameWidth = 80;
        iValueWidth = 120;
    }
    int iHeight = 36;

    m_pHole1B = new CHLabelLineEdit(tr("孔1-B"));
    m_pHole1B->ResetLabelSize(iNameWidth, iHeight);
    m_pHole1B->ResetLineEditSize(iValueWidth, iHeight);

    m_pHole2B = new CHLabelLineEdit(tr("孔2-B"));
    m_pHole2B->ResetLabelSize(iNameWidth, iHeight);
    m_pHole2B->ResetLineEditSize(iValueWidth, iHeight);

    m_pHole1G = new CHLabelLineEdit(tr("孔1-G"));
    m_pHole1G->ResetLabelSize(iNameWidth, iHeight);
    m_pHole1G->ResetLineEditSize(iValueWidth, iHeight);

    m_pHole2G = new CHLabelLineEdit(tr("孔2-G"));
    m_pHole2G->ResetLabelSize(iNameWidth, iHeight);
    m_pHole2G->ResetLineEditSize(iValueWidth, iHeight);

    m_pHole1Y = new CHLabelLineEdit(tr("孔1-Y"));
    m_pHole1Y->ResetLabelSize(iNameWidth, iHeight);
    m_pHole1Y->ResetLineEditSize(iValueWidth, iHeight);

    m_pHole2Y = new CHLabelLineEdit(tr("孔2-Y"));
    m_pHole2Y->ResetLabelSize(iNameWidth, iHeight);
    m_pHole2Y->ResetLineEditSize(iValueWidth,iHeight);

    m_pHole1R = new CHLabelLineEdit(tr("孔1-R"));
    m_pHole1R->ResetLabelSize(iNameWidth, iHeight);
    m_pHole1R->ResetLineEditSize(iValueWidth, iHeight);

    m_pHole2R = new CHLabelLineEdit(tr("孔2-R"));
    m_pHole2R->ResetLabelSize(iNameWidth, iHeight);
    m_pHole2R->ResetLineEditSize(iValueWidth, iHeight);

}

void CFLSetParamWidget::_InitWidget()
{
    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(1080, 580);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");


    m_pCHLabelTitleWidget = new CHLabelTitleWidget(tr("荧光参数设置"));


    _InitTabBtnWidget();
    // 串扰矩阵
    QStringList RowHeaders1 = {"B1", "G1", "Y1", "R1"};
    QStringList ColumnHeaders1 = {"KB1", "KG1", "KY1", "KR1"};
    QStringList Rowheaders2 = {"B2", "G2", "Y2", "R2"};
    QStringList ColumnHeaders2 = {"KB2", "KG2", "KY2", "KR2"};
    _InitTableWidget(m_FirstTableWidget,RowHeaders1,ColumnHeaders1);
    _InitTableWidget(m_SecondTableWidget,Rowheaders2,ColumnHeaders2);

    m_pResetBtn = new QPushButton(tr("重置"));
    m_pResetBtn->setFixedSize(120, 38);
    connect(m_pResetBtn, &QPushButton::clicked, this, &CFLSetParamWidget::_SlotResetBtn);



    // 规范化输入框
    _InitStandardWidget();



    // 底部按钮
    m_pCancelBtn = new QPushButton(tr("取消"));
    m_pCancelBtn->setFixedSize(120, 38);
    m_pCancelBtn->setObjectName("CancelBtn");
    connect(m_pCancelBtn, &QPushButton::clicked, this, &CFLSetParamWidget::_SlotCancelBtn);

    m_pConfirmBtn = new QPushButton(tr("确认"));
    m_pConfirmBtn->setFixedSize(120, 38);
    connect(m_pConfirmBtn, &QPushButton::clicked, this, &CFLSetParamWidget::_SlotConfirmBtn);
}

void CFLSetParamWidget::_InitLayout()
{
    m_pStackWidget = new QStackedWidget();
    QGridLayout *pGridLayout = new QGridLayout;
    pGridLayout->setMargin(0);
    pGridLayout->setSpacing(40);
    pGridLayout->addWidget(m_pHole1B, 0, 0);
    pGridLayout->addWidget(m_pHole2B, 0, 1);
    pGridLayout->addWidget(m_pHole1G, 1, 0);
    pGridLayout->addWidget(m_pHole2G, 1, 1);
    pGridLayout->addWidget(m_pHole1Y, 2, 0);
    pGridLayout->addWidget(m_pHole2Y, 2, 1);
    pGridLayout->addWidget(m_pHole1R, 3, 0);
    pGridLayout->addWidget(m_pHole2R, 3, 1);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(60);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pCancelBtn);
    pBtnLayout->addWidget(m_pConfirmBtn);
    pBtnLayout->addStretch(1);


    QHBoxLayout *pTableLayout = new QHBoxLayout;
    pTableLayout->setMargin(0);
    pTableLayout->setSpacing(10);
    pTableLayout->addStretch(1);
    pTableLayout->addWidget(m_FirstTableWidget, 0, Qt::AlignHCenter);
    pTableLayout->addWidget(m_SecondTableWidget, 0, Qt::AlignHCenter);
    pTableLayout->addStretch(1);

    QWidget * pWidgetFirst = new QWidget;
    pWidgetFirst->setLayout(pTableLayout);
    m_pStackWidget->addWidget(pWidgetFirst);

    QWidget * pWidgetSecond = new QWidget;
    pWidgetSecond->setLayout(pGridLayout);
    m_pStackWidget->addWidget(pWidgetSecond);


    QVBoxLayout *pBackLayout = new QVBoxLayout;
    pBackLayout->setContentsMargins(24, 12, 24, 24);
    pBackLayout->addSpacing(10);
    pBackLayout->addWidget(m_pCHLabelTitleWidget, 0, Qt::AlignLeft);
    pBackLayout->addSpacing(5);
    pBackLayout->addWidget(m_pTopTabBackgroundLabel,0,Qt::AlignLeft);
    pBackLayout->addWidget(m_pStackWidget,0,Qt::AlignLeft);
    pBackLayout->addWidget(m_pResetBtn, 0, Qt::AlignHCenter);
    pBackLayout->addStretch(1);
    pBackLayout->addLayout(pBtnLayout);

    m_pBackgroundLabel->setLayout(pBackLayout);
    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pBackgroundLabel, 0, Qt::AlignCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}
