#ifndef CPyrolysisCalibration_H
#define CPyrolysisCalibration_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: chenhao
  * Date: 2024-07-10
  * Description: CPyrolysis-校准
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include "CCmdBase.h"
#include "CLabelComboBox.h"
#include <QLabel>
#include <QPushButton>
#include "CLineEdit.h"
#include "HRM/CHrmOneCalibrate.h"


class CPyrolysisCalibration : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CPyrolysisCalibration(QWidget *parent = nullptr);
    ~CPyrolysisCalibration();

    void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

private slots:
    void _Slot2RouteBtn();
    void _Slot4RouteBtn();

private:
    void _InitWidget();

private:
    CLysisOneCalibrate *m_pLysisOneCalibrate1;
    CLysisOneCalibrate *m_pLysisOneCalibrate2;
    CLysisOneCalibrate *m_pLysisOneCalibrate3;
    CLysisOneCalibrate *m_pLysisOneCalibrate4;

    QPushButton *m_p2RouteBtn, *m_p4RouteBtn;
    CLabelComboBox *m_pMachineComboBox;
};

#endif // CPyrolysisCalibration_H
