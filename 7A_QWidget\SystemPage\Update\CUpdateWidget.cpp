﻿#include "CUpdateWidget.h"
#include <QDir>
#include <QFile>
#include <QThread>
#include <QProcess>
#include <QBoxLayout>
#include <QApplication>
#include <QJsonObject>
#include <QJsonDocument>
#include <QCryptographicHash>

#include "CRunTest.h"
#include "CHeartBeat.h"
#include "CMessageBox.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "ZipManager/zip.h"
#include "ZipManager/unzip.h"
#include "COperationUnit.h"
#include "CUpdateResultPage.h"
#include "QAes/qaesencryption.h"

static const int G_iUpdateTimeout = 45 * 1000;

CUpdateWidget::CUpdateWidget(QWidget *parent) : QWidget(parent)
{
    m_bShow = false;
    m_bUpdateStart = false;
    m_iUpdateType = eUpdate_FL;
    m_strCurrentDir = QApplication::applicationDirPath() + "/";
    m_strBackupDir = m_strCurrentDir + "bak/";
    m_strAppDir = m_strCurrentDir + "app/";
    m_strLibDir = m_strCurrentDir + "lib/";
    m_strUpdateDir = m_strCurrentDir + "update/";

    CreateDir(m_strBackupDir);
    CreateDir(m_strAppDir);
    CreateDir(m_strLibDir);
    CreateDir(m_strUpdateDir);

    m_strAllVersionPath = m_strCurrentDir + "/Resources/all.txt";
    if(QFile::exists(m_strAllVersionPath))
    {
        ReadFile(m_strAllVersionPath, m_strCurrentVersion);
        m_strCurrentVersion.remove("\r").remove("\n").remove("\t");
    }
    else
    {
        m_strCurrentVersion = GetAppVersion();
        WriteFile(m_strAllVersionPath, m_strCurrentVersion);
    }
    qDebug()<<"总版本:"<<m_strCurrentVersion;

    Register2Map(Method_reboot);
    Register2Map(Method_upgrade_data);
    Register2Map(Method_upgrade_end);

    Register2Map(Method_pcr_reboot);
    Register2Map(Method_pcr_upgrade_data);
    Register2Map(Method_pcr_upgrade_end);

    Register2Map(Method_fl_reboot);
    Register2Map(Method_fl_upgrade_data);
    Register2Map(Method_fl_upgrade_end);

    _InitWidget();
    _InitLayout();

    m_pProgressBar = new CProgressBar(tr("软件升级"), tr("进度")); //升级全屏显示

    LoadQSS(this, ":/qss/qss/system/update.qss");

    connect(this, &CUpdateWidget::SignalCopyError, this, &CUpdateWidget::_SlotCopyError);
    connect(this, &CUpdateWidget::SignalFileMissing, this, &CUpdateWidget::_SlotFileMissing);
    connect(this, &CUpdateWidget::SignalCodeError, this, &CUpdateWidget::_SlotCodeError);
    connect(this, &CUpdateWidget::SignalExtarctEnd, this, &CUpdateWidget::_SlotExtarctEnd);

    m_pUpdateTimer = new QTimer(this);
    connect(m_pUpdateTimer, &QTimer::timeout, this, &CUpdateWidget::_SlotUpdateTimeout);
}

void CUpdateWidget::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    if(!m_bShow)
        return;
    if(!m_bUpdateStart)
        return;

    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    switch (iMethodID)
    {
    case Method_upgrade_data:
    case Method_pcr_upgrade_data:
    case Method_fl_upgrade_data:
        _UpdateFirmReply(iMachineID, iMethodID, iResult, qVarData);
        break;
    case Method_upgrade_end:
    case Method_pcr_upgrade_end:
    case Method_fl_upgrade_end:
        _UpdateFirmEnd(iMachineID, iMethodID, iResult);
        break;
    default:
        break;
    }
}

void CUpdateWidget::_UpdateFirmEnd(int iMachineID, int iMethodID, int iResult)
{
    int iUpdateType = -1;
    if(Method_upgrade_end == iMethodID)
        iUpdateType = eUpdate_Median;
    else if(Method_pcr_upgrade_end == iMethodID)
        iUpdateType = eUpdate_PCR;
    else if(Method_fl_upgrade_end == iMethodID)
        iUpdateType = eUpdate_FL;
    else
        return;

    if(0 == iResult)
    {
        m_iUpdateResultMap[iMachineID][iUpdateType] = tr("成功");
        m_iNeedUpdateMap[iMachineID][iUpdateType] = false; //标记此模块不需要升级
        _NextUpdateLowerFirm();
    }
    else
    {
        m_iUpdateResultMap[iMachineID][iUpdateType] = tr("失败");
        _UpdateProgressEnd();
        _ShowResultTable();
    }
}

void CUpdateWidget::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;

    if(CPublicConfig::GetInstance()->GetLoginLevel() < eUser_Factory)
    {
        m_pRestoreBtn->setVisible(false);
        m_pDBCheckBox->setVisible(false);
    }
    else
    {
        m_pRestoreBtn->setVisible(true);
        m_pDBCheckBox->setVisible(true);
    }

    QWidget::showEvent(pEvent);
}

void CUpdateWidget::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;

    QWidget::hideEvent(pEvent);
}

static bool CompareVersion(QString strVersion1, QString strVersion2)
{
    if(strVersion2.isEmpty())
        return true;

    strVersion1 = strVersion1.toUpper();
    strVersion2 = strVersion2.toUpper();

    if(strVersion1.startsWith('V'))
        strVersion1.remove(0, 1);
    if(strVersion2.startsWith('V'))
        strVersion2.remove(0, 1);

    //v0.3.8_RC1 v0.3.10_RC1 先比较数字版本再比较RC版本
    QString strNum1 = strVersion1;
    QString strRC1;
    if(strNum1.contains('_'))
    {
        strNum1 = strVersion1.split('_').at(0);
        strRC1 = strVersion1.split('_').at(1);
        strRC1.remove("RC");
    }

    QString strNum2 = strVersion2;
    QString strRC2;
    if(strNum2.contains('_'))
    {
        strNum2 = strVersion2.split('_').at(0);
        strRC2 = strVersion2.split('_').at(1);
        strRC2.remove("RC");
    }

    QStringList strList1 = strNum1.split('.');
    QStringList strList2 = strNum2.split('.');
    int iMin = qMin(strList1.size(), strList2.size());
    for(int i=0; i<iMin; i++)
    {
        int num1 = strList1.at(i).toInt();
        int num2 = strList2.at(i).toInt();
        if(num1 == num2)
            continue;

        return num1 > num2;
    }

    return strRC1.toInt() > strRC2.toInt(); //v0.3.8_RC2 v0.3.8_RC10
}

void CUpdateWidget::_SlotUpdateBtn()
{
#if 0
    QStringList strList1 = {tr("成功"), tr("成功"), tr("成功")};
    QStringList strList2 = {tr("失败"), tr("失败"), tr("失败")};
    QStringList strList3 = {tr("未升级"), tr("未升级"), tr("未升级")};
    m_iUpdateResultMap[0] = strList1;
    m_iUpdateResultMap[1] = strList2;
    m_iUpdateResultMap[2] = strList3;
    CUpdateResultPage *pCResultPage = new CUpdateResultPage(this);
    pCResultPage->SetResultMap(m_iUpdateResultMap);
    pCResultPage->show();
    return;
#endif

    for(int iMachineID=0; iMachineID<gk_iMachineCount; iMachineID++)
    {
        if(CRunTest::GetInstance()->GetRunInfoStruct(iMachineID).bRunning)
        {
            ShowInformation(this, m_strTipsText, tr("正在进行测试，不允许升级，请稍后重试"));
            return;
        }
    }

    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定要升级软件吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    for(int iMachineID=0; iMachineID<gk_iMachineCount; iMachineID++)
    {
        if(CRunTest::GetInstance()->GetRunInfoStruct(iMachineID).bRunning)
        {
            ShowInformation(this, m_strTipsText, tr("正在进行测试，不允许升级，请稍后重试"));
            return;
        }
    }

    bool bWiFi = false;
    bool bEth0 = false;
    bool bNetwork = bWiFi && bEth0;

    QDir qDir(GetUDiskDir());
    if(!qDir.exists()) //没接U盘
    {
        if(bNetwork)
        {
            qDebug()<<"一键升级,没接U盘.有网,查询服务器是否有新版本";
            // TODO
        }
        else
        {
            qDebug()<<"一键升级,既没U盘也没网";
            ShowInformation(this, m_strTipsText, tr("请先插入U盘"));
        }
        return;
    }

    //U盘里是否有Wondfo-SFW-WonDx1000-xxx
    QString strLastNewVersion;
    QString strLastZipPath;
    QString strLastZipName;

    qDir.setPath(GetUDiskUpdateDir());
    QFileInfoList infoList = qDir.entryInfoList(QDir::Files);
    for(int i=0; i<infoList.size(); i++)
    {
        QString strFilePath = infoList.at(i).absoluteFilePath();
        QString strFileName = infoList.at(i).fileName();
        if(!strFileName.startsWith("Wondfo-SFW-WonDx1000", Qt::CaseInsensitive))
            continue;

        qDebug()<<"一键升级,U盘升级文件:"<<strFileName;
        QString strVersion = strFileName.split("-").last().remove(".zip");
        strVersion.remove("\r").remove("\n");
        if(CompareVersion(strVersion, strLastNewVersion))
        {
            strLastNewVersion = strVersion;
            strLastZipPath = strFilePath;
            strLastZipName = strFileName;
        }
    }

    if(strLastZipPath.isEmpty())
    {
        if(bNetwork)
        {
            qDebug()<<"有U盘但没升级文件.有网,查询服务器是否有新版本";
            // TODO
        }
        else
        {
            qDebug()<<"一键升级,U盘里没有升级文件且没网";
            ShowInformation(this, m_strTipsText, tr("U盘里没有升级文件"));
        }
        return;
    }

    int iUserLevel = CPublicConfig::GetInstance()->GetLoginLevel();
    QString strUserName = CPublicConfig::GetInstance()->GetLoginUser();
    qDebug()<<"一键升级,U盘最新版本升级文件:"<<strLastZipPath<<"当前账号:"<<strUserName<<iUserLevel<<",当前版本:"<<m_strCurrentVersion;
    if(iUserLevel < eUser_Maintain)
    {
        if(!CompareVersion(strLastNewVersion, m_strCurrentVersion))
        {
            if(bNetwork)
            {
                qDebug()<<"一键升级,当前账号等级低不能升级U盘里的旧版本,有网,查询服务器是否有新版本";
                // TODO
            }
            else
            {
                qDebug()<<"一键升级,当前账号等级低不能升级U盘里的旧版本,且没网";
                ShowInformation(this, m_strTipsText, tr("U盘没有最新版本的升级文件"));
            }
            return;
        }
    }

    qDebug()<<"开始升级:"<<strLastZipPath;
    m_strUpdateVersion = strLastNewVersion;
    m_strUpdateZipPath = m_strCurrentDir + strLastZipName;
    //m_pUpdateBtn->setEnabled(false);
    m_pProgressBar->SetRange(0, 100);
    m_pProgressBar->SetValue(0);

    if(nullptr == m_pProgressBar->parentWidget())
        m_pProgressBar->setParent((QWidget*)gk_pMainWindow);
    m_pProgressBar->raise();
    m_pProgressBar->show();
    m_pProgressBar->activateWindow();

    std::thread mythread(&CUpdateWidget::_Thread2ExtarctZip, this, strLastZipName, strLastZipPath);
    mythread.detach();
}

void CUpdateWidget::_SlotCopyError()
{
    _UpdateProgressEnd();
    ShowWarning(this, m_strTipsText, tr("升级失败，文件拷贝出错，请检查U盘"));
}

void CUpdateWidget::_SlotFileMissing()
{
    _UpdateProgressEnd();
    ShowWarning(this, m_strTipsText, tr("升级失败，文件缺失，请检查升级包"));
}

void CUpdateWidget::_SlotCodeError()
{
    _UpdateProgressEnd();
    ShowWarning(this, m_strTipsText, tr("升级失败，文件安全码校验异常，请检查升级包"));
}

static QByteArray GetFileMD5(QString strFilePath)
{
    QByteArray byteMD5;
    QFile file(strFilePath);
    if(!file.open(QFile::ReadOnly))
        return byteMD5;
    QByteArray fileContent = file.readAll();
    file.close();

    byteMD5 = QCryptographicHash::hash(fileContent,QCryptographicHash::Md5);
    return byteMD5;
}

/*
 * 1.计算升级包zip的md5
 * 2.计算升级key的md5
 * 3.QAES加密
 * 4.计算加密结果的md5
*/
QString CUpdateWidget::_CalcUpdateZipCode(QString strZipFilePath)
{
    QString strKey = "WF-MDX301";
    QByteArray byteZipMd5 = GetFileMD5(strZipFilePath);
    QAESEncryption encryption(QAESEncryption::AES_128,QAESEncryption::ECB,QAESEncryption::ZERO);
    QByteArray byteKeyMd5 = QCryptographicHash::hash(strKey.toUtf8(),QCryptographicHash::Md5);
    QByteArray encodedText = encryption.encode(byteZipMd5,byteKeyMd5);
    QByteArray byteCode = QCryptographicHash::hash(encodedText,QCryptographicHash::Md5);
    QString strCode = byteCode.toHex();

    qDebug()<<"一键升级,升级压缩包加密key:"<<strKey
           <<",zip md5:"<<byteZipMd5.toHex().toUpper()
          <<",key md5:"<<byteKeyMd5.toHex().toUpper()
         <<",加密结果:"<<encodedText.toBase64()
        <<",code:"<<strCode;

    return strCode;
}

void CUpdateWidget::_Thread2ExtarctZip(QString strLastZipName, QString strLastZipPath)
{
    QString strLocalZipPath = m_strCurrentDir + strLastZipName;
    QFile::remove(strLocalZipPath);
    bool bCopy = QFile::copy(strLastZipPath, strLocalZipPath);
    if(!bCopy)
    {
        emit SignalCopyError();
        return;
    }
    System("sync");

    QString strUpgradeName = "update.zip";
    QString strCodeName = "code.txt";
    QFile::remove(strUpgradeName);
    QFile::remove(strCodeName);

    //解压第1层
    {
        HZIP hz = OpenZip(strLocalZipPath.toLocal8Bit().data(), 0);
        SetUnzipBaseDir(hz, m_strCurrentDir.toLocal8Bit().data());
        ZIPENTRY ze;
        GetZipItem(hz, -1, &ze);
        int num = ze.index;
        qDebug()<<"解压数量:"<<num;
        for(int i=0; i<num; i++)
        {
            int m = GetZipItem(hz, i, &ze);
            int n = UnzipItem(hz, i, ze.name);
            qDebug()<<"解压文件:"<<i<<QString(ze.name)<<m<<n;
        }
        CloseZip(hz);

        if(!QFile::exists(strUpgradeName)/* || !QFile::exists(strCodeName)*/)
        {
            emit SignalFileMissing();
            return;
        }

        QString strReadCode;
        ReadFile(m_strCurrentDir + strCodeName, strReadCode);
        strReadCode = strReadCode.remove("\r").remove("\n").remove("\t");

        QString strCalcCode = _CalcUpdateZipCode(m_strCurrentDir + strUpgradeName);
        qDebug()<<"一键升级，读取安全码和计算安全码:"<<strReadCode<<strCalcCode;
        if(strReadCode != strCalcCode)
        {
            //emit SignalCodeError();
            //return;
        }
    }

    //解压第2层
    QString strUpgradePath = m_strCurrentDir + strUpgradeName;
    qDebug()<<"开始解压:"<<strUpgradePath;
    QString strRMCmd = QString("rm -r %1*").arg(this->m_strUpdateDir);
    System(strRMCmd);
    HZIP hz = OpenZip(strUpgradePath.toLocal8Bit().data(), 0);
    SetUnzipBaseDir(hz, m_strUpdateDir.toLocal8Bit().data());
    ZIPENTRY ze;
    GetZipItem(hz, -1, &ze);
    int num = ze.index;
    qDebug()<<"解压数量:"<<num;
    for(int i=0; i<num; i++)
    {
        int m = GetZipItem(hz, i, &ze);
        int n = UnzipItem(hz, i, ze.name);
        qDebug()<<"解压文件:"<<i<<QString(ze.name)<<m<<n;
    }
    CloseZip(hz);

    emit SignalExtarctEnd();
}

static QByteArray GetFileByteData(QString strFilePath)
{
    QByteArray byteData;
    QFile file(strFilePath);
    if(!file.open(QIODevice::ReadOnly))
    {
        qDebug()<<Q_FUNC_INFO<<"升级文件打开失败:"<<strFilePath<<file.errorString();
        return byteData;
    }

    byteData = file.readAll();
    file.close();
    return byteData;
}

void CUpdateWidget::_SlotExtarctEnd()
{
    m_pUpdateBtn->setEnabled(true);

    QString strChmodCmd = QString("chmod -R 777 %1").arg(m_strUpdateDir);
    int iRet = System(strChmodCmd);
    qDebug()<<"一键升级,修改解压文件权限:"<<strChmodCmd<<iRet;

    //1、判断升级文件是否齐全
    QString strFLPath = m_strUpdateDir + gk_strFLName;
    QString strPCRPath = m_strUpdateDir + gk_strPCRName;
    QString strMedianPath = m_strUpdateDir + gk_strSlaveName;

    QString strAPPPath = m_strUpdateDir + gk_strAppName;
    QString strCalcPath = m_strUpdateDir + gk_strAlgorithmName;
    QString strAutoPath = m_strUpdateDir + gk_strAutoName;
    QString strSHPath = m_strUpdateDir + gk_strMyShName;
    qDebug()<<"一键升级,文件路径:"<<strFLPath<<strPCRPath<<strMedianPath<<strAPPPath;

    bool bFileExist = QFile::exists(strFLPath) &&
            QFile::exists(strPCRPath) &&
            QFile::exists(strMedianPath) &&
            QFile::exists(strAPPPath);
    if(!bFileExist)
    {
        _UpdateProgressEnd();
        ShowWarning(this, m_strTipsText, tr("一键升级包里缺少必要的升级文件"));
        return;
    }

    //2、读取升级版本
    QString strFLVersion = "x", strPCRVersion = "x", strMedianVersion = "x";
    {
        QString strVerPath = m_strUpdateDir + "version.txt";
        QString strReadData;
        ReadFile(strVerPath, strReadData);
        strReadData.remove("\r").remove("\n").remove("\t");
        qDebug()<<Q_FUNC_INFO<<strReadData;
        QString strLog;
        QJsonObject qRootObj;
        QJsonParseError qErr;
        QJsonDocument qJsonDoc = QJsonDocument::fromJson(strReadData.toLocal8Bit(), &qErr);
        if(QJsonParseError::NoError == qErr.error)
        {
            qRootObj = qJsonDoc.object();
            strFLVersion = qRootObj.value(gk_strFLName).toString();
            strPCRVersion = qRootObj.value(gk_strPCRName).toString();
            strMedianVersion = qRootObj.value(gk_strSlaveName).toString();
        }
    }
    qDebug()<<"升级版本:"<<strFLVersion<<strPCRVersion<<strMedianVersion;

    //3、确认在线设备数量
    QList<int> iOnlineList;
    for(int i=0; i<gk_iMachineCount; i++)
    {
        if(CHeartBeat::GetInstance()->GetDeviceHearbeatStruct(i)->bDeviceConnect)
            iOnlineList.push_back(i);
    }
    qDebug()<<"一键升级,当前在线设备:"<<iOnlineList;

    if(iOnlineList.isEmpty())
    {
        m_pProgressBar->close();
        int iBtnType = ShowQuestion(this, m_strTipsText, tr("当前没有检测模块在线，是否只升级控制台程序"));
        if(QMessageBox::Yes == iBtnType)
            _UpdateUpperMachinePart();
        else
            ShowInformation(this, m_strTipsText, tr("没有进行任何模块的升级"));
        return;
    }

    //4、确认下位机哪些模块需要升级
    m_iNeedUpdateMap.clear();
    m_iUpdateResultMap.clear();
    for(int i=0; i<iOnlineList.size(); i++)
    {
        int iMachineID = iOnlineList.at(i);
        QList<bool> bList = {true, true, true};
        SPLCVerStruct sVerStruct = CPublicConfig::GetInstance()->GetPLCVersionStruct(iMachineID);
        qDebug()<<QString("%1#各模块版本:").arg(iMachineID+1)<<sVerStruct.strFLAppVersion<<sVerStruct.strPCRAppVersion<<sVerStruct.strMedianAppVersion;

        bList[eUpdate_Median] = sVerStruct.strMedianAppVersion == strMedianVersion ? false : true;
        bList[eUpdate_PCR] = sVerStruct.strPCRAppVersion == strPCRVersion ? false : true;
        bList[eUpdate_FL] = sVerStruct.strFLAppVersion == strFLVersion ? false : true;
        m_iNeedUpdateMap[iMachineID] = bList;

        QStringList strList = {tr("未升级"), tr("未升级"), tr("未升级")};
        m_iUpdateResultMap[iMachineID] = strList;
    }
    qDebug()<<"一键升级,下位机需要升级模块:"<<m_iNeedUpdateMap;

    //5、读取升级文件、计算升级进度、初始化升级结果map
    m_byteFLFileData = GetFileByteData(strFLPath);
    m_bytePCRFileData = GetFileByteData(strPCRPath);
    m_byteMedianFileData = GetFileByteData(strMedianPath);

    int iFLPackets = m_byteFLFileData.size() / 1024;
    if(0 != m_byteFLFileData.size() % 1024)
        iFLPackets++;

    int iPCRPackets = m_bytePCRFileData.size() / 1024;
    if(0 != m_bytePCRFileData.size() % 1024)
        iPCRPackets++;

    int iMedianPackets = m_byteMedianFileData.size() / 1024;
    if(0 != m_byteMedianFileData.size() % 1024)
        iMedianPackets++;

    int iProgressValue = 1;
    for(auto it=m_iNeedUpdateMap.constBegin(); it!=m_iNeedUpdateMap.constEnd(); it++)
    {
        QList<bool> bList = it.value();
        if(true == bList.at(eUpdate_FL))
            iProgressValue += iFLPackets;
        if(true == bList.at(eUpdate_PCR))
            iProgressValue += iPCRPackets;
        if(true == bList.at(eUpdate_Median))
            iProgressValue += iMedianPackets;
    }
    qDebug()<<"一键升级,进度条大小:"<<iProgressValue;

    m_bUpdateStart = true;
    m_pProgressBar->SetRange(0, iProgressValue);

    _NextUpdateLowerFirm();
}

void CUpdateWidget::_NextUpdateLowerFirm()
{
    qDebug()<<Q_FUNC_INFO<<m_iNeedUpdateMap;
    m_iUpdateType = -2;
    int iMachineID = -2;
    for(auto it=m_iNeedUpdateMap.begin(); it!=m_iNeedUpdateMap.end(); it++)
    {
        bool bFind = false;
        QList<bool> bList = it.value();
        for(int i=0; i<bList.size(); i++)
        {
            if(true == bList.at(i))
            {
                iMachineID = it.key();
                m_iUpdateType = i;
                bFind = true;
                break;
            }
        }
        if(bFind)
            break;
    }
    if(iMachineID < 0)
    {
        //重启下位机
        QList<int> iIDList = m_iNeedUpdateMap.keys();
        for(int i=0; i<iIDList.size(); i++)
        {
            int t_iID = iIDList.at(i);
            QString strRebootPCRCmd = GetJsonCmdString(Method_pcr_reboot);
            qDebug()<<QString("一键升级,%1#重启PCR").arg(t_iID + 1)<<strRebootPCRCmd;
            SendJsonCmd(t_iID, Method_pcr_reboot, strRebootPCRCmd);

            QString strRebootFLCmd = GetJsonCmdString(Method_fl_reboot);
            qDebug()<<QString("一键升级,%1#重启FL").arg(t_iID + 1)<<strRebootFLCmd;
            SendJsonCmd(t_iID, Method_fl_reboot, strRebootFLCmd);

            Delay_MSec(1000);
        }

        Delay_MSec(3000);
        for(int i=0; i<iIDList.size(); i++)
        {
            int t_iID = iIDList.at(i);
            QString strRebootCmd = GetJsonCmdString(Method_reboot);
            qDebug()<<QString("一键升级,%1#重启中位机").arg(t_iID + 1)<<strRebootCmd;
            SendJsonCmd(t_iID, Method_reboot, strRebootCmd);
        }

        qDebug()<<"下位机升级结束,开始升级上位机各模块";
        _UpdateUpperMachinePart();
        return;
    }
    qDebug()<<QString("一键升级%1#开始升级模块:%2").arg(iMachineID + 1).arg(m_iUpdateType);


    int iMethodID = -1;
    QByteArray byteData;
    if(eUpdate_FL == m_iUpdateType)
    {
        byteData = m_byteFLFileData;
        iMethodID = Method_fl_upgrade_req;
    }
    else if(eUpdate_PCR == m_iUpdateType)
    {
        byteData = m_bytePCRFileData;
        iMethodID = Method_pcr_upgrade_req;
    }
    else if(eUpdate_Median == m_iUpdateType)
    {
        byteData = m_byteMedianFileData;
        iMethodID = Method_upgrade_req;
    }
    else
    {
        return;
    }

    QString strSync = byteData.mid(0, 4).toHex();
    QString strCrc = byteData.mid(4, 4).toHex();
    int iLen = byteData.mid(8, 4).toHex().toInt(nullptr, 16);
    QString strLoadAddr = byteData.mid(12, 4).toHex();
    QString strEpAddr = byteData.mid(16, 4).toHex();

    QVariantList qVarList = {iLen};
    SendJsonCmd(iMachineID, iMethodID, GetJsonCmdString(iMethodID, qVarList));

    m_pUpdateTimer->start(G_iUpdateTimeout);
}

void CUpdateWidget::_UpdateFirmReply(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    QByteArray byteFileData;
    if(Method_fl_upgrade_data == iMethodID)
    {
        if(eUpdate_FL != m_iUpdateType)
        {
            qDebug()<<QString("一键升级,当前没有在升级%1#荧光").arg(iMachineID + 1);
            return;
        }
        byteFileData = m_byteFLFileData;
    }
    else if(Method_pcr_upgrade_data == iMethodID)
    {
        if(eUpdate_PCR != m_iUpdateType)
        {
            qDebug()<<QString("一键升级,当前没有在升级%1#PCR").arg(iMachineID + 1);
            return;
        }
        byteFileData = m_bytePCRFileData;
    }
    else if(Method_upgrade_data == iMethodID)
    {
        if(eUpdate_Median != m_iUpdateType)
        {
            qDebug()<<QString("一键升级,当前没有在升级%1#中位机").arg(iMachineID + 1);
            return;
        }
        byteFileData = m_byteMedianFileData;
    }
    else
    {
        return;
    }

    QVariantMap qVarMap = qVarData.toMap();
    int iPackID = qVarMap.value("packID").toInt();
    qDebug()<<QString("%1# %2 升级PackID:%3 %4").arg(iMachineID + 1).arg(iMethodID).arg(iPackID).arg(iResult);
    if(iPackID < 0)
        return;

    int iTotalSize = byteFileData.size();
    int iOnePieceLen = 1024;
    int iOffset = iPackID * iOnePieceLen;
    int iLeftSize = iTotalSize - iOffset;
    if(iLeftSize < 0)
    {
        qDebug()<<QString("%1# packID有误,超过文件大小:").arg(iMachineID + 1)<<iLeftSize;
        return;
    }

    if(iLeftSize < iOnePieceLen)
    {
        qDebug()<<"升级最后一包大小:"<<iTotalSize<<iOffset<<iLeftSize;
        iOnePieceLen = iLeftSize;
    }

    QByteArray byteID;
    byteID[0] = uchar(iPackID / 256);
    byteID[1] = uchar(iPackID % 256);

    QByteArray byteData = byteFileData.mid(iOffset, iOnePieceLen);

    QByteArray payload;
    payload.append(byteID);
    payload.append(byteData);

    if(eUpdate_Median == m_iUpdateType)
        COperationUnit::GetInstance()->MedianUpdate(iMachineID, payload);
    else if(eUpdate_PCR == m_iUpdateType)
        COperationUnit::GetInstance()->PCRUpdate(iMachineID, payload);
    else if(eUpdate_FL == m_iUpdateType)
        COperationUnit::GetInstance()->FLUpdate(iMachineID, payload);

    m_pUpdateTimer->start(G_iUpdateTimeout);
    m_pProgressBar->AddValue();
}

void CUpdateWidget::_SlotUpdateTimeout()
{
    _UpdateProgressEnd();
    _ShowResultTable();
    ShowWarning(this, m_strTipsText, tr("升级失败，超时"));
}

void CUpdateWidget::_UpdateProgressEnd()
{
    m_pUpdateTimer->stop();
    m_bUpdateStart = false;
    m_byteFLFileData.clear();
    m_bytePCRFileData.clear();
    m_byteMedianFileData.clear();
    m_pProgressBar->close();
    m_iUpdateType = eUpdate_FL;
}

void CUpdateWidget::_ShowResultTable()
{
    CUpdateResultPage *pCResultPage = new CUpdateResultPage;
    pCResultPage->SetResultMap(m_iUpdateResultMap);
    pCResultPage->show();
}

void CUpdateWidget::_UpdateUpperMachinePart()
{
    m_pProgressBar->SetMaxValue();
    qDebug()<<"一键升级,开始升级上位机";

    //删除网络配置,防止核心板换来换去
    System("rm /etc/udev/rules.d/70-persistent-net.rules");

    //Resources
    System("cp /root/update/Resources/* /root/Resources/");

    qDebug()<<"是否覆盖时序数据库:"<<m_pDBCheckBox->isChecked();
    if(false == m_pDBCheckBox->isChecked())
    {
        System("cp /root/db/timing.db /root/update/db/");
        System("sync");
    }

    //db
    System("cp /root/update/db/* /root/db/");

    //fonts
    System("cp /root/update/fonts/* /usr/share/fonts/");

    //firmeware
    System("cp -rf /root/update/fireware /root/");

    //start sh
    System("cp /root/update/S99_7C /etc/init.d/");

    //ncftp
    System("cp /root/update/ftp/* /usr/bin/");

    //backup
    System("rm -rf /root/bak/*");
    System("cp /root/7CPDF /root/bak/");
    System("cp /root/7CStart /root/bak/");
    System("cp /root/7CAPP /root/bak/");
    System("cp /root/lib/* /root/bak/");

    //lib
    System("cp /root/update/lib/* /root/lib/");

    //app
    System("rm -rf /root/app/*");
    System("cp /root/update/7CPDF /root/app/");
    System("cp /root/update/7CStart /root/app/");
    System("cp /root/update/7CAPP /root/app/");

    QString strStartSH;
    ReadFile("/root/update/S99_7C", strStartSH);
    qDebug()<<Q_FUNC_INFO<<strStartSH;
    if(false == strStartSH.contains("cp /root/app/* /root/", Qt::CaseInsensitive))
    {
		System("rm -rf /root/app/*"); //防止旧->新，新->旧，再旧->新
		
        //兼容回退旧版本升级包
        System("rm /root/7CPDF");
        System("cp /root/update/7CPDF /root/");

        System("rm /root/7CStart");
        System("cp /root/update/7CStart /root/");

        System("rm /root/7CAPP");
        System("cp /root/update/7CAPP /root/");
    }

    _UpdateProgressEnd();
    ShowSuccess(this, m_strTipsText, tr("软件升级完成，正在重启，请稍后"));

    //save version
    WriteFile(m_strAllVersionPath, m_strUpdateVersion);

    //reboot
    System("sync");
    System("reboot");
}

void CUpdateWidget::_SlotRestoreBtn()
{
    int iBtnType = ShowQuestion(this, tr("提示"), tr("恢复出厂设置将会删除所有用户数据并重启，确定吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    int iRet = -1;
    QString strCmd;
    //删除db
    QString strDBDir = QApplication::applicationDirPath() + "/db/";
    QStringList strDBList = {"History.db", "log.db", "project.db", "user.db", "ftp.db", "devInfo.db"};
    for(int i=0; i<strDBList.size(); i++)
    {
        strCmd = "rm " + strDBDir + strDBList.at(i);
        iRet = System(strCmd.toStdString().c_str());
        qDebug()<<"删除db:"<<strCmd<<iRet;
    }

    //删除配置
    strCmd = "rm " + CPublicConfig::GetInstance()->GetConfigFilePath();
    iRet = System(strCmd.toStdString().c_str());
    qDebug()<<"删除配置:"<<strCmd<<iRet;

    //删除pdf
    strCmd = "rm -rf " + CPublicConfig::GetInstance()->GetPdfDir();
    iRet = System(strCmd.toStdString().c_str());
    qDebug()<<"删除pdf:"<<strCmd<<iRet;

    //删除excel
    strCmd = "rm -rf " + CPublicConfig::GetInstance()->GetXlsxDir();
    iRet = System(strCmd.toStdString().c_str());
    qDebug()<<"删除xlsx:"<<strCmd<<iRet;

    //删除日志
    strCmd = "rm -rf " + CPublicConfig::GetInstance()->GetLogSaveDir();
    iRet = System(strCmd.toStdString().c_str());
    qDebug()<<"删除日志:"<<strCmd<<iRet;

    System("sync");
    System("reboot");
}

void CUpdateWidget::_InitWidget()
{
    m_pCSysTtileLabelWidget = new CSysFirstTitleWidget(tr("系统设置"), tr("软件升级"));
    connect(m_pCSysTtileLabelWidget, &CSysFirstTitleWidget::SignalTitlePress, this, &CUpdateWidget::SignalReturn);

    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(1684, 904);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");

    m_pCHLabelTitleWidget = new CHLabelTitleWidget(tr("软件升级"));

    m_pSoftInfoLabel = new QLabel(tr("软件信息"));
    m_pSoftInfoLabel->setObjectName("InfoLabel");

    m_pSoftNameLabel = new QLabel(tr("软件名称：全自动核酸扩增分析仪软件"));

    int times = 0, index = 0;
    for(int i=0; i<m_strCurrentVersion.size(); i++)
    {
        if('.' == m_strCurrentVersion.at(i))
            times++;

        if(2 == times)
        {
            index = i;
            break;
        }
    }
    QString strReleaseVer = m_strCurrentVersion.mid(0, index);

    int iDev = 1, iItem = 8;
    CPublicConfig::GetInstance()->GetDevItemNum(iDev, iItem);
    m_pSoftModelLabel = new QLabel(tr("软件型号：%1").arg("WonDx-1000"));
    m_pReleaseVerLabel = new QLabel(tr("发布版本：%1").arg(strReleaseVer));
    m_pTotalVerLabel = new QLabel(tr("完整版本：%1").arg(m_strCurrentVersion));
    m_pManufacturerLabel = new QLabel(tr("注册申请人：广州万孚生物技术股份有限公司"));
    m_pAddressNameLabel = new QLabel(tr("设计开发地址："));
    m_pAddressNameLabel->setAlignment(Qt::AlignTop);
    m_pAddressValueLabel = new QLabel(tr("广州市黄埔区科学城荔枝山路8号、\n\n广州市黄埔区神舟路268号"));
    m_pAddressValueLabel->setAlignment(Qt::AlignTop);

#ifdef Q_OS_WIN
    m_pReleaseVerLabel->setText(tr("发布版本：") + "V1");
    m_pTotalVerLabel->setText(tr("完整版本：") + GetAppVersion());
#endif

    m_pGrayLabel = new QLabel;
    m_pGrayLabel->setFixedSize(800, 600);
    m_pGrayLabel->setObjectName("GrayLabel");

    m_pReturnBtn = new QPushButton(tr("返回"));
    m_pReturnBtn->setFixedSize(150, 56);
    m_pReturnBtn->setObjectName("CancelBtn");
    connect(m_pReturnBtn, &QPushButton::clicked, this, &CUpdateWidget::SignalReturn);

    m_pUpdateBtn = new QPushButton(tr("软件升级"));
    m_pUpdateBtn->setFixedSize(150, 56);
    connect(m_pUpdateBtn, &QPushButton::clicked, this, &CUpdateWidget::_SlotUpdateBtn);

    m_pRestoreBtn = new QPushButton(tr("恢复出厂设置"));
    m_pRestoreBtn->setFixedSize(180, 56);
    connect(m_pRestoreBtn, &QPushButton::clicked, this, &CUpdateWidget::_SlotRestoreBtn);

    m_pDBCheckBox = new QCheckBox(tr("升级覆盖时序数据库"), this);
    m_pDBCheckBox->setChecked(true);

    if(eLanguage_English == gk_iLanguage)
    {
        m_pGrayLabel->setFixedSize(1580, 600);
        m_pRestoreBtn->setFixedSize(320, 56);
    }
    else if(eLanguage_Spanish == gk_iLanguage)
    {
        m_pGrayLabel->setFixedSize(1580, 600);
        m_pRestoreBtn->setFixedSize(360, 56);
    }
    else if(eLanguage_German == gk_iLanguage)
    {
        m_pGrayLabel->setFixedSize(1580, 600);
        m_pRestoreBtn->setFixedSize(450, 56);
    }
    else if(eLanguage_Italian == gk_iLanguage)
    {
        m_pGrayLabel->setFixedSize(1580, 600);
        m_pUpdateBtn->setFixedSize(210, 56);
        m_pRestoreBtn->setFixedSize(500, 56);
    }
}

void CUpdateWidget::_InitLayout()
{
    QHBoxLayout *pAddressLayout = new QHBoxLayout;
    pAddressLayout->setMargin(0);
    pAddressLayout->setSpacing(0);
    pAddressLayout->addWidget(m_pAddressNameLabel);
    pAddressLayout->addWidget(m_pAddressValueLabel);
    pAddressLayout->addStretch(1);

    QVBoxLayout *pInfoLayout = new QVBoxLayout;
    pInfoLayout->setContentsMargins(100, 0, 100, 0);
    pInfoLayout->setSpacing(30);
    pInfoLayout->addSpacing(40);
    pInfoLayout->addWidget(m_pSoftInfoLabel, 0, Qt::AlignHCenter);
    pInfoLayout->addWidget(m_pSoftNameLabel);
    pInfoLayout->addWidget(m_pSoftModelLabel);
    pInfoLayout->addWidget(m_pReleaseVerLabel);
    pInfoLayout->addWidget(m_pTotalVerLabel);
    pInfoLayout->addWidget(m_pManufacturerLabel);
    pInfoLayout->addLayout(pAddressLayout);
    pInfoLayout->addStretch(1);
    m_pGrayLabel->setLayout(pInfoLayout);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(60);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pReturnBtn);
    pBtnLayout->addWidget(m_pUpdateBtn);
    pBtnLayout->addWidget(m_pRestoreBtn);
    pBtnLayout->addWidget(m_pDBCheckBox);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pBackLayout = new QVBoxLayout;
    pBackLayout->setContentsMargins(24, 10, 24, 24);
    pBackLayout->setSpacing(0);
    pBackLayout->addWidget(m_pCHLabelTitleWidget, 0, Qt::AlignLeft);
    //pBackLayout->addLayout(pBtnLayout);
    pBackLayout->addStretch(1);
    pBackLayout->addWidget(m_pGrayLabel, 0, Qt::AlignHCenter);
    pBackLayout->addStretch(1);
    pBackLayout->addLayout(pBtnLayout);
    m_pBackgroundLabel->setLayout(pBackLayout);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pCSysTtileLabelWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pBackgroundLabel);
    this->setLayout(pLayout);
}
