#ifndef CCONFIGJSON_H
#define CCONFIGJSON_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-02-02
  * Description: 配置文件读写
  * -------------------------------------------------------------------------
  * History: 2024-10-30直接断电保存失败的问题,修改文件读写
  * 2024-12-03:每写一个参数再保存，连续保存多个参数时有明显耗时卡顿,增加对QJsonObject的操作
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QFile>
#include <QObject>
#include <QVariant>
#include <QJsonObject>

class CConfigJson : public QObject
{
    Q_OBJECT
public:
    static CConfigJson *GetInstance();
    ~CConfigJson();

public:
    // config.json
    QJsonObject GetConfigJsonObject();                      //根节点
    QJsonObject GetConfigJsonObject(const QString &strKey); //下一级子节点
    QVariant GetConfigValue(const QString &strKey);
    QVariant GetConfigValue(const QString &strFirstKey, const QString &strSecondKey);

    void SetConfigValue(const QString &strKey, const QVariant &varValue);
    void SetConfigValue(const QString &strFirstKey, const QString &strSecondKey, const QVariant &varValue);
    void SetConfigJsonObject(const QJsonObject &qRootObject);
    void SetConfigJsonObject(const QString &strKey, const QJsonObject &qJsonObject);
    void IncrementInsertJsonObject(const QString &strKey, const QJsonObject &qJsonObject);

public:
    //system.json
    QVariant GetSystemValue(const QString &strKey);
    void SetSystemValue(const QString &strKey, const QVariant &varValue);

private:
    CConfigJson();

    QJsonObject _ReadJsonObject(const QString &strFilePath);
    QJsonObject _ReadJsonObject(const QString &strFilePath, const QString &strKey);
    bool _WriteJsonObject(const QString &strFilePath, const QJsonObject &qRootObj);

private:
    QString m_strConfigPath, m_strSystemPath;
    static CConfigJson *m_spInstance;

private:
    Q_DISABLE_COPY(CConfigJson)
};

#endif // CCONFIGJSON_H
