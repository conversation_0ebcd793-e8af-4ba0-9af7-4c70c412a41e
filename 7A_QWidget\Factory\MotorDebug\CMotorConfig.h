#ifndef CMOTORCONFIG_H
#define CMOTORCONFIG_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-12-07
  * Description: 电机配置
  * -------------------------------------------------------------------------
  * History: 此页面没用,后面删除
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QPushButton>

#include "CCmdBase.h"
#include "CLineEdit.h"
#include "CLabelComboBox.h"

class CMotorConfig : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CMotorConfig(QWidget *parent = nullptr);
    ~CMotorConfig();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

public slots:
    void SlotSoftTypeChanged(int iSoftType);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;

private slots:
    void _SlotReadText();
    void _SlotSetText();
    void _SlotAddConsum();
    void _SlotDelConsum();
    void _SlotMotor2DB();
    void _SlotDB2Motor();

private:
    void _InitWidget();

private:
    CLabelComboBox *m_pMotorComboBox;
    CLabelComboBox *m_pMachineComboBox;
    CLabelComboBox *m_pTextComboBox;
    CLineEdit *m_pTextLineEdit;
    QPushButton *m_pReadTextBtn, *m_pSetTextBtn;
    CLabelComboBox *m_pConsumComboBox;
    QPushButton *m_pAddConsumBtn, *m_pDelConsumBtn, *m_pMotor2DBBtn, *m_pDB2MotorBtn;

    QStringList m_strConfigIDList;
    QStringList m_strConsumList;
};

#endif // CMOTORCONFIG_H
