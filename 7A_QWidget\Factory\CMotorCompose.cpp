#include "CMotorCompose.h"
#include <QCheckBox>
#include <QBoxLayout>
#include <QGridLayout>
#include <QHeaderView>
#include <QTime>
#include <QEvent>
#include <QStyleFactory>

#include "CMessageBox.h"
#include "CMotorDB.h"

#define T_PARAMS   3
#define SIX_PARAMS 6

CMotorCompose::CMotorCompose(QWidget *parent) : QWidget(parent)
{
    QTime start = QTime::currentTime();

    Register2Map(Method_read_motor_cmd);
    Register2Map(Method_set_motor_cmd);
    Register2Map(Method_read_all_cmds);
    Register2Map(Method_reset_all_cmds);

    m_iUiMachineID = 0;
    _InitTextList();

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalSoftTypeChanged, this, &CMotorCompose::_SlotSoftTypeChanged);

    _InitWidget();
    _InitLayout();

    m_pSixParams = new CSixParams(this);
    connect(m_pSixParams, &CSixParams::SignalParamsConfirm, this, &CMotorCompose::_SlotSixParmasConfirm);
    m_pSixParams->setVisible(false);

    m_pThreeParams = new CThreeParams(this);
    connect(m_pThreeParams, &CThreeParams::SignalParamsConfirm, this, &CMotorCompose::_SlotThreeParmasConfirm);
    m_pThreeParams->setVisible(false);

    m_pOptoCountTouchStop = new COptoCountTouchStop(this);
    connect(m_pOptoCountTouchStop, &COptoCountTouchStop::SignalParamsConfirm, this, &CMotorCompose::_SlotOptoTouchStopConfirm);
    m_pOptoCountTouchStop->setVisible(false);

    m_pOptoCompose = new COptoCompose(this);
    connect(m_pOptoCompose, &COptoCompose::SignalParamsConfirm, this, &CMotorCompose::_SlotOptoComposeConfirm);
    m_pOptoCompose->setVisible(false);

    _LoadName2Table();

    qDebug()<<"电机组合页面构造时间:"<<start.msecsTo(QTime::currentTime());
}

CMotorCompose::~CMotorCompose()
{
    UnRegister2Map(Method_read_motor_cmd);
    UnRegister2Map(Method_set_motor_cmd);
    UnRegister2Map(Method_read_all_cmds);
    UnRegister2Map(Method_reset_all_cmds);
}

void CMotorCompose::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    if(Method_read_motor_cmd == iMethodID)
    {
        //0,278,TV1RST,60;22;2,0,0,0;4,0,6;1,2;12,0,1,3500;24;4,0,2;32;1,2;12,0,0,5000;5,2,0;4,0,3;17;28;32
        QString strReadData = qVarData.toString();
        QStringList strList = strReadData.split(SPLIT_BETWEEN_CMD);
        if(strList.size() >= 2)
        {
            QString strCmdParam = strList.at(0);
            QStringList paramList = strCmdParam.split(SPLIT_IN_CMD);
            if(paramList.size() < 4)
            {
                qDebug()<<"读取电机指令返回内容错误,长度小于4"<<strCmdParam;
                return;
            }

            QString strCmdID = paramList.at(1);
            QString strCmdName = paramList.at(2);
            QString strTimeout = paramList.at(3);
            QString strCmdText = CMotorDB::GetInstance().GetCmdTextByCmdID(strCmdID);

            m_pMotorComboBox->SetCurrentIndex(paramList.at(0).toInt());
            m_pCmdIDLineEidt->SetLineEditText(strCmdID);
            m_pCmdNameLineEdit->SetLineEditText(strCmdName);
            m_pTimeoutLineEdit->SetLineEditText(strTimeout);
            m_pCHTextLineEdit->setText(strCmdText);

            int iRow = m_pFileTableWidget->rowCount();
            m_pFileTableWidget->insertRow(iRow);
            _InsertCmdIDName2Table(m_pFileTableWidget->rowCount(), strCmdID + "_" + strCmdName);

            strList.pop_front();
            QString strCmdContent = strList.join(SPLIT_BETWEEN_CMD);
            CMotorDB::GetInstance().AddOneCmd(strCmdID, strCmdName, strCmdText, strCmdParam, strCmdContent);
        }
        if(!m_varReadAllIDList.isEmpty())
        {
            QVariant qVarID = m_varReadAllIDList.takeFirst();
            QVariantList qVarList = {qVarID};
            QString strCmd = GetJsonCmdString(Method_read_motor_cmd, qVarList);
            qDebug()<<QString("%1#").arg(iMachineID + 1)<<"读取电机组合单条指令:"<<qVarID.toInt()<<strCmd<<",剩余:"<<m_varReadAllIDList.size();
            SendJsonCmd(iMachineID, Method_read_motor_cmd, strCmd);
        }
        else
        {
            emit CPublicConfig::GetInstance()->SignalReGetMotorTextIDData();
        }
    }
    else if(Method_read_all_cmds == iMethodID)
    {
        m_varReadAllIDList = qVarData.toList();
        if(m_varReadAllIDList.size() > 0)
        {
            QVariant qVarID = m_varReadAllIDList.takeFirst();
            QVariantList qVarList = {qVarID};
            QString strCmd = GetJsonCmdString(Method_read_motor_cmd, qVarList);
            qDebug()<<QString("%1#").arg(iMachineID + 1)<<"读取电机组合单条指令:"<<qVarID.toInt()<<strCmd<<",剩余:"<<m_varReadAllIDList.size();
            SendJsonCmd(iMachineID, Method_read_motor_cmd, strCmd);
        }
    }
    else if(Method_reset_all_cmds == iMethodID)
    {
        qDebug()<<QString("%1#").arg(iMachineID + 1)<<"清空全部电机组合下位机应答:"<<iResult;
    }
    else if(Method_set_motor_cmd == iMethodID)
    {
        qDebug()<<QString("%1#").arg(iMachineID + 1)<<"设置电机指令:"<<iResult;
    }
}

void CMotorCompose::_SlotSoftTypeChanged(int iSoftType)
{
    Q_UNUSED(iSoftType);
    m_strMotorNameList = CPublicConfig::GetInstance()->GetMotorNameList();
    m_pMotorComboBox->SetComboBoxList(m_strMotorNameList);
}

void CMotorCompose::_SlotSixParmasConfirm(int iRow, const QString &strParmas)
{
    CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(m_pCmdTableWidget->cellWidget(iRow, COL_NUM_PARAM2));
    if(pLineEdit)
        pLineEdit->setText(strParmas);
}

void CMotorCompose::_SlotThreeParmasConfirm(int iRow, const QString &strParmas)
{
    CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(m_pCmdTableWidget->cellWidget(iRow, COL_NUM_PARAM3));
    if(pLineEdit)
        pLineEdit->setText(strParmas);
}

void CMotorCompose::_SlotOptoTouchStopConfirm(int iRow, const QString &strParmas)
{
    CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(m_pCmdTableWidget->cellWidget(iRow, COL_NUM_PARAM1));
    if(pLineEdit)
        pLineEdit->setText(strParmas);
}

void CMotorCompose::_SlotOptoComposeConfirm(int iRow, const QString &strParmas)
{
    CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(m_pCmdTableWidget->cellWidget(iRow, COL_NUM_PARAM2));
    if(pLineEdit)
        pLineEdit->setText(strParmas);
}

void CMotorCompose::showEvent(QShowEvent *pEvent)
{
    int index = m_pMotorComboBox->GetCurrentIndex();
    m_strMotorCompensateNameList = CMotorDB::GetInstance().GetCompensateNameListByMotorIndex(index);

    QWidget::showEvent(pEvent);
}

bool CMotorCompose::eventFilter(QObject *pObject, QEvent *pEvent)
{
    if(pObject->inherits("QComboBox"))
    {
        if(QEvent::Wheel == pEvent->type())
            return true;
    }

    return QWidget::eventFilter(pObject, pEvent);
}

void CMotorCompose::_SlotParseBtn()
{
    QString strData = m_pCHTextLineEdit->text();
    if(strData.isEmpty())
        return;

    QStringList strList = strData.split(SPLIT_BETWEEN_CMD);
    int size = strList.size();
    qDebug()<<"电机组合解析指令:"<<size<<strData;
    if(0 == size)
        return;

    m_pLoadBtn->setEnabled(false);

    m_pCmdTableWidget->setRowCount(size);
    for(int i=0; i<size; i++)
    {
        QStringList oneList = strList.at(i).split(SPLIT_IN_CMD);
        if(oneList.isEmpty())
            continue;

        _SetOneRowData(m_pCmdTableWidget, i, oneList);
    }

    m_pLoadBtn->setEnabled(true);
}

void CMotorCompose::_SlotFindBtn()
{   
    QString strFindText = m_pFindLineEdit->text();
    if(strFindText.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("查找内容不能为空"));
        return;
    }

    for(int i=0; i<m_strIDNameList.size(); i++)
    {
        if(m_strIDNameList.at(i).contains(strFindText))
        {
            m_pFileTableWidget->selectRow(i);
            return;
        }
    }

    ShowInformation(this, m_strTipsText, tr("查找结果为空"));
}

void CMotorCompose::_SlotLoadBtn()
{
    int iRow = m_pFileTableWidget->currentRow();
    if(iRow < 0)
    {
        ShowInformation(this, m_strTipsText, tr("请先选择指令"));
        return;
    }

    _ClearTableWidget();

    QString strIDName = m_pFileTableWidget->item(iRow, 1)->text();
    QStringList strIDNameList = strIDName.split("_");
    QString strCmdID = (strIDName.size() >= 1) ? strIDNameList.at(0) : "";
    m_pCmdIDLineEidt->SetLineEditText(strCmdID);
    if(strCmdID.isEmpty())
        return;

    QStringList strInfoList = CMotorDB::GetInstance().GetCmdInfoByCmdID(strCmdID);
    qDebug()<<"电机组合加载指令:"<<strCmdID<<strInfoList;
    QString strCmdName = strInfoList.at(2);    //指令名称
    QString strCmdText = strInfoList.at(3);    //指令中文描述
    QString strCmdParam = strInfoList.at(4);   //指令参数,电机index + id + name + timeout
    QString strCmdContent = strInfoList.at(5); //指令内容

    m_pCmdNameLineEdit->SetLineEditText(strCmdName);
    m_pCHTextLineEdit->setText(strCmdText);
    QStringList strParamList = strCmdParam.split(SPLIT_IN_CMD);
    if(strParamList.size() >= 4)
    {
        m_pMotorComboBox->SetCurrentIndex(strParamList.at(0).toInt());
        m_pCmdIDLineEidt->SetLineEditText(strParamList.at(1));
        m_pCmdNameLineEdit->SetLineEditText(strParamList.at(2));
        m_pTimeoutLineEdit->SetLineEditText(strParamList.at(3));
    }

    if(strCmdContent.isEmpty())
        return;
    QStringList strList = strCmdContent.split(SPLIT_BETWEEN_CMD);
    int size = strList.size();
    if(0 == size)
        return;

    m_pLoadBtn->setEnabled(false);

    m_pCmdTableWidget->setRowCount(size);
    for(int i=0; i<size; i++)
    {
        QStringList oneList = strList.at(i).split(SPLIT_IN_CMD);
        if(oneList.isEmpty())
            continue;

        _SetOneRowData(m_pCmdTableWidget, i, oneList);
    }

    m_pLoadBtn->setEnabled(true);
}

void CMotorCompose::_SlotDelBtn()
{
    int iRow = m_pFileTableWidget->currentRow();
    if(iRow < 0)
    {
        ShowInformation(this, m_strTipsText, tr("请先选择时序"));
        return;
    }

    QString strIDName = m_pFileTableWidget->item(iRow, 1)->text();
    qDebug()<<"删除电机组合指令:"<<strIDName;
    m_strIDNameList.removeOne(strIDName);
    m_pFileTableWidget->removeRow(iRow);
    ResortTableWidget(m_pFileTableWidget);

    QStringList strList = strIDName.split("_");
    if(strList.size() >= 2)
        CMotorDB::GetInstance().DeleteOneCmd(strList.at(0));

    emit CPublicConfig::GetInstance()->SignalReGetMotorTextIDData();
}

void CMotorCompose::_SlotMoveUpBtn()
{
    int iRow = m_pCmdTableWidget->currentRow();
    if(iRow <= 0)
        return;

    QString strCurrentRowData = _GetOneRowData(m_pCmdTableWidget, iRow);
    QString strUpRowData = _GetOneRowData(m_pCmdTableWidget, iRow - 1);
    qDebug()<<"电机组合上移一行:"<<strCurrentRowData<<strUpRowData;
    _SetOneRowData(m_pCmdTableWidget, iRow - 1, strCurrentRowData.split(SPLIT_IN_CMD));
    _SetOneRowData(m_pCmdTableWidget, iRow, strUpRowData.split(SPLIT_IN_CMD));
    ResortTableWidget(m_pCmdTableWidget);
    m_pCmdTableWidget->selectRow(iRow - 1);
}

void CMotorCompose::_SlotMoveDownBtn()
{
    int iRow = m_pCmdTableWidget->currentRow();
    if(iRow < 0 || iRow >= m_pCmdTableWidget->rowCount() -1)
        return;

    QString strCurrentRowData = _GetOneRowData(m_pCmdTableWidget, iRow);
    QString strDownRowData = _GetOneRowData(m_pCmdTableWidget, iRow + 1);
    qDebug()<<"电机组合下移一行:"<<strCurrentRowData<<strDownRowData;
    _SetOneRowData(m_pCmdTableWidget, iRow + 1, strCurrentRowData.split(SPLIT_IN_CMD));
    _SetOneRowData(m_pCmdTableWidget, iRow, strDownRowData.split(SPLIT_IN_CMD));
    ResortTableWidget(m_pCmdTableWidget);
    m_pCmdTableWidget->selectRow(iRow + 1);
}

void CMotorCompose::_SlotCopyBtn()
{
    QTableWidget *pTableWidget = m_pCmdTableWidget;
    int iRow = pTableWidget->currentRow();
    QString strCopy = m_pCopyLineEidt->text();
    qDebug()<<"电机组合行复制:"<<iRow<<strCopy;
    if(strCopy.contains(",") && strCopy.contains(":"))
    {
        QString strLeft = strCopy.split(":").first();
        int iBegin = strLeft.split(",").first().toInt() - 1;
        int iEnd = strLeft.split(",").last().toInt() - 1;
        int iDst = strCopy.split(":").last().toInt() - 1;
        qDebug()<<"电机组合多行复制:"<<iBegin<<iEnd<<iDst;
        if(iBegin < 0 || iEnd < 0 || iDst < 0)
            return;

        QStringList strList;
        for(int i=iBegin; i<=iEnd; i++)
            strList.push_back(_GetOneRowData(pTableWidget, i));

        for(int i=0; i<=iEnd-iBegin; i++)
        {
            qDebug()<<"insert:"<<i+iDst;
            pTableWidget->insertRow(i+iDst);
        }

        for(int i=0; i<strList.size(); i++)
            _SetOneRowData(pTableWidget, iDst+i, strList.at(i).split(SPLIT_IN_CMD));
    }
    else
    {
        if(iRow < 0)
            return;

        int iCopyRow = strCopy.toInt();
        if(iCopyRow <= 0)
            return;

        QString strSelectData = _GetOneRowData(pTableWidget, iRow);
        pTableWidget->insertRow(iCopyRow - 1);
        _SetOneRowData(pTableWidget, iCopyRow - 1, strSelectData.split(SPLIT_IN_CMD));
    }

    ResortTableWidget(pTableWidget);
}

void CMotorCompose::_SlotListBtn()
{
    QPushButton *pBtn = dynamic_cast<QPushButton *>(sender());
    if(nullptr == pBtn)
        return;

    int index = pBtn->property("index").toInt();
    switch (index)
    {
    case 0: _AddOneRow(); break;
    case 1: _DelOneRow(); break;
    case 2: _SaveAndSendData(); break;
    case 3: _ClearTableWidget(); break;
    case 4: _ReadOne(); break;
    case 5: _ReadAll(); break;
    case 6: _ClearDB(); break;
    case 7: _ClearMachine(); break;
    default: break;
    }
}

void CMotorCompose::_SlotMachineComboBoxChanged(int iMachineID)
{
    m_iUiMachineID = iMachineID;
}

void CMotorCompose::_SlotMotorChangedBoxChanged(int index)
{
    m_strMotorCompensateNameList = CMotorDB::GetInstance().GetCompensateNameListByMotorIndex(index);
}

void CMotorCompose::_AddOneRow()
{
    if(nullptr == m_pCmdTableWidget)
        return;

    int iRow = m_pCmdTableWidget->currentRow();
    if(iRow < 0)
        iRow = -1;
    iRow++;

    m_pCmdTableWidget->insertRow(iRow);
    QStringList strList = {QString::number(0)};
    _SetOneRowData(m_pCmdTableWidget, iRow, strList);
    ResortTableWidget(m_pCmdTableWidget);
    m_pCmdTableWidget->selectRow(iRow);
}

void CMotorCompose::_DelOneRow()
{
    if(nullptr == m_pCmdTableWidget || m_pCmdTableWidget->currentRow() < 0)
        return;

    int iRow = m_pCmdTableWidget->currentRow();
    m_pCmdTableWidget->removeRow(iRow);
    ResortTableWidget(m_pCmdTableWidget);
}

void CMotorCompose::_SaveAndSendData()
{
    int size = m_pCmdTableWidget->rowCount();
    if(size < 1)
    {
        ShowInformation(this, m_strTipsText, tr("表格内容为空"));
        return;
    }

    QString strCmdID = m_pCmdIDLineEidt->GetLineEditText();
    QString strCmdName = m_pCmdNameLineEdit->GetLineEditText();
    QString strTime = m_pTimeoutLineEdit->GetLineEditText();
    QString strCmdText = m_pCHTextLineEdit->text();

    if(strCmdText.contains("_"))
    {
        ShowInformation(this, m_strTipsText, tr("指令描述不能含有字符'_'"));
        return;
    }

    if(strCmdID.isEmpty() || strCmdName.isEmpty() || strTime.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("指令ID、指令名称、超时时间不能为空"));
        return;
    }

    QString strDBID = CMotorDB::GetInstance().GetCmdIDByCmdName(strCmdName);
    QString strDBName = CMotorDB::GetInstance().GetCmdNameByCmdID(strCmdID);
    //if(strCmdID != strDBID || strCmdName != strDBName)
    //{
        //ShowInformation(this, gk_strTipsText, tr("指令ID与指令名称和数据库内容不匹配"));
        //return;
    //}

    QString strParamData = QString("%1,%2,%3,%4").arg(m_pMotorComboBox->GetCurrentIndex())
            .arg(strCmdID).arg(strCmdName).arg(strTime);

    QStringList strAllRowDataList;
    for(int i=0; i<size; i++)
    {
        QString strOneRowData = _GetOneRowData(m_pCmdTableWidget, i);
        strAllRowDataList.push_back(strOneRowData);
    }

    QString strCmdContent = strAllRowDataList.join(SPLIT_BETWEEN_CMD);
    qDebug()<<"保存电机组合内容:"<<strCmdID<<strCmdName<<strParamData<<strCmdContent;
    CMotorDB::GetInstance().AddOneCmd(strCmdID, strCmdName, strCmdText, strParamData, strCmdContent);

    QVariant qVarData = strParamData + ";" + strCmdContent;
    QString strCmd = GetJsonCmdString(Method_set_motor_cmd, qVarData);
    qDebug()<<"下发电机组合指令:"<<strCmd;
    SendJsonCmd(m_iUiMachineID, Method_set_motor_cmd, strCmd);

    emit CPublicConfig::GetInstance()->SignalReGetMotorTextIDData();

    QString strNewIDName = QString("%1_%2").arg(strCmdID).arg(strCmdName);
    if(m_strIDNameList.contains(strNewIDName))
        return;
    m_strIDNameList.push_back(strNewIDName);

    int iRow = m_pFileTableWidget->rowCount();
    m_pFileTableWidget->insertRow(iRow);
    _InsertCmdIDName2Table(iRow, strNewIDName);
    ResortTableWidget(m_pFileTableWidget);
    m_pFileTableWidget->selectRow(iRow+1);
}

void CMotorCompose::_ClearTableWidget()
{
    m_pCmdIDLineEidt->SetLineEditText("");
    m_pCmdNameLineEdit->SetLineEditText("");
    m_pTimeoutLineEdit->SetLineEditText("");
    m_pCHTextLineEdit->setText("");

    QList<QComboBox *> comboBoxList = m_pCmdTableWidget->findChildren<QComboBox *>();
    foreach (QComboBox* pComboBox, comboBoxList)
    {
        disconnect(pComboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotCmdComboBoxChanged(int)));
        delete pComboBox;
        pComboBox = nullptr;
    }

    QList<CLineEdit *> lineEditList = m_pCmdTableWidget->findChildren<CLineEdit *>();
    foreach (CLineEdit* pLineEdit, lineEditList)
    {
        delete pLineEdit;
        pLineEdit = nullptr;
    }

    QList<QCheckBox *> checkBoxList = m_pCmdTableWidget->findChildren<QCheckBox *>();
    foreach (QCheckBox *pCheckBox, checkBoxList)
    {
        delete pCheckBox;
        pCheckBox = nullptr;
    }

    qDebug()<<"清空电机组合表格:"<<comboBoxList.size()<<lineEditList.size()<<checkBoxList.size();

    m_pCmdTableWidget->setRowCount(0);
    m_pCmdTableWidget->clearContents();
}

void CMotorCompose::_ReadOne()
{
    QString strID = m_pCmdIDLineEidt->GetLineEditText();
    if(strID.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("请填写读取指令ID"));
        return;
    }

    m_pMotorComboBox->SetCurrentIndex(-1);
    m_pCmdNameLineEdit->SetLineEditText("");
    m_pTimeoutLineEdit->SetLineEditText("");

    QVariantList qVarList = {strID.toInt()};
    QString strCmd = GetJsonCmdString(Method_read_motor_cmd, qVarList);
    qDebug()<<QString("%1#").arg(m_iUiMachineID + 1)<<"读取电机组合单条指令:"<<strID.toInt()<<strCmd;
    SendJsonCmd(m_iUiMachineID, Method_read_motor_cmd, strCmd);
}

void CMotorCompose::_ReadAll()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定读取全部吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    QString strCmd = GetJsonCmdString(Method_read_all_cmds);
    qDebug()<<QString("%1#").arg(m_iUiMachineID + 1)<<"读取电机组合全部指令:"<<strCmd;
    SendJsonCmd(m_iUiMachineID, Method_read_all_cmds, strCmd);
}

void CMotorCompose::_ClearDB()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定清空数据库所有电机指令吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    m_strIDNameList.clear();
    m_pFileTableWidget->clearContents();
    m_pFileTableWidget->setRowCount(0);

    _ClearTableWidget();

    CMotorDB::GetInstance().DeleteAllCmd();

    emit CPublicConfig::GetInstance()->SignalReGetMotorTextIDData();
}

void CMotorCompose::_ClearMachine()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定清空下位机所有电机指令吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    QString strCmd = GetJsonCmdString(Method_reset_all_cmds);
    qDebug()<<QString("%1#").arg(m_iUiMachineID + 1)<<"清空电机组合全部指令:"<<strCmd;
    SendJsonCmd(m_iUiMachineID, Method_reset_all_cmds, strCmd);
}

void CMotorCompose::_SlotCmdComboBoxChanged(int index)
{
    _SetOneRowData(m_pCmdTableWidget, m_pCmdTableWidget->currentRow(), {QString::number(index)});
}

void CMotorCompose::_SlotTComboBoxChanged(int index)
{
    QComboBox *pComboBox = dynamic_cast<QComboBox *>(sender());
    if(nullptr == pComboBox)
        return;

    QTableWidget *pTableWidget = m_pCmdTableWidget;
    int iRow = pComboBox->property("row").toInt();
    QComboBox *pIDComboBox = dynamic_cast<QComboBox *>(pTableWidget->cellWidget(iRow, COL_NUM_CMD));
    if(nullptr == pIDComboBox)
        return;

    int iType = -1;
    int iCmdID = pIDComboBox->currentIndex();
    if(0 == iCmdID)
        iType = T_PARAMS;
    else if(1 == iCmdID)
        iType = SIX_PARAMS;
    else
        return;

    QStringList strDataList = pComboBox->property("list").toStringList();
    QString param2, param3, param4;
    if(strDataList.size() >= 3)
        param2 = strDataList.at(2);
    if(strDataList.size() >= 4)
        param3 = strDataList.at(3);
    if(strDataList.size() >= 5)
        param4 = strDataList.at(4);

    if(0 == index || 1 == index)
    {
        if(T_PARAMS == iType)
        {
            CLineEdit *pLineEdit1 = new CLineEdit(param2);
            pLineEdit1->setPlaceholderText("AMAX");
            pTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pLineEdit1);

            CLineEdit *pLineEdit2 = new CLineEdit(param3);
            pLineEdit2->setPlaceholderText("VMAX");
            pTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, pLineEdit2);

            CLineEdit *pLineEdit3 = new CLineEdit(param4);
            pLineEdit3->setPlaceholderText("DMAX");
            pTableWidget->setCellWidget(iRow, COL_NUM_PARAM4, pLineEdit3);
        }
        else if(SIX_PARAMS == iType)
        {
            QStringList strSixList;
            if(strDataList.size() >= 2)
            {
                strSixList = strDataList;
                strSixList.pop_front();
                strSixList.pop_front();
            }

            CLineEdit *pSixLineEdit = new CLineEdit(strSixList.join(SPLIT_IN_CMD));
            connect(pSixLineEdit, &CLineEdit::SignalPressEvent, this, &CMotorCompose::_SlotSixParamsShow);

            pTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pSixLineEdit);
            pTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, nullptr);
            pTableWidget->setCellWidget(iRow, COL_NUM_PARAM4, nullptr);
        }
    }
    else if(2 == index || 3 == index)
    {
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, nullptr);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, nullptr);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM4, nullptr);
    }
    else if(4 == index)
    {
        QStringList strNumber10List;
        for(int i=0; i<=10; i++)
            strNumber10List.push_back(QString::number(i));

        QComboBox *pNum10ComboBox = new QComboBox;
        pNum10ComboBox->setView(new QListView);
        pNum10ComboBox->addItems(strNumber10List);
        pNum10ComboBox->setCurrentIndex(param2.toInt());
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pNum10ComboBox);

        CLineEdit *pLineEdit = new CLineEdit(param3);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, pLineEdit);

        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM4, nullptr);
    }
    else if(5 == index)
    {
        CLineEdit *pLineEdit1 = new CLineEdit(param2);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pLineEdit1);

        CLineEdit *pLineEdit2 = new CLineEdit(param3);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, pLineEdit2);

        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM4, nullptr);
    }
}

void CMotorCompose::_SlotSixParamsShow()
{
    CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(sender());
    if(nullptr == pLineEdit)
        return;

    int iRow = pLineEdit->property("row").toInt();
    m_pSixParams->Show(iRow, pLineEdit->text());

    m_pThreeParams->setVisible(false);
    m_pOptoCountTouchStop->setVisible(false);
    m_pOptoCompose->setVisible(false);

    m_pSixParams->setVisible(true);
}

void CMotorCompose::_SlotThreeParamsShow()
{
    CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(sender());
    if(nullptr == pLineEdit)
        return;

    int iRow = pLineEdit->property("row").toInt();
    m_pThreeParams->Show(iRow, pLineEdit->text());

    m_pSixParams->setVisible(false);
    m_pOptoCountTouchStop->setVisible(false);
    m_pOptoCompose->setVisible(false);

    m_pThreeParams->setVisible(true);
}

void CMotorCompose::_SlotSetOptoComboBoxChanged(int index)
{
    QComboBox *pComboBox = dynamic_cast<QComboBox *>(sender());
    if(nullptr == pComboBox)
        return;

    int iRow = pComboBox->property("row").toInt();
    QStringList strDataList = pComboBox->property("list").toStringList();
    QString param2, parma3, param4;
    if(strDataList.size() >= 3)
        param2 = strDataList.at(2);
    if(strDataList.size() >= 4)
        parma3 = strDataList.at(3);
    if(strDataList.size() >= 5)
        param4 = strDataList.at(4);

    if(0 == index)
    {
        CLineEdit *pLineEdit = new CLineEdit(param2);
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pLineEdit);

        QStringList strList = {tr("覆盖(0)"), tr("非覆盖(1)")};
        QComboBox *pComboBox = new QComboBox;
        pComboBox->setView(new QListView);
        pComboBox->addItems(strList);
        pComboBox->setCurrentIndex(parma3.toInt());
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, pComboBox);
    }
    else if(1 == index)
    {
        CLineEdit *pLineEdit1 = new CLineEdit(param2);
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pLineEdit1);

        CLineEdit *pLineEdit2 = new CLineEdit(parma3);
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, pLineEdit2);
    }
}

void CMotorCompose::_SlotOptoStopComboBoxChanged(int index)
{
    QComboBox *pComboBox = dynamic_cast<QComboBox *>(sender());
    if(nullptr == pComboBox)
        return;

    int iRow = pComboBox->property("row").toInt();
    QStringList strDataList = pComboBox->property("list").toStringList();
    QString param2;
    if(strDataList.size() >= 3)
        param2 = strDataList.at(2);

    if(0 == index || 1 == index)
    {
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, nullptr);
        return;
    }
    else if(2 <= index && index <= 5)
    {
        QComboBox *pComboBox1 = new QComboBox;
        pComboBox1->setView(new QListView);
        pComboBox1->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        pComboBox1->setMaxVisibleItems(10);
        pComboBox1->setStyle(QStyleFactory::create("Windows"));
        pComboBox1->addItems(m_strMotorCompensateNameList);
        pComboBox1->setCurrentIndex(param2.toInt());
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pComboBox1);
    }
    else if(6 == index || 7 == index)
    {
        QStringList strDirList = {"STOPL_NOSWAP_POL0", "STOPR_NOSWAP_POL0", "STOPL_SWAP_POL0", "STOPR_SWAP_POL0",
                                  "STOPL_NOSWAP_POL1", "STOPR_NOSWAP_POL1", "STOPL_SWAP_POL1", "STOPR_SWAP_POL1"};
        QComboBox *pDirComboBox = new QComboBox;
        pDirComboBox->setView(new QListView);
        pDirComboBox->addItems(strDirList);
        pDirComboBox->setCurrentIndex(param2.toInt());
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pDirComboBox);
    }
}

void CMotorCompose::_SlotSpeedRunComboBoxChanged(int index)
{
    QComboBox *pComboBox = dynamic_cast<QComboBox *>(sender());
    if(nullptr == pComboBox)
        return;

    int iRow = pComboBox->property("row").toInt();
    QStringList strDataList = pComboBox->property("list").toStringList();
    QString param2, param3;
    if(strDataList.size() >= 3)
        param2 = strDataList.at(2);
    if(strDataList.size() >= 4)
        param3 = strDataList.at(3);

    if(0 == index)
    {
        QStringList strList = {tr("HOME"), tr("END")};
        QComboBox *pComboBox2 = new QComboBox;
        pComboBox2->setView(new QListView);
        pComboBox2->addItems(strList);
        pComboBox2->setCurrentIndex(param2.toInt());
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pComboBox2);
    }
    else
    {
        CLineEdit *pLineEdit2 = new CLineEdit(param2);
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pLineEdit2);
    }

    CLineEdit *pLineEdit3 = new CLineEdit(param3);
    m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, pLineEdit3);
}

void CMotorCompose::_SlotGoStepsComboBoxChanged(int index)
{
    QComboBox *pComboBox = dynamic_cast<QComboBox *>(sender());
    if(nullptr == pComboBox)
        return;

    int iRow = pComboBox->property("row").toInt();
    QStringList strDataList = pComboBox->property("list").toStringList();
    QString param2, param3;
    if(strDataList.size() >= 3)
        param2 = strDataList.at(2);
    if(strDataList.size() >= 4)
        param3 = strDataList.at(3);

    if(0 == index || 1 == index)
    {
        CLineEdit *pLineEdit2 = new CLineEdit(param2);
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pLineEdit2);
    }
    else
    {
        QComboBox *pComboBox3 = new QComboBox;
        pComboBox3->setView(new QListView);
        pComboBox3->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        pComboBox3->setMaxVisibleItems(10);
        pComboBox3->setStyle(QStyleFactory::create("Windows"));
        pComboBox3->addItems(m_strMotorCompensateNameList);
        pComboBox3->setCurrentIndex(param3.toInt());
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pComboBox3);
    }
}

void CMotorCompose::_SlotWithDirGoStepsComboBoxChanged(int index)
{
    QComboBox *pComboBox = dynamic_cast<QComboBox *>(sender());
    if(nullptr == pComboBox)
        return;

    int iRow = pComboBox->property("row").toInt();
    QStringList strDataList = pComboBox->property("list").toStringList();
    QString param2, param3, param4;
    if(strDataList.size() >= 3)
        param2 = strDataList.at(2);
    if(strDataList.size() >= 4)
        param3 = strDataList.at(3);
    if(strDataList.size() >= 5)
        param4 = strDataList.at(4);

    if(0 == index)
    {
        QStringList strList = {tr("HOME(0)"), tr("END(1)")};
        QComboBox *pComboBox2 = new QComboBox;
        pComboBox2->setView(new QListView);
        pComboBox2->addItems(strList);
        pComboBox2->setCurrentIndex(param2.toInt());
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pComboBox2);

        CLineEdit *pLineEdit3 = new CLineEdit(param3);
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, pLineEdit3);
    }
    else if(1 == index)
    {
        CLineEdit *pLineEdit2 = new CLineEdit(param2);
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pLineEdit2);

        CLineEdit *pLineEdit3 = new CLineEdit(param3);
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, pLineEdit3);
    }
    else if(2 == index  || 3 == index)
    {
        QStringList strList = {tr("HOME(0)"), tr("END(1)")};
        QComboBox *pComboBox2 = new QComboBox;
        pComboBox2->setView(new QListView);
        pComboBox2->addItems(strList);
        pComboBox2->setCurrentIndex(param2.toInt());
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pComboBox2);

        QComboBox *pComboBox3 = new QComboBox;
        pComboBox3->setView(new QListView);
        pComboBox3->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        pComboBox3->setMaxVisibleItems(10);
        pComboBox3->setStyle(QStyleFactory::create("Windows"));
        pComboBox3->addItems(m_strMotorCompensateNameList);
        pComboBox3->setCurrentIndex(param3.toInt());
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, pComboBox3);
    }
}

void CMotorCompose::_SlotHomeEndMoveComboBoxChanged(int index)
{
    QComboBox *pComboBox1 = dynamic_cast<QComboBox *>(sender());
    if(nullptr == pComboBox1)
        return;

    int iRow = pComboBox1->property("row").toInt();
    QStringList strDataList = pComboBox1->property("list").toStringList();
    QString param2, param3, param4;
    if(strDataList.size() >= 3)
        param2 = strDataList.at(2);
    if(strDataList.size() >= 4)
        param3 = strDataList.at(3);
    if(strDataList.size() >= 5)
        param4 = strDataList.at(4);

    if(0 == index || 1 == index)
    {
        CLineEdit *pLineEdit2 = new CLineEdit(param2);
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pLineEdit2);
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, nullptr);
    }
    else if(2 == index)
    {
        QComboBox *pComboBox2 = new QComboBox;
        pComboBox2->setView(new QListView);
        pComboBox2->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        pComboBox2->setMaxVisibleItems(10);
        pComboBox2->setStyle(QStyleFactory::create("Windows"));
        pComboBox2->addItems(m_strMotorCompensateNameList);
        pComboBox2->setCurrentIndex(param2.toInt());
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pComboBox2);
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, nullptr);
    }
    else if(3 == index || 4 == index)
    {
        CLineEdit *pLineEdit2 = new CLineEdit(param2);
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pLineEdit2);

        QStringList strThreeList;
        if(strDataList.size() >= 3)
        {
            strThreeList = strDataList;
            strThreeList.pop_front();
            strThreeList.pop_front();
            strThreeList.pop_front();
        }

        CLineEdit *pLineEdit3 = new CLineEdit(strThreeList.join(SPLIT_IN_CMD));
        pLineEdit3->setProperty("row", iRow);
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, pLineEdit3);
        connect(pLineEdit3, &CLineEdit::SignalPressEvent, this, &CMotorCompose::_SlotThreeParamsShow);
    }
    else if(5 == index)
    {
        QComboBox *pComboBox2 = new QComboBox;
        pComboBox2->setView(new QListView);
        pComboBox2->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        pComboBox2->setMaxVisibleItems(10);
        pComboBox2->setStyle(QStyleFactory::create("Windows"));
        pComboBox2->addItems(m_strMotorCompensateNameList);
        pComboBox2->setCurrentIndex(param2.toInt());
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pComboBox2);

        QStringList strThreeList;
        if(strDataList.size() >= 3)
        {
            strThreeList = strDataList;
            strThreeList.pop_front();
            strThreeList.pop_front();
            strThreeList.pop_front();
        }

        CLineEdit *pLineEdit3 = new CLineEdit(strThreeList.join(SPLIT_IN_CMD));
        pLineEdit3->setProperty("row", iRow);
        m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, pLineEdit3);
        connect(pLineEdit3, &CLineEdit::SignalPressEvent, this, &CMotorCompose::_SlotThreeParamsShow);
    }

    m_pCmdTableWidget->setCellWidget(iRow, COL_NUM_PARAM4, nullptr);
}

void CMotorCompose::_SlotOptoCountTouchStopShow()
{
    CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(sender());
    if(nullptr == pLineEdit)
        return;

    int iRow = pLineEdit->property("row").toInt();
    m_pOptoCountTouchStop->Show(iRow, pLineEdit->text());

    m_pSixParams->setVisible(false);
    m_pThreeParams->setVisible(false);
    m_pOptoCompose->setVisible(false);

    m_pOptoCountTouchStop->setVisible(true);
}

void CMotorCompose::_SlotOptoComposeShow()
{
    CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(sender());
    if(nullptr == pLineEdit)
        return;

    int iRow = pLineEdit->property("row").toInt();
    QComboBox *pComboBox = dynamic_cast<QComboBox *>(m_pCmdTableWidget->cellWidget(iRow, COL_NUM_PARAM1));
    int iType = 0;
    if(pComboBox)
        iType = pComboBox->currentIndex();
    m_pOptoCompose->Show(iRow, iType, pLineEdit->text());

    m_pSixParams->setVisible(false);
    m_pThreeParams->setVisible(false);
    m_pOptoCountTouchStop->setVisible(false);

    m_pOptoCompose->setVisible(true);
}

void CMotorCompose::_InitTextList()
{
    m_strMotorNameList = CPublicConfig::GetInstance()->GetMotorNameList();
    m_strMotorNameList.push_back(tr("组合电机"));

    m_strCmdNameList<<tr("T型参数")<<tr("六点参数")<<tr("设置光耦")<<tr("冲突光耦数组")
                   <<tr("光耦判定")<<tr("光耦触停")<<tr("光耦触发跳转")<<tr("数量循环")
                  <<tr("时间循环")<<tr("速度运行")<<tr("走指定位")<<tr("带方向走指定位")
                 <<tr("移动指定步")<<tr("软停")<<tr("硬停")<<tr("返初始位")
                <<tr("走标记位")<<tr("重置绝对位置")<<tr("Stepclock模式")<<tr("S型曲线")
               <<tr("设置参数")<<tr("读取参数")<<tr("指定复位")<<tr("重置运动方向")
              <<tr("到位检测")<<tr("停止检测")<<tr("组合")<<tr("定时器")
             <<tr("结束(成功)")<<tr("跳转")<<tr("HOME移动")<<tr("END移动")
            <<tr("结束(失败)")<<tr("HOME速度模式")<<tr("END速度模式")<<tr("速度模式")
           <<tr("预检到位")<<tr("S型参数")<<tr("等待")<<tr("触发")
          <<tr("光耦计数判定")<<tr("光耦计数触停")<<tr("光耦计数触发跳转")<<tr("组合光耦")
         <<tr("高优先级光耦触停");
}

void CMotorCompose::_LoadName2Table()
{
    m_pFileTableWidget->clearContents();
    m_pFileTableWidget->setRowCount(0);
    m_strIDNameList.clear();

    QList<QStringList> strCmdIDNameList = CMotorDB::GetInstance().GetCmdIDNameList();
    for(int i=0; i<strCmdIDNameList.size(); i++)
    {
        QStringList strOneIDName = strCmdIDNameList.at(i);
        if(strOneIDName.size() < 2)
            continue;

        m_strIDNameList << QString("%1_%2").arg(strOneIDName.at(0)).arg(strOneIDName.at(1));
    }

    if(m_strIDNameList.size() <= 0)
        return;

    m_pFileTableWidget->setRowCount(m_strIDNameList.size());
    for(int i=0; i<m_strIDNameList.size(); i++)
       _InsertCmdIDName2Table(i, m_strIDNameList.at(i));
}

void CMotorCompose::_InsertCmdIDName2Table(int iRow, QString strIDName)
{
    QTableWidgetItem* pIdItem = new QTableWidgetItem;
    pIdItem->setText(QString::number(iRow + 1));
    pIdItem->setTextAlignment(Qt::AlignCenter);
    m_pFileTableWidget->setItem(iRow, 0, pIdItem);

    QTableWidgetItem* pNameItem = new QTableWidgetItem;
    pNameItem->setText(strIDName);
    pNameItem->setTextAlignment(Qt::AlignCenter);
    m_pFileTableWidget->setItem(iRow, 1, pNameItem);
}

void CMotorCompose::_SetOneRowData(QTableWidget *pTableWidget, int iRow, const QStringList &oneList)
{
    qDebug()<<Q_FUNC_INFO<<iRow<<oneList;
    if(oneList.isEmpty())
        return;
    QString strCmdID = oneList.at(0);
    if(strCmdID.isEmpty())
        return;

    QString param1, param2, param3, param4;
    if(oneList.size() >= 2)
        param1 = oneList.at(1);
    if(oneList.size() >= 3)
        param2 = oneList.at(2);
    if(oneList.size() >= 4)
        param3 = oneList.at(3);
    if(oneList.size() >= 5)
        param4 = oneList.at(4);

    QTableWidgetItem *pIdItem = new QTableWidgetItem;
    pIdItem->setText(QString::number(iRow + 1));
    pIdItem->setTextAlignment(Qt::AlignCenter);
    pTableWidget->setItem(iRow, COL_NUM_ID, pIdItem);

    int iCmdID = strCmdID.toInt();
    QComboBox *pComboBoxID = dynamic_cast<QComboBox *>(pTableWidget->cellWidget(iRow, COL_NUM_CMD));
    if(nullptr != pComboBoxID)
    {
        disconnect(pComboBoxID, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotCmdComboBoxChanged(int)));
        pComboBoxID->deleteLater();
    }
    pComboBoxID= new QComboBox;
    pComboBoxID->setMinimumWidth(200);
    pComboBoxID->setView(new QListView);
    pComboBoxID->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    pComboBoxID->setMaxVisibleItems(10);
    pComboBoxID->setStyle(QStyleFactory::create("Windows"));
    pComboBoxID->addItems(m_strCmdNameList);
    pComboBoxID->setCurrentIndex(iCmdID);
    connect(pComboBoxID, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotCmdComboBoxChanged(int)));
    pTableWidget->setCellWidget(iRow, COL_NUM_CMD, pComboBoxID);
    pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, nullptr);
    pTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, nullptr);
    pTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, nullptr);
    pTableWidget->setCellWidget(iRow, COL_NUM_PARAM4, nullptr);

    switch (iCmdID)
    {
    case 0: //T型参数
    case 1: //六点参数
    {
        QStringList strParams = {tr("自定义"), tr("可替换"), tr("复位"), tr("运行"), tr("挡位"), tr("可替换档位")};
        QComboBox *pComboxBox1 = new QComboBox;
        pComboxBox1->setProperty("row", iRow);
        pComboxBox1->setProperty("list", oneList);
        pComboxBox1->setView(new QListView);
        pComboxBox1->addItems(strParams);
        pComboxBox1->setCurrentIndex(param1.toInt());
        connect(pComboxBox1, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotTComboBoxChanged(int)));
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pComboxBox1);
        emit pComboxBox1->currentIndexChanged(param1.toInt());

        break;
    }
    case 2: //设置光耦
    {
        QStringList strList = {tr("自定义"), tr("可替换")};
        QComboBox *pComboBox1 = new QComboBox;
        pComboBox1->setProperty("row", iRow);
        pComboBox1->setProperty("list", oneList);
        pComboBox1->setView(new QListView);
        pComboBox1->addItems(strList);
        pComboBox1->setCurrentIndex(param1.toInt());
        connect(pComboBox1, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotSetOptoComboBoxChanged(int)));
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pComboBox1);
        emit pComboBox1->currentIndexChanged(param1.toInt());

        break;
    }
    case 3: //冲突光耦数组 需确认
    {
        CLineEdit *pLineEdit1 = new CLineEdit(param1);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pLineEdit1);

        break;
    }
    case 4: //光耦判定
    case 6: //光耦触发跳转
    case 29: //跳转
    {
        QStringList strList = {tr("REL"), tr("ABS")};
        QComboBox *pComboBox1 = new QComboBox;
        pComboBox1->setView(new QListView);
        pComboBox1->addItems(strList);
        pComboBox1->setCurrentIndex(param1.toInt());
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pComboBox1);

        CLineEdit *pLineEdit2 = new CLineEdit(param2);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pLineEdit2);

        break;
    }
    case 5:  //光耦触停
    case 44: //高优先级光耦触停
    {
        QStringList strList = {tr("硬停"), tr("软停"), tr("硬停补偿"), tr("软停补偿"), tr("硬停偏移补偿"),
                               tr("软停偏移补偿"), tr("TMC硬停"), tr("TMC软停")};
        QComboBox *pComboBox1 = new QComboBox;
        pComboBox1->setProperty("row", iRow);
        pComboBox1->setProperty("list", oneList);
        pComboBox1->setView(new QListView);
        pComboBox1->addItems(strList);
        pComboBox1->setCurrentIndex(param1.toInt());
        connect(pComboBox1, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotOptoStopComboBoxChanged(int)));
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pComboBox1);
        emit pComboBox1->currentIndexChanged(param1.toInt());

        break;
    }
    case 7: //数量循环
    case 8: //时间循环
    {
        QStringList strList = {tr("自定义"), tr("可替换")};
        QComboBox *pComboBox1 = new QComboBox;
        pComboBox1->setView(new QListView);
        pComboBox1->addItems(strList);
        pComboBox1->setCurrentIndex(param1.toInt());
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pComboBox1);

        CLineEdit *pLineEdit2 = new CLineEdit(param2);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pLineEdit2);

        CLineEdit *pLineEdit3 = new CLineEdit(param3);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, pLineEdit3);

        break;
    }
    case 9: //速度运行
    {
        QStringList strList = {tr("自定义"), tr("可替换")};
        QComboBox *pComboBox1 = new QComboBox;
        pComboBox1->setProperty("row", iRow);
        pComboBox1->setProperty("list", oneList);
        pComboBox1->setView(new QListView);
        pComboBox1->addItems(strList);
        pComboBox1->setCurrentIndex(param1.toInt());
        connect(pComboBox1, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotSpeedRunComboBoxChanged(int)));
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pComboBox1);
        emit pComboBox1->currentIndexChanged(param1.toInt());

        break;
    }
    case 10: //走指定位
    {
        QStringList strList = {tr("自定义"), tr("可替换"), tr("补偿替换")};
        QComboBox *pComboBox1 = new QComboBox;
        pComboBox1->setProperty("row", iRow);
        pComboBox1->setProperty("list", oneList);
        pComboBox1->setView(new QListView);
        pComboBox1->addItems(strList);
        pComboBox1->setCurrentIndex(param1.toInt());
        connect(pComboBox1, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotGoStepsComboBoxChanged(int)));
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pComboBox1);
        emit pComboBox1->currentIndexChanged(param1.toInt());

        break;
    }
    case 11: //带方向走指定位
    case 12: //移动指定步
    {
        QStringList strList = {tr("自定义"), tr("可替换"), tr("补偿替换"), tr("xLatch偏移补偿替换")};
        QComboBox *pComboBox1 = new QComboBox;
        pComboBox1->setProperty("row", iRow);
        pComboBox1->setProperty("list", oneList);
        pComboBox1->setView(new QListView);
        pComboBox1->addItems(strList);
        pComboBox1->setCurrentIndex(param1.toInt());
        connect(pComboBox1, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotWithDirGoStepsComboBoxChanged(int)));
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pComboBox1);
        emit pComboBox1->currentIndexChanged(param1.toInt());

        break;
    }
    case 13: //软停
    case 14: //硬停
    case 15: //返初始位
    case 16: //走标记位
    case 17: //重置绝对位置
    case 18: //Stepclock模式
    case 19: //S型曲线
    case 22: //指定复位
    case 24: //到位检测
    case 25: //停止检测
    case 28: //结束(成功)
    case 32: //结束(失败)
    case 33: //HOME速度模式
    case 34: //END速度模式
        break;
    case 20: //设置参数
    case 27: //定时器 已弃用
    {
        CLineEdit *pLineEdit1 = new CLineEdit(param1);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pLineEdit1);

        CLineEdit *pLineEdit2 = new CLineEdit(param2);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pLineEdit2);

        break;
    }
    case 21: //读取参数
    case 36: //预检到位
    case 38: //等待
    case 39: //触发
    {
        CLineEdit *pLineEdit1 = new CLineEdit(param1);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pLineEdit1);

        break;
    }
    case 23: //重置运动方向
    case 35: //速度模式
    {
        QStringList strList = {tr("HOME"), tr("END")};
        QComboBox *pComboBox1 = new QComboBox;
        pComboBox1->setView(new QListView);
        pComboBox1->addItems(strList);
        pComboBox1->setCurrentIndex(param1.toInt());
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pComboBox1);

        break;
    }
    case 26: //组合 需确认
    {
        CLineEdit *pLineEdit1 = new CLineEdit(param1);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pLineEdit1);

        break;
    }
    case 30: //HOME移动
    case 31: //END移动
    {
        QStringList strList = {tr("自定义"), tr("可替换"), tr("补偿替换"), tr("自适应速度"),
                               tr("自适应可替换"), tr("自适应补偿替换")};
        QComboBox *pComboBox1 = new QComboBox;
        pComboBox1->setProperty("row", iRow);
        pComboBox1->setProperty("list", oneList);
        pComboBox1->setView(new QListView);
        pComboBox1->addItems(strList);
        pComboBox1->setCurrentIndex(param1.toInt());
        connect(pComboBox1, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotHomeEndMoveComboBoxChanged(int)));
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pComboBox1);
        emit pComboBox1->currentIndexChanged(param1.toInt());

        break;
    }
    case 37: //S型参数 需确认
    {
        CLineEdit *pLineEdit1 = new CLineEdit(param1);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pLineEdit1);

        break;
    }
    case 40:
    case 42:
    {
        QStringList strList = {tr("自定义"), tr("可替换")};
        QComboBox *pComboBox1 = new QComboBox;
        pComboBox1->setView(new QListView);
        pComboBox1->addItems(strList);
        pComboBox1->setCurrentIndex(param1.toInt());
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pComboBox1);

        CLineEdit *pLineEdit2 = new CLineEdit(param2);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pLineEdit2);

        QStringList strList3 = {tr("REL"), tr("ABS")};
        QComboBox *pComboBox3 = new QComboBox;
        pComboBox3->setView(new QListView);
        pComboBox3->addItems(strList3);
        pComboBox3->setCurrentIndex(param3.toInt());
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, pComboBox3);

        CLineEdit *pLineEdit4 = new CLineEdit(param4);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM4, pLineEdit4);

        break;
    }
    case 41: //光耦计数触停
    {
        QStringList strOptoCountStopList;
        if(oneList.size() >= 1)
        {
            strOptoCountStopList = oneList;
            strOptoCountStopList.pop_front();
        }

        CLineEdit *pLineEdit1 = new CLineEdit(strOptoCountStopList.join(SPLIT_IN_CMD));
        pLineEdit1->setProperty("row", iRow);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pLineEdit1);
        connect(pLineEdit1, &CLineEdit::SignalPressEvent, this, &CMotorCompose::_SlotOptoCountTouchStopShow);

        break;
    }
    case 43: //组合光耦
    {
        QStringList strList = {tr("自定义"), tr("可替换")};
        QComboBox *pComboBox1 = new QComboBox;
        pComboBox1->setView(new QListView);
        pComboBox1->addItems(strList);
        pComboBox1->setCurrentIndex(param1.toInt());
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pComboBox1);

        QStringList strOptoComposeList;
        if(oneList.size() >= 2)
        {
            strOptoComposeList = oneList;
            strOptoComposeList.pop_front();
            strOptoComposeList.pop_front();
        }

        CLineEdit *pLineEdit2 = new CLineEdit(strOptoComposeList.join(SPLIT_IN_CMD));
        pLineEdit2->setProperty("row", iRow);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pLineEdit2);
        connect(pLineEdit2, &CLineEdit::SignalPressEvent, this, &CMotorCompose::_SlotOptoComposeShow);

        break;
    }
    default:
        break;
    }

    QList<QComboBox *> pComboBoxList = pTableWidget->findChildren<QComboBox *>();
    for(int i=0; i<pComboBoxList.size(); i++)
        pComboBoxList.at(i)->installEventFilter(this);
}

QString CMotorCompose::_GetOneRowData(QTableWidget *pTableWidget, int iRow)
{
    if(nullptr == pTableWidget || iRow < 0 || iRow > pTableWidget->rowCount())
        return "";

    QStringList strList;
    QComboBox *pIDComboBox = dynamic_cast<QComboBox *>(pTableWidget->cellWidget(iRow, COL_NUM_CMD));
    if(nullptr == pIDComboBox)
        return "";

    int iCmdID = pIDComboBox->currentIndex();
    strList.push_back(QString::number(iCmdID));

    for(int iColumn=COL_NUM_PARAM1; iColumn<=COL_NUM_PARAM4; iColumn++)
    {
        QWidget *pWidget = pTableWidget->cellWidget(iRow, iColumn);
        if(nullptr == pWidget)
            continue;

        QComboBox *pComboBox = dynamic_cast<QComboBox *>(pWidget);
        if(pComboBox)
            strList.push_back(QString::number(pComboBox->currentIndex()));

        CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(pWidget);
        if(pLineEdit)
            strList.push_back(pLineEdit->text());
    }

    QString strData = strList.join(SPLIT_IN_CMD);
    qDebug()<<"电机组合_GetOneRowData:"<<iRow<<strData;

    return strData;
}

void CMotorCompose::_InitWidget()
{
    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(80, 50);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMachineComboBoxChanged(int)));

    m_pMotorComboBox = new CLabelComboBox(tr("电机:"));
    m_pMotorComboBox->SetComboBoxFixedSize(190, 50);
    m_pMotorComboBox->SetComboBoxList(m_strMotorNameList);
    connect(m_pMotorComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMotorChangedBoxChanged(int)));

    m_pCmdIDLineEidt = new CLabelLineEdit(tr("指令ID:"));
    m_pCmdIDLineEidt->SetLineEditFixedSize(100, 50);

    m_pCmdNameLineEdit = new CLabelLineEdit(tr("指令名称:"));
    m_pCmdNameLineEdit->SetLineEditFixedSize(100, 50);

    m_pTimeoutLineEdit = new CLabelLineEdit(tr("超时时间:"));
    m_pTimeoutLineEdit->SetLineEditFixedSize(100, 50);

    m_pCHTextLineEdit = new CLineEdit;
    m_pCHTextLineEdit->setFixedHeight(50);
    m_pCHTextLineEdit->setMinimumWidth(200);
    m_pCHTextLineEdit->setPlaceholderText(tr("指令中文描述"));
    m_pCHTextLineEdit->setAlignment(Qt::AlignCenter);

    QStringList strCmdList = {tr("序号"), tr("命令"), tr("参数1"), tr("参数2"), tr("参数3"), tr("参数4")};
    m_pCmdTableWidget = new QTableWidget;
    m_pCmdTableWidget->setColumnCount(strCmdList.size());
    m_pCmdTableWidget->setHorizontalHeaderLabels(strCmdList);

    QHeaderView* pVerticalHeader = m_pCmdTableWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(50);
    QHeaderView* pHorizontalHeader = m_pCmdTableWidget->horizontalHeader();
    pHorizontalHeader->resizeSection(0, 70);
    pHorizontalHeader->resizeSection(1, 200);
    //pHorizontalHeader->resizeSection(2, 150);
    //pHorizontalHeader->resizeSection(3, 120);
    pHorizontalHeader->resizeSection(4, 110);
    pHorizontalHeader->resizeSection(5, 110);
    pHorizontalHeader->setSectionResizeMode(2, QHeaderView::Stretch);
    pHorizontalHeader->setSectionResizeMode(3, QHeaderView::Stretch);

    m_pCmdTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pCmdTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pCmdTableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_pCmdTableWidget->setShowGrid(true);
    m_pCmdTableWidget->setFocusPolicy(Qt::NoFocus);

    QString strQSS = "QLineEdit{border-radius: 0px; border: 1px solid #CAD2DC; font-size: 20px;}"
                     "QComboBox{border-radius: 0px; border: 1px solid #CAD2DC; font-size: 20px;}"
                     "QCheckBox::indicator{width: 35px; height: 35px; subcontrol-position:center  center;}";
    m_pCmdTableWidget->setStyleSheet(strQSS);

    m_pFindLineEdit = new CLineEdit;
    m_pFindLineEdit->setFixedSize(110, 50);

    m_pFindBtn = new QPushButton(tr("搜索"));
    m_pFindBtn->setFixedSize(110, 50);
    connect(m_pFindBtn, &QPushButton::clicked, this, &CMotorCompose::_SlotFindBtn);

    QStringList strFileList = {tr("序号"), tr("指令")};
    m_pFileTableWidget = new QTableWidget;
    m_pFileTableWidget->setFixedWidth(270);
    m_pFileTableWidget->setColumnCount(strFileList.size());
    m_pFileTableWidget->setHorizontalHeaderLabels(strFileList);

    QHeaderView* pFileVerticalHeader = m_pFileTableWidget->verticalHeader();
    pFileVerticalHeader->setDefaultSectionSize(50);
    QHeaderView* pFileHorizontalHeader = m_pFileTableWidget->horizontalHeader();
    pFileHorizontalHeader->resizeSection(0, 70);
    pFileHorizontalHeader->resizeSection(1, 140);
    pFileHorizontalHeader->setStretchLastSection(true);

    m_pFileTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pFileTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pFileTableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_pFileTableWidget->setShowGrid(true);

    int iWidth = 130;
    m_pLoadBtn = new QPushButton(tr("加载"));
    m_pLoadBtn->setFixedSize(iWidth, 50);
    connect(m_pLoadBtn, &QPushButton::clicked, this, &CMotorCompose::_SlotLoadBtn);

    m_pDelBtn = new QPushButton(tr("删除"));
    m_pDelBtn->setFixedSize(iWidth, 50);
    connect(m_pDelBtn, &QPushButton::clicked, this, &CMotorCompose::_SlotDelBtn);

    m_pMoveUpBtn = new QPushButton(tr("上移"));
    m_pMoveUpBtn->setFixedSize(iWidth, 50);
    connect(m_pMoveUpBtn, &QPushButton::clicked, this, &CMotorCompose::_SlotMoveUpBtn);

    m_pMoveDownBtn = new QPushButton(tr("下移"));
    m_pMoveDownBtn->setFixedSize(iWidth, 50);
    connect(m_pMoveDownBtn, &QPushButton::clicked, this, &CMotorCompose::_SlotMoveDownBtn);

    m_pCopyLineEidt = new CLineEdit;
    m_pCopyLineEidt->setFixedSize(iWidth, 50);

    m_pCopyBtn = new QPushButton(tr("行复制"));
    m_pCopyBtn->setFixedSize(iWidth, 50);
    connect(m_pCopyBtn, &QPushButton::clicked, this, &CMotorCompose::_SlotCopyBtn);

    QStringList strBtnTextList = {tr("加一行"), tr("减一行"), tr("保存下发"), tr("清除表格"), tr("读取"),
                                  tr("读取全部"), tr("清空数据库"), tr("清空下位机")};
    for(int i=0; i<strBtnTextList.size(); i++)
    {
        QPushButton *pBtn = new QPushButton(strBtnTextList.at(i));
        pBtn->setProperty("index", i);
        pBtn->setFixedSize(125, 50);
        m_pBtnList.push_back(pBtn);
        connect(pBtn, &QPushButton::clicked, this, &CMotorCompose::_SlotListBtn);
    }

    m_pBtnList.at(strBtnTextList.size() - 1)->setFixedSize(140, 50);
    m_pBtnList.at(strBtnTextList.size() - 2)->setFixedSize(140, 50);
    if(eLanguage_Spanish == gk_iLanguage)
        m_pBtnList.at(strBtnTextList.size() - 1)->setFixedSize(215, 50);
    else if(eLanguage_German == gk_iLanguage)
        m_pBtnList.at(strBtnTextList.size() - 1)->setFixedSize(160, 50);
    else if(eLanguage_Italian == gk_iLanguage)
        m_pBtnList.at(strBtnTextList.size() - 1)->setFixedSize(320, 50);
}

void CMotorCompose::_InitLayout()
{
    QHBoxLayout *pTop1Layout = new QHBoxLayout;
    pTop1Layout->setMargin(0);
    pTop1Layout->setSpacing(10);
    pTop1Layout->addWidget(m_pMachineComboBox);
    pTop1Layout->addWidget(m_pMotorComboBox);
    pTop1Layout->addWidget(m_pCmdIDLineEidt);
    pTop1Layout->addWidget(m_pCmdNameLineEdit);
    pTop1Layout->addWidget(m_pTimeoutLineEdit);
    pTop1Layout->addStretch(1);

    QVBoxLayout *pLeftLayout = new QVBoxLayout;
    pLeftLayout->setMargin(0);
    pLeftLayout->setSpacing(0);
    pLeftLayout->addWidget(m_pCHTextLineEdit);
    pLeftLayout->addSpacing(10);
    pLeftLayout->addWidget(m_pCmdTableWidget);

    QHBoxLayout *pFindLayout = new QHBoxLayout;
    pFindLayout->setMargin(0);
    pFindLayout->setSpacing(0);
    pFindLayout->addWidget(m_pFindLineEdit);
    pFindLayout->addSpacing(10);
    pFindLayout->addWidget(m_pFindBtn);

    QGridLayout *pGridLayout = new QGridLayout;
    pGridLayout->setMargin(0);
    pGridLayout->setVerticalSpacing(10);
    pGridLayout->setHorizontalSpacing(10);
    pGridLayout->addWidget(m_pLoadBtn, 1, 0);
    pGridLayout->addWidget(m_pDelBtn, 1, 1);
    pGridLayout->addWidget(m_pMoveUpBtn, 2, 0);
    pGridLayout->addWidget(m_pMoveDownBtn, 2, 1);
    pGridLayout->addWidget(m_pCopyLineEidt, 3, 1);
    pGridLayout->addWidget(m_pCopyBtn, 3, 0);

    QVBoxLayout *pRightLayout = new QVBoxLayout;
    pRightLayout->setMargin(0);
    pRightLayout->setSpacing(10);
    pRightLayout->addLayout(pFindLayout);
    pRightLayout->addWidget(m_pFileTableWidget);
    pRightLayout->addLayout(pGridLayout);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(10);
    for(int i=0; i<m_pBtnList.size(); i++)
        pBtnLayout->addWidget(m_pBtnList.at(i));
    pBtnLayout->addStretch(1);

    QHBoxLayout *pMidLayout = new QHBoxLayout;
    pMidLayout->setMargin(0);
    pMidLayout->setSpacing(0);
    pMidLayout->addLayout(pLeftLayout);
    pMidLayout->addSpacing(10);
    pMidLayout->addLayout(pRightLayout);

    QVBoxLayout *pMainLayout = new QVBoxLayout;
    pMainLayout->setMargin(0);
    pMainLayout->setSpacing(10);
    pMainLayout->addSpacing(10);
    pMainLayout->addLayout(pTop1Layout);
    pMainLayout->addLayout(pMidLayout);
    pMainLayout->addLayout(pBtnLayout);
    this->setLayout(pMainLayout);
}
