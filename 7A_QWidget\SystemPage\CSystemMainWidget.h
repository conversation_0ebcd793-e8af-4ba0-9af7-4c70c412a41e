#ifndef CSYSTEMMAINWIDGET_H
#define CSYSTEMMAINWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-25
  * Description: 系统主页
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QPushButton>
#include <QGridLayout>

#include "CSysButton.h"
#include "CHLabelTitleWidget.h"

class CSystemMainWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CSystemMainWidget(QWidget *parent = nullptr);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;

signals:
    void SignalTitleIndex(int index);

private slots:
    void _SlotSysBtn();

private:
    void _InitWidget();
    void _InitLayout();

private:
    CHLabelTitleWidget *m_pCHLabelTitleWidget;
    QList<CSysButton *> m_pSysBtnList;
    QLabel *m_pBackgroundLabel;
    QGridLayout *m_pGridLayout;
    int m_iLastUserLevel;

    enum{eNormal, eNetwork, eUser, eLog, eUpdate, eInfo, eSelfTest, eCalibrate, ePrint, eMaintain, eFactory};
};

#endif // CSYSTEMMAINWIDGET_H
