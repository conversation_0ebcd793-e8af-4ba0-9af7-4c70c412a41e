﻿#ifndef INPUTMETHOD_H
#define INPUTMETHOD_H

#include <QObject>

class InputMethod:public QObject
{
    Q_OBJECT

public:
    InputMethod();

    ~InputMethod();

    void SetPagingEnabled(const bool&);

    void SetGeometry(int x, int y, int w, int h);

    void SetColor(const QString& backgroundColor, const QString& buttonColor,
                  const QString& textColor, const QString& pressedColor);

    void SetFontFamily(const QString& fontFamily);

    void SetFontPixelSize(const int& size);

    void SetPageSize(const int& size);

protected:
    bool eventFilter(QObject *obj, QEvent *event);

signals:
    void SignalKeyPress(int,int);

    void SignalKeyRelese(int);

    void SinglaReturnPressed();

private slots:
    void SlotCharacterGenerated(QChar c);

    void SlotEditFinished();

private:
    void HandleMouseButtonRelease(QObject *obj);

    bool HandleKeyPress(QObject *obj, QEvent *event);

    void HandleKeyRelease(QObject *obj, QEvent *event);

private:
    class PrivateData;
    PrivateData *const md;
};

#endif // INPUTMETHOD_H
