#ifndef CSYSFIRSTTITLE_H
#define CSYSFIRSTTITLE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-02
  * Description: 系统一级标题 xxx/xxx
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include "CPressLabel.h"

class CSysFirstTitleWidget : public QWidget
{
    Q_OBJECT
public:
    CSysFirstTitleWidget(const QString &strTitle1, const QString &strTitle2, QWidget *parent = nullptr);

signals:
    void SignalTitlePress();

private:
    CPressLabel *m_pIconLabel;
    CPressLabel *m_pTitleLabel1;
    QLabel *m_pSlashLabel;
    QLabel *m_pTitleLabel2;
};

#endif // CSYSFIRSTTITLE_H
