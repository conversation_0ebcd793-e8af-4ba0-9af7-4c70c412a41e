QLabel
{
    color: #6B788F;
    font-size: 24px;
    font-weight: 400;
    font-family: "Source Han Sans CN";
}

QLabel#TitleIconLabel
{
   border-radius: 3px;
   background-color: #3D78E5;
}

QLabel#TitleTextLabel
{
   color: #353E4E;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
}

/*一级标题*/
QLabel#SysTitleLabel1
{
    color: #6B788F;
    font-size: 24px;
    font-weight: 400;
    font-family: "Source Han Sans CN";
}

/*一级标题*/
QLabel#SysTitleLabel2
{
    color: #353E4E;
    font-size: 24px;
    font-weight: 500;
    font-family: "Source Han Sans CN";
}

QLineEdit
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    padding-left: 20px;
    border-radius: 28px;
    border: 0px solid #A1ABBB;
    background-color: #F3F8FF;
}
QLineEdit:disabled
{
    background-color: #A2A2A2;
}

QGroupBox
{
   border-radius: 32px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
       stop: 0 #DEE8FB, stop: 0.15 #FFF);
}

QPushButton
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #8490FF, stop: 1 #3D78E5);
}
QPushButton:focus{outline: none;}
QPushButton:pressed
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #6670c7, stop: 1 #3361b8);
}

QPushButton#CancelBtn
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 2px solid #3D78E5;
   background-color: #FFF;
}
QPushButton#CancelBtn:hover
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 3px solid #3D78E5;
   background-color: #FFF;
}
QPushButton#CancelBtn:pressed
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 4px solid #3D78E5;
   background-color: #FFF;
}

QComboBox
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    padding-left: 20px;
    border-radius: 28px;
    background-color: #F3F8FF;
}

QComboBox::drop-down
{
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 60px;
    font-weight: 500;
    border-left: 0px solid red;
}
QComboBox::down-arrow
{
    width: 32px;
    height: 32px;
    image: url(:/image/ico/login/commod.png);
    padding: 0px 20px 0px 0px;
}
QComboBox QAbstractItemView
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    selection-background-color: #248CEB;
}
QComboBox QAbstractItemView::item
{
    min-height: 56px;/*下拉列表的行高，也可以看做行距*/
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
}


QTableWidget
{
    color: #353E4E;
    font-size: 20px;
    font-family: "Source Han Sans CN";
    selection-background-color: #60C8FF;
    alternate-background-color: #F2F2F2;
    border: 0px solid #D6DFE9;
    border-radius: 0px;
    background-color: #fff;
}

QHeaderView::section
{
    color: #353E4E;
    font-size: 24px;
    font-weight: 500;
    font-family:"Source Han Sans CN";
    border: 0px solid #D6DFE9;
    border-radius: 0px;
    background-color: #EAEDF6;
    height: 50px;
}

QScrollBar:vertical
{
    width: 30px;
    background: #F0F0F0;
    padding-top: 30px;
    padding-bottom: 30px;
}

QScrollBar::handle:vertical
{
    width: 30px;
    background: #B6B6B6;
    min-height: 35px;
}
