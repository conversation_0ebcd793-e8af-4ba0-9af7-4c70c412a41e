#include "rollingbox.h"
#include <QScrollBar>
#include <QDebug>

#define MOVE_MIN (10)

RollingBox::RollingBox(QWidget* parent):
    QListView(parent)
{
    _InitUi();
    m_moveFlag = false;
}
void RollingBox::_InitUi()
{
    QFont font;
    font.setPointSize(20);
    font.setBold(true);
    font.setWeight(50);
    this->setFont(font);
    this->setFlow(QListView::LeftToRight);
    this->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    this->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    this->setHorizontalScrollMode(QAbstractItemView::ScrollPerPixel);
    this->setEditTriggers(QAbstractItemView::NoEditTriggers);
    this->setFocusPolicy(Qt::NoFocus);
    this->setSelectionMode(QAbstractItemView::NoSelection);
    this->setSpacing(5);
//    this->setItemDelegate(new ItemDelegate());
}

void RollingBox::mousePressEvent(QMouseEvent *e)
{
    m_mousePressPosX = e->pos().x();
    return QListView::mousePressEvent(e);
}

void RollingBox::mouseMoveEvent(QMouseEvent *e)
{
    int scroolLX = m_mousePressPosX - e->pos().x();
    if(scroolLX > MOVE_MIN)
    {
        m_moveFlag = true;
        m_mousePressPosX = e->pos().x();

        int value = this->horizontalScrollBar()->value() + scroolLX;
        this->horizontalScrollBar()->setValue(value);
    }
}

void RollingBox::mouseReleaseEvent(QMouseEvent *e)
{
    if(!m_moveFlag)
    {
        QString str = this->model()->data(this->currentIndex()).toString();
        if(!str.isEmpty())
        {
            emit SelectText(str);
        }
    }
    m_moveFlag = false;
    return QListView::mouseReleaseEvent(e);
}
