#include "CSysFirstTitleWidget.h"
#include <QPixmap>
#include <QBoxLayout>

CSysFirstTitleWidget::CSysFirstTitleWidget(const QString &strTitle1, const QString &strTitle2, QWidget *parent) : QWidget(parent)
{
    m_pIconLabel = new CPressLabel;
    m_pIconLabel->setFixedSize(36, 36);
    m_pIconLabel->setPixmap(QPixmap(":/image/ico/history/previous.png"));
    connect(m_pIconLabel, &CPressLabel::SignalPressEvent, this, &CSysFirstTitleWidget::SignalTitlePress);

    m_pTitleLabel1 = new CPressLabel(strTitle1);
    m_pTitleLabel1->setFixedHeight(36);
    m_pTitleLabel1->setObjectName("SysTitleLabel1");
    connect(m_pTitleLabel1, &CPressLabel::SignalPressEvent, this, &CSysFirstTitleWidget::SignalTitlePress);

    m_pSlashLabel = new QLabel("/");
    m_pSlashLabel->setFixedHeight(36);
    m_pSlashLabel->setObjectName("SysTitleLabel2");

    m_pTitleLabel2 = new QLabel(strTitle2);
    m_pTitleLabel2->setFixedHeight(36);
    m_pTitleLabel2->setObjectName("SysTitleLabel2");

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pIconLabel);
    pLayout->addSpacing(5);
    pLayout->addWidget(m_pTitleLabel1);
    pLayout->addSpacing(5);
    pLayout->addWidget(m_pSlashLabel);
    pLayout->addSpacing(5);
    pLayout->addWidget(m_pTitleLabel2);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}
