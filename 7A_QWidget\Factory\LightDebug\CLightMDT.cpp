#include "CLightMDT.h"
#include <QBoxLayout>
#include "PublicParams.h"
#include "PublicConfig.h"

CLightMDT::CLightMDT(QWidget *parent) : QWidget(parent)
{
    Register2Map(Method_FLMDT);

    _InitWidget();

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalTimingTestStart,
            this, &CLightMDT::SlotClearMDTData);
}

CLightMDT::~CLightMDT()
{
    UnRegister2Map(Method_FLMDT);
}

void CLightMDT::receiveMachineCmdReplay(int iMachineID ,int iMethodID, int iResult, const QVariant &qVarData)
{
    Q_UNUSED(iResult);

    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if(Method_FLMDT == iMethodID)
        m_pMDTList.at(iMachineID)->ReceiveMDTData(qVarData);
}

void CLightMDT::SlotClearMDTData(int iMachineID)
{
    m_pMDTList.at(iMachineID)->ClearData();
}

void CLightMDT::_SlotClearBtn()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_pMDTList.at(iMachineID)->ClearData();
}

void CLightMDT::_SlotSwitchBtn()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_pMDTList.at(iMachineID)->SwitchPage();
}

void CLightMDT::_SlotExportBtn()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_pMDTList.at(iMachineID)->ExportData();
}

void CLightMDT::_InitWidget()
{
    m_pStackedWidget = new QStackedWidget;
    for(int i=0; i<gk_iMachineCount; i++)
    {
        CLightOneMDT *pMDT = new CLightOneMDT(i);
        m_pMDTList.push_back(pMDT);
        m_pStackedWidget->addWidget(pMDT);
    }

    m_pClearBtn = new QPushButton(tr("清空"));
    m_pClearBtn->setFixedSize(140, 50);
    connect(m_pClearBtn, &QPushButton::clicked, this, &CLightMDT::_SlotClearBtn);

    m_pSwitchBtn = new QPushButton(tr("切换"));
    m_pSwitchBtn->setFixedSize(140, 50);
    connect(m_pSwitchBtn, &QPushButton::clicked, this, &CLightMDT::_SlotSwitchBtn);

    m_pExportBtn = new QPushButton(tr("导出"));
    m_pExportBtn->setFixedSize(140, 50);
    connect(m_pExportBtn, &QPushButton::clicked, this, &CLightMDT::_SlotExportBtn);

    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(80, 50);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), m_pStackedWidget, SLOT(setCurrentIndex(int)));

    QHBoxLayout *pHLayout = new QHBoxLayout;
    pHLayout->setMargin(0);
    pHLayout->setSpacing(15);
    pHLayout->addWidget(m_pClearBtn);
    pHLayout->addWidget(m_pSwitchBtn);
    pHLayout->addWidget(m_pExportBtn);
    pHLayout->addStretch(1);
    pHLayout->addWidget(m_pMachineComboBox);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addWidget(m_pStackedWidget);
    pLayout->addSpacing(10);
    pLayout->addLayout(pHLayout);
    this->setLayout(pLayout);
}
