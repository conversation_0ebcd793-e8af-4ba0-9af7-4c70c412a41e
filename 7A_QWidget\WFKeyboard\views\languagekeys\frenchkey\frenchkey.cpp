#include "frenchkey.h"

#include "common/keyboardtoolbutton/keyboardtoolbutton.h"

FrenchKey::<PERSON><PERSON><PERSON>(const QString& name) :
    LanguageBase<PERSON>ey(name)
{
    InitButtons();
}

void FrenchKey::InitButtons()
{
    QMap<int, QList<KeyBoardToolButton*> > map;
    QList<KeyBoardToolButton*> firstButtons, secondButtons, thirdButtons;

    firstButtons.append(new KeyBoardToolButton("\x0061","\x0041","à,â,æ,á,ä,ã,å,ā,ª","À,Â,Æ,Á,Ä,Ã,Å,Ā,ª"));
    firstButtons.append(new KeyBoardToolButton("\x007A","\x005A","",""));
    firstButtons.append(new KeyBoardToolButton("\x0065","\x0045","é,è,ê,ë,ę,ė,ē","É,È,Ê,Ë,Ę,Ė,Ē"));
    firstButtons.append(new KeyBoardToolButton("\x0072","\x0052","",""));
    firstButtons.append(new KeyBoardToolButton("\x0074","\x0054","",""));
    firstButtons.append(new KeyBoardToolButton("\x0079","\x0059","ÿ","Ÿ"));
    firstButtons.append(new KeyBoardToolButton("\x0075","\x0055","ū,ú,ü,ù,û","Ū,Ú,Ü,Ù,Û"));
    firstButtons.append(new KeyBoardToolButton("\x0069","\x0049","ī,į,í,ì,ï,î","Ī,Į,Í,Ì,Ï,Î"));
    firstButtons.append(new KeyBoardToolButton("\x006F","\x004F","º,ō,ø,õ,ó,ò,ö,œ,ô","º,Ō,Ø,Õ,Ó,Ò,Ö,Œ,Ô"));
    firstButtons.append(new KeyBoardToolButton("\x0070","\x0050","",""));

    secondButtons.append(new KeyBoardToolButton("\x0071","\x0051","",""));
    secondButtons.append(new KeyBoardToolButton("\x0073","\x0053","",""));
    secondButtons.append(new KeyBoardToolButton("\x0064","\x0044","",""));
    secondButtons.append(new KeyBoardToolButton("\x0066","\x0046","",""));
    secondButtons.append(new KeyBoardToolButton("\x0067","\x0047","",""));
    secondButtons.append(new KeyBoardToolButton("\x0068","\x0048","",""));
    secondButtons.append(new KeyBoardToolButton("\x006A","\x004A","",""));
    secondButtons.append(new KeyBoardToolButton("\x006B","\x004B","",""));
    secondButtons.append(new KeyBoardToolButton("\x006C","\x004C","",""));
    secondButtons.append(new KeyBoardToolButton("\x006D","\x004D","",""));

    thirdButtons.append(GetLeftCapsLockBtn());
    thirdButtons.append(new KeyBoardToolButton("\x0077","\x0057","",""));
    thirdButtons.append(new KeyBoardToolButton("\x0078","\x0058","",""));
    thirdButtons.append(new KeyBoardToolButton("\x0063","\x0043","ç,ć,č","Ç,Ć,Č"));
    thirdButtons.append(new KeyBoardToolButton("\x0076","\x0056","",""));
    thirdButtons.append(new KeyBoardToolButton("\x0062","\x0042","",""));
    thirdButtons.append(new KeyBoardToolButton("\x006E","\x004E","",""));
    thirdButtons.append(new KeyBoardToolButton("'","'","",""));
    thirdButtons.append(GetRightCapsLockBtn());

    map.insert(0,firstButtons);
    map.insert(1,secondButtons);
    map.insert(2,thirdButtons);

    SetButtonsMap(map);
    SetTranslate("Chinois","Anglais","Mathématiques","Espace");
}

