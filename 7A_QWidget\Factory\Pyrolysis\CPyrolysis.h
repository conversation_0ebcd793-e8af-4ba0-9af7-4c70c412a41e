#pragma once

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: chenhao
  * Date: 2024-7-8
  * Description: Pyrolysis
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QStackedWidget>
#include "CHBtnTitleWidget.h"


class CPyrolysisCurve;
class CPyrolysisDebug;
class CPyrolysisCalibration;

class CPyrolysis : public QWidget
{
    Q_OBJECT
public:
    explicit CPyrolysis(QWidget *parent = nullptr);
    ~CPyrolysis();

private slots:
    void _SlotTitleChanged(int);

private:
    void _InitWidget();

private:
    CPyrolysisCurve *m_pPyrolysisCurve{nullptr};
    CPyrolysisDebug *m_pPyrolysisDebug{nullptr};
    CPyrolysisCalibration * m_CPyrolysisCalibration{nullptr};
    CHBtnTitleWidget *m_pHBtnTitle{nullptr};
    QStackedWidget *m_pStackedWidget{nullptr};
};
