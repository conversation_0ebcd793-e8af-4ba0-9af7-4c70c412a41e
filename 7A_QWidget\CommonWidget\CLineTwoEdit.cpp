#include "CLineTwoEdit.h"
#include <QBoxLayout>
#include <QRegularExpressionValidator>

CLineTwoEdit::CLineTwoEdit(QWidget *parent)
    : QWidget(parent)
{
    _InitWidget();
}

CLineTwoEdit::~CLineTwoEdit()
{

}

void CLineTwoEdit::getText(QString &strFirst, QString &strSecond)
{
    strFirst = m_pEditFirst->text();
    strSecond = m_pEditSecond->text();
}

void CLineTwoEdit::setText(const QString &strFirst, const QString &strSecond)
{
    m_pEditFirst->setText(strFirst);
    m_pEditSecond->setText(strSecond);
}

void CLineTwoEdit::clearData()
{
    m_pEditFirst->clear();
    m_pEditSecond->clear();
}

void CLineTwoEdit::_InitWidget()
{

    this->setFixedSize(250, 56);
    QRegularExpression regex("^(4[0-5]|[0-3]?[0-9])$"); // 0-45 的正则表达式
    QRegularExpressionValidator *validator = new QRegularExpressionValidator(regex, this);

    m_pSplitLabel = new QLabel(tr("━"));
    m_pSplitLabel->setFixedSize(30,56);

    m_pEditFirst  = new CLineEdit;
    m_pEditFirst->setFixedSize(100, 56);
    m_pEditFirst->setValidator(validator);

    m_pEditSecond  = new CLineEdit;
    m_pEditSecond->setFixedSize(100, 56);
    m_pEditSecond->setValidator(validator);

    QHBoxLayout* pLayout = new QHBoxLayout();
    pLayout->setMargin(0);
    pLayout->addWidget(m_pEditFirst, 0 ,Qt::AlignHCenter);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pSplitLabel, 0 ,Qt::AlignHCenter);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pEditSecond, 0 ,Qt::AlignHCenter);
    setLayout(pLayout);
}
