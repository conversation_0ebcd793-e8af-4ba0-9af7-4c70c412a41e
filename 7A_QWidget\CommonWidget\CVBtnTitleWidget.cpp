#include "CVBtnTitleWidget.h"
#include <QVariant>
#include <QVBoxLayout>

CVBtnTitleWidget::CVBtnTitleWidget(const QStringList &strTitleList, int iSpacing, QWidget *parent)
    : QWidget(parent)
    , m_iSpacing(iSpacing)
    , m_strTitleList(strTitleList)
{
    Q_ASSERT(strTitleList.size() > 1);

    _InitWidget();
}

CVBtnTitleWidget::~CVBtnTitleWidget()
{

}

void CVBtnTitleWidget::SetTitleIndex(int index)
{
    _ButtonClicked(index);
}

void CVBtnTitleWidget::SetBtnFixedSize(int iWidth, int iHeight)
{
    for(int i=0; i<m_btnList.size(); i++)
        m_btnList[i]->setFixedSize(iWidth, iHeight);
}

void CVBtnTitleWidget::_SlotBtnClicked()
{
    QPushButton* pBtn = dynamic_cast<QPushButton*>(sender());
    if(nullptr == pBtn)
        return;
    int index = pBtn->property("BtnIndex").toInt();
    _ButtonClicked(index);
}

void CVBtnTitleWidget::_ButtonClicked(int index)
{
    if(index < 0 || index >= m_btnList.size())
        return;
    for(int i=0; i<m_btnList.size(); i++)
    {
        if(i == index)
            m_btnList[i]->setEnabled(false);
        else
            m_btnList[i]->setEnabled(true);
    }
    emit SignalTitleChanged(index);
}

void CVBtnTitleWidget::_InitWidget()
{
    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0); //垂直布局默认左边有11px的间隙
    pLayout->setSpacing(m_iSpacing);
    int size = m_strTitleList.size();
    for(int i=0; i<size; i++)
    {
        QPushButton *pBtn = new QPushButton(m_strTitleList.at(i));
        pBtn->setFixedSize(150, 50);
        pBtn->setObjectName("VBtnTitle");
        pBtn->setProperty("BtnIndex", QVariant(i));
        connect(pBtn, &QPushButton::clicked, this, &CVBtnTitleWidget::_SlotBtnClicked);
        m_btnList.push_back(pBtn);
        pLayout->addWidget(pBtn);

       // if(i != size -1)
       //     pLayout->addSpacing(m_iSpacing);
    }

    setLayout(pLayout);
}
