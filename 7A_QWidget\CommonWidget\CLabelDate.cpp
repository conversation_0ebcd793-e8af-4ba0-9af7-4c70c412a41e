#include "CLabelDate.h"
#include <QHBoxLayout>

CLabelDate::CLabelDate(const QString &strName, const QString &strDate, int iSpacing, QWidget *parent)
    : QWidget(parent)
    , m_iSpacing(iSpacing)
    , m_strName(strName)
    , m_strDate(strDate)
{
    m_pNameLabel = new QLabel(m_strName);

    m_pDateLabel = new CPressLabel(m_strDate);
    m_pDateLabel->setObjectName("daylabel");
    m_pDateLabel->setAlignment(Qt::AlignCenter);
    connect(m_pDateLabel, &CPressLabel::SignalPressEvent, this, &CLabelDate::SignalPressEvent);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pNameLabel);
    pLayout->addSpacing(m_iSpacing);
    pLayout->addWidget(m_pDateLabel);
    this->setLayout(pLayout);
}

CLabelDate::~CLabelDate()
{

}

QString CLabelDate::GetDateString() const
{
    return m_pDateLabel->text();
}

void CLabelDate::SetDateString(const QString &strDate)
{
    m_pDateLabel->setText(strDate);
}

void CLabelDate::SetLabelFixedSize(int iWidth, int iHeight)
{
    m_pNameLabel->setFixedSize(iWidth, iHeight);
}

void CLabelDate::SetDateFixedSize(int iWidth, int iHeight)
{
    m_pDateLabel->setFixedSize(iWidth, iHeight);
}
