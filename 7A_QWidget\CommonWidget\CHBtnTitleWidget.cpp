#include "CHBtnTitleWidget.h"
#include <QVariant>
#include <QHBoxLayout>

CHBtnTitleWidget::CHBtnTitleWidget(const QStringList &strTitleList, QWidget *parent)
    : QWidget(parent)
    , m_strTitleList(strTitleList)
{
    Q_ASSERT(strTitleList.size() > 1);

    _InitWidget();
}

CHBtnTitleWidget::~CHBtnTitleWidget()
{

}

void CHBtnTitleWidget::SetTitleIndex(int index)
{
    _ButtonClicked(index);
}

void CHBtnTitleWidget::_SlotBtnClicked()
{
    QPushButton* pBtn = dynamic_cast<QPushButton*>(sender());
    if(nullptr == pBtn)
        return;
    int index = pBtn->property("BtnIndex").toInt();
    _ButtonClicked(index);
}

void CHBtnTitleWidget::_ButtonClicked(int index)
{
    if(index < 0 || index >= m_btnList.size())
        return;
    for(int i=0; i<m_btnList.size(); i++)
    {
        if(i == index)
            m_btnList[i]->setEnabled(false);
        else
            m_btnList[i]->setEnabled(true);
    }
    emit SignalTitleChanged(index);
}

void CHBtnTitleWidget::_InitWidget()
{
    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setContentsMargins(10, 0, 10, 0);
    pLayout->setSpacing(0);
    int size = m_strTitleList.size();
    for(int i=0; i<size; i++)
    {
        QPushButton *pBtn = new QPushButton(m_strTitleList.at(i));
        pBtn->setMinimumHeight(50);
        pBtn->setMinimumWidth(100);
        pBtn->setObjectName("HBtnTitleMiddle");
        pBtn->setProperty("BtnIndex", QVariant(i));
        connect(pBtn, &QPushButton::clicked, this, &CHBtnTitleWidget::_SlotBtnClicked);
        m_btnList.push_back(pBtn);
        pLayout->addWidget(pBtn);
    }

    m_btnList.first()->setObjectName("HBtnTitleLeft");
    m_btnList.last()->setObjectName("HBtnTitleRight");

    setLayout(pLayout);
}
