#ifndef CHISTORYSEARCHWIDGET_H
#define CHISTORYSEARCHWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-28
  * Description: 历史查询
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QPushButton>

#include "CAgeWidget.h"
#include "CHLabelTitleWidget.h"
#include "CLabelLineEdit.h"
#include "CLabelComboBox.h"
#include "CNewLabelDate.h"
#include "CDateTimeWidget.h"

struct SHistroySearchStruct
{
    SHistroySearchStruct()
    {
        Clear();
    }

    void Clear()
    {
        strStartDate.clear();
        strEndDate.clear();
        strSampleID.clear();
        strSampleType.clear();
        strCardID.clear();
        strName.clear();
        strTelephone.clear();
        strGender.clear();
        strAge.clear();
        strBirthday.clear();
        strProject.clear();
        strTestType.clear();
    }

    QString strStartDate;
    QString strEndDate;
    QString strSampleID;
    QString strSampleType;
    QString strCardID;
    QString strName;
    QString strTelephone;
    QString strGender;
    QString strAge;
    QString strBirthday;
    QString strProject;
    QString strTestType;
};

class CHistorySearchWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CHistorySearchWidget(QWidget *parent = nullptr);

protected:
    void paintEvent(QPaintEvent* pEvent) override;
    void showEvent(QShowEvent *pEvent) override;

signals:
    void SignalSearchConfirm(const SHistroySearchStruct &sSearchStruct);

private slots:
    void _SlotProjectChanged(QString strProject);
    void _SlotCancelBtn();
    void _SlotConfirmBtn();
    void _SlotShowDateWidget();
    void _SlotConfirmDate(const QString &strDate);

private:
    void _ClearData();

private:
    void _InitWidget();
    void _InitLayout();

private:
    enum {eStartDate, eEndDate, eBirthDate};

private:
    QLabel *m_pBackgroundLabel;
    CHLabelTitleWidget *m_pCHLabelTitleWidget;
    CHNewLabelDate *m_pStartDateWidget, *m_pEndDateWidget;
    CHLabelLineEdit *m_pSampleIDWidget, *m_pCardIDWidget;
    CHLabelLineEdit *m_pNameWidget, *m_pTelWidget;
    CHLabelComboBox *m_pGenderWidget;
    CHAgeWidget *m_pAgeWidget;
    CHNewLabelDate *m_pBirthdayWidget;
    CHLabelComboBox *m_pProjetcWidget, *m_pTestTypeWidget;
    CHLabelComboBox *m_pSampleTypeWidget;
    QPushButton *m_pCancelBtn, *m_pConfirmBtn;
    CDateTimeWidget *m_pCDateTimeWidget;
    int m_iDateType;
};

#endif // CHISTORYSEARCHWIDGET_H
