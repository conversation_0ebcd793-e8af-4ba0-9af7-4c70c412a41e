﻿#include "CScanCodeThread.h"
#include <QDebug>
#include "PublicFunction.h"

CScanCodeThread *CScanCodeThread::m_spInstance = nullptr;

CScanCodeThread *CScanCodeThread::GetInstance()
{
    if(nullptr == m_spInstance)
        m_spInstance = new CScanCodeThread;
    return m_spInstance;
}

CScanCodeThread::CScanCodeThread()
{
    m_iScantimes = 0;
    m_bOpenOK = false;
    m_bScanning = false;

    connect(this, &CScanCodeThread::SignalInitThread, this, &CScanCodeThread::_SlotInitThread);
    connect(this, &CScanCodeThread::SignalExitThread, this, &CScanCodeThread::_SlotExitThread, Qt::BlockingQueuedConnection);
    connect(this, &CScanCodeThread::SignalStartScan, this, &CScanCodeThread::_SlotStartScan);
    connect(this, &CScanCodeThread::SignalStopScan, this, &CScanCodeThread::_SlotStopScan);

    m_pThread = new QThread;
    this->moveToThread(m_pThread);
    m_pThread->start();

    emit SignalInitThread();
}

CScanCodeThread::~CScanCodeThread()
{

}

void CScanCodeThread::StartScan()
{
    emit SignalStartScan();
}

void CScanCodeThread::StopScan()
{
    emit SignalStopScan();
}

void CScanCodeThread::_SlotInitThread()
{
    QString strEcho0 = "echo 0 > /proc/wondfo_gpio/scan_pwr_en";
    System(strEcho0.toStdString().c_str());
    QString strEcho1 = "echo 1 > /proc/wondfo_gpio/scan_pwr_en";
    System(strEcho1.toStdString().c_str());

    m_pScanTimer = new QTimer(this);
    connect(m_pScanTimer, &QTimer::timeout, this, &CScanCodeThread::_SlotScanTimeout);

    m_pDelayTimer = new QTimer(this);
    connect(m_pDelayTimer, &QTimer::timeout, this, &CScanCodeThread::_SlotDelayTimeout);

    m_pSerialPort = new QSerialPort(this);
    m_pSerialPort->setPortName("/dev/ttyS9");
    m_pSerialPort->setBaudRate(115200);
    m_pSerialPort->setParity(QSerialPort::NoParity);
    m_pSerialPort->setDataBits(QSerialPort::Data8);
    m_pSerialPort->setStopBits(QSerialPort::OneStop);
    m_bOpenOK = m_pSerialPort->open(QIODevice::ReadWrite);
    m_pSerialPort->setReadBufferSize(1024);
    connect(m_pSerialPort, &QSerialPort::readyRead, this, &CScanCodeThread::_SlotReadSerial);

    qDebug()<<"扫码串口打开:"<<m_bOpenOK<<QThread::currentThreadId();
}

void CScanCodeThread::_SlotExitThread()
{

}

void CScanCodeThread::_SlotStartScan()
{
    if(!m_bOpenOK)
        return;

    QByteArray byte;
    byte.append(0x16);
    byte.append(0x54);
    byte.append(0x0d);
    m_pSerialPort->write(byte);

    m_bScanning = true;
    m_iScantimes = 0;
    m_pScanTimer->start(20000);

    qDebug()<<"开始扫描:"<<byte.toHex().toUpper();
}

void CScanCodeThread::_SlotStopScan()
{
    if(!m_bOpenOK)
        return;

    QByteArray byte;
    byte.append(0x16);
    byte.append(0x55);
    byte.append(0x0d);
    m_pSerialPort->write(byte);

    m_bScanning = false;
    m_iScantimes = 0;
    m_pScanTimer->stop();
    m_pDelayTimer->stop();
    m_qScanData.clear();

    qDebug()<<"停止扫描:"<<byte.toHex().toUpper();
}

void CScanCodeThread::_SlotScanTimeout()
{
    qDebug()<<"扫描次数:"<<m_iScantimes;
    if(m_iScantimes >= 5)
    {
        _SlotStopScan();
        return;
    }

    QByteArray byte;
    byte.append(0x16);
    byte.append(0x54);
    byte.append(0x0d);
    m_pSerialPort->write(byte);

    m_iScantimes++;
}

void CScanCodeThread::_SlotReadSerial()
{
    QByteArray qReadData = m_pSerialPort->readAll();
    qDebug()<<"扫描数据:"<<qReadData<<m_bScanning;

    if(!m_bScanning || qReadData.isEmpty())
        return;

    char ch = qReadData[0];
    if(!isprint(ch))
    {
        _SlotStartScan();
        return;
    }

    m_qScanData += qReadData;
    m_pDelayTimer->stop();
    m_pDelayTimer->start(50);
}

void CScanCodeThread::_SlotDelayTimeout()
{
    emit SignalScanData(m_qScanData);

    _SlotStopScan();
}
