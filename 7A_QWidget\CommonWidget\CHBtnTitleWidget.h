#ifndef CHBTNTITLEWIDGET_H
#define CHBTNTITLEWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-10-30
  * Description: 由一组按钮组成的横标题
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QPushButton>

class CHBtnTitleWidget : public QWidget
{
    Q_OBJECT
public:
    CHBtnTitleWidget(const QStringList &strTitleList, QWidget *parent = nullptr);
    ~CHBtnTitleWidget();

    void SetTitleIndex(int index);

signals:
    void SignalTitleChanged(int);

private slots:
    void _SlotBtnClicked();

private:
    void _InitWidget();
    void _ButtonClicked(int index);

private:
    QStringList m_strTitleList;
    QList<QPushButton*> m_btnList;
};

#endif // CHBTNTITLEWIDGET_H
