#include "CLoginWidget.h"
#include <QBoxLayout>
#include <QStyle>
#include <QEvent>
#include <QPixmap>
#include <QPalette>
#include <QListView>
#include <QDateTime>
#include <QRandomGenerator>

#include "CConfigJson.h"
#include "PublicParams.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "DBControl/CUserDB.h"
#include "DBControl/CLogDB.h"

CLoginWidget::CLoginWidget(QWidget *parent) : QWidget(parent)
{
    m_bDynamicPassword = false;
    m_iAdminErrTimes = 0;
    m_pAlgorithmDesCrypt = new AlgorithmDesCrypt();

    QPalette pale = this->palette();
    pale.setBrush(this->backgroundRole(), QBrush(QPixmap(":/image/ico/login/login.png")));
    this->setPalette(pale);
    this->setAutoFillBackground(true);

    _InitWidget();
    _InitLayout();

    LoadQSS(this, ":/qss/qss/login.qss");
}

void CLoginWidget::showEvent(QShowEvent *pEvent)
{
    m_bDynamicPassword = CPublicConfig::GetInstance()->GetDynamicPassword();

    QStringList strUserList = CUserDB::GetInstance()->GetAllUserNameList();
    m_pUserNameComboBox->clear();
    m_pUserNameComboBox->addItems(strUserList);

    QString strLastUser = CConfigJson::GetInstance()->GetConfigValue("Login", "user_name").toString();
    if(strLastUser.isEmpty())
        m_pUserNameComboBox->setCurrentText(gk_strAdminUserName);
    else
        m_pUserNameComboBox->setCurrentText(strLastUser);

    QWidget::showEvent(pEvent);
}

bool CLoginWidget::eventFilter(QObject *pWatched, QEvent *pEvent)
{
    if(pWatched->inherits("QLabel") && pWatched == m_pVersionLabel)
    {
        if(QEvent::MouseButtonPress == pEvent->type())
        {
            m_pVersionLabel->setObjectName("VerLabelSee");
            m_pVersionLabel->style()->polish(m_pVersionLabel);
        }
        if(QEvent::MouseButtonRelease == pEvent->type())
        {
            m_pVersionLabel->setObjectName("VerLabelNoSee");
            m_pVersionLabel->style()->polish(m_pVersionLabel);
        }
    }

    return QWidget::eventFilter(pWatched, pEvent);
}

void CLoginWidget::_SlotUserTextChanged(const QString &strText)
{
    m_pPasswordLineEdit->clear();
    m_pPasswordLineEdit2->clear();
    m_pPasswordLineEdit2->setVisible(false);
    m_pInfoLabel->clear();
    m_pCodeLabel->clear();

    if(gk_strAdminUserName == strText)
    {
        bool bAdminExist = CUserDB::GetInstance()->IsUserExistInDB(gk_strAdminUserName);
        if(!bAdminExist)
        {
            m_pPasswordLineEdit->clear();
            m_pPasswordLineEdit2->clear();
            m_pPasswordLineEdit2->setVisible(true);
            m_pInfoLabel->setText(tr("请修改您的密码"));
        }
        else
        {
            if(m_bDynamicPassword && m_iAdminErrTimes > 3)
            {
                _CodeAdminPassword();
            }
        }
        return;
    }

    if(gk_strFactoryUserName == strText && m_bDynamicPassword)
    {
        _CodeFactoryPassword();
        return;
    }
}

void CLoginWidget::_SlotLoginBtn()
{
    QString strInputUser = m_pUserNameComboBox->currentText();
    QString strInputPassword = m_pPasswordLineEdit->text();
    qDebug()<<Q_FUNC_INFO<<strInputUser<<strInputPassword;

    if(strInputUser.isEmpty())
    {
        m_pInfoLabel->setText(tr("请输入用户名"));
        return;
    }
    if(strInputPassword.isEmpty())
    {
        m_pInfoLabel->setText(tr("请输入密码"));
        _SaveLoginLog(eLogNoPwd, strInputUser);
        _SaveOperationLog(strInputUser, tr("登录失败，没输入密码"));
        return;
    }

    //FLY登录
    if(gk_strFlyUserName == strInputUser)
    {
        if(gk_strFlyPassword == strInputPassword)
        {
            _LoginOK(gk_strFlyUserName, strInputPassword);
        }
        else
        {
            m_pInfoLabel->setText(tr("密码错误"));
            _SaveLoginLog(eLogPWDError, strInputUser);
            _SaveOperationLog(strInputUser, tr("登录失败，密码错误"));
        }
        return;
    }

    //factory登录
    if(gk_strFactoryUserName == strInputUser)
    {
        if(m_bDynamicPassword)
        {
            if(m_strCodeFactory == strInputPassword)
            {
                _LoginOK(gk_strFactoryUserName, strInputPassword);
            }
            else
            {
                m_pInfoLabel->setText(tr("密码错误"));
                _SaveLoginLog(eLogPWDError, strInputUser);
                _SaveOperationLog(strInputUser, tr("登录失败，密码错误"));
            }
        }
        else
        {
            if(gk_strFactoryPassWord == strInputPassword)
            {
                _LoginOK(gk_strFactoryUserName, strInputPassword);
            }
            else
            {
                m_pInfoLabel->setText(tr("密码错误"));
                _SaveLoginLog(eLogPWDError, strInputUser);
                _SaveOperationLog(strInputUser, tr("登录失败，密码错误"));
            }
        }
        return;
    }

#if 0
    //maintian登录
    if(gk_strMaintianUserName == strInputUser)
    {
        if(gk_strMaintianPassword == strInputPassword)
        {
            _LoginOK(gk_strMaintianUserName, strInputPassword);
        }
        else
        {
            m_pInfoLabel->setText(tr("密码错误"));
            _SaveLoginLog(eLogPWDError, strInputUser);
        }
        return;
    }
#endif

    //Admin登陆
    if(gk_strAdminUserName == strInputUser)
    {
        //Admin首次登陆需要注册
        if(!CUserDB::GetInstance()->IsUserExistInDB(gk_strAdminUserName))
        {
            QString strPassword2 = m_pPasswordLineEdit2->text();
            if(strInputPassword != strPassword2)
            {
                m_pInfoLabel->setText(tr("两次输入密码不一致"));
                _SaveLoginLog(eLogPwdDiff, gk_strAdminUserName);
                _SaveOperationLog(gk_strAdminUserName, tr("首次登录失败，两次输入密码不一致"));
                return;
            }

            int iLegal = CheckPasswordIsLegal(strInputPassword);
            if(0 != iLegal)
            {
                m_pInfoLabel->setText(tr("密码长度必须大于等于6且包含数字和大小写字母"));
                _SaveLoginLog(eLogPwdInvalid, gk_strAdminUserName);
                _SaveOperationLog(gk_strAdminUserName, tr("首次登录失败，密码长度必须大于等于6且包含数字和大小写字母"));
                return;
            }

            QStringList strList;
            strList << gk_strAdminUserName << strInputPassword << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss") << "";
            CUserDB::GetInstance()->AddUserData(strList);
            _LoginOK(gk_strAdminUserName, strInputPassword);
            return;
        }

        if(!CUserDB::GetInstance()->IsUserEnabled(strInputUser))
        {
            m_pInfoLabel->setText(tr("此用户名已被禁用"));
            _SaveLoginLog(eLogDisabled, strInputUser);
            _SaveOperationLog(strInputUser, tr("登录失败，用户名被禁用"));
            return;
        }

        QString strAdminDBPassword = CUserDB::GetInstance()->FindUserPasswordByName(gk_strAdminUserName);
        if(strAdminDBPassword == strInputPassword)
        {
            _LoginOK(gk_strAdminUserName, strInputPassword);
            return;
        }
        else
        {
            if(m_bDynamicPassword && m_strCodeAdmin == strInputPassword)
            {
                _LoginOK(gk_strAdminUserName, strInputPassword);
                return;
            }

            m_iAdminErrTimes++;
            m_pInfoLabel->setText(tr("密码错误"));
            _SaveLoginLog(eLogPWDError, gk_strAdminUserName);
            _SaveOperationLog(gk_strAdminUserName, tr("登录失败，密码错误"));
            qDebug()<<Q_FUNC_INFO<<"Admin登录密码错误,次数:"<<m_iAdminErrTimes;
            if(m_iAdminErrTimes > 3 && m_bDynamicPassword && m_strCodeAdmin.isEmpty())
            {
                m_iAdminErrTimes = 0;
                _CodeAdminPassword();
            }
        }

        return;
    }

    if(!CUserDB::GetInstance()->IsUserExistInDB(strInputUser))
    {
        m_pInfoLabel->setText(tr("此用户名不存在"));
        _SaveLoginLog(eLogUserNotExist, strInputUser);
        _SaveOperationLog(strInputUser, tr("登录失败，用户名不存在"));
        return;
    }

    if(!CUserDB::GetInstance()->IsUserEnabled(strInputUser))
    {
        m_pInfoLabel->setText(tr("此用户名已被禁用"));
        _SaveLoginLog(eLogDisabled, strInputUser);
        _SaveOperationLog(strInputUser, tr("登录失败，用户名被禁用"));
        return;
    }

    QString strDBPassword = CUserDB::GetInstance()->FindUserPasswordByName(strInputUser);
    if(strDBPassword == strInputPassword)
    {
        _LoginOK(strInputUser, strInputPassword);
    }
    else
    {
        m_pInfoLabel->setText(tr("密码错误"));
        _SaveLoginLog(eLogPWDError, strInputUser);
        _SaveOperationLog(strInputUser, tr("登录失败，密码错误"));
    }
}

void CLoginWidget::_LoginOK(const QString &strUser, const QString &strPassword)
{
    int iUserLevel = 0;
    if(gk_strFlyUserName == strUser)
        iUserLevel = eUser_Fly;
    else if(gk_strFactoryUserName == strUser)
        iUserLevel = eUser_Factory;
    else if(gk_strMaintianUserName == strUser)
        iUserLevel = eUser_Maintain;
    else if(gk_strAdminUserName == strUser)
        iUserLevel = eUser_Admin;
    else
        iUserLevel = eUser_Normal;
    CPublicConfig::GetInstance()->SetLoginLevel(iUserLevel);
    CPublicConfig::GetInstance()->SetLoginUser(strUser);
    CPublicConfig::GetInstance()->SetLoginPassword(strPassword);
    qDebug()<<"登录成功:"<<strUser<<strPassword<<iUserLevel;

    m_pPasswordLineEdit->clear();
    m_pPasswordLineEdit2->clear();
    m_pPasswordLineEdit2->setVisible(false);
    m_pInfoLabel->clear();
    m_pCodeLabel->clear();

    m_iAdminErrTimes = 0;
    m_strCodeAdmin.clear();
    m_strCodeFactory.clear();

    //QJsonObject qLoginObj = CConfigJson::GetInstance()->GetConfigJsonObject("Login");
    //qLoginObj.insert("user_name", strUser);
    //qLoginObj.insert("user_password", strPassword);
    //CConfigJson::GetInstance()->SetConfigJsonObject("Login", qLoginObj);
    CConfigJson::GetInstance()->SetConfigValue("Login", "user_name", strUser);
    CConfigJson::GetInstance()->SetConfigValue("Login", "user_password", strPassword);
    emit SignalLoginOK();

    _SaveLoginLog(eLogOK, strUser);
    _SaveOperationLog(strUser, tr("登录成功"));
}

void CLoginWidget::_CodeFactoryPassword()
{
    int iRand= QRandomGenerator::global()->bounded(1000, 9999);
    QString strCode = "F" + QTime::currentTime().toString("ssmm") + QString::number(iRand);
    m_pCodeLabel->setText(tr("机器代码：") + strCode);
    m_pAlgorithmDesCrypt->SetKey("Wondfofc");
    m_pAlgorithmDesCrypt->SetSrc(strCode);
    m_strCodeFactory = m_pAlgorithmDesCrypt->encrypt();
    qDebug()<<Q_FUNC_INFO<<"生成factory动态密码:"<<m_strCodeFactory;
    _SaveLoginLog(eLogMachineCode, gk_strFactoryUserName, m_strCodeFactory);
    _SaveOperationLog(gk_strAdminUserName, tr("用户登录，生成动态密码：") + m_strCodeAdmin);
}

void CLoginWidget::_CodeAdminPassword()
{
    int iRand= QRandomGenerator::global()->bounded(1000, 9999);
    QString strCode = "A" + QTime::currentTime().toString("ssmm") + QString::number(iRand);
    m_pCodeLabel->setText(tr("机器代码：") + strCode);
    m_pAlgorithmDesCrypt->SetKey("Wondfoad");
    m_pAlgorithmDesCrypt->SetSrc(strCode);
    m_strCodeAdmin = m_pAlgorithmDesCrypt->encrypt();
    qDebug()<<Q_FUNC_INFO<<"生成Admin动态密码:"<<m_strCodeAdmin;
    _SaveLoginLog(eLogMachineCode, gk_strAdminUserName, m_strCodeAdmin);
    _SaveOperationLog(gk_strAdminUserName, tr("用户登录，生成动态密码：") + m_strCodeAdmin);
}

void CLoginWidget::_SaveLoginLog(ELOGINTYPE eLogType, QString strUserName, QString strMachineCode)
{
    return;

    QString strLoginLog = GetLoginLogString(eLogType, strUserName, strMachineCode);
    qDebug()<<strLoginLog;
    CLogDB::instance().AddLoginLog(eLogType, strUserName, strLoginLog, strMachineCode);
}

void CLoginWidget::_SaveOperationLog(QString strUser, QString strLog)
{
    CLogDB::instance().AddOperationLog(strUser, strLog, CLogDB::eLoginLog);
}

QGroupBox *CLoginWidget::_CreateGroupBox()
{
    m_pLogoLabel = new QLabel;
    m_pLogoLabel->setFixedSize(226, 56);
    m_pLogoLabel->setObjectName("LogoLabel");

    m_pUserNameComboBox = new CUserNameComboBox;
    m_pUserNameComboBox->setFixedSize(460, 56);
    m_pUserNameComboBox->setView(new QListView);    
    m_pUserNameComboBox->setCompleter(nullptr);
    m_pUserNameComboBox->setEditable(true);
    m_pUserNameComboBox->lineEdit()->setPlaceholderText(tr("请输入用户名"));
    connect(m_pUserNameComboBox, SIGNAL(currentTextChanged(QString)), this, SLOT(_SlotUserTextChanged(QString)));

    m_pPasswordLineEdit = new CPasswordLineEdit;
    m_pPasswordLineEdit->setFixedSize(460, 56);
    m_pPasswordLineEdit->setPlaceholderText(tr("请输入密码"));

    m_pPasswordLineEdit2 = new CPasswordLineEdit;
    m_pPasswordLineEdit2->setFixedSize(460, 56);
    m_pPasswordLineEdit2->setPlaceholderText(tr("请再次输入密码"));
    m_pPasswordLineEdit2->setVisible(false);

    m_pLoginBtn = new QPushButton(tr("登录"));
    m_pLoginBtn->setFixedSize(460, 56);
    m_pLoginBtn->setObjectName("LoginBtn");
    connect(m_pLoginBtn, &QPushButton::clicked, this, &CLoginWidget::_SlotLoginBtn);

    m_pInfoLabel = new QLabel;
    m_pInfoLabel->setFixedSize(460, 70);
    m_pInfoLabel->setWordWrap(true);
    m_pInfoLabel->setObjectName("InfoLabel");
    m_pInfoLabel->setAlignment(Qt::AlignCenter);

    m_pCodeLabel = new QLabel;
    m_pCodeLabel->setObjectName("InfoLabel");
    m_pCodeLabel->setFixedWidth(460);
    m_pCodeLabel->setAlignment(Qt::AlignCenter);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addSpacing(80);
    pLayout->addWidget(m_pLogoLabel, 0, Qt::AlignCenter);
    pLayout->addSpacing(156);
    pLayout->addWidget(m_pUserNameComboBox, 0, Qt::AlignHCenter);
    pLayout->addSpacing(40);
    pLayout->addWidget(m_pPasswordLineEdit, 0, Qt::AlignHCenter);
    pLayout->addSpacing(40);
    pLayout->addWidget(m_pPasswordLineEdit2, 0, Qt::AlignHCenter);
    pLayout->addSpacing(60);
    pLayout->addWidget(m_pLoginBtn, 0, Qt::AlignHCenter);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pInfoLabel, 0, Qt::AlignHCenter);
    pLayout->addSpacing(5);
    pLayout->addWidget(m_pCodeLabel, 0, Qt::AlignHCenter);
    pLayout->addStretch(1);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(640, 800);
    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}

void CLoginWidget::_InitWidget()
{
    m_pTitleLabel1 = new QLabel(tr("欢迎使用"));
    //m_pTitleLabel1->setFixedHeight(72);
    m_pTitleLabel1->setObjectName("TitleLabel");
    m_pTitleLabel1->setAlignment(Qt::AlignRight | Qt::AlignTop);

    m_pTitleLabel2 = new QLabel(tr("全自动核酸扩增分析仪"));
    //m_pTitleLabel2->setFixedHeight(72);
    m_pTitleLabel2->setObjectName("TitleLabel");
    m_pTitleLabel2->setWordWrap(true);
    m_pTitleLabel2->setAlignment(Qt::AlignRight | Qt::AlignTop);
    //m_pTitleLabel2->setIndent(5);

    m_pVersionLabel = new QLabel(tr("发布版本: ") + GetAppVersion(), this);
    m_pVersionLabel->setGeometry(1200, 980, 640, 80);
    m_pVersionLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    m_pVersionLabel->setObjectName("VerLabelNoSee");
    m_pVersionLabel->installEventFilter(this);
}

void CLoginWidget::_InitLayout()
{
    QVBoxLayout *pTitleLayout = new QVBoxLayout;
    pTitleLayout->setMargin(0);
    pTitleLayout->setSpacing(0);
    if(eLanguage_English == gk_iLanguage)
        pTitleLayout->addSpacing(418);
    else
        pTitleLayout->addStretch(1);
    pTitleLayout->addWidget(m_pTitleLabel1);
    pTitleLayout->addWidget(m_pTitleLabel2);
    pTitleLayout->addStretch(1);

    QHBoxLayout *pVerLayout = new QHBoxLayout;
    pVerLayout->setMargin(0);
    pVerLayout->setSpacing(0);
    pVerLayout->addStretch(1);
    pVerLayout->addWidget(m_pVersionLabel);
    pVerLayout->addSpacing(50);

    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(0);
    pTopLayout->addStretch(1);
    pTopLayout->addLayout(pTitleLayout);
    pTopLayout->addSpacing(80);
    pTopLayout->addWidget(_CreateGroupBox(), 0, Qt::AlignHCenter);
    pTopLayout->addSpacing(80);

    this->setLayout(pTopLayout);
}
