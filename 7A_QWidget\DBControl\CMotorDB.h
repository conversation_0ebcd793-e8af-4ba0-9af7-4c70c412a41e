#ifndef CMOTORDB_H
#define CMOTORDB_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-04-10
  * Description: 电机数据库 电机组合指令,电机补偿
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QObject>
#include "CSqliteDBBase.h"

class CMotorDB : public QObject , public CSqliteDBBase
{
    Q_OBJECT
public:
    static CMotorDB &GetInstance();
    virtual ~CMotorDB();

    // 电机指令
    bool AddOneCmd(const QStringList &strDataList);
    bool AddOneCmd(const QString &strCmdID, const QString &strCmdName, const QString &strCmdText,
                   const QString &strCmdParam, const QString &strCmdContent);
    bool DeleteOneCmd(const QString &strCmdID);
    bool DeleteAllCmd();
    QList<QStringList> GetCmdIDNameList();
    QMap<QString,QString> GetCmdIDNameMap();
    QStringList GetCmdInfoByCmdID(const QString &strCmdID);
    QString GetCmdIDByCmdName(const QString &strCmdName);
    QString GetCmdNameByCmdID(const QString &strCmdID);
    QString GetCmdTextByCmdID(const QString &strCmdID);

    //电机补偿
    bool DeleteMotorCompensate(int iMotorIndex);
    bool AddOneCompensate(int iMotorIndex, int iCompensateIndex, const QString &strName, const QString &strValue);
    bool AddOneCompensate(QStringList strInfoList);
    QStringList GetCompensateNameListByMotorIndex(int iMotorIndex);
    QList<QStringList> GetCompensateInfoByMotorIndex(int iMotorIndex);

    //电机通用参数
    bool AddOneParam(const QStringList &strDataList);
    QString GetParamValueByMotorIndexAndType(int iMotorIndex, int iType);
    QString GetParamValueByMotorIndexAndParam8(int iMotorIndex, int iParam8);

private:
    CMotorDB();

    void _InitMotorCmdTable();
    void _InitMotorCompensateTable();
    void _InitMotorParamTable();

    Q_DISABLE_COPY(CMotorDB)
};

#endif // CMOTORDB_H
