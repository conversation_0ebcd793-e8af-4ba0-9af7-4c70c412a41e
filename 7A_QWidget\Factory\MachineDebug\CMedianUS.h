#ifndef CMEDIANUS_H
#define CMEDIANUS_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-12-19
  * Description: 超声
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>

#include "CCmdBase.h"
#include "CTextBrowser.h"
#include "CLabelComboBox.h"
#include "CLabelLineEdit.h"

class CMedianUS : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CMedianUS(QWidget *parent = nullptr);
    ~CMedianUS();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

private slots:
    void _SlotMachineChanged(int iMachineID);
    void _SlotStartBtn();
    void _SlotStopBtn();
    void _SlotRebootBtn();
    void _SlotSetAmpBtn();
    void _SlotSetPIDBtn();
    void _SlotReadFtyBtn();
    void _SlotSetFtyBtn();
    void _SlotReadParamBtn();
    void _SlotSetParamBtn();
    void _SlotReadPWRBtn();
    void _SlotReadVerBtn();
    void _SlotReadAmpListBtn();
    void _SlotSetAmpListBtn();

private:
    void _SaveLog(const QString &strLog);
    void _InitWidget();
    void _InitLayout();

private:
    CLabelComboBox *m_pMachineComboBox;
    QPushButton *m_pStartBtn, *m_pStopBtn, *m_pRebootBtn;
    CLabelLineEdit *m_pAmpLineEdit;
    QPushButton *m_pSetAmpBtn;

    QList<CLabelLineEdit *> m_pPIDTLineEditList;
    QComboBox *m_pPIDComboBox;
    QPushButton *m_pSetPIDBtn;
    QComboBox *m_pFtyComboBox;
    QPushButton *m_pReadFtyBtn, *m_pSetFtyBtn;

    QList<CLabelLineEdit *> m_pParamLineEditList;
    QPushButton *m_pReadParamBtn, *m_pSetParamBtn;

    QList<CLabelLineEdit *> m_pPWRLineEditList;
    QPushButton *m_pReadPWRBtn, *m_pGetPWRBtn;
    QLabel *m_pVerLabel;
    QPushButton *m_pReadVerBtn;

    QPushButton *m_pReadAmpListBtn, *m_pSetAmpListBtn;
    QList<CLabelLineEdit *> m_pAmpListLineEditList;

    CTextBrowser *m_pTextBrowser;

    int m_iUiMachineID;
};

#endif // CMEDIANUS_H
