#ifndef CHOMEDEVICEWIDGET_H
#define CHOMEDEVICEWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-17
  * Description: 设备页
  * -------------------------------------------------------------------------
  * History: 2024-08-09去除设备组 添加 1x5 1x6 1x7 1x8模式
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QGroupBox>
#include "CHomeDevItemWidget.h"

#include "CHomeDevGroupWidget.h"

class CHistoryDetailWidget;
class CHomeDeviceWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CHomeDeviceWidget(QWidget *parent = nullptr);

    void SetCardSampleInfo(int iMachineID, const SCardInfoStruct &sCardInfo, const SSampleInfoStruct &sSampleInfo);

public slots:
    void SlotAppStartEnd();
    void SlotUpdateFLData(int iMachineID, const QList<QMap<double, double>> &dFLMap);
    void SlotUpdateMeltingFLData(int iMachineId, const QList<double>& dTempList,const QList<QMap<double, double>> & dFLMap);
    void SlotTestStart(int iMachineID);

private slots:
    void SlotUpdateItemStatus(int iMachineID, DeviceStatus eStatus);
    void SlotDetailWidgetShow(int iMachinedID);
    void SoltClearFlData(int iMachinedID);

signals:
    void SignalCreateTest(int iMachineID);

private:
    void _Init_1x8();
    void _SetIndexFlValue(int index,const QList<QMap<double,double>>& FlMapList);
    QList<QMap<double, double>> _GetIndexFlValue(int index);
    QList<double> _GetIndexMeltingTempValue(int index);
    QList<QMap<double, double>> _GetIndexMeltingFlValue(int index);
    void _ClearIndexFlValue(int index);
    void _ClearIndexMeltingValue(int index);
    void _SetIndexMeltingFlValue(int index,const QList<double>& dTempList,const QList<QMap<double,double>>& meltingFlMapList);



private:
    int m_iDevNum;
    int m_iItemNum;
    int m_iMachinedId {-1};
    CHistoryDetailWidget *m_pDetailWidget;

    QList<QMap<double, double>> m_dFLMapList[8]; // 八个 * 八
    QList<QMap<double, double>> m_dMeltingFLMapList[8]; // 八个 * 八
    QList<double> m_dMeltingTempMapList[8]; // 八个 * 八

    QGroupBox *m_pGroupBox;
    QList<CHomeDevItemWidget *> m_pDevItemList;
};

#endif // CHOMEDEVICEWIDGET_H
