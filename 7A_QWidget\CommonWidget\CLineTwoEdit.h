#ifndef CLINETWOEDIT_H
#define CLINETWOEDIT_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: chenhao
  * Date: 2024-9-25
  * Description: QLineEdit-QSPin组合控件
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <CLineEdit.h>

//setLayoutDirection(Qt::RightToLeft);

class CLineTwoEdit : public QWidget
{
    Q_OBJECT
public:
    CLineTwoEdit(QWidget *parent = nullptr);
    ~CLineTwoEdit();
    void getText(QString& strFirst,QString& strSecond);
    void setText(const QString& strFirst,const QString& strSecond);
    void clearData();
private:
    void _InitWidget();

private:
    // spin 按钮
    QLabel* m_pSplitLabel;
    CLineEdit* m_pEditFirst,*m_pEditSecond;
};

#endif // CLABELCHECKBOX_H
