#include "CHomePage.h"
#include <QBoxLayout>
#include <QTime>
#include <QDebug>

#include "CRunTest.h"
#include "PublicFunction.h"

CHomePage::CHomePage(QWidget *parent) : QWidget(parent)
{
    m_pCHomeDeviceWidget = new CHomeDeviceWidget;
    connect(m_pCHomeDeviceWidget, &CHomeDeviceWidget::SignalCreateTest, this, &CHomePage::_SlotCreateNormalTest);

    m_pCHomeEnterInfoWidget = new CHomeEnterInfoWidget;
    connect(m_pCHomeEnterInfoWidget, &CHomeEnterInfoWidget::SignalCancelTest, this, &CHomePage::_SlotCancelTest);
    connect(m_pCHomeEnterInfoWidget, &CHomeEnterInfoWidget::SignalStartTest, this, &CHomePage::_SlotStartTest);

    m_pStackWidget = new QStackedWidget;
    m_pStackWidget->setFixedSize(1684, 958);
    m_pStackWidget->addWidget(m_pCHomeDeviceWidget);
    m_pStackWidget->addWidget(m_pCHomeEnterInfoWidget);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pStackWidget);
    pLayout->addStretch(1);
    this->setLayout(pLayout);

    LoadQSS(this, ":/qss/qss/home/<USER>");

    m_pStackWidget->setCurrentIndex(0);
}

int CHomePage::GetCurrentPage()
{
    return m_pStackWidget->currentIndex();
}

int CHomePage::GetCurrentMachineID()
{
    return m_pCHomeEnterInfoWidget->GetCurrentMachineID();
}

void CHomePage::Set2DevicePage()
{
    m_pStackWidget->setCurrentIndex(0);
}

void CHomePage::CreateQCTest(int iMachineID,QString strQCModel)
{
    m_pStackWidget->setCurrentIndex(1);
    m_pCHomeEnterInfoWidget->CreateTest(iMachineID, strQCModel);
}

void CHomePage::_SlotCancelTest()
{
    m_pStackWidget->setCurrentIndex(0);
}

void CHomePage::_SlotCreateNormalTest(int iMachineID)
{
    m_pStackWidget->setCurrentIndex(1);
    m_pCHomeEnterInfoWidget->CreateTest(iMachineID, "T");
}

void CHomePage::_SlotStartTest(int iMachineID)
{
    QString strTimingName, strTecName;    
    m_pCHomeEnterInfoWidget->GetRunTimingTecName(strTimingName, strTecName);

    SRunningInfoStruct &sRunInfo = CRunTest::GetInstance()->GetRunInfoStruct(iMachineID);
    sRunInfo.iTecIndex = GetTecIndex(strTecName);
    sRunInfo.bFactroyTest = false;
    sRunInfo.iRunTimes = 1;
    sRunInfo.strTecName = strTecName;
    sRunInfo.strTimingName = strTimingName;

    SCardInfoStruct sCardInfo = m_pCHomeEnterInfoWidget->GetCardInfoStruct();
    SSampleInfoStruct sSampleInfo = m_pCHomeEnterInfoWidget->GetSampleInfoStruct();
    m_pCHomeDeviceWidget->SetCardSampleInfo(iMachineID, sCardInfo, sSampleInfo);

    sRunInfo.sCardInfo = sCardInfo;
    sRunInfo.sSampleInfo = sSampleInfo;

    m_pStackWidget->setCurrentIndex(0);

    CRunTest::GetInstance()->StartTest(iMachineID);
}
