﻿#ifndef _HL7_REPETITION_COLLECTION_H_
#define _HL7_REPETITION_COLLECTION_H_

#include "HL7Repetition.h"
#include <vector>
class HL7RepetitionCollection
{
public:
	HL7RepetitionCollection();
	~HL7RepetitionCollection();
	/*
	*@brief 在末尾添加重复字段元素
	*/
	void AddRepetition(HL7Repetition repetition);

	/*
	*@brief 在index位置插入重复字段元素，合法的index从0开始
	*/
	void AddRepetition(HL7Repetition repetition, std::size_t index);

	/*
	*@brief 删除指定位置的重复字段元素,合法的index从0开始,并返回是否删除成功
	*/
	bool DeleteRepetition(HL7Repetition repetition, std::size_t index);

	/*
	*@brief 返回重复字段集合
	*/
	std::vector<HL7Repetition> GetRepetitionVector();


	void ClearRepetition();
private:
	std::vector<HL7Repetition> m_repetitionVect;
};

#endif
