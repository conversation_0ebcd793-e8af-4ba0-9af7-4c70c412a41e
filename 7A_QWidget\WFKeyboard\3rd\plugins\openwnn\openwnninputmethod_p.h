/****************************************************************************
**
** Copyright (C) 2016 The Qt Company Ltd.
** Contact: https://www.qt.io/licensing/
**
** This file is part of the Qt Virtual Keyboard module of the Qt Toolkit.
**
** $QT_BEGIN_LICENSE:GPL$
** Commercial License Usage
** Licensees holding valid commercial Qt licenses may use this file in
** accordance with the commercial license agreement provided with the
** Software or, alternatively, in accordance with the terms contained in
** a written agreement between you and The Qt Company. For licensing terms
** and conditions see https://www.qt.io/terms-conditions. For further
** information use the contact form at https://www.qt.io/contact-us.
**
** GNU General Public License Usage
** Alternatively, this file may be used under the terms of the GNU
** General Public License version 3 or (at your option) any later version
** approved by the KDE Free Qt Foundation. The licenses are as published by
** the Free Software Foundation and appearing in the file LICENSE.GPL3
** included in the packaging of this file. Please review the following
** information to ensure the GNU General Public License requirements will
** be met: https://www.gnu.org/licenses/gpl-3.0.html.
**
** $QT_END_LICENSE$
**
****************************************************************************/

#ifndef OPENWNNINPUTMETHOD_P_H
#define OPENWNNINPUTMETHOD_P_H

//
//  W A R N I N G
//  -------------
//
// This file is not part of the Qt API.  It exists purely as an
// implementation detail.  This header file may change from version to
// version without notice, or even be removed.
//
// We mean it.
//

#include "qinputcontext.h"

class OpenWnnInputMethodPrivate;

class OpenWnnInputMethod : public QInputContext
{
    Q_OBJECT
    Q_DECLARE_PRIVATE(OpenWnnInputMethod)

public:
    explicit OpenWnnInputMethod(QObject *parent = NULL);
    ~OpenWnnInputMethod();

//    QList<QVirtualKeyboardInputEngine::InputMode> inputModes(const QString &locale);
//    bool setInputMode(const QString &locale, QVirtualKeyboardInputEngine::InputMode inputMode);
//    bool setTextCase(QVirtualKeyboardInputEngine::TextCase textCase);

    bool keyEventFun(Qt::Key key, const QString &text, Qt::KeyboardModifiers modifiers);

//    QList<QVirtualKeyboardSelectionListModel::Type> selectionLists();
//    int selectionListItemCount(QVirtualKeyboardSelectionListModel::Type type);
//    QVariant selectionListData(QVirtualKeyboardSelectionListModel::Type type, int index, QVirtualKeyboardSelectionListModel::Role role);
//    void selectionListItemSelected(QVirtualKeyboardSelectionListModel::Type type, int index);

protected:
    bool eventFilter(QObject *obj, QEvent *event);
    QString identifierName();
    QString language();
    void reset();
    void update();
    bool isComposing() const;
    QString inputText;
private:
    QScopedPointer<OpenWnnInputMethodPrivate> d_ptr;
};

#endif
