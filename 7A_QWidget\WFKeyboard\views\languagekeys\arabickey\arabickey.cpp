#include "arabickey.h"

#include "common/keyboardtoolbutton/keyboardtoolbutton.h"

ArabicKey::<PERSON><PERSON><PERSON>(const QString& name) :
    LanguageBase<PERSON><PERSON>(name)
{
    InitButtons();
}

void ArabicKey::InitButtons()
{
    QMap<int, QList<KeyBoardToolButton*> > map;
    QList<KeyBoardToolButton*> firstButtons, secondButtons, thirdButtons;

    firstButtons.append(new KeyBoardToolButton("\u0636","\u0611","",""));
    firstButtons.append(new KeyBoardToolButton("\u0635","","",""));
    firstButtons.append(new KeyBoardToolButton("\u062B","","",""));
    firstButtons.append(new KeyBoardToolButton("\u0642","","",""));
    firstButtons.append(new KeyBoardToolButton("\u0641","","",""));
    firstButtons.append(new KeyBoardToolButton("\u063A","","",""));
    firstButtons.append(new KeyBoardToolButton("\u0639","","",""));
    firstButtons.append(new KeyBoardToolButton("\u0647","","",""));
    firstButtons.append(new KeyBoardToolButton("\u062E","","",""));
    firstButtons.append(new KeyBoardToolButton("\u062D","","",""));
    firstButtons.append(new KeyBoardToolButton("\u062C","","",""));
    firstButtons.append(new KeyBoardToolButton("\u062F","","",""));

    secondButtons.append(new KeyBoardToolButton("\u0634","","",""));
    secondButtons.append(new KeyBoardToolButton("\u0633","","",""));
    secondButtons.append(new KeyBoardToolButton("\u064A","","",""));
    secondButtons.append(new KeyBoardToolButton("\u0628","","",""));
    secondButtons.append(new KeyBoardToolButton("\u0644","","",""));
    secondButtons.append(new KeyBoardToolButton("\u0627","","",""));
    secondButtons.append(new KeyBoardToolButton("\u062A","","",""));
    secondButtons.append(new KeyBoardToolButton("\u0646","","",""));
    secondButtons.append(new KeyBoardToolButton("\u0645","","",""));
    secondButtons.append(new KeyBoardToolButton("\u0643","","",""));
    secondButtons.append(new KeyBoardToolButton("\u0637","","",""));

    thirdButtons.append(GetLeftCapsLockBtn());
    thirdButtons.append(new KeyBoardToolButton("\u0626","","",""));
    thirdButtons.append(new KeyBoardToolButton("\u0621","","",""));
    thirdButtons.append(new KeyBoardToolButton("\u0624","","",""));
    thirdButtons.append(new KeyBoardToolButton("\u0631","","",""));
    thirdButtons.append(new KeyBoardToolButton("\u0644\u0627","","",""));
    thirdButtons.append(new KeyBoardToolButton("\u0649","","",""));
    thirdButtons.append(new KeyBoardToolButton("\u0629","","",""));
    thirdButtons.append(new KeyBoardToolButton("\u0648","","",""));
    thirdButtons.append(new KeyBoardToolButton("\u0632","","",""));
    thirdButtons.append(new KeyBoardToolButton("\u0638","","",""));
    thirdButtons.append(GetRightCapsLockBtn());

    map.insert(0,firstButtons);
    map.insert(1,secondButtons);
    map.insert(2,thirdButtons);

    SetButtonsMap(map);
    SetTranslate("الصينية .","إنجليزي","الرياضيات","الفضاء");
}

