﻿#ifndef _OBX_H_
#define _OBX_H_
#include "../common/HL7Segment.h"
#include "../interface/base.h"
#include "../interface/IOBX.h"
class OBX :
	public HL7Segment, public IOBX
{
public:
	OBX();
	virtual ~OBX();
	DECLARE_OBJECTBASE

	BEGIN_IMPL_QUERYIF(OBX)
		IMPL_QUERYIF_BYPATH(IF_OBJECTBASE, IHL7Segment, IObjectBase)
		IMPL_QUERYIF(IF_HL7SEGMENT, IHL7Segment)
		IMPL_QUERYIF(IF_OBX, IOBX)
		END_IMPL_QUERYIF()
	/*
	 *	\brief 递增ID, index 0
	 */
	void SetOBXIndex(const char* obxIndexStr);

	void GetOBXIndex(char** obxIndexStr);

	/*
	 *	\brief 值类型，index 1
	 */
	void SetValueType(const char* valueTypeStr);

	void GetValueType(char** valueTypeStr);

	/*
	 *	\brief 项目ID, index 2
	 */
	void SetItemID(const char* itemIDStr);

	void GetItemID(char** itemIDStr);

	/*
	 *	\brief 项目名称, index 3
	 */
	void SetItemName(const char* itemName);

	void GetItemName(char** itemName);

	/*
	 *	\brief 结果，index 4
	 */
	void SetItemResult(const char* itemResultStr);

	void GetItemResult(char** itemResultStr);

	/*
	*	\brief code卡批号，index 4
	*/
	void SetCodeBatchNo(const char* codeBatchNo);

	void GetCodeBatchNo(char** codeBatchNo);

	/*
	*	\brief 试剂卡批号，index 4
	*/
	void SetReagentCardNo(const char* reagentCardNo);

	void GetReagentCardNo(char** reagentCardNo);

	/*
	 *	\brief 单位，index 5
	 */
	void SetUnit(const char* unitStr);

	void GetUnit(char** unitStr);

	/*
	 *	\brief 参考值范围, index 6
	 */
	void SetRefValue(const char* refValue);

	void GetRefValue(char** refValue);

	/*
	 *	\brief 不正常标记，index 7
	 */
	void SetUnnormalMark(const char* unnormalMark);

	void GetUnnormalMark(char** unnormalMark);

	/*
	 *	\brief 结果状态，index 10
	 */
	void SetResultState(const char* resultStateStr);

	void GetResultState(char** resultStateStr);

	/*
	*	\brief 原始结果, index 12
	*/
	void SetOriginalResult(const char* originalResult);

	void GetOriginalResult(char** originalResult);

	/*
	 *	\brief 检验日期, index 13
	 */
	void SetTestTime(const char* testDateStr);

	void GetTestTime(char** testDateStr);

	/*
	*	\brief 检验医生, index 15
	*/
	void SetTestPhysician(const char* testPhysician);

	void GetTestPhysician(char** testPhysician);

	virtual void Parse(const char* segmentStr, EncodingCharacters encodingCharacters);

	OBX& operator= (OBX& obx);

	void BuildItemResult();

	void ParseItemResult();
private:
	HL7Field m_obxIndex;//递增ID，从1开始
	HL7Field m_valueType;//这里只有两个取值，NM和ST，NM代表数值，ST代表字符串
	HL7Field m_itemID;//observation identifier
	HL7Field m_itemName;//observation sub-id
	HL7Field m_itemResult;//observation sub-id
	HL7Field m_unit;
	HL7Field m_refValue;
	HL7Field m_unNormalMark;
	HL7Field m_resultState;//Observation Result State "F"
	HL7Field m_originalResult;
	HL7Field m_testTime;//Date/Time of the observation
	HL7Field m_testPhysician;

	HL7Component m_itemResultText;//结果
	HL7Component m_codeBatchNo;//code卡批号
	HL7Component m_reagentCardNo;//试剂卡批号
};

REGISTER_CLASS(OBX);

#endif
