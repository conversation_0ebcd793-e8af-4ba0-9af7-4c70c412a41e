#ifndef CSYSTEMLOGWIDGET_H
#define CSYSTEMLOGWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-08-06
  * Description: 系统日志
  * -------------------------------------------------------------------------
  * History: 20250225 添加功能
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QDir>
#include <QLabel>
#include <QPushButton>
#include "CNewLabelDate.h"
#include "CDateTimeWidget.h"

class CSystemLogWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CSystemLogWidget(QWidget *parent = nullptr);

signals:
    void SignalReturn();
    void SignalUpdateInfo(QString strInfo);
    void SignalExportError(QString strError);
    void SignalExportEnd();

private slots:
    void _SlotExportBtn();
    void _SlotUpdateInfo(QString strInfo);
    void _SlotExportError(QString strError);
    void _SlotExportEnd();
    void _SlotShowDateWidget();
    void _SlotConfirmDate(const QString &strDate);

private:
    void _Thread2Export(QString strBeginDate, QString strEndDate);
    bool _ExportLoginLog(QString strBeginDate, QString strEndDate);
    bool _ExportDB(QString strExportDir);
    bool _ExportResources(QString strExportDir);
    bool _ExportLog(QString strExportDir, QString strBeginDate, QString strEndDate);
    bool _ExportPdf(QString strExportDir, QString strBeginDate, QString strEndDate);
    bool _ExportXlsx(QString strExportDir, QString strBeginDate, QString strEndDate);
    bool _ZipFolder(QString strFolderName);
    bool _ContinueExport();
    void _DelZipDirAndFile();
    QStringList _GetMacthDateFilesFromDir(int iFileType, QDir qDir, QString strBeginDate, QString strEndDate);

private:
    void _InitWidget();
    void _InitLayout();

private:
    CHNewLabelDate *m_pBeginDate, *m_pEndDate;
    QLabel *m_pGrayLabel, *m_pInfoLabel;
    QPushButton *m_pExportBtn;
    QPushButton *m_pReturnBtn;
    CDateTimeWidget *m_pCDateTimeWidget;

private:
    enum {eLog, ePdf, eXlsx};
    enum {eBeginDate, eEndDate};
    bool m_bExporting;
    const QString m_strErrString;
    QString m_strLoginLogPath;
    QString m_strFolderName;
    int m_iDateType;
};

#endif // CSYSTEMLOGWIDGET_H
