#include "russiakey.h"
#include <QDebug>

#include "common/keyboardtoolbutton/keyboardtoolbutton.h"

Russiakey::Russiakey(QString name) :
    LanguageB<PERSON><PERSON><PERSON>(name)
{
    InitButtons();
}

Russiakey::~Russiakey()
{
}

void Russiakey::InitButtons()
{
    QMap<int, QList<KeyBoardToolButton*> > map;
    QList<KeyBoardToolButton*> firstButtons, secondButtons, thirdButtons;

    firstButtons.append(new KeyBoardToolButton("\u0439","\u0419","",""));
    firstButtons.append(new KeyBoardToolButton("\u0446","\u0426","",""));
    firstButtons.append(new KeyBoardToolButton("\u0443","\u0423","",""));
    firstButtons.append(new KeyBoardToolButton("\u043A","\u041A","",""));
    firstButtons.append(new KeyBoardToolButton("\u0435","\u0415","\u0451","\u0401"));
    firstButtons.append(new KeyBoardToolButton("\u043D","\u041D","",""));
    firstButtons.append(new KeyBoardToolButton("\u0433","\u0413","",""));
    firstButtons.append(new KeyBoardToolButton("\u0448","\u0428","",""));
    firstButtons.append(new KeyBoardToolButton("\u0449","\u0429","",""));
    firstButtons.append(new KeyBoardToolButton("\u0437","\u0417","",""));
    firstButtons.append(new KeyBoardToolButton("\u0445","\u0425","",""));

    secondButtons.append(new KeyBoardToolButton("\u0444","\u0424","",""));
    secondButtons.append(new KeyBoardToolButton("\u044B","\u042B","",""));
    secondButtons.append(new KeyBoardToolButton("\u0432","\u0412","",""));
    secondButtons.append(new KeyBoardToolButton("\u0430","\u0410","",""));
    secondButtons.append(new KeyBoardToolButton("\u043F","\u041F","",""));
    secondButtons.append(new KeyBoardToolButton("\u0440","\u0420","",""));
    secondButtons.append(new KeyBoardToolButton("\u043E","\u041E","",""));
    secondButtons.append(new KeyBoardToolButton("\u043B","\u041B","",""));
    secondButtons.append(new KeyBoardToolButton("\u0434","\u0414","",""));
    secondButtons.append(new KeyBoardToolButton("\u0436","\u0416","",""));
    secondButtons.append(new KeyBoardToolButton("\u044D","\u042D","",""));

    thirdButtons.append(GetLeftCapsLockBtn());
    thirdButtons.append(new KeyBoardToolButton("\u044F","\u042F","",""));
    thirdButtons.append(new KeyBoardToolButton("\u0447","\u0427","",""));
    thirdButtons.append(new KeyBoardToolButton("\u0441","\u0421","",""));
    thirdButtons.append(new KeyBoardToolButton("\u043C","\u041C","",""));
    thirdButtons.append(new KeyBoardToolButton("\u0438","\u0418","",""));
    thirdButtons.append(new KeyBoardToolButton("\u0442","\u0422","",""));
    thirdButtons.append(new KeyBoardToolButton("\u044C","\u042C","\u044A","\u042A"));
    thirdButtons.append(new KeyBoardToolButton("\u0431","\u0411","",""));
    thirdButtons.append(new KeyBoardToolButton("\u044E","\u042E","",""));
    thirdButtons.append(GetRightCapsLockBtn());

    map.insert(0,firstButtons);
    map.insert(1,secondButtons);
    map.insert(2,thirdButtons);

    SetButtonsMap(map);
    SetTranslate("Китайский","Английский","Расчет","Пробел");
}



