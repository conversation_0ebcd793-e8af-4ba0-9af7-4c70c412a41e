#ifndef CPERFORMANCETEST_H
#define CPERFORMANCETEST_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: system
  * Date: 2024-12-19
  * Description: 性能测试
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QComboBox>
#include <QCheckBox>
#include <QPushButton>
#include <QTextBrowser>
#include <QStackedWidget>
#include <QTableWidget>
#include "CCmdBase.h"
#include "CLabelComboBox.h"
#include "CLabelLineEdit.h"
#include "CLightOneTiming.h"
#include "CBusyProgressBar.h"

class CPerformanceTest : public QWidget, public CCmdBase
{
    Q_OBJECT
public:
    explicit CPerformanceTest(QWidget* parent = nullptr);
    ~CPerformanceTest();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant& qVarData) override;
    void ClearAllData();
    void SetFluorescenceType(int iTypeIndex);  // 设置荧光类型：0-荧光片，1-染料

private slots:
    void _SlotFamMDTBtn();
    void _SlotHexMDTBtn();
    void _SlotRoxMDTBtn();
    void _SlotCY5MDTBtn();
    void _SlotMachineChanged(int iMachineID);
    void _SlotRatioChanged(int iRatioIndex);
    void _SlotTimingEnd(void);
    void _SlotTimingStopped(void);   // 时序停止槽函数
    void _SlotStopTest(void);        // 停止测试槽函数

private:
    void _InitWidget();
    void _SetTableItem(int iRow, int iCol, QString strText);
    void _SetTableWidget(int iRow, int iCol, int iWidth, int iHeight, QWidget* pUIWidget);
    void _SetPrecisionTableItem(int iRow, int iCol, QString strText);  // 设置精密度表格项
    void _ReceiveMDTData(const QVariant& qVarData);
    bool _SaveXlsxData(int colorIndex, int ratioIndex);
    void _UpdateRatioComboBoxAndTable();  // 更新光衰减度/浓度下拉框和表格标题

    double calculateStdevP(const QList<double>& data);
    double pearsonOptimized(const QList<double>& x, const QList<double>& y);
private:

    typedef struct {
        double value[4];
    }sHoleFl_t;

    QList <sHoleFl_t> m_flHole1AllDataVlist;
    QList <sHoleFl_t> m_flHole2AllDataVlist;

    typedef struct {
        QList <double> hole1VList;
        QList <double> hole2VList;
        double hole1Average;
        double hole2Average;
        double hole1StdevP;
        double hole2StdevP;
        double hole1Cv;
        double hole2Cv;
        double allHoleCv;
        bool bIsTested;
    }sFlData_t;

    CLightOneTiming* m_pLightOneTiming;
    sFlData_t m_flData[4][5];
    int m_colorIndex;
    int m_ratioIndex;
    bool m_bLinearTest;
    int m_iFluorescenceType;  // 荧光类型：0-荧光片，1-染料

    CLabelComboBox* m_pMachineComboBox;
    CLabelComboBox* m_pRatioComboBox;
    CBusyProgressBar* m_pCBusyProgressBar;
    QPushButton* m_pFamMDTBtn, * m_pHexMDTBtn, * m_pRoxMDTBtn, * m_pCY5MDTBtn;
    QTableWidget* m_pTableWidget;
    QTableWidget* m_pPrecisionTableWidget;  // 新增的精密度表格
};
#endif // CPERFORMANCETEST_H 