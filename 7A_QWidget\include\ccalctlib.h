﻿#ifndef CCALCTLIB_H
#define CCALCTLIB_H

#include <QtCore/qglobal.h>
#include <QObject>
#include <QPointF>

#if defined(CCALCTLIB_LIBRARY)
#   define CCALCTLIB_EXPORT Q_DECL_EXPORT
#else
#  define CCALCTLIB_EXPORT Q_DECL_IMPORT
#endif

class  CCalCTLib
{
public:
    CCalCTLib();

    // @brief add by chenhao
    enum fitModelEm
    {
        kFourParamsSigmoid,
        kFiveParamsSigmoid,
        kSevenParamsSigmoid,
        kDelatRnSigmoid
    };

    //取pointf数据
    QList<QPointF> getLMPointList();
    QList<QPointF> getLMSPointList();
    QList<QPointF> getLMDSPointList();



    //
    QList<QPointF> getBeseLinePointList();
    QList<QPointF> getBeseLinePointListS();
    QList<QPointF> getBaseLinePointListDS();


    QList<QPointF> getDeltaLinePointList();
    QList<QPointF> getDeltaLinePointListS();
    QList<QPointF> getDeltaLinePointListDS();


    // 一阶导
    QList<QPointF> getDerivatePointListS();

    // 平滑
    QList<QPointF> getSmoothPointList();

    // 获取Ct
    // 抬升值 基线到最大值的差值！
    QList<qreal> getCTInfoList();// 阈值，抬升值，CT

    QStringList getResultInfoStringList();// 阈值，抬升值，CT,基线

    //@brief add by chenhao
    QList<qreal> getSModelCTInfoList();

    QList<qreal> getDSModelCTInfoList();

    QList<qreal> getSmoothModelCTInfoList();

    QStringList getSModelCTInfoStringList();

    QStringList getDSModelCTInfoStringList();

    QStringList getSmoothModelCTInfoStringList();

    QString getFinalResultInfo();

public:
    // 获取各个数据段
    // 获取基线数据段
    QList<QPointF> getBaseLinePointFList(); // 一条

    // 获取平滑数据段
    QList<QPointF> getSmoothPointFList(); // 一条

    // 获取平滑数据段
    QList<QPointF> getDeltaPointFList(); // 一条

    // 获取平滑数据段
    QList<QPointF> getDeltaRnCorrectPointFList(); // 一条

    // 获取阈值抬升值与Ct值
    QStringList getCtValueInfoStringList();

    // @brief 获取拟合预测点
    // @param nFitMdel是fitModelEm枚举值
    QList<QPointF> getSigmoidFitPointFList(int nFitModel);

    // @brief 获取拟合数据原始点
    // @param nFitMdel是fitModelEm枚举值
    QList<QPointF> getSigmoidRawDataFList(int nFitModel);


    // @brief 获取一阶导点
    // @param nFitMdel是fitModelEm枚举值
    QList<QPointF> getFirstDevPointFList(int nFitModel);

    // @brief 获取二阶导数点
    // @param nFitMdel是fitModelEm枚举值
    QList<QPointF> getSecondDevPointFList(int nFitModel);

    // @brief 获取二阶导数导数最大值点
    // @param nFitMdel是fitModelEm枚举值
    QStringList getSecondDevMaxValueStringList(int nFitModel);

    // @brief ct值Sigmoid模型
    // @param dThreadValue 给定阈值
    float CalcCTFromThreadSigmoid(const qreal dThreadValue);

    // @brief  ct值DSigmoid模型
    // @param dThreadValue 给定阈值
    float CalcCTFromThreadDSigmoid(const qreal dThreadValue);


    // @brief  ct值smooth模型
    // @param dThreadValue 给定阈值
    float CalcCTFromThreadSmooth(const qreal dThreadValue);

    // add end

    // 版本号
    QString getVersion();



public:
    // init Calc
    static void initCalc();

    // 计算ct
    // strConfing =  "抬升值;荧光阈值;一阶导数阈值;基线起始点,基线截止点"
    float CalcCtValue(const QList<qreal> qDataRaw,QString strConfig = "");


    //  根据threashold计算Ct
    float CalcCTFromThread(const qreal dThreadValue);


    // 找最长直线 T值
    void setLongestTValue(double dValue);
private:
    struct ProcessValues
    {
        ProcessValues()
        {clear();};
        void clear()
        {
            m_fFirstDevThreshold = 0;
            m_fUpliftThreshold = 0;
            m_fMaxFirstDevValue = 0;
            m_fBaseLineAvgValue = 0;
            m_fBenchValue = 0;
            m_threshould = 0;
            m_fCtValue = 0;
            m_fEnlargeNum = 0;
            m_fThreshouldLittleUplift = 0;
            m_bNegativeCurve = false;
            m_bLittleUpliftCurve = false;
            m_bPreprocessErr = false;
            m_fSmoothListPoint.clear();
            m_fDelatListPoint.clear();
            m_fNormalizationListPoint.clear();
            m_fCorrectCurveListPoint.clear();
        };
        QList<QPointF> m_fSmoothListPoint; // 平滑处理的荧光数据
        QList<QPointF> m_fDelatListPoint; // 平滑后的数据-基线预测数据
        QList<QPointF> m_fNormalizationListPoint;// 归一化后的值；（将y值归一化为0-1）；
        QList<QPointF> m_fCorrectCurveListPoint;// 展示给用户看的修正deltaRn曲线
        bool m_bLittleUpliftCurve{false}; // 是否小抬升
        float m_fThreshouldLittleUplift{0}; // 小抬升阈值
        bool  m_bNegativeCurve = {false}; // 阴性曲线
        float m_fBaseLineAvgValue{0};    //基线平均值
        float m_fMaxFirstDevValue{0};    //一阶导最大值
        float m_fBenchValue{0};    //抬升值
        float m_threshould{0}; // 阈值
        float m_fUpliftThreshold{0}; // 抬升阈值
        float m_fFirstDevThreshold{0}; // 一阶导数阈值
        float m_fCtValue{0}; // Ct值
        float m_fEnlargeNum{0}; // 放大倍数
        bool m_bPreprocessErr{false};
    };


    struct BaseLineValues
    {
        BaseLineValues()
        {clear();};
        void clear()
        {
            m_iBaseLineStart = 0;
            m_iBaseLineEnd = 0;
            m_fSlop = 0;
            m_fIntercept = 0;
            m_BaseLinefitDegree = 0;
            m_bHasBaseLine = false;
            m_fBaseLine_data.clear();
        };

        bool m_bHasBaseLine{false};// 是否有平缓基线
        int m_iBaseLineStart{0};// 基线起始点
        int m_iBaseLineEnd{0}; // 基线截止点
        float m_fSlop{0}; // 斜率
        float m_fIntercept{0};   // 截距
        float  m_BaseLinefitDegree{0}; // 基线拟合度
        QList<QPointF> m_fBaseLine_data; // 这个是基线数据  考虑将基线归零
    };

    struct FitModelVaules
    {
        FitModelVaules()
        {clear();};
        void clear()
        {
            m_fitModelParamListDouble.clear();
            m_fFitRawDataListPoint.clear();
            m_fFitResultListPoint.clear();
            m_fFirstDevResultListPoint.clear();
            m_fSecondDevResultListPoint.clear();
            m_fitDegree = 0;
            m_fInflectionPoint = 0;
            m_iFindSigmoidPointLength = 0;
            m_offset = 0;
            m_max_first_derivative_index = 0;
            m_max_second_derivative_index = 0;
            m_SecondDevThreshouleData = 0;
        }
        QList<QPointF> m_fFitRawDataListPoint;  // 拟合的原始数据，目前是平滑数据
        QList<double> m_fitModelParamListDouble;  // 拟合模型参数
        QList<QPointF> m_fFitResultListPoint; // 拟合后的荧光数据
        QList<QPointF> m_fFirstDevResultListPoint;
        QList<QPointF> m_fSecondDevResultListPoint;
        int m_iFindSigmoidPointLength{0}; // sigmoid拟合 数据长度
        double  m_fitDegree{0};  // 拟合度
        float   m_fInflectionPoint{0}; // 曲线拐点
        float m_offset{0};// 残差
        double m_max_first_derivative_index{0};
        double m_max_second_derivative_index{0};
        float m_SecondDevThreshouleData{0};
    };

    struct ParamConfig
    {
        // 配置参数形式：
        // 抬升值;荧光阈值;一阶导数阈值;基线起始点,基线截止点
        ParamConfig()
        {clear();};
        void clear()
        {
            m_upliftThreshould = 0;
            m_FlThreshould  = 0;
            m_firstDevThreshould = 0;
            m_BaseLineStartPoint = 0;
            m_BaseLineEndPoint = 0;
            m_bManualBaseLine =false;
            m_bModifyUpValue = false;
            m_bAmplify = false;
            m_fAmplify = 1.0;
        };
       float m_upliftThreshould{0};
       float m_FlThreshould{0};
       float m_firstDevThreshould{0};
       int m_BaseLineStartPoint{0};
       int m_BaseLineEndPoint{0};
       bool m_bManualBaseLine{false};
       bool m_bModifyUpValue{false};
       float m_fAmplify{1.0};
       bool m_bAmplify{false};
    };

private:

    // fourSigmoid

    void fourParamSigmoidFun_LM(FitModelVaules& fitModul,const QList<QPointF>& fitData);
    // 解析参数
    void parseParam(const QString& strParam);

    bool bNegativeCure();

    bool _bCalcEndProcess();

    void getFirstDevValue();

    void modifyNegativeSlopeBaseline(BaseLineValues& baseLineValue,const QList<QPointF>& smPoint);

    //chenhao  判断假抬升
    //@brief 小抬升曲线判断
    void littleUpliftCorrect();

    bool IsLittleUpliftCurve();

    void littleUpliftCurveReCalcCt();

    float getLittleUpliftFirstDevThreshoule();
    //chenhao end
    // 对连续下降推平的曲线，做一个判断
    // 如果基线>0 则不替换；
    bool bReplaceDeclineData(const std::vector<double>& data ,int baseline_len);

    void ClearAllData();
    int GetErrorCode();

    bool preprocess_algorithm();

    quint8 min_data_index(const QList<QPointF>& data,int start,int end);

    // 计算 y=ax+b
    void calc_line_ab(const QList<QPointF>& data,int start,int end);

    int CalcGradient(QList<QPointF> &data);

    // lossM
    void ceresLM(const QList<QPointF> &raw);

    void ceresLM_Double(const QList<QPointF> &raw);

    void ceresSP(const QList<QPointF> &raw);

    // 拟合
    bool fit_algorithm_ex();

    //寻找最长直线的函数
    int find_baseLine_Points_ex(const QList<QPointF>& data);



    // @brief add by chenhao

    // init log
    static void initLoggerLib();

    // 基线算法，使用平滑数据计算
    int baseLine_calc();

    // 计算阈值
    int threshoule_calc();

    // 计算Ct值
    int cycleThreshoule_calc();

    // 自动修正基线
    int AutoAadjustBaseline();

    // 修正曲线
    int specialCurveModify();

    // 二阶导数法计算阈值
    void cycleThreshoule_SecDerivative_calc();

    // 阈值法计算Ct值
    void cycleThreshoule_th_calc(bool bManualThreshold = false);

    float cycleThreshouleHandle(const QList<QPointF>& deltaList,float fThreshouldValue);

    // sigmoid 参数计算
    void fourSigmoidFun_LM();


    // 4参数拟合原始曲线数据
    void smoothfourSigmoidFun_LM();


    // 4参数拟合delatRn曲线 后续拟合曲线可以优化成一个函数
    void delatRnfourSigmoidFun_LM();

    // 五参数sigmoid参数计算
    void fiveSigmoidFun_LM();
    // doubleSigmoid参数计算
    void doubleSigmoidFun_LM();

    void oldCeresLMProcess();


    // 从0-20循环开始 选择15个最合适的基线
    int select_baseline(const QList<QPointF> &data);

    // 固定基线起始点开始确定基线区
    int manualBaselineHandle(const QList<QPointF> &data, BaseLineValues& baseLineValue,const ParamConfig& paramConfig);

    // chenhaoEnd



    // 基线算法
    int baseLine_algorithm(int iFitAlgorithm);

    // 计算偏移
    float CalcOffset(const QList<QPointF> &Ddata, const QList<QPointF> &Sdata, int iStart, int iEnd);

    // 计算阈值
    int calc_Thread_value_ex(int iFitAlgorithm);

    // 计算Ct
    float calc_CT_value(const qreal dThreadValue = 0);


private:
    // double s
    // s
    enum{
        Double_SIGMOID,
        SIGMOID
    };

    // @brief add by  chenhao

    float m_fSigmoidCTValue{0};
    float m_f6CTValue{0};



    ParamConfig m_paramConfig;
    FitModelVaules m_CorrectCurveFourParamSigmoid;
    FitModelVaules m_smoothfourParamSigmoid;
    FitModelVaules m_fourParamSigmoid;
    FitModelVaules m_delatRnfourParamSigmoid;
    FitModelVaules m_fiveParamSigmoid;
    FitModelVaules m_sevenParamSigmoid;
    ProcessValues m_ProcessValue;
    BaseLineValues m_baseLineValue;

    // chenhao end
    // 连续直线的段落


    struct Section{
        Section()
        {
            iSectionLen = 0; // 段落长度
            iSectionStartX = 0;// 段落起始点
        }
        int iSectionLen;
        int iSectionStartX;
        QPointF qPointf; // 最小一阶导值
        bool bmerge{false};
    };





    int m_iSectionLen;
    float m_fBenchValue;// 抬升值f

    int m_iErrorCode;
    int m_iLocalNum;

    QList<QPointF> data_raw,data_doubleS_fit,data_baseLine,
    data_delta,data_delta_double_s,data_delta_s,data_sigmoid_fit,
    dataRaw,dataRaw_fit,data_noise,dataDelta_fit;

    int startPoint,endPoint;    //基线点
    int thread_startPoint,thread_endPoint,thread_startPoint_6, thread_startPoint_s,thread_endPoint_double_s,thread_endPoint_s;  //阈值点
    float m_fSigmoidThreaddef,m_fDoubleSThreaddef;
    float m_fThreaddef; // 阈值
    float line_b,line_a;
    int m_iPcrEndPonit;
    bool m_bCanUseSigmoid;

    QStringList m_strCtConfigList;
    int m_iCtConfigLength;
    int m_iFindSigmoidPoint;

    QString strRemark;


    qreal m_dCTValue;
    //
    QList<QPointF> m_dLMPointList;
    QList<qreal> m_dLMParameList;
    QList<QPointF> m_dLMDoublePointList;
    QList<qreal> m_dLMDoubleParameList;
    QList<QPointF> m_dSPPointList;
    QList<qreal> m_dSParameList;
    qreal m_dLMRSquared;
    qreal m_dDoubleSRSquared;
    qreal m_dDoubleLM_E, m_dDoubleLM_G;// 双S拟合初始化的两个重要参数  ?

    QList<QPointF> m_dExtLogPointListBaseLineS;
    QList<QPointF> m_dExtLogPointListBaseLineDS;
    QList<QPointF> m_dExtLogPointListDeltaLineS;
    QList<QPointF> m_dExtLogPointListDeltaLineDS;
    QList<QPointF> m_dExtLogPointListDerivateLineS;
    QList<QPointF> m_dExtLogPointListDerivateLineDS;

    double m_dLongestTValue;                    // UI参数
    QList<QPointF> m_dSmoothListLine;

    int m_iMinPointCountS;
    double m_dMaxResidualS;
    int m_iMinPointCountDS;
    double m_dMaxResidualDS;

    QString m_strSor6;
    double m_dFindTh;
    double m_dFind_X, m_dFind_X_2;

    QString m_strShowInfoMsg;

};
#endif // CCALCTLIB_H
