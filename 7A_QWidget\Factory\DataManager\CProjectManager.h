#ifndef CPROJECTMANAGER_H
#define CPROJECTMANAGER_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-01-22
  * Description: 项目管理
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QPushButton>
#include <QTableWidget>

#include "CLineEdit.h"

class CModifyParam;
class CProjectManager : public QWidget
{
    Q_OBJECT
public:
    explicit CProjectManager(QWidget *parent = nullptr);
    ~CProjectManager();

private slots:
    void _SlotListBtn();
protected:
    void showEvent(QShowEvent *pEvent) override;
    void hideEvent(QHideEvent *pEvent) override;

private:
    void _ModifyParam();
    void _ShowDetail();
    void _ImportProject();
    void _ExportProject();
    void _UpdateTable();

private:
    void _InitWidget();
    void _InitLayout();

private:
    QTableWidget *m_pTableWidget;
    QList<QPushButton *> m_pBtnList;
    QString m_strTipsText;
    CModifyParam* m_pModifyDialog;
};

#endif // CPROJECTMANAGER_H
