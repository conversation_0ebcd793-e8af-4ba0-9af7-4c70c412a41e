﻿#ifndef _ACK_H_
#define _ACK_H_

#include "../common/HL7Message.h"
#include "../interface/IACK.h"
#include "../interface/IMSH.h"
#include "../interface/IMSA.h"
#include "../interface/IERR.h"
#include "../segment/MSH.h"
#include "../segment/MSA.h"
#include "../segment/ERR.h"
/***ACK消息结构**
 * MSH
 * MSA
 * [ERR]
 **/

class ACK :
	public HL7Message, public IACK
{
public:
	ACK();
	~ACK();
	DECLARE_OBJECTBASE

	BEGIN_IMPL_QUERYIF(ACK)
			IMPL_QUERYIF_BYPATH(IF_OBJECTBASE, IHL7Message, IObjectBase)
			IMPL_QUERYIF(IF_HL7MESSAGE, IHL7Message)
			IMPL_QUERYIF(IF_ACK, IACK)
			END_IMPL_QUERYIF()

	void Free();

	void SetMSH(IMSH* msh);
	bool GetMSH(IMSH* msh);

	void SetMSA(IMSA* msa);
	bool GetMSA(IMSA* msa);

	void SetERR(IERR* err);
	bool GetERR(IERR* err);

	virtual void Build();

	virtual void Parse(const char* messageStr);

private:
	MSH* m_msh;
	MSA* m_msa;
	ERR* m_err;
};

#endif
