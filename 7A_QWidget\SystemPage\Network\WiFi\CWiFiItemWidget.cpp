﻿#include "CWiFiItemWidget.h"
#include <QDebug>
#include <QBoxLayout>
#include "PublicFunction.h"

CWiFiItemWidget::CWiFiItemWidget(const QStringList &strInfoList, QWidget *parent)
    : QWidget(parent)
    , m_eStatus(eWiFi_Unconnect)
    , m_index(0)
    , m_strName("")
    , m_iStrength(0)
{
    if(strInfoList.size() >= 3)
    {
        m_index = strInfoList.at(0).toInt();
        m_strName = strInfoList.at(1);
        m_iStrength = strInfoList.at(2).toInt();
    }

    _InitWidget();
    _InitLayout();
}

void CWiFiItemWidget::SetInfoList(const QStringList &strInfoList)
{
    if(strInfoList.size() < 3)
        return;

    m_index = strInfoList.at(0).toInt();
    m_pIndexLabel->setText(QString::number(m_index));

    m_strName = strInfoList.at(1);
    m_pNameLabel->setText(m_strName);

    m_pStatusLabel->clear();
    m_pConnectBtn->setVisible(false);

    m_iStrength = strInfoList.at(2).toInt();
    if(1 == m_iStrength)
        SetLabelBackImage(m_pStrengthLabel, ":/image/ico/system/network/wifi1.png");
    else if(2 == m_iStrength)
        SetLabelBackImage(m_pStrengthLabel, ":/image/ico/system/network/wifi2.png");
    else if(3 == m_iStrength)
        SetLabelBackImage(m_pStrengthLabel, ":/image/ico/system/network/wifi3.png");
    else if(4 == m_iStrength)
        SetLabelBackImage(m_pStrengthLabel, ":/image/ico/system/network/wifi4.png");
    else
        SetLabelBackImage(m_pStrengthLabel, ":/image/ico/system/network/wifi5.png");
}

void CWiFiItemWidget::SetBtnVisable(bool bVisable)
{
    m_pConnectBtn->setVisible(bVisable);
}

void CWiFiItemWidget::SetStatus(WiFiStatus eStatus)
{
    m_eStatus = eStatus;

    if(eWiFi_Unconnect == eStatus)
    {
        m_pConnectBtn->setText(tr("连接"));
        m_pStatusLabel->setText("");

        if(eLanguage_Italian == gk_iLanguage)
            m_pConnectBtn->setFixedSize(130, 50);
    }
    else if(eWiFi_Connecting == eStatus)
    {
        m_pConnectBtn->setText(tr("取消"));
        m_pStatusLabel->setText(tr("正在连接"));

        if(eLanguage_Italian == gk_iLanguage)
            m_pConnectBtn->setFixedSize(130, 50);
    }
    else if(eWiFi_Connected == eStatus)
    {
        m_pConnectBtn->setText(tr("断开"));
        m_pStatusLabel->setText(tr("已连接"));

        if(eLanguage_Italian == gk_iLanguage)
            m_pConnectBtn->setFixedSize(200, 50);
    }
}

QString CWiFiItemWidget::GetName()
{
    return m_strName;
}

void CWiFiItemWidget::_SlotConnectBtn()
{    
    qDebug()<<Q_FUNC_INFO<<m_strName<<m_eStatus;
    if(eWiFi_Unconnect == m_eStatus)
        emit SignalConnect(m_strName);
    else
        emit SignalDisconnect(m_strName);
}

void CWiFiItemWidget::_InitWidget()
{
    m_pIndexLabel = new QLabel(QString::number(m_index));
    m_pIndexLabel->setFixedSize(25, 50);
    m_pIndexLabel->setAlignment(Qt::AlignCenter);

    m_pNameLabel = new QLabel(m_strName);
    m_pNameLabel->setFixedHeight(50);

    m_pStatusLabel = new QLabel;
    m_pStatusLabel->setObjectName("StatusLabel");

    int iBtnWidth = 130;
    if(eLanguage_Spanish == gk_iLanguage)
        iBtnWidth = 155;

    m_pConnectBtn = new QPushButton(tr("连接"));
    m_pConnectBtn->setFixedSize(iBtnWidth, 50);
    m_pConnectBtn->setObjectName("ConnectBtn");
    connect(m_pConnectBtn, &QPushButton::clicked, this, &CWiFiItemWidget::_SlotConnectBtn);
    m_pConnectBtn->setVisible(false);

    m_pStrengthLabel = new QLabel;
    m_pStrengthLabel->setFixedSize(40, 40);
}

void CWiFiItemWidget::_InitLayout()
{
    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addSpacing(15);
    pLayout->addWidget(m_pIndexLabel);
    pLayout->addSpacing(30);
    pLayout->addWidget(m_pNameLabel);
    pLayout->addSpacing(30);
    pLayout->addWidget(m_pStatusLabel);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pConnectBtn);
    pLayout->addSpacing(40);
    pLayout->addWidget(m_pStrengthLabel);
    pLayout->addSpacing(50);
    this->setLayout(pLayout);
}
