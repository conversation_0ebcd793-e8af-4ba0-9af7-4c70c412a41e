#ifndef CREALFL_H
#define CREALFL_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-12-26
  * Description:实时荧光
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QStackedWidget>

#include "CCmdBase.h"
#include "CLabelComboBox.h"
#include "RealFL/CFLOneWidget.h"

class QCustomPlot;

class CRealFL : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CRealFL(QWidget *parent = nullptr);
    ~CRealFL();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

public slots:
    void SlotTestStart(int iMachineID);

private slots:
    void _SlotMachineChanged(int iMachineID);
    void _SlotCardIDComboBoxChanged(const QString &strCardID);
    void _SlotCardIDLineEditTextChanged(const QString &strCardID);
    void _SlotShowCurrentTestCardID(int iMachineID);

private:
    void _ShowLast100FLID();

private:
    void _InitWidget();
    void _InitLayout();

private:
    CLabelComboBox *m_pMachineComboBox;
    CLabelLineEdit *m_pCardIDLineEdit;
    QComboBox *m_pCardIDComboBox;
    QStackedWidget *m_pStackedWidget;
    QList<CFLOneWidget *> m_pFLWidgetList;
};

#endif // CREALFL_H
