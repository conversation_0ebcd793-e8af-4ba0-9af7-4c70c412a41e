#ifndef CFAULTLOGWIDGET_H
#define CFAULTLOGWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-31
  * Description: 故障日志
  * -------------------------------------------------------------------------
  * History: 20250225 添加功能
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QComboBox>
#include <QPushButton>
#include <QTableWidget>

#include "CLineEdit.h"
#include "CBusyProgressBar.h"

class CFaultLogWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CFaultLogWidget(QWidget *parent = nullptr);
    ~CFaultLogWidget();

public slots:
    void SlotRefreshPage();

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual void hideEvent(QHideEvent *pEvent) override;

signals:
    void SignalReturn();

private slots:
    void _SlotGotoPageBtn();
    void _SlotPrePageBtn();
    void _SlotNextPageBtn();
    void _SlotQueryBtn();
    void _SlotShowAllBtn();
    void _SlotDetailBtn();
    void _SlotExportBtn();

private:
    void _Thread2Export();
    void _ShowCurrentPageAllData();   //显示当前页所有数据
    void _ShowCurrentPageQueryData(); //显示当前页查询数据
    void _UpdateTableWidget(const QList<QStringList> &strList);
    void _UpdateGroupBoxInfo();

private:
    void _InitWidget();
    void _InitLayout();

private:
    QStringList m_strTitleList;
    bool m_bRefresh;
    bool m_bShow;
    bool m_bHasQuery;
    int m_iTotalLines;
    int m_iTotalPages;
    int m_iCurrentPage;
    int m_iLeftLines;
    int m_iOnePageLines;
    QString m_strTipsText;
    QList<QStringList> m_strAllLogList, m_strQueryLogList;

private:
    QTableWidget *m_pTableWidget;

    QLabel *m_pGotoLabel1, *m_pGotoLabel2;
    CLineEdit *m_pGotoLineEdit;
    QPushButton *m_pGotoBtn;
    QPushButton *m_pPrePageBtn;
    QLabel *m_pPageLabel, *m_pLinesLabel;
    QPushButton *m_pNextPageBtn;

    QPushButton *m_pReturnBtn;
    QComboBox *m_pQueryComboBox;
    CLineEdit *m_pQueryLineEdit;
    QPushButton *m_pQueryBtn;
    QPushButton *m_pShowAllBtn;
    QPushButton *m_pDetailBtn;
    QPushButton *m_pExportBtn;
    CBusyProgressBar *m_pExportBar; 
};

#endif // CFAULTLOGWIDGET_H
