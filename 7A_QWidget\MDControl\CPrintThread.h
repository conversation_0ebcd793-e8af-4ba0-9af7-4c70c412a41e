#ifndef CMYSERIALPRINTTHREAD_H
#define CMYSERIALPRINTTHREAD_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-09-11
  * Description: 打印线程
  * -------------------------------------------------------------------------
  * History: 打印机没有取消打印指令也没有打印结束通知指令
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QTimer>
#include <QMutex>
#include <QThread>
#include <QtSerialPort>
#include "PublicParams.h"

class CPrintThread : public QObject
{
    Q_OBJECT
public:
    static CPrintThread *GetInstance();
    ~CPrintThread();

    bool GetPrinterStatus();
    void AddPrintHistoryID(int iHistoryID);
    void UpdatePrintInfo(SPrintInfoStruct sSPrintInfoStruct);
    void PrintStringData(QString strData);
    void CancelPrint();

signals:
    void SignalInitThread();
    void SignalPrintStringData(QString strData);
    void SignalAddPrintHistoryID(int iHistoryID);
    void SignalUpdatePrintInfo(SPrintInfoStruct sSPrintInfoStruct);
    void SignalPrintLeftNum(int iLeftNum);
    void SignalCleanPrint();

private slots:
    void _SlotInitThread();
    void _SlotReadSerial();
    void _SlotRunTimeout();
    void _SlotPrintStringData(QString strData);
    void _SlotAddPrintHistoryID(int iHistoryID);
    void _SlotUpdatePrintInfo(SPrintInfoStruct sSPrintInfoStruct);
    void _SlotCleanPrint();

private:
    CPrintThread();

    void _InitPort(QString strSerialName);
    void _PrintID(int iHistoryID);
    void _Add2PrintArray(uchar ucFont,QString strPrintData,QByteArray& bytePrintData);
    QList<QByteArray> _GetNewPrintArrayList(uchar ucFont,QByteArray printArray);
    void _AddNewPrintList2WriteArray(const QList<QByteArray>& newPrintArrayList,QByteArray& writeArray);
    int _GetIsPrintNums(QByteArray byteArray);
    void _SetPrintCode(int iLanguage);

private:
    static CPrintThread *m_spInstance;
    QThread *m_pThread;
    QSerialPort *m_pSerialPort;
    bool m_bOpenSerialPort;
    SPrintInfoStruct m_sSPrintInfoStruct;
    bool m_bPrinting;
    QTimer *m_pRunTimer;
    QVector<int> m_iPrintIDVector;
};

#endif // CMYSERIALPRINTTHREAD_H
