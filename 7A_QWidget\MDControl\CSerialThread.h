#ifndef CSERIALPORTTHREAD_H
#define CSERIALPORTTHREAD_H

/*******************************************************************
* @Copyright:
* @projectName: MDx-5D
* @brief:       重置串口通信
* @author:      hongxirong
* @date:        2023-06-14
-------------------------------------------------------------------
History:


*******************************************************************/

#include <QObject>
#include <QThread>
#include <QTimer>
#include <QMutex>
#include <QDateTime>
#include <QtSerialPort>
#include "PublicParams.h"

class CSerialThread : public QObject
{
    Q_OBJECT
public:
    static CSerialThread* GetInstance();
    static void FreeInstance();

    void ResetSerial(QString strNewSerialName,QString strBandRate);
    void WriteSerial(const SCanBusDataStruct& sSCanBusDataStruct);
    QByteArray GetReciveCanMsg();
    QList<QByteArray> GetReciveCanMsgList();
    bool IsOpenSuccess();

private:
    CSerialThread();
    ~CSerialThread();

signals:
    void SignalInitThread();
    void SignalExitThread();
    void SignalResetSerial(QString,QString);
    void SignalAddWriteMap(SCanBusDataStruct);
    void SignalACKOut(int);
    void SignalLog(bool bOpen,const QString &strLog);

private slots:
    void _SlotInitThread();
    void _SlotExitThread();
    void _SlotReadSerial();
    void _SlotResetSerial(QString,QString);
    void _SlotAddWriteMap(SCanBusDataStruct sSCanBusDataStruct);
    void _SlotRunTimeout();

private:
    void _InitSerial(QString strSerialName,QString strBandRate);
    void _HandleSendBuff();
    void _HandleReadBuff();
    void _HandleReadBuff(QByteArray& readBuff);
    QByteArray _GetSendData(SCanBusDataStruct sSCanBusDataStruct, quint16 quSeq);

private:
    static CSerialThread* m_spInstance;

    QThread* m_pThread;
    QString m_strSerialName;
    QSerialPort* m_pSerialPort;
    bool m_bOpenSerialPort;

    QTimer* m_pRunTimer;

    QByteArray m_readBuff;
    QMap<int,STSendStruct> m_sendFrameMap;
    QList<QByteArray> m_receiveFrameList;

    quint16 m_iSendSeq;
    QMutex* m_pMutex;

    int m_iLoseTimes; //连续丢字节次数
};

#endif // CSERIALPORTTHREAD_H
