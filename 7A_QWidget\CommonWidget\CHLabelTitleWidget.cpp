#include "CHLabelTitleWidget.h"
#include <QBoxLayout>

CHLabelTitleWidget::CHLabelTitleWidget(const QString &strTitle, QWidget *parent) : QWidget(parent)
{
    this->setFixedHeight(36);

    m_pIconLabel = new QLabel;
    m_pIconLabel->setFixedSize(6, 24);
    m_pIconLabel->setObjectName("TitleIconLabel");

    m_pTextLabel = new QLabel(strTitle);
    m_pTextLabel->setFixedHeight(36);
    m_pTextLabel->setObjectName("TitleTextLabel");

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pIconLabel, 0, Qt::AlignVCenter);
    pLayout->addSpacing(12);
    pLayout->addWidget(m_pTextLabel);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}

void CHLabelTitleWidget::ResetTitle(const QString &strTitle)
{
    m_pTextLabel->setText(strTitle);
}
