#ifndef CMAINTAINWIDGET_H
#define CMAINTAINWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-05-23
  * Description: 运维内容
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QCheckBox>
#include <QPushButton>
#include "CLineEdit.h"
#include "CTextBrowser.h"
#include "CmdBus/CCmdBase.h"
#include "SystemPage/CSysFirstTitleWidget.h"

class CMaintainWidget : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CMaintainWidget(QWidget *parent = nullptr);
    ~CMaintainWidget();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;

signals:
    void SignalReturn();

private slots:
    void _SlotReadCanIDBtn();
    void _SlotSetCanIDBtn();
    void _SlotDynamicPwdCheckBox(bool bClicked);
    void _SlotShowRawCurveCheckBox(bool bClicked);

private:
    void _ReadCfg();

private:
    void _InitWidget();
    void _InitLayout();

private:
    CSysFirstTitleWidget *m_pCSysTtileLabelWidget;
    QLabel *m_pBackgroundLabel;

    CLineEdit *m_pCanIDLineEdit;
    QPushButton *m_pReadCanIDBtn, *m_pSetCanIDBtn;
    QCheckBox *m_pDynamicPwdCheckBox;
    QCheckBox *m_pShowRawCurveCheckBox;

    CTextBrowser *m_pTextBrowser;
};

#endif // CMAINTAINWIDGET_H
