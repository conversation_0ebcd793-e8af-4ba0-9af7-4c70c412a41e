#include "CBusyProgressBar.h"
#include <QPainter>
#include <QBoxLayout>
#include "PublicParams.h"
#include "PublicFunction.h"

CBusyProgressBar::CBusyProgressBar(const QString &strTitle, const QString &strLabelText, QWidget *parent)
    : QWidget(parent)
    , m_strTitleText(strTitle)
    , m_strInfoText(strLabelText)
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setFixedSize(parent ? parent->size() : G_QRootSize);
    this->setAttribute(Qt::WA_TranslucentBackground);
    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroup(), 0, Qt::AlignCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
    LoadQSS(this,":/qss/qss/default.qss");
}

CBusyProgressBar::~CBusyProgressBar()
{

}

void CBusyProgressBar::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, gk_iBackTransparency));
    QWidget::paintEvent(pEvent);
}

void CBusyProgressBar::_SlotCancelBtn()
{
    emit this->SignalCancel(); 
}

void CBusyProgressBar::SetCancelButtonVisible(bool visible)
{
    m_pCancelBtn->setVisible(visible);
}

void CBusyProgressBar::ResetInfoText(const QString &strTitle)
{
    m_pInfoLabel->setText(strTitle);
}

QGroupBox *CBusyProgressBar::_CreateGroup()
{
    QGroupBox *pGroupBox = new QGroupBox(this);
    pGroupBox->setWindowOpacity(1);
    pGroupBox->setFixedSize(530, 330);
    pGroupBox->setObjectName("CMessageGroupBox");

    m_pTitleLabel = new QLabel(m_strTitleText, this);
    m_pTitleLabel->setFixedSize(pGroupBox->width(), 50);
    m_pTitleLabel->setObjectName("m_pDlgTitleLabel");

    m_pProgressBar = new QProgressBar(this);
    m_pProgressBar->setFixedSize(pGroupBox->width() - 100, 30);
    m_pProgressBar->setTextVisible(false);
    m_pProgressBar->setObjectName("BusyProgressBar");
    m_pProgressBar->setRange(0, 0);

    m_pInfoLabel = new QLabel(m_strInfoText);

    m_pCancelBtn = new QPushButton(tr("停止"));
    m_pCancelBtn->setFixedSize(100, 50);
    m_pCancelBtn->setObjectName("CancelBtn");
    m_pCancelBtn->setVisible(false);  // 添加默认不可见
    connect(m_pCancelBtn, &QPushButton::clicked, this, &CBusyProgressBar::_SlotCancelBtn);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addWidget(m_pTitleLabel, 0, Qt::AlignLeft);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pProgressBar, 0, Qt::AlignCenter);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pInfoLabel, 0, Qt::AlignCenter);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pCancelBtn, 0, Qt::AlignCenter);
    pLayout->addStretch(1);

    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}
