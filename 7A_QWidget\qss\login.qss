QGroupBox
{
    background-color: #FFF;
    border-radius: 32px;
}
QComboBox
{
    color: #000;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    border: 0px solid #CAD2DC;
    border-radius: 28px;
    background-color: #F4F7FE;
}
QComboBox::drop-down
{
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 60px;
    border-left: 0px solid red;
}
QComboBox::down-arrow
{
    width: 32px;
    height: 32px;
    image: url(:/image/ico/login/commod.png);
    padding: 0px 24px 0px 0px;
}
QComboBox QAbstractItemView
{
    color: #000;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    border: 0px solid darkgray;/*下拉列表的边线的粗细、实虚*/
    border-radius:0px;
    selection-background-color: #248CEB;/*下拉列表选中的行的背景色*/
}
QComboBox QAbstractItemView::item
{
    min-height: 56px;/*下拉列表的行高，也可以看做行距*/
    color: #000;
    font-size: 24px;
    font-weight: bold;
    font-family: "Source Han Sans CN";
}

QLineEdit
{
   color: #000;
   font-size: 24px;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   padding-left: 70px;
   border: 0px solid #cad2dc;
   background-color: #F4F7FE;
}
QLineEdit:focus{ padding-left: 70px; }

QPushButton:focus{outline: none;}
QPushButton#LoginBtn
{
   color: #FFF;   
   font-size: 26px;
   font-weight: bold;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #8490FF, stop: 1 #3D78E5);
}
QPushButton#LoginBtn:pressed
{
   color: #FFF;
   font-size: 26px;
   font-weight: bold;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #6670c7, stop: 1 #3361b8);
}


QPushButton#SeeBtn
{
   border-radius: 0px;
   background-color: transparent;
}

QLabel
{
   color: #000;
   font-size: 26px;
   font-family: "Source Han Sans CN";
}
QLabel#InfoLabel
{
   color: red;
}
QLabel#TitleLabel
{
   font-weight: bold;
   font-size: 48px;
   color: #3D78E5;
   border: 0px solid red;
}
QLabel#LogoLabel
{
   image: url(:/image/ico/login/wondfo.png);
}
QLabel#VerLabelSee
{
   color: #000;
   font-size: 26px;
   font-family: "Source Han Sans CN";
   border: 0px solid red;
}
QLabel#VerLabelNoSee
{
   color: transparent;
   font-size: 28px;
   font-family: "Source Han Sans CN";
   border: 0px solid red;
}
QScrollBar:vertical
{
    width: 30px;
    background: #F0F0F0;
    padding-top: 30px;
    padding-bottom: 30px;
}

QScrollBar::handle:vertical
{
    width: 30px;
    background: #B6B6B6;
    min-height: 35px;
}
