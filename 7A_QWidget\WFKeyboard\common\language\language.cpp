#include "language.h"

#include <QMap>
#include <QStringList>
#include <QDebug>
#include <QMetaEnum>

using namespace WfKeyboard;

LanguageFunction* LanguageFunction::m_Instance = NULL;

class LanguageFunction::PrivateData
{
public:
    PrivateData()
    {
    }

    LANGUAGE_TYPE GetLanguageType(QString tmpStr);

public:
    LANGUAGE_TYPE curLanguageType;

    QMap<LANGUAGE_TYPE, QString> languageNodesMap;
};


LANGUAGE_TYPE LanguageFunction::PrivateData::GetLanguageType(QString language)
{
    QMap<LANGUAGE_TYPE,QString>::Iterator it = languageNodesMap.begin();
    while(it != languageNodesMap.end())
    {
        if(language.contains(it.value()))
        {
            return it.key();
        }
        it++;
    }
    return chinese;
}

LanguageFunction::LanguageFunction()
    : md(new PrivateData())
{
    md->curLanguageType = chinese;

    md->languageNodesMap.insert(chinese,    "chinese");
    md->languageNodesMap.insert(english,    "english");
    md->languageNodesMap.insert(russia,     "russia");
    md->languageNodesMap.insert(greek,      "greek");
    md->languageNodesMap.insert(german,     "german");
    md->languageNodesMap.insert(arabic,     "arabic");
    md->languageNodesMap.insert(french,     "french");
    md->languageNodesMap.insert(italian,    "italian");
    md->languageNodesMap.insert(spanish,    "spanish");
    md->languageNodesMap.insert(polski,     "polski");
    md->languageNodesMap.insert(romania,    "romania");
    md->languageNodesMap.insert(croatia,    "croatia");
    md->languageNodesMap.insert(bulgaria,   "bulgaria");
    md->languageNodesMap.insert(portugal,   "portugal");
    md->languageNodesMap.insert(turkey,     "turkey");
    md->languageNodesMap.insert(finnish,    "finnish");
    md->languageNodesMap.insert(ukrainian,  "ukrainian");
    md->languageNodesMap.insert(kazakh,     "kazakh");
}

LanguageFunction* LanguageFunction::GetInstance()
{
    if(m_Instance == NULL)
    {
        m_Instance = new LanguageFunction();
    }
    return m_Instance;
}

QStringList  LanguageFunction::GetSupportLanguages()
{
    QStringList strList;
    QMap<LANGUAGE_TYPE, QString>::Iterator it = md->languageNodesMap.begin();
    while(it != md->languageNodesMap.end())
    {
        strList.append(it.value());
        it++;
    }
    return strList;
}

void LanguageFunction::SetCurrentLanguage(QString tmpStr)
{
    md->curLanguageType = md->GetLanguageType(tmpStr);
}

LANGUAGE_TYPE LanguageFunction::GetCurrentLanguageType()
{
    return md->curLanguageType;
}
