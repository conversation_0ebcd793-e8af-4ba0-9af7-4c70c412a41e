#ifndef CCMDBUS_H
#define CCMDBUS_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-10-30
  * Description: 指令注册
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QDebug>
#include <QMultiMap>
#include <QLabel>
#include <QPushButton>
#include <QBoxLayout>

#include "PublicConfig.h"
#include "PublicFunction.h"

class CCmdBase
{
public:
    CCmdBase();
    virtual ~CCmdBase();

    QString GetClassName();

    void Register2Map(int iMethodID);
    void UnRegister2Map(int iMethodID);

    static QString GetJsonCmdString(int iMethodID);
    static QString GetJsonCmdString(int iMethodID, const QVariant &qVarData);
    static QString GetJsonCmdString(int iMethodID, const QVariantList &qVarList);
    static QString GetJsonCmdString(int iMethodID, const QVariantMap &qVarMap);

    void SendJsonCmd(int iMachineID, int iMethodID, const QString &strCmd);

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) = 0;

protected:
    QString m_strClassName;
    QString m_strTipsText;

private:
    void _InitMethodMap();

private:    
    QMap<int, QStringList> m_iMethodIDNameTextMap; // 指令id, 指令name + 指令中文
};

#endif // CCMDBUS_H
