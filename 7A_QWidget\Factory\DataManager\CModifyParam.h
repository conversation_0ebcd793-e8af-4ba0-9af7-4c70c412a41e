/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: 陈皓
  * Date: 2024-01-22
  * Description: 修改参数类
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/
#pragma once
#include "PublicFunction.h"


#include <QWidget>
#include <QLabel>
#include <QComboBox>
#include <QLineEdit>
#include <QPushButton>


class CModifyParam : public QWidget
{
    Q_OBJECT
public:
    explicit CModifyParam(QWidget* parent = nullptr);
    void SetProjectName(const QString& strProjectName);

private:
    void initUI();
    void initData();

    
private slots:
    void _SlotOnConfirm();
    void _SlotCancelBtn();
    void _SlotHoleComboBoxChanged(int index);
private:
    QString m_strProjectName;
    
    QLabel *m_pBackgroundLabel{nullptr};
    QLabel* m_pTitleLabel;
    QComboBox* m_pFieldCombo;
    QLineEdit* m_pValueEdit;
    QPushButton* m_pConfirmBtn;
    QPushButton* m_pCancelBtn;
    SLotInfoStruct m_sLotInfo;
    QMap<QString, QString> m_fieldMap; // 字段显示名与实际列名的映射
};
