#include "CMedianUS.h"
#include <QDateTime>
#include "CMessageBox.h"

CMedianUS::CMedianUS(QWidget *parent) : QWidget(parent) , m_iUiMachineID(0)
{
    QTime t1 = QTime::currentTime();

    for(int iMethodID=Method_US_USST; iMethodID<=Method_US_USAMPLIST; iMethodID++)
        Register2Map(iMethodID);

    _InitWidget();
    _InitLayout();

    qDebug()<<"超声页面构造时间:"<<t1.msecsTo(QTime::currentTime());
}

CMedianUS::~CMedianUS()
{
    for(int iMethodID=Method_US_USST; iMethodID<=Method_US_USAMPLIST; iMethodID++)
        UnRegister2Map(iMethodID);
}

void CMedianUS::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    qDebug()<<Q_FUNC_INFO<<iMachineID<<iMethodID<<iResult;
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    QString strLog;
    if(Method_US_USST == iMethodID)
        strLog = "启动超声";
    else if(Method_US_USSP == iMethodID)
        strLog = "停止超声";
    else if(Method_US_USREBOOT == iMethodID)
        strLog = "重启超声";
    else if(Method_US_AMP == iMethodID)
        strLog = "设置振幅";
    else if(Method_US_USPSET == iMethodID)
        strLog = "设置PID参数";
    else if(Method_US_USFTY == iMethodID)
    {
        QVariantList qVarList = qVarData.toList();
        if(qVarList.isEmpty())
            strLog = "设置超声厂家";
        else
        {
            strLog = "读取超声厂家";
            m_pFtyComboBox->setCurrentIndex(qVarList.at(0).toInt());
        }
    }
    else if(Method_US_USPARAM == iMethodID)
    {
        QVariantList qVarList = qVarData.toList();
        if(qVarList.isEmpty())
            strLog = "设置模组参数";
        else
        {
            strLog = "读取模组参数";
            if(6 == qVarList.size())
            {
                for(int i=0; i<qVarList.size(); i++)
                    m_pParamLineEditList.at(i)->SetLineEditText(qVarList.at(i).toString());
            }
        }
    }
    else if(Method_US_GPWR == iMethodID)
    {
        strLog = "读取超声运行状态";
        QVariantList qVarList = qVarData.toList();
        if(5 == qVarList.size())
        {
            for(int i=0; i<qVarList.size(); i++)
                m_pPWRLineEditList.at(i)->SetLineEditText(qVarList.at(i).toString());
        }
    }
    else if(Method_US_USVERSION == iMethodID)
    {
        strLog = "读取超声版本";
        m_pVerLabel->setText(qVarData.toString());
    }
    else if(Method_US_USAMPLIST == iMethodID)
    {
        QVariantList qVarList = qVarData.toList();
        if(4 == qVarList.size())
        {
            for(int i=0; i<qVarList.size(); i++)
                m_pAmpListLineEditList.at(i)->SetLineEditText(qVarList.at(i).toString());
            strLog = "读取超声列表";
        }
        else
        {
            strLog = "设置超声列表";
        }
    }

    QString strResult = (0 == iResult) ? "成功" : "失败";
    _SaveLog(QString("%1#%2: %3").arg(iMachineID + 1).arg(strLog).arg(strResult));
}

void CMedianUS::_SlotMachineChanged(int iMachineID)
{
    m_iUiMachineID = iMachineID;
}

void CMedianUS::_SlotStartBtn()
{
    QString strCmd = GetJsonCmdString(Method_US_USST);
    _SaveLog(QString("%1#启动超声:%2").arg(m_iUiMachineID + 1).arg(strCmd));
    SendJsonCmd(m_iUiMachineID, Method_US_USST, strCmd);
}

void CMedianUS::_SlotStopBtn()
{
    QString strCmd = GetJsonCmdString(Method_US_USSP);
    _SaveLog(QString("%1#停止超声:%2").arg(m_iUiMachineID + 1).arg(strCmd));
    SendJsonCmd(m_iUiMachineID, Method_US_USSP, strCmd);
}

void CMedianUS::_SlotRebootBtn()
{
    QString strCmd = GetJsonCmdString(Method_US_USREBOOT);
    _SaveLog(QString("%1#重启超声:%2").arg(m_iUiMachineID + 1).arg(strCmd));
    SendJsonCmd(m_iUiMachineID, Method_US_USREBOOT, strCmd);
}

void CMedianUS::_SlotSetAmpBtn()
{
    double dAmp = m_pAmpLineEdit->GetLineEditText().toDouble();
    if(dAmp <= 0)
    {
        ShowInformation(this, m_strTipsText, tr("请输入正确的振幅数值"));
        return;
    }

    QVariantList qVarList = {dAmp};
    QString strCmd = GetJsonCmdString(Method_US_AMP, qVarList);
    _SaveLog(QString("%1#设置超声振幅:%2").arg(m_iUiMachineID + 1).arg(strCmd));
    SendJsonCmd(m_iUiMachineID, Method_US_AMP, strCmd);
}

void CMedianUS::_SlotSetPIDBtn()
{
    QVariantList qVarList;
    for(int i=0; i<m_pPIDTLineEditList.size(); i++)
    {
        QString strValue = m_pPIDTLineEditList.at(i)->GetLineEditText();
        if(strValue.isEmpty())
        {
            ShowInformation(this, tr("提示"), tr("请输入正确的PID参数"));
            return;
        }
        qVarList.push_back(strValue.toDouble());
    }

    QVariant dT = qVarList.last();
    qVarList.pop_back();
    qVarList.push_front(dT);

    int iMethodID = (0 == m_pPIDComboBox->currentIndex()) ? Method_US_USPSET : Method_US_USMSET;
    QString strCmd = GetJsonCmdString(iMethodID, qVarList);
    _SaveLog(QString("%1#设置超声PID参数:%2").arg(m_iUiMachineID + 1).arg(strCmd));
    SendJsonCmd(m_iUiMachineID, iMethodID, strCmd);
}

void CMedianUS::_SlotReadFtyBtn()
{
    QString strCmd = GetJsonCmdString(Method_US_USFTY);
    _SaveLog(QString("%1#读取超声厂家:%2").arg(m_iUiMachineID + 1).arg(strCmd));
    SendJsonCmd(m_iUiMachineID, Method_US_USFTY, strCmd);
}

void CMedianUS::_SlotSetFtyBtn()
{
    QVariantList qVarList = {m_pFtyComboBox->currentIndex()};
    QString strCmd = GetJsonCmdString(Method_US_USFTY, qVarList);
    _SaveLog(QString("%1#设置超声厂家:%2").arg(m_iUiMachineID + 1).arg(strCmd));
    SendJsonCmd(m_iUiMachineID, Method_US_USFTY, strCmd);
}

void CMedianUS::_SlotReadParamBtn()
{
    QString strCmd = GetJsonCmdString(Method_US_USPARAM);
    _SaveLog(QString("%1#读取超声模组参数:%2").arg(m_iUiMachineID + 1).arg(strCmd));
    SendJsonCmd(m_iUiMachineID, Method_US_USPARAM, strCmd);
}

void CMedianUS::_SlotSetParamBtn()
{
    QVariantList qVarList;
    for(int i=0; i<m_pParamLineEditList.size(); i++)
    {
        QString strValue = m_pParamLineEditList.at(i)->GetLineEditText();
        if(strValue.isEmpty())
        {
            ShowInformation(this, tr("提示"), tr("请输入正确的模组参数"));
            return;
        }
        qVarList.push_back(strValue.toDouble());
    }

    QString strCmd = GetJsonCmdString(Method_US_USPARAM, qVarList);
    _SaveLog(QString("%1#设置超声模组参数:%2").arg(m_iUiMachineID + 1).arg(strCmd));
    SendJsonCmd(m_iUiMachineID, Method_US_USPARAM, strCmd);
}

void CMedianUS::_SlotReadPWRBtn()
{
    QString strCmd = GetJsonCmdString(Method_US_GPWR);
    _SaveLog(QString("%1#读取超声运行状态:%2").arg(m_iUiMachineID + 1).arg(strCmd));
    SendJsonCmd(m_iUiMachineID, Method_US_GPWR, strCmd);
}

void CMedianUS::_SlotReadVerBtn()
{
    QString strCmd = GetJsonCmdString(Method_US_USVERSION);
    _SaveLog(QString("%1#读取超声版本:%2").arg(m_iUiMachineID + 1).arg(strCmd));
    SendJsonCmd(m_iUiMachineID, Method_US_USVERSION, strCmd);
}

void CMedianUS::_SlotReadAmpListBtn()
{
    QString strCmd = GetJsonCmdString(Method_US_USAMPLIST);
    SendJsonCmd(m_iUiMachineID, Method_US_USAMPLIST, strCmd);
}

void CMedianUS::_SlotSetAmpListBtn()
{
    QVariantList qVarList;
    for(int i=0; i<m_pAmpListLineEditList.size(); i++)
    {
        QString strValue = m_pAmpListLineEditList.at(i)->GetLineEditText();
        if(strValue.isEmpty())
        {
            ShowInformation(this, tr("提示"), tr("请输入正确的振幅参数"));
            return;
        }
        qVarList.push_back(strValue.toDouble());
    }
    QString strCmd = GetJsonCmdString(Method_US_USAMPLIST, qVarList);
    SendJsonCmd(m_iUiMachineID, Method_US_USAMPLIST, strCmd);
}

void CMedianUS::_SaveLog(const QString &strLog)
{
    qDebug()<<strLog;
    m_pTextBrowser->AppendLog(strLog);
}

void CMedianUS::_InitWidget()
{
    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(110, 50);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMachineChanged(int)));

    m_pStartBtn = new QPushButton(tr("启动超声"));
    m_pStartBtn->setFixedSize(110, 50);
    connect(m_pStartBtn, &QPushButton::clicked, this, &CMedianUS::_SlotStartBtn);

    m_pStopBtn = new QPushButton(tr("停止超声"));
    m_pStopBtn->setFixedSize(110, 50);
    connect(m_pStopBtn, &QPushButton::clicked, this, &CMedianUS::_SlotStopBtn);

    m_pRebootBtn = new QPushButton(tr("重启超声"));
    m_pRebootBtn->setFixedSize(110, 50);
    connect(m_pRebootBtn, &QPushButton::clicked, this, &CMedianUS::_SlotRebootBtn);

    m_pAmpLineEdit = new CLabelLineEdit(tr("振幅:"));
    m_pAmpLineEdit->SetLineEditFixedSize(100, 50);

    m_pSetAmpBtn = new QPushButton(tr("设置振幅"));
    m_pSetAmpBtn->setFixedSize(110, 50);
    connect(m_pSetAmpBtn, &QPushButton::clicked, this, &CMedianUS::_SlotSetAmpBtn);

    QStringList strPIDList = {"P:", "I:", "D:", "T:"};
    for(int i=0; i<strPIDList.size(); i++)
    {
        CLabelLineEdit *pLineEdit = new CLabelLineEdit(strPIDList.at(i));
        pLineEdit->SetLineEditFixedSize(70, 50);
        m_pPIDTLineEditList.push_back(pLineEdit);
    }

    QStringList strNameList = {tr("功率"), tr("电机")};
    m_pPIDComboBox = new QComboBox;
    m_pPIDComboBox->setView(new QListView);
    m_pPIDComboBox->addItems(strNameList);
    m_pPIDComboBox->setFixedSize(100, 50);

    m_pSetPIDBtn = new QPushButton(tr("设置PID"));
    m_pSetPIDBtn->setFixedSize(110, 50);
    connect(m_pSetPIDBtn, &QPushButton::clicked, this, &CMedianUS::_SlotSetPIDBtn);

    QStringList strFtyList = {tr("佳源达"), tr("东方金荣"), tr("自研超声")};
    m_pFtyComboBox = new QComboBox;
    m_pFtyComboBox->setView(new QListView);
    m_pFtyComboBox->addItems(strFtyList);
    m_pFtyComboBox->setFixedSize(110, 50);

    m_pReadFtyBtn = new QPushButton(tr("读取"));
    m_pReadFtyBtn->setFixedSize(110, 50);
    connect(m_pReadFtyBtn, &QPushButton::clicked, this, &CMedianUS::_SlotReadFtyBtn);

    m_pSetFtyBtn = new QPushButton(tr("设置"));
    m_pSetFtyBtn->setFixedSize(110, 50);
    connect(m_pSetFtyBtn, &QPushButton::clicked, this, &CMedianUS::_SlotSetFtyBtn);

    QStringList strParamList = {tr("最大\n频率"), tr("最小\n频率"), tr("最大\n振幅"),
                               tr("最小\n振幅"), tr("保护\n电流"), tr("校正\n时间")};
    for(int i=0; i<strParamList.size(); i++)
    {
        CLabelLineEdit *pLineEdit = new CLabelLineEdit(strParamList.at(i));
        pLineEdit->SetLineEditFixedSize(80, 50);
        m_pParamLineEditList.push_back(pLineEdit);
    }

    m_pReadParamBtn = new QPushButton(tr("读取"));
    m_pReadParamBtn->setFixedSize(110, 50);
    connect(m_pReadParamBtn, &QPushButton::clicked, this, &CMedianUS::_SlotReadParamBtn);

    m_pSetParamBtn = new QPushButton(tr("设置"));
    m_pSetParamBtn->setFixedSize(110, 50);
    connect(m_pSetParamBtn, &QPushButton::clicked, this, &CMedianUS::_SlotSetParamBtn);

    QStringList strPWRList = {tr("状态"), tr("频率"), tr("振幅"), tr("功率"), tr("电流")};
    for(int i=0; i<strPWRList.size(); i++)
    {
        CLabelLineEdit *pLineEdit = new CLabelLineEdit(strPWRList.at(i));
        pLineEdit->SetLineEditFixedSize(80, 50);
        m_pPWRLineEditList.push_back(pLineEdit);
    }

    m_pReadPWRBtn = new QPushButton(tr("读取"));
    m_pReadPWRBtn->setFixedSize(70, 50);
    connect(m_pReadPWRBtn, &QPushButton::clicked, this, &CMedianUS::_SlotReadPWRBtn);

    m_pVerLabel = new QLabel;
    m_pVerLabel->setObjectName("daylabel");
    m_pVerLabel->setFixedSize(80, 50);

    m_pReadVerBtn = new QPushButton(tr("读版本"));
    m_pReadVerBtn->setFixedSize(120, 50);
    connect(m_pReadVerBtn, &QPushButton::clicked, this, &CMedianUS::_SlotReadVerBtn);

    QStringList strAmpValueList = {tr("裂解振幅"), tr("洗涤1振幅"), tr("洗涤2振幅"), tr("洗脱振幅")};
    for(int i=0; i<strAmpValueList.size(); i++)
    {
        CLabelLineEdit *pLineEdit = new CLabelLineEdit(strAmpValueList.at(i));
        pLineEdit->SetLineEditFixedSize(80, 50);
        m_pAmpListLineEditList.push_back(pLineEdit);
    }
    m_pReadAmpListBtn = new QPushButton(tr("读取振幅列表"));
    m_pReadAmpListBtn->setFixedSize(150, 50);
    connect(m_pReadAmpListBtn, &QPushButton::clicked, this, &CMedianUS::_SlotReadAmpListBtn);
    m_pSetAmpListBtn = new QPushButton(tr("设置振幅列表"));
    m_pSetAmpListBtn->setFixedSize(150, 50);
    connect(m_pSetAmpListBtn, &QPushButton::clicked, this, &CMedianUS::_SlotSetAmpListBtn);

    m_pTextBrowser = new CTextBrowser;
}

void CMedianUS::_InitLayout()
{
    QHBoxLayout *pHLayout1 = new QHBoxLayout;
    pHLayout1->setContentsMargins(10, 0, 0, 0);
    pHLayout1->setSpacing(20);
    pHLayout1->addWidget(m_pMachineComboBox);
    pHLayout1->addSpacing(20);
    pHLayout1->addWidget(m_pStartBtn);
    pHLayout1->addWidget(m_pStopBtn);
    pHLayout1->addWidget(m_pRebootBtn);
    pHLayout1->addSpacing(20);
    pHLayout1->addWidget(m_pAmpLineEdit);
    pHLayout1->addWidget(m_pSetAmpBtn);
    pHLayout1->addStretch(1);

    QHBoxLayout *pHLayout2 = new QHBoxLayout;
    pHLayout2->setContentsMargins(10, 0, 0, 0);
    pHLayout2->setSpacing(10);
    for(int i=0; i<m_pPIDTLineEditList.size(); i++)
        pHLayout2->addWidget(m_pPIDTLineEditList.at(i));
    pHLayout2->addSpacing(10);
    pHLayout2->addWidget(m_pPIDComboBox);
    pHLayout2->addWidget(m_pSetPIDBtn);
    pHLayout2->addSpacing(20);
    pHLayout2->addWidget(m_pFtyComboBox);
    pHLayout2->addWidget(m_pReadFtyBtn);
    pHLayout2->addWidget(m_pSetFtyBtn);
    pHLayout2->addStretch(1);

    QHBoxLayout *pHLayout3 = new QHBoxLayout;
    pHLayout3->setContentsMargins(10, 0, 0, 0);
    pHLayout3->setSpacing(5);
    for(int i=0; i<m_pParamLineEditList.size(); i++)
        pHLayout3->addWidget(m_pParamLineEditList.at(i));
    pHLayout3->addSpacing(10);
    pHLayout3->addWidget(m_pReadParamBtn);
    pHLayout3->addWidget(m_pSetParamBtn);
    pHLayout3->addStretch(1);

    QHBoxLayout *pHLayout4 = new QHBoxLayout;
    pHLayout4->setContentsMargins(10, 0, 0, 0);
    pHLayout4->setSpacing(5);
    for(int i=0; i<m_pPWRLineEditList.size(); i++)
        pHLayout4->addWidget(m_pPWRLineEditList.at(i));
    pHLayout4->addSpacing(10);
    pHLayout4->addWidget(m_pReadPWRBtn);
    pHLayout4->addSpacing(20);
    pHLayout4->addWidget(m_pVerLabel);
    pHLayout4->addWidget(m_pReadVerBtn);
    pHLayout4->addStretch(1);

    QHBoxLayout *pHLayout5 = new QHBoxLayout;
    pHLayout5->setContentsMargins(10, 0, 0, 0);
    pHLayout5->setSpacing(5);
    for(int i=0; i<m_pAmpListLineEditList.size(); i++)
        pHLayout5->addWidget(m_pAmpListLineEditList.at(i));
    pHLayout5->addSpacing(10);
    pHLayout5->addWidget(m_pReadAmpListBtn);
    pHLayout5->addSpacing(20);
    pHLayout5->addWidget(m_pSetAmpListBtn);
    pHLayout5->addStretch(1);

    QVBoxLayout *pMainLayout = new QVBoxLayout;
    pMainLayout->setMargin(0);
    pMainLayout->setSpacing(10);
    pMainLayout->addLayout(pHLayout1);
    pMainLayout->addLayout(pHLayout2);
    pMainLayout->addLayout(pHLayout3);
    pMainLayout->addLayout(pHLayout4);
    pMainLayout->addLayout(pHLayout5);
    pMainLayout->addWidget(m_pTextBrowser);
    this->setLayout(pMainLayout);
}
