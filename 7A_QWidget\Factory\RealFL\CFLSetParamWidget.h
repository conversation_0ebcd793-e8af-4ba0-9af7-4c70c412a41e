#ifndef CFLSETPARAMWIDGET_H
#define CFLSETPARAMWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: chenhao
  * Date: 2024-12-19
  * Description: FL的测试参数设置
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QPushButton>
#include <qstackedwidget.h>

#include <QTableWidget>
#include "CHLabelTitleWidget.h"
#include "CLabelLineEdit.h"


class CFLSetParamWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CFLSetParamWidget(QWidget *parent = nullptr);
    void setParamText(const QList<float> & qFList);

protected:
    void paintEvent(QPaintEvent* pEvent) override;
    void showEvent(QShowEvent *pEvent) override;
signals:
    void SignalStandardSetParamConfirm(QList<float> qFList);
    void SignalFLCrossSetParamConfirm(QList<float> qFList);
private slots:
    void _SlotCancelBtn();
    void _SlotConfirmBtn();
    void _SlotResetBtn();
    void _SlotFlCrossBtn();
    void _SlotFlStadardBtn();

private:
    void _ClearData();

private:
    void _InitTabBtnWidget();
    void _InitTableWidget(QTableWidget*& tableWidget,const QStringList& RowHeaders,const  QStringList& ColumnHeaders);
    void _InitStandardWidget();
    void _InitWidget();
    void _InitLayout();

private:
    QLabel *m_pBackgroundLabel{nullptr};
    CHLabelTitleWidget *m_pCHLabelTitleWidget{nullptr};
    // 标题
    QPushButton* m_pFLCrossBtn{nullptr};
    QPushButton* m_pFLStandardBtn{nullptr};
    QLabel *m_pTopTabBackgroundLabel{nullptr};

    // 荧光串扰
    QTableWidget * m_FirstTableWidget{nullptr},* m_SecondTableWidget{nullptr};
    QStackedWidget* m_pStackWidget{nullptr};
    QPushButton *m_pResetBtn{nullptr};
    // 规范化
    CHLabelLineEdit *m_pHole1B{nullptr}, *m_pHole2B{nullptr};
    CHLabelLineEdit *m_pHole1G{nullptr}, *m_pHole2G{nullptr};
    CHLabelLineEdit *m_pHole1Y{nullptr}, *m_pHole2Y{nullptr};
    CHLabelLineEdit *m_pHole1R{nullptr}, *m_pHole2R{nullptr};

    QList<float> m_pfList;
    QList<float> m_pfStandardList;
    QPushButton *m_pCancelBtn{nullptr}, *m_pConfirmBtn{nullptr};
};

#endif // CFLSetParamWidget_H
