#ifndef CCALCULATETHREAD_H
#define CCALCULATETHREAD_H

#include<QObject>
#include<QThread>
#include "PublicParams.h"

#include "include/ccalctlib.h"
#include "include/cmeltingcalclib.h"
#include "include/cpniresultlib.h"

class CCalculateThread : public QObject
{
    Q_OBJECT
public:
    static CCalculateThread* GetInstance();

private:
    CCalculateThread();
    ~CCalculateThread();
 // 那些地方再用？
 // 1.实时数据计算；
 // 这个传入的数据是1runInfo与荧光数据即可；

// 返回结果,从数据库中取数据，界面显示就更新界面

 // 2.人工审核那里；
//只需要runInfo,荧光数据可以为空，后面去sql中取
// 返回结果,从数据库中取数据，界面显示就更新界面

public:
    void calculateHandle(const SRunningInfoStruct& sRunInfo,const QList<QMap<double, double>>& dFLDataMapList);
    void calculateMeltingHandle(const SRunningInfoStruct &sConstRunInfo, const QList<double>& dTempVec, const QList<QMap<double, double> > &dFLDataMapList);

private slots:
    void doCalclateWork(SRunningInfoStruct sRunInfo,QList<QMap<double, double>> dFLDataMapList);
    void doMeltingCalclateWork(SRunningInfoStruct sConstRunInfo, QList<double> dTempVec, QList<QMap<double, double> > dFLDataMapList);
signals:
    void SingalCalcResultReady(int iMachineID,int intiHistoryID,int iStatus);
    void SingalCalculate(SRunningInfoStruct sRunInfo, QList<QMap<double, double>> dFLDataMapList);
    void SingalMeltingCalculate(SRunningInfoStruct sRunInfo, QList<double> dTempVec, QList<QMap<double, double>> dFLDataMapList);

private:
    QString _GetAmpResult(const QString &strCt, const QString& strCtCutoffValue, const QString& strProjectName, const QString& strHoleName,const QString& strQCTestModel,const QString& strQCCtCutoffValue,bool b3PNMethod = true);
    QStringList DoubleList2StringList(const QList<double> &dDataList);
    QList<qreal> getYListFromQPontF(const QList<QPointF> &qSrc);
private:
    QThread* m_pThread{nullptr};
    CCalCTLib m_calcLib;
    CMeltingCalcLib  m_MeltingCalc;
    CPNICodeResult m_CPNICodeResult;
};

#endif // CCALCULATETHREAD_H
