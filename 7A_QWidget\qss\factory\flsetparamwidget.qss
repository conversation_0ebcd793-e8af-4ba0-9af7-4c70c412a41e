QLabel
{
   color: #6B788F;
   font-size: 24px;
   font-family: "Source Han Sans CN";
   border: 0px solid red;
}

QLabel#BackgroundLabel
{
   border-radius: 32px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
       stop: 0 #DEE8FB, stop: 0.1 #FFF);
}

QLabel#TitleIconLabel
{
   border-radius: 3px;
   background-color: #3D78E5;
}
QLabel#TitleTextLabel
{
   color: #353E4E;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
}

QPushButton
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #8490FF, stop: 1 #3D78E5);
}
QPushButton:pressed
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #6670c7, stop: 1 #3361b8);
}
QPushButton:focus{outline: none;}

QPushButton#TopBtn
{
   color: #353E4E;
   font-size: 20px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   background-color: #FFF;
}

QPushButton#CancelBtn
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 2px solid #3D78E5;
   background-color: #FFF;
}

QPushButton#CancelBtn:hover
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 3px solid #3D78E5;
   background-color: #FFF;
}

QPushButton#CancelBtn:pressed
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 4px solid #3D78E5;
   background-color: #FFF;
}

QLineEdit
{
   color: #000000;
   font-size: 20px;
   font-family: "Source Han Sans CN";
   padding-left: 20px;
   background-color: #F8F8FF;
}
QLineEdit:focus{ padding-left: 20px; }

QTableWidget
{
    color: #353E4E;
    font-size: 20px;
    font-family: "Source Han Sans CN";
    selection-background-color: #60C8FF;
    border: 1px solid #D6DFE9;
    border-radius: 0px;
    background-color: #FFFF00;
}
QTableWidget::item
{
    height: 56px; /* 表格单元格高度 */
    width: 100px; /* 表格单元格宽度 */
}
QHeaderView::section
{
    color: #353E4E;
    font-size: 20px;
    font-weight: 500;
    font-family: "Source Han Sans CN";
    border: 0px solid #D6DFE9;
    border-radius: 0px;
    height: 56px;
}

QHeaderView::horizontal::section
{
    background-color: #FFFF00;
    max-width: 480px; /* 水平表头最大宽度 */
    height: 56px; /* 水平表头高度 */
}
QHeaderView::vertical::section
{
    background-color: #FFFF00;
    min-width: 56px;  /* 竖直表头最小宽度 */
    max-width: 480px; /* 竖直表头最大宽度 */
    height: 56px; /* 竖直表头高度 */
}

QComboBox
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    padding-left: 20px;
    border-radius: 28px;
    background-color: #F3F8FF;
}

QComboBox::drop-down
{
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 60px;
    font-weight: 500;
    border-left: 0px solid red;
}
QComboBox::down-arrow
{
    width: 32px;
    height: 32px;
    image: url(:/image/ico/login/commod.png);
    padding: 0px 20px 0px 0px;
}
QComboBox QAbstractItemView
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    selection-background-color: #248CEB;
}
QComboBox QAbstractItemView::item
{
    min-height: 56px;/*下拉列表的行高，也可以看做行距*/
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
}

QComboBox:focus{outline: none;}
