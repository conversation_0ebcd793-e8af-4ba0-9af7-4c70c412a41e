#ifndef CLIGHTDEBUG_H
#define CLIGHTDEBUG_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-11-07
  * Description: 光学调试
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QStackedWidget>
#include <QPushButton>
#include <QApplication>
#include "CHBtnTitleWidget.h"
#include "CLabelLabel.h"
#include "CLabelComboBox.h"
#include "CLabelLineEdit.h"
#include "CLabelDate.h"
#include "CDateTime.h"
#include <QDir>

class CLightSingleCmd;
class CLightCurve;
class CLightCalibrate;
class CLightMDT;
class COpticalCalibration;
class CFluorescenceInterference;
class CPerformanceTest;
class CNoiseTest;

class CLightDebug : public QWidget
{
    Q_OBJECT
public:
    explicit CLightDebug(QWidget* parent = nullptr);
    ~CLightDebug();

signals:
    void SignalExportError();
    void SignalExportEnd();

private slots:
    void _SlotTitleChanged(int);
    void _SlotCreateFlTestBtn();
    void _SlotReturnCreateBtn();
    void _SlotFluorescenceTypeChanged(int iTypeIndex);
    void _SlotExportExcelBtn();
    void _SlotShowDateWidget();
    void _SlotConfirmDate(const QString& strDate);
    void _SlotExportError();
    void _SlotExportEnd();

private:
    void _InitWidget();
    void _Thread2ExportExcel(const QString& strBeginDate, const QString& strEndDate);
    QStringList _GetMatchDateFilesFromDir(const QDir& qDir, const QString& strBeginDate, const QString& strEndDate);

private:
    // 创建页面相关
    QWidget* m_pWidgetCreate;
    CLabelComboBox* m_pFluorescenceTypeComboBox;  // 荧光类型下拉框
    CLabelLineEdit* m_pSNLineEdit;
    QPushButton* m_pCreateFlTestBtn;

    // 功能页面相关
    QWidget* m_pWidgetFunction;
    CLabelLabel* m_pSNLabel;
    QPushButton* m_pReturnCreateBtn;
    QPushButton* m_pExportExcelBtn;
    CLabelDate* m_pBeginDate;
    CLabelDate* m_pEndDate;
    CDateTime* m_pDateWidget;
    CHBtnTitleWidget* m_pHBtnTitle;
    QStackedWidget* m_pStackedWidgetFunction;

    // 日期选择状态
    enum EnumDateType { eStartDate = 0, eEndDate };
    EnumDateType m_eDateType;

    // 主堆叠窗口
    QStackedWidget* m_pStackedWidget;

    // 原有页面
    CLightSingleCmd* m_pCLightSingleCmd;
    CLightCurve* m_pCLightCurve;
    CLightCalibrate* m_pCLightCalibrate;
    CLightMDT* m_pCLightMDT;

    // 新增页面
    COpticalCalibration* m_pOpticalCalibration;
    CFluorescenceInterference* m_pFluorescenceInterference;
    CPerformanceTest* m_pPerformanceTest;
    CNoiseTest* m_pNoiseTest;
};

#endif // CLIGHTDEBUG_H
