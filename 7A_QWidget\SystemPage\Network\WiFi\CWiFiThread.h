﻿#ifndef CWIFITHREAD_H
#define CWIFITHREAD_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-01-16
  * Description: WIFI thread
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QMap>
#include <QObject>
#include <QThread>

class CWiFiThread : public QObject
{
    Q_OBJECT
public:
    static CWiFiThread *GetInstance();
    ~CWiFiThread();

    void OpenWiFi();
    void CloseWiFi();
    void ConnectWiFi(QString strName, QString strPwd);
    void DisconnectWiFi();

signals:
    void SignalConnectEnd(bool bConnectOK, QString strName, QString strPwd);
    void SignalInit();
    void SignalOpenWiFi();
    void SignalCloseWiFi();
    void SignalConnectWiFi(QString strName, QString strPWD);
    void SignalDisconnectWiFi();
    void SignalScanMap(QMap<QString, QString> strScanMap);

private slots:
    void _SlotInit();
    void _SlotOpenWiFi();
    void _SlotCloseWiFi();
    void _SlotConnectWiFi(QString strName, QString strPwd);
    void _SlotDisconnectWiFi();

private:
    CWiFiThread();
    void _WriteNoPwdConf(QString strName);
    void _ParseScanData(QString strScanData);
    void _ClearIP();

private:
    static CWiFiThread *m_spInstance;

    QThread *m_pThread;
    bool m_bOpen;
    bool m_bScanning;    
    bool m_bConnect;
};

#endif // CWIFITHREAD_H
