#ifndef CMOTORINFODB_H
#define CMOTORINFODB_H

#include <QObject>
#include "CSqliteDBBase.h"

class CMotorInfoDB : public QObject, public CSqliteDBBase
{
    Q_OBJECT
public:
    static CMotorInfoDB *GetInstance();
    Q_INVOKABLE void initDataBase();

public:
    //转换旧数据库 index -> id
    Q_INVOKABLE void convertTimingChildDBIndex2ID();

    QMap<QString, QString> readAllCmdNameContent();
    QMap<QString, QStringList> readAllMotorComposeInfo();
    QList<QStringList> readAllMotorCompensate();
    QList<QStringList> readAllNormalData();

    // 电机子指令集合
    Q_INVOKABLE bool addMotorChildTiming(QString strName, QString strContent);
    Q_INVOKABLE bool deleteMotorChildTimingFromName(QString strName);
    Q_INVOKABLE QStringList getAllMotorChildTimingNames();
    Q_INVOKABLE QString getMotorChildContentFromName(QString strName);
    Q_INVOKABLE bool deleteAllMotorChildTiming();
    // 电机组合指令集合
    Q_INVOKABLE bool addMotorComposeTiming(QString strCmdID, QString strCmdName, QString strContent);
    Q_INVOKABLE bool addMotorComposeTiming(QString strCmdID, QString strCmdName, QString strContent, QString strCmdText);
    Q_INVOKABLE bool deleteMotorComposeTimingFromName(QString strName);
    Q_INVOKABLE QString findCmdNameFromMotorComposeCmdID(QString strCmdID);
    Q_INVOKABLE QString findCmdIDFromMotorComposeCmdName(QString strCmdName);
    Q_INVOKABLE QString findCmdTextFromMotorComposeCmdID(QString strCmdID);
    Q_INVOKABLE QString findContentFromMotorComposeCmdName(QString strCmdName);
    Q_INVOKABLE QStringList getAllMotorComposeTimingNames();
    Q_INVOKABLE QStringList getAllMotorComposeTimingIDNames();
    Q_INVOKABLE QMap<QString,QString> getAllMotorComposeCmdTextIDMap();
    Q_INVOKABLE bool deleteAllMotorComposeTiming();
    // 时序子指令集合
    Q_INVOKABLE bool addTimingChildTiming(QString strName, QString strContent);
    Q_INVOKABLE bool deleteTimingChildTimingFromName(QString strName);
    Q_INVOKABLE QStringList getAllTimingChildTimingNames();
    Q_INVOKABLE QString getTimingChildContentFromName(QString strName);
    Q_INVOKABLE bool deleteAllTimingChildTiming();
    Q_INVOKABLE QStringList getAllTimingComposeTimingNames();
                QList<QStringList> readAllTimingList();
    // 时序组合指令集合
    Q_INVOKABLE bool addTimingComposeTiming(QString strCmdID, QString strCmdName, QString strContent);
    Q_INVOKABLE bool deleteTimingComposeTimingFromName(QString strName);
    Q_INVOKABLE QString findTimingCmdNameFromMotorComposeCmdID(QString strCmdID);
    Q_INVOKABLE QString findTimingCmdIDFromMotorComposeCmdName(QString strCmdName);
    Q_INVOKABLE QString findTimingContentFromMotorComposeCmdName(QString strCmdName);
    Q_INVOKABLE bool deleteAllTimingComposeTiming();
    // 电机补偿
    Q_INVOKABLE bool addMotorCompensate(QStringList strDataList);
    Q_INVOKABLE QStringList getCompensateNameFromMotorIndex(QString strMotorIndex);
    QList<QStringList> getCompensateDataFromMotorIndex(QString strMotorIndex);
    bool deleteAllMotorCompenstateFromMotorIndex(QString strMotorIndex);
    Q_INVOKABLE QString getCompensateStringData();// 组合电机补偿,索引:名称:数值,索引:名称:数值;索引:名称:数值;
    // 电机耗材
    Q_INVOKABLE QStringList getAllCompensateType();
    Q_INVOKABLE QString getV1hFromType(QString strType);
    Q_INVOKABLE QString getContextFromType(QString strType);
    Q_INVOKABLE bool addCompensateType(QStringList strDataList);
    Q_INVOKABLE bool deleteFromType(QString strType);

    // 通用电机数据
    Q_INVOKABLE bool addMotorNormalData(QStringList strDataList);
    Q_INVOKABLE int getOneDataFromMotor(QString strMotorIndex, QString strType);
    Q_INVOKABLE QStringList getSixParamFromMotor(QString strMotorIndex, QString strParam);

    //指令描述
    QList<QStringList> readAllMethod();
    bool saveOneMethod(const QStringList &strOneMethod);
    bool saveAllMethod(const QList<QStringList> &strAllMethod);

private:
    CMotorInfoDB();
    ~CMotorInfoDB();

    void _updateMethodText();

    typedef struct _SDBAddDataStruct
    {
        _SDBAddDataStruct()
        {

        }
        QStringList strCheckNameList;
        QStringList strCheckValueList;
        QString strTableName;
        QMap<QString,QString> addMap;
    }SDBAddDataStruct;

    QString _getOneDataByParam(QString strSelect,QString strTableName,QString strParamName,QString strParamValue);
    QStringList _getColumnData(QString strSelect,QString strTableName,QString strOrder="");
    bool _deleteDataByParam(QString strTableName,QString strParamName,QString strParamValue);
    bool _addDBData(const SDBAddDataStruct& addStruct);

private:
    QString _GetNewContent(QString strOldContent);
    static CMotorInfoDB *m_spInstance;
};

#endif // CMOTORINFODB_H
