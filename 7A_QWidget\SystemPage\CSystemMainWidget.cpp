#include "CSystemMainWidget.h"
#include <QDebug>
#include <QVBoxLayout>
#include <QGridLayout>

#include "PublicConfig.h"
#include "PublicFunction.h"

CSystemMainWidget::CSystemMainWidget(QWidget *parent) : QWidget(parent), m_iLastUserLevel(eUser_Admin)
{
    _InitWidget();
    _InitLayout();

    LoadQSS(this, ":/qss/qss/system/main.qss");
}

void CSystemMainWidget::showEvent(QShowEvent *pEvent)
{
    int iUserLevel = CPublicConfig::GetInstance()->GetLoginLevel();
    m_pSysBtnList.at(eMaintain)->setVisible(iUserLevel >= eUser_Maintain);
    m_pSysBtnList.at(eFactory)->setVisible(iUserLevel >= eUser_Factory);

    if(m_iLastUserLevel != iUserLevel)
    {
        if(iUserLevel < eUser_Admin)
        {
            m_pGridLayout->removeWidget(m_pSysBtnList.at(eUpdate));
            m_pGridLayout->removeWidget(m_pSysBtnList.at(eInfo));
            m_pGridLayout->removeWidget(m_pSysBtnList.at(eSelfTest));
            m_pGridLayout->removeWidget(m_pSysBtnList.at(eCalibrate));
            m_pGridLayout->removeWidget(m_pSysBtnList.at(ePrint));

            m_pGridLayout->addWidget(m_pSysBtnList.at(eInfo), 1, 0);
            m_pGridLayout->addWidget(m_pSysBtnList.at(ePrint), 1, 1);
        }
        else
        {
            m_pGridLayout->removeWidget(m_pSysBtnList.at(eInfo));
            m_pGridLayout->removeWidget(m_pSysBtnList.at(ePrint));

            m_pGridLayout->addWidget(m_pSysBtnList.at(eUpdate), 1, 0);
            m_pGridLayout->addWidget(m_pSysBtnList.at(eInfo), 1, 1);
            m_pGridLayout->addWidget(m_pSysBtnList.at(eSelfTest), 1, 2);
            m_pGridLayout->addWidget(m_pSysBtnList.at(eCalibrate), 1, 3);
            m_pGridLayout->addWidget(m_pSysBtnList.at(ePrint), 2, 0);
        }

        m_pSysBtnList.at(eUpdate)->setVisible(iUserLevel >= eUser_Admin);
        m_pSysBtnList.at(eSelfTest)->setVisible(iUserLevel >= eUser_Admin);
        m_pSysBtnList.at(eCalibrate)->setVisible(iUserLevel >= eUser_Admin);
    }
    m_iLastUserLevel = iUserLevel;

    QWidget::showEvent(pEvent);
}

void CSystemMainWidget::_SlotSysBtn()
{
    CSysButton *pSysBtn = dynamic_cast<CSysButton *>(sender());
    if(nullptr == pSysBtn)
        return;

    int index = pSysBtn->property("index").toInt();
    emit SignalTitleIndex(index);
}

void CSystemMainWidget::_InitWidget()
{
    m_pCHLabelTitleWidget = new CHLabelTitleWidget(tr("系统设置"));

    QStringList strTextList = {tr("常规设置"), tr("网络设置"), tr("用户管理"), tr("日志管理"),
                               tr("软件升级"), tr("仪器信息"), tr("仪器自检"), tr("仪器校准"),
                               tr("打印设置"), tr("运维模式"), tr("工厂模式")};
    QStringList strObjList = {"GeneralBtn", "NetworkBtn", "UserBtn", "LogBtn",
                              "UpdateBtn", "DevInfoBtn", "SelfTestBtn", "CalibrateBtn",
                              "PrinterBtn", "FactoryBtn", "FactoryBtn"};

    for(int i=0; i<strTextList.size(); i++)
    {
        CSysButton *pBtn = new CSysButton(strTextList.at(i), this);
        pBtn->setProperty("index", i);
        pBtn->setObjectName(strObjList.at(i));
        connect(pBtn, &QPushButton::clicked, this, &CSystemMainWidget::_SlotSysBtn);

        m_pSysBtnList.push_back(pBtn);
    }

    m_pBackgroundLabel = new QLabel(this);
    m_pBackgroundLabel->setGeometry(0, 0, 1684, 960);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");
}

void CSystemMainWidget::_InitLayout()
{
    QGridLayout *pGridLayout = new QGridLayout;
    m_pGridLayout = pGridLayout;
    pGridLayout->setMargin(0);
    pGridLayout->setHorizontalSpacing(16);
    pGridLayout->setVerticalSpacing(23);
    pGridLayout->addWidget(m_pSysBtnList.at(eNormal), 0, 0);
    pGridLayout->addWidget(m_pSysBtnList.at(eNetwork), 0, 1);
    pGridLayout->addWidget(m_pSysBtnList.at(eUser), 0, 2);
    pGridLayout->addWidget(m_pSysBtnList.at(eLog), 0, 3);

    pGridLayout->addWidget(m_pSysBtnList.at(eUpdate), 1, 0);
    pGridLayout->addWidget(m_pSysBtnList.at(eInfo), 1, 1);
    pGridLayout->addWidget(m_pSysBtnList.at(eSelfTest), 1, 2);
    pGridLayout->addWidget(m_pSysBtnList.at(eCalibrate), 1, 3);

    if(eLanguage_Chinese == gk_iLanguage)
    {
        m_pSysBtnList.at(ePrint)->setVisible(false);
        pGridLayout->addWidget(m_pSysBtnList.at(eMaintain), 2, 0);
        pGridLayout->addWidget(m_pSysBtnList.at(eFactory), 2, 1);
    }
    else
    {
        pGridLayout->addWidget(m_pSysBtnList.at(ePrint), 2, 0);
        pGridLayout->addWidget(m_pSysBtnList.at(eMaintain), 2, 1);
        pGridLayout->addWidget(m_pSysBtnList.at(eFactory), 2, 2);
    }

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setContentsMargins(24, 15, 24, 0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pCHLabelTitleWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(20);
    pLayout->addLayout(pGridLayout);
    pLayout->addStretch(1);
    m_pBackgroundLabel->setLayout(pLayout);
}
