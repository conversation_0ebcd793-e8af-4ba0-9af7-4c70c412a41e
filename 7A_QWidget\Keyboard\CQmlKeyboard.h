#ifndef CQMLKEYBOARD_H
#define CQMLKEYBOARD_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-10-27
  * Description: qml虚拟键盘中间类
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QObject>
#include <QVariant>
#include <QQmlEngine>
#include <QLineEdit>

class CQmlKeybaord : public QObject
{
    Q_OBJECT
public:
    explicit CQmlKeybaord(QObject *parent = nullptr);
    ~CQmlKeybaord();

    static QObject* qmlSingletonInstance(QQmlEngine* engine, QJSEngine* scriptEngine)
    {
        Q_UNUSED(engine)
        Q_UNUSED(scriptEngine)
        return GetInstance();
    }
    static CQmlKeybaord* GetInstance();

    Q_INVOKABLE void hideKeyboard();
    Q_INVOKABLE int getCurrentLanguage();
    Q_INVOKABLE void showQuickWidget();
    Q_INVOKABLE void setVersion(QString strVersion);

    void SetKeyboardWidth(int iWidth);
    void SetKeyboardPosition(int x, int y, int iHeight, QLineEdit* pLineEdit = NULL);
    void SetKeyboardParentWidget(QWidget* pWidget);

signals:
    void SignalHideKeyboard();
    void SignalShowQucikWidget();
    void SignalSetKeyboardWidth(int iWidth);
    void SignalSetKeyboardPosition(int x, int y, int iHeight, QLineEdit* pLineEdit);
    void SignalSetKeyboardParentWidget(QWidget* pWidget);
    void SignalSetVersion(QString strVersion);

private:
    static CQmlKeybaord* m_spInstance;
};
#endif // CQMLKEYBOARD_H
