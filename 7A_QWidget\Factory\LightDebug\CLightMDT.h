#ifndef CLIGHTMDT_H
#define CLIGHTMDT_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-11-08
  * Description: 光学调试-MDT
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QPushButton>
#include <QStackedWidget>

#include "CCmdBase.h"
#include "CLabelComboBox.h"
#include "CLightOneMDT.h"

class CLightMDT : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CLightMDT(QWidget *parent = nullptr);
    ~CLightMDT();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

public slots:
    void SlotClearMDTData(int iMachineID);

private slots:
    void _SlotClearBtn();
    void _SlotSwitchBtn();
    void _SlotExportBtn();

private:
    void _InitWidget();

private:
    QPushButton *m_pClearBtn;
    QPushButton *m_pSwitchBtn;
    QPushButton *m_pExportBtn;
    CLabelComboBox *m_pMachineComboBox;
    QStackedWidget *m_pStackedWidget;
    QVector<CLightOneMDT*> m_pMDTList;

private:
    Q_DISABLE_COPY(CLightMDT)
};

#endif // CLIGHTMDT_H
