#include "CLogWidget.h"
#include <QBoxLayout>
#include "PublicFunction.h"

CLogWidget::CLogWidget(QWidget *parent) : QWidget(parent)
{
    _InitWidget();
    _InitLayout();

    LoadQSS(this, ":/qss/qss/system/log.qss");
}

void CLogWidget::_SlotTitleChanged(int iTitle)
{
    m_pStackedWidget->setCurrentIndex(iTitle);
}

void CLogWidget::_InitWidget()
{
    m_pCSysTtileLabelWidget = new CSysFirstTitleWidget(tr("系统设置"), tr("日志管理"));
    connect(m_pCSysTtileLabelWidget, &CSysFirstTitleWidget::SignalTitlePress, this, &CLogWidget::SignalReturn);

    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(1684, 904);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");

    m_pCSysSecondTitleWidget = new CSysSecondTitleWidget({tr("故障日志"), tr("操作日志"), tr("系统日志")});
    connect(m_pCSysSecondTitleWidget, &CSysSecondTitleWidget::SignalSecondTitle, this, &CLogWidget::_SlotTitleChanged);

    m_pCFaultLogWidget = new CFaultLogWidget;
    connect(m_pCFaultLogWidget, &CFaultLogWidget::SignalReturn, this, &CLogWidget::SignalReturn);

    m_pCOperationLogWidget = new COperationLogWidget;
    connect(m_pCOperationLogWidget, &COperationLogWidget::SignalReturn, this, &CLogWidget::SignalReturn);

    m_pCSystemLogWidget = new CSystemLogWidget;
    connect(m_pCSystemLogWidget, &CSystemLogWidget::SignalReturn, this, &CLogWidget::SignalReturn);

    m_pStackedWidget = new QStackedWidget;
    m_pStackedWidget->setFixedSize(1636, 800);
    m_pStackedWidget->addWidget(m_pCFaultLogWidget);
    m_pStackedWidget->addWidget(m_pCOperationLogWidget);
    m_pStackedWidget->addWidget(m_pCSystemLogWidget);
}

void CLogWidget::_InitLayout()
{
    QVBoxLayout *pBackLayout = new QVBoxLayout;
    pBackLayout->setContentsMargins(24, 15, 24, 24);
    pBackLayout->addWidget(m_pCSysSecondTitleWidget, 0, Qt::AlignLeft);
    pBackLayout->addStretch(1);
    pBackLayout->addWidget(m_pStackedWidget, 0, Qt::AlignHCenter);
    m_pBackgroundLabel->setLayout(pBackLayout);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pCSysTtileLabelWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pBackgroundLabel);
    this->setLayout(pLayout);
}
