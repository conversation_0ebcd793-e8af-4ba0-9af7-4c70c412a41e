#include "PublicFunction.h"
#include <QFile>
#include <QDir>
#include <QDebug>
#include <QMutex>
#include <QTimer>
#include <QProcess>
#include <QEventLoop>
#include <QJsonArray>
#include <QJsonObject>
#include <QJsonDocument>
#include <QTextCodec>
#include <QApplication>
#include <QNetworkInterface>
#include <QFileInfo>
#include <stdio.h>
#include <errno.h>

#include "PublicConfig.h"
#include "PublicParams.h"
#include "CMyLogThread.h"
#include "CMessageBox.h"
#include "CConfigJson.h"
#include <iostream>
#include "CMylog.h"

void SetWidgetBackColor(QWidget *pWidget, QColor qColor)
{
    if(nullptr == pWidget)
        return;

    QPalette  qPalette;
    qPalette.setBrush(pWidget->backgroundRole(), qColor);
    pWidget->setPalette(qPalette);
    pWidget->setAutoFillBackground(true);
}

void SetLabelBackImage(QLabel *pLabel, QString strImagePath)
{
    if(nullptr == pLabel || strImagePath.isEmpty())
        return;

    QPixmap qPixmap(strImagePath);
    pLabel->setPixmap(qPixmap.scaled(pLabel->width(), pLabel->height(), Qt::KeepAspectRatio, Qt::SmoothTransformation));
    pLabel->setAlignment(Qt::AlignCenter);
}

int GetLanguange()
{
    //语言配置移到system.json, config.json恢复出厂设置会被删除.
    int iLanguage = eLanguage_Chinese;
    QString strSystemPath = QApplication::applicationDirPath() + "/Resources/system.json";
    if(!QFile::exists(strSystemPath))
    {
        iLanguage = CConfigJson::GetInstance()->GetConfigValue("Language").toInt();
        CConfigJson::GetInstance()->SetSystemValue("Language", iLanguage);
    }
    else
    {
        iLanguage = CConfigJson::GetInstance()->GetSystemValue("Language").toInt();
    }

    if(eLanguage_Chinese != iLanguage && eLanguage_English != iLanguage
            && eLanguage_Spanish != iLanguage && eLanguage_German != iLanguage
                    && eLanguage_Italian != iLanguage)
    {
        iLanguage = eLanguage_Chinese;
        CConfigJson::GetInstance()->SetSystemValue("Language", iLanguage);
    }
    return iLanguage;
}

void OutputMessage(QtMsgType type, const QMessageLogContext &context, const QString &msg)
{
    Q_UNUSED(context)

    static QMutex mutex;
    mutex.lock();

    CMylog::LogStruct slogStruct;
    //SLogSaveStruct sLogSaveStruct;
    QString strCurrentTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz ");

    switch(type)
    {
    case QtInfoMsg:
        //sLogSaveStruct.logType = LOG_TYPE_INFO;
        //sLogSaveStruct.strLog = QString("INFO ") + strCurrentTime + msg;
        slogStruct.level = CMylog::eLog_Info;
        slogStruct.strLog = QString("INFO ") + strCurrentTime + msg;
        break;
    case QtDebugMsg:
        //sLogSaveStruct.logType = LOG_TYPE_DEBUG;
        //sLogSaveStruct.strLog = QString("DEBUG ") + strCurrentTime + msg;
        slogStruct.level = CMylog::eLog_Debug;
        slogStruct.strLog = QString("DEBUG ") + strCurrentTime + msg;
        break;
    case QtWarningMsg:
        //sLogSaveStruct.logType = LOG_TYPE_WARNING;
        //sLogSaveStruct.strLog = QString("WARNING ") + strCurrentTime + msg;
        slogStruct.level = CMylog::eLog_Warning;
        slogStruct.strLog = QString("WARNING ") + strCurrentTime + msg;
        break;
    case QtCriticalMsg:
        //sLogSaveStruct.logType  = LOG_TYPE_ERROR;
        //sLogSaveStruct.strLog = QString("ERROR ") + strCurrentTime + msg;
        slogStruct.level = CMylog::eLog_Error;
        slogStruct.strLog = QString("ERROR ") + strCurrentTime + msg;
        break;
    case QtFatalMsg:
        //sLogSaveStruct.logType = LOG_TYPE_FATAL;
        //sLogSaveStruct.strLog = QString("FATAL ") + strCurrentTime + msg;
        slogStruct.level = CMylog::eLog_Fatal;
        slogStruct.strLog = QString("FATAL ") + strCurrentTime + msg;
        break;
    default:
        break;
    }

    //CMyLogThread::GetInstance()->AddLog(sLogSaveStruct);
    CMylog::GetInstance().AddLog(slogStruct);

    mutex.unlock();
}

void Delay_MSec(uint iMSecTime)
{
    QEventLoop loop;
    QTimer::singleShot(iMSecTime, &loop, SLOT(quit()));
    loop.exec();
}

int System(const QString &strCmd)
{
    int iRet = -1;
#ifdef Q_OS_LINUX
    iRet = system(strCmd.toStdString().c_str());
#endif
    if(0 == iRet)
        qDebug()<<strCmd<<iRet;
    else
        qDebug()<<strCmd<<iRet<<QString(strerror(errno));

    return iRet;
}

void LoadQSS(QWidget *pWidget, const QString &strPath)
{
    if(nullptr == pWidget)
        return;

    QFile file(strPath);
    if(!file.open(QIODevice::ReadOnly))
    {
        qDebug()<<"打开样式表失败"<<strPath<<file.errorString();
        return;
    }
    QByteArray byteQss = file.readAll();
    file.close();
    pWidget->setStyleSheet(byteQss);
}

bool CreateDir(const QString &strDir)
{
    QDir dir(strDir);
    if(dir.exists(strDir))
        return true;

#ifdef Q_OS_LINUX
    QString cmd = "mkdir " + strDir;
    System(cmd);
    System("sync");
    Delay_MSec(1000);
    for(int i=0; i<10; i++)
    {
        if(dir.exists())
            break;

        qDebug()<<Q_FUNC_INFO<<cmd<<i;
        System(cmd);
        System("sync");
    }
    return dir.exists();
#endif

    bool bMK = dir.mkdir(strDir);
    if(bMK)
        return true;

    QString strCmd = QString("mkdir %1").arg(strDir);
    return System(strCmd);
}

bool CopyQFile(const QString &strSrcPath, const QString &strDestPath)
{
#ifdef Q_OS_LINUX
    QString cmd = QString("cp %1 %2").arg(strSrcPath).arg(strDestPath);
    System(cmd);
    System("sync");

    QFileInfo qSrcInfo(strSrcPath);
    QFileInfo qDestInfo(strDestPath);
    int iSrcSize = qSrcInfo.size();
    for(int i=0; i<10; i++)
    {
        //兼容拷当天日志
        if(iSrcSize <= qDestInfo.size())
            break;

        qDebug()<<Q_FUNC_INFO<<strSrcPath<<strDestPath<<i;

        System(cmd);
        System("sync");

        qSrcInfo.setFile(strSrcPath);
        qDestInfo.setFile(strDestPath);
    }

    bool ok = iSrcSize <= qDestInfo.size();
    qDebug()<<strSrcPath<<iSrcSize<<strDestPath<<qDestInfo.size()<<ok;
    return ok;
#endif

    QFile qSrcFile(strSrcPath);
    if(!qSrcFile.exists())
        return false;

    if(strSrcPath == strDestPath)
        return true;

    if(QFile::exists(strDestPath))
    {
        QFile::remove(strDestPath);
        System("sync");
    }
    bool bCopy = qSrcFile.copy(strDestPath);
    qSrcFile.flush();
    if(bCopy)
    {
        qDebug()<<"copy file:"<<strSrcPath<<strDestPath<<bCopy;
    }
    else
    {
        qDebug()<<"copy file err:"<<strSrcPath<<strDestPath<<bCopy<<qSrcFile.errorString();
    }
    System("sync");
    return bCopy;
}

bool CopyQFileDir(const QString &strSrcPath, const QDir &dir)
{
    QString strPath = dir.absolutePath();
    if(!strPath.endsWith("/"))
    {
        strPath += "/";
    }
#ifdef Q_OS_LINUX
    QFileInfo qSrcInfo(strSrcPath);
    strPath += qSrcInfo.fileName();
    return CopyQFile(strSrcPath, strPath);
#else
    if(!QFile::exists(strSrcPath) || !dir.exists())
        return false;

    QFileInfo info(strSrcPath);
    QString strDestPath = strPath + info.fileName();
    return CopyQFile(strSrcPath, strDestPath);
#endif
}

bool MoveQFile(const QString &strSrcPath, const QString &strDestPath)
{
#ifdef Q_OS_LINUX
    QString cmd = QString("mv %1 %2").arg(strSrcPath).arg(strDestPath);
    System(cmd);
    System("sync");
    return true;
#endif

    if(!QFile::exists(strSrcPath))
        return false;

    if(!CopyQFile(strSrcPath, strDestPath))
        return false;
    QFile::remove(strSrcPath);
    System("sync");
    return true;
}

bool MoveQFile(const QString &strSrcPath, const QDir &dir)
{
#ifdef Q_OS_LINUX
    QString cmd = QString("mv %1 %2").arg(strSrcPath).arg(dir.path());
    System(cmd);
    //System(cmd);
    System("sync");
    return true;
#endif

    if(!QFile::exists(strSrcPath) || !dir.exists())
        return false;

    QFileInfo info(strSrcPath);
    QString strDestPath = dir.absolutePath() + "/" + info.fileName();
    return MoveQFile(strSrcPath, strDestPath);
}

bool ReadFile(const QString &strFileName, QString& strFileContext, QIODevice::OpenModeFlag flag)
{
    QFile file(strFileName);
    if(!file.open(flag))
    {
        qDebug()<<"ERR:open "<<strFileName<<file.errorString();
        return false;
    }
    QByteArray byteJson = file.readAll();
    file.close();
    strFileContext = QString::fromLocal8Bit(byteJson);
    return true;
}

static bool _WriteFile(const QString &strFileName, const QByteArray &byteFileContext, QIODevice::OpenModeFlag flag)
{
    QFile file(strFileName);
    if(!file.open(flag))
    {
        qDebug()<<__FUNCTION__<<"ERR:open"<<strFileName<<file.errorString();
        return false;
    }

    file.write(byteFileContext);
    file.flush();
    file.close();
    System("sync");
    return true;
}

static bool _WriteFileList(const QString &strFileName, const QList<QByteArray> &byteDataList, QIODevice::OpenModeFlag flag)
{
    QFile file(strFileName);
    if(!file.open(flag))
    {
        qDebug()<<__FUNCTION__<<"ERR:open"<<strFileName<<file.errorString();
        return false;
    }

    for(int i=0; i<byteDataList.size(); i++)
        file.write(byteDataList.at(i));
    file.flush();
    file.close();
    System("sync");
    return true;
}

//写2次
bool WriteFile(const QString &strFileName, const QString &strFileContext, QIODevice::OpenModeFlag flag)
{
    _WriteFile(strFileName, strFileContext.toLocal8Bit(), flag);
    return _WriteFile(strFileName, strFileContext.toLocal8Bit(), flag);
}

bool WriteFile(const QString &strFileName, const QByteArray &byteFileContext, QIODevice::OpenModeFlag flag)
{
    _WriteFile(strFileName, byteFileContext, flag);
    return _WriteFile(strFileName, byteFileContext, flag);
}

bool WriteFile(const QString &strFileName, const QList<QByteArray> &byteDataList, QIODevice::OpenModeFlag flag)
{
    _WriteFileList(strFileName, byteDataList, flag);
    return _WriteFileList(strFileName, byteDataList, flag);
}

bool ReadJsonFile(const QString &strFilePath, QVariantMap &qVarMap)
{
    QString strContext;
    if(!ReadFile(strFilePath, strContext))
        return false;

    QJsonParseError err;
    QJsonDocument doc = QJsonDocument::fromJson(strContext.toLocal8Bit(),&err);
    if(QJsonParseError::NoError != err.error)
        return false;

    QJsonObject obj = doc.object();
    qVarMap = obj.toVariantMap();

    return true;
}

bool WriteJsonFile(const QString &strFilePath, const QVariantMap &qVarMap)
{
    if(strFilePath.isEmpty())
        return false;

    QJsonObject obj;
    for(auto it=qVarMap.constBegin(); it!=qVarMap.constEnd(); it++)
        obj.insert(it.key(), QJsonValue::fromVariant(it.value()));

    QJsonDocument doc(obj);
    QByteArray json = doc.toJson(QJsonDocument::Indented);
    WriteFile(strFilePath, json);
    return true;
}

bool GIsStringListHasEmptry(const QStringList &strList)
{
    for(int i=0; i<strList.size(); i++)
    {
        if(strList.at(i).isEmpty())
            return true;
    }
    return false;
}

void ResortTableWidget(QTableWidget *pTableWidget)
{
    if(nullptr == pTableWidget)
        return;
    int iCnt = pTableWidget->rowCount();
    for(int iRow=0; iRow<iCnt; iRow++)
    {
        QTableWidgetItem* pIdItem = new QTableWidgetItem;
        pIdItem->setText(QString::number(iRow + 1));
        pIdItem->setTextAlignment(Qt::AlignCenter);
        pTableWidget->setItem(iRow, COL_NUM_ID, pIdItem);
    }
}

QString GetUDiskDir()
{
    QString strDir = "/udisk/";
#ifndef __aarch64__
    strDir = "../";
#endif

    return strDir;
}

QString GetUDiskUpdateDir()
{
    return GetUDiskDir() + "7C_Update/";
}

bool UDiskExist(QWidget *pWidget)
{
    QDir dir(GetUDiskDir());
    if(!dir.exists())
    {
        ShowInformation(pWidget, QObject::tr("提示"), QObject::tr("请先插入U盘"));
        return false;
    }

    return true;
}

bool IsUDiskAndUpdateFileExist(const QString &strFilePath, QWidget *pWidget)
{
    QDir dir(GetUDiskDir());
    if(!dir.exists())
    {
        ShowInformation(pWidget, QObject::tr("提示"), QObject::tr("请先插入U盘"));
        return false;
    }

    if(!QFile::exists(strFilePath))
    {
        ShowInformation(pWidget, QObject::tr("提示"), QObject::tr("U盘中文件不存在"));
        return false;
    }

    return true;
}

bool UDiskExistAndCreateDir(const QString &strDir, QWidget *pWidget)
{
    QDir dir(GetUDiskDir());
    if(!dir.exists())
    {
        ShowInformation(pWidget, QObject::tr("提示"), QObject::tr("请先插入U盘"));
        return false;
    }

    if(!CreateDir(strDir))
    {
        ShowInformation(pWidget, QObject::tr("提示"), QObject::tr("创建U盘文件夹失败%1").arg(strDir));
        return false;
    }
    return true;
}

int CheckPasswordIsLegal(const QString &strPassword)
{
    if(strPassword.length() < 6)
        return -1;

    bool bHasDigit = false;
    bool bHasLower = false;
    bool bHasUpper = false;

    for(int i=0; i<strPassword.size(); i++)
    {
        QChar ch = strPassword.at(i);
        if(ch.isDigit())
            bHasDigit = true;
        if(ch.isLower())
            bHasLower = true;
        if(ch.isUpper())
            bHasUpper = true;
    }

    if(!bHasDigit)
        return -2;
    if(!bHasLower)
        return -3;
    if(!bHasUpper)
        return -4;

    return 0;
}

bool IsCtrlTarget(const QString &strTarget)
{
    //IC IC1 IC-1 SC SC1 SC-1
    static QStringList strCtrlNameList = {"IC", "SC", "RNaseP", "β-actin", "内控","外源性内控","内源性内控"};
    foreach (QString strName, strCtrlNameList)
    {
        if(strTarget.startsWith(strName, Qt::CaseInsensitive))
            return true;
    }
    return false;
}

QStringList DoubleVector2StringList(const QVector<double> &dDataVec)
{
    QStringList strList;
    for(int i=0; i<dDataVec.size(); i++)
        strList.push_back(QString::number(dDataVec.at(i)));
    return strList;
}

QStringList DoubleList2StringList(const QList<double> &dDataList)
{
    QStringList strList;
    for(int i=0; i<dDataList.size(); i++)
        strList.push_back(QString::number(dDataList.at(i)));
    return strList;
}

static unsigned short ccitt_table[256] = {
    0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50A5, 0x60C6, 0x70E7,
    0x8108, 0x9129, 0xA14A, 0xB16B, 0xC18C, 0xD1AD, 0xE1CE, 0xF1EF,
    0x1231, 0x0210, 0x3273, 0x2252, 0x52B5, 0x4294, 0x72F7, 0x62D6,
    0x9339, 0x8318, 0xB37B, 0xA35A, 0xD3BD, 0xC39C, 0xF3FF, 0xE3DE,
    0x2462, 0x3443, 0x0420, 0x1401, 0x64E6, 0x74C7, 0x44A4, 0x5485,
    0xA56A, 0xB54B, 0x8528, 0x9509, 0xE5EE, 0xF5CF, 0xC5AC, 0xD58D,
    0x3653, 0x2672, 0x1611, 0x0630, 0x76D7, 0x66F6, 0x5695, 0x46B4,
    0xB75B, 0xA77A, 0x9719, 0x8738, 0xF7DF, 0xE7FE, 0xD79D, 0xC7BC,
    0x48C4, 0x58E5, 0x6886, 0x78A7, 0x0840, 0x1861, 0x2802, 0x3823,
    0xC9CC, 0xD9ED, 0xE98E, 0xF9AF, 0x8948, 0x9969, 0xA90A, 0xB92B,
    0x5AF5, 0x4AD4, 0x7AB7, 0x6A96, 0x1A71, 0x0A50, 0x3A33, 0x2A12,
    0xDBFD, 0xCBDC, 0xFBBF, 0xEB9E, 0x9B79, 0x8B58, 0xBB3B, 0xAB1A,
    0x6CA6, 0x7C87, 0x4CE4, 0x5CC5, 0x2C22, 0x3C03, 0x0C60, 0x1C41,
    0xEDAE, 0xFD8F, 0xCDEC, 0xDDCD, 0xAD2A, 0xBD0B, 0x8D68, 0x9D49,
    0x7E97, 0x6EB6, 0x5ED5, 0x4EF4, 0x3E13, 0x2E32, 0x1E51, 0x0E70,
    0xFF9F, 0xEFBE, 0xDFDD, 0xCFFC, 0xBF1B, 0xAF3A, 0x9F59, 0x8F78,
    0x9188, 0x81A9, 0xB1CA, 0xA1EB, 0xD10C, 0xC12D, 0xF14E, 0xE16F,
    0x1080, 0x00A1, 0x30C2, 0x20E3, 0x5004, 0x4025, 0x7046, 0x6067,
    0x83B9, 0x9398, 0xA3FB, 0xB3DA, 0xC33D, 0xD31C, 0xE37F, 0xF35E,
    0x02B1, 0x1290, 0x22F3, 0x32D2, 0x4235, 0x5214, 0x6277, 0x7256,
    0xB5EA, 0xA5CB, 0x95A8, 0x8589, 0xF56E, 0xE54F, 0xD52C, 0xC50D,
    0x34E2, 0x24C3, 0x14A0, 0x0481, 0x7466, 0x6447, 0x5424, 0x4405,
    0xA7DB, 0xB7FA, 0x8799, 0x97B8, 0xE75F, 0xF77E, 0xC71D, 0xD73C,
    0x26D3, 0x36F2, 0x0691, 0x16B0, 0x6657, 0x7676, 0x4615, 0x5634,
    0xD94C, 0xC96D, 0xF90E, 0xE92F, 0x99C8, 0x89E9, 0xB98A, 0xA9AB,
    0x5844, 0x4865, 0x7806, 0x6827, 0x18C0, 0x08E1, 0x3882, 0x28A3,
    0xCB7D, 0xDB5C, 0xEB3F, 0xFB1E, 0x8BF9, 0x9BD8, 0xABBB, 0xBB9A,
    0x4A75, 0x5A54, 0x6A37, 0x7A16, 0x0AF1, 0x1AD0, 0x2AB3, 0x3A92,
    0xFD2E, 0xED0F, 0xDD6C, 0xCD4D, 0xBDAA, 0xAD8B, 0x9DE8, 0x8DC9,
    0x7C26, 0x6C07, 0x5C64, 0x4C45, 0x3CA2, 0x2C83, 0x1CE0, 0x0CC1,
    0xEF1F, 0xFF3E, 0xCF5D, 0xDF7C, 0xAF9B, 0xBFBA, 0x8FD9, 0x9FF8,
    0x6E17, 0x7E36, 0x4E55, 0x5E74, 0x2E93, 0x3EB2, 0x0ED1, 0x1EF0
};

/**
     * @brief getCRC16  获取CRC16
     * @param data  计算的数据
     * @param len  数据的长度
     * @param oldCRC16  上一个CRC16的值，用于循环计算大文件的CRC16。第一个数据的CRC16则传入0x0。
     * @return
     */
unsigned short GetCRC16(const char *pData, unsigned long ulLen, unsigned long ulOldCRC16)
{
    unsigned short crc16 = ulOldCRC16;

    while (ulLen-- > 0)
        crc16 = ccitt_table[(crc16 >> 8 ^ *pData++) & 0xff] ^ (crc16 << 8);
    return crc16;
}
quint16 GetSmallByte(quint16 qDate)
{
    char *pByte = (char*)&qDate;

    quint16 quSmallByte = *((quint8*)pByte);
    pByte++;
    quint16 quBigByte = *((quint8*)pByte);
    int iNumber = (quSmallByte << 8) | quBigByte;
    qDebug() << __func__ << qDate << QString::number(qDate).toLocal8Bit().toHex()
             << iNumber << QString::number(iNumber).toLocal8Bit().toHex();
    return iNumber;
}

std::vector<double> getXVectorFromQPontF(const QList<QPointF> &qSrc)
{
    std::vector<double> dSrcDataVector;
    for(int i = 0; i < qSrc.length(); ++i)
    {
        dSrcDataVector.push_back(qSrc.at(i).x());
    }
    return dSrcDataVector;
}

std::vector<double> getYVectorFromQPontF(const QList<QPointF> & qSrc)
{
    std::vector<double> dSrcDataVector;
    for(int i = 0; i < qSrc.length(); ++i)
    {
        dSrcDataVector.push_back(qSrc.at(i).y());
    }
    return dSrcDataVector;
}

void getXYVectorFromQPontF(const QList<QPointF> &qSrc, std::vector<double> &dXVector, std::vector<double> &dYVector)
{
    dXVector.clear();
    dYVector.clear();
    for(int i = 0; i < qSrc.length(); ++i)
    {
        dXVector.push_back(qSrc.at(i).x());
        dYVector.push_back(qSrc.at(i).y());
    }
}
QList<qreal> getXListFromQPontF(const QList<QPointF> &qSrc)
{
    QList<qreal> dSrcDataVector;
    for(int i = 0; i < qSrc.length(); ++i)
    {
        dSrcDataVector.push_back(qSrc.at(i).x());
    }
    return dSrcDataVector;
}

QList<qreal> getYListFromQPontF(const QList<QPointF> &qSrc)
{
    QList<qreal> dSrcDataVector;
    for(int i = 0; i < qSrc.length(); ++i)
    {
        dSrcDataVector.push_back(qSrc.at(i).y());
    }
    return dSrcDataVector;
}

void getXYListFromQPontF(const QList<QPointF> &qSrc, QList<qreal> &qXList, QList<qreal> &qYList)
{
    qXList.clear();
    qYList.clear();
    for(int i = 0; i < qSrc.length(); ++i)
    {
        qXList.push_back(qSrc.at(i).x());
        qYList.push_back(qSrc.at(i).y());
    }
}

void GetListMinMaxValue(const QList<qreal> &qDataList, double &dMin, double &dMax)
{
    if(qDataList.isEmpty())
        return;

    dMin = qDataList.at(0);
    dMax = qDataList.at(0);
    for(int i=0; i<qDataList.size(); i++)
    {
        dMin = qMin(dMin, qDataList.at(i));
        dMax = qMax(dMax, qDataList.at(i));
    }
}
void _GetCtInfoList(const QString& strCtInfo,QStringList& strCtList)
{
    QStringList strCtInfoList = strCtInfo.split(";");
    for(int i = 0 ; i < strCtInfoList.size(); i++)
    {
        QStringList strCtValueList = strCtInfoList.at(i).split(",");
        if(strCtValueList.size() >= 4 )
        {
            strCtList.push_back(strCtValueList.at(2));
        }
        else
        {
            strCtList.push_back(" ");
        }
    }
}


void _GetTmFormMeltInfo(const QString &strMeltInfo, QStringList &strTmList, QStringList &strRmList,QStringList &strYmList,QString &strThreashould)
{
    // info-review: Tm1-Rm1,Tm2-Rm2,yuzhi,fuduzhi;
    QStringList strInfoList = strMeltInfo.split(",");
    if(strInfoList.size() >= 4)
    {
        QString strTmRM1 = strInfoList.at(0);
        QString strTmRM2 = strInfoList.at(1);
        QStringList strTmRmList1 = strTmRM1.split("&");
        QStringList strTmRmList2 = strTmRM2.split("&");
        if(strTmRmList1.size() >=3 )
        {
            float value = strTmRmList1.at(0).toFloat();
            strTmList.push_back(QString::number(value, 'f', 2));
            value = strTmRmList1.at(1).toFloat();
            strRmList.push_back(QString::number(value, 'f', 2));
            value = strTmRmList1.at(2).toFloat();
            strYmList.push_back(QString::number(value, 'f', 2));
        }
        if(strTmRmList2.size() >= 3 )
        {
            float value = strTmRmList2.at(0).toFloat();
            strTmList.push_back(QString::number(value, 'f', 2));
            value = strTmRmList2.at(1).toFloat();
            strRmList.push_back(QString::number(value, 'f', 2));
            value = strTmRmList2.at(2).toFloat();
            strYmList.push_back(QString::number(value, 'f', 2));
        }
        float value = strInfoList.at(2).toFloat();
        strThreashould = QString::number(value, 'f', 2);
    }
}

void _GetTmRmList(const QString &strMeltInfo, QStringList &strTm_RmList)
{
    QStringList strMeltInfoList = strMeltInfo.split(";");
    for(int i = 0 ; i < strMeltInfoList.size(); i++)
    {
        QStringList strMeltValueList = strMeltInfoList.at(i).split(",");

        QStringList  strTmList,strRmList,strYmList;
        QString strThreashould;
        _GetTmFormMeltInfo(strMeltInfoList.at(i),strTmList,strRmList,strYmList,strThreashould);
        QString TmRm;
        for(int j = 0 ; j < strTmList.size(); j++)
        {
            TmRm += QString("Tm%1:%2 Rm%3:%4 Ym%5:%6").arg(j+1).arg(strTmList.at(j)).arg(j+1).arg(strRmList.at(j)).arg(j+1).arg(strYmList.at(j));
            TmRm += " ";
        }
        strTm_RmList.push_back(TmRm);
    }
}


QString GetMeltingResult(const QString& strProjectName,const QStringList& strHoleNameList,
                         const QStringList& strCtInfoList,const QStringList& strCtResultList, const QStringList& strMeltInfoList,const QStringList& strTmWildValue,const QStringList& strTmRangeValue)
{
    // 用到Ct值，Tm值，Ct结果，Tm参考区间
    // 考虑到多国语言翻译问题，不写到库中，在这加一个结果计算方法
    qDebug()<<"GetMeltingResult:"<<strProjectName<<"孔道名称:" <<strHoleNameList<<"CtInfo值:" <<strCtInfoList
           <<"MeltingInfo值:" <<strMeltInfoList<<"Tm参考值:" <<strTmWildValue<<"Tm间隔:" <<strTmRangeValue;
    // 计算总结果：

    if(strProjectName == "MTB&RIF")
    {
        QStringList stMTBResult= {QObject::tr("无效"),QObject::tr("结核分枝杆菌复合群阴性"),QObject::tr("结核分枝杆菌复合群阳性,利福平耐药"),QObject::tr("结核分枝杆菌复合群阳性,利福平敏感"),QObject::tr("结核分枝杆菌复合群阳性,利福平耐药性不明确")};

        QList<stHrmResult> sCtrlList;
        stHrmResult sFirstList;
        QList<stHrmResult> sSecondList;
        QList<stHrmResult> sThirdList;

        for(int i = 0; i < strHoleNameList.size(); i++)
        {
            // 如果等于0，直接return；
            if("0" == strHoleNameList.at(i))
            {
                continue;
            }
            stHrmResult sResult;
            // 先找内控
            sResult.nIndex = i;
            if(IsCtrlTarget(strHoleNameList.at(i)))
            {
                if(i < strCtInfoList.size() && i < strCtResultList.size())
                {
                    QString strCtInfo = strCtInfoList.at(i);
                    QStringList strCTInfoListTemp = strCtInfo.split(",");
                    if(strCTInfoListTemp.size() >= 4 && strCTInfoListTemp.at(2).toFloat() > 0
                            && (strCtResultList.at(i) == "P" || strCtResultList.at(i) == "S" ))
                    {
                        sResult.strCtValue = strCTInfoListTemp.at(2);
                        sResult.bPostive = true;
                    }

                }
                if( i < 4)
                {
                    sResult.bHole1Ctrl = true;
                }
                else if(i >= 4 )
                {
                    sResult.bHole2Ctrl = true;
                }
                sCtrlList.push_back(sResult);
                continue;
            }


            // 再找一级
            if(i == 1)
            {
                // 范围内报阳性
                sResult.nLevel = 0;
                //分析
                if(i < strCtInfoList.size() && i < strCtResultList.size())
                {
                    QString strCtInfo = strCtInfoList.at(i);
                    QStringList strCTInfoListTemp = strCtInfo.split(",");
                    bool isPostive  =  false;
                    //
                    if(strCtResultList.at(i) == "P" || (IsPositiveQcTest("PQC")&&strCtResultList.at(i) == "S" ) || (IsPositiveQcTest("NQC")&&strCtResultList.at(i) == "F" ))
                    {
                        isPostive = true;
                    }
                    if(strCTInfoListTemp.size() >= 4 && strCTInfoListTemp.at(2) != -1 && isPostive)
                    {
                        sResult.strCtValue = strCTInfoListTemp.at(2);
                        if(strCTInfoListTemp.at(2) > 0)
                        {
                            sResult.bPostive = true;
                        }
                    }
                }
                sFirstList = sResult;
                continue;
            }
            else if( i == 2 || i == 7)
            {
                sResult.nLevel = 1;
            }
            else if( i == 3 || i == 5)
            {
                sResult.nLevel = 2;
            }

            if(i < strMeltInfoList.size())
            {
                QString strMeltingInfo = strMeltInfoList.at(i);
                QString strThreashould;
                QStringList strTmInfoList,strRmInfoList,strYmInfoList;
                _GetTmFormMeltInfo(strMeltingInfo,strTmInfoList,strRmInfoList,strYmInfoList,strThreashould);


                if(i < strTmWildValue.size() && i < strTmRangeValue.size())
                {
                    float fMin = strTmWildValue.at(i).toFloat() - strTmRangeValue.at(i).toFloat();
                    float fMax = strTmWildValue.at(i).toFloat() + strTmRangeValue.at(i).toFloat();
                    for(int j = 0; j < strTmInfoList.size() ;j++)
                    {

                        bool bOk = false;
                        float TmValue = strTmInfoList.at(j).toFloat(&bOk);
                        // 这里的TmValue,需有且合法
                        // 如果是0 或者 90怎么样？

                        if(bOk && TmValue > 10)
                        {
                            sResult.bHaveTm = true;
                        }

                        if(sResult.bPostive)
                        {
                            continue;
                        }
                        if(fMin <= TmValue &&  TmValue <= fMax)
                        {
                            sResult.bPostive = false;
                        }
                        else
                        {                            
                            sResult.bPostive = true;
                        }
                    }
                }
            }

            if(sResult.nLevel == 1)
            {
                sSecondList.push_back(sResult);
            }
            if(sResult.nLevel == 1 || sResult.nLevel == 2)
            {
                sThirdList.push_back(sResult);
            }
        }

        int nResultIndex = 0;
        if(sFirstList.bPostive)
        {
            // 敏感 四个探针都在区间内(不存在突变)，且有峰值
            bool bNResistance = true;
            for(const auto& item : sThirdList)
            {
                if(item.bPostive || !item.bHaveTm)
                {
                    bNResistance = false;
                }
            }

            if(bNResistance)
            {
                // 敏感型
                nResultIndex = 3;
                return stMTBResult.at(nResultIndex);
            }
            // 耐药性
            // 1. 包含1个或者两个不在规定的Tm范围（存在突变），
            // 2. 其他都在Tm范围内（不存在突变）
            bool bPResistance = true;
            int nNumber = 0;
            for(const auto& item : sThirdList)
            {
                // 没有峰
                if(!item.bHaveTm)
                {
                    bPResistance = false;
                }
                if(item.bPostive)
                {
                    nNumber++;
                }
            }

            if(bPResistance &&
                    0 < nNumber &&
                    nNumber <= 2)
            {
                // 耐药型
                nResultIndex = 2;
                return stMTBResult.at(nResultIndex);
            }


            // 耐药性
            // 探针1 探针2 缺失 无峰值，但是其他都有峰（不存在突变），判定耐药
            for(const auto& item : sSecondList)
            {
                if(!item.bHaveTm)
                {
                    // 其他都有峰
                    bool bPR = true;
                    for(const auto& item2 : sThirdList)
                    {
                        if(item2.nIndex != item.nIndex)
                        {
                            if(!item2.bHaveTm || item2.bPostive)
                            {
                                bPR = false;
                            }
                        }
                    }
                    if(bPR)
                    {
                        // 耐药型
                        nResultIndex = 2;
                        return stMTBResult.at(nResultIndex);
                    }
                }
            }
            // 耐药性不明确
            nResultIndex = 4;
            return stMTBResult.at(nResultIndex);

        }
        else if(!sFirstList.bPostive)
        {
            // 利福平阴性
            for(const auto& item : sCtrlList)
            {
                if(item.bHole1Ctrl && item.bPostive)
                {
                    nResultIndex = 1;
                    return stMTBResult.at(nResultIndex);
                }
            }
            // 利福平实验无效
            nResultIndex = 0;
            return stMTBResult.at(nResultIndex);
        }
        else
        {
            // 利福平实验无效
            nResultIndex = 0;
            return stMTBResult.at(nResultIndex);
        }

    }
    return  QObject::tr("未导入此实验项目");
}


QString DeleteSpecialCharacters(QString strOldString)
{
    QString strNewString;
    for(int i=0; i<strOldString.size(); i++)
    {
        QChar ch = strOldString.at(i);
        if(ch.isDigit() || ch.isUpper() || ch.isLower() || '-' == ch || '#' == ch || '+' == ch)
            strNewString.push_back(ch);
    }
    return strNewString;
}

void ExportEndUmountUSB()
{    
    System("sync");
    Delay_MSec(1000);
    return;
    /*
    System("umount /media/usb0/");
    System("rm -rf /media/usb0/");
    System("sync");
    */
}

QString GetAppVersion()
{
#ifdef Q_OS_WIN
    return "V1.1.0.df8c";
#endif

    QString strVersion = QString(VERSION_FULL);
    if(!strVersion.startsWith("V", Qt::CaseInsensitive))
        strVersion.push_front("V");
    return strVersion;
}

QStringList GetTargetOutputInfoList(SResultInfoStruct sResultInfo, SLotInfoStruct sLotInfo)
{
    bool bReview = (sResultInfo.strReview == "y");
    bool bHrmTest = bHrmTecType(sResultInfo.iTestProject);

    QStringList strCTTmList;
    QStringList strTargetList = sLotInfo.strCurveName.split(";");

    QStringList  strDBResultList;
    QStringList strCtTmRmYmResultList;
    QString strPostive;
    QString strNegative;
    QString strCTTM;
    // 如果是Qc需要返回总结果
    // 按照熔解返回
    if(IsQCTest(sResultInfo.strMode))
    {
         QString strNameProject = sResultInfo.strProjectName;
         QStringList strQcInfoList;
         bool bQCResult = true;
         strQcInfoList.push_back(QString("%1: %2").arg(strNameProject).arg(GetTestResultText(sResultInfo.strMode,sResultInfo.strResult,sLotInfo.strCurveName,bQCResult)));
         return  strQcInfoList;
    }
    if(bHrmTest)
    {
        QString strNameProject = sResultInfo.strProjectName;
        QStringList strHrmInfoList;
        QString strResult = bReview ? sResultInfo.strHrmResult_Review : sResultInfo.strHrmResult;
        strHrmInfoList.push_back(QString("%1: %2").arg(strNameProject).arg(strResult));
        return strHrmInfoList;
        /*
        // 目前只搞 Tm Rm;
        //QString strMeltingInfo = bReview ? sResultInfo.strMeltingInfo_Review : sResultInfo.strMeltingInfo ;
        //_GetTmRmList(strMeltingInfo,strCTTmList);
        strPostive = QObject::tr("耐药型");
        strNegative = QObject::tr("敏感型");
        strCTTM = "TmRmYm";
        */
    }
    else
    {
        _GetCtResultList(bReview,strTargetList,sResultInfo.strCTInfo,sResultInfo.strCTInfo_Review, sResultInfo.strResult,sResultInfo.strResult_Review,strCTTmList,strDBResultList);
        if(IsQCTest(sResultInfo.strMode))
        {
            strPostive = QObject::tr("通过");
            strNegative = QObject::tr("不通过");
        }
        else
        {
            strPostive = QObject::tr("阳性");
            strNegative = QObject::tr("阴性");
        }
        strCTTM = "Ct";
    }

    QStringList strInfoList;
    int iMin = qMin(strDBResultList.size(), strTargetList.size());
    for(int index=0; index<iMin; index++)
    {
        QString strOneTarget = strTargetList.at(index);
        if("0" == strOneTarget || strOneTarget.isEmpty())
            continue;
        QString strOneCT;
        if(index < strCTTmList.size())
            strOneCT = strCTTmList.at(index);

        QString strOneResult;
        if(false == bHrmTest)
        {
            strOneResult = GetResultFormFlag(strDBResultList.at(index));
            if(!("P" == strDBResultList.at(index) || "S" == strDBResultList.at(index)))
            {
                strOneCT = "N/A";
            }
        }
        else
        {
            if("P" == strDBResultList.at(index))
                strOneResult = strPostive;
            else if("N" == strDBResultList.at(index))
                strOneResult = strNegative;
            else if("E" == strDBResultList.at(index))
                strOneResult = QObject::tr("无效");
            else
                strOneResult = "/";
        }

        strOneCT = strOneCT.replace(",", " ");
        strOneCT = strOneCT.trimmed();

        QString str;
        if(bHrmTest)
        {
            str = QString("%1: (%2:%3)").arg(strOneTarget).arg(strCTTM).arg(strOneCT);
        }
        else
        {
            str = QString("%1: %2 (%3:%4)").arg(strOneTarget).arg(strOneResult).arg(strCTTM).arg(strOneCT);
        }
        qDebug()<<"target output info:"<<str;
        strInfoList << str;
    }

    if(false == IsQCTest(sResultInfo.strMode))
    {
        if("GBS Test" == sResultInfo.strProjectName)
        {
            QString strName = QObject::tr("B族链球菌:");
            bool bCtrlPositive = false;
            bool bAllItemNegative = true;
            QStringList strRawInfoList = strInfoList;
            for(int i=0; i<strRawInfoList.size(); i++)
            {
                QString strOne = strRawInfoList.at(i);
                if("0" == strOne || strOne.isEmpty())
                    continue;

                if(IsCtrlTarget(strOne))
                {
                    bCtrlPositive = strOne.contains(strPostive);
                    continue;
                }

                if(strOne.contains(strPostive))
                {
                    strInfoList << strName + strPostive;
                    qDebug()<<__FUNCTION__<<__LINE__<<strInfoList;
                    return strInfoList;
                }

                if(false == strOne.contains(strNegative))
                {
                    bAllItemNegative = false;
                }
            }

            if(bCtrlPositive && bAllItemNegative)
            {
                strInfoList << strName + strNegative;
                qDebug()<<__FUNCTION__<<__LINE__<<strInfoList;
                return strInfoList;
            }

            strInfoList << strName + QObject::tr("测试异常");
            qDebug()<<__FUNCTION__<<__LINE__<<strInfoList;
            return strInfoList;
        }
    }

    return strInfoList;
}


void _GetCtResultList(bool bReview,const QStringList &strTargetList,const QString &strCtInfo, const QString &strCtReviewInfo,
                      const QString &strResultInfo, const QString &strResultReviewInfo,QStringList& strCtList,QStringList& strResultList)
{
    QStringList strCTReviewList;
    QStringList strCTListTemp;
    // 分出来只取Ct
    _GetCtInfoList( strCtInfo,strCTListTemp);
    _GetCtInfoList(strCtReviewInfo,strCTReviewList);

    // 结果继续
    QStringList strResultReviewList = strResultReviewInfo.split(";");
    QStringList strResultListTemp =strResultInfo.split(";");

    for(int i = 0; i < strTargetList.size(); i++)
    {
        QString strCtValue = "/";
        QString strResultValue = "/";
        if(bReview && i < strResultReviewList.size())
        {
            if(strResultReviewList.at(i) == "/")
            {
                if(i < strResultListTemp.size()&&i < strCTListTemp.size())
                {
                    strCtValue = strCTListTemp.at(i).toFloat() < 0 ? "0" : strCTListTemp.at(i);
                    strResultValue = strResultListTemp.at(i);
                }
            }
            else if(i < strCTReviewList.size() && i < strResultReviewList.size())
            {
                strCtValue = strCTReviewList.at(i).toFloat() < 0 ? "0" : strCTReviewList.at(i);
                strResultValue = strResultReviewList.at(i);
            }
        }
        else
        {
            if(i < strResultListTemp.size()&&i < strCTListTemp.size())
            {
                strCtValue = strCTListTemp.at(i).toFloat() < 0 ? "0" : strCTListTemp.at(i);
                strResultValue = strResultListTemp.at(i);
            }
        }
        strResultList.push_back(strResultValue);
        strCtList.push_back(strCtValue);
    }
}

QString RunQProcess(QString strCmd)
{
    QProcess process;
    process.start(strCmd);
    process.waitForFinished();
    QString strOuput = process.readAllStandardOutput();
    process.close();
    return strOuput;
}

bool GetHasNetwork(QString strDevName)
{
    QList<QNetworkInterface> interfaceList = QNetworkInterface::allInterfaces();

    bool bNetwork = false;
    foreach(QNetworkInterface interfaceItem, interfaceList)
    {
        if(interfaceItem.flags().testFlag(QNetworkInterface::IsRunning))
        {
            QList<QNetworkAddressEntry> addressEntryList=interfaceItem.addressEntries();
            foreach(QNetworkAddressEntry addressEntryItem, addressEntryList)
            {
                if(addressEntryItem.ip().protocol()==QAbstractSocket::IPv4Protocol)
                {
                    if(interfaceItem.name() == strDevName)
                    {
                        return true;
                    }
                }
            }
        }
    }
    return bNetwork;
}

QStringList GetIPInfoList(QString strDevName)
{
    QStringList strInfoList;
    QList<QNetworkInterface> interfaceList = QNetworkInterface::allInterfaces();
    foreach(QNetworkInterface interfaceItem, interfaceList)
    {
        if(interfaceItem.flags().testFlag(QNetworkInterface::IsUp) &&
                interfaceItem.flags().testFlag(QNetworkInterface::IsRunning))
        {
            QList<QNetworkAddressEntry> addressEntryList=interfaceItem.addressEntries();
            foreach(QNetworkAddressEntry addressEntryItem, addressEntryList)
            {
                if(QAbstractSocket::IPv4Protocol == addressEntryItem.ip().protocol())
                {
                    qDebug()<<"Adapter Name:"<<interfaceItem.name();
                    qDebug()<<"Adapter Address:"<<interfaceItem.hardwareAddress();
                    qDebug()<<"IP Address:"<<addressEntryItem.ip().toString();
                    qDebug()<<"IP Mask:"<<addressEntryItem.netmask().toString();
                    qDebug()<<"broadcast"<<addressEntryItem.broadcast().toString();

                    if(strDevName == interfaceItem.name())
                    {
                        strInfoList.clear();
                        strInfoList << addressEntryItem.ip().toString();
                        strInfoList << addressEntryItem.netmask().toString();
                        strInfoList << addressEntryItem.broadcast().toString();
                        strInfoList << interfaceItem.hardwareAddress();
                        break;
                    }
                }
            }
        }
    }

    return strInfoList;
}

QString RunCmdPipe(QString strCmd)
{
    QString strResult;
    char buff[4096] = {0};
    FILE *fp = popen(strCmd.toStdString().c_str(), "r");
    if(nullptr == fp)
    {
        qDebug()<<__FUNCTION__<<strCmd<<"cmd error!!!";
    }
    else
    {
        while(nullptr != fgets(buff, sizeof(buff), fp))
        {
            strResult += QString(buff);
        }
    }
    pclose(fp);
    return strResult;
}

QString GetGateway(QString strDevName)
{
    QString strGateway;
    int iRunTimes = 0;
    QTime t1 = QTime::currentTime();
    while (strGateway.isEmpty())
    {
        if(iRunTimes >= 20)
            break;
        iRunTimes++;

        QString strResult = RunQProcess("route -n");
        qDebug()<<"route -n:"<<strResult;
        QStringList strList = strResult.split('\n');
        for(int i=0; i<strList.size(); i++)
        {
            QStringList oneList = strList.at(i).split(' ');
            oneList.removeAll("");
            if(oneList.size() < 3)
                continue;

            if(oneList.contains("UG", Qt::CaseInsensitive))
            {
                if(oneList.contains(strDevName, Qt::CaseInsensitive))
                {
                    strGateway = oneList[1];
                    qDebug()<<strDevName<<"网关信息:"<<strGateway;
                    break;
                }
            }
        }

        Delay_MSec(500);
    }
    qDebug()<<strDevName<<"获取网关耗时:"<<t1.msecsTo(QTime::currentTime());
    return strGateway;
}

bool IsValidIPv4(const QString &strIP)
{
    QRegularExpression regex(R"(^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$)");
    return regex.match(strIP).hasMatch();
}

QString GetResultFormFlag(const QString &strFlag)
{

    static QStringList strResultList = {QObject::tr("阳性"), QObject::tr("阴性"), QObject::tr("在控"),QObject::tr("失控"),QObject::tr("无效")};

    if("P" == strFlag)
    {
        return  strResultList.at(0);
    }
    else if("N" == strFlag)
    {
        return  strResultList.at(1);
    }
    else if("S" == strFlag)
    {
        return  strResultList.at(2);
    }
    else if("F" == strFlag)
    {
        return  strResultList.at(3);
    }
    else if("I" == strFlag)
    {
        return  strResultList.at(3);
    }
    else if("E" == strFlag)
    {
        return  strResultList.at(4);
    }
    else
    {
        return "";
    }

}

bool IsQCTest(const QString &strModel)
{
    return IsPositiveQcTest(strModel) || IsNegativeQcTest(strModel);
}

bool IsPositiveQcTest(const QString &strQCTestModel)
{
    bool bPositiveQc = false;
    QStringList strList = strQCTestModel.split(";");
    if(strList.size() >= 1)
    {
        bPositiveQc = strList.at(0) == "PQC";
    }
    return bPositiveQc;
}

bool IsNegativeQcTest(const QString &strQCTestModel)
{
    bool bNegativeQc = false;
    QStringList strList = strQCTestModel.split(";");
    if(strList.size() >= 1)
    {
        bNegativeQc = strList.at(0) == "NQC";
    }
    return bNegativeQc;
}

QString GetTestResultText(const QString &strModel, const QString &strResult, const QString &strHoleName, bool& bQCResult)
{

    QString strResultTemp;
    QStringList strHoleNameList =  strHoleName.split(";");
    QStringList strResultList = strResult.split(";");
    int nSize = qMin(strHoleNameList.size(),strResultList.size());
    bQCResult = true;
    if(IsPositiveQcTest(strModel))
    {
        strResultTemp = QObject::tr("阳性质控通过");
        for(int i = 0 ; i < nSize;i++)
        {
            if(strResultList.at(i) != "S")
            {
                strResultTemp = QObject::tr("阳性质控不通过");
                bQCResult = false;
                break;
            }
        }
        if(nSize < 4 )
        {
            strResultTemp = QObject::tr("阳性质控不通过");
            bQCResult = false;
        }
    }
    else if(IsNegativeQcTest(strModel))
    {
        strResultTemp =  QObject::tr("阴性质控通过");
        for(int i = 0 ; i < nSize;i++)
        {
            if(strResultList.at(i) != "S")
            {
                strResultTemp = QObject::tr("阴性质控不通过");
                bQCResult = false;
                break;
            }
        }
        if(nSize < 4 )
        {
            strResultTemp = QObject::tr("阴性质控不通过");
            bQCResult = false;
        }
    }
    else
    {
        // 扩展
        // hrm
        // prc
        return " ";
    }
    return strResultTemp;
}

QString GetLoginLogString(ELOGINTYPE eLogType, QString strUserName, QString strMachineCode)
{
    static QMap<int, QString> strDefaultMap;
    if(strDefaultMap.isEmpty())
    {
        strDefaultMap[eLogUserNotExist] = QObject::tr("用户 \"%1\" 登录失败，用户名不存在");
        strDefaultMap[eLogNoPwd] = QObject::tr("用户 \"%1\" 登录失败，没有输入密码");
        strDefaultMap[eLogNoReEnterPwd] = QObject::tr("用户 \"%1\" 登录失败，没有重复输入密码");
        strDefaultMap[eLogPwdDiff] = QObject::tr("用户 \"%1\" 登录失败，两次密码不一致");
        strDefaultMap[eLogPwdInvalid] = QObject::tr("用户 \"%1\" 登录失败，密码长度必须大于等于6且包含数字和大小写字母");
        strDefaultMap[eLogPWDError] = QObject::tr("用户 \"%1\" 登录失败，密码错误");
        strDefaultMap[eLogMachineCode] = QObject::tr("用户 \"%1\" 机器代码：%2");
        strDefaultMap[eLogOK] = QObject::tr("用户 \"%1\" 登录成功");
        strDefaultMap[eLogout] = QObject::tr("用户 \"%1\" 退出");
        strDefaultMap[eLogDisabled] = QObject::tr("用户 \"%1\" 已被禁用");
    }

    QString strLog;
    QString strText = strDefaultMap.value(eLogType, "");
    if(eLogMachineCode == eLogType)
        strLog = strText.arg(strUserName).arg(strMachineCode);
    else
        strLog = strText.arg(strUserName);

    return strLog;
}


bool bShowNmzaCure(const QString &strResult, const QString &strTestMode,bool bControl)
{
    // 内控 通过了 可以show
    bool bShowNmzaCure = false;
    if(strResult == "P")
    {
        bShowNmzaCure = true;
    }
    else if(strResult == "S" && bControl)
    {
         bShowNmzaCure = true;
    }
    else if(strResult == "S" && IsPositiveQcTest(strTestMode))
    {
        bShowNmzaCure = true;
    }
    else if(strResult == "F" && IsNegativeQcTest(strTestMode) && !bControl)
    {
        bShowNmzaCure = true;
    }

    return  bShowNmzaCure;
}

int GetTecIndex(const QString &strTecName)
{
    int index = 0;
    if (strTecName.contains("hrm",Qt::CaseInsensitive)) {
        index = 1;
        if(strTecName.contains("hrmtd", Qt::CaseInsensitive))
        {
            index = 2;
        }
    }
    return index;
}

bool bPCRTecType(int indexTec)
{
    if(indexTec == TESTTEC_TYPE::eTecType_Pcr)
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool bHrmTecType(int indexTec)
{
    if(indexTec == TESTTEC_TYPE::eTecType_Hrm || indexTec == eTecType_HrmTD)
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool bHrmTDTecType(int indexTec)
{
    if(indexTec == TESTTEC_TYPE::eTecType_HrmTD)
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool bMeltingResultShowCtInfo(const QString &strProjectName, const QStringList &strHoleNameList,int nIndex)
{
    bool bResult = false;
    if(strProjectName == "MTB&RIF")
    {
        if(nIndex < strHoleNameList.size()
                && nIndex == 1)
        {
            bResult = true;
        }
    }
    return bResult;
}
