#ifndef CPRESSLABEL_H
#define CPRESSLABEL_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-27
  * Description: 触发鼠标左键事件的QLabel
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>

class CPressLabel : public QLabel
{
    Q_OBJECT
public:
    explicit CPressLabel(QWidget *parent = nullptr);
    CPressLabel(const QString &strText, QWidget *parent = nullptr);

signals:
    void SignalPressEvent();

protected:
    void mousePressEvent(QMouseEvent *pEvent) override;

};

#endif // CPRESSLABEL_H
