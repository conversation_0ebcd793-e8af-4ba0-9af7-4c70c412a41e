#include "CLightCurve.h"
#include <QListView>
#include <QBoxLayout>
#include "CLightOneCurve.h"

CLightCurve::CLightCurve(QWidget *parent) : QWidget(parent)
{
    Register2Map(Method_FLCDT);

    for(int i=0; i<gk_iMachineCount; i++)
    {
        SLightCurveStruct *pStruct = new SLightCurveStruct;
        m_sCurveUiList.push_back(pStruct);
    }

    _InitWidget();
}

CLightCurve::~CLightCurve()
{
    UnRegister2Map(Method_FLCDT);

    for(int i=0; i<m_sCurveUiList.size(); i++)
    {
        SLightCurveStruct *pStruct = m_sCurveUiList.at(i);
        delete pStruct;
        pStruct = nullptr;
    }
    m_sCurveUiList.clear();
}

void CLightCurve::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    Q_UNUSED(iResult);
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if(Method_FLCDT == iMethodID)
        m_sCurveUiList.at(iMachineID)->pCLightOneCurve->ReceiveData(qVarData);
}

void CLightCurve::_SlotMachineChanged(int iMachineID)
{
    SLightCurveStruct *pStruct = m_sCurveUiList.at(iMachineID);
    m_pReqLineEdit->SetLineEditText(pStruct->strReq);
    m_pLed1CheckBox->setChecked(pStruct->bLed1);
    m_pLed2CheckBox->setChecked(pStruct->bLed2);
    m_pPDComboBox->setCurrentIndex(pStruct->iPDIndex);
    m_pStackedWidget->setCurrentIndex(iMachineID);
}

void CLightCurve::_SlotReqChanged(const QString &strReq)
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_sCurveUiList[iMachineID]->strReq = strReq;
}

void CLightCurve::_SlotReqBtn()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    QVariantList qVarList = {m_pReqLineEdit->GetLineEditText().toDouble()};
    QString strCmd = GetJsonCmdString(Method_FLFREQ, qVarList);
    SendJsonCmd(iMachineID, Method_FLFREQ, strCmd);
}

void CLightCurve::_SlotLed1Checked(bool bChecked)
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_sCurveUiList[iMachineID]->bLed1 = bChecked;
}

void CLightCurve::_SlotLed2Checked(bool bChecked)
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_sCurveUiList[iMachineID]->bLed2 = bChecked;
}

void CLightCurve::_SlotLedBtn()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    int iLed = 0;
    if(!m_pLed1CheckBox->isChecked() && !m_pLed2CheckBox->isChecked())
        iLed = 0;
    else if(m_pLed1CheckBox->isChecked() && !m_pLed2CheckBox->isChecked())
        iLed = 1;
    else if(!m_pLed1CheckBox->isChecked() && m_pLed2CheckBox->isChecked())
        iLed = 2;
    else if(m_pLed1CheckBox->isChecked() && m_pLed2CheckBox->isChecked())
        iLed = 3;

    QVariantList qVarList = {iLed};
    QString strCmd = GetJsonCmdString(Method_FLLED, qVarList);
    SendJsonCmd(iMachineID, Method_FLLED, strCmd);
}

void CLightCurve::_SlotPDChanged(int index)
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_sCurveUiList[iMachineID]->iPDIndex = index;
}

void CLightCurve::_SlotCSTBtn()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_sCurveUiList[iMachineID]->pCLightOneCurve->ClearData();
    QVariantList qVarList = {m_pPDComboBox->currentIndex()};
    QString strCmd = GetJsonCmdString(Method_FLCST, qVarList);
    SendJsonCmd(iMachineID, Method_FLCST, strCmd);
}

void CLightCurve::_SlotCSPBtn()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    QVariantList qVarList = {m_pPDComboBox->currentIndex()};
    QString strCmd = GetJsonCmdString(Method_FLCSP, qVarList);
    SendJsonCmd(iMachineID, Method_FLCSP, strCmd);
}

void CLightCurve::_InitWidget()
{
    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(80, 50);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMachineChanged(int)));

    m_pReqLineEdit = new CLabelLineEdit(tr("频率:"), "");
    m_pReqLineEdit->SetLineEditInputMethod(Qt::ImhDigitsOnly);
    m_pReqLineEdit->SetLineEditFixedSize(80, 50);
    connect(m_pReqLineEdit, &CLabelLineEdit::SignalTextChanged,
            this, &CLightCurve::_SlotReqChanged);

    m_pReqBtn = new QPushButton(tr("执行"));
    m_pReqBtn->setFixedSize(100, 50);
    connect(m_pReqBtn, &QPushButton::clicked, this, &CLightCurve::_SlotReqBtn);

    m_pLed1CheckBox = new QCheckBox(tr("1号灯"));
    m_pLed1CheckBox->setFixedHeight(50);
    m_pLed1CheckBox->setLayoutDirection(Qt::RightToLeft);
    connect(m_pLed1CheckBox, &QCheckBox::clicked, this, &CLightCurve::_SlotLed1Checked);

    m_pLed2CheckBox = new QCheckBox(tr("2号灯"));
    m_pLed2CheckBox->setFixedHeight(50);
    m_pLed2CheckBox->setLayoutDirection(Qt::RightToLeft);
    connect(m_pLed2CheckBox, &QCheckBox::clicked, this, &CLightCurve::_SlotLed2Checked);

    m_pLedBtn = new QPushButton(tr("执行"));
    m_pLedBtn->setFixedSize(100, 50);
    connect(m_pLedBtn, &QPushButton::clicked, this, &CLightCurve::_SlotLedBtn);

    m_pPDComboBox = new QComboBox;
    m_pPDComboBox->setView(new QListView);
    m_pPDComboBox->addItems({"PD1", "PD2"});
    m_pPDComboBox->setFixedSize(80, 50);
    connect(m_pPDComboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotPDChanged(int)));

    m_pCSPBtn = new QPushButton(tr("停止采光"));
    m_pCSPBtn->setFixedSize(100, 50);
    connect(m_pCSPBtn, &QPushButton::clicked, this, &CLightCurve::_SlotCSPBtn);

    m_pCSTBtn = new QPushButton(tr("开始采光"));
    m_pCSTBtn->setFixedSize(100, 50);
    connect(m_pCSTBtn, &QPushButton::clicked, this, &CLightCurve::_SlotCSTBtn);

    m_pStackedWidget = new QStackedWidget;
    for(int i=0; i<gk_iMachineCount; i++)
    {
        CLightOneCurve *pCurve = new CLightOneCurve;
        pCurve->setMinimumSize(this->width(), this->height() - 130);
        m_sCurveUiList.at(i)->pCLightOneCurve = pCurve;
        m_pStackedWidget->addWidget(pCurve);
    }

    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(10);
    pTopLayout->addWidget(m_pMachineComboBox);
    pTopLayout->addStretch(1);
    pTopLayout->addWidget(m_pReqLineEdit);
    pTopLayout->addWidget(m_pReqBtn);
    pTopLayout->addSpacing(10);
    pTopLayout->addWidget(m_pLed1CheckBox);
    pTopLayout->addWidget(m_pLed2CheckBox);
    pTopLayout->addWidget(m_pLedBtn);
    pTopLayout->addSpacing(10);
    pTopLayout->addWidget(m_pPDComboBox);
    pTopLayout->addWidget(m_pCSTBtn);
    pTopLayout->addWidget(m_pCSPBtn);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(10);
    pLayout->addLayout(pTopLayout);
    pLayout->addWidget(m_pStackedWidget);

    this->setLayout(pLayout);
}
