#include "CSetSerialPort.h"
#include <QBoxLayout>
#include <QtSerialPort>

#include "PublicConfig.h"
#include "CSerialThread.h"
#include "CFirmLogSerialThread.h"

CSetSerialPort::CSetSerialPort(QWidget *parent) : QWidget(parent)
{
    _InitWidget();

    connect(CSerialThread::GetInstance(), &CSerialThread::SignalLog,
            this, &CSetSerialPort::_SlotSerialLog);
    connect(CFirmLogSerialThread::GetInstance(), &CFirmLogSerialThread::SignalLog,
            this, &CSetSerialPort::_SlotFirmSerialLog);
}

CSetSerialPort::~CSetSerialPort()
{

}

void CSetSerialPort::_SlotOpenUpBtn()
{
    if(tr("打开") == m_pOpenUpBtn->text())
    {
        QString strName = m_pUpSerialComboBox->GetCurrentText();
        QString strRate = m_pRateComboBox->currentText();
        CSerialThread::GetInstance()->ResetSerial(strName, strRate);
        return;
    }

    CSerialThread::GetInstance()->ResetSerial("", "");
}

void CSetSerialPort::_SlotOpenLowBtn()
{
    if(tr("打开") == m_pOpenLowBtn->text())
    {
        QString strName = m_pLowSerialComboBox->GetCurrentText();
        CFirmLogSerialThread::GetInstance()->ResetCom(strName);
        return;
    }

    CFirmLogSerialThread::GetInstance()->ResetCom("");
}

void CSetSerialPort::_SlotReflashBtn()
{
    QStringList strComList = _GetComNameList();
    m_pUpSerialComboBox->SetComboBoxList(strComList);
    m_pLowSerialComboBox->SetComboBoxList(strComList);
}

void CSetSerialPort::_SlotSerialLog(bool bOpen,const QString &strLog)
{
    if(bOpen)
        m_pOpenUpBtn->setText(tr("关闭"));
    else
        m_pOpenUpBtn->setText(tr("打开"));
    QString strCurrentTime = QDateTime::currentDateTime().toString("[yyyy-MM-dd hh:mm:ss]");
    m_pTextBrowser->append(strCurrentTime + ": " + strLog);
}

void CSetSerialPort::_SlotFirmSerialLog(bool bOpen, const QString &strLog)
{
    if(bOpen)
        m_pOpenLowBtn->setText(tr("关闭"));
    else
        m_pOpenLowBtn->setText(tr("打开"));
    QString strCurrentTime = QDateTime::currentDateTime().toString("[yyyy-MM-dd hh:mm:ss]");
    m_pTextBrowser->append(strCurrentTime + ": " + strLog);
}

void CSetSerialPort::_SlotAutoCheckBox(bool bCheck)
{
    int iSoftType = bCheck ? Soft_Auto : Soft_Extarct;
    CPublicConfig::GetInstance()->SetSoftTypeChanged(iSoftType);
    m_pExtractCheckBox->setChecked(!bCheck);
}

void CSetSerialPort::_SlotExtractCheckBox(bool bCheck)
{
    int iSoftType = !bCheck ? Soft_Auto : Soft_Extarct;
    CPublicConfig::GetInstance()->SetSoftTypeChanged(iSoftType);
    m_pAutoCheckBox->setChecked(!bCheck);
}

void CSetSerialPort::_InitWidget()
{
    QStringList strComList = _GetComNameList();

    m_pUpSerialComboBox = new CLabelComboBox(tr("上位机串口:"), strComList);
    m_pUpSerialComboBox->SetComboBoxFixedSize(100, 50);

    QStringList strRateList;
    strRateList<<"57600"<<"115200"<<"128000"<<"230400"<<"256000"<<"460800"
              <<"500000"<<"512000"<<"600000"<<"921600";
    m_pRateComboBox = new QComboBox;
    m_pRateComboBox->setFixedSize(100, 50);
    m_pRateComboBox->setView(new QListView);
    m_pRateComboBox->addItems(strRateList);
    m_pRateComboBox->setCurrentIndex(1);            //设置默认波特率为115200
    //m_pRateComboBox->setEditable(true);
    //m_pRateComboBox->setCompleter(nullptr); //取消自动补全

    m_pOpenUpBtn= new QPushButton(tr("打开"));
    m_pOpenUpBtn->setFixedSize(120, 50);
    connect(m_pOpenUpBtn, &QPushButton::clicked, this, &CSetSerialPort::_SlotOpenUpBtn);

    m_pLowSerialComboBox = new CLabelComboBox(tr("下位机串口:"), strComList);
    m_pLowSerialComboBox->SetComboBoxFixedSize(120, 50);

    m_pOpenLowBtn = new QPushButton(tr("打开"));
    m_pOpenLowBtn->setFixedSize(120, 50);
    connect(m_pOpenLowBtn, &QPushButton::clicked, this, &CSetSerialPort::_SlotOpenLowBtn);

    m_pReflashBtn = new QPushButton(tr("刷新"));
    m_pReflashBtn->setFixedSize(120, 50);
    connect(m_pReflashBtn, &QPushButton::clicked, this, &CSetSerialPort::_SlotReflashBtn);

    m_pAutoCheckBox = new QCheckBox(tr("全自动"));
    m_pAutoCheckBox->setFixedHeight(40);
    m_pAutoCheckBox->setLayoutDirection(Qt::RightToLeft);
    m_pAutoCheckBox->setChecked(true);
    connect(m_pAutoCheckBox, &QCheckBox::clicked, this, &CSetSerialPort::_SlotAutoCheckBox);

    m_pExtractCheckBox = new QCheckBox(tr("气密性工装"));
    m_pExtractCheckBox->setFixedHeight(40);
    m_pExtractCheckBox->setLayoutDirection(Qt::RightToLeft);
    m_pExtractCheckBox->setChecked(false);
    connect(m_pExtractCheckBox, &QCheckBox::clicked, this, &CSetSerialPort::_SlotExtractCheckBox);

    m_pTextBrowser = new CTextBrowser;

    QHBoxLayout *pSerialLayout = new QHBoxLayout;
    pSerialLayout->setMargin(0);
    pSerialLayout->setSpacing(10);
    pSerialLayout->addWidget(m_pUpSerialComboBox);
    pSerialLayout->addWidget(m_pRateComboBox);
    pSerialLayout->addWidget(m_pOpenUpBtn);
    pSerialLayout->addSpacing(20);
    pSerialLayout->addWidget(m_pLowSerialComboBox);
    pSerialLayout->addWidget(m_pOpenLowBtn);
    pSerialLayout->addSpacing(20);
    pSerialLayout->addWidget(m_pReflashBtn);
    pSerialLayout->addStretch(1);

    QHBoxLayout *pCheckBoxLayout = new QHBoxLayout;
    pCheckBoxLayout->setMargin(0);
    pCheckBoxLayout->addWidget(m_pAutoCheckBox);
    pCheckBoxLayout->addSpacing(100);
    pCheckBoxLayout->addWidget(m_pExtractCheckBox);
    pCheckBoxLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(20);
    pLayout->addSpacing(10);
    pLayout->addLayout(pSerialLayout);
    pLayout->addLayout(pCheckBoxLayout);
    pLayout->addWidget(m_pTextBrowser);
    this->setLayout(pLayout);
}

QStringList CSetSerialPort::_GetComNameList()
{
    QStringList strList;
    foreach (const QSerialPortInfo &info, QSerialPortInfo::availablePorts())
        strList.push_back(info.portName());
    return strList;
}


