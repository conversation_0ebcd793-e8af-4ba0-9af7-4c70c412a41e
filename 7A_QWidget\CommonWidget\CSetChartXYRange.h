#ifndef CSETCHARTXYRANGE_H
#define CSETCHARTXYRANGE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-11-07
  * Description:
  * -------------------------------------------------------------------------
  * History: x0 x1 y0 y1 btn 设置坐标组合控件
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QPushButton>
#include "CLabelLineEdit.h"

class CSetChartXYRange : public QWidget
{
    Q_OBJECT
public:
    explicit CSetChartXYRange(QWidget *parent = nullptr);
    explicit CSetChartXYRange(const QStringList &strRangeList, QWidget *parent= nullptr);
    ~CSetChartXYRange();

    void SetRange(const QStringList &strRangeList);
    void SetLineEditTextAlignment(Qt::Alignment qAli = Qt::AlignCenter);
    void ChangedObjName(const QString &strLineEditName, const QString &strBtnName);
    void SetObjFixedHeight(int iHeight);

signals:
    void SignalSetRange(const QStringList &);

private slots:
    void _SlotBtnClicked();

private:
    void _InitWidget();

private:
    CLabelLineEdit *m_pX0;
    CLabelLineEdit *m_pX1;
    CLabelLineEdit *m_pY0;
    CLabelLineEdit *m_pY1;
    QPushButton    *m_pSetButton;

    QStringList m_strRangeList;
};

#endif // CSETCHARTXYRANGE_H
