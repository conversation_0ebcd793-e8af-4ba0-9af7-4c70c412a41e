#include "CTecDB.h"
#include <QDebug>
#include "PublicConfig.h"
#include "PublicParams.h"

CTecDB &CTecDB::instance()
{
    static CTecDB tecDB;
    return tecDB;
}

QStringList CTecDB::ReadAllTecName()
{
    QString strCmd = "select name from tec_table";
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return _GetColumnValueList(strList);
}

QString CTecDB::ReadTecDataByName(const QString &tecName)
{
    if(tecName.isEmpty())
        return QString("");

    QString strCmd = QString("select data from tec_table where name = '%1'").arg(tecName);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return _GetFirstValue(strList);
}

bool CTecDB::InsertOneTec(const QString &tecName, const QString &tecData)
{
    if(tecName.isEmpty())
        return false;

    QString strCmd = QString("select * from tec_table where name = '%1'").arg(tecName);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    if(strList.isEmpty())
    {
        strCmd = QString("insert into tec_table (name,data,Remarks) values('%1','%2','')")
                .arg(tecName).arg(tecData);
        return _ExecuteDB(strCmd);
    }
    else
    {
        strCmd = QString("update tec_table set data = '%1' where name = '%2'")
                .arg(tecData).arg(tecName);
        return _ExecuteDB(strCmd);
    }
}

bool CTecDB::DeleteOneTecByName(const QString &tecName)
{
    QString strCmd = QString("delete from tec_table where name = '%1'").arg(tecName);
    return _ExecuteDB(strCmd);
}

QList<QStringList> CTecDB::RealAllTecList()
{
    QString strCmd = "select * from tec_table";
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return strList;
}


CTecDB::CTecDB()
    : CSqliteDBBase(CPublicConfig::GetInstance()->GetTecDBPath(), gk_strTecDBConnect)
{
    _InitTecTable();
}

CTecDB::~CTecDB()
{

}

void CTecDB::_InitTecTable()
{
    QString strCreateTable = "create table if not exists tec_table ("
                             "id INTEGER PRIMARY KEY AUTOINCREMENT,"
                             "name VARCHAR,"
                             "data VARCHAR,"
                             "remarks VARCHAR)";
    _ExecuteDB(strCreateTable);
}
