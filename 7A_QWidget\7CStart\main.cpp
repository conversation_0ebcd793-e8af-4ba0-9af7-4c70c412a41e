﻿#include "CStartWidget.h"

#include <QApplication>
#include <QFile>
#include <QLabel>
#include <QMovie>
#include <QPixmap>
#include <QJsonObject>
#include <QJsonDocument>

//语言
enum EnumLanguage
{
    eLanguage_Chinese = 0,
    eLanguage_English = 1,
    eLanguage_Spanish = 2,
    eLanguage_German  = 3,
    eLanguage_Italian = 4,
    eLanguage_Thai    = 5

};

int GetLanguage()
{
    QString strCfgPath = QApplication::applicationDirPath() + "/Resources/system.json";
    QFile file(strCfgPath);
    if(!file.open(QIODevice::ReadOnly))
        return eLanguage_English;
    QByteArray byteJson = file.readAll();
    file.close();

    QJsonParseError err;
    QJsonDocument doc = QJsonDocument::fromJson(byteJson, &err);
    if(QJsonParseError::NoError != err.error)
        return eLanguage_English;

    QJsonObject rootObj = doc.object();
    if(rootObj.contains("Language"))
        return rootObj.value("Language").toInt();
    return eLanguage_English;
}

void SetLabelBackImage(QLabel *pLabel, QString strImagePath)
{
    if(nullptr == pLabel || strImagePath.isEmpty())
        return;

    QPixmap qPixmap(strImagePath);
    pLabel->setPixmap(qPixmap.scaled(pLabel->width(), pLabel->height(), Qt::KeepAspectRatio, Qt::SmoothTransformation));
    pLabel->setAlignment(Qt::AlignCenter);
}

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    QLabel *pLabel = new QLabel;
    pLabel->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    pLabel->setFixedSize(1920, 1080);
    pLabel->move(0, 0);

    QString strPath = ":/ico/english.png";
    int iLang = GetLanguage();
    if(eLanguage_Chinese == iLang)
        strPath = ":/ico/chinese.png";
    else if(eLanguage_English == iLang)
        strPath = ":/ico/english.png";
    else if(eLanguage_German == iLang)
        strPath = ":/ico/german.png";
    else if(eLanguage_Spanish == iLang)
        strPath = ":/ico/spanish.png";
    else if(eLanguage_Italian == iLang)
        strPath = ":/ico/italian.png";

    QPixmap qPixmap(strPath);
    pLabel->setPixmap(qPixmap.scaled(pLabel->width(), pLabel->height(), Qt::KeepAspectRatio, Qt::SmoothTransformation));

    pLabel->show();

    return a.exec();
}
