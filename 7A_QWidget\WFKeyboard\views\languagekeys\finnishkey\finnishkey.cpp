#include "finnishkey.h"

#include "common/keyboardtoolbutton/keyboardtoolbutton.h"

FinnishKey::<PERSON><PERSON><PERSON>(const QString& name) :
    LanguageBase<PERSON>ey(name)
{
    InitButtons();
}

void FinnishKey::InitButtons()
{
    QMap<int, QList<KeyBoardToolButton*> > map;
    QList<KeyBoardToolButton*> firstButtons, secondButtons, thirdButtons;

    firstButtons.append(new KeyBoardToolButton("q","Q","",""));
    firstButtons.append(new KeyBoardToolButton("w","W","",""));
    firstButtons.append(new KeyBoardToolButton("e","E","",""));
    firstButtons.append(new KeyBoardToolButton("r","R","",""));
    firstButtons.append(new KeyBoardToolButton("t","T","",""));
    firstButtons.append(new KeyBoardToolButton("y","Y","",""));
    firstButtons.append(new KeyBoardToolButton("u","U","",""));
    firstButtons.append(new KeyBoardToolButton("i","I","",""));
    firstButtons.append(new KeyBoardToolButton("o","O","",""));
    firstButtons.append(new KeyBoardToolButton("p","P","",""));
    firstButtons.append(new KeyBoardToolButton("\u00E5","\u00C5","",""));

    secondButtons.append(new KeyBoardToolButton("a","A","",""));
    secondButtons.append(new KeyBoardToolButton("s","S","",""));
    secondButtons.append(new KeyBoardToolButton("d","D","",""));
    secondButtons.append(new KeyBoardToolButton("f","F","",""));
    secondButtons.append(new KeyBoardToolButton("g","G","",""));
    secondButtons.append(new KeyBoardToolButton("h","H","",""));
    secondButtons.append(new KeyBoardToolButton("j","J","",""));
    secondButtons.append(new KeyBoardToolButton("k","K","",""));
    secondButtons.append(new KeyBoardToolButton("l","L","",""));
    secondButtons.append(new KeyBoardToolButton("\u00F6","\u00D6","",""));
    secondButtons.append(new KeyBoardToolButton("\u00E4","\u00C4","",""));

    thirdButtons.append(GetLeftCapsLockBtn());
    thirdButtons.append(new KeyBoardToolButton("z","Z","",""));
    thirdButtons.append(new KeyBoardToolButton("x","X","",""));
    thirdButtons.append(new KeyBoardToolButton("c","C","",""));
    thirdButtons.append(new KeyBoardToolButton("v","V","",""));
    thirdButtons.append(new KeyBoardToolButton("b","B","",""));
    thirdButtons.append(new KeyBoardToolButton("n","N","",""));
    thirdButtons.append(new KeyBoardToolButton("m","M","",""));
    thirdButtons.append(GetRightCapsLockBtn());

    map.insert(0,firstButtons);
    map.insert(1,secondButtons);
    map.insert(2,thirdButtons);

    SetButtonsMap(map);
    SetTranslate("Kiinalainen","Englanti","Matematiikka","Tila");
}

