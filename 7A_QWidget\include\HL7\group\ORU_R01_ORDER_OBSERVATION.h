﻿#ifndef _ORU_R01_ORDER_OBSERVATION_H
#define _ORU_R01_ORDER_OBSERVATION_H
#include "../segment/OBR.h"
#include "../segment/OBX.h"
#include "../macros.h"
#include "../interface/IOBR.h"
#include "../interface/IOBX.h"
#include "../interface/IORU_R01_ORDER_OBSERVATION.h"

class ORU_R01_ORDER_OBSERVATION : public IORU_R01_ORDER_OBSERVATION
{

public:

	ORU_R01_ORDER_OBSERVATION();
	~ORU_R01_ORDER_OBSERVATION();

	DECLARE_OBJECTBASE
	BEGIN_IMPL_QUERYIF(ORU_R01_ORDER_OBSERVATION)
		IMPL_QUERYIF_BYPATH(IF_OBJECTBASE, IORU_R01_ORDER_OBSERVATION, IObjectBase)
		END_IMPL_QUERYIF()

	void Free();

	bool GetOBR(IOBR* obr);

	virtual void SetOBR(IOBR* obr);

	virtual NRET GetOBXCollection(IObjectList* objectList);

	void GetString(char** str);

	virtual void AddOBX(IOBX* obx);

	virtual int GetOBXSize();

	virtual bool GetOBX(IOBX* obx, std::size_t obxIndex);

private:

	OBR* m_obr;
	std::vector<IOBX*> m_obxVect;
};

#endif
