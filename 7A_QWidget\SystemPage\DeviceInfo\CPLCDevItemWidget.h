#ifndef CPLCDEVITEMWIDGET_H
#define CPLCDEVITEMWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-31
  * Description: 下位机item
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QGroupBox>
#include "PublicParams.h"

class CPLCDevItemWidget : public QWidget
{
    Q_OBJECT
public:
    CPLCDevItemWidget(int iMachineID, const SDevParamsStruct &sDevParams, QWidget *parent = nullptr);

public slots:
    void SlotSetDevStatus(int iMachineID, DeviceStatus eStatus);

protected:
    virtual void mousePressEvent(QMouseEvent *pEvent) override;

signals:
    void SignalShowDetailWidget(int iMachineID);

private:
    QGroupBox *_CreateGroupBox();

private:
    QLabel *m_pTitleLabel;
    QLabel *m_pIndexLabel, *m_pTextLabel;
    QLabel *m_pImageLabel;

private:
    int m_iMachineID;
    SDevParamsStruct m_sDevParams;
    DeviceStatus m_eStatus;
};

#endif // CPLCDEVITEMWIDGET_H
