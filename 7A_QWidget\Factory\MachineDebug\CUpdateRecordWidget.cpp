#include "CUpdateRecordWidget.h"
#include "PublicFunction.h"
#include <QBoxLayout>
#include <QApplication>

CUpdateRecordWidget::CUpdateRecordWidget(QWidget *parent) : QWidget(parent)
{
    QString strRecordPath = QApplication::applicationDirPath() + "/Resources/readme.txt";
    QString strData;
    ReadFile(strRecordPath, strData);
    m_pTextBrowser = new QTextBrowser(this);
    m_pTextBrowser->setGeometry(0, 0, 1490, 790);
    m_pTextBrowser->append(strData);
}
