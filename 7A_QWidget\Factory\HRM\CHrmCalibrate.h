#ifndef CHRMCALIBRATE_H
#define CHRMCALIBRATE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-11-14
  * Description: HRM-校准
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include "CCmdBase.h"
#include "CLabelComboBox.h"
#include "CHrmOneCalibrate.h"

class CHrmCalibrate : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CHrmCalibrate(QWidget *parent = nullptr);
    ~CHrmCalibrate();

    void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

private slots:
    void _Slot2RouteBtn();
    void _Slot4RouteBtn();

private:
    void _InitWidget();

private:
    CLysisOneCalibrate *m_pHrmOneCalibrate1;
    CLysisOneCalibrate *m_pHrmOneCalibrate2;
    CLysisOneCalibrate *m_pHrmOneCalibrate3;
    CLysisOneCalibrate *m_pHrmOneCalibrate4;

    QPushButton *m_p2RouteBtn, *m_p4RouteBtn;
    CLabelComboBox *m_pMachineComboBox;
};

#endif // CHRMCALIBRATE_H
