#include "wfkeyboard.h"

#include <QApplication>
#include <QDesktopWidget>

//#include "../src/views/inputmethod.h"
//#include "../src/common/language/language.h"

#include "views/inputmethod.h"
#include "common/language/language.h"

#define KEYBOARD_WIDTH_MIN (800)
#define KEYBOARD_HEIGHT_MIN (350)

const static QString version = "1.0.0.1";

class WfKeyBoard::PrivateData
{
public:
    PrivateData()
        : inputMethod(NULL)
        , bottomPadding(5)
        , isPaging(false)
        , fontSize(30)
        , pageSize(9)
        , btnColor(QString("#FFFFFF"))
        , bkgdColor(QString("#E6E6E6"))
        , textColor("#000")
        , pressedColor("#868482")
        , fontFamily("Source Han Sans CN")
    {
        QDesktopWidget* desk = qApp->desktop();

        w = KEYBOARD_WIDTH_MIN;
        h = KEYBOARD_HEIGHT_MIN;
        x = 0;
        y = desk->height() - h - bottomPadding;

        if(desk->width() > w)
        {
            w = desk->width();
        }
    }

public:
    InputMethod *inputMethod;

    int w,h,x,y;
    int bottomPadding;
    bool isPaging;
    int fontSize;
    int pageSize;

    QString btnColor;
    QString bkgdColor;
    QString textColor;
    QString pressedColor;
    QString fontFamily;
};

WfKeyBoard::WfKeyBoard()
    :md(new PrivateData())
{

}

void WfKeyBoard::SetColor(const QString& backgroundColor, const QString& buttonColor, const QString &textColor, const QString& pressedColor)
{
    if(backgroundColor.isEmpty() || buttonColor.isEmpty() || textColor.isEmpty() || pressedColor.isEmpty())
        return;

    md->btnColor = buttonColor;
    md->bkgdColor = backgroundColor;
    md->textColor = textColor;
    md->pressedColor = pressedColor;

    if(md->inputMethod != NULL)
    {
        md->inputMethod->SetColor(md->bkgdColor, md->btnColor, md->textColor, md->pressedColor);
    }
}

void WfKeyBoard::SetBottomPadding(int padding)
{
    md->bottomPadding = padding;

    QDesktopWidget* desk = qApp->desktop();
    md->y = desk->height() - md->h - md->bottomPadding;
    if(md->inputMethod != NULL)
    {
        md->inputMethod->SetGeometry(md->x, md->y, md->w, md->h);
    }
}

void WfKeyBoard::SetFontFamily(const QString &fontFamily)
{
    md->fontFamily = fontFamily;
    if(md->inputMethod)
    {
        md->inputMethod->SetFontFamily(fontFamily);
    }
}

void WfKeyBoard::InstallKeyBoard(QApplication *app, QString language)
{
    WfKeyboard::LanguageFunction::GetInstance()->SetCurrentLanguage(language);

    md->inputMethod = new InputMethod();
    md->inputMethod->setParent(app);
    md->inputMethod->SetGeometry(md->x, md->y, md->w, md->h);
    md->inputMethod->SetPagingEnabled(md->isPaging);
    md->inputMethod->SetColor(md->bkgdColor, md->btnColor, md->textColor, md->pressedColor);
    md->inputMethod->SetFontFamily(md->fontFamily);
    md->inputMethod->SetFontPixelSize(md->fontSize);
    md->inputMethod->SetPageSize(md->pageSize);
    app->installEventFilter(md->inputMethod);
}

static QString BuildDateTime()
{
    QString dateTime;
    dateTime += __DATE__;
    dateTime += " ";
    dateTime += __TIME__;
    return dateTime;
}

QString WfKeyBoard::Version() const
{
    QString ver = QString("Release Version : V%1  %2").arg(version).arg(BuildDateTime());
    return ver;
}

QStringList WfKeyBoard::GetSupportLanguages()
{
    return WfKeyboard::LanguageFunction::GetInstance()->GetSupportLanguages();
}

void WfKeyBoard::SetPagingEnabled(const bool& enable)
{
    md->isPaging = enable;

    if(md->inputMethod != NULL)
    {
        md->inputMethod->SetPagingEnabled(md->isPaging);
    }
}

void WfKeyBoard::SetGeometry(int x, int y, int w, int h)
{
    md->w = w;
    md->h = h;
    md->x = x;
    md->y = y;

    QDesktopWidget* desk = qApp->desktop();

    if(w > desk->width())
    {
        md->w = desk->width();
    }

    if(h > desk->height())
    {
        md->h = desk->height();
    }

    if(w < KEYBOARD_WIDTH_MIN)
    {
        md->w = KEYBOARD_WIDTH_MIN;
    }

    if(h < KEYBOARD_HEIGHT_MIN)
    {
        md->h = KEYBOARD_HEIGHT_MIN;
    }

    if(md->inputMethod != NULL)
    {
        md->inputMethod->SetGeometry(md->x, md->y, md->w, md->h);
    }
}

void WfKeyBoard::SetFontPixelSize(const int& size)
{
    if(size < 9 || size > 90)
        return;

    md->fontSize = size;

    if(md->inputMethod != NULL)
    {
        md->inputMethod->SetFontPixelSize(md->fontSize);
    }
}

void WfKeyBoard::SetPageSize(const int& size)
{
    if(size <= 0)
        return;

    md->pageSize = size;

    if(md->inputMethod != NULL)
    {
        md->inputMethod->SetPageSize(md->pageSize);
    }
}
