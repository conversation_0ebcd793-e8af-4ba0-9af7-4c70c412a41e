#ifndef CMACHINEDEBUG_H
#define CMACHINEDEBUG_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-12-15
  * Description: 通信调试
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

class CMachineCmd;
class CSoftUpdate;
class CUpdateRecordWidget;
class CMachineLogConfig;
class CMedianUS;

#include <QWidget>
#include <QStackedWidget>

#include "CHBtnTitleWidget.h"

class CMachineDebug : public QWidget
{
    Q_OBJECT
public:
    explicit CMachineDebug(QWidget *parent = nullptr);
    ~CMachineDebug();

private slots:
    void _SlotTitleChanged(int index);

private:
    void _InitWidget();

private:
    CMachineCmd *m_pCMachineCmd;
    CSoftUpdate *m_pCSoftUpdate;
    CUpdateRecordWidget *m_pCUpdateRecordWidget;
    CMachineLogConfig *m_pCMachineLogConfig;
    CMedianUS *m_pCMachineUS;

    CHBtnTitleWidget *m_pCHBtnTitle;
    QStackedWidget *m_pStackedWidget;
};

#endif // CMACHINEDEBUG_H
