#ifndef CVBTNTITLEWIDGET_H
#define CVBTNTITLEWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-10-30
  * Description: 由一组按钮组成的竖标题
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QPushButton>

class CVBtnTitleWidget : public QWidget
{
    Q_OBJECT
public:
    CVBtnTitleWidget(const QStringList &strTitleList, int iSpacing = 15, QWidget *parent = nullptr);
    ~CVBtnTitleWidget();

    void SetTitleIndex(int index);
    void SetBtnFixedSize(int iWidth, int iHeight);

signals:
    void SignalTitleChanged(int);

private slots:
    void _SlotBtnClicked();

private:
    void _InitWidget();
    void _ButtonClicked(int index);

private:
    int m_iSpacing;
    QStringList m_strTitleList;
    QList<QPushButton*> m_btnList;
};

#endif // CVBTNTITLEWIDGET_H
