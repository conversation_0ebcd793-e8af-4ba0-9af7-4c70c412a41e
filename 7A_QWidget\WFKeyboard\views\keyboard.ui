<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>KeyBoard</class>
 <widget class="QWidget" name="KeyBoard">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>413</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>800</width>
    <height>300</height>
   </size>
  </property>
  <property name="font">
   <font>
    <family>微软雅黑</family>
    <pointsize>18</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <property name="layoutDirection">
   <enum>Qt::LeftToRight</enum>
  </property>
  <property name="styleSheet">
   <string notr="true">QStackedWidget
{
border: 0px solid blue;
}</string>
  </property>
  <property name="locale">
   <locale language="Chinese" country="China"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout" stretch="0,1,0">
   <property name="spacing">
    <number>10</number>
   </property>
   <property name="leftMargin">
    <number>30</number>
   </property>
   <property name="topMargin">
    <number>10</number>
   </property>
   <property name="rightMargin">
    <number>30</number>
   </property>
   <property name="bottomMargin">
    <number>20</number>
   </property>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,3,0">
     <property name="spacing">
      <number>10</number>
     </property>
     <property name="leftMargin">
      <number>0</number>
     </property>
     <property name="topMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>0</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="QLabel" name="lbl_title">
       <property name="minimumSize">
        <size>
         <width>225</width>
         <height>55</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>1500</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="font">
        <font>
         <family>Sans</family>
         <pointsize>20</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Plain</enum>
       </property>
       <property name="lineWidth">
        <number>0</number>
       </property>
       <property name="text">
        <string notr="true"/>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_3">
       <property name="spacing">
        <number>10</number>
       </property>
       <item>
        <widget class="QLineEdit" name="Edit_ori">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>55</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>1500</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <family>文泉驿微米黑</family>
          </font>
         </property>
         <property name="layoutDirection">
          <enum>Qt::RightToLeft</enum>
         </property>
         <property name="locale">
          <locale language="Arabic" country="Iraq"/>
         </property>
         <property name="cursorPosition">
          <number>0</number>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QTextEdit" name="tEdit_ori">
         <property name="enabled">
          <bool>true</bool>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>55</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>48</height>
          </size>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <widget class="KeyBoardToolButton" name="toolButton_backspace">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>90</width>
         <height>55</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>1500</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="font">
        <font>
         <family>文泉驿微米黑</family>
        </font>
       </property>
       <property name="focusPolicy">
        <enum>Qt::NoFocus</enum>
       </property>
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <property name="text">
        <string notr="true"/>
       </property>
       <property name="icon">
        <iconset>
         <normalon>:/svg/backspace.svg</normalon>
        </iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>40</width>
         <height>40</height>
        </size>
       </property>
       <property name="toolButtonStyle">
        <enum>Qt::ToolButtonIconOnly</enum>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QStackedWidget" name="stackedWidget">
     <property name="lineWidth">
      <number>0</number>
     </property>
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="pageNormal">
      <layout class="QVBoxLayout" name="verticalLayout_3" stretch="1,3">
       <property name="spacing">
        <number>10</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QStackedWidget" name="stackedWidget_Normal_Header">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>55</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>55</height>
          </size>
         </property>
         <property name="currentIndex">
          <number>0</number>
         </property>
         <widget class="QWidget" name="pageNormalHeader_Num">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>55</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>55</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_2">
           <property name="spacing">
            <number>10</number>
           </property>
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="KeyBoardToolButton" name="toolButton_1">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>55</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>文泉驿微米黑</family>
               <pointsize>24</pointsize>
              </font>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="text">
              <string notr="true">1</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="KeyBoardToolButton" name="toolButton_2">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>55</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>文泉驿微米黑</family>
               <pointsize>24</pointsize>
              </font>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="text">
              <string notr="true">2</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="KeyBoardToolButton" name="toolButton_3">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>55</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>文泉驿微米黑</family>
               <pointsize>24</pointsize>
              </font>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="text">
              <string notr="true">3</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="KeyBoardToolButton" name="toolButton_4">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>55</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>文泉驿微米黑</family>
               <pointsize>24</pointsize>
              </font>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="text">
              <string notr="true">4</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="KeyBoardToolButton" name="toolButton_5">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>55</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>文泉驿微米黑</family>
               <pointsize>24</pointsize>
              </font>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="text">
              <string notr="true">5</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="KeyBoardToolButton" name="toolButton_6">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>55</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>文泉驿微米黑</family>
               <pointsize>24</pointsize>
              </font>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="text">
              <string notr="true">6</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="KeyBoardToolButton" name="toolButton_7">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>55</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>文泉驿微米黑</family>
               <pointsize>24</pointsize>
              </font>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="text">
              <string notr="true">7</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="KeyBoardToolButton" name="toolButton_8">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>55</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>文泉驿微米黑</family>
               <pointsize>24</pointsize>
              </font>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="text">
              <string notr="true">8</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="KeyBoardToolButton" name="toolButton_9">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>55</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>55</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>文泉驿微米黑</family>
               <pointsize>24</pointsize>
              </font>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="text">
              <string notr="true">9</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="KeyBoardToolButton" name="toolButton_0">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>55</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>文泉驿微米黑</family>
               <pointsize>24</pointsize>
              </font>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="text">
              <string notr="true">0</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
       <item>
        <widget class="QStackedWidget" name="stackedWidget_Normal">
         <property name="lineWidth">
          <number>0</number>
         </property>
         <property name="currentIndex">
          <number>0</number>
         </property>
         <widget class="QWidget" name="page_Qwerty_Board"/>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="bottomWidget" native="true">
     <layout class="QGridLayout" name="gridLayout_2">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="spacing">
       <number>0</number>
      </property>
      <item row="0" column="0">
       <layout class="QHBoxLayout" name="layout_Normal_Row05" stretch="10,0,0,30,0,0,0">
        <property name="spacing">
         <number>10</number>
        </property>
        <item>
         <widget class="KeyBoardToolButton" name="toolButton_symbol">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>65</width>
            <height>55</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>文泉驿微米黑</family>
            <pointsize>20</pointsize>
           </font>
          </property>
          <property name="focusPolicy">
           <enum>Qt::NoFocus</enum>
          </property>
          <property name="text">
           <string notr="true">;!?</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="KeyBoardToolButton" name="toolButton_znen">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>60</width>
            <height>55</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>文泉驿微米黑</family>
            <pointsize>20</pointsize>
           </font>
          </property>
          <property name="focusPolicy">
           <enum>Qt::NoFocus</enum>
          </property>
          <property name="locale">
           <locale language="Chinese" country="China"/>
          </property>
          <property name="text">
           <string notr="true">EN</string>
          </property>
          <property name="iconSize">
           <size>
            <width>49</width>
            <height>21</height>
           </size>
          </property>
          <property name="toolButtonStyle">
           <enum>Qt::ToolButtonIconOnly</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="KeyBoardToolButton" name="toolButton__">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>60</width>
            <height>55</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>文泉驿微米黑</family>
            <pointsize>20</pointsize>
           </font>
          </property>
          <property name="focusPolicy">
           <enum>Qt::NoFocus</enum>
          </property>
          <property name="text">
           <string notr="true">-</string>
          </property>
          <property name="toolButtonStyle">
           <enum>Qt::ToolButtonTextOnly</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QToolButton" name="toolButton_space">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>265</width>
            <height>55</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>文泉驿微米黑</family>
            <pointsize>20</pointsize>
           </font>
          </property>
          <property name="focusPolicy">
           <enum>Qt::NoFocus</enum>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string notr="true"/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="KeyBoardToolButton" name="toolButton_dot">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>60</width>
            <height>55</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>文泉驿微米黑</family>
            <pointsize>30</pointsize>
           </font>
          </property>
          <property name="focusPolicy">
           <enum>Qt::NoFocus</enum>
          </property>
          <property name="text">
           <string notr="true">.</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="KeyBoardToolButton" name="toolButton_num">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>60</width>
            <height>55</height>
           </size>
          </property>
          <property name="focusPolicy">
           <enum>Qt::NoFocus</enum>
          </property>
          <property name="text">
           <string notr="true">123</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QToolButton" name="toolButton_hide">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>90</width>
            <height>55</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>文泉驿微米黑</family>
            <pointsize>20</pointsize>
           </font>
          </property>
          <property name="focusPolicy">
           <enum>Qt::NoFocus</enum>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string notr="true"/>
          </property>
          <property name="icon">
           <iconset>
            <normalon>:/svg/esc.svg</normalon>
           </iconset>
          </property>
          <property name="iconSize">
           <size>
            <width>36</width>
            <height>36</height>
           </size>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>KeyBoardToolButton</class>
   <extends>QToolButton</extends>
   <header>./common/keyboardtoolbutton/keyboardtoolbutton.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
