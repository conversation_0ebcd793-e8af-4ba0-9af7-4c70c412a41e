#include "CLineEditSpinBox.h"
#include <QBoxLayout>
#include <QRegularExpressionValidator>

CLineEditSpinBox::CLineEditSpinBox(QWidget *parent)
    : QWidget(parent)
{
    _InitWidget();
}

CLineEditSpinBox::~CLineEditSpinBox()
{

}

QString CLineEditSpinBox::getText()
{
    return m_pEdit->text();
}

void CLineEditSpinBox::setText(const QString &strText)
{
    m_pEdit->setText(strText);
}

void CLineEditSpinBox::clearData()
{
    m_pEdit->clear();
}

void CLineEditSpinBox::_SlotSpinUpBtn()
{
    float tem = m_pEdit->text().toFloat()+m_fStepValue;
    if(tem < 0.001)
    {
        return;
    }
    m_pEdit->setText(QString::number(tem,'f',2));
}

void CLineEditSpinBox::_SlotSpinDownBtn()
{
    float tem = m_pEdit->text().toFloat()-m_fStepValue;
    if(tem < 0.001)
    {
        return;
    }
    m_pEdit->setText(QString::number(tem,'f',2));
}
void CLineEditSpinBox::_InitWidget()
{
    QRegularExpression regex("^-?[0-9]+(\\.[0-9]{0,2})?$"); // 至少一个数字且不允许为负数
    QRegularExpressionValidator *validator = new QRegularExpressionValidator(regex, this);

    m_pEdit = new CLineEdit;
    m_pEdit->setFixedSize(200, 68);
    m_pEdit->setObjectName("SpinEdit");
    m_pEdit->setValidator(validator);

    m_pSpinUp = new QPushButton("");
    m_pSpinUp->setObjectName("SpinButtonUp");
    m_pSpinUp->setFixedSize(50,36);
    connect(m_pSpinUp,&QPushButton::clicked,this,&CLineEditSpinBox::_SlotSpinUpBtn);

    m_pSpinDown = new QPushButton("");
    m_pSpinDown->setObjectName("SpinButtonDown");
    m_pSpinDown->setFixedSize(50,36);
    connect(m_pSpinDown,&QPushButton::clicked,this,&CLineEditSpinBox::_SlotSpinDownBtn);

    QVBoxLayout* pSpinLayout = new QVBoxLayout;
    pSpinLayout->setMargin(0);
    pSpinLayout->setSpacing(0);
    pSpinLayout->addWidget(m_pSpinUp,0,Qt::AlignLeft|Qt::AlignTop);
    pSpinLayout->addStretch(1);
    pSpinLayout->addWidget(m_pSpinDown,0,Qt::AlignLeft|Qt::AlignBottom);

    QHBoxLayout* pLayout = new QHBoxLayout();
    pLayout->setMargin(0);
    pLayout->addWidget(m_pEdit, 0 ,Qt::AlignRight|Qt::AlignHCenter);
    pLayout->addLayout(pSpinLayout);

    setLayout(pLayout);
}

