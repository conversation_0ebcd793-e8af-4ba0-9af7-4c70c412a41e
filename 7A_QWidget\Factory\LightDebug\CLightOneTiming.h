#ifndef CLIGHTONETIMING_H
#define CLIGHTONETIMING_H

#include "CCmdBase.h"
#include <QWidget>

class CLightOneTiming : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CLightOneTiming(QWidget *parent = nullptr);
    ~CLightOneTiming();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;
    void startTiming(int iMachineID, int cycle = 1);
    void startTimingType2(int iMachineID, int cycle = 1);
    void startPreTiming(int iMachineID);     // 单独执行前置时序
    void startPostTiming(int iMachineID);    // 单独执行后置时序
    void timingSendNext(int iMachineID);

signals:
    void SignalTimingEnd(void);
    void SignalPreTimingEnd(void);   // 前置时序完成信号

private:
    typedef enum{
        eLightTimingState_Idle = 0,
        eLightTimingState_SendNext,
        eLightTimingState_WaitRet,
        eLightTimingState_Error,
        eLightTimingState_Stop,
        eLightTimingState_End, 
    }eLightTimingState_t;

    typedef enum{
        eTimingPhase_Pre = 0,    // 前置阶段
        eTimingPhase_Loop,       // 循环阶段
        eTimingPhase_Post,       // 后置阶段
        eTimingPhase_End,        // 结束阶段
        eTimingPhase_OnlyLoop,   // 仅循环阶段
        eTimingPhase_OnlyPre,    // 仅前置阶段
        eTimingPhase_OnlyPost    // 仅后置阶段
    }eTimingPhase_t;

    typedef struct{
        int index;
        int timingLength;
        int methodID;
        bool bMethodReply;
        int machineID;
        eLightTimingState_t timingState;
        int cycle;
        int cycleCount;
        eTimingPhase_t phase;    // 当前执行阶段
    }sLightTimingInfo_t;

    sLightTimingInfo_t m_lightTimingInfo;
    QList<int> m_iCurrentTiming;
};

#endif // CLIGHTONETIMING_H
