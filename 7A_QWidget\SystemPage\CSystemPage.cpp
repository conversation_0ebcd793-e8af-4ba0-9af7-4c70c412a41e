#include "CSystemPage.h"
#include <QBoxLayout>

#include "PublicFunction.h"

CSystemPage::CSystemPage(QWidget *parent) : QWidget(parent)
{
    m_pCSystemTitleWidget = new CSystemMainWidget;
    connect(m_pCSystemTitleWidget, &CSystemMainWidget::SignalTitleIndex, this, &CSystemPage::_SlotGotoPage);

    m_pCGeneralWidget = new CGeneralWidget;
    connect(m_pCGeneralWidget, &CGeneralWidget::SignalReturn, this, &CSystemPage::_SlotReturn2Main);

    m_pCNetworkWidget = new CNetworkWidget;
    connect(m_pCNetworkWidget, &CNetworkWidget::SignalReturn, this, &CSystemPage::_SlotReturn2Main);

    m_pCUserWidget = new CUserWidget;
    connect(m_pCUserWidget, &CUserWidget::SignalReturn, this, &CSystemPage::_SlotReturn2Main);

    m_pCLogWidget = new CLogWidget;
    connect(m_pCLogWidget, &CLogWidget::SignalReturn, this, &CSystemPage::_SlotReturn2Main);

    m_pCUpdateWidget = new CUpdateWidget;
    connect(m_pCUpdateWidget, &CUpdateWidget::SignalReturn, this, &CSystemPage::_SlotReturn2Main);

    m_pCDeviceInfoWidget = new CDeviceInfoWidget;
    connect(m_pCDeviceInfoWidget, &CDeviceInfoWidget::SignalReturn, this, &CSystemPage::_SlotReturn2Main);

    m_pCSelfTestWidget = new CSelfTestWidget;
    connect(m_pCSelfTestWidget, &CSelfTestWidget::SignalReturn, this, &CSystemPage::_SlotReturn2Main);

    m_pCCalibrationWidget = new CCalibrationWidget;
    connect(m_pCCalibrationWidget, &CCalibrationWidget::SignalReturn, this, &CSystemPage::_SlotReturn2Main);

    m_CPrinterWidget = new CPrinterWidget;
    connect(m_CPrinterWidget, &CPrinterWidget::SignalReturn, this, &CSystemPage::_SlotReturn2Main);

    m_pCMaintainWidget = new CMaintainWidget;
    connect(m_pCMaintainWidget, &CMaintainWidget::SignalReturn, this, &CSystemPage::_SlotReturn2Main);

    m_pCFactoryWidget = new CFactoryWidget;
    connect(m_pCFactoryWidget, &CFactoryWidget::SignalReturn, this, &CSystemPage::_SlotReturn2Main);

    m_pStackWidget = new QStackedWidget;
    m_pStackWidget->setFixedSize(1684, 958);
    m_pStackWidget->addWidget(m_pCSystemTitleWidget);
    m_pStackWidget->addWidget(m_pCGeneralWidget);
    m_pStackWidget->addWidget(m_pCNetworkWidget);
    m_pStackWidget->addWidget(m_pCUserWidget);
    m_pStackWidget->addWidget(m_pCLogWidget);
    m_pStackWidget->addWidget(m_pCUpdateWidget);
    m_pStackWidget->addWidget(m_pCDeviceInfoWidget);
    m_pStackWidget->addWidget(m_pCSelfTestWidget);
    m_pStackWidget->addWidget(m_pCCalibrationWidget);
    m_pStackWidget->addWidget(m_CPrinterWidget);
    m_pStackWidget->addWidget(m_pCMaintainWidget);
    m_pStackWidget->addWidget(m_pCFactoryWidget);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pStackWidget);
    pLayout->addStretch(1);
    this->setLayout(pLayout);

    LoadQSS(this, ":/qss/qss/system/system.qss");
}

void CSystemPage::GotoRunLogPage()
{
    m_pStackWidget->setCurrentIndex(11);
    m_pCFactoryWidget->GotoRunLog();
}

void CSystemPage::GotoMainPage()
{
    m_pStackWidget->setCurrentIndex(0);
}

void CSystemPage::_SlotGotoPage(int iPage)
{
    m_pStackWidget->setCurrentIndex(iPage + 1);
}

void CSystemPage::_SlotReturn2Main()
{
    m_pStackWidget->setCurrentIndex(0);
}
