#include "CTimingCompose.h"
#include <QBoxLayout>
#include <QGridLayout>
#include <QHeaderView>
#include <QTime>
#include <QEvent>
#include <QStyleFactory>

#include "CMotorDB.h"
#include "CLotInfoDB.h"
#include "CTimingTecDB.h"
#include "CMessageBox.h"
#include "CCmdManager.h"
#include "CRunTest.h"
#include "CHeartBeat.h"

CTimingCompose::CTimingCompose(QWidget *parent)
    : QWidget(parent)
    , m_iSoftType(Soft_Extarct)
    , m_iUiMachineID(0)
{
    m_strClassName = "CTimingCompose";

    QTime start = QTime::currentTime();

    _InitMap();
    _InitTextList();

    Register2Map(Method_pause);
    Register2Map(Method_resume);
    Register2Map(Method_timing_step);

    for(int i=0; i<gk_iMachineCount; i++)
    {
        STimingStruct *pStruct = new STimingStruct;
        m_stTimingStructList.push_back(pStruct);
    }

    _InitWidget();
    _InitLayout();

    connect(CHeartBeat::GetInstance(), &CHeartBeat::SignalUpdateItemStatus,
            this, &CTimingCompose::SlotUpdateItemStatus);
    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalSoftTypeChanged,
            this, &CTimingCompose::SlotSoftTypeChanged);
    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalReGetMotorTextIDData,
            this, &CTimingCompose::SlotReGetMotorTextIDData);
    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalUpdatePCRNameInfo,
            this, &CTimingCompose::SlotUpdatePCRNameInfo);
    connect(CRunTest::GetInstance(), &CRunTest::SignalAgingLeftTimes, this, &CTimingCompose::SlotUpdateAgingLeftTime);

    m_pMotorSerialCompose = new CMotorSerialCompose(this);
    m_pMotorSerialCompose->SetMotorComboBoxList(m_strMotorTextIDMap.values());
    connect(m_pMotorSerialCompose, &CMotorSerialCompose::SignalMotorComposeConfirm, this, &CTimingCompose::SlotMotorSerialComposeData);
    m_pMotorSerialCompose->setVisible(false);

    _LoadName2Table();

    qDebug()<<"时序页面构造时间:"<<start.msecsTo(QTime::currentTime());
}

CTimingCompose::~CTimingCompose()
{
    UnRegister2Map(Method_pause);
    UnRegister2Map(Method_resume);
    UnRegister2Map(Method_timing_step);
}

void CTimingCompose::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    switch (iMethodID)
    {
    case Method_pause:
        _parsePasuseCmd(iMachineID, iResult);
        break;
    case Method_resume:
        _parseResumeCmd(iMachineID, iResult);
        break;
    case Method_timing_step:
        _parseTimingStepCmd(iMachineID, iResult, qVarData);
        break;
    default:
        break;
    }
}

void CTimingCompose::_parsePasuseCmd(int iMachineID, int iResult)
{
    if(0 == iResult)
        ShowSuccess(this, m_strTipsText, tr("%1#时序暂停成功").arg(iMachineID + 1));
    else
        ShowError(this, m_strTipsText, tr("%1#时序暂停失败").arg(iMachineID + 1));
}

void CTimingCompose::_parseResumeCmd(int iMachineID, int iResult)
{
    if(0 == iResult)
        ShowSuccess(this, m_strTipsText, tr("%1#时序恢复成功").arg(iMachineID + 1));
    else
        ShowError(this, m_strTipsText, tr("%1#时序恢复失败").arg(iMachineID + 1));
}

void CTimingCompose::_parseTimingStepCmd(int iMachineID, int iResult, const QVariant &qVarData)
{
    if(0 != iResult)
        return;

    QVariantList qVarList = qVarData.toList();
    if(qVarList.size() < 1)
        return;

    int iStep1 = qVarList.at(0).toInt();
    qDebug()<<QString("%1#时序第%2步").arg(iMachineID + 1).arg(iStep1);
    m_stTimingStructList.at(iMachineID)->pTableWidget->selectRow(iStep1);
}

void CTimingCompose::SlotUpdateItemStatus(int iMachineID, DeviceStatus eStatus, bool bCardExist)
{
    Q_UNUSED(bCardExist);
    if(iMachineID < 0 || iMachineID >= m_stTimingStructList.size())
        return;

    m_stTimingStructList[iMachineID]->eStatus = eStatus;
}

void CTimingCompose::SlotSoftTypeChanged(int iSoftType)
{
    return;

    if(m_iSoftType == iSoftType)
        return;

    m_iSoftType = iSoftType;
    m_strMotorNameList = CPublicConfig::GetInstance()->GetMotorNameList();

    QTableWidget *pTableWidget = m_stTimingStructList[m_iUiMachineID]->pTableWidget;
    for(int iRow=0; iRow<pTableWidget->rowCount(); iRow++)
    {
        QComboBox *pComboBox = dynamic_cast<QComboBox *>(pTableWidget->cellWidget(iRow, COL_NUM_CMD));
        if(pComboBox)
        {
            pComboBox->clear();
            if(Soft_Auto == m_iSoftType)
                pComboBox->addItems(m_strAutoCmdTextList);
            else
                pComboBox->addItems(m_strExtractCmdTextList);
        }
    }
}

void CTimingCompose::SlotMotorSerialComposeData(const QString &strData)
{
    qDebug()<<"时序电机串行组合数据确认:"<<strData;
    QTableWidget *pTableWidget = m_stTimingStructList[m_iUiMachineID]->pTableWidget;
    int iRow = pTableWidget->currentRow();
    CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(pTableWidget->cellWidget(iRow, COL_NUM_PARAM1));
    if(pLineEdit)
        pLineEdit->setText(strData);
}

void CTimingCompose::SlotReGetMotorTextIDData()
{
    m_strMotorTextIDMap = CMotorDB::GetInstance().GetCmdIDNameMap();
    m_pMotorSerialCompose->SetMotorComboBoxList(m_strMotorTextIDMap.values());

    for(int iMachine=0; iMachine<m_stTimingStructList.size(); iMachine++)
    {
        QTableWidget *pTableWidget = m_stTimingStructList.at(iMachine)->pTableWidget;
        for(int iRow=0; iRow<pTableWidget->rowCount(); iRow++)
        {
            QComboBox *pCmdComboBox = dynamic_cast<QComboBox *>(pTableWidget->cellWidget(iRow, COL_NUM_CMD));

            if(Soft_Extarct == m_iSoftType) //提取工装才有的单个电机指令
            {
                if(pCmdComboBox->currentIndex() == _GetCmdIndexByID(Method_Motor_CMD))
                {
                    QComboBox *pComboBox = dynamic_cast<QComboBox *>(pTableWidget->cellWidget(iRow, COL_NUM_PARAM1));
                    int width = pTableWidget->horizontalHeader()->sectionSize(COL_NUM_PARAM1);
                    pComboBox->setMinimumSize(width, 50);
                    int index = pComboBox->currentIndex();
                    pComboBox->clear();
                    pComboBox->addItems(m_strMotorTextIDMap.values());
                    pComboBox->setCurrentIndex(index);
                }
            }

            //电机串行组合
            if(pCmdComboBox->currentIndex() == _GetCmdIndexByID(Method_MOTOR_COMPOSE))
            {
                CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(pTableWidget->cellWidget(iRow, COL_NUM_PARAM1));
                QStringList strList = pLineEdit->text().split(SPLIT_MOTOR);
                for(int i=0; i<strList.size(); i++)
                {
                    QStringList oneList = strList.at(i).split(SPLIT_IN_CMD);
                    if(oneList.isEmpty())
                        continue;
                    QString strTextID = oneList.at(0);
                    if(strTextID.contains("_"))
                    {
                        QStringList myList = strTextID.split("_");
                        QString strID = myList.last();
                        QString strNewTextID = m_strMotorTextIDMap.value(strID);
                        oneList[0] = strNewTextID;
                    }
                    strList[i] = oneList.join(SPLIT_IN_CMD);
                }
                pLineEdit->setText(strList.join(SPLIT_MOTOR));
            }
        }
    }
}

void CTimingCompose::SlotUpdatePCRNameInfo(const QStringList &strPCRList)
{
    QString strCurrentPCR = m_pPCRComboBox->GetCurrentText();
    QStringList strList = strPCRList;
    strList.push_front("");
    m_pPCRComboBox->SetComboBoxList(strList);
    m_pPCRComboBox->SetCurrentIndex(strList.indexOf(strCurrentPCR));
}

void CTimingCompose::SlotUpdateAgingLeftTime(int iMachineID, int iLeftTimes)
{
    if(iMachineID < m_stTimingStructList.size())
        m_stTimingStructList[iMachineID]->strRunTimes = QString::number(iLeftTimes);

    if(m_iUiMachineID == iMachineID)
        m_pAgingLineEdit->setText(QString::number(iLeftTimes));
}

void CTimingCompose::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;
    QWidget::showEvent(pEvent);
}

void CTimingCompose::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;
    QWidget::hideEvent(pEvent);
}

bool CTimingCompose::eventFilter(QObject *pObject, QEvent *pEvent)
{
    if(pObject->inherits("QComboBox"))
    {
        if(QEvent::Wheel == pEvent->type())
            return true;
    }

    return QWidget::eventFilter(pObject, pEvent);
}

void CTimingCompose::_SlotMachineComboBoxChanged(int iMachineID)
{
    m_iUiMachineID = iMachineID;
    m_pStackedWidget->setCurrentIndex(iMachineID);

    STimingStruct *pStruct = m_stTimingStructList.at(iMachineID);
    m_pTimingNameLineEdit->SetLineEditText(pStruct->strTimingName);
    m_pCardIDLineEdit->SetLineEditText(pStruct->strCardID);
    m_pPCRComboBox->SetCurrentIndex(pStruct->iPCRIndex);
    m_pProjectComboBox->SetCurrentIndex(pStruct->iProjectIndex);
    m_pAgingLineEdit->setText(pStruct->strRunTimes);
}

void CTimingCompose::_SlotTimingNameTextChanged(const QString &strName)
{
    m_stTimingStructList[m_iUiMachineID]->strTimingName = strName;
}

void CTimingCompose::_SlotCardIDTextChanged(const QString &strCardID)
{
    m_stTimingStructList[m_iUiMachineID]->strCardID = strCardID;
	qDebug()<<Q_FUNC_INFO<<m_iUiMachineID<<strCardID;

    if(strCardID.toLocal8Bit().length() > 50)
    {
        ShowInformation(this, m_strTipsText, tr("卡盒ID长度不能大于50"));
        return;
    }
}

void CTimingCompose::_SlotPCRIndexChanged(int index)
{
    m_stTimingStructList[m_iUiMachineID]->iPCRIndex = index;
}

void CTimingCompose::_SlotProjectIndexChanged(int index)
{
    m_stTimingStructList[m_iUiMachineID]->iProjectIndex = index;
}

void CTimingCompose::_SlotLoadBtn()
{
    int iRow = m_pFileTableWidget->currentRow();
    if(iRow < 0)
    {
        ShowInformation(this, m_strTipsText, tr("请先选择时序"));
        return;
    }

    QTableWidget *pWidget = m_stTimingStructList[m_iUiMachineID]->pTableWidget;
    _ClearTableWidget(pWidget);

    QString strTimingName = m_pFileTableWidget->item(iRow, 1)->text();
    if(strTimingName.isEmpty())
        return;
    m_pTimingNameLineEdit->SetLineEditText(strTimingName);

    QString strTimingContent = CTimingTecDB::GetInstance().GetTimingContent(strTimingName);
    if(strTimingContent.isEmpty())
        return;
    QStringList strList = strTimingContent.split(SPLIT_BETWEEN_CMD);
    int size = strList.size();
    qDebug()<<"timing加载时序:"<<size<<strTimingName<<strTimingContent;
    if(0 == size)
        return;

    m_pLoadBtn->setEnabled(false); //防止连点

    pWidget->setRowCount(size);
    for(int i=0; i<size; i++)
    {
        QStringList oneList = strList.at(i).split(SPLIT_IN_CMD);
        if(oneList.isEmpty())
            continue;

        _SetOneRowData(pWidget, i, oneList);
    }

    m_pLoadBtn->setEnabled(true);
}

void CTimingCompose::_SlotDelBtn()
{
    int iRow = m_pFileTableWidget->currentRow();
    if(iRow < 0)
    {
        ShowInformation(this, m_strTipsText, tr("请先选择时序"));
        return;
    }

    QString strTimingName = m_pFileTableWidget->item(iRow, 1)->text();
    qDebug()<<"删除时序:"<<strTimingName;
    m_strTimingNameList.removeOne(strTimingName);
    m_pFileTableWidget->removeRow(iRow);
    ResortTableWidget(m_pFileTableWidget);
    CTimingTecDB::GetInstance().DeleteOneTiming(strTimingName);
}

void CTimingCompose::_SlotReleaseBtn()
{
    QString strCmd = GetJsonCmdString(Method_PCRMRST);
    SendJsonCmd(m_iUiMachineID, Method_PCRMRST, strCmd);
}

void CTimingCompose::_SlotPressBtn()
{
    QString strCmd = GetJsonCmdString(Method_PCRPRTH);
    SendJsonCmd(m_iUiMachineID, Method_PCRPRTH, strCmd);
}

void CTimingCompose::_SlotCopyBtn()
{
    QTableWidget *pTableWidget = m_stTimingStructList.at(m_iUiMachineID)->pTableWidget;
    int iRow = pTableWidget->currentRow();
    QString strCopy = m_pCopyLineEidt->text();
    qDebug()<<"时序行复制:"<<iRow<<strCopy;
    if(strCopy.contains(",") && strCopy.contains(":"))
    {
        QString strLeft = strCopy.split(":").first();
        int iBegin = strLeft.split(",").first().toInt() - 1;
        int iEnd = strLeft.split(",").last().toInt() - 1;
        int iDst = strCopy.split(":").last().toInt() - 1;
        qDebug()<<"时序多行复制:"<<iBegin<<iEnd<<iDst;
        if(iBegin < 0 || iEnd < 0 || iDst < 0)
            return;

        QStringList strList;
        for(int i=iBegin; i<=iEnd; i++)
            strList.push_back(_GetOneRowData(pTableWidget, i));

        for(int i=0; i<=iEnd-iBegin; i++)
        {
            qDebug()<<"insert:"<<i + iDst;
            pTableWidget->insertRow(i+iDst);
        }

        for(int i=0; i<strList.size(); i++)
            _SetOneRowData(pTableWidget, iDst+i, strList.at(i).split(SPLIT_IN_CMD));
    }
    else
    {
        if(iRow < 0)
            return;

        int iCopyRow = strCopy.toInt();
        if(iCopyRow <= 0)
            return;

        QString strSelectData = _GetOneRowData(pTableWidget, iRow);
        pTableWidget->insertRow(iCopyRow - 1);
        _SetOneRowData(pTableWidget, iCopyRow - 1, strSelectData.split(SPLIT_IN_CMD));
    }

    ResortTableWidget(pTableWidget);
}

void CTimingCompose::_SlotRunTimesLineEditTextChanged(const QString &strTimes)
{
    m_stTimingStructList[m_iUiMachineID]->strRunTimes = strTimes;
}

void CTimingCompose::_SlotResetCheckBox(bool bChecked)
{
    RUN_LOG(QString("时序测试结束是否自动复位:").arg(bChecked));
    CPublicConfig::GetInstance()->SetTestDoneAutoReset(bChecked);
}

void CTimingCompose::_SlotCmdComboBoxChanged(int index)
{
    int iCmdID = _GetCmdIDByIndex(index);
    QTableWidget *pTableWidget = m_stTimingStructList[m_iUiMachineID]->pTableWidget;
    _SetOneRowData(pTableWidget, pTableWidget->currentRow(), {QString::number(iCmdID)});
}

void CTimingCompose::_SlotListBtn()
{
    QPushButton *pBtn = dynamic_cast<QPushButton *>(sender());
    if(nullptr == pBtn)
        return;

    QTableWidget *pTableWidget = m_stTimingStructList[m_iUiMachineID]->pTableWidget;
    int index = pBtn->property("index").toInt();
    switch (index)
    {
    case 0: _AddOneRow(pTableWidget); break;
    case 1: _DelOneRow(pTableWidget); break;
    case 2: _MoveUpRow(pTableWidget); break;
    case 3: _MoveDownRow(pTableWidget); break;
    case 4: _SaveTabelData(pTableWidget); break;
    case 5: _ClearTableWidget(pTableWidget); break;
    case 6: _StartTest(m_iUiMachineID); break;
    case 7: _PauseTest(); break;
    case 8: _StopTest(); break;
    case 9: _ResumeTest(); break;
    case 10: _ImportTiming(); break;
    case 11: _ExportTiming(); break;
    default: break;
    }
}

void CTimingCompose::_SlotMotorSerialComposeShow()
{
    CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(sender());
    if(nullptr == pLineEdit)
        return;

    m_pMotorSerialCompose->ShowWithData(pLineEdit->text());
}

void CTimingCompose::_LoadName2Table()
{
    m_pFileTableWidget->clearContents();
    m_pFileTableWidget->setRowCount(0);

    m_strTimingNameList = CTimingTecDB::GetInstance().GetTimingNameList();
    if(m_strTimingNameList.size() <= 0)
        return;
    m_pFileTableWidget->setRowCount(m_strTimingNameList.size());
    for(int i=0; i<m_strTimingNameList.size(); i++)
    {
        QTableWidgetItem* pIdItem = new QTableWidgetItem;
        pIdItem->setText(QString::number(i + 1));
        pIdItem->setTextAlignment(Qt::AlignCenter);
        m_pFileTableWidget->setItem(i, 0, pIdItem);

        QTableWidgetItem* pNameItem = new QTableWidgetItem;
        pNameItem->setText(m_strTimingNameList.at(i));
        pNameItem->setTextAlignment(Qt::AlignCenter);
        m_pFileTableWidget->setItem(i, 1, pNameItem);
    }
}

void CTimingCompose::_ClearTableWidget(QTableWidget *pWidget)
{
    m_pTimingNameLineEdit->SetLineEditText("");

    QList<QComboBox *> comboBoxList = pWidget->findChildren<QComboBox *>();
    foreach (QComboBox *pComboBox, comboBoxList)
    {
        disconnect(pComboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotCmdComboBoxChanged(int)));
        delete pComboBox;
        pComboBox = nullptr;
    }

    QList<CLineEdit *> lineEditList = pWidget->findChildren<CLineEdit *>();
    foreach (CLineEdit *pLineEdit, lineEditList)
    {
        delete pLineEdit;
        pLineEdit = nullptr;
    }

    QList<QCheckBox *> checkBoxList = pWidget->findChildren<QCheckBox *>();
    foreach (QCheckBox *pCheckBox, checkBoxList)
    {
        delete pCheckBox;
        pCheckBox = nullptr;
    }

    qDebug()<<"delete timing cmd tablewidget:"<<comboBoxList.size()<<lineEditList.size()<<checkBoxList.size();

    pWidget->setRowCount(0);
    pWidget->clearContents();
}

void CTimingCompose::_SetOneRowData(QTableWidget *pTableWidget, int iRow, const QStringList &oneList)
{
    if(oneList.isEmpty())
        return;
    QString first = oneList.at(0);
    if(first.isEmpty())
        return;
    QString second;
    if(oneList.size() >= 2)
        second = oneList.at(1);
    QString third;
    if(oneList.size() >= 3)
        third = oneList.at(2);
    QString forth;
    if(oneList.size() >= 4)
        forth = oneList.at(3);

    QTableWidgetItem *pIdItem = new QTableWidgetItem;
    pIdItem->setText(QString::number(iRow + 1));
    pIdItem->setTextAlignment(Qt::AlignCenter);
    pTableWidget->setItem(iRow, COL_NUM_ID, pIdItem);

    int iCmdID = first.toInt();
    int iCmdIndex = _GetCmdIndexByID(iCmdID);
    QComboBox *pComboBox = dynamic_cast<QComboBox *>(pTableWidget->cellWidget(iRow, COL_NUM_CMD));
    if(nullptr != pComboBox)
    {
        disconnect(pComboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotCmdComboBoxChanged(int)));
        pComboBox->deleteLater();
    }
    pComboBox= new QComboBox;
    pComboBox->setFixedWidth(350);
    pComboBox->setView(new QListView);
    pComboBox->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    pComboBox->setMaxVisibleItems(10);
    pComboBox->setStyle(QStyleFactory::create("Windows"));
    if(Soft_Auto == m_iSoftType)
        pComboBox->addItems(m_strAutoCmdTextList);
    else
        pComboBox->addItems(m_strExtractCmdTextList);
    pComboBox->setCurrentIndex(iCmdIndex);
    connect(pComboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotCmdComboBoxChanged(int)));
    pTableWidget->setCellWidget(iRow, COL_NUM_CMD, pComboBox);
    pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, nullptr);
    pTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, nullptr);
    pTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, nullptr);

    switch (iCmdID)
    {
    case Method_DELAY:
    case Method_loop:
    case Method_PEXT:
    case Method_PINJ:
    case Method_PETMC:
    case Method_PITEC:
    case Method_PITDC:
    case Method_PFEXT:
    case Method_US_AMP:
    {
        CLineEdit *pLineEdit = new CLineEdit(second);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pLineEdit);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, nullptr);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, nullptr);
        break;
    }
    case Method_jump:
    case Method_wait_signal:
    case Method_HTST:
    {
        QStringList strTextList;
        if(Method_jump == iCmdID || Method_wait_signal == iCmdID)
            strTextList = m_strRelList;
        else if(Method_HTST == iCmdID)
            strTextList = m_strHTOpenList;

        QComboBox *pComboBox1 = new QComboBox;
        pComboBox1->setView(new QListView);
        pComboBox1->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        pComboBox1->setMaxVisibleItems(10);
        pComboBox1->setStyle(QStyleFactory::create("Windows"));
        pComboBox1->addItems(strTextList);
        if (Method_HTST == iCmdID)
        {
            pComboBox1->setCurrentIndex(CPublicConfig::GetInstance()->GetPyroLysisParamIndexById(second.toInt()));
        }
        else
        {
            pComboBox1->setCurrentIndex(second.toInt());
        }

        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pComboBox1);

        CLineEdit *pLineEdit = new CLineEdit(third);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pLineEdit);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, nullptr);
        break;
    }
    case Method_Motor_CMD:
    {
        QStringList strMotorTextIDList = m_strMotorTextIDMap.values();
        int width = pTableWidget->horizontalHeader()->sectionSize(COL_NUM_PARAM1);
        QComboBox *pComboBox1 = new QComboBox;
        pComboBox1->setMinimumSize(width, 50);
        pComboBox1->setView(new QListView);
        pComboBox1->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        pComboBox1->setMaxVisibleItems(10);
        pComboBox1->setStyle(QStyleFactory::create("Windows"));
        pComboBox1->addItems(strMotorTextIDList);
        pComboBox1->setCurrentIndex(m_strMotorTextIDMap.keys().indexOf(second));
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pComboBox1);

        QString strParam;
        if(oneList.size() >= 3)
        {
            QStringList paramList = oneList;
            paramList.pop_front();
            paramList.pop_front();
            strParam = paramList.join(SPLIT_IN_CMD);
        }
        CLineEdit *pLineEdit = new CLineEdit(strParam);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pLineEdit);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, nullptr);
        break;
    }
    case Method_MOTOR_COMPOSE:
    {
        QStringList strDataList = oneList;
        if(strDataList.isEmpty())
            break;
        strDataList.pop_front(); //去掉436

        QStringList allMotorComposeList = strDataList.join(SPLIT_IN_CMD).split(SPLIT_MOTOR);
        for(int i=0; i<allMotorComposeList.size(); i++)
        {
            QStringList oneComposeList = allMotorComposeList.at(i).split(SPLIT_IN_CMD);
            if(oneComposeList.isEmpty())
                continue;

            QString strID = oneComposeList.at(0);
            oneComposeList[0] = m_strMotorTextIDMap.value(strID);
            allMotorComposeList[i] = oneComposeList.join(SPLIT_IN_CMD);
        }
        QString strShowData = allMotorComposeList.join(SPLIT_MOTOR);

        CLineEdit *pLineEdit = new CLineEdit(strShowData);
        connect(pLineEdit, &CLineEdit::SignalPressEvent, this, &CTimingCompose::_SlotMotorSerialComposeShow);

        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pLineEdit);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, nullptr);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, nullptr);
        break;
    }
    case Method_FLLED:
    case Method_FLADC:
    case Method_FLCST:
    case Method_FLMST:
    case Method_HTSP:
    case Method_US_USAMPOPT:
    {
        QStringList strTextList;
        if(Method_FLLED == iCmdID)
            strTextList = m_strLightList;
        else if(Method_FLADC == iCmdID || Method_FLCST == iCmdID || Method_FLMST == iCmdID)
            strTextList = m_strPDList;
        else if(Method_HTSP == iCmdID)
            strTextList = m_strHTCloseList;
        else if(Method_US_USAMPOPT == iCmdID)
            strTextList = m_strUSAmpList;

        QComboBox *pComboBox1 = new QComboBox;
        pComboBox1->setView(new QListView);
        pComboBox1->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        pComboBox1->setMaxVisibleItems(10);
        pComboBox1->setStyle(QStyleFactory::create("Windows"));
        pComboBox1->addItems(strTextList);
        if (Method_HTSP == iCmdID)
        {
            pComboBox1->setCurrentIndex(CPublicConfig::GetInstance()->GetPyroLysisParamIndexById(second.toInt()));
        }
        else
        {
            pComboBox1->setCurrentIndex(second.toInt());
        }

        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pComboBox1);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, nullptr);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, nullptr);
        break;
    }
    case Method_MOVE:
    {
        QComboBox *pComboBox1 = new QComboBox;
        pComboBox1->setView(new QListView);
        pComboBox1->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        pComboBox1->setMaxVisibleItems(10);
        pComboBox1->setStyle(QStyleFactory::create("Windows"));
        pComboBox1->addItems(m_strMotorNameList);
        pComboBox1->setCurrentIndex(second.toInt());
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM1, pComboBox1);

        QComboBox *pComboBox2 = new QComboBox;
        pComboBox2->setView(new QListView);
        pComboBox2->addItems(m_strHomeEndList);
        pComboBox2->setCurrentIndex(third.toInt());
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM2, pComboBox2);

        CLineEdit *pLineEdit = new CLineEdit(forth);
        pTableWidget->setCellWidget(iRow, COL_NUM_PARAM3, pLineEdit);
        break;
    }
    default:
        break;
    }

    QList<QComboBox *> pComboBoxList = pTableWidget->findChildren<QComboBox *>();
    for(int i=0; i<pComboBoxList.size(); i++)
        pComboBoxList.at(i)->installEventFilter(this);

    //QCheckBox *pCheckBox = new QCheckBox;
    //pCheckBox->setChecked(true);
    //pTableWidget->setCellWidget(iRow, COL_NUM_PARAM4, pCheckBox);
}

QString CTimingCompose::_GetOneRowData(QTableWidget *pTableWidget, int iRow)
{
    if(nullptr == pTableWidget || iRow < 0 || iRow > pTableWidget->rowCount())
        return "";

    QStringList strList;
    QComboBox *pComboBox = dynamic_cast<QComboBox *>(pTableWidget->cellWidget(iRow, COL_NUM_CMD));
    if(nullptr == pComboBox)
        return "";

    int iCmdID = _GetCmdIDByIndex(pComboBox->currentIndex());
    strList.push_back(QString::number(iCmdID));

    //电机串行组合特殊处理
    if(Method_MOTOR_COMPOSE == iCmdID)
    {
        CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(pTableWidget->cellWidget(iRow, COL_NUM_PARAM1));
        QString strMotorComposeData = pLineEdit->text();
        for(auto it=m_strMotorTextIDMap.begin(); it!=m_strMotorTextIDMap.end(); it++)
        {
            QString strMotorCmdTextID = it.value();
            QString strMotorCmdID = it.key();
            strMotorComposeData.replace(strMotorCmdTextID, strMotorCmdID);
        }
        strList.push_back(strMotorComposeData);
        QString strData = strList.join(SPLIT_IN_CMD);
        qDebug()<<"时序组合_GetOneRowData,电机串行组合:"<<iRow<<strData;
        return strData;
    }
    //电机特殊处理
    if(Method_Motor_CMD == iCmdID)
    {
        QComboBox *pComboBox = dynamic_cast<QComboBox *>(pTableWidget->cellWidget(iRow, COL_NUM_PARAM1));
        if(pComboBox)
        {
            QString strMotorCmdID = m_strMotorTextIDMap.key(pComboBox->currentText());
            strList.push_back(strMotorCmdID);
        }
        CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(pTableWidget->cellWidget(iRow, COL_NUM_PARAM2));
        if(pLineEdit)
        {
            if(!pLineEdit->text().isEmpty())
                strList.push_back(pLineEdit->text());
        }
        QString strData = strList.join(SPLIT_IN_CMD);
        qDebug()<<"时序组合_GetOneRowData,单个电机:"<<iRow<<strData;
        return strData;
    }
    if(Method_HTST == iCmdID || Method_HTSP  == iCmdID)
    {
        QComboBox *pComboBox1 = dynamic_cast<QComboBox *>(pTableWidget->cellWidget(iRow, COL_NUM_PARAM1));
        // 这里开始改 ， 改完之后改htsp
        if(pComboBox1)
            strList.push_back(QString::number(CPublicConfig::GetInstance()->GetPyroLysisParamId((pComboBox1->currentIndex()))));

        CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(pTableWidget->cellWidget(iRow, COL_NUM_PARAM2));
        if(pLineEdit)
            strList.push_back(pLineEdit->text().trimmed());

        qDebug()<<"时序组合_GetOneRowData,热裂解:"<<iRow<<strList;
        return strList.join(SPLIT_IN_CMD);;
    }

    for(int iColumn=COL_NUM_PARAM1; iColumn<=COL_NUM_PARAM3; iColumn++)
    {
        QWidget *pWidget = pTableWidget->cellWidget(iRow, iColumn);
        if(nullptr == pWidget)
            continue;

        QComboBox *pComboBox = dynamic_cast<QComboBox *>(pWidget);
        if(pComboBox)
            strList.push_back(QString::number(pComboBox->currentIndex()));

        CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(pWidget);
        if(pLineEdit)
            strList.push_back(pLineEdit->text());
    }

    QString strData = strList.join(SPLIT_IN_CMD);
    qDebug()<<"时序组合_GetOneRowData:"<<iRow<<strData;
    return strData;

    switch (iCmdID)
    {
    case Method_DELAY:
    case Method_loop:
        // case Method_MMIX:
    case Method_PEXT:
    case Method_PINJ:
    case Method_PETMC:
    case Method_PITEC:
    case Method_PITDC:
    case Method_PFEXT:
    case Method_US_AMP:
    {
        CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(pTableWidget->cellWidget(iRow, COL_NUM_PARAM1));
        if(pLineEdit)
            strList.push_back(pLineEdit->text().trimmed());
        break;
    }
    case Method_jump:
    case Method_wait_signal:
    case Method_MOTOR_COMPOSE:
    case Method_HTST:
    {
        QComboBox *pComboBox1 = dynamic_cast<QComboBox *>(pTableWidget->cellWidget(iRow, COL_NUM_PARAM1));
        // 这里开始改 ， 改完之后改htsp
        if(pComboBox1)
            strList.push_back(QString::number(CPublicConfig::GetInstance()->GetPyroLysisParamId((pComboBox1->currentIndex()))));

        CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(pTableWidget->cellWidget(iRow, COL_NUM_PARAM2));
        if(pLineEdit)
            strList.push_back(pLineEdit->text().trimmed());
        break;
    }
    case Method_FLLED:
    case Method_FLADC:
    case Method_FLCST:
    case Method_FLMST:
    case Method_HTSP:
    {
        QComboBox *pComboBox1 = dynamic_cast<QComboBox *>(pTableWidget->cellWidget(iRow, COL_NUM_PARAM1));
        if(pComboBox1)
            strList.push_back(QString::number(CPublicConfig::GetInstance()->GetPyroLysisParamId((pComboBox1->currentIndex()))));
        break;
    }
    case Method_MOVE:
    {
        QComboBox *pComboBox1 = dynamic_cast<QComboBox *>(pTableWidget->cellWidget(iRow, COL_NUM_PARAM1));
        if(pComboBox1)
            strList.push_back(QString::number(pComboBox1->currentIndex()));

        QComboBox *pComboBox2 = dynamic_cast<QComboBox *>(pTableWidget->cellWidget(iRow, COL_NUM_PARAM2));
        if(pComboBox2)
            strList.push_back(QString::number(pComboBox2->currentIndex()));

        CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(pTableWidget->cellWidget(iRow, COL_NUM_PARAM3));
        if(pLineEdit)
            strList.push_back(pLineEdit->text().trimmed());
        break;
    }
    default:
        break;
    }

    return strList.join(SPLIT_IN_CMD);
}

void CTimingCompose::_AddOneRow(QTableWidget *pTableWidget)
{
    if(nullptr == pTableWidget)
        return;

    int iRow = pTableWidget->currentRow();
    if(iRow < 0)
        iRow = -1;
    iRow++;

    pTableWidget->insertRow(iRow);
    QStringList strList = {QString::number(Method_MCHK)};
    _SetOneRowData(pTableWidget, iRow, strList);
    ResortTableWidget(pTableWidget);
    pTableWidget->selectRow(iRow);
}

void CTimingCompose::_DelOneRow(QTableWidget *pTableWidget)
{
    if(nullptr == pTableWidget || pTableWidget->currentRow() < 0)
        return;

    int iRow = pTableWidget->currentRow();
    pTableWidget->removeRow(iRow);
    ResortTableWidget(pTableWidget);
}

void CTimingCompose::_MoveUpRow(QTableWidget *pTableWidget)
{
    if(nullptr == pTableWidget || pTableWidget->currentRow() <= 0)
        return;

    int iRow = pTableWidget->currentRow();
    QString strCurrentRowData = _GetOneRowData(pTableWidget, iRow);
    QString strUpRowData = _GetOneRowData(pTableWidget, iRow - 1);
    _SetOneRowData(pTableWidget, iRow - 1, strCurrentRowData.split(SPLIT_IN_CMD));
    _SetOneRowData(pTableWidget, iRow, strUpRowData.split(SPLIT_IN_CMD));
    ResortTableWidget(pTableWidget);
    pTableWidget->selectRow(iRow - 1);
}

void CTimingCompose::_MoveDownRow(QTableWidget *pTableWidget)
{
    if(nullptr == pTableWidget || pTableWidget->currentRow() < 0)
        return;

    int iRow = pTableWidget->currentRow();
    if(iRow + 1 == pTableWidget->rowCount())
        return;

    QString strCurrentRowData = _GetOneRowData(pTableWidget, iRow);
    QString strDownRowData = _GetOneRowData(pTableWidget, iRow + 1);
    _SetOneRowData(pTableWidget, iRow + 1, strCurrentRowData.split(SPLIT_IN_CMD));
    _SetOneRowData(pTableWidget, iRow, strDownRowData.split(SPLIT_IN_CMD));
    ResortTableWidget(pTableWidget);
    pTableWidget->selectRow(iRow + 1);
}

void CTimingCompose::_SaveTabelData(QTableWidget *pTableWidget)
{
    if(nullptr == pTableWidget)
        return;

    QString strTimingName = m_pTimingNameLineEdit->GetLineEditText();
    if(strTimingName.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("请填写时序名称"));
        return;
    }

    QStringList strAllRowDataList;
    int size = pTableWidget->rowCount();
    if(size < 1)
    {
        ShowInformation(this, m_strTipsText, tr("时序表格内容为空"));
        return;
    }
    for(int i=0; i<size; i++)
    {
        QString strOneRowData = _GetOneRowData(pTableWidget, i);
        strAllRowDataList.push_back(strOneRowData);
    }

    QString strTableData = strAllRowDataList.join(SPLIT_BETWEEN_CMD);
    CTimingTecDB::GetInstance().AddOneTiming(strTimingName, "", strTableData);

    if(m_strTimingNameList.contains(strTimingName))
        return;
    m_strTimingNameList.push_back(strTimingName);

    int iRow = m_pFileTableWidget->rowCount();
    m_pFileTableWidget->insertRow(iRow);

    QTableWidgetItem *pIdItem = new QTableWidgetItem;
    pIdItem->setText(QString::number(iRow));
    pIdItem->setTextAlignment(Qt::AlignCenter);
    m_pFileTableWidget->setItem(iRow, 0, pIdItem);

    QTableWidgetItem *pNameItem = new QTableWidgetItem;
    pNameItem->setText(strTimingName);
    pNameItem->setTextAlignment(Qt::AlignCenter);
    m_pFileTableWidget->setItem(iRow, 1, pNameItem);

    ResortTableWidget(m_pFileTableWidget);
    m_pFileTableWidget->selectRow(iRow+1);
}

void CTimingCompose::_StartTest(int iMachineID)
{
    QString strTimingName = m_pTimingNameLineEdit->GetLineEditText();
    QString strTecName = m_pPCRComboBox->GetCurrentText();
    QString strProject = m_pProjectComboBox->GetCurrentText();

    STimingStruct *pStruct = m_stTimingStructList[iMachineID];
    int size = pStruct->pTableWidget->rowCount();
    if(size < 1)
    {
        ShowInformation(this, m_strTipsText, tr("时序表格内容为空"));
        return;
    }

    if(CRunTest::GetInstance()->GetRunInfoStruct(iMachineID).bRunning)
    {
        ShowWarning(this, m_strTipsText, tr("当前机器正在测试，无法重复创建测试"));
        return;
    }

    if(eDeviceReset == pStruct->eStatus)
    {
        ShowWarning(this, m_strTipsText, tr("当前机器正在复位，请稍后重试"));
        return;
    }

    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定开始运行时序吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    QStringList strAllRowDataList;
    for(int i=0; i<size; i++)
    {
        QString strOneRowData = _GetOneRowData(pStruct->pTableWidget, i);
        strAllRowDataList.push_back(strOneRowData);
    }
    QString strTimingData = strAllRowDataList.join(SPLIT_BETWEEN_CMD);

    QDateTime dateTime = QDateTime::currentDateTime();
    QString strCurrentTime = dateTime.toString("yyyyMMddhhmmss");
    QString strCardID = m_pCardIDLineEdit->GetLineEditText();
    if(strCardID.isEmpty())
        strCardID = "C" + strCurrentTime;
    m_pCardIDLineEdit->SetLineEditText(strCardID);
    QString strSampleID = "S" + strCurrentTime;

    if(strCardID.toLocal8Bit().length() > 50)
    {
        ShowInformation(this, m_strTipsText, tr("卡盒ID长度不能大于50"));
        return;
    }

    SRunningInfoStruct &sRunInfo = CRunTest::GetInstance()->GetRunInfoStruct(iMachineID);

    sRunInfo.iTecIndex = GetTecIndex(strTecName);
    sRunInfo.bFactroyTest = true;
    sRunInfo.iRunTimes = m_pAgingLineEdit->text().toInt();
    sRunInfo.strTecName = strTecName;
    sRunInfo.strTimingName = strTimingName;
    sRunInfo.strTimingData = strTimingData;
    sRunInfo.sCardInfo.strCardID = strCardID;
    sRunInfo.sCardInfo.strProject = strProject;
    sRunInfo.sSampleInfo.strSampleID = strSampleID;
    sRunInfo.sSampleInfo.strQCTestModel = "T";
    sRunInfo.sSampleInfo.strOperator = CPublicConfig::GetInstance()->GetLoginUser();
    qDebug()<<QString("%1#工厂模式创建时序测试,项目:%2,TEC:%3,时序:%4,,次数:%5")
              .arg(iMachineID + 1).arg(strProject).arg(strTecName).arg(strTimingName).arg(sRunInfo.iRunTimes);

    CRunTest::GetInstance()->StartTest(iMachineID);
}

void CTimingCompose::_PauseTest()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定暂停运行时序吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    QString strCmd = GetJsonCmdString(Method_pause);
    SendJsonCmd(m_iUiMachineID, Method_pause, strCmd);
}

void CTimingCompose::_StopTest()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定停止运行时序吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    SRunningInfoStruct &sRunInfo = CRunTest::GetInstance()->GetRunInfoStruct(m_iUiMachineID);
    sRunInfo.bRunning = false;

    QString strCmd = GetJsonCmdString(Method_stop);
    SendJsonCmd(m_iUiMachineID, Method_stop, strCmd);
}

void CTimingCompose::_ResumeTest()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定恢复运行时序吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    QString strCmd = GetJsonCmdString(Method_resume);
    SendJsonCmd(m_iUiMachineID, Method_resume, strCmd);
}

void CTimingCompose::_ImportTiming()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("导入时序将会重启，确定导入时序吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    QString strSrcPath = GetUDiskUpdateDir() + "timing.db";
    QDir dir(GetUDiskDir());
    if(!dir.exists())
    {
        ShowInformation(this, QObject::tr("提示"), QObject::tr("请先插入U盘"));
        return;
    }

    if(!QFile::exists(strSrcPath))
    {
        ShowInformation(this, QObject::tr("提示"), QObject::tr("U盘中文件不存在"));
        return;
    }

    QString strDestPath = CPublicConfig::GetInstance()->GetTimingDBPath();
    CopyQFile(strSrcPath, strDestPath);

    if(QFile::exists(strDestPath))
    {
        ShowSuccess(this, tr("提示"), tr("时序导入成功，正在重启"));
        System("reboot");
    }
    else
    {
        ShowError(this, m_strTipsText, tr("时序导入失败"));
    }
}

void CTimingCompose::_ExportTiming()
{
    if(!UDiskExist(this))
        return;

    CreateDir(GetUDiskUpdateDir());
    QString strSrcPath = CPublicConfig::GetInstance()->GetTimingDBPath();
    QString strDestPath = GetUDiskUpdateDir() + "timing.db";
    CopyQFile(strSrcPath, strDestPath);

    bool bExist = QFile::exists(strDestPath);

    ExportEndUmountUSB();

    if(bExist)
        ShowSuccess(this, m_strTipsText, tr("时序导出成功"));
    else
        ShowWarning(this, m_strTipsText, tr("时序导出失败"));
}

void CTimingCompose::_InitTextList()
{
    m_strAutoCmdTextList << tr("泵接大气")
                         << tr("泵接卡盒")
                         << tr("电机复位")
                         << tr("光学滤片切换电机复位")
                         << tr("孔1光学滤片切换到特定位置")
                         << tr("孔2光学滤片切换到特定位置")
                         << tr("光学滤片检光")
                         << tr("光纤电机复位")
                         << tr("光纤电机运动到孔位2")
                         << tr("PCR夹紧电机复位")
                         << tr("PCR夹紧电机夹紧")
                         << tr("光学滤片电机慢速移动到第一个孔位")
                         << tr("光纤电机慢速检测")
                         << tr("顶针阀1复位/松开")
                         << tr("顶针阀1压紧")
                         << tr("顶针阀2复位/松开")
                         << tr("顶针阀2压紧")
                         << tr("刺破电机1复位")
                         << tr("刺破电机1刺破")
                         << tr("刺破电机2复位")
                         << tr("刺破电机2刺破")
                         << tr("刺破电机3复位")
                         << tr("刺破电机3刺破")
                         << tr("气嘴复位/松开")
                         << tr("气嘴压紧")
                         << tr("PCR阀电机松开/复位")
                         << tr("PCR阀电机压紧")
                         << tr("钢珠混匀电机复位")
                         << tr("钢珠混匀电机混匀**圈")
                         << tr("提取组件升降运动电机复位/混匀位")
                         << tr("提取组件升降运动电机到吸附位")
                         << tr("提取组件升降运动电机到加热位")
                         << tr("提取组件水平运动电机复位")
                         << tr("提取组件水平运动电机到加热位")
                         << tr("提取组件水平运动电机到磁珠吸附位")
                         << tr("提取组件水平运动电机到混匀位")
                         << tr("柱塞泵电机复位")
                         << tr("柱塞泵电机抽取**ml")
                         << tr("柱塞泵电机注射**ml")
                         << tr("柱塞泵抽取样本**ml到混合腔")
                         << tr("柱塞泵注射样本**ml到废液腔")
                         << tr("柱塞泵注射样本**ml到PCR腔/检测腔")
                         << tr("走指定步数")
                         << tr("启动PCR")
                         << tr("停止PCR")
                         << tr("等待PCR信号")
                         << tr("开启LED灯")
                         << tr("获取荧光数据")
                         << tr("开启连续采光")
                         << tr("结束连续采光")
                         << tr("启动运动采光")
                         << tr("结束运动采光")
                         << tr("启动加热")
                         << tr("停止加热")
                         << tr("启动超声")
                         << tr("停止超声")
                         << tr("延迟")
                         << tr("循环开始")
                         << tr("循环")
                         << tr("跳转")
                         << tr("开启压力检测")
                         << tr("停止压力检测")
                         << tr("卡盒接大气")
                         << tr("卡盒接泵")
                         << tr("柱塞泵电机快速抽取**ml")
                         << tr("电机串行组合")
                         << tr("设置超声振幅");

    m_strExtractCmdTextList << tr("电机复位")
                            << tr("走指定步数")
                            << tr("启动PCR")
                            << tr("停止PCR")
                            << tr("等待PCR信号")
                            << tr("开启LED灯")
                            << tr("获取荧光数据")
                            << tr("开启连续采光")
                            << tr("结束连续采光")
                            << tr("启动运动采光")
                            << tr("结束运动采光")
                            << tr("启动加热")
                            << tr("停止加热")
                            << tr("启动超声")
                            << tr("停止超声")
                            << tr("延迟")
                            << tr("循环开始")
                            << tr("循环")
                            << tr("跳转")
                            << tr("开启压力检测")
                            << tr("停止压力检测")
                               //  << tr("三通阀2通电")
                               //  << tr("三通阀2断电")
                               //  << tr("柱塞泵电机快速抽取**ml")
                            << tr("电机串行组合")
                            << tr("设置超声振幅")
                            << tr("超声振幅选择")
                            << tr("气嘴1接泵/气嘴2接大气")
                            << tr("气嘴2接泵/气嘴1接大气")
                            << tr("泵接大气")
                            << tr("电机")
                            << tr("时序并行开始")
                            << tr("时序并行结束")
                            << tr("时序串行开始")
                            << tr("时序串行结束");

    m_strParams2List << tr("自定义") << tr("可替换");
    m_strParams3List << tr("自定义") << tr("可替换") << tr("补偿替换");
    m_strParams4List << tr("自定义") << tr("可替换") << tr("复位") << tr("运行");
    m_strParams6List << tr("自定义") << tr("可替换") << tr("复位") << tr("运行") << tr("档位") << tr("替换档位");
    m_strLightCoverList << tr("覆盖(0)") << tr("非覆盖(1)");
    m_strHomeEndList << tr("HOME(0)") << tr("END(1)");

    for(int i=0; i<=10; i++)
        m_strNumber10List.push_back(QString::number(i));
    for(int i=0; i<=4; i++)
        m_strNumber4List.push_back(QString::number(i));

    m_strLightList << tr("两个灯关") << tr("1号灯开") << tr("2号灯开") << tr("1+2灯开");
    m_strPDList << tr("PD1") << tr("PD2") << tr("PD1+PD2");
    m_strUSAmpList << tr("裂解") << tr("洗涤1") << tr("洗涤2") << tr("洗脱");

    m_strHTOpenList = CPublicConfig::GetInstance()->GetPyrolysisStringList(Method_HTST);
    m_strHTCloseList = CPublicConfig::GetInstance()->GetPyrolysisStringList(Method_HTSP);

    m_strRelList << tr("REL(0)") << tr("ABS(1)");
    m_strStopList << tr("硬停") << tr("软停") << tr("硬停补偿") << tr("软停补偿");
    m_strMotorNameList<<CPublicConfig::GetInstance()->GetExtractMotorNameList();

    m_strMotorTextIDMap = CMotorDB::GetInstance().GetCmdIDNameMap();
    //qDebug() << "电机指令说明:" << m_strMotorTextIDMap.size() << m_strMotorTextIDMap;
}

void CTimingCompose::_InitMap()
{
    int index = 0;
    m_strIndexIDMap.insert(index++, Method_valve_on);
    m_strIndexIDMap.insert(index++, Method_valve_off);
    m_strIndexIDMap.insert(index++, Method_SRST);

    for(int i=0; i<39; i++)
        m_strIndexIDMap.insert(index++, Method_OFRST + i);

    m_strIndexIDMap.insert(index++, Method_MOVE);
    m_strIndexIDMap.insert(index++, Method_pcr_start);
    m_strIndexIDMap.insert(index++, Method_pcr_stop);
    m_strIndexIDMap.insert(index++, Method_wait_signal);
    m_strIndexIDMap.insert(index++, Method_FLLED);
    m_strIndexIDMap.insert(index++, Method_FLADC);
    m_strIndexIDMap.insert(index++, Method_FLCST);
    m_strIndexIDMap.insert(index++, Method_FLCSP);
    m_strIndexIDMap.insert(index++, Method_FLMST);
    m_strIndexIDMap.insert(index++, Method_FLMSP);
    m_strIndexIDMap.insert(index++, Method_HTST);
    m_strIndexIDMap.insert(index++, Method_HTSP);
    m_strIndexIDMap.insert(index++, Method_US_USST);
    m_strIndexIDMap.insert(index++, Method_US_USSP);
    m_strIndexIDMap.insert(index++, Method_DELAY);
    m_strIndexIDMap.insert(index++, Method_loop_st);
    m_strIndexIDMap.insert(index++, Method_loop);
    m_strIndexIDMap.insert(index++, Method_jump);
    m_strIndexIDMap.insert(index++, Method_pressure_on);
    m_strIndexIDMap.insert(index++, Method_pressure_stop);
    m_strIndexIDMap.insert(index++, Method_valve2_on);
    m_strIndexIDMap.insert(index++, Method_valve2_off);
    m_strIndexIDMap.insert(index++, Method_PFEXT);
    m_strIndexIDMap.insert(index++, Method_MOTOR_COMPOSE);
    m_strIndexIDMap.insert(index++, Method_US_AMP);
    m_strIndexIDMap.insert(index++, Method_N1CP_N2CA);
    m_strIndexIDMap.insert(index++, Method_N2CP_N1CA);
    m_strIndexIDMap.insert(index++, Method_PumpConAir);

    m_strExtractIndexIDMap = m_strIndexIDMap;
    for(int i=0; i<23; i++)
        m_strExtractIndexIDMap.remove(i+14);

    m_strExtractIndexIDMap.remove(40);
    m_strExtractIndexIDMap.remove(41);
    m_strExtractIndexIDMap.remove(42);

    QList<int> idList = m_strExtractIndexIDMap.values();
    idList << Method_Motor_CMD;
    idList << Method_parallel_start << Method_parallel_end << Method_serial_start << Method_serial_end;

    m_strExtractIndexIDMap.clear();
    for(int i=0; i<idList.size(); i++)
        m_strExtractIndexIDMap.insert(i, idList.at(i));

    index = 0;
    m_strExtractIndexIDMap.clear();
    m_strExtractIndexIDMap.insert(index++, Method_SRST);    //电机复位
    m_strExtractIndexIDMap.insert(index++, Method_MOVE);    //走指定步数
    m_strExtractIndexIDMap.insert(index++, Method_pcr_start);  //启动PCR
    m_strExtractIndexIDMap.insert(index++, Method_pcr_stop);   //停止PCR
    m_strExtractIndexIDMap.insert(index++, Method_wait_signal);//等待PCR信号
    m_strExtractIndexIDMap.insert(index++, Method_FLLED); //开启LED灯
    m_strExtractIndexIDMap.insert(index++, Method_FLADC); //获取荧光数据
    m_strExtractIndexIDMap.insert(index++, Method_FLCST); //开启连续采光
    m_strExtractIndexIDMap.insert(index++, Method_FLCSP); //结束连续采光
    m_strExtractIndexIDMap.insert(index++, Method_FLMST); //启动运动采光
    m_strExtractIndexIDMap.insert(index++, Method_FLMSP); //结束运动采光
    m_strExtractIndexIDMap.insert(index++, Method_HTST);  //启动加热
    m_strExtractIndexIDMap.insert(index++, Method_HTSP);  //停止加热
    m_strExtractIndexIDMap.insert(index++, Method_US_USST); //启动超声
    m_strExtractIndexIDMap.insert(index++, Method_US_USSP); //停止超声
    m_strExtractIndexIDMap.insert(index++, Method_DELAY);   //延时
    m_strExtractIndexIDMap.insert(index++, Method_loop_st); //循环开始
    m_strExtractIndexIDMap.insert(index++, Method_loop);    //循环
    m_strExtractIndexIDMap.insert(index++, Method_jump);    //跳转
    m_strExtractIndexIDMap.insert(index++, Method_pressure_on);    //开启压力检测
    m_strExtractIndexIDMap.insert(index++, Method_pressure_stop);  //停止压力检测
    m_strExtractIndexIDMap.insert(index++, Method_MOTOR_COMPOSE);  //电机串行组合
    m_strExtractIndexIDMap.insert(index++, Method_US_AMP);         //设置超声振幅
    m_strExtractIndexIDMap.insert(index++, Method_US_USAMPOPT);    //超声振幅选择
    m_strExtractIndexIDMap.insert(index++, Method_N1CP_N2CA);      //气嘴1接泵/气嘴2接大气
    m_strExtractIndexIDMap.insert(index++, Method_N2CP_N1CA);      //气嘴2接泵/气嘴1接大气
    m_strExtractIndexIDMap.insert(index++, Method_PumpConAir);     //泵接大气
    m_strExtractIndexIDMap.insert(index++, Method_Motor_CMD);      //电机
    m_strExtractIndexIDMap.insert(index++, Method_parallel_start); //时序并行开始
    m_strExtractIndexIDMap.insert(index++, Method_parallel_end);   //时序并行结束
    m_strExtractIndexIDMap.insert(index++, Method_serial_start);   //时序串行开始
    m_strExtractIndexIDMap.insert(index++, Method_serial_end);     //时序串行结束

    //qDebug()<<"全自动map"<<m_strIndexIDMap;
    //qDebug()<<"全自动map value list:"<<m_strIndexIDMap.values();
    //qDebug()<<"提取工装map"<<m_strExtractIndexIDMap;
    //qDebug()<<"提取工装map value list:"<<m_strExtractIndexIDMap.values();

}

int CTimingCompose::_GetCmdIDByIndex(int index)
{
    if(Soft_Auto == m_iSoftType)
        return m_strIndexIDMap.value(index);
    else
        return m_strExtractIndexIDMap.value(index);
}

int CTimingCompose::_GetCmdIndexByID(int iID)
{
    if(Soft_Auto == m_iSoftType)
        return m_strIndexIDMap.key(iID);
    else
        return m_strExtractIndexIDMap.key(iID);
}

void CTimingCompose::_InitWidget()
{
    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(80, 50);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMachineComboBoxChanged(int)));

    m_pTimingNameLineEdit = new CLabelLineEdit(tr("时序名称:"), "");
    m_pTimingNameLineEdit->SetLineEditFixedSize(180, 50);
    connect(m_pTimingNameLineEdit, &CLabelLineEdit::SignalTextChanged, this, &CTimingCompose::_SlotTimingNameTextChanged);

    m_pCardIDLineEdit = new CLabelLineEdit(tr("卡盒ID:"), "");
    m_pCardIDLineEdit->SetLineEditFixedSize(180, 50);
    connect(m_pCardIDLineEdit, &CLabelLineEdit::SignalTextChanged, this, &CTimingCompose::_SlotCardIDTextChanged);

    m_pPCRComboBox = new CLabelComboBox(tr("PCR时序:"));
    m_pPCRComboBox->SetComboBoxFixedSize(180, 50);
    QStringList strTecList = CTimingTecDB::GetInstance().GetTecNameList();
    strTecList.push_front("");
    m_pPCRComboBox->SetComboBoxList(strTecList);
    connect(m_pPCRComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotPCRIndexChanged(int)));

    QStringList strProjectList = CLotInfoDB::GetInstance()->GetAllProjectShowName();
    m_pProjectComboBox = new CLabelComboBox(tr("项目:"), strProjectList);
    m_pProjectComboBox->SetComboBoxFixedSize(300, 50);
    connect(m_pProjectComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotProjectIndexChanged(int)));

    m_pStackedWidget = new QStackedWidget;
    for(int i=0; i<gk_iMachineCount; i++)
    {
        QTableWidget *pTable = _CreateCmdTableWidget();
        m_stTimingStructList[i]->pTableWidget = pTable;
        m_pStackedWidget->addWidget(pTable);
    }

    QStringList strBtnTextList = {tr("加一行"), tr("减一行"), tr("上移"), tr("下移"), tr("保存"),
                                  tr("清除表格"), tr("开始"), tr("暂停"), tr("停止"), tr("恢复"),
                                  tr("导入时序"), tr("导出时序")};
    for(int i=0; i<strBtnTextList.size(); i++)
    {
        QPushButton *pBtn = new QPushButton(strBtnTextList.at(i));
        pBtn->setProperty("index", i);
        pBtn->setFixedSize(110, 50);
        m_pBtnList.push_back(pBtn);
        connect(pBtn, &QPushButton::clicked, this, &CTimingCompose::_SlotListBtn);
    }

    if(eLanguage_German == gk_iLanguage)
    {
        m_pBtnList.first()->setFixedSize(130, 50);
        m_pBtnList.last()->setFixedSize(130, 50);
    }

    int iWidth = 130;
    int iHeight = 50;

    QStringList strFileList = {tr("序号"), tr("时序")};
    m_pFileTableWidget = new QTableWidget;
    m_pFileTableWidget->setFixedWidth(2 * iWidth + 10);
    m_pFileTableWidget->setColumnCount(strFileList.size());
    m_pFileTableWidget->setHorizontalHeaderLabels(strFileList);

    QHeaderView* pFileVerticalHeader = m_pFileTableWidget->verticalHeader();
    pFileVerticalHeader->setDefaultSectionSize(50);
    QHeaderView* pFileHorizontalHeader = m_pFileTableWidget->horizontalHeader();
    pFileHorizontalHeader->resizeSection(0, 70);
    pFileHorizontalHeader->resizeSection(1, 150);
    pFileHorizontalHeader->setStretchLastSection(true);

    m_pFileTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pFileTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pFileTableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_pFileTableWidget->setShowGrid(true);

    m_pLoadBtn = new QPushButton(tr("加载"));
    m_pLoadBtn->setFixedSize(iWidth, iHeight);
    connect(m_pLoadBtn, &QPushButton::clicked, this, &CTimingCompose::_SlotLoadBtn);

    m_pDelBtn = new QPushButton(tr("删除"));
    m_pDelBtn->setFixedSize(iWidth, iHeight);
    connect(m_pDelBtn, &QPushButton::clicked, this, &CTimingCompose::_SlotDelBtn);

    m_pReleaseBtn = new QPushButton(tr("松开卡盒"));
    m_pReleaseBtn->setFixedSize(iWidth, iHeight);
    connect(m_pReleaseBtn, &QPushButton::clicked, this, &CTimingCompose::_SlotReleaseBtn);

    m_pPressedBtn = new QPushButton(tr("夹紧卡盒"));
    m_pPressedBtn->setFixedSize(iWidth, iHeight);
    connect(m_pPressedBtn, &QPushButton::clicked, this, &CTimingCompose::_SlotPressBtn);

    m_pCopyBtn = new QPushButton(tr("行复制"));
    m_pCopyBtn->setFixedSize(iWidth, iHeight);
    connect(m_pCopyBtn, &QPushButton::clicked, this, &CTimingCompose::_SlotCopyBtn);

    m_pCopyLineEidt = new CLineEdit;
    m_pCopyLineEidt->setFixedSize(iWidth, iHeight);

    m_pAgingLabel = new QLabel(tr("老化剩余:"));
    m_pAgingLabel->setFixedSize(iWidth, iHeight);

    m_pAgingLineEdit = new CLineEdit("0");
    m_pAgingLineEdit->setFixedSize(iWidth, iHeight);
    connect(m_pAgingLineEdit, &QLineEdit::textChanged, this, &CTimingCompose::_SlotRunTimesLineEditTextChanged);

    m_pResetCheckBox = new QCheckBox(tr("结束自动复位"));
    m_pResetCheckBox->setChecked(true);
    m_pResetCheckBox->setLayoutDirection(Qt::LeftToRight);
    connect(m_pResetCheckBox, &QCheckBox::clicked, this, &CTimingCompose::_SlotResetCheckBox);
}

void CTimingCompose::_InitLayout()
{
    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(10);
    pTopLayout->addWidget(m_pMachineComboBox);
    pTopLayout->addWidget(m_pTimingNameLineEdit);
    pTopLayout->addWidget(m_pCardIDLineEdit);
    pTopLayout->addWidget(m_pPCRComboBox);
    pTopLayout->addWidget(m_pProjectComboBox);
    pTopLayout->addStretch(1);

    QGridLayout *pGridLayout = new QGridLayout;
    pGridLayout->setMargin(0);
    pGridLayout->setVerticalSpacing(10);
    pGridLayout->setHorizontalSpacing(10);
    pGridLayout->addWidget(m_pLoadBtn, 0, 0);
    pGridLayout->addWidget(m_pDelBtn, 0, 1);
    pGridLayout->addWidget(m_pReleaseBtn, 1, 0);
    pGridLayout->addWidget(m_pPressedBtn, 1, 1);
    pGridLayout->addWidget(m_pCopyBtn, 2, 0);
    pGridLayout->addWidget(m_pCopyLineEidt, 2, 1);
    pGridLayout->addWidget(m_pAgingLabel, 3, 0);
    pGridLayout->addWidget(m_pAgingLineEdit, 3, 1);
    pGridLayout->addWidget(m_pResetCheckBox, 4, 0, 1, 2);

    QVBoxLayout *pRightLayout = new QVBoxLayout;
    pRightLayout->setMargin(0);
    pRightLayout->setSpacing(0);
    pRightLayout->addWidget(m_pFileTableWidget);
    pRightLayout->addSpacing(10);
    pRightLayout->addLayout(pGridLayout);

    QHBoxLayout *pMidLayout = new QHBoxLayout;
    pMidLayout->setMargin(0);
    pMidLayout->setSpacing(0);
    pMidLayout->addWidget(m_pStackedWidget);
    pMidLayout->addSpacing(10);
    pMidLayout->addLayout(pRightLayout);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(10);
    for(int i=0; i<m_pBtnList.size(); i++)
        pBtnLayout->addWidget(m_pBtnList.at(i));
    pBtnLayout->addStretch(1);

    QVBoxLayout *pMainLayout = new QVBoxLayout;
    pMainLayout->setMargin(0);
    pMainLayout->setSpacing(10);
    pMainLayout->addSpacing(10);
    pMainLayout->addLayout(pTopLayout);
    pMainLayout->addLayout(pMidLayout);
    pMainLayout->addLayout(pBtnLayout);
    this->setLayout(pMainLayout);
}

QTableWidget *CTimingCompose::_CreateCmdTableWidget()
{
    QStringList strList;
    strList << tr("序号") << tr("命令") << tr("参数1") << tr("参数2") << tr("参数3");

    QTableWidget *pCmdWidget = new QTableWidget;
    pCmdWidget->setColumnCount(strList.size());
    pCmdWidget->setHorizontalHeaderLabels(strList);

    QHeaderView* pVerticalHeader = pCmdWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(50);
    QHeaderView* pHorizontalHeader = pCmdWidget->horizontalHeader();
    pHorizontalHeader->resizeSection(0, 70);
    pHorizontalHeader->resizeSection(1, 350);
    pHorizontalHeader->resizeSection(3, 100);
    pHorizontalHeader->resizeSection(4, 100);
    pHorizontalHeader->setSectionResizeMode(2, QHeaderView::Stretch);

    pCmdWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    pCmdWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    pCmdWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    pCmdWidget->setShowGrid(true);
    pCmdWidget->setFocusPolicy(Qt::NoFocus);

    QString strQSS = "QLineEdit{border-radius: 0px; border: 1px solid #CAD2DC; font-size: 20px;}"
                     "QComboBox{border-radius: 0px; border: 1px solid #CAD2DC; font-size: 20px;}"
                     "QCheckBox::indicator{width: 35px; height: 35px; subcontrol-position:center  center;}";
    pCmdWidget->setStyleSheet(strQSS);

    return pCmdWidget;
}
