#include "CHrmWidget.h"
#include <QTime>
#include <QDebug>
#include <QVBoxLayout>
#include "CHrmCurve.h"
#include "CHrmTiming.h"
#include "CHrmCalibrate.h"

CHrmWidget::CHrmWidget(QWidget *parent) : QWidget(parent)
{
    _InitWidget();
}

CHrmWidget::~CHrmWidget()
{

}

void CHrmWidget::_SlotTitleChanged(int index)
{
    m_pStackedWidget->setCurrentIndex(index);
}

void CHrmWidget::_InitWidget()
{
    QStringList strList = {tr("曲线图"), tr("时序表"), tr("温度校准")};
    m_pHBtnTitle = new CHBtnTitleWidget(strList);
    m_pHBtnTitle->SetTitleIndex(0);
    connect(m_pHBtnTitle, &CHBtnTitleWidget::SignalTitleChanged, this, &CHrmWidget::_SlotTitleChanged);

    m_pHrmCurve = new CHrmCurve;
    m_pHrmTiming = new CHrmTiming;
    m_pHrmCalibrate = new CHrmCalibrate;
    connect(m_pHrmTiming, &CHrmTiming::SignalStartPCR, m_pHrmCurve, &CHrmCurve::SlotClearData);

    m_pStackedWidget = new QStackedWidget;
    m_pStackedWidget->addWidget(m_pHrmCurve);
    m_pStackedWidget->addWidget(m_pHrmTiming);
    m_pStackedWidget->addWidget(m_pHrmCalibrate);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pHBtnTitle);
    pLayout->addSpacing(20);
    pLayout->addWidget(m_pStackedWidget);
    this->setLayout(pLayout);
}
