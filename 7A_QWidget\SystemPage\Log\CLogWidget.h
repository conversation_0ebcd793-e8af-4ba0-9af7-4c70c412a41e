#ifndef CLOGWIDGET_H
#define CLOGWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-15
  * Description: 日志管理
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QStackedWidget>

#include "CFaultLogWidget.h"
#include "COperationLogWidget.h"
#include "CSystemLogWidget.h"
#include "SystemPage/CSysFirstTitleWidget.h"
#include "SystemPage/CSysSecondTitleWidget.h"

class CLogWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CLogWidget(QWidget *parent = nullptr);

signals:
    void SignalReturn();

private slots:
    void _SlotTitleChanged(int iTitle);

private:
    void _InitWidget();
    void _InitLayout();

private:
    CSysFirstTitleWidget *m_pCSysTtileLabelWidget;
    QLabel *m_pBackgroundLabel;

    CSysSecondTitleWidget *m_pCSysSecondTitleWidget;
    QStackedWidget *m_pStackedWidget;

    CFaultLogWidget *m_pCFaultLogWidget;
    COperationLogWidget *m_pCOperationLogWidget;
    CSystemLogWidget *m_pCSystemLogWidget;
};

#endif // CLOGWIDGET_H
