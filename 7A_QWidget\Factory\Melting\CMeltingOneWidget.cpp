#include "CMeltingOneWidget.h"
#include "CHistoryDB.h"
#include "sparamfun.h"
#include "CMessageBox.h"
#include "PublicParams.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "qcustomplot.h"
#include "CRunTest.h"
#include "CProjectDB.h"
#include "CLotInfoDB.h"
#include "CCalculateThread.h"
#include "CReadWriteXlsxThread.h"
#include "CPdfHelper.h"
#include "CFtpDB.h"

CMeltingOneWidget::CMeltingOneWidget(int iMachineID, QWidget *parent)
    : QWidget(parent)
    , m_iMachineID(iMachineID)
{
    m_bShow = false;
    m_bReplot = false;
    m_bHasCalcResult = false;

    for(int i=0; i<gk_iHoleCount * gk_iBGYRCount; i++)
    {
        QMap<double, double> map;
        m_dRawFLDataList.push_back(map);
        m_dDerFLDataList.push_back(map);
    }

    _InitWidget();
    _InitLayout();
}

CMeltingOneWidget::~CMeltingOneWidget()
{

}

void CMeltingOneWidget::SetCardID(const QString &strCardID)
{
    m_strNewCardID = strCardID;
    int hole  = m_pHoleComboBox->GetCurrentIndex();
    _UpdateCalcParam(hole);
}

void CMeltingOneWidget::ClearData()
{
    m_bHasCalcResult = false;
    m_dTempVec.clear();
    for(int i=0; i<m_dRawFLDataList.size(); i++)
    {
        m_dRawFLDataList[i].clear();
        m_dDerFLDataList[i].clear();
    }


    if(!m_pTest->isChecked())
        return;

    QVector<double> x, y;
    for(int i=0; i<gk_iBGYRCount; i++)
    {
        m_pCustomPlot->graph(i)->setVisible(true);
        m_pCustomPlot->graph(i)->setData(x, y);
        m_pCustomPlot->legend->item(i)->setVisible(true);
    }

    for(const auto& item :  m_pDotLineFirstList)
    {
         item->setVisible(false);
    }
    for(const auto& item : m_pDotLineSecondList)
    {
        item->setVisible(false);
    }


    m_pCustomPlot->replot();
}

void CMeltingOneWidget::InitTestInfo()
{

}

void CMeltingOneWidget::ParseFLCmd(const QVariant &qVarData)
{
    QString strFLString = qVarData.toString();
    QStringList strFLList = strFLString.split(SPLIT_BETWEEN_CMD);
    int size = strFLList.size();
    if(size < 2)
        return;

    QStringList strCycleTempList = strFLList.at(0).split(SPLIT_IN_CMD);
    if(strCycleTempList.size() < 2)
        return;
    double dTemp = strCycleTempList.at(1).toDouble();
    if(0 == dTemp) //全流程数据
        return;
    qDebug()<<Q_FUNC_INFO<<"熔解荧光数据:"<<strFLString;

    int iCycle = strCycleTempList.at(0).toInt();
    if(0 == iCycle)
    {
        m_pCustomPlot->xAxis->setRange(0, 100);
        m_pCustomPlot->yAxis->setRange(0, 2000);
    }
    // 感觉这里数据结构没设置好！ 可以保存再结构体中使温度和荧光绑定一起！
    m_dTempVec.push_back(dTemp);

    QStringList strHole1List = strFLList.at(1).split(SPLIT_IN_CMD);
    if(5 == strHole1List.size())
    {
        for(int i=0; i<gk_iBGYRCount; i++)
        {
            QMap<double, double> &rawMap = m_dRawFLDataList[i];
            if(rawMap.contains(iCycle))
            {
                qDebug()<<Q_FUNC_INFO<<"fl data循环数重复:"<<iCycle;
            }


            // 保存原始数据再m_dRawFLDataList中
            rawMap[iCycle] = strHole1List.at(i + 1).toDouble();


            //进行实时导数，有没有必要，可以有！ 点击倒数就显示导数
            QMap<double, double> &derMap = m_dDerFLDataList[i];
            derMap[iCycle] = _GetDerivatePoint(m_dTempVec, rawMap.values());

        }
    }
    if(3 == size)
    {
        QStringList strHole2List = strFLList.at(2).split(SPLIT_IN_CMD);
        if(5 == strHole2List.size())
        {
            for(int i=0; i<gk_iBGYRCount; i++)
            {
                QMap<double, double> &rawMap = m_dRawFLDataList[i + 4];
                rawMap[iCycle] = strHole2List.at(i + 1).toDouble();

                //导数
                QMap<double, double> &derMap = m_dDerFLDataList[i + 4];
                derMap[iCycle] = _GetDerivatePoint(m_dTempVec, rawMap.values());
            }
        }
    }

    emit CPublicConfig::GetInstance()->SignalUpdateMeltingFLData(m_iMachineID,QList<double>::fromVector(m_dTempVec),m_dRawFLDataList);

    // 当前测试的数据
    if(m_pTest->isChecked())
    {
        int iHole = m_pHoleComboBox->GetCurrentIndex();
        _ShowCurrentHoleFLData(iHole);
    }
}

void CMeltingOneWidget::ParsePCRSignalCmd(const QVariant &qVarData)
{
    QVariantList qVarList = qVarData.toList();
    if(qVarList.isEmpty())
        return;

    int iSignal = qVarList.at(0).toInt();
    switch (iSignal)
    {
    case 0:
        // PCR运行完成;
        break;
    case 1:
        // PCR光学采样触发，每一轮均有此条
        break;
    case 2:
        // PCR采样循环结束，开始计算CT
        break;
    case 3:
        // HRM熔解光学采样触发，每一轮均有此条
        break;
    case 4:
        // HRM熔解光学采样结束
        RUN_LOG(QString("%1#HRM熔解光学采样结束").arg(m_iMachineID + 1));
        break;
    case 5:
        // PCR采样循环开始
        break;
    case 6:
        // HRM采样循环开始,起始温度,间隔
        break;
    default:
        break;
    }
}

void CMeltingOneWidget::ParseStartCmd(int iResult)
{
    Q_UNUSED(iResult);
    _CalcTmRmResultValue(iResult);

    _SaveMelting2Xlsx();
}

void CMeltingOneWidget::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;
    if(m_bReplot)
    {
        m_bReplot = false;
        m_pCustomPlot->replot();
    }

    QWidget::showEvent(pEvent);
}

void CMeltingOneWidget::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;
    QWidget::hideEvent(pEvent);
}

void CMeltingOneWidget::_SlotSetXYRange(const QStringList &strList)
{
    if(4 != strList.size())
        return;

    m_pCustomPlot->xAxis->setRange(strList.at(0).toDouble(), strList.at(1).toDouble());
    m_pCustomPlot->yAxis->setRange(strList.at(2).toDouble(), strList.at(3).toDouble());
    m_pCustomPlot->replot();
}

void CMeltingOneWidget::_SlotHistoryComboBoxChanged(int index)
{
    Q_UNUSED(index);
}

void CMeltingOneWidget::_SlotHoleComboBoxChanged(int iHole)
{
    Q_UNUSED(iHole);    
    int hole  = m_pHoleComboBox->GetCurrentIndex();
    _UpdateCalcParam(hole);
}

void CMeltingOneWidget::ClearLineEidt()
{
    // 清楚结果，要清楚阈值嘛？
    // 不需要
    for(int i = 0; i < m_pLineEditList.size();i++)
    {
        if(i%5 != 0)
        {
            m_pLineEditList.at(i)->setText("");
        }
    }
    m_pResultLineEdit->setPlainText("");
    // 清空阈值线
    for(const auto& item :  m_pDotLineFirstList)
    {
         item->setVisible(false);
    }
    for(const auto& item : m_pDotLineSecondList)
    {
        item->setVisible(false);
    }
}

void CMeltingOneWidget::_SlotTestChecked(bool bChecked)
{
    // 点击Text，显示测试实时数据，这个时候return;

    if(!bChecked)
        return;


    // 取消显示测试数据，显示测试卡盒ID数据
    emit SignalShowCurrentTestCardID(m_iMachineID);


    //填充 计算结果
    /*
    int size = qMin(m_pLineEditList.size(), m_dCTInfoList.size());
    for(int i=0; i<size; i++)
        m_pLineEditList.at(i)->setText(QString::number(m_dCTInfoList.at(i)));
    */

    _UpdateCurve();
    //_ShowHoleCurrentFLData(m_pHoleComboBox->currentIndex());



    //m_pDerCheckBox->setChecked(!bChecked);
}

void CMeltingOneWidget::_SlotYmRmChecked(bool bChecked)
{
    if(bChecked)
    {
        // 显示ym
        // 改掉标签
        m_pLabelList.at(3)->setText("Ym1");
        m_pLabelList.at(5)->setText("Ym2");
        // 改一下值
        // 改一下值
    }
    else
    {
        // 显示Rm
        m_pLabelList.at(3)->setText("Rm1");
        m_pLabelList.at(5)->setText("Rm2");
    }
    _UpdateCurve();
}

void CMeltingOneWidget::_SlotShowBtn()
{
    m_pTest->setChecked(false);
    _UpdateCurve();
}

void CMeltingOneWidget::_SlotSavePdfBtn()
{
    int iHole = m_pHoleComboBox->GetCurrentIndex();
    QString TestTime;
    QString strCardID;
    int lastPlusIndex = m_strNewCardID.lastIndexOf('+');

    if (lastPlusIndex == -1)
    {
        return;
    }
    TestTime = m_strNewCardID.mid(lastPlusIndex + 1);
    if (TestTime.length() < 14) {
        return;
    }

    QString year = TestTime.left(4);
    QString month = TestTime.mid(4, 2);
    QString day = TestTime.mid(6, 2);
    QString hour = TestTime.mid(8, 2);
    QString minute = TestTime.mid(10, 2);
    QString second = TestTime.mid(12, 2);
    TestTime = year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second;

    strCardID = m_strNewCardID.left(lastPlusIndex);
    QList<double> dTempList;

    SResultInfoStruct sResult;
    if(!CProjectDB::GetInstance()->GetHistoryData(strCardID,TestTime,sResult))
    {
        return;
    }



    QString strBaseName = QString("%1_%2#_%3_%4_%5")
            .arg(sResult.strTestTime.remove(" ").remove("-").remove(":"))
            .arg(sResult.iMachineID + 1)
            .arg(DeleteSpecialCharacters(sResult.strSampleID))
            .arg(DeleteSpecialCharacters(sResult.strCardID))
            .arg(DeleteSpecialCharacters(sResult.strProjectName));
    strBaseName.remove("\\").remove("/").remove(":").remove("*").remove("?");
    strBaseName.remove("\"").remove("<").remove(">").remove("|").remove(" ");
    QString strPDFName = CPublicConfig::GetInstance()->GetPdfDir() + strBaseName + QString("_Review_hole%1").arg(iHole+1)+".pdf";;

    CPdfHelper pdfHelper;
    pdfHelper.SetPdfName(strPDFName);

    pdfHelper.WriteTextToPdf(QString("样本编号:%1").arg(sResult.strSampleID));
    pdfHelper.WriteTextToPdf(QString("试剂卡编号:%1").arg(sResult.strCardID));
    pdfHelper.WriteTextToPdf(QString("测试项目:%1").arg(sResult.strProjectName));
    pdfHelper.WriteTextToPdf(QString("开始时间:%1").arg(TestTime));
    pdfHelper.WriteTextToPdf(QString("通道孔:%1#").arg(iHole+1));
    pdfHelper.WriteTextToPdf(" ");

    QPixmap QTemPixmap = this->grab(this->rect());
    pdfHelper.InsertPictureToPdf(QTemPixmap);
    pdfHelper.EndPainter();
    CFtpDB::GetInstance()->AddFtpUploadFile(strPDFName);
}

void CMeltingOneWidget::_UpdateCurve()
{

    // 清空结果
    ClearLineEidt();

    QVector<double> x, y;
    for(int i=0; i<4; i++)
        m_pCustomPlot->graph(i)->setData(x, y);

    // 荧光通道
    int iColor = m_pColorComboBox->currentIndex();

    //ALL  显示四个荧光通道
    if(gk_iBGYRCount == iColor)
    {
        for(int i=0; i<gk_iBGYRCount; i++)
        {
            m_pCustomPlot->graph(i)->setVisible(true);
            m_pCustomPlot->legend->item(i)->setVisible(true);
        }
    }
    else
    {
        for(int i=0; i<gk_iBGYRCount; i++)
        {
            bool bShow = (iColor == i) ? true : false;
            m_pCustomPlot->graph(i)->setVisible(bShow);
            m_pCustomPlot->legend->item(i)->setVisible(bShow);
        }
    }


    // 获取当前孔道
    int iHole = m_pHoleComboBox->GetCurrentIndex();
    // 历史或者当前测试数据
    bool bTest = m_pTest->isChecked();
    int iDataType = m_pDataDisplayType->currentIndex();

    if(bTest)
    {
        int iTempSize = m_dTempVec.size();
        if(iDataType == 2) //求导少一个点
            iTempSize -= 1;

        QVector<double> dTempVec;
        for(int i=0; i<iTempSize; i++)
            dTempVec.push_back(m_dTempVec.at(i) / 100.0);

        QList<double> dFLList, dYList;
        for(int i=0; i<gk_iBGYRCount; i++)
        {
            if(iDataType == 0)
            {
                dFLList = m_dRawFLDataList.at(i + iHole * 4).values();
            }
            else if(iDataType == 1)
            {
                // 先实现固定系数平滑吧！
                dFLList = m_MeltingCalc.moving_average(m_dRawFLDataList.at(i + iHole * 4).values(),3);
            }
            else if(iDataType == 2)
            {
                dFLList = m_dDerFLDataList.at(i + iHole * 4).values();
            }

            dYList.append(dFLList);
            m_pCustomPlot->graph(i)->setData(dTempVec, dFLList.toVector());
        }
        _SetPlotXYRange(dTempVec, dYList.toVector());
        m_pCustomPlot->replot();
    }
    else// 历史数据
    {

        QString strCardID = m_strNewCardID;
        QList<double> dTempList;
        // 历史温度数据
        CHistoryDB::GetInstance()->GetMeltingDoubleData(strCardID, dTempList);

        _MeltingCalc(dTempList);        
    }
}

void CMeltingOneWidget:: _MeltingCalc(QList<double>& dTempVec)
{
    // 历史数据显示，计算结果，显示结果!
    if(dTempVec.isEmpty())
    {
        return;
    }
    QList<double> realTempVec;
    for (int i = 0;i < dTempVec.size(); i++)
    {
        realTempVec.push_back(dTempVec.at(i));
    }

    int iHole = m_pHoleComboBox->GetCurrentIndex();
    int iColor = m_pColorComboBox->currentIndex();
    QList<QString> strList{"0","0","0","0"};
    // 一个荧光通道显示一个荧光通道结果；荧光数据得显示单个荧光数据！
    if(iColor < gk_iBGYRCount)
    {
        QString strFLID = QString("%1_%2-%3").arg(m_strNewCardID).arg(iHole).arg(gk_strColorNameList.at(iColor));
        QList<double> dFLList;
        CHistoryDB::GetInstance()->GetMeltingDoubleData(strFLID, dFLList);
        // 计算并 显示结果
        _ShowResult(realTempVec,dFLList,iColor);
        strList[iColor] = m_MeltingCalc.getTmRmStrResult();
        _SetPlotXYRange(realTempVec.toVector(), dFLList.toVector());
        m_pCustomPlot->replot();
        return;
    }
    QList<double> dYList;
    QString strDebugOutput;
    for(int i=0; i<gk_iBGYRCount; i++)
    {
        QString strFLID = QString("%1_%2-%3").arg(m_strNewCardID).arg(iHole).arg(gk_strColorNameList.at(i));
        QList<double> dFLList;
        CHistoryDB::GetInstance()->GetMeltingDoubleData(strFLID, dFLList);
        _ShowResult(realTempVec,dFLList,i);
        dYList.append(dFLList);
        strList[i] = m_MeltingCalc.getTmRmStrResult();
        QString strDebugOutPut = m_MeltingCalc.getDebugOutput();
        QString outParam = "    %1通道: ";
        strDebugOutput += outParam.arg(gk_strColorNameList.at(i));
        strDebugOutput += strDebugOutPut;
    }
    // 结果保存
    // 孔道 iHole ，通道 i 或者color
    _updateResult(iHole, strList);
    m_pResultLineEdit->setPlainText(strDebugOutput);
    _SetPlotXYRange(realTempVec.toVector(), dYList.toVector());
    m_pCustomPlot->replot();
}

void CMeltingOneWidget::_updateResult(int nHole,const QList<QString>& strInfoList)
{
    QString TestTime;
    QString strCardID;
    int lastPlusIndex = m_strNewCardID.lastIndexOf('+');
    if (lastPlusIndex == -1)
    {
        return;
    }
    TestTime = m_strNewCardID.mid(lastPlusIndex + 1);
    if (TestTime.length() < 14) {
        return;
    }

    QString year = TestTime.left(4);
    QString month = TestTime.mid(4, 2);
    QString day = TestTime.mid(6, 2);
    QString hour = TestTime.mid(8, 2);
    QString minute = TestTime.mid(10, 2);
    QString second = TestTime.mid(12, 2);
    TestTime = year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second;

    strCardID = m_strNewCardID.left(lastPlusIndex);
    QList<double> dTempList;

    SResultInfoStruct sResult;
    if(!CProjectDB::GetInstance()->GetHistoryData(strCardID,TestTime,sResult))
    {
        return;
    }
    QList<QString> strMeltingList = sResult.strMeltingInfo.split(";");
    QList<QString> strMeltingReviewList = sResult.strMeltingInfo_Review.split(";");
    QString strHrmResult = sResult.strHrmResult;
    QString strHrmResultReview = sResult.strHrmResult_Review;


    SLotInfoStruct sLotInfo;
    CLotInfoDB::GetInstance()->GetLotInfoByShowName(sResult.strProjectName, sLotInfo);
    QStringList strThresholdList = sLotInfo.strWildTypeTmValue.split(";");
    QStringList strTempRangeValue =  sLotInfo.strTempRangeValue.split(";");

    bool bReview = sResult.strReview == "y";
    QList<QString>* pStrMeltingInfo = bReview ? &strMeltingReviewList : &strMeltingList;
    QString* pStrHrmResultInfo = bReview ? &strHrmResultReview : &strHrmResult;

    if((* pStrMeltingInfo).size() < 8 || nHole > 1)
    {
        return;
    }
    for (int i = 0; i < strInfoList.size(); i++)
    {
        if(strInfoList.at(i) != "0")
        {
            int nIndex = nHole*4 + i;
            (*pStrMeltingInfo)[nIndex] = strInfoList.at(i);
        }
    }
    QStringList strMeltinfoList = *pStrMeltingInfo;
    QStringList strHoleNameList = sLotInfo.strCurveName.split(";");
    QStringList strTmVildList = sLotInfo.strWildTypeTmValue.split(";");
    QStringList strTmRangeList = sLotInfo.strTempRangeValue.split(";");
    QStringList strCtInfoList = sResult.strCTInfo.split(";");
    QStringList strResultList = sResult.strResult.split(";");

    *pStrHrmResultInfo = GetMeltingResult(sLotInfo.strProjectShowName,strHoleNameList,strCtInfoList
                                   ,strResultList,strMeltinfoList,strTmVildList,strTmRangeList);

    sResult.strReview = "y";
    sResult.strHrmResult_Review = *pStrHrmResultInfo;
    sResult.strMeltingInfo_Review = (*pStrMeltingInfo).join(";");
    CProjectDB::GetInstance()->UpdateHistoryReviewData(sResult);
}

void CMeltingOneWidget::_ShowResult(QList<double> &dTempVec, QList<double> &dFLList, int nColor)
{
    // 取幅度阈值
    QString strAmplitudethreshold = m_pAmplitudethreshold->GetLineEditText();
    QString strTmThreshould;
    // 取阈值
    if(nColor*5 < m_pLineEditList.size())
    {
        strTmThreshould = m_pLineEditList.at(nColor*5)->text();
    }
    QString strTmRange;
    if(nColor < m_CComboLineEditList.size())
    {
        strTmRange = m_CComboLineEditList.at(nColor)->getTmRangeText();
    }
    m_MeltingCalc.meltingCalc(dTempVec,dFLList,QString("%1;%2;%3").arg(strTmThreshould).arg(strAmplitudethreshold).arg(strTmRange));
    m_pResultLineEdit->setPlainText("");

    QList<QPointF> pointDevList = m_MeltingCalc.getDerivatePointList();
    QList<QPointF> pointSmoothList = m_MeltingCalc.getSmoothPointList();
    QList<double> devList;
    for (auto& item : pointDevList) {
        devList.push_back(item.y());
    }
    QList<double> smoothList;
    for (auto& item : pointSmoothList) {
        smoothList.push_back(item.y());
    }
    // 设置荧光数据 可以是原始、平滑、导数

    //这里根据选择进行显示！
    int iDataType = m_pDataDisplayType->currentIndex();
    if(iDataType == 0)
    {
        m_pCustomPlot->graph(nColor)->setData(dTempVec.toVector(), dFLList.toVector());
    }
    else if(iDataType == 1)
    {
        m_pCustomPlot->graph(nColor)->setData(dTempVec.toVector(), smoothList.toVector());
        dFLList = smoothList;
    }
    else if(iDataType == 2)
    {
        m_pCustomPlot->graph(nColor)->setData(dTempVec.toVector(), devList.toVector());
        dFLList = devList;
    }

    // 显示结果
    QString strMeltingInfo = m_MeltingCalc.getTmRmStrResult();
    QStringList strTmList,strRmList,strYmList;
    QString strThreashould;
    _GetTmFormMeltInfo(strMeltingInfo,strTmList,strRmList,strYmList,strThreashould);
    _SetTmRmValue(iDataType,nColor,strTmList,strRmList,strYmList);
    QString strDebugOutPut = m_MeltingCalc.getDebugOutput();
    m_pResultLineEdit->setPlainText(strDebugOutPut);

}

QList<double> CMeltingOneWidget::moving_average(const QList<double> &dSrcDataList, qreal window_size)
{
    return m_MeltingCalc.moving_average(dSrcDataList,window_size);
}


void CMeltingOneWidget::_ShowCurrentHoleFLData(int iHole)
{
    // 0  --- 1  孔道
    QVector<double> realTempVec;
    for(int i=0; i<m_dTempVec.size(); i++)
        realTempVec.push_back(m_dTempVec.at(i) / 100.0);


    QVector<double> dYVec;

    for(int i=0; i<gk_iBGYRCount; i++)
    {
        QMap<double, double> map = m_dRawFLDataList[i + iHole * 4];
        int dataType = m_pDataDisplayType->currentIndex();
        QList<double> dFLList;
        QVector<double> dFLVec;

        if(dataType == 0)
        {
            dFLVec = map.values().toVector();
        }
        else if(dataType == 1)
        {
            dFLList = m_MeltingCalc.moving_average(m_dRawFLDataList.at(i + iHole * 4).values(),3);
            dFLVec = dFLList.toVector();
        }
        else if(dataType == 2)
        {
            map = m_dDerFLDataList[i + iHole * 4];
            dFLVec = map.values().toVector();
        }
        dYVec.append(dFLVec);
        m_pCustomPlot->graph(i)->setData(realTempVec, dFLVec);
    }
    _SetPlotXYRange(realTempVec, dYVec);

    m_bReplot = true;
    if(m_bShow)
    {
        m_bReplot = false;
        m_pCustomPlot->replot();
    }
}

double CMeltingOneWidget::_GetDerivatePoint(const QVector<double> &dTempVec, const QList<double> &dFLList)
{

    return m_MeltingCalc._GetDerivateList(dTempVec,dFLList);
}

void CMeltingOneWidget::_SetPlotXYRange(const QVector<double> &x, const QVector<double> &y)
{
    double dXMin = 0, dXMax = 100, dYMin = 0, dYMax = 2000;
    GetListMinMaxValue(x.toList(), dXMin, dXMax);
    GetListMinMaxValue(y.toList(), dYMin, dYMax);
    double dMin = dYMin;
    dYMin -= (dYMax - dYMin) * 0.2;
    dYMax += (dYMax - dMin) * 0.2;
    if((dYMax - dYMin) < 1)
    {
        dYMin -= 1;
        dYMax += 1;
    }
    m_pCustomPlot->xAxis->setRange(dXMin, dXMax);
    m_pCustomPlot->yAxis->setRange(dYMin, dYMax);
    QStringList strRangeList = {QString::number((int)dXMin), QString::number((int)dXMax),
                                QString::number(dYMin, 'f', 1), QString::number(dYMax, 'f', 1)};
    m_pCSetXY->SetRange(strRangeList);
}

void CMeltingOneWidget::_CalcTmRmResultValue(int iResult)
{
    SRunningInfoStruct sRunInfo = CRunTest::GetInstance()->GetRunInfoStruct(m_iMachineID);
    sRunInfo.sResultInfo.iStatus = (0 == iResult)? eTestDone :eTestFail;
    qDebug()<<Q_FUNC_INFO<<"开始计算:"<<m_iMachineID<<sRunInfo.sResultInfo.iHistoryID<<"测试状态:"<<iResult;
    CCalculateThread::GetInstance()->calculateMeltingHandle(sRunInfo,QList<double>::fromVector(m_dTempVec),m_dRawFLDataList);
    return;
}

void CMeltingOneWidget::_SaveMelting2Xlsx()
{
    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;

    pXlsxStruct->strXlsxName = CPublicConfig::GetInstance()->GetTestXlsxName(m_iMachineID);
    pXlsxStruct->strTableName = "melting";
    pXlsxStruct->bDrawChart = true;

    pXlsxStruct->strTitleList<<"Temp"<<"B1"<<"G1"<<"Y1"<<"R1"<<"B2"<<"G2"<<"Y2"<<"R2";

    int iCycle = m_dTempVec.size();
    for(int i=0; i<iCycle; i++)
    {
        QVariantList qRowList;
        qRowList<<m_dTempVec.at(i) / 100.0;

        for(int j=0; j<m_dRawFLDataList.size(); j++)
            qRowList<<m_dRawFLDataList.at(j).value(i, 0);

        pXlsxStruct->varWriteDataList<<qRowList;
    }

    QSize qChartSize;
    qChartSize.setWidth(580);
    qChartSize.setHeight(355);

    ChartNoteStruct chart1;
    chart1.iRow = 1;
    chart1.iColumn = 10;
    chart1.qSize = qChartSize;
    chart1.strChartTitle = "孔1";
    chart1.strXTitle = "cycles";
    chart1.strYTitle = "melting";
    chart1.strMarkSymbolList << "none" << "none" << "none" << "none";
    chart1.strSerialNameList<<"B1"<<"G1"<<"Y1"<<"R1";
    chart1.strSerialColorList<<HEX_COLOR_B<<HEX_COLOR_G<<HEX_COLOR_Y<<HEX_COLOR_R;
    chart1.strXDataRange = QString("%1!$A$2:$A$%2").arg(pXlsxStruct->strTableName).arg(iCycle + 1);
    chart1.strNumDataRange = QString("B2:E%1").arg(iCycle+1);

    ChartNoteStruct chart2 = chart1;
    chart2.iRow = 19;
    chart2.strChartTitle = "孔2";
    chart2.strSerialNameList.clear();
    chart2.strSerialNameList<<"B2"<<"G2"<<"Y2"<<"R2";
    chart2.strNumDataRange = QString("F2:I%1").arg(iCycle + 1);

    pXlsxStruct->chartNoteList<<chart1<<chart2;
    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
    qDebug()<<QString("%1# melting data write to result xlsx end").arg(m_iMachineID + 1);
}

void CMeltingOneWidget::_SetTmRmValue(int iDataType,int nColor, const QStringList& strTmList,const QStringList& strRmList,const QStringList& strYmList)
{

    bool bShowDotLine = iDataType == 2;
    if(strTmList.size() == 1)
    {
        // 5 * 4  20个
        m_pLineEditList.at(nColor*5 + 1)->setText(strTmList.at(0));
        m_pLineEditList.at(nColor*5 + 2)->setText(m_pYmRm->isChecked()?strYmList.at(0):strRmList.at(0));
        float threshouldFirst = strTmList.at(0).toFloat();
        if(threshouldFirst < 0.001)
        {
            m_pDotLineFirstList.at(nColor)->setVisible(false);
        }
        else
        {
            m_pDotLineFirstList.at(nColor)->setVisible(true && bShowDotLine);
        }
        float fRange  = 0.2*strYmList.at(0).toFloat();
        if(fRange < 3 && fRange > -3)
        {
            fRange = 3;
        }
        m_pDotLineFirstList.at(nColor)->start->setCoords(threshouldFirst, strYmList.at(0).toFloat()-fRange);
        m_pDotLineFirstList.at(nColor)->end->setCoords(threshouldFirst, strYmList.at(0).toFloat()+fRange);

    }
    if(strTmList.size()>=2)
    {
        // 5 * 4  20个
        m_pLineEditList.at(nColor*5 + 1)->setText(strTmList.at(0));
        m_pLineEditList.at(nColor*5 + 2)->setText(m_pYmRm->isChecked()?strYmList.at(0):strRmList.at(0));
        // 5 * 4  20个
        m_pLineEditList.at(nColor*5 + 3)->setText(strTmList.at(1));
        m_pLineEditList.at(nColor*5 + 4)->setText(m_pYmRm->isChecked()?strYmList.at(1):strRmList.at(1));

        float threshouldFirst = strTmList.at(0).toFloat();
        if(threshouldFirst < 0.001)
        {
            m_pDotLineFirstList.at(nColor)->setVisible(false);
        }
        else
        {
            m_pDotLineFirstList.at(nColor)->setVisible(true && bShowDotLine);
        }
        float fRange1  = 0.2 * strYmList.at(0).toFloat();
        if(fRange1 < 3 && fRange1 > -3)
        {
            fRange1 = 3;
        }

        m_pDotLineFirstList.at(nColor)->start->setCoords(threshouldFirst,  strYmList.at(0).toFloat() - fRange1);
        m_pDotLineFirstList.at(nColor)->end->setCoords(threshouldFirst,  strYmList.at(0).toFloat() + fRange1);

        float threshouldSecond = strTmList.at(1).toFloat();
        if(threshouldSecond < 0.001)
        {
            m_pDotLineSecondList.at(nColor)->setVisible(false);
        }
        else
        {
            m_pDotLineSecondList.at(nColor)->setVisible(true && bShowDotLine);
        }

        float fRange2  = 0.2 * strYmList.at(1).toFloat();
        if(fRange2 < 3 && fRange2 > -3)
        {
            fRange2 = 3;
        }

        m_pDotLineSecondList.at(nColor)->start->setCoords(threshouldSecond,  strYmList.at(1).toFloat() - fRange2);
        m_pDotLineSecondList.at(nColor)->end->setCoords(threshouldSecond, strYmList.at(1).toFloat() + fRange2);
    }
}

void CMeltingOneWidget::_InitWidget()
{
    int iHeight = 35;
    m_pHoleComboBox = new CLabelComboBox(tr("孔位:"), CPublicConfig::GetInstance()->GetHoleNameList());
    m_pHoleComboBox->SetComboBoxFixedSize(100, iHeight);
    connect(m_pHoleComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotHoleComboBoxChanged(int)));

    QStringList strColorNameList = gk_strColorNameList;
    strColorNameList.push_back("All");
    m_pColorComboBox = new QComboBox;
    m_pColorComboBox->setView(new QListView);
    m_pColorComboBox->addItems(strColorNameList);
    m_pColorComboBox->setFixedSize(100, iHeight);
    m_pColorComboBox->setCurrentIndex(4);

    m_pAmplitudethreshold = new CLabelLineEdit(tr("幅度阈值:"), "3");
    m_pAmplitudethreshold->SetLineEditFixedSize(70, iHeight);
    m_pAmplitudethreshold->setFixedSize(170, iHeight);
    m_pAmplitudethreshold->setInputMethodHints(Qt::ImhDigitsOnly);

    for(int i = 0; i < gk_strColorNameList.size() ; i++)
    {
        CComboLineEdit* pLineEdit = new CComboLineEdit(this);
        m_CComboLineEditList.push_back(pLineEdit);
    }


    //connect(m_pAmplitudethreshold, &CLabelLineEdit::SignalTextChanged, this, &CFLOneWidget::_SlotCrossBGChanged);



    m_pShowBtn = new QPushButton(tr("显示"));
    m_pShowBtn->setFixedSize(100, 50);
    connect(m_pShowBtn, &QPushButton::clicked, this, &CMeltingOneWidget::_SlotShowBtn);

    m_pSavePDFBtn = new QPushButton(tr("保存pdf"));
    m_pSavePDFBtn->setFixedSize(140, 50);
    connect(m_pSavePDFBtn, &QPushButton::clicked, this, &CMeltingOneWidget::_SlotSavePdfBtn);


    _InitCustomPlot();

    m_pCSetXY = new CSetChartXYRange({"0", "100", "0", "2000"});
    m_pCSetXY->SetLineEditTextAlignment();
    connect(m_pCSetXY, &CSetChartXYRange::SignalSetRange, this, &CMeltingOneWidget::_SlotSetXYRange);

    // 初始化右边控间
    //  m_pLineEditList = new CLineEdit[6*4];
    QStringList strLabelNameList = {tr("Tm区间"),tr("峰高阈值"),tr("Tm1"),tr("Rm1"),tr("Tm2"),tr("Rm2")};
    for(int i = 0 ;  i < strLabelNameList.size(); i++)
    {
        QLabel *pLabel = new QLabel(strLabelNameList.at(i));
        pLabel->setFixedHeight(iHeight);
        m_pLabelList.push_back(pLabel);
    }

    m_pTest = new QCheckBox(tr("测试"));
    m_pTest->setLayoutDirection(Qt::RightToLeft);
    m_pTest->setFixedSize(120, iHeight + 10);
    m_pTest->setChecked(true);
    connect(m_pTest, &QCheckBox::clicked, this, &CMeltingOneWidget::_SlotTestChecked);

    m_pYmRm = new QCheckBox(tr("显示Ym"));
    m_pYmRm->setLayoutDirection(Qt::RightToLeft);
    m_pYmRm->setFixedSize(120, iHeight + 10);
    m_pYmRm->setChecked(false);
    connect(m_pYmRm, &QCheckBox::clicked, this, &CMeltingOneWidget::_SlotYmRmChecked);



    for (int i = 0; i < 20; i++)
    {
        CLineEdit * pLineEdit = new CLineEdit;
        pLineEdit->setAlignment(Qt::AlignCenter);
        pLineEdit->setInputMethodHints(Qt::ImhDigitsOnly);
        pLineEdit->setFixedSize(120,iHeight);
        if(0 == i%5)
        {
            pLineEdit->setText("5");
        }
        m_pLineEditList.push_back(pLineEdit);
    }

    QStringList strDataDisplayType = {tr("原始熔解曲线"),tr("平滑曲线"),tr("熔解峰值曲线")};
    m_pDataDisplayType = new QComboBox;
    m_pDataDisplayType->setFixedSize(210,iHeight);
    m_pDataDisplayType->setView(new QListView);
    m_pDataDisplayType->addItems(strDataDisplayType);
    connect(m_pDataDisplayType,SIGNAL(activated(int)),this,SLOT(_SlotDataDisplayTypeComboBoxChanged(int)));

    m_pResultLineEdit = new QPlainTextEdit();
    m_pResultLineEdit->setFixedSize(200,iHeight*2);
}

void CMeltingOneWidget::_InitLayout()
{
    // initLayout
    QGridLayout* pLeftGridLayout = new QGridLayout;
    pLeftGridLayout->setMargin(0);
    pLeftGridLayout->setVerticalSpacing(5);
    pLeftGridLayout->setHorizontalSpacing(10);

    //pLeftGridLayout->addWidget(m_CComboLineEditList.at(0), 0, 0);
    pLeftGridLayout->addWidget(m_pLabelList.at(0),0,0);
    pLeftGridLayout->addWidget(m_pLabelList.at(1),1,0);
    pLeftGridLayout->addWidget(m_pLabelList.at(2),2,0);
    pLeftGridLayout->addWidget(m_pLabelList.at(3),3,0);
    pLeftGridLayout->addWidget(m_pLabelList.at(4),4,0);
    pLeftGridLayout->addWidget(m_pLabelList.at(5),5,0);

    pLeftGridLayout->addWidget(m_CComboLineEditList.at(0), 0, 1);
    pLeftGridLayout->addWidget(m_pLineEditList.at(0), 1, 1);
    pLeftGridLayout->addWidget(m_pLineEditList.at(1), 2, 1);
    pLeftGridLayout->addWidget(m_pLineEditList.at(2), 3, 1);
    pLeftGridLayout->addWidget(m_pLineEditList.at(3), 4, 1);
    pLeftGridLayout->addWidget(m_pLineEditList.at(4), 5, 1);

    pLeftGridLayout->addWidget(m_CComboLineEditList.at(1), 0, 2);
    pLeftGridLayout->addWidget(m_pLineEditList.at(5), 1, 2);
    pLeftGridLayout->addWidget(m_pLineEditList.at(6), 2, 2);
    pLeftGridLayout->addWidget(m_pLineEditList.at(7), 3, 2);
    pLeftGridLayout->addWidget(m_pLineEditList.at(8), 4, 2);
    pLeftGridLayout->addWidget(m_pLineEditList.at(9), 5, 2);


    pLeftGridLayout->addWidget(m_CComboLineEditList.at(2), 0, 3);
    pLeftGridLayout->addWidget(m_pLineEditList.at(10), 1, 3);
    pLeftGridLayout->addWidget(m_pLineEditList.at(11), 2, 3);
    pLeftGridLayout->addWidget(m_pLineEditList.at(12), 3, 3);
    pLeftGridLayout->addWidget(m_pLineEditList.at(13), 4, 3);
    pLeftGridLayout->addWidget(m_pLineEditList.at(14), 5, 3);


    pLeftGridLayout->addWidget(m_CComboLineEditList.at(3), 0, 4);
    pLeftGridLayout->addWidget(m_pLineEditList.at(15), 1, 4);
    pLeftGridLayout->addWidget(m_pLineEditList.at(16), 2, 4);
    pLeftGridLayout->addWidget(m_pLineEditList.at(17), 3, 4);
    pLeftGridLayout->addWidget(m_pLineEditList.at(18), 4, 4);
    pLeftGridLayout->addWidget(m_pLineEditList.at(19), 5, 4);


    // 右侧布局
    QGridLayout *pRightGridLayout = new QGridLayout;
    pRightGridLayout->setMargin(0);
    pRightGridLayout->setVerticalSpacing(10);
    pRightGridLayout->setHorizontalSpacing(20);
    pRightGridLayout->addWidget(m_pHoleComboBox, 0, 0);
    pRightGridLayout->addWidget(m_pColorComboBox, 0, 1);
    pRightGridLayout->addWidget(m_pShowBtn, 0, 2);
    pRightGridLayout->addWidget(m_pSavePDFBtn, 0, 3);

    pRightGridLayout->addWidget(m_pDataDisplayType, 1, 0);
    pRightGridLayout->addWidget(m_pTest, 1, 1);
    pRightGridLayout->addWidget(m_pResultLineEdit, 1, 2);

    pRightGridLayout->addWidget(m_pAmplitudethreshold, 2, 0);
    pRightGridLayout->addWidget(m_pYmRm, 2, 1);


    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(10);
    pTopLayout->addLayout(pLeftGridLayout);
    pTopLayout->setSpacing(40);
    pTopLayout->addLayout(pRightGridLayout);
    pTopLayout->addStretch(1);

    QVBoxLayout *pMainLayout = new QVBoxLayout;
    pMainLayout->setMargin(0);
    pMainLayout->addSpacing(10);
    pMainLayout->setSpacing(10);
    pMainLayout->addLayout(pTopLayout);
    pMainLayout->addWidget(m_pCustomPlot);
    pMainLayout->addWidget(m_pCSetXY, 0, Qt::AlignLeft);

    this->setLayout(pMainLayout);
}


void CMeltingOneWidget::_InitCustomPlot()
{
    m_pCustomPlot = new QCustomPlot;

    QFont font;
    font.setPointSize(10);
    m_pCustomPlot->legend->setFont(font);
    m_pCustomPlot->legend->setSelectedFont(font);
    m_pCustomPlot->legend->setVisible(true);
    m_pCustomPlot->legend->setSelectableParts(QCPLegend::spItems);
    m_pCustomPlot->legend->setBorderPen(Qt::NoPen);
    m_pCustomPlot->legend->setWrap(1);
    m_pCustomPlot->axisRect()->insetLayout()->setInsetAlignment(0, Qt::AlignTop);

    QList<QColor> colorList = gk_strColorValueList;
    _AddGraph(m_pCustomPlot, colorList[B], colorList[B], B, gk_strColorNameList[B]);
    _AddGraph(m_pCustomPlot, colorList[G], colorList[G], G, gk_strColorNameList[G]);
    _AddGraph(m_pCustomPlot, colorList[Y], colorList[Y], Y, gk_strColorNameList[Y]);
    _AddGraph(m_pCustomPlot, colorList[R], colorList[R], R, gk_strColorNameList[R]);

    m_pCustomPlot->xAxis->setRange(0, 100);
    m_pCustomPlot->xAxis->ticker()->setTickCount(9);
    m_pCustomPlot->xAxis->setSubTicks(false);
    m_pCustomPlot->yAxis->setRange(0, 2000);
    m_pCustomPlot->yAxis->ticker()->setTickCount(9);
    m_pCustomPlot->yAxis->setSubTicks(false);
    for(int i = 0; i < gk_strColorNameList.size() ; i++)
    {
        QCPItemLine* pDotLineFirst = new QCPItemLine(m_pCustomPlot);
        QCPItemLine* pDotLineSecond = new QCPItemLine(m_pCustomPlot);
        QPen pen(Qt::DashLine);
        pen.setWidth(2);
        pen.setColor(colorList.at(i));
        pDotLineFirst->setPen(pen);
        pDotLineFirst->setVisible(false);
        pDotLineSecond->setPen(pen);
        pDotLineSecond->setVisible(false);
        m_pDotLineFirstList.push_back(pDotLineFirst);
        m_pDotLineSecondList.push_back(pDotLineSecond);
    }
}

void CMeltingOneWidget::_AddGraph(QCustomPlot *pCustomPlot, QColor penColor, QColor pointColor, int iChart, QString strChartName)
{
    QPen pen;
    pen.setWidth(2);
    pen.setColor(penColor);
    pCustomPlot->addGraph();
    pCustomPlot->graph(iChart)->setPen(pen);
    pCustomPlot->graph(iChart)->setName(strChartName);
    pCustomPlot->graph(iChart)->setAntialiasedFill(true);
    pCustomPlot->graph(iChart)->setScatterStyle(
                QCPScatterStyle(QCPScatterStyle::ssNone,
                                QPen(pointColor, 2),
                                QBrush(pointColor), 2));
}

void CMeltingOneWidget::_SlotDataDisplayTypeComboBoxChanged(int nIndex)
{
    _UpdateCurve();
    Q_UNUSED(nIndex);
}

bool CMeltingOneWidget::_UpdateCalcParam(int iHole)
{

    bool bResult = false;
    QString strCardIDTemp = m_strNewCardID;
    QStringList strCardIdList = strCardIDTemp.split("+");
    if(strCardIdList.size() >= 2)
    {
        QString strCardIDLeft = strCardIdList.at(0);
        QString strTestTime = strCardIdList.at(1);

        if (strTestTime.length() < 14)
        {
            return bResult;
        }
        QString year = strTestTime.left(4);
        QString month = strTestTime.mid(4, 2);
        QString day = strTestTime.mid(6, 2);
        QString hour = strTestTime.mid(8, 2);
        QString minute = strTestTime.mid(10, 2);
        QString second = strTestTime.mid(12, 2);
        strTestTime = year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second;

        QList<double> dTempList;
        SResultInfoStruct sResult;
        if(!CProjectDB::GetInstance()->GetHistoryData(strCardIDLeft,strTestTime,sResult))
        {
            return bResult;
        }
        SLotInfoStruct sLotInfo;
        CLotInfoDB::GetInstance()->GetLotInfoByShowName(sResult.strProjectName, sLotInfo);
        // 需要整理这个方法，可以共用 需要整理； 这两个荧光类可以抽象出一层基类，方便代码维护和编写 共用；

        QStringList strRmThresholdList = sLotInfo.strRmThreshold.split(";");
        QStringList strTmThresholdList = sLotInfo.strTmRange.split(";");
        QStringList strAmpThreshouldList = sLotInfo.strAmplitudeThresholdValue.split(";");
        qDebug()<<Q_FUNC_INFO<<",TmThreshold： "<<strTmThresholdList <<", RmThreshold: " << strRmThresholdList <<"   ,AmpThreshold: "<< strAmpThreshouldList; //阈值配置

        for(int i = 0; i < gk_strColorNameList.size() ; i++)
        {

            int index = i + iHole*4;


            QString strRmThre("5"),strAmpThre("3"),strTmThre("40.0&90.0");
            if( index< strRmThresholdList.size() && strRmThresholdList.size() >=4)
            {
                strRmThre = strRmThresholdList.at(index);
            }
            if( index < strAmpThreshouldList.size() && strAmpThreshouldList.size() >=4 )
            {
                strAmpThre = strAmpThreshouldList.at(index);
            }
            if( index< strTmThresholdList.size() && strTmThresholdList.size() >=4 )
            {
                strTmThre = strTmThresholdList.at(index);//Tm范围  45.1&90.1;
            }
            CComboLineEdit* pComboLineEdit = m_CComboLineEditList.at(i);
            QStringList strTmThreList = strTmThre.split("&");
            if(strTmThreList.size() >=2)
            {
                pComboLineEdit->setText(strTmThreList.at(0),strTmThreList.at(1));
            }
            else
            {
                pComboLineEdit->setText("40.0","90.0");
            }
            if(i*5 < m_pLineEditList.size())
            {
                CLineEdit* pLineEditTemp = m_pLineEditList.at(i*5);
                pLineEditTemp->setText(strRmThre);
            }

        }
        bResult = true;
    }
    return bResult;
}
