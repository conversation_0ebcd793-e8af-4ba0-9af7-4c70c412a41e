#include "CLabelCheckBox.h"
#include <QHBoxLayout>

CLabelCheckBox::CLabelCheckBox(const QString &strText, bool bChecked, int iSpacing, QWidget *parent)
    : QWidget(parent)
    , m_iSpacing(iSpacing)
    , m_strText(strText)
    , m_bChecked(bChecked)
{
    _InitWidget();
}

CLabelCheckBox::~CLabelCheckBox()
{

}

void CLabelCheckBox::SetLabelAlignment(Qt::Alignment qAlig)
{
    m_pLabel->setAlignment(qAlig);
}

void CLabelCheckBox::SetCheckBoxChecked(bool bChecked)
{
    m_bChecked = bChecked;
    m_pCheckBox->setChecked(bChecked);
}

void CLabelCheckBox::SetLabelMinSize(int iWidth, int iHeight)
{
    m_pLabel->setMinimumSize(iWidth, iHeight);
}

void CLabelCheckBox::SetLabelFixedSize(int iWidth, int iHeight)
{
    m_pLabel->setFixedSize(iWidth, iHeight);
}

void CLabelCheckBox::SetLabelObjectName(const QString &strObjName)
{
    m_pLabel->setObjectName(strObjName);
}

void CLabelCheckBox::SetCheckBoxObjectName(const QString &strObjName)
{
    m_pCheckBox->setObjectName(strObjName);
}

bool CLabelCheckBox::GetChecked() const
{
    return m_pCheckBox->isChecked();
}

void CLabelCheckBox::_InitWidget()
{
    m_pLabel = new QLabel(m_strText, this);
    m_pLabel->setAlignment(Qt::AlignRight);
    m_pCheckBox = new QCheckBox(this);
    connect(m_pCheckBox, &QCheckBox::clicked, this, &CLabelCheckBox::SignalClicked);

    QHBoxLayout* pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->addWidget(m_pLabel, 0 ,Qt::AlignVCenter);
    pLayout->addSpacing(m_iSpacing);
    pLayout->addWidget(m_pCheckBox, 0 ,Qt::AlignVCenter);
    setLayout(pLayout);
}
