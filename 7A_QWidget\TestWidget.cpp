#include "TestWidget.h"
#include "ui_Widget.h"
#include <QHBoxLayout>
#include <QFile>
#include <QDebug>
#include <QDate>
#include "CProgressBar.h"

#include "CMessageBox.h"
#include "PublicParams.h"
#include "PublicFunction.h"
#include <QTimer>
#include <QGroupBox>

int gk_inputMethod;

TestWidget::TestWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::Widget)
{
    ui->setupUi(this);

    QTime q1 = QTime::currentTime();

    m_pInfoBtn = new QPushButton("测试弹窗");
    m_pInfoBtn->setMinimumHeight(50);
    connect(m_pInfoBtn, &QPushButton::clicked, this, &TestWidget::SlotInfoBtn);

    m_pUpdateBtn = new QPushButton("测试升级");
    m_pUpdateBtn->setMinimumHeight(50);
    connect(m_pUpdateBtn, &QPushButton::clicked, this, &TestWidget::SlotUpdateBtn);

    m_pExportBtn  = new QPushButton("测试导出");
    m_pExportBtn->setMinimumHeight(50);
    connect(m_pExportBtn, &QPushButton::clicked, this, &TestWidget::SlotExportBtn);

    m_pTestBtn = new QPushButton("函数测试");
    m_pTestBtn->setMinimumHeight(50);
    connect(m_pTestBtn, &QPushButton::clicked, this, &TestWidget::SlotTestBtn);

    QStringList strList = {"设置","网络","打印","工厂","自检"};
    m_pHBtnTitleWidget = new CHBtnTitleWidget(strList);

    strList.clear();
    strList<<"日期"<<"光学"<<"PCR";
    m_pVBtnTitleWidget = new CVBtnTitleWidget(strList);
    connect(m_pVBtnTitleWidget, &CVBtnTitleWidget::SignalTitleChanged, this, &TestWidget::_SlotVTitleBtn);

    m_pCDateTime = new CDateTime();
    m_pCLabelDate = new CLabelDate("选择日期:", QDate::currentDate().toString("yyyy-MM-dd"));
    m_pCLabelDate->SetLabelFixedSize(100,50);
    m_pCLabelDate->SetDateFixedSize(130,50);

    m_pCIPLabelLineEdit = new CIPLabelLineEdit("IP地址:",{"192","168","1","1"});
    m_pCIPLabelLineEdit->setFixedHeight(60);

    _InitCommonWidget();
    _InitProgressBar();
    _InitLayout();

    qDebug()<<"TestWidget Time:"<<q1.msecsTo(QTime::currentTime());
}

TestWidget::~TestWidget()
{
    delete ui;
}

void TestWidget::SlotInfoBtn()
{
    ShowInformation(NULL,"提示","提示提示提示");
    ShowSuccess(NULL,"提示","成功成功成功");
    ShowWarning(NULL,"提示","警告警告警告");
    ShowQuestion(NULL,"提示","提问提问提问");
}

void TestWidget::_InitLayout()
{
    QHBoxLayout *pLabelLayout = new QHBoxLayout;
    pLabelLayout->addWidget(m_pCLabelLabel);
    pLabelLayout->addSpacing(5);
    pLabelLayout->addWidget(m_pCLabelComboBox);
    pLabelLayout->addSpacing(5);
    pLabelLayout->addWidget(m_pCLabelLineEdit);

    QHBoxLayout *pInputLayout = new QHBoxLayout;
    pInputLayout->addWidget(m_pInputLabelComboBox);
    pInputLayout->addSpacing(5);
    pInputLayout->addWidget(m_pInputLabelLineEidt);

    QHBoxLayout* pBtnLayout = new QHBoxLayout;
    pBtnLayout->addWidget(m_pInfoBtn);
    pBtnLayout->addSpacing(5);
    pBtnLayout->addWidget(m_pUpdateBtn);
    pBtnLayout->addSpacing(5);
    pBtnLayout->addWidget(m_pExportBtn);
    pBtnLayout->addSpacing(5);
    pBtnLayout->addWidget(m_pTestBtn);
    pBtnLayout->addSpacing(5);
    pBtnLayout->addWidget(m_pHBtnTitleWidget);

    QHBoxLayout *p4 = new QHBoxLayout;
    p4->addWidget(m_pVBtnTitleWidget);
    p4->addSpacing(20);
    p4->addWidget(m_pCLabelDate);
    p4->addSpacing(20);
    p4->addWidget(m_pCIPLabelLineEdit);
    p4->addStretch(1);

    QVBoxLayout* p3 = new QVBoxLayout;
    p3->addSpacing(50);
    p3->addLayout(pLabelLayout);
    p3->addStretch(1);
    p3->addLayout(pInputLayout);
    p3->addStretch(1);
    p3->addLayout(pBtnLayout);
    p3->addStretch(1);
    p3->addLayout(p4);
    p3->addSpacing(50);

    setLayout(p3);
}

void TestWidget::_InitCommonWidget()
{
    m_pCLabelLabel = new CLabelLabel("标签:","21313232");
    m_pCLabelLabel->SetNameLabelFixedSize(70,50);
    m_pCLabelLabel->SetValueLabelMinSize(120,50);

    m_pCLabelComboBox = new CLabelComboBox("时序:",{"123","abc"});
    m_pCLabelComboBox->SetLabelFixedSize(70,50);
    m_pCLabelComboBox->SetComboBoxMinSize(120,50);

    m_pCLabelLineEdit = new CLabelLineEdit("时间:","2019-01-01");
    m_pCLabelLineEdit->SetLabelFixedSize(70,50);
    m_pCLabelLineEdit->SetLineEditMinSize(120,50);

    QStringList strList={"Date","Time","DigitsOnly","ImhPreferNumbers",
                         "ImhPreferUppercase","ImhPreferLowercase",
                         "ImhUppercaseOnly","ImhLowercaseOnly",
                         "ImhLatinOnly","ImhNone"};
    m_inputMap.insert(0,Qt::ImhDate);
    m_inputMap.insert(1,Qt::ImhTime);
    m_inputMap.insert(2,Qt::ImhDigitsOnly);
    m_inputMap.insert(3,Qt::ImhPreferNumbers);
    m_inputMap.insert(4,Qt::ImhPreferUppercase);
    m_inputMap.insert(5,Qt::ImhPreferLowercase);
    m_inputMap.insert(6,Qt::ImhUppercaseOnly);
    m_inputMap.insert(7,Qt::ImhLowercaseOnly);
    m_inputMap.insert(8,Qt::ImhLatinOnly);
    m_inputMap.insert(9,Qt::ImhNone);

    m_pInputLabelComboBox = new CLabelComboBox("输入法",strList);
    m_pInputLabelComboBox->SetLabelFixedSize(70,50);
    m_pInputLabelComboBox->SetComboBoxFixedSize(250,50);
    connect(m_pInputLabelComboBox, SIGNAL(SignalCurrentIndexChanged(int)),
            this, SLOT(_SlotInputMethodChanged(int)));

    m_pInputLabelLineEidt = new CLabelLineEdit("Input","");
    m_pInputLabelLineEidt->SetLabelFixedSize(70,50);
    m_pInputLabelLineEidt->SetLineEditMinSize(120,50);
}

void TestWidget::_SlotInputMethodChanged(int iMethod)
{
    gk_inputMethod = m_inputMap.value(iMethod);
   // qDebug()<<"input method:"<<iMethod<<gk_inputMethod;
}

void TestWidget::SlotUpdateBtn()
{
    m_pUpdateProgressBar->show();

    QTimer *pTimer = new QTimer(this);
    connect(pTimer, &QTimer::timeout, [&](){
        static int i=0;
        if(i > 100)
        {
            pTimer->stop();
            m_pUpdateProgressBar->close();
        }
        m_pUpdateProgressBar->SetValue(i);
        i++;
    });
    pTimer->start(50);
}

void TestWidget::SlotExportBtn()
{
    m_pExportProgressBar->show();

    QTimer *pTimer = new QTimer(this);
    connect(pTimer, &QTimer::timeout, [&](){
        static int i=0;
        if(i > 100)
        {
            pTimer->stop();
            m_pExportProgressBar->close();
        }
        m_pExportProgressBar->SetValue(i);
        i++;
    });
    pTimer->start(100);
}

void TestWidget::SlotTestBtn()
{
    QString strOldPath = "./1.txt";
    QString strNewPath = "./2.txt";
    QDir strNewDir("../");
    CopyFile(strOldPath,strNewPath);
    CopyFile(strOldPath,strNewDir);

    qDebug()<<"keyboard rect:"<<QApplication::inputMethod()->inputItemRectangle()
           <<QApplication::inputMethod()->inputItemClipRectangle();
}

void TestWidget::_SlotVTitleBtn(int index)
{
    if(0 == index)
    {
        m_pCDateTime->SetDate(QDate::currentDate().toString("yyyy-MM-dd"));
        m_pCDateTime->show();
    }
}

void TestWidget::_InitProgressBar()
{
    m_pUpdateProgressBar = new CProgressBar("升级","进度");
    m_pUpdateProgressBar->SetRange(0,100);
    m_pUpdateProgressBar->SetBarObjectName("UpdateProgressBar");

    m_pExportProgressBar = new CProgressBar("导出","");
    m_pExportProgressBar->SetRange(0,100);
    m_pExportProgressBar->SetLabelVisible(false);
    m_pExportProgressBar->SetBarObjectName("DownloadProgressBar");
}

void TestWidget::resizeEvent(QResizeEvent *pEvent)
{
    QWidget::resizeEvent(pEvent);
    // G_iWidth = this->width();
    // G_iHeight = this->height();
}

void TestWidget::moveEvent(QMoveEvent *pEvent)
{
    QWidget::moveEvent(pEvent);
    //  G_qPoint = pos();
}
