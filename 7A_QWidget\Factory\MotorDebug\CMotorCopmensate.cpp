#include "CMotorCopmensate.h"
#include <QBoxLayout>
#include <QGridLayout>
#include <QHeaderView>
#include <QDebug>
#include "CMessageBox.h"
#include "CSystemDB.h"
#include "CMotorDB.h"

CMotorCopmensate::CMotorCopmensate(QWidget *parent) : QWidget(parent)
{
    m_bShow = false;
    m_iUiMachineID = 0;
    m_strMachineLog = QString("%1#").arg(m_iUiMachineID);
    m_iMotorIndex = 0;
    m_strMotorName = CPublicConfig::GetInstance()->GetMotorNameList().first();

    m_strComboTextList << QStringList({"MHRST", "MHMW", "MHMD", "VTOMW"})
                       << QStringList({"HRRST", "HRMV;3,0", "HRMV;4,0",
                                       "HTTHR", "HTTWO", "HTONE",
                                       "MTTH<PERSON>", "MTTWO", "MTO<PERSON>",
                                       "MTF<PERSON>", "MTFIV", "MTSIX"})
                       << QStringList({"TCRST", "TCMW", "TCMD", "TCOMH;5,0", "VTOMW"})
                       << QStringList({"SDRST", "SDMV"})
                       << QStringList({"BRST", "BMV", "BRSTBK", "MTONE", "MTFOR",
                                       "MTTWO", "MTFIV", "MTTHR", "MTSIX"})
                       << QStringList({"TCRST", "MOVE"});

    m_strLabelTextList1 << tr("上:复位") << tr("左:复位3/4板") << tr("上:复位")
                        << tr("前:遮挡") << tr("前:复位4/5/6板") << tr("上:复位");
    m_strLabelTextList2 << tr("下:底部") << tr("右:1/6板")    << tr("下:底部")
                        << tr("后:复位") << tr("后:1/2/3板") << tr("下:底部");

    Register2Map(Method_GCMP);
    Register2Map(Method_ACTUALPOS);

    _InitWidget();
    _InitLayout();

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalSoftTypeChanged, this, &CMotorCopmensate::SlotSoftTypeChanged);
}

CMotorCopmensate::~CMotorCopmensate()
{
    UnRegister2Map(Method_GCMP);
    UnRegister2Map(Method_ACTUALPOS);
}

void CMotorCopmensate::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    qDebug()<<QString("%1#电机补偿").arg(iMachineID + 1)<<iMethodID<<iResult;

    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if(iMethodID == Method_GCMP)
    {
        QStringList strList = qVarData.toString().split(SPLIT_BETWEEN_CMD);
        if(strList.size() < 1)
            return;
        int iReadMotor = strList.at(0).toInt();
        m_pMotorComboBox->SetCurrentIndex(iReadMotor);

        QList<QStringList> strDataList;
        for(int i=1; i<strList.size(); i++)
        {
            strDataList.push_back(strList.at(i).split(SPLIT_IN_CMD));
        }
        _LoadData2TableWidget(strDataList);
    }
    else if(Method_ACTUALPOS == iMethodID)
    {
        QStringList strList = qVarData.toString().split(SPLIT_IN_CMD);
        if(strList.size() < 1)
            return;
        int iReadMotor = strList.at(0).toInt();
        m_pMotorComboBox->SetCurrentIndex(iReadMotor);
        m_pValueLineEdit->setText(strList.at(1));
    }
}

void CMotorCopmensate::SlotSoftTypeChanged(int iSoftType)
{
    Q_UNUSED(iSoftType);
    m_iMotorIndex = 0;
    m_pMotorComboBox->SetComboBoxList(CPublicConfig::GetInstance()->GetMotorNameList());
    m_pMotorComboBox->SetCurrentIndex(0);
    m_strMotorName = m_pMotorComboBox->GetCurrentText();
}

void CMotorCopmensate::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;
    QWidget::showEvent(pEvent);
}

void CMotorCopmensate::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;
    QWidget::hideEvent(pEvent);
}

void CMotorCopmensate::_SlotMachineComboBoxChanged(int iMachineID)
{
    m_iUiMachineID = iMachineID;
}

void CMotorCopmensate::_SlotMotorComboBoxChanged(int index)
{
    if(index < 0 || !m_bShow)
        return;

    m_iMotorIndex = index;
    m_strMotorName = m_pMotorComboBox->GetCurrentText();

    m_pMethodComboBox->clear();
    if(index >=0 && index < m_strComboTextList.size())
        m_pMethodComboBox->addItems(m_strComboTextList.at(index));

    m_pLabel1->clear();
    m_pLabel2->clear();
    if(index >=0 && index < m_strLabelTextList1.size())
    {
        m_pLabel1->setText(m_strLabelTextList1.at(index));
        m_pLabel2->setText(m_strLabelTextList2.at(index));
    }

    m_pTableWidget->clearContents();
    m_pTableWidget->setRowCount(0);

    if(5 == index)
        m_pSetBtn->setText(tr("设置到参数配置"));
    else
        m_pSetBtn->setText(tr("设置到当前行"));

    QVariant qVarData = QString::number(index);
    QString strCmd = GetJsonCmdString(Method_GCMP, qVarData);
    qDebug()<<QString("%1#%2,获取电机补偿:%3").arg(m_iUiMachineID + 1).arg(m_strMotorName).arg(strCmd);
    SendJsonCmd(m_iUiMachineID, Method_GCMP, strCmd);
}

void CMotorCopmensate::_SlotClearBtn()
{
    int iMotor = m_iMotorIndex;
    if(5 == iMotor)
        iMotor = 2;
    QVariant qVarData = QString::number(iMotor);
    QString strCmd = GetJsonCmdString(Method_CLEARPOS, qVarData);
    qDebug()<<QString("%1#%2,电机位置清零:%3").arg(m_iUiMachineID + 1).arg(m_strMotorName).arg(strCmd);
    SendJsonCmd(m_iUiMachineID, Method_CLEARPOS, strCmd);
}

void CMotorCopmensate::_SlotReadBtn()
{
    int iMotor = m_iMotorIndex;
    if(5 == iMotor)
        iMotor = 2;
    QVariant qVarData = QString::number(iMotor);
    QString strCmd = GetJsonCmdString(Method_ACTUALPOS, qVarData);
    qDebug()<<QString("%1#%2,读取电机位置:%3").arg(m_iUiMachineID + 1).arg(m_strMotorName).arg(strCmd);
    SendJsonCmd(m_iUiMachineID, Method_ACTUALPOS, strCmd);
}

void CMotorCopmensate::_SlotSetBtn()
{
    QString strValue = m_pValueLineEdit->text();
    if(5 == m_iMotorIndex)
    {
        if(strValue.isEmpty())
        {
            ShowInformation(this, m_strTipsText, tr("请先读取当前值"));
            return;
        }

        CSystemDB::GetInstance()->addKeyValue("timing_v1h", strValue.toDouble() / 1000);
        return;
    }

    int iRow = m_pTableWidget->currentRow();
    if(iRow < 0)
    {
        ShowInformation(this, m_strTipsText, tr("请先选中一行"));
        return;
    }

    if(!strValue.isEmpty())
    {
        CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(m_pTableWidget->cellWidget(iRow, 3));
        pLineEdit->setText(strValue);
    }
}

void CMotorCopmensate::_SlotExeBtn()
{
    QString strCmd;
    QString strText = m_pMethodComboBox->currentText();
    if(5 == m_iMotorIndex)
    {
        int iMethodID = CPublicConfig::GetInstance()->GetMethodIDByName(strText);
        if(0 == m_pMethodComboBox->currentIndex())
        {
            strCmd = GetJsonCmdString(iMethodID);
            qDebug()<<QString("%1#%2:%3").arg(m_iUiMachineID + 1).arg(m_strMotorName).arg(strCmd);
            SendJsonCmd(m_iUiMachineID, iMethodID, strCmd);
        }
        else if(1 == m_pMethodComboBox->currentIndex())
        {
            if(m_pValueLineEdit->text().isEmpty())
            {
                ShowInformation(this, m_strTipsText, tr("请先读取当前值"));
                return;
            }

            QVariantList qVarList = {2, 1, m_pValueLineEdit->text().toDouble()};
            strCmd = GetJsonCmdString(iMethodID, qVarList);
            qDebug()<<QString("%1#%2:%3").arg(m_iUiMachineID + 1).arg(m_strMotorName).arg(strCmd);
            SendJsonCmd(m_iUiMachineID, iMethodID, strCmd);
        }
        return;
    }

    if(strText.contains(";"))
    {
        QStringList strList = strText.split(";");
        if(strList.size() >= 2)
        {
            int iMethodID = CPublicConfig::GetInstance()->GetMethodIDByName(strList.at(0));
            QStringList strParamList = strList.at(1).split(",");
            QVariantList qVarList;
            for(int i=0; i<strParamList.size(); i++)
                qVarList.push_back(strParamList.at(i).toInt());
            strCmd = GetJsonCmdString(iMethodID, qVarList);
            qDebug()<<QString("%1#%2:%3").arg(m_iUiMachineID + 1).arg(m_strMotorName).arg(strCmd);
            SendJsonCmd(m_iUiMachineID, iMethodID, strCmd);
        }
    }
    else
    {
        int iMethodID = CPublicConfig::GetInstance()->GetMethodIDByName(strText);
        strCmd = GetJsonCmdString(iMethodID);
        qDebug()<<QString("%1#%2:%3").arg(m_iUiMachineID + 1).arg(m_strMotorName).arg(strCmd);
        SendJsonCmd(m_iUiMachineID, iMethodID, strCmd);
    }
}

void CMotorCopmensate::_SlotMoveBtnList()
{
    QPushButton *pBtn = dynamic_cast<QPushButton *>(sender());
    if(nullptr == pBtn)
        return;

    int iMotor = m_iMotorIndex;
    if(5 == iMotor)
        iMotor = 2;
    QVariantList qVarList = {iMotor};

    int iBtn = pBtn->property("index").toInt();
    switch (iBtn)
    {
    case 0: qVarList << 0 << 1000; break;
    case 1: qVarList << 1 << 1000; break;
    case 2: qVarList << 0 << 500;  break;
    case 3: qVarList << 1 << 1000; break;
    case 4: qVarList << 0 << 300;  break;
    case 5: qVarList << 1 << 300;  break;
    default: break;
    }
    QString strCmd = GetJsonCmdString(Method_MOVE, qVarList);
    qDebug()<<QString("%1#%2,走指定步数:%3").arg(m_iUiMachineID + 1).arg(m_strMotorName).arg(strCmd);
    SendJsonCmd(m_iUiMachineID, Method_MOVE, GetJsonCmdString(Method_MOVE, qVarList));
}

void CMotorCopmensate::_SlotMoveDirBtn()
{
    if(m_pStepLineEdit->text().isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("请先填写步数"));
        return;
    }

    int iMotor = m_iMotorIndex;
    if(5 == iMotor)
        iMotor = 2;
    QVariantList qVarList = {iMotor, m_pDirComboBox->currentIndex(), m_pStepLineEdit->text().toInt()};
    QString strCmd = GetJsonCmdString(Method_MOVE, qVarList);
    qDebug()<<QString("%1#%2,走指定步数:%3").arg(m_iUiMachineID + 1).arg(m_strMotorName).arg(strCmd);
    SendJsonCmd(m_iUiMachineID, Method_MOVE, strCmd);
}

void CMotorCopmensate::_SlotBottomBtnList()
{
    QPushButton *pBtn = dynamic_cast<QPushButton *>(sender());
    if(nullptr == pBtn)
        return;

    int iBtn = pBtn->property("index").toInt();
    QVariantList qVarList = {m_iMotorIndex};
    switch (iBtn)
    {
    case 0: //读取
    {
        QVariant qVarData = QString::number(m_iMotorIndex);
        SendJsonCmd(m_iUiMachineID, Method_GCMP, GetJsonCmdString(Method_GCMP, qVarData));
        break;
    }
    case 1: //设置
    {
        QVariant qVarData = QString("%1;%2").arg(m_iMotorIndex).arg(_GetTableData());
        SendJsonCmd(m_iUiMachineID, Method_SCMP, GetJsonCmdString(Method_SCMP,qVarData));
        break;
    }
    case 2: //加一行
    {
        int iRow = m_pTableWidget->currentRow();
        if(iRow < 0)
            iRow = -1;
        iRow++;
        m_pTableWidget->insertRow(iRow);
        _ResortCompensateIndex();
        CLineEdit *pNameLineEdit = new CLineEdit;
        pNameLineEdit->setAlignment(Qt::AlignCenter);
        m_pTableWidget->setCellWidget(iRow, 2, pNameLineEdit);

        CLineEdit *pValueLineEdit = new CLineEdit;
        pValueLineEdit->setAlignment(Qt::AlignCenter);
        m_pTableWidget->setCellWidget(iRow, 3, pValueLineEdit);
        m_pTableWidget->selectRow(iRow);
        break;
    }
    case 3: //减一行
    {
        int iRow = m_pTableWidget->currentRow();
        if(iRow >= 0)
            m_pTableWidget->removeRow(iRow);
        _ResortCompensateIndex();
        break;
    }
    case 4:
    {
        m_pTableWidget->clearContents();
        m_pTableWidget->setRowCount(0);
        break;
    }
    case 5:
    {
        QList<QStringList> strList = CMotorDB::GetInstance().GetCompensateInfoByMotorIndex(m_iMotorIndex);
        qDebug()<<"从数据库加载电机补偿:"<<m_strMotorName<<strList;
        _LoadData2TableWidget(strList);
        break;
    }
    case 6:
    {
        //先删除之前的内容再保存
        CMotorDB::GetInstance().DeleteMotorCompensate(m_iMotorIndex);
        _SaveTableData2DB();
        break;
    }
    case 7:
    {
        int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定要恢复所有电机的补偿为出厂设置吗"));
        if(QMessageBox::Yes != iBtnType)
            return;

        SendJsonCmd(m_iUiMachineID, Method_RLCMP, GetJsonCmdString(Method_RLCMP));
        break;
    }
    default:
        break;
    }
}

QString CMotorCopmensate::_GetTableData()
{
    QStringList strList;
    for(int iRow=0; iRow<m_pTableWidget->rowCount(); iRow++)
    {
        QStringList one;

        QTableWidgetItem *pIndexItem = m_pTableWidget->item(iRow, 1);
        if(pIndexItem)
            one.push_back(pIndexItem->text());

        CLineEdit *pNameLineEdit = dynamic_cast<CLineEdit *>(m_pTableWidget->cellWidget(iRow, 2));
        if(pNameLineEdit)
            one.push_back(pNameLineEdit->text().trimmed());

        CLineEdit *pValueLineEdit = dynamic_cast<CLineEdit *>(m_pTableWidget->cellWidget(iRow, 3));
        if(pValueLineEdit)
            one.push_back(pValueLineEdit->text().trimmed());

        strList.push_back(one.join(SPLIT_IN_CMD));
    }
    return strList.join(SPLIT_BETWEEN_CMD);
}

void CMotorCopmensate::_LoadData2TableWidget(const QList<QStringList> &strList)
{
    m_pTableWidget->clearContents();
    m_pTableWidget->setRowCount(strList.size());
    for(int i=0; i<strList.size(); i++)
    {
        QStringList one = strList.at(i);
        if(one.size() < 3)
            continue;

        QTableWidgetItem *pIDItem = new QTableWidgetItem;
        pIDItem->setText(QString::number(i + 1));
        pIDItem->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(i, 0, pIDItem);

        QTableWidgetItem *pIndexItem = new QTableWidgetItem;
        pIndexItem->setText(one.at(0));
        pIndexItem->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(i, 1, pIndexItem);

        CLineEdit *pNameLineEdit = new CLineEdit(one.at(1));
        pNameLineEdit->setAlignment(Qt::AlignCenter);
        m_pTableWidget->setCellWidget(i, 2, pNameLineEdit);

        CLineEdit *pValueLineEdit = new CLineEdit(one.at(2));
        pValueLineEdit->setAlignment(Qt::AlignCenter);
        m_pTableWidget->setCellWidget(i, 3, pValueLineEdit);
    }
}

void CMotorCopmensate::_SaveTableData2DB()
{
    int iMotor = m_iMotorIndex;
    for(int iRow=0; iRow<m_pTableWidget->rowCount(); iRow++)
    {
        int iCompenIndex = 0;
        QString strName, strValue;

        QTableWidgetItem *pIndexItem = m_pTableWidget->item(iRow, 1);
        if(pIndexItem)
            iCompenIndex = pIndexItem->text().toInt();

        CLineEdit *pNameLineEdit = dynamic_cast<CLineEdit *>(m_pTableWidget->cellWidget(iRow, 2));
        if(pNameLineEdit)
            strName = pNameLineEdit->text().trimmed();

        CLineEdit *pValueLineEdit = dynamic_cast<CLineEdit *>(m_pTableWidget->cellWidget(iRow, 3));
        if(pValueLineEdit)
            strValue = pValueLineEdit->text().trimmed();

        CMotorDB::GetInstance().AddOneCompensate(iMotor, iCompenIndex, strName, strValue);
    }

}

void CMotorCopmensate::_ResortCompensateIndex()
{
    for(int i=0; i<m_pTableWidget->rowCount(); i++)
    {
        QTableWidgetItem *pIDItem = new QTableWidgetItem;
        pIDItem->setText(QString::number(i + 1));
        pIDItem->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(i, 0, pIDItem);

        QTableWidgetItem *pIndexItem = new QTableWidgetItem;
        pIndexItem->setText(QString::number(i));
        pIndexItem->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(i, 1, pIndexItem);
    }
}

void CMotorCopmensate::_InitWidget()
{
    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(80, 50);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMachineComboBoxChanged(int)));

    m_pMotorComboBox = new CLabelComboBox(tr("电机:"), CPublicConfig::GetInstance()->GetMotorNameList());
    m_pMotorComboBox->SetComboBoxMinSize(180, 50);
    connect(m_pMotorComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMotorComboBoxChanged(int)));

    m_pClearBtn = new QPushButton(tr("清零"));
    m_pClearBtn->setFixedSize(120, 50);
    connect(m_pClearBtn, &QPushButton::clicked, this, &CMotorCopmensate::_SlotClearBtn);

    m_pValueLineEdit = new CLineEdit;
    m_pValueLineEdit->setFixedSize(100, 50);

    m_pReadBtn = new QPushButton(tr("读取当前位置"));
    m_pReadBtn->setFixedSize(150, 50);
    connect(m_pReadBtn, &QPushButton::clicked, this, &CMotorCopmensate::_SlotReadBtn);

    m_pSetBtn = new QPushButton(tr("设置到当前行"));
    m_pSetBtn->setFixedSize(170, 50);
    connect(m_pSetBtn, &QPushButton::clicked, this, &CMotorCopmensate::_SlotSetBtn);

    QStringList strTitleList = {tr("序号"), tr("补偿索引"), tr("名称"), tr("数值")};
    m_pTableWidget = new QTableWidget;
    m_pTableWidget->setColumnCount(strTitleList.size());
    m_pTableWidget->setHorizontalHeaderLabels(strTitleList);

    QHeaderView* pVerticalHeader = m_pTableWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(50);
    QHeaderView* pHorizontalHeader = m_pTableWidget->horizontalHeader();
    pHorizontalHeader->resizeSection(0, 70);
    pHorizontalHeader->resizeSection(1, 150);
    pHorizontalHeader->resizeSection(3, 150);
    pHorizontalHeader->setSectionResizeMode(2, QHeaderView::Stretch);

    m_pTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pTableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_pTableWidget->setShowGrid(true);
    m_pTableWidget->setFocusPolicy(Qt::NoFocus);

    QString strQSS = "QLineEdit{border-radius: 0px; border: 1px solid #CAD2DC; font-size: 20px;}"
                     "QComboBox{border-radius: 0px; border: 1px solid #CAD2DC; font-size: 20px;}"
                     "QCheckBox::indicator{width: 35px; height: 35px; subcontrol-position:center  center;}";
    m_pTableWidget->setStyleSheet(strQSS);

    m_pMethodComboBox = new QComboBox();
    m_pMethodComboBox->setView(new QListView);
    m_pMethodComboBox->setFixedSize(120, 50);
    m_pMethodComboBox->addItems(m_strComboTextList.at(0));

    m_pExeBtn = new QPushButton(tr("执行"));
    m_pExeBtn->setFixedSize(120, 50);
    connect(m_pExeBtn, &QPushButton::clicked, this, &CMotorCopmensate::_SlotExeBtn);

    m_pLabel1 = new QLabel(m_strLabelTextList1.at(0));
    //m_pLabel1->setFixedSize(120, 50);
    m_pLabel1->setAlignment(Qt::AlignCenter);

    m_pLabel2 = new QLabel(m_strLabelTextList2.at(1));
    //m_pLabel2->setFixedSize(120, 50);
    m_pLabel2->setAlignment(Qt::AlignCenter);

    QStringList strList;
    strList << "+1000" << "-1000" << "+500" << "-500" << "+300" << "-300";
    for(int i=0; i<strList.size(); i++)
    {
        QPushButton *pBtn = new QPushButton(strList.at(i));
        pBtn->setFixedSize(120, 50);
        pBtn->setProperty("index", i);
        connect(pBtn, &QPushButton::clicked, this, &CMotorCopmensate::_SlotMoveBtnList);
        m_pMoveBtnList.push_back(pBtn);
    }

    m_pStepLineEdit = new CLineEdit;
    m_pStepLineEdit->setFixedSize(80, 50);
    m_pStepLineEdit->setPlaceholderText(tr("步数"));

    strList.clear();
    strList << "HOME" << "END";
    m_pDirComboBox = new QComboBox;
    m_pDirComboBox->setView(new QListView);
    m_pDirComboBox->addItems(strList);
    m_pDirComboBox->setFixedSize(80, 50);

    m_pMoveDirBtn = new QPushButton(tr("运行"));
    m_pMoveDirBtn->setFixedSize(130, 50);
    connect(m_pMoveDirBtn, &QPushButton::clicked, this, &CMotorCopmensate::_SlotMoveDirBtn);

    strList.clear();
    strList << tr("读取") << tr("设置") << tr("加一行") << tr("减一行")
            << tr("清空表格") << tr("加载数据库") << tr("保存数据库") << tr("恢复出厂");
    int iWidth = 130;
    if(eLanguage_German == gk_iLanguage)
        iWidth = 155;
    for(int i=0; i<strList.size(); i++)
    {
        QPushButton *pBtn = new QPushButton(strList.at(i));
        pBtn->setFixedSize(iWidth, 50);
        pBtn->setProperty("index", i);
        connect(pBtn, &QPushButton::clicked, this, &CMotorCopmensate::_SlotBottomBtnList);
        m_pBottomBtnList.push_back(pBtn);
    }
}

void CMotorCopmensate::_InitLayout()
{
    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(10);
    pTopLayout->addWidget(m_pMachineComboBox);
    pTopLayout->addSpacing(10);
    pTopLayout->addWidget(m_pMotorComboBox);
    pTopLayout->addSpacing(10);
    pTopLayout->addWidget(m_pClearBtn);
    pTopLayout->addSpacing(10);
    pTopLayout->addWidget(m_pValueLineEdit);
    pTopLayout->addWidget(m_pReadBtn);
    pTopLayout->addSpacing(10);
    pTopLayout->addWidget(m_pSetBtn);
    pTopLayout->addStretch(1);

    QGridLayout *pGridLayout = new QGridLayout;
    pGridLayout->setMargin(0);
    pGridLayout->setVerticalSpacing(20);
    pGridLayout->setHorizontalSpacing(10);
    pGridLayout->addWidget(m_pMethodComboBox, 0, 0);
    pGridLayout->addWidget(m_pExeBtn, 0, 1);
    pGridLayout->addWidget(m_pLabel1, 1, 0);
    pGridLayout->addWidget(m_pLabel2, 1, 1);
    for(int i=0; i<m_pMoveBtnList.size(); i++)
    {
        pGridLayout->addWidget(m_pMoveBtnList.at(i), (i / 2) + 2, i % 2);
    }

    QHBoxLayout *pMoveLayout = new QHBoxLayout;
    pMoveLayout->setMargin(0);
    pMoveLayout->setSpacing(20);
    pMoveLayout->addWidget(m_pStepLineEdit);
    pMoveLayout->addWidget(m_pDirComboBox);
    pMoveLayout->addWidget(m_pMoveDirBtn);

    QVBoxLayout *pRightLayout = new QVBoxLayout;
    pRightLayout->setMargin(0);
    pRightLayout->addLayout(pGridLayout);
    pRightLayout->addSpacing(20);
    pRightLayout->addLayout(pMoveLayout);

    QHBoxLayout *pMidLayout = new QHBoxLayout;
    pMidLayout->setMargin(0);
    pMidLayout->addWidget(m_pTableWidget);
    pMidLayout->addSpacing(20);
    pMidLayout->addLayout(pRightLayout);

    QHBoxLayout *pBottomBtnLayout = new QHBoxLayout;
    pBottomBtnLayout->setMargin(0);
    pBottomBtnLayout->setSpacing(10);
    for(int i=0; i<m_pBottomBtnList.size(); i++)
        pBottomBtnLayout->addWidget(m_pBottomBtnList.at(i));
    pBottomBtnLayout->addStretch(1);

    QVBoxLayout *pMainLayout = new QVBoxLayout;
    pMainLayout->setMargin(0);
    pMainLayout->setSpacing(10);
    pMainLayout->addLayout(pTopLayout);
    pMainLayout->addLayout(pMidLayout);
    pMainLayout->addLayout(pBottomBtnLayout);
    this->setLayout(pMainLayout);
}
