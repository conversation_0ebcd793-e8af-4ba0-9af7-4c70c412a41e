#include "CHomeDevGroupWidget.h"
#include <QDebug>
#include <QBoxLayout>
#include <QRandomGenerator>

CHomeDevGroupWidget::CHomeDevGroupWidget(const QString &strDevName, const SDevParamsStruct &sDevParams,
                                         const QList<int> &iMachineIDList, QWidget *parent)
    : QWidget(parent)
    , m_strDevName(strDevName)
    , m_sDevParams(sDevParams)
    , m_iMachineIDList(iMachineIDList)
{
    this->setFixedSize(m_sDevParams.iGroupWidth, m_sDevParams.iGroupHeight);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroupBox());
    pLayout->addStretch(1);
    this->setLayout(pLayout);

    //for(int i=0; i<m_pDevItemWidgetList.size(); i++)
    //{
        //int s = QRandomGenerator::global()->bounded(1, 7) - 1;
       // m_pDevItemWidgetList.at(i)->SetDeviceStatus(DeviceStatus(s));
    //}
}

void CHomeDevGroupWidget::SetCardSampleInfo(int iMachineID, const SCardInfoStruct &sCardInfo, const SSampleInfoStruct &sSampleInfo)
{
    int iItem = iMachineID % 4;
    if(iItem < m_pDevItemWidgetList.size())
        m_pDevItemWidgetList.at(iItem)->SetCardSampleInfo(sCardInfo, sSampleInfo);
}

void CHomeDevGroupWidget::SetFLData(int iMachineID, const QList<QMap<double, double> > &dFLMap)
{
    int iItem = iMachineID % 4;
    if(iItem < m_pDevItemWidgetList.size())
        m_pDevItemWidgetList.at(iItem)->SetFLDataMap(iItem,dFLMap);
}

QGroupBox *CHomeDevGroupWidget::_CreateGroupBox()
{
    QHBoxLayout *pItemLayout = new QHBoxLayout;
    pItemLayout->setMargin(0);
    pItemLayout->setSpacing(24);
    pItemLayout->addStretch(1);
    for(int i=0; i<m_iMachineIDList.size(); i++)
    {
        CHomeDevItemWidget *pDevItemWidget = new CHomeDevItemWidget(m_iMachineIDList.at(i), m_sDevParams);
        connect(pDevItemWidget, &CHomeDevItemWidget::SignalCreateTest, this, &CHomeDevGroupWidget::SignalCreateTest);
        m_pDevItemWidgetList.push_back(pDevItemWidget);
        pItemLayout->addWidget(pDevItemWidget);
    }
    pItemLayout->addStretch(1);

    m_pTitleWidget = new CHLabelTitleWidget(m_strDevName);

    QHBoxLayout *pTitleLayout = new QHBoxLayout;
    pTitleLayout->setMargin(0);
    pTitleLayout->setSpacing(0);
    pTitleLayout->addSpacing(24);
    pTitleLayout->addWidget(m_pTitleWidget);
    pTitleLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addSpacing(16);
    pLayout->addLayout(pTitleLayout);
    pLayout->addStretch(1);
    pLayout->addLayout(pItemLayout);
    pLayout->addStretch(1);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(m_sDevParams.iGroupWidth, m_sDevParams.iGroupHeight);
    pGroupBox->setObjectName("DevGroupBox");
    pGroupBox->setLayout(pLayout);

    return pGroupBox;
}
