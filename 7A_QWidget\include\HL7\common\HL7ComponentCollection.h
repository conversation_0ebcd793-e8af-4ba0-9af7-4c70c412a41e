﻿#ifndef _HL7_COMPONENT_COLLECTION_H_
#define _HL7_COMPONENT_COLLECTION_H_

#include "HL7Component.h"
#include "../interface/base.h"
#include <vector>
class HL7ComponentCollection
{
public:
	HL7ComponentCollection();
	~HL7ComponentCollection();


	/*
	*@brief 在末尾添加成分元素
	*/
	void AddComponent(HL7Component component);

	/*
	*@brief 在index位置插入成分元素，合法的index从0开始
	*/
	void AddComponent(HL7Component component, std::size_t index);

	/*
	*@brief 删除指定位置的成分元素,合法的index从0开始,并返回是否删除成功
	*/
	bool DeleteComponent(HL7Component component, std::size_t index);

	/*
	*@brief 返回成分集合
	*/
	std::vector<HL7Component> GetComponentVector();


	void ClearComponent();

private:
	std::vector<HL7Component> m_componentVect;
};
#endif
