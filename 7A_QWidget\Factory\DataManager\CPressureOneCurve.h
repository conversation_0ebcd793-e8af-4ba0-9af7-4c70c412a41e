#ifndef CPRESSUREONEINFO_H
#define CPRESSUREONEINFO_H

#include <QWidget>
#include <QDateTime>
#include <QPushButton>
#include "qcustomplot.h"

class CSetChartXYRange;

class CPressureOneCurve : public QWidget
{
    Q_OBJECT
public:
    explicit CPressureOneCurve(int iMachineID, QWidget *parent = nullptr);

    void ClearData();
    void ExportData();
    void TestEnd();
    void ReceiveData(const QVariant &qVarData);
    void GetData(QVector<double> &dTimeVec, QVector<double> &dPreValueVec1, QVector<double> &dPreValueVec2);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual void hideEvent(QHideEvent *pEvent) override;

private slots:
    void _SlotSetXYRange(const QStringList &strList);
    void _SlotPlotClicked(QCPAbstractPlottable *pPlottable, int iDataIndex, QMouseEvent *pEvent);

private:
    void _InitWidget();
    void _InitCustomPlot();
    void _AddGraph(QCustomPlot *pCustomPlot, QColor penColor, QColor pointColor, int iChart, QString strChartName);

private:
    bool m_bShow;
    bool m_bReplot;
    const int m_iMachineID;
    QCustomPlot *m_pCustomPlot;
    QCPItemText *m_pCPItemText;
    CSetChartXYRange *m_pCSetChartXYRange;

    QDateTime m_beginDateTime;
    QVector<double> m_dTimeVec, m_dPreValueVec1, m_dPreValueVec2;
};

#endif // CPRESSUREONEINFO_H
