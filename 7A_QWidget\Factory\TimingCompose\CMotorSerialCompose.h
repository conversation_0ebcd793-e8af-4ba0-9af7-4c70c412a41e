#ifndef CADDMOTORCOMPOSE_H
#define CADDMOTORCOMPOSE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-12-12
  * Description: 电机串行组合,可加可减
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QComboBox>
#include <QPushButton>
#include <QBoxLayout>
#include <QGroupBox>

#include "CLineEdit.h"

class CMotorSerialCompose : public QWidget
{
    Q_OBJECT
public:
    explicit CMotorSerialCompose(QWidget *parent = nullptr);
    ~CMotorSerialCompose();

    void SetMotorComboBoxList(const QStringList &strList);
    void ShowWithData(const QString &strData);

protected:
    void paintEvent(QPaintEvent* pEvent) override;

signals:
    void SignalMotorComposeConfirm(const QString &);

private slots:
    void _SlotBtnList();

private:
    int _GetCmdTextIDIndex(const QString &strCmdTextID);
    QGroupBox *_CreateGroup();
    void _AddOneLine(int iCHIndex = 0, const QString &strParam = "");

private:
    QVBoxLayout *m_pBoxLayout;
    QList<QComboBox *> m_pComboBoxList;
    QList<CLineEdit *> m_pLineEditList;
    QList<QPushButton *> m_pBtnList;

    QStringList m_strMotorTextIDList;
};

#endif // CADDMOTORCOMPOSE_H
