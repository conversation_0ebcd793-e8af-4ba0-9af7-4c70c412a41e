QWidget#KeyBoard
{
   background-color:&bkgdColor;
}
QStackedWidget
{
   /*background-color: red;*/
}
QLineEdit
{
   background-color: &btnColor;
   color:&textColor;
   font-size:&fontSizepx;
   font-family:"&fontFamily";
   border-radius:3px;
}
QTextEdit
{
   background-color:&btnColor;
   color:&textColor;
   font-size:&fontSizepx;
   font-family:"&fontFamily";
   border-radius:3px;
}
QLabel
{
   background-color:&btnColor;
   color:&textColor;
   font-family:"&fontFamily";
   font-size:&fontSizepx;
}
QListView
{
   background-color:&btnColor;
   color:&textColor;
   font-family:"&fontFamily";
   font-size:&listViewfontSizepx;
   border-radius:3px;
}
QToolButton
{
   background-color:&btnColor;
   color:&textColor;
   font-family:"&fontFamily";
   font-size:&fontSizepx;
   border-radius:3px;
}
QToolButton:pressed
{
   background-color:&btnColor;
   color:&pressedColor;
   font-size:&fontSizepx;
   border-radius:3px;
}
QToolButton:hover
{

}
QToolButton#capsLockBtn
{
   image: url(:/images/icon_small_normal.png);
}
QToolButton#capsLockBtn:checked
{
   image: url(:/images/icon_big.png);
}
QToolButton#toolButton_Return
{
   /*image: url(:/images/back1.png);*/
}
QToolButton#toolButton_Return:pressed
{
   /*image: url(:/images/back2.png);*/
}
QToolButton#toolButton_Hide
{
   /*image: url(:/svg/esc.svg);*/
}
QToolButton#toolButton_Hide:pressed
{
   /*image: url(:/svg/esc.svg);*/
}
QToolButton#toolButton_backspace
{
   /*image: url(:/svg/backspace.svg);*/
}
QToolButton#toolButton_backspace:pressed
{
   /*image: url(:/svg/backspace.svg);*/
}
QToolButton#toolButton_hide
{
   /*image: url(:/svg/esc.svg);*/
}
QToolButton#toolButton_hide:pressed
{
   /*image: url(:/svg/esc.svg);*/
}

QToolButton#toolButton_PageUp
{
   /*image: url(:/images/ch_up1.png);*/
}
QToolButton#toolButton_PageUp:pressed
{
   /*image: url(:/image/keypad/ch_up2.png);*/
}
QToolButton#toolButton_PageDown
{
   /*image: url(:/images/ch_down1.png);*/
}
QToolButton#toolButton_PageDown:pressed
{
   /*image: url(:/images/ch_down2.png);*/
}
QToolButton#toolButton_prevPage
{
   image: url(:/images/br_left.png);
}
QToolButton#toolButton_prevPage:disabled
{
   image: none;
}
QToolButton#toolButton_nextPage
{
   image: url(:/images/br_right.png);
}
QToolButton#toolButton_nextPage:disabled
{
   image: none;
}
