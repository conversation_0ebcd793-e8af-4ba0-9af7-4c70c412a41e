#ifndef CTHREEPARAMS_H
#define CTHREEPARAMS_H

#include <QWidget>
#include <QGroupBox>
#include <QPushButton>

#include "CLabelLineEdit.h"

class CThreeParams : public QWidget
{
    Q_OBJECT
public:
    explicit CThreeParams(QWidget *parent = nullptr);
    ~CThreeParams();

    void Show(int iRow, const QString &strRawParams);

protected:
    void paintEvent(QPaintEvent* pEvent) override;

signals:
    void SignalParamsConfirm(int iRow, const QString &strParams);

private slots:
    void _SlotConfirmBtn();
    void _SlotCancelBtn();

private:
    QGroupBox *_CreateGroup();

private:
    CLabelLineEdit *m_pTimeLineEidt, *m_pInLineEdit, *m_pDeLineEdit;
    QPushButton *m_pConfirmBtn, *m_pCancelBtn;
    int m_iRow;
};

#endif // CTHREEPARAMS_H
