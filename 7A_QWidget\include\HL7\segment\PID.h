﻿#ifndef _PID_H_
#define _PID_H_

#include "../interface/base.h"
#include "../interface/IPID.h"
#include "../common/HL7Segment.h"
#include "../common/HL7Field.h"
#include "../StringUtil.h"
using namespace Utility;
class PID : public HL7Segment, public IPID
{
public:
	PID();
	virtual ~PID();
	DECLARE_OBJECTBASE

	BEGIN_IMPL_QUERYIF(PID)
		IMPL_QUERYIF_BYPATH(IF_OBJECTBASE, IHL7Segment, IObjectBase)
		IMPL_QUERYIF(IF_HL7SEGMENT, IHL7Segment)
		IMPL_QUERYIF(IF_PID, IPID)
		END_IMPL_QUERYIF()
	/*
	 *	\brief 设置Id-PID 递增ID
	 */
	void SetPIDIndex(const char* pidIndex);
	
	void GetPIDIndex(char** pidIndex);
	/*
	*	\brief 设置身份证
	*/
	void SetID(const char* id);

	void GetID(char** id);

	/*
	*	\brief 设置病历号
	*/
	void SetCardNum(const char* cardNum);

	void GetCardNum(char** cardNum);
	/*
	 *	\brief 设置住院号
	 */
	void SetAdmissionNum(const char* admissionNo);

	void BuildPID();

	void GetAdmissionNum(char** admissionNum);
	/*
	 *	\brief 设置床号
	 */
	void SetBed(const char* bed);

	void GetBed(char** bed);
	/*
	 *	\brief 患者姓名
	 */
	void SetName(const char* name);

	void GetName(char** name);
	/*
	 *	\brief 出生日期
	 */
	void SetBirthDate(const char* age);

	void GetBirthDate(char** birthDate);
	/*
	 *	\brief 性别
	 */
	void SetSex(const char* sex);

	void GetSex(char** sex);
	/*
	 *	\brief 血型
	 */
	void SetBloodType(const char* bloodType);

	void GetBloodType(char** bloodType);
    /*
     *	\brief 种族
     */
    void SetRace(const char* race);

    void GetRace(char** race);
	/*
	 *	\brief 地址
	 */
	void SetAddress(const char* address);

	void GetAddress(char** address);

	/*
	 *	\brief 家庭号码
	 */
	void SetHomePhoneNum(const char* homePhoneNum);

	void GetHomePhoneNum(char** homePhoneNum);
	/*
	 *	\brief 手机号码
	 */
	void SetTelephoneNum(const char* telephoneNum);

	void GetTelephoneNum(char** telephoneNum);
	/*
	 *	\brief 社保号
	 */
	void SetSSNNum(const char* ssnNum);

	void GetSSNNum(char** ssnNum);
	/*
	 *	\brief 民族
	 */
	void SetNation(const char* nation); 

	void GetNation(char** nation);
	/*
	 *	\brief 籍贯
	 */
	void SetNativePlace(const char* nativePlace);

	void GetNativePlace(char** nativePlace);

	virtual void Parse(const char* segmentStr, EncodingCharacters encodingChars);

	void ParsePID();

	PID& operator=(PID& pid);

private:
	HL7Component m_cardNum;//病历号
	HL7Component m_admissionNum;//住院号
	HL7Component m_bedNum;//床号
	HL7Field m_pidIndex;//1patient id index
	HL7Field m_id;//2身份证号
	HL7Field m_pid;//3patient id 病历号^住院号^床号组成，如果其中某项为空连接符^依然存在
	HL7Field m_name;//5
	HL7Field m_birthDate;//7出生日期YYYYMMDDHHMMSS
	HL7Field m_sex;//8性别
	HL7Field m_bloodType;//9血型
    HL7Field m_race;//10种族
	HL7Field m_address;//11患者地址
	HL7Field m_homePhoneNum;//13家中电话号码
	HL7Field m_telephoneNum;//14移动电话号码
	HL7Field m_ssnNum;//社保号19
	HL7Field m_nation;//民族22
	HL7Field m_nativePlace;//籍贯23
};

REGISTER_CLASS(PID);
#endif

