#ifndef CHRMCURVE_H
#define CHRMCURVE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-11-14
  * Description: HRM-曲线
  * -------------------------------------------------------------------------
  * History: 20241205 hxr 横坐标改为数据个数
  * 20250415 改成滚动显示100个
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QDateTime>
#include <QCheckBox>
#include <QPushButton>
#include <QStackedWidget>

#include "CCmdBase.h"
#include "CLabelComboBox.h"
#include "CSetChartXYRange.h"

class QCustomPlot;
class QCPItemText;

class CHrmCurve : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CHrmCurve(QWidget *parent = nullptr);
    ~CHrmCurve();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

public slots:
    void SlotClearData(int iMachineID);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual void hideEvent(QHideEvent *pEvent) override;

private slots:
    void _SlotExportBtn();
    void _SlotClearBtn();
    void _SlotSetXYRange(const QStringList &strRangeList);
    void _SlotFLCheckBox(bool bChecked);
    void _SlotMachineChange(int iMachineID);

private:
    void _HandlePCRInfo(int iMachineID, const QVariant &qVarData);
    void _UpdateMachinePlot(int iMachineID);
    void _HandelFLCDTData(int iMachineID, const QVariant &qVarData);
    void _TestEnd(int iMachineID);

private:
    void _InitWidget();
    QCustomPlot *_InitCustomPlot();
    QCPItemText *InitCPItemText(QCustomPlot *pCustomPlot);
    void _AddGraph(QCustomPlot *pCustomPlot, QColor penColor, QColor pointColor, int iChart, QString strChartName);

private:
    typedef struct _SHrmCurveStruct
    {
        _SHrmCurveStruct()
        {
            bReplot = false;
            bFLChecked = false;
            dMaxValue = 110;
            pCustomPlot = nullptr;
            pCPItemText = nullptr;
            strRangeList<<"0"<<"100"<<"0"<<"110";
        }

        bool bReplot;
        bool bFLChecked;
        double dMaxValue;
        QCustomPlot *pCustomPlot;
        QCPItemText *pCPItemText;
        QStringList strRangeList;
        QVector<double> dTimeVec;
        QVector<double> dM1TempVec, dM2TempVec; //实时的数据
        QVector<double> dY1Vec, dY2Vec; //滚动显示的数据
        QDateTime qBeginTime;
        QVector<double> dFLVec;
    }SHrmCurveStruct;

private:
    bool m_bShow;
    QStackedWidget *m_pStackWidget;
    QPushButton *m_pExportBtn, *m_pClearBtn;
    CSetChartXYRange *m_pCSetChartXYRange;
    QCheckBox *m_pFLCheckBox;
    CLabelComboBox *m_pMachineComboBox;
    QList<SHrmCurveStruct *> m_sCurveUiList;
};

#endif // CHRMCURVE_H
