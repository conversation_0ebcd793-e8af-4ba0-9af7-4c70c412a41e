#ifndef CLOGINWIDGET_H
#define CLOGINWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-14
  * Description: 登录界面
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QEvent>
#include <QLabel>
#include <QGroupBox>
#include <QPushButton>

#include "PublicParams.h"
#include "CUserNameComboBox.h"
#include "CPasswordLineEdit.h"
#include "algorithmdescrypt.h"

class CLoginWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CLoginWidget(QWidget *parent = nullptr);

signals:
    void SignalLoginOK();

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual bool eventFilter(QObject *pWatched, QEvent *pEvent) override;

private slots:
    void _SlotUserTextChanged(const QString &strText);
    void _SlotLoginBtn();

private:
    void _LoginOK(const QString &strUser, const QString &strPassword);
    void _CodeFactoryPassword();
    void _CodeAdminPassword();
    void _SaveLoginLog(ELOGINTYPE eLogType, QString strUserName, QString strMachineCode = "");
    void _SaveOperationLog(QString strUser, QString strLog);

private:
    QGroupBox *_CreateGroupBox();
    void _InitWidget();
    void _InitLayout();

private:
    QLabel *m_pTitleLabel1, *m_pTitleLabel2;
    QLabel *m_pLogoLabel;
    CUserNameComboBox *m_pUserNameComboBox;
    CPasswordLineEdit *m_pPasswordLineEdit, *m_pPasswordLineEdit2;
    QPushButton *m_pLoginBtn;
    QLabel *m_pInfoLabel, *m_pCodeLabel;
    QLabel *m_pVersionLabel;
    AlgorithmDesCrypt *m_pAlgorithmDesCrypt;

    bool m_bDynamicPassword;
    int m_iAdminErrTimes; //admin登录错误次数
    QString m_strCodeAdmin, m_strCodeFactory; //Admin和factroy的动态密码
};
#endif // CLOGINWIDGET_H
