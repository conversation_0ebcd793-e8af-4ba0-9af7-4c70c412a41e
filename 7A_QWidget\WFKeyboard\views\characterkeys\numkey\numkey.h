#ifndef NUMKEY_H
#define NUMKEY_H

#include "../characterbasekey.h"

class Numkey : public CharacterBaseKey
{
    Q_OBJECT    
public:
    explicit Numkey(KeyBoard *parent, QStackedWidget *stackedWidget);

    ~Numkey();

    void Translate(const QString& space);

protected:
    void changeEvent(QEvent *event);

private slots:
    void SlotGotoSymbol();

private:
    class PrivateData;
    PrivateData *const md;
};

#endif // NUMKEY_H
