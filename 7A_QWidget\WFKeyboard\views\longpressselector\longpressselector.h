#ifndef LONGPRESSSELECTOR_H
#define LONGPRESSSELECTOR_H

#include <QWidget>
#include <QButtonGroup>

class LongPressSelector : public QWidget
{
    Q_OBJECT

public:
    explicit LongPressSelector(QWidget *parent = 0);

    ~LongPressSelector();

     QList<QAbstractButton*> GetAllButtons();

     void SetAlternativeText(QString);

private:
    class PrivateData;
    PrivateData *const md;
};

#endif // LONGPRESSSELECTOR_H
