#ifndef CPNICODERESULT_H
#define CPNICODERESULT_H

#include <QList>
#include<QString>

#if defined(CPNICODERESULT_LIBRARY)
#define CPNICODERESULT_EXPORT Q_DECL_EXPORT
#else
#  define CPNICODERESULT_EXPORT Q_DECL_IMPORT
#endif

struct SPNIParamStruct
{
    SPNIParamStruct()
    {

    };
    void init()
    {
        m_strCt.clear();
        m_strProjectName.clear();
        m_strHoleName.clear();
        m_strCtCutoffValue.clear();
        m_strQCTestModel.clear();
        m_strQCCtCutoff.clear();
        m_b3PNMethod = true;
    };
    QString m_strCt;
    QString m_strProjectName;
    QString m_strHoleName;
    QString m_strQCTestModel;
    QString m_strQCCtCutoff;
    bool m_b3PNMethod{true};
    QString m_strCtCutoffValue;
};
class CPNICodeResult
{
public:
    CPNICodeResult();

public:
    QString GetResultFromCTValue(const SPNIParamStruct& sPNIParamStruct);

private:

};

#endif // CPNICODERESULT_H

