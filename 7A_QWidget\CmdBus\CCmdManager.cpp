#include "CCmdManager.h"
#include "CCmdBase.h"
#include "CCanBusThread.h"
#include "CSerialThread.h"
#include "CHeartBeat.h"
#include "PublicFunction.h"

#include "CRunTest.h"
#include "CRegisterDB.h"
#include <QApplication>

extern bool G_RegisterMode;

CCmdManager *CCmdManager::m_spInstance = nullptr;

CCmdManager *CCmdManager::GetInstance()
{
    if(nullptr == m_spInstance)
        m_spInstance = new CCmdManager;
    return m_spInstance;
}

void CCmdManager::FreeInstance()
{
    if(nullptr != m_spInstance)
    {
        delete m_spInstance;
        m_spInstance = nullptr;
    }
}

/*******************************************************************
* @brief: 注册,先注册的排在value list最后1个,注意start命令的注册
* @param:
* @return:
* @author: hxr
*******************************************************************/

void CCmdManager::Register2Map(int iMethodID, CCmdBase *pClass)
{
    if(m_cmdObjMap.contains(iMethodID))
    {
        QList<CCmdBase *> pList = m_cmdObjMap.values(iMethodID);
        for(int i=0; i<pList.size(); i++)
        {
            if(pClass == pList.at(i))
            {
                qDebug()<<"重复注册:"<<iMethodID<<pClass<<pClass->GetClassName();
                return;
            }
        }
    }

    m_cmdObjMap.insertMulti(iMethodID, pClass);
}

void CCmdManager::UnRegister2Map(int iMethodID, CCmdBase *pClass)
{
    if(!m_cmdObjMap.contains(iMethodID))
        return;
    m_cmdObjMap.remove(iMethodID, pClass);
}

void CCmdManager::_SlotTimeout()
{
    QList<QByteArray> qFrameList;
#ifndef __aarch64__
    qFrameList = CSerialThread::GetInstance()->GetReciveCanMsgList();
#else
    qFrameList = CCanBusThread::GetInstance()->GetReciveCanMsgList();
#endif
    if(qFrameList.isEmpty())
        return;

    for(int i=0; i<qFrameList.size(); i++)
    {
        QByteArray byteFrame = qFrameList.at(i);
        qDebug()<<Q_FUNC_INFO<<byteFrame;

        QByteArray byteLen = byteFrame.mid(18, 2);
        char ch = byteLen[0];
        byteLen[0] = byteLen[1];
        byteLen[1] = ch;
        int iLen = byteLen.toHex().toInt(nullptr, 16);

        QByteArray bytePayload = byteFrame.mid(20, iLen);
        if(0x00 == bytePayload[iLen - 1])
            bytePayload = bytePayload.left(iLen - 1);

        int iMachineID = byteFrame.mid(5, 1).toHex().toInt(nullptr, 16);
        int iMethodID = -1;
        int iResult = -1;
        QVariant qVarData;
        CHeartBeat::GetInstance()->ResetStatus(iMachineID);

        //提取日志特殊指令特殊处理
        if(0x05 == byteFrame[12])
        {
            int iPackID = bytePayload.mid(0, 2).toHex().toInt(nullptr, 16);
            int iLen = bytePayload.length();
            QByteArray bytePackData = bytePayload.mid(2, iLen-2);
            //qDebug()<<"Log Data:"<<bytePackData;
            CPublicConfig::GetInstance()->SignalGetLogData(iMachineID, iPackID, bytePackData);
            continue;
        }
        // 新增状态信息解析 (0x02协议)
        else if(0x02 == byteFrame[12])
        {
            qDebug()<<Q_FUNC_INFO<<__LINE__;
            ParsedStatus status;

            if(ProtocolParser::parseStatusInfo(bytePayload, status))
            {
                if(status.hasVM())
                {
                    // 构造参数数组
                    QVariantList paramsList;
                    paramsList.append(status.timingStep);
                    iMethodID = Method_timing_step;
                    QList<CCmdBase *> objList = m_cmdObjMap.values(iMethodID);
                    for(int i=0; i<objList.size(); i++)
                    {
                        CCmdBase *pObj = objList.at(i);
                        //qDebug()<<Q_FUNC_INFO<<__LINE__;
                        pObj->receiveMachineCmdReplay(iMachineID, iMethodID, 0, paramsList);
                        //qDebug()<<Q_FUNC_INFO<<__LINE__;
                    }
                }
                if(status.hasPcr())
                {
                    // 构造PCR参数对象
                    QVariantMap paramsMap;
                    paramsMap["RunStep"] = status.pcrInfo.runStep;
                    paramsMap["RunCir"] = status.pcrInfo.runCir;
                    paramsMap["TargetTemp"] = status.pcrInfo.targetTemp;
                    paramsMap["Module1Temp"] = status.pcrInfo.module1Temp;
                    paramsMap["Module2Temp"] = status.pcrInfo.module2Temp;
                    iMethodID = Method_pcr_info;

                    QList<CCmdBase *> objList = m_cmdObjMap.values(iMethodID);
                    for(int i=0; i<objList.size(); i++)
                    {
                        CCmdBase *pObj = objList.at(i);
                        //qDebug()<<Q_FUNC_INFO<<__LINE__;
                        pObj->receiveMachineCmdReplay(iMachineID, iMethodID, 0, paramsMap);
                        //qDebug()<<Q_FUNC_INFO<<__LINE__;
                    }
                }
                if(status.hasHT())
                {
                    QString paramsStr = QString("1,%1,0;2,%2,0;3,%3,0;4,%4,0")
                            .arg(status.htInfo.moduleTemp[0])
                            .arg(status.htInfo.moduleTemp[1])
                            .arg(status.htInfo.moduleTemp[2])
                            .arg(status.htInfo.moduleTemp[3]);
                    iMethodID = Method_ht_info;

                    QList<CCmdBase *> objList = m_cmdObjMap.values(iMethodID);
                    for(int i=0; i<objList.size(); i++)
                    {
                        CCmdBase *pObj = objList.at(i);
                        //qDebug()<<Q_FUNC_INFO<<__LINE__;
                        pObj->receiveMachineCmdReplay(iMachineID, iMethodID, 0, paramsStr);
                        //qDebug()<<Q_FUNC_INFO<<__LINE__;
                    }
                }
                if(status.hasPressure())
                {
                    QVariantList paramsList;
                    paramsList.append(status.pressure);
                    iMethodID = Method_pressure_info;
                    qVarData = paramsList;  // 赋值给统一参数变量

                    QList<CCmdBase *> objList = m_cmdObjMap.values(iMethodID);
                    for(int i=0; i<objList.size(); i++)
                    {
                        CCmdBase *pObj = objList.at(i);
                        //qDebug()<<Q_FUNC_INFO<<__LINE__;
                        pObj->receiveMachineCmdReplay(iMachineID, iMethodID, 0, paramsList);
                        //qDebug()<<Q_FUNC_INFO<<__LINE__;
                    }
                }

            }
            else
            {
                qDebug() << "statusInfoData状态协议解析失败"<< bytePayload.toHex();
            }
            qDebug()<<Q_FUNC_INFO<<__LINE__;
            continue;
        }

        //iMachineID = 0;
        //bytePayload = "{\"id\":45,\"method\":\"gas_info\",\"result\":0,\"params\":[-9121.547318]}";

        QJsonParseError err;
        QJsonDocument doc = QJsonDocument::fromJson(bytePayload, &err);
        if(QJsonParseError::NoError != err.error)
        {
            qDebug()<<Q_FUNC_INFO<<__LINE__<<"ERR:接收内容不是JSON"<<bytePayload;
            continue;
        }

        QJsonObject rootObj = doc.object();
        if(rootObj.contains("id"))
            iMethodID = rootObj.value("id").toInt();
        if(rootObj.contains("result"))
            iResult = rootObj.value("result").toInt();

        if(rootObj.contains("params"))
        {
            QJsonValue paramsValue = rootObj.value("params");
            qVarData = paramsValue.toVariant();
        }

        if(Method_fl_data == iMethodID)
        {
            if(!_RegisterFLMode(iMachineID, qVarData))
                continue;
        }

        QList<CCmdBase *> objList = m_cmdObjMap.values(iMethodID);
        for(int i=0; i<objList.size(); i++)
        {
            CCmdBase *pObj = objList.at(i);
            //qDebug()<<Q_FUNC_INFO<<__LINE__;
            pObj->receiveMachineCmdReplay(iMachineID, iMethodID, iResult, qVarData);
            //qDebug()<<Q_FUNC_INFO<<__LINE__;
        }
    }
}

bool CCmdManager::_RegisterFLMode(int iMachineID, QVariant &qVarRawData)
{
    if(G_RegisterMode)
    {
        QString strCardID = CRunTest::GetInstance()->GetRunInfoStruct(iMachineID).sResultInfo.strCardID;
        QString strFL;
        CRegisterDB::GetInstance()->GetFLData(strCardID, strFL);
        if(!strFL.isEmpty())
        {
            QStringList strRawList = qVarRawData.toString().split(";");
            if(strRawList.size() >= 3)
            {
                QStringList strFirstList = strRawList.first().split(",");
                if(strFirstList.size() >= 2)
                {
                    int iCycle = strFirstList.at(0).toInt();
                    QStringList strDBFLList = strFL.split(";");
                    if(iCycle >= strDBFLList.size())
                        return false;

                    double dTemp = strFirstList.at(1).toDouble();
                    if(0 == dTemp)
                    {
                        QStringList strCircleList = strDBFLList.at(iCycle).split(",");
                        if(strCircleList.size() < 8)
                        {
                            qDebug()<<Q_FUNC_INFO<<"没有8个数据";
                            return false;
                        }

                        QString strHole1 = QString("0,%1,%2,%3,%4")
                                .arg(strCircleList.at(0)).arg(strCircleList.at(1))
                                .arg(strCircleList.at(2)).arg(strCircleList.at(3));
                        strHole1.remove(" ");

                        QString strHole2 = QString("1,%1,%2,%3,%4")
                                .arg(strCircleList.at(4)).arg(strCircleList.at(5))
                                .arg(strCircleList.at(6)).arg(strCircleList.at(7));
                        strHole2.remove(" ");

                        QString strNewParam = QString("%1,0;%2;%3").arg(iCycle).arg(strHole1).arg(strHole2);
                        qDebug()<<"注册模式修改fl:"<<strCardID<<qVarRawData.toString()<<strNewParam;
                        qVarRawData = strNewParam;
                    }
                }
            }
        }
    }

    return true;
}

CCmdManager::CCmdManager(QObject *parent) : QObject(parent)
{
    m_pTimer = new QTimer(this);
    connect(m_pTimer, &QTimer::timeout, this, &CCmdManager::_SlotTimeout);
    m_pTimer->start(10);
}

CCmdManager::~CCmdManager()
{
    if(nullptr == m_pTimer)
    {
        m_pTimer->stop();
        m_pTimer->deleteLater();
    }
}

QVariantList CCmdManager::_Makedata(QList<bool> bPnList)
{
    QString str11 ;
    QString str12 ;
    QString str13 ;
    QString str14 ;

    QString str21 ;
    QString str22 ;
    QString str23 ;
    QString str24 ;
    if (CPublicConfig::GetInstance()->GetCalcParam())
    {
        // 从数据库txt读取 ，也可以注册模式
        QVariantMap qVarMap;
        if(ReadJsonFile(QApplication::applicationDirPath() + + "/Resources/FLData.json",qVarMap))
        {
            str11 = qVarMap.value("PCRB1").toString();
            str12 = qVarMap.value("PCRG1").toString();
            str13 = qVarMap.value("PCRY1").toString();
            str14 = qVarMap.value("PCRR1").toString();

            str21 = qVarMap.value("PCRB2").toString();
            str22 = qVarMap.value("PCRG2").toString();
            str23 = qVarMap.value("PCRY2").toString();
            str24 = qVarMap.value("PCRR2").toString();
        }
        else
        {
            qDebug()<<Q_FUNC_INFO<<__LINE__<<"FLData.json read error";
        }
    }
    else
    {        
        QStringList strNList =
        {
            "164.092,163.001,162.193,161.7,161.856,161.439,161.036,161.298,161.085,160.904,160.448,159.784,160.631,160.309,160.414,160.012,159.64,159.584,159.757,159.751,159.578,159.654,159.855,160.092,159.68,159.758,159.748,159.883,160.241,159.334,159.444,159.28,159.447,159.6,159.771,159.17,159.583,159.637,159.711,159.86,159.17,159.583,159.637,159.711,159.86",
            "383.552, 380.855, 377.883, 376.667, 376.522, 376.656, 376.77, 376.768, 376.823, 376.896, 377.057, 377.69, 378.742, 379.355, 379.442, 379.014, 377.827, 377.592, 378.836, 380.121, 380.785, 381.14, 382.026, 381.841, 381.491, 381.967, 382.24, 382.22, 382.173, 381.707, 380.934, 381.191, 382.416, 383.273, 383.436, 382.972, 382.063, 381.711, 381.974, 382.488, 382.735, 381.936, 381.289, 381.205, 381.249",
            "334.64, 331.20, 327.23, 324.45, 324.23, 325.09, 326.92, 325.93, 325.67, 325.92,325.57, 327.03, 328.64, 327.31, 328.25, 327.78, 327.16, 325.14, 327.09, 329.47,329.79, 329.15, 330.81, 329.76, 330.71, 329.33, 330.98, 330.02, 330.36, 329.72,329.81, 329.29, 329.85, 330.56, 330.82, 331.34, 330.88, 329.12, 328.12, 329.01,330.47, 328.77, 328.14, 327.99, 328.71",
            "276.27, 273.39, 270.76, 267.69, 268.37, 269.10, 270.77, 267.50, 267.40, 269.84, 270.52, 270.72, 271.56, 271.57, 270.96, 270.23, 270.43, 268.96, 270.83, 272.25, 272.06, 272.33, 274.74, 273.68, 273.44, 273.13, 274.25, 273.89, 273.11, 274.08, 273.62, 273.76, 274.57, 273.73, 274.15, 274.37, 273.81, 272.69, 273.20, 273.58, 273.18, 272.01, 273.06, 272.16, 272.01",
            "235.76, 232.68, 229.43, 227.87, 227.58, 227.58, 229.58, 226.88, 226.64, 228.45, 229.22, 229.04, 229.90, 229.73, 229.42, 228.88, 228.79, 227.55, 229.19, 230.47, 230.41, 230.12, 232.12, 230.42, 230.26, 229.91, 231.54, 230.68, 230.01, 231.02, 229.34, 229.94, 230.77, 229.93, 230.15, 230.22, 229.31, 228.08, 229.05, 229.32, 228.35, 227.53, 229.17, 227.58, 227.12",
            "303.45, 303.92, 300.66, 300.26, 299.75, 299.13, 302.37, 300.27, 300.11, 300.81, 302.05, 302.76, 300.99, 302.62, 302.35, 301.76, 301.80, 300.84, 300.37, 301.90, 300.59, 300.51, 303.14, 301.65, 302.50, 300.33, 301.60, 302.32, 300.60, 300.38, 302.48, 300.79, 302.53, 300.42, 301.84, 300.12, 300.90, 299.74, 301.56, 300.05, 299.29, 300.16, 300.77, 299.81, 300.57",
            "468.45, 470.92, 465.66, 465.26, 465.75, 462.13, 465.37, 465.27, 465.11, 467.81, 466.05, 470.76, 467.99, 467.62, 464.35, 465.76, 467.80, 465.84, 465.37, 465.90, 464.59, 464.51, 467.14, 464.65, 467.50, 467.33, 466.60, 467.32, 465.60, 465.38, 467.48, 465.79, 467.53, 465.42, 466.84, 465.12, 465.90, 464.74, 466.56, 465.05, 464.29, 466.16, 465.77, 464.81, 465.57",
            "260.27, 258.39, 255.76, 252.69, 253.37, 251.10, 255.77, 251.50, 251.40, 253.84, 254.52, 255.72, 253.56, 254.57, 254.96, 254.23, 254.43, 250.96, 255.83, 257.25, 257.06, 254.33, 258.74, 255.68, 256.44, 257.13, 259.25, 256.89, 258.11, 257.08, 258.62, 256.76, 257.57, 255.73, 257.15, 258.37, 258.81, 256.69, 255.20, 257.58, 256.18, 257.01, 255.06, 256.16, 255.01"
        };
        QStringList strPList =
        {
            "891.997,890.067,894.123,894.726,894.012,895.657,896.491,894.931,894.45,900.035,896.452,897.674,896.969,898.457,897.718,901.548,901.107,901.002,898.667,903.59,902.42,901.653,901.978,903.984,905.109,906.136,902.973,905.747,907.178,906.213,910.167,907.792,913.283,913.735,920.628,927.955,935.166,946.784,956.849,977.706,987.706,997.706,1007.706,1017.706,1027.706",
            "410.178, 410.182, 410.571, 410.972, 411.084, 411.394, 411.875, 412.076, 412.148, 412.413, 412.655, 412.643, 412.563, 412.784, 413.05, 413.195, 413.58, 414.413, 415.365, 416.688, 418.983, 422.712, 428.673, 437.892, 451.144, 468.731, 489.801, 512.393, 535.178, 558.368, 582.656, 608.116, 634.524, 661.319, 687.322, 711.743, 734.917, 756.962, 777.861, 798.149, 817.649, 836.476, 854.612, 870.136, 880.703",
            "199.808, 198.893, 197.669, 196.982, 196.897, 197, 197.255, 198.204, 200.091, 201.857, 202.991, 203.656, 203.84, 203.791, 204.115, 204.625, 205.269, 206.328, 207.616, 208.933, 210.014, 210.799, 211.435, 212.16, 213.269, 214.877, 217.083, 220.96, 226.508, 232.703, 239.233, 245.986, 252.269, 258.164, 264.074, 269.452, 273.653, 276.632, 278.896, 281.044, 283.145, 285.658, 288.098, 289.666, 290.527",
            "408.785, 408.296, 408.415, 408.928, 409.257, 409.396, 409.42, 409.309, 409.124, 408.907, 408.591, 408.421, 408.447, 408.468, 408.664, 408.886, 408.97, 408.891, 408.648, 408.624, 409.125, 409.863, 410.54, 411.367, 412.548, 414.234, 416.746, 419.844, 422.764, 425.292, 427.306, 429.042, 431.179, 433.977, 436.585, 438.678, 440.968, 443.497, 445.71, 447.922, 450.12, 452.246, 454.513, 456.549, 457.841",
            "497.487,497.629,494.339,496.274,498.196,498.437,496.696,497.925,498.539,499.464,499.261,497.984,496.273,497.43,500.495,499.482,497.443,500.34,499.669,500.346,497.812,499.571,501.89,501.357,500.284,498.431,499.163,499.982,501.055,500.546,501.557,502.607,502.919,503.164,504.505,505.652,506.795,512.999,521.954,529.154,530.154,549.154,559.154,569.154,572.154",
            "346.777, 345.646, 344.07, 342.63, 341.712, 341.358, 341.128, 341.159, 342.095, 343.376, 344.11, 344.161, 343.653, 342.68, 341.665, 341.146, 341.535, 343.14, 346.522, 351.203, 354.918, 358.028, 362.433, 369.342, 379.167, 391.486, 405.577, 420.895, 436.755, 453.279, 471.146, 489.996, 508.943, 527.959, 547.136, 565.893, 583.121, 598.033, 611.16, 624.075, 637.352, 650.271, 662.343, 672.97, 680.203",
            "326.455, 326.495, 326.518, 326.534, 326.67, 327.126, 327.594, 327.833, 327.812, 327.852, 328.257, 328.628, 328.699, 328.728, 328.863, 329.016, 329.227, 329.542, 329.874, 330.213, 330.373, 330.801, 331.849, 333.406, 335.44, 338.293, 342.372, 347.753, 354.497, 362.516, 371.563, 380.931, 390.276, 399.66, 408.807, 417.551, 425.992, 433.906, 441.372, 448.696, 455.246, 460.788, 465.852, 470.424, 473.786",
            "585.023,576.699,572.928,572.291,572.784,572.077,570.158,573.561,573.081,573.516,573.762,574.265,574.445,573.965,573.038,577.967,577.29,576.696,574.884,580.102,578.852,578.427,578.282,579.72,580.879,579.867,578.828,580.467,579.894,582.878,583.154,584.059,587.133,585.552,592.474,593.576,598.996,607.663,615.66,624.959,634.959,644.959,654.959,664.959,674.959"
        };


        if(bPnList.size() >= 8)
        {
            str11 = bPnList.at(0) ? strPList.at(0) : strNList.at(0);
            str12 = bPnList.at(1) ? strPList.at(1) : strNList.at(1);
            str13 = bPnList.at(2) ? strPList.at(2) : strNList.at(2);
            str14 = bPnList.at(3) ? strPList.at(3) : strNList.at(3);
            str21 = bPnList.at(4) ? strPList.at(4) : strNList.at(4);
            str22 = bPnList.at(5) ? strPList.at(5) : strNList.at(5);
            str23 = bPnList.at(6) ? strPList.at(6) : strNList.at(6);
            str24 = bPnList.at(7) ? strPList.at(7) : strNList.at(7);
        }
    }

    QStringList strList11 = str11.split(",");
    QStringList strList12 = str12.split(",");
    QStringList strList13 = str13.split(",");
    QStringList strList14 = str14.split(",");

    QStringList strList21 = str21.split(",");
    QStringList strList22 = str22.split(",");
    QStringList strList23 = str23.split(",");
    QStringList strList24 = str24.split(",");


    QString strTem("%1,0;0,%2,%3,%4,%5;1,%6,%7,%8,%9");
    QVariantList strFLDataList;

    for (int i = 0; i < strList11.size(); i++)
    {
        strFLDataList.push_back(strTem.arg(i).arg(strList11.at(i)).arg(strList12.at(i)).arg(strList13.at(i)).arg(strList14.at(i)).arg(strList21.at(i)).arg(strList22.at(i)).arg(strList23.at(i)).arg(strList24.at(i)));
    }
    return strFLDataList;
}


QVariantList CCmdManager::_MakeMeltingdata(bool bNegtive)
{
    QString str11 ;
    QString str12 ;
    QString str13 ;
    QString str14 ;

    QString str21 ;
    QString str22 ;
    QString str23 ;
    QString str24 ;
    if (CPublicConfig::GetInstance()->GetCalcParam())
    {
        // 从数据库txt读取 ，也可以注册模式
        QVariantMap qVarMap;
        if(ReadJsonFile(QApplication::applicationDirPath() + + "/Resources/FLData.json",qVarMap))
        {
            str11 = qVarMap.value("HRMB1").toString();
            str12 = qVarMap.value("HRMG1").toString();
            str13 = qVarMap.value("HRMY1").toString();
            str14 = qVarMap.value("HRMR1").toString();

            str21 = qVarMap.value("HRMB2").toString();
            str22 = qVarMap.value("HRMG2").toString();
            str23 = qVarMap.value("HRMY2").toString();
            str24 = qVarMap.value("HRMR2").toString();
        }
        else
        {
            qDebug()<<Q_FUNC_INFO<<__LINE__<<"FLData.json read error";
        }
    }
    else
    {
        if(bNegtive)
        {

            //加了两个水平的线
            str11 = "281.3268, 281.3499, 281.3783, 281.41, 281.4408, 281.4652, 281.4773, 281.4726, 281.4495, 281.41, 281.3588, 281.3018, 281.2451, 281.195, 281.1574, 281.1379, 281.1394, 281.1603, 281.1937, 281.2291, 281.2555, 281.2645, 281.2527, 281.2227, 281.1818, 281.14, 281.1066, 281.0873, 281.0833, 281.0926, 281.1131, 281.1447, 281.1896, 281.2507, 281.3301, 281.4286, 281.5458, 281.6798, 281.827, 281.982, 282.1387, 282.2932, 282.4466, 282.6055, 282.7812, 282.9863, 283.231, 283.5207, 283.8555, 284.2312, 284.6412, 285.078, 285.533, 285.9961, 286.4569, 286.906, 287.3358, 287.741, 288.1164, 288.4552, 288.748, 288.9827, 289.1461, 289.2268, 289.2183, 289.1218, 288.9475, 288.7139, 288.4449, 288.1661, 287.9009, 287.6674, 287.4775, 287.3364, 287.2451, 287.2014, 287.2019, 287.2433, 287.3228, 287.4377, 287.5855, 287.7638, 287.97, 288.2018, 288.4574, 288.7361, 289.038, 289.3642, 289.715, 290.0903, 290.4892, 290.9105, 291.353, 291.8155, 292.2976, 292.8004, 293.3257, 293.8746, 294.4459, 295.0344, 295.6331, 296.2343, 296.8317, 297.4202, 297.9955, 298.5537, 299.0923, 299.6106, 300.11, 300.596, 301.07, 301.536, 301.991, 302.434, 302.859, 303.258, 303.623, 303.948, 304.225, 304.455, 304.642, 304.79, 304.909, 305.003, 305.078, 305.137";
            str12 = "81.3268, 81.3499, 81.3783, 81.41, 81.4408, 81.4652, 81.4773, 81.4726, 81.4495, 81.41, 81.3588, 81.3018, 81.2451, 81.195, 81.1574, 81.1379, 81.1394, 81.1603, 81.1937, 81.2291, 81.2555, 81.2645, 81.2527, 81.2227, 81.1818, 81.14, 81.1066, 81.0873, 81.0833, 81.0926, 81.1131, 81.1447, 81.1896, 81.2507, 81.3301, 81.4286, 81.5458, 81.6798, 81.827, 81.982, 82.1387, 82.2932, 82.4466, 82.6055, 82.7812, 82.9863, 83.231, 83.5207, 83.8555, 84.2312, 84.6412, 85.078, 85.533, 85.9961, 86.4569, 86.906, 87.3358, 87.741, 88.1164, 88.4552, 88.748, 88.9827, 89.1461, 89.2268, 89.2183, 89.1218, 88.9475, 88.7139, 88.4449, 88.1661, 87.9009, 87.6674, 87.4775, 87.3364, 87.2451, 87.2014, 87.2019, 87.2433, 87.3228, 87.4377, 87.5855, 87.7638, 87.97, 88.2018, 88.4574, 88.7361, 89.038, 89.3642, 89.715, 90.0903, 90.4892, 90.9105, 91.353, 91.8155, 92.2976, 92.8004, 93.3257, 93.8746, 94.4459, 95.0344, 95.6331, 96.2343, 96.8317, 97.4202, 97.9955, 98.5537, 99.0923, 99.6106, 100.11, 100.596, 101.07, 101.536, 101.991, 102.434, 102.859, 103.258, 103.623, 103.948, 104.225, 104.455, 104.642, 104.79, 104.909, 105.003, 105.078, 105.137";
            str13 = "577.695, 577.706, 577.717, 577.728, 577.739, 577.75, 577.761, 577.772, 577.783, 577.794, 577.805, 577.816, 577.827, 577.838, 577.849, 577.86, 577.871, 577.882, 577.893, 577.904, 577.915, 577.926, 577.937, 577.948, 577.959, 577.97, 577.981, 577.992, 578.003, 578.014, 578.025, 578.036, 578.047, 578.058, 578.069, 578.08, 578.091, 578.102, 578.113, 578.124, 578.135, 578.146, 578.157, 578.168, 578.179, 578.19, 578.201, 578.212, 578.223, 578.234, 578.245, 578.256, 578.267, 578.278, 578.289, 578.3, 578.311, 578.322, 578.333, 578.344, 578.355, 578.366, 578.377, 578.388, 578.399, 578.41, 578.421, 578.432, 578.443, 578.454, 578.465, 578.476, 578.487, 578.498, 578.509, 578.52, 578.531, 578.542, 578.553, 578.564, 578.575, 578.586, 578.597, 578.608, 578.619, 578.63, 578.641, 578.652, 578.663, 578.674, 578.685, 578.696, 578.707, 578.718, 578.729, 578.74, 578.751, 578.762, 578.773, 578.784, 578.795, 578.806, 578.817, 578.828, 578.839, 578.85, 578.861, 578.872, 578.883, 578.894, 578.905, 578.916, 578.927, 578.938, 578.949, 578.96, 578.971, 578.982, 578.993, 579.004, 579.015, 579.026, 579.037, 579.048, 579.059, 579.07, 579.081, 579.092, 579.103, 579.114, 579.125, 579.136, 579.147, 579.158, 579.169, 579.18, 579.191, 579.202, 579.213, 579.224, 579.235, 579.246, 579.257, 579.268, 579.279, 579.29, 579.301, 579.312, 579.323, 579.334, 579.345, 579.356, 579.367, 579.378, 579.389, 579.4, 579.411, 579.422, 579.433, 579.444, 579.455, 579.466, 579.477, 579.488, 579.499, 579.51, 579.521, 579.532, 579.543, 579.554, 579.565, 579.576, 579.587, 579.598, 579.609, 579.62, 579.631, 579.642, 579.653, 579.664, 579.675, 579.686, 579.697, 579.708, 579.719, 579.73, 579.741, 579.752, 579.763, 579.774, 579.785, 579.796, 579.807, 579.818, 579.829, 579.84, 579.851, 579.862, 579.873, 579.884, 579.895, 579.906, 579.917, 579.928, 579.939, 579.95, 579.961, 579.972, 579.983, 579.994, 580.005, 580.016, 580.027, 580.038, 580.049, 580.06, 580.071, 580.082, 580.093, 580.104, 580.115, 580.126, 580.137, 580.148, 580.159, 580.17, 580.181, 580.192, 580.203, 580.214, 580.225, 580.236, 580.247, 580.258, 580.269, 580.28, 580.291, 580.302, 580.313, 580.324, 580.335, 580.346, 580.357, 580.368, 580.379, 580.39, 580.401, 580.412, 580.423, 580.434, 580.445, 580.456, 580.467, 580.478, 580.489, 580.5, 580.511, 580.522, 580.533, 580.544, 580.555, 580.566, 580.577, 580.588, 580.599, 580.61, 580.621, 580.632, 580.643, 580.654, 580.665, 580.676, 580.687, 580.698, 580.709, 580.72, 580.731, 580.742, 580.753, 580.764, 580.775, 580.786, 580.797, 580.808, 580.819, 580.83, 580.841, 580.852, 580.863, 580.874, 580.885, 580.896, 580.907, 580.918, 580.929, 580.94, 580.951, 580.962, 580.973, 580.984, 580.995, 581.006, 581.017, 581.028, 581.039, 581.05, 581.061, 581.072, 581.083, 581.094, 581.105, 581.116, 581.127, 581.138, 581.149, 581.16, 581.171, 581.182, 581.193, 581.204, 581.215, 581.226, 581.237, 581.248, 581.259, 581.27, 581.281, 581.292, 581.303, 581.314, 581.325, 581.336, 581.347, 581.358, 581.369, 581.38, 581.391, 581.402, 581.413, 581.424, 581.435, 581.446, 581.457, 581.468, 581.479, 581.49, 581.501, 581.512, 581.523, 581.534, 581.545, 581.556, 581.567, 581.578, 581.589, 581.6, 581.611, 581.622, 581.633, 581.644, 581.655, 581.666, 581.677, 581.688, 581.699, 581.71, 581.721, 581.732, 581.743, 581.754, 581.765, 581.776, 581.787, 581.798, 581.809, 581.82, 581.831, 581.842, 581.853, 581.864, 581.875, 581.886, 581.897, 581.908, 581.919, 581.93, 581.941, 581.952, 581.963";
            str14 = "612.3268, 612.3499, 612.3783, 612.41, 612.4408, 612.4652, 612.4773, 612.4726, 612.4495, 612.41, 612.3588, 612.3018, 612.2451, 612.195, 612.1574, 612.1379, 612.1394, 612.1603, 612.1937, 612.2291, 612.2555, 612.2645, 612.2527, 612.2227, 612.1818, 612.14, 612.1066, 612.0873, 612.0833, 612.0926, 612.1131, 612.1447, 612.1896, 612.2507, 612.3301, 612.4286, 612.5458, 612.6798, 612.827, 612.982, 613.1387, 613.2932, 613.4466, 613.6055, 613.7812, 613.9863, 614.231, 614.5207, 614.8555, 615.2312, 615.6412, 616.078, 616.533, 616.9961, 617.4569, 617.906, 618.3358, 618.741, 619.1164, 619.4552, 619.748, 619.9827, 620.1461, 620.2268, 620.2183, 620.1218, 619.9475, 619.7139, 619.4449, 619.1661, 618.9009, 618.6674, 618.4775, 618.3364, 618.2451, 618.2014, 618.2019, 618.2433, 618.3228, 618.4377, 618.5855, 618.7638, 618.97, 619.2018, 619.4574, 619.7361, 620.038, 620.3642, 620.715, 621.0903, 621.4892, 621.9105, 622.353, 622.8155, 623.2976, 623.8004, 624.3257, 624.8746, 625.4459, 626.0344, 626.6331, 627.2343, 627.8317, 628.4202, 629.9955, 630.5537, 631.0923, 631.6106, 632.11, 632.596, 633.07, 633.536, 633.991, 634.434, 634.859, 635.258, 635.623, 635.948, 636.225, 636.455, 636.642, 636.79, 636.909, 637.003, 637.078, 637.137";

            str21 = "176.717, 176.631, 175.725, 176.113, 176.012, 175.142, 175.018, 174.941, 174.896, 174.856, 174.797, 174.699, 174.556, 174.374, 174.161, 173.925, 173.666, 173.379, 173.063, 172.723, 172.38, 172.057, 171.779, 171.557, 171.388, 171.257, 171.14, 171.011, 170.85, 170.649, 170.408, 170.144, 169.881, 169.643, 169.444, 169.286, 169.148, 169.001, 168.81, 168.549, 168.209, 167.802, 167.352, 166.892, 166.449, 166.039, 165.664, 165.316, 164.986, 164.668, 164.362, 164.068, 163.783, 163.499, 163.2, 162.873, 162.515, 162.131, 161.738, 161.353, 160.987, 160.64, 160.304, 159.968, 159.629, 159.284, 158.936, 158.589, 158.247, 157.917, 157.605, 157.314, 157.032, 156.733, 156.384, 155.953, 155.422, 154.799, 154.112, 153.406, 152.719, 152.071, 151.458, 150.855, 150.235, 149.583, 148.9, 148.2, 147.499, 146.807, 146.12, 145.426, 144.708, 143.96, 143.193, 142.43, 141.698, 141.016, 140.379, 139.769, 139.16, 138.541, 137.914, 137.296, 136.698, 136.119, 135.54, 134.939, 134.299, 133.618, 132.911, 132.197, 131.49, 130.791, 130.088, 129.359, 128.585, 127.752, 126.868, 125.963, 125.083, 124.276, 123.579, 123.008, 122.558, 122.213";
            str22 = "411.4268, 411.4499, 410.3783, 411.41, 411.5408, 411.4652, 411.6773, 411.6726, 411.6495, 411.61, 411.5588, 411.5018, 411.4451, 411.395, 411.3574, 411.3379, 411.3394, 411.3603, 411.3937, 411.4291, 411.4555, 411.4645, 411.4527, 411.4227, 411.3818, 411.34, 411.3066, 411.2873, 411.1833, 411.2926, 411.3131, 411.2447, 411.3896, 411.4507, 411.5301, 411.6286, 411.6458, 411.7798, 411.927, 412.082, 412.2387, 412.2932, 412.3466, 412.4055, 412.5812, 412.7863, 413.031, 413.3207, 413.6555, 414.0312, 414.4412, 414.878, 415.333, 415.7961, 416.2569, 416.706, 417.1358, 417.541, 418.0164, 418.2552, 418.548, 418.7827, 418.9461, 419.2268, 419.2183, 419.1218, 418.9475, 418.7139, 418.5449, 418.3661, 418.2009, 418.0674, 417.9775, 417.8364, 417.7451, 417.7014, 417.7019, 417.7433, 417.8228, 417.9377, 418.0855, 418.2638, 418.47, 418.7018, 418.8574, 419.0361, 419.238, 419.5642, 419.915, 420.2903, 420.7892, 421.2105, 421.753, 422.2155, 422.6976, 423.3004, 423.8257, 424.3746, 424.9459, 425.5344, 426.1331, 426.7343, 427.3317, 427.9202, 428.5955, 429.1537, 429.7923, 430.4106, 431.11, 431.796, 432.37, 432.836, 433.391, 433.834, 434.459, 435.458, 436.223, 436.848, 437.425, 438.055, 438.642, 439.51, 439.929, 440.423, 440.898, 441.057";
            str23 = "81.265, 81.297, 81.329, 81.362, 81.394, 81.426, 81.458, 81.490, 81.522, 81.554, 81.586, 81.618, 81.650, 81.682, 81.714, 81.746, 81.778, 81.810, 81.842, 81.874, 81.906, 81.938, 81.970, 82.002, 82.034, 82.066, 82.098, 82.130, 82.162, 82.194, 82.226, 82.258, 82.290, 82.322, 82.354, 82.386, 82.418, 82.450, 82.482, 82.514, 82.546, 82.578, 82.610, 82.642, 82.674, 82.706, 82.738, 82.770, 82.802, 82.834, 82.866, 82.898, 82.930, 82.962, 82.994, 83.026, 83.058, 83.090, 83.122, 83.154, 83.186, 83.218, 83.250, 83.282, 83.314, 83.346, 83.378, 83.410, 83.442, 83.474, 83.506, 83.538, 83.570, 83.602, 83.634, 83.666, 83.698, 83.730, 83.762, 83.794, 83.826, 83.858, 83.890, 83.922, 83.954, 83.986, 84.018, 84.050, 84.082, 84.114, 84.146, 84.178, 84.210, 84.242, 84.274, 84.306, 84.338, 84.370, 84.402, 84.434, 84.466, 84.498, 84.530, 84.562, 84.594, 84.626, 84.658, 84.690, 84.722, 84.754, 84.786, 84.818, 84.850, 84.882, 84.914, 84.946, 84.978, 85.010, 85.042, 85.074, 85.106, 85.138, 85.170, 85.202, 85.234, 85.266, 85.298, 85.330, 85.362, 85.394, 85.426, 85.458, 85.490, 85.522, 85.554, 85.586, 85.618, 85.650, 85.682, 85.714, 85.746, 85.778, 85.810, 85.842, 85.874, 85.906, 85.938, 85.970, 86.002, 86.034, 86.066, 86.098, 86.130, 86.162, 86.194, 86.226, 86.258, 86.290, 86.322, 86.354, 86.386, 86.418, 86.450, 86.482, 86.514, 86.546, 86.578, 86.610, 86.642, 86.674, 86.706, 86.738, 86.770, 86.802, 86.834, 86.866, 86.898, 86.930, 86.962, 86.994, 87.026, 87.058, 87.090, 87.122, 87.154, 87.186, 87.218, 87.250, 87.282, 87.314, 87.346, 87.378, 87.410, 87.442, 87.474, 87.506, 87.538, 87.570, 87.602, 87.634, 87.666, 87.698, 87.730, 87.762, 87.794, 87.826, 87.858, 87.890, 87.922, 87.954, 87.986, 88.018, 88.050, 88.082, 88.114, 88.146, 88.178, 88.210, 88.242, 88.274, 88.306, 88.338, 88.370, 88.402, 88.434, 88.466, 88.498, 88.530, 88.562, 88.594, 88.626, 88.658, 88.690, 88.722, 88.754, 88.786, 88.818, 88.850, 88.882, 88.914, 88.946, 88.978, 89.010, 89.042, 89.074, 89.106, 89.138, 89.170, 89.202, 89.234, 89.266, 89.298, 89.330, 89.362, 89.394, 89.426, 89.458, 89.490, 89.522, 89.554, 89.586, 89.618, 89.650, 89.682, 89.714, 89.746, 89.778, 89.810, 89.842, 89.874, 89.906, 89.938, 89.970, 90.002, 90.034, 90.066, 90.098, 90.130, 90.162, 90.194, 90.226, 90.258, 90.290, 90.322, 90.354, 90.386, 90.418, 90.450, 90.482, 90.514, 90.546, 90.578, 90.610, 90.642, 90.674, 90.706, 90.738, 90.770, 90.802, 90.834, 90.866, 90.898, 90.930, 90.962, 90.994, 91.026, 91.058, 91.090, 91.122, 91.154, 91.186, 91.218, 91.250, 91.282, 91.314, 91.346, 91.378, 91.410, 91.442, 91.474, 91.506, 91.538, 91.570, 91.602, 91.634, 91.666, 91.698, 91.730, 91.762, 91.794, 91.826, 91.858, 91.890, 91.922, 91.954, 91.986, 92.018, 92.050, 92.082, 92.114, 92.146, 92.178, 92.210, 92.242, 92.274, 92.306, 92.338, 92.370, 92.402, 92.434, 92.466, 92.498, 92.530, 92.562, 92.594, 92.626, 92.658, 92.690, 92.722, 92.754, 92.786, 92.818, 92.850, 92.882, 92.914, 92.946, 92.978, 93.010, 93.042, 93.074, 93.106, 93.138, 93.170, 93.202, 93.234, 93.266, 93.298, 93.330, 93.362, 93.394, 93.426, 93.458, 93.490, 93.522, 93.554, 93.586, 93.618, 93.650, 93.682, 93.714, 93.746, 93.778, 93.810, 93.842, 93.874, 93.906, 93.938, 93.970, 94.002, 94";
            str24 = "511.6268, 511.5499, 511.1783, 511.61, 511.6408, 511.6652, 511.6773, 511.6726, 511.6495, 511.61, 511.5588, 511.5018, 511.4451, 511.395, 511.3574, 511.3379, 511.3394, 511.3603, 511.3937, 511.4291, 511.4555, 511.4645, 511.4527, 511.4227, 511.3818, 511.34, 511.3066, 511.2873, 511.2833, 511.2926, 511.3131, 511.3447, 511.3896, 511.4507, 511.5301, 511.6286, 511.7458, 511.8798, 512.027, 512.182, 512.3387, 512.4932, 512.6466, 512.8055, 512.9812, 513.1863, 513.431, 513.7207, 514.0555, 514.4312, 514.8412, 515.278, 515.733, 516.1961, 516.6569, 517.106, 517.5358, 517.941, 518.3164, 518.6552, 518.948, 519.1827, 519.3461, 519.4268, 519.4183, 519.3218, 519.1475, 518.9139, 518.6449, 518.3661, 518.1009, 517.8674, 517.6775, 517.5364, 517.4451, 517.4014, 517.4019, 517.4433, 517.5228, 517.6377, 517.7855, 517.9638, 518.17, 518.4018, 518.6574, 518.9361, 519.238, 519.5642, 519.915, 520.2903, 520.6892, 521.1105, 521.553, 522.0155, 522.4976, 523.0004, 523.5257, 524.0746, 524.6459, 525.2344, 525.8331, 526.4343, 527.0317, 527.6202, 528.1955, 528.7537, 529.2923, 529.8106, 530.31, 530.796, 531.27, 531.736, 532.191, 532.634, 533.059, 533.458, 533.823, 534.148, 534.425, 534.655, 534.842, 535.01, 535.129, 535.223, 535.298, 535.357";

            /*// 原始平滑数据
            str11 = "274.435, 274.316, 274.202, 274.104, 274.036, 274.005, 274.012, 274.053, 274.123, 274.216, 274.333, 274.476, 274.643, 274.83, 275.025, 275.221, 275.412, 275.603, 275.803, 276.025, 276.287, 276.608, 277.012, 277.52, 278.154, 278.928, 279.849, 280.919, 282.138, 283.513, 285.059, 286.798, 288.755, 290.959, 293.439, 296.222, 299.343, 302.84, 306.756, 311.145, 316.07, 321.599, 327.805, 334.753, 342.5, 351.089, 360.548, 370.886, 382.088, 394.121, 406.926, 420.42, 434.491, 448.986, 463.706, 478.402, 492.782, 506.517, 519.241, 530.567, 540.086, 547.408, 552.199, 554.243, 553.487, 550.075, 544.344, 536.81, 528.111, 518.951, 510.018, 501.909, 495.073, 489.787, 486.161, 484.178, 483.743, 484.722, 486.97, 490.345, 494.718, 499.97, 506.004, 512.742, 520.123, 528.098, 536.626, 545.666, 555.175, 565.101, 575.391, 585.988, 596.835, 607.884, 619.104, 630.477, 642.002, 653.676, 665.489, 677.412, 689.398, 701.398, 713.36, 725.237, 736.978, 748.528, 759.817, 770.774, 781.325, 791.411, 800.987, 810.034, 818.541, 826.5, 833.891, 840.682, 846.836, 852.316, 857.097, 861.166, 864.527, 867.219, 869.308, 870.89, 872.07, 872.947";
            str12 = "81.3268, 81.3499, 81.3783, 81.41, 81.4408, 81.4652, 81.4773, 81.4726, 81.4495, 81.41, 81.3588, 81.3018, 81.2451, 81.195, 81.1574, 81.1379, 81.1394, 81.1603, 81.1937, 81.2291, 81.2555, 81.2645, 81.2527, 81.2227, 81.1818, 81.14, 81.1066, 81.0873, 81.0833, 81.0926, 81.1131, 81.1447, 81.1896, 81.2507, 81.3301, 81.4286, 81.5458, 81.6798, 81.827, 81.982, 82.1387, 82.2932, 82.4466, 82.6055, 82.7812, 82.9863, 83.231, 83.5207, 83.8555, 84.2312, 84.6412, 85.078, 85.533, 85.9961, 86.4569, 86.906, 87.3358, 87.741, 88.1164, 88.4552, 88.748, 88.9827, 89.1461, 89.2268, 89.2183, 89.1218, 88.9475, 88.7139, 88.4449, 88.1661, 87.9009, 87.6674, 87.4775, 87.3364, 87.2451, 87.2014, 87.2019, 87.2433, 87.3228, 87.4377, 87.5855, 87.7638, 87.97, 88.2018, 88.4574, 88.7361, 89.038, 89.3642, 89.715, 90.0903, 90.4892, 90.9105, 91.353, 91.8155, 92.2976, 92.8004, 93.3257, 93.8746, 94.4459, 95.0344, 95.6331, 96.2343, 96.8317, 97.4202, 97.9955, 98.5537, 99.0923, 99.6106, 100.11, 100.596, 101.07, 101.536, 101.991, 102.434, 102.859, 103.258, 103.623, 103.948, 104.225, 104.455, 104.642, 104.79, 104.909, 105.003, 105.078, 105.137";
            str13 = "576.917, 576.731, 576.525, 576.313, 576.112, 575.942, 575.818, 575.741, 575.696, 575.656, 575.597, 575.499, 575.356, 575.174, 574.961, 574.725, 574.466, 574.179, 573.863, 573.523, 573.18, 572.857, 572.579, 572.357, 572.188, 572.057, 571.94, 571.811, 571.65, 571.449, 571.208, 570.944, 570.681, 570.443, 570.244, 570.086, 569.948, 569.801, 569.61, 569.349, 569.009, 568.602, 568.152, 567.692, 567.249, 566.839, 566.464, 566.116, 565.786, 565.468, 565.162, 564.868, 564.583, 564.299, 564, 563.673, 563.315, 562.931, 562.538, 562.153, 561.787, 561.44, 561.104, 560.768, 560.429, 560.084, 559.736, 559.389, 559.047, 558.717, 558.405, 558.114, 557.832, 557.533, 557.184, 556.753, 556.222, 555.599, 554.912, 554.206, 553.519, 552.871, 552.258, 551.655, 551.035, 550.383, 549.7, 549, 548.299, 547.607, 546.92, 546.226, 545.508, 544.76, 543.993, 543.23, 542.498, 541.816, 541.179, 540.569, 539.96, 539.341, 538.714, 538.096, 537.498, 536.919, 536.34, 535.739, 535.099, 534.418, 533.711, 532.997, 532.29, 531.591, 530.888, 530.159, 529.385, 528.552, 527.668, 526.763, 525.883, 525.076, 524.379, 523.808, 523.358, 523.013";
            str14 = "627.929, 627.433, 626.835, 626.131, 625.325, 624.427, 623.457, 622.436, 621.393, 620.355, 619.351, 618.403, 617.522, 616.708, 615.949, 615.223, 614.503, 613.761, 612.975, 612.136, 611.258, 610.373, 609.53, 608.77, 608.119, 607.576, 607.114, 606.694, 606.276, 605.836, 605.375, 604.912, 604.48, 604.107, 603.809, 603.595, 603.466, 603.423, 603.472, 603.617, 603.873, 604.259, 604.802, 605.537, 606.5, 607.732, 609.271, 611.15, 613.4, 616.05, 619.136, 622.707, 626.816, 631.521, 636.882, 642.956, 649.805, 657.494, 666.086, 675.639, 686.192, 697.766, 710.349, 723.894, 738.321, 753.512, 769.312, 785.529, 801.934, 818.268, 834.242, 849.546, 863.848, 876.789, 887.977, 896.976, 903.305, 906.447, 905.88, 901.13, 891.848, 877.902, 859.464, 837.066, 811.597, 784.235, 756.305, 729.11, 703.76, 681.054, 661.432, 645.012, 631.666, 621.122, 613.043, 607.08, 602.904, 600.215, 598.744, 598.257, 598.541, 599.409, 600.693, 602.249, 603.962, 605.748, 607.551, 609.335, 611.072, 612.743, 614.331, 615.816, 617.181, 618.407, 619.481, 620.4, 621.169, 621.797, 622.294, 622.67, 622.934, 623.102, 623.191, 623.223, 623.218, 623.189";

            str21 = "230.232, 230.387, 230.589, 230.844, 231.154, 231.514, 231.916, 232.35, 232.806, 233.279, 233.763, 234.255, 234.75, 235.243, 235.729, 236.205, 236.673, 237.14, 237.616, 238.108, 238.624, 239.167, 239.735, 240.324, 240.929, 241.548, 242.182, 242.838, 243.524, 244.252, 245.026, 245.853, 246.734, 247.673, 248.672, 249.732, 250.851, 252.027, 253.254, 254.523, 255.825, 257.148, 258.489, 259.853, 261.258, 262.723, 264.269, 265.904, 267.63, 269.436, 271.308, 273.232, 275.197, 277.198, 279.235, 281.311, 283.439, 285.63, 287.898, 290.253, 292.703, 295.255, 297.92, 300.715, 303.651, 306.731, 309.945, 313.273, 316.695, 320.199, 323.784, 327.46, 331.236, 335.117, 339.097, 343.165, 347.307, 351.513, 355.777, 360.097, 364.466, 368.873, 373.304, 377.745, 382.193, 386.655, 391.144, 395.675, 400.25, 404.861, 409.486, 414.1, 418.678, 423.199, 427.651, 432.022, 436.309, 440.509, 444.621, 448.64, 452.562, 456.379, 460.087, 463.685, 467.183, 470.594, 473.93, 477.191, 480.366, 483.436, 486.379, 489.184, 491.855, 494.404, 496.848, 499.199, 501.457, 503.618, 505.675, 507.632, 509.495, 511.273, 512.967, 514.573, 516.086, 517.503, 518.832, 520.087, 521.277, 522.401, 523.446, 524.39, 525.212, 525.895, 526.43, 526.816, 527.058, 527.171, 527.173, 527.083, 526.913, 526.663, 526.317, 525.846, 525.218, 524.403, 523.374, 522.116, 520.613, 518.855, 516.83, 514.523, 511.915, 508.978, 505.675, 501.961, 497.787, 493.107, 487.888, 482.109, 475.762, 468.85, 461.381, 453.37, 444.847, 435.86, 426.48, 416.804, 406.949, 397.051, 387.251, 377.701, 368.547, 359.925, 351.946, 344.691, 338.202, 332.492, 327.541, 323.308, 319.737, 316.76, 314.308, 312.315, 310.72, 309.471, 308.52, 307.824, 307.345, 307.047, 306.898, 306.87, 306.939, 307.088, 307.308, 307.593, 307.941, 308.344, 308.796, 309.288, 309.824, 310.414, 311.075, 311.825, 312.671, 313.605, 314.603, 315.634, 316.668, 317.682, 318.667, 319.621, 320.547, 321.45, 322.332, 323.199, 324.06, 324.925, 325.802, 326.687, 327.563, 328.4, 329.168, 329.845, 330.417, 330.888";
            str22 = "478.897, 479.776, 480.852, 482.147, 483.67, 485.413, 487.351, 489.452, 491.686, 494.03, 496.466, 498.986, 501.581, 504.247, 506.985, 509.797, 512.689, 515.664, 518.726, 521.877, 525.118, 528.447, 531.859, 535.343, 538.886, 542.483, 546.139, 549.877, 553.73, 557.734, 561.92, 566.302, 570.88, 575.635, 580.542, 585.57, 590.688, 595.869, 601.092, 606.345, 611.627, 616.948, 622.319, 627.752, 633.248, 638.8, 644.394, 650.011, 655.632, 661.237, 666.801, 672.299, 677.701, 682.981, 688.126, 693.137, 698.036, 702.861, 707.651, 712.433, 717.215, 721.989, 726.737, 731.446, 736.103, 740.702, 745.24, 749.715, 754.133, 758.502, 762.829, 767.115, 771.355, 775.539, 779.66, 783.717, 787.716, 791.665, 795.577, 799.457, 803.311, 807.142, 810.958, 814.768, 818.58, 822.399, 826.221, 830.037, 833.84, 837.629, 841.408, 845.186, 848.972, 852.775, 856.598, 860.442, 864.304, 868.173, 872.036, 875.88, 879.694, 883.472, 887.216, 890.934, 894.638, 898.341, 902.05, 905.762, 909.462, 913.134, 916.766, 920.354, 923.906, 927.436, 930.956, 934.47, 937.974, 941.462, 944.922, 948.349, 951.737, 955.086, 958.393, 961.653, 964.856, 967.986, 971.021, 973.942, 976.725, 979.352, 981.795, 984.02, 985.983, 987.633, 988.917, 989.787, 990.205, 990.141, 989.574, 988.488, 986.866, 984.694, 981.96, 978.656, 974.781, 970.342, 965.364, 959.891, 953.994, 947.771, 941.343, 934.843, 928.405, 922.153, 916.187, 910.591, 905.427, 900.743, 896.565, 892.902, 889.743, 887.058, 884.797, 882.901, 881.306, 879.954, 878.802, 877.819, 876.988, 876.291, 875.708, 875.214, 874.78, 874.376, 873.983, 873.59, 873.198, 872.812, 872.435, 872.066, 871.694, 871.302, 870.878, 870.415, 869.917, 869.394, 868.857, 868.311, 867.754, 867.177, 866.568, 865.916, 865.215, 864.475, 863.717, 862.97, 862.256, 861.579, 860.919, 860.246, 859.527, 858.743, 857.896, 856.999, 856.07, 855.125, 854.167, 853.191, 852.181, 851.118, 849.981, 848.757, 847.442, 846.043, 844.579, 843.076, 841.561, 840.062, 838.601, 837.196, 835.869, 834.642, 833.537, 832.566, 831.735, 831.037";
            str23 = "468.665, 469.032, 469.457, 469.945, 470.495, 471.101, 471.747, 472.415, 473.083, 473.733, 474.352, 474.937, 475.494, 476.035, 476.571, 477.107, 477.646, 478.184, 478.719, 479.249, 479.777, 480.306, 480.836, 481.363, 481.877, 482.369, 482.832, 483.273, 483.709, 484.167, 484.672, 485.241, 485.874, 486.56, 487.274, 487.99, 488.686, 489.351, 489.985, 490.597, 491.201, 491.812, 492.438, 493.076, 493.719, 494.357, 494.979, 495.585, 496.186, 496.793, 497.417, 498.062, 498.722, 499.385, 500.044, 500.697, 501.345, 501.991, 502.631, 503.26, 503.866, 504.444, 504.993, 505.521, 506.037, 506.554, 507.084, 507.632, 508.199, 508.78, 509.362, 509.932, 510.48, 511.003, 511.501, 511.981, 512.447, 512.898, 513.333, 513.746, 514.139, 514.518, 514.895, 515.28, 515.678, 516.089, 516.505, 516.922, 517.339, 517.758, 518.178, 518.595, 519.001, 519.392, 519.77, 520.147, 520.537, 520.956, 521.41, 521.901, 522.422, 522.964, 523.518, 524.077, 524.643, 525.22, 525.812, 526.414, 527.015, 527.598, 528.15, 528.673, 529.184, 529.707, 530.265, 530.865, 531.503, 532.164, 532.833, 533.503, 534.174, 534.852, 535.541, 536.243, 536.96, 537.693, 538.443, 539.211, 539.994, 540.785, 541.574, 542.353, 543.115, 543.856, 544.579, 545.288, 545.991, 546.699, 547.42, 548.159, 548.916, 549.686, 550.465, 551.246, 552.026, 552.8, 553.563, 554.317, 555.068, 555.825, 556.601, 557.409, 558.255, 559.146, 560.086, 561.087, 562.165, 563.337, 564.616, 566.004, 567.493, 569.069, 570.719, 572.435, 574.213, 576.049, 577.939, 579.873, 581.836, 583.811, 585.784, 587.744, 589.685, 591.605, 593.51, 595.409, 597.321, 599.259, 601.231, 603.233, 605.252, 607.266, 609.261, 611.231, 613.185, 615.134, 617.09, 619.063, 621.056, 623.072, 625.116, 627.189, 629.287, 631.401, 633.519, 635.629, 637.719, 639.777, 641.791, 643.758, 645.691, 647.623, 649.599, 651.659, 653.82, 656.062, 658.329, 660.552, 662.667, 664.635, 666.436, 668.071, 669.543, 670.862, 672.047, 673.134, 674.176, 675.225, 676.319, 677.456, 678.604, 679.708, 680.716, 681.595, 682.334, 682.938";
            str24 = "671.282, 671.404, 671.553, 671.736, 671.958, 672.216, 672.503, 672.805, 673.107, 673.395, 673.661, 673.899, 674.108, 674.288, 674.449, 674.602, 674.761, 674.938, 675.138, 675.361, 675.609, 675.891, 676.216, 676.595, 677.027, 677.505, 678.016, 678.553, 679.128, 679.765, 680.503, 681.377, 682.411, 683.612, 684.963, 686.438, 688.004, 689.634, 691.317, 693.056, 694.868, 696.774, 698.791, 700.933, 703.201, 705.592, 708.098, 710.712, 713.426, 716.236, 719.139, 722.125, 725.177, 728.272, 731.384, 734.504, 737.64, 740.821, 744.085, 747.465, 750.973, 754.603, 758.338, 762.16, 766.058, 770.028, 774.067, 778.17, 782.323, 786.507, 790.7, 794.88, 799.03, 803.136, 807.186, 811.171, 815.085, 818.923, 822.688, 826.379, 829.997, 833.538, 836.99, 840.343, 843.585, 846.712, 849.723, 852.619, 855.404, 858.075, 860.623, 863.04, 865.322, 867.473, 869.502, 871.417, 873.216, 874.877, 876.366, 877.642, 878.676, 879.453, 879.981, 880.285, 880.393, 880.328, 880.091, 879.658, 878.986, 878.024, 876.729, 875.069, 873.031, 870.608, 867.79, 864.549, 860.838, 856.597, 851.763, 846.289, 840.154, 833.362, 825.947, 817.961, 809.474, 800.57, 791.352, 781.938, 772.468, 763.098, 753.99, 745.3, 737.157, 729.655, 722.842, 716.728, 711.298, 706.524, 702.378, 698.836, 695.875, 693.466, 691.574, 690.151, 689.139, 688.47, 688.077, 687.904, 687.912, 688.078, 688.39, 688.842, 689.421, 690.116, 690.912, 691.803, 692.787, 693.863, 695.024, 696.253, 697.525, 698.814, 700.098, 701.37, 702.632, 703.893, 705.167, 706.459, 707.762, 709.056, 710.308, 711.481, 712.549, 713.507, 714.373, 715.181, 715.968, 716.761, 717.564, 718.361, 719.12, 719.81, 720.405, 720.9, 721.304, 721.637, 721.92, 722.173, 722.407, 722.627, 722.831, 723.011, 723.156, 723.248, 723.275, 723.229, 723.111, 722.926, 722.687, 722.411, 722.123, 721.854, 721.63, 721.467, 721.362, 721.291, 721.221, 721.115, 720.939, 720.66, 720.244, 719.658, 718.88, 717.909, 716.774, 715.535, 714.268, 713.049, 711.929, 710.929, 710.042, 709.25, 708.537, 707.895, 707.324, 706.826";
            */
        }
        else
        {
            str11 = "343.478,344.092,344.852,345.78,346.889,348.18,349.633,351.217,352.898,354.651,356.467,358.352,360.32,362.378,364.523,366.744,369.028,371.372,373.783,376.277,378.869,381.568,384.381,387.309,390.359,393.54,396.863,400.337,403.965,407.744,411.663,415.705,419.852,424.082,428.377,432.719,437.096,441.495,445.907,450.331,454.774,459.253,463.787,468.396,473.093,477.885,482.774,487.767,492.872,498.098,503.452,508.933,514.535,520.245,526.053,531.95,537.924,543.956,550.024,556.108,562.199,568.301,574.43,580.607,586.847,593.163,599.567,606.069,612.678,619.396,626.222,633.148,640.171,647.289,654.512,661.858,669.348,676.994,684.791,692.704,700.676,708.637,716.526,724.309,731.984,739.577,747.131,754.695,762.314,770.015,777.795,785.614,793.401,801.073,808.563,815.834,822.883,829.72,836.348,842.744,848.854,854.595,859.864,864.54,868.489,871.571,873.64,874.552,874.179,872.414,869.176,864.407,858.071,850.159,840.703,829.789,817.57,804.257,790.106,775.397,760.425,745.484,730.864,716.832,703.617,691.4,680.305,670.402,661.714,654.225,647.901,642.696,638.566,635.465,633.336,632.104,631.678,631.957,632.845,634.264,636.156,638.481,641.207,644.305,647.739,651.47,655.459,659.663,664.036,668.538,673.137,677.81,682.546,687.332,692.151,696.98,701.787,706.548,711.248,715.887,720.477,725.027,729.539,733.997,738.374,742.641,746.776,750.776,754.645,758.392,762.012,765.494,768.821,771.985,774.991,777.854,780.59,783.211,785.72,788.111,790.377,792.506,794.491,796.329,798.017,799.556,800.944,802.185,803.285,804.257,805.12,805.895,806.608,807.284,807.944,808.602,809.263,809.914,810.536,811.105,811.601,812.016,812.355,812.626,812.843,813.012,813.133,813.205,813.221,813.179,813.075,812.909,812.687,812.414,812.092,811.718,811.286,810.792,810.247,809.676,809.116,808.604,808.163,807.797,807.501,807.26";
            str12 = "662.437,662.961,663.637,664.485,665.507,666.692,668.017,669.451,670.971,672.561,674.21,675.907,677.642,679.405,681.188,682.99,684.81,686.646,688.492,690.341,692.185,694.024,695.868,697.733,699.643,701.62,703.68,705.835,708.084,710.416,712.817,715.265,717.741,720.231,722.726,725.225,727.726,730.228,732.724,735.202,737.656,740.085,742.5,744.917,747.355,749.833,752.367,754.972,757.66,760.436,763.296,766.221,769.185,772.162,775.133,778.093,781.046,783.997,786.948,789.898,792.845,795.789,798.736,801.689,804.652,807.627,810.62,813.637,816.689,819.785,822.928,826.116,829.339,832.583,835.84,839.109,842.403,845.736,849.124,852.572,856.072,859.609,863.164,866.722,870.277,873.828,877.38,880.947,884.543,888.177,891.851,895.555,899.275,902.998,906.722,910.46,914.237,918.079,922.008,926.032,930.149,934.352,938.629,942.973,947.377,951.832,956.328,960.85,965.384,969.922,974.47,979.037,983.633,988.262,992.919,997.6,1002.31,1007.05,1011.86,1016.74,1021.71,1026.81,1032.04,1037.42,1042.96,1048.65,1054.47,1060.38,1066.37,1072.41,1078.51,1084.68,1090.95,1097.32,1103.81,1110.41,1117.06,1123.74,1130.36,1136.89,1143.29,1149.52,1155.59,1161.49,1167.21,1172.75,1178.09,1183.22,1188.13,1192.78,1197.17,1201.26,1205.04,1208.48,1211.57,1214.33,1216.75,1218.84,1220.63,1222.12,1223.35,1224.33,1225.06,1225.57,1225.85,1225.92,1225.8,1225.53,1225.11,1224.54,1223.8,1222.85,1221.64,1220.14,1218.34,1216.22,1213.8,1211.09,1208.09,1204.81,1201.22,1197.32,1193.09,1188.52,1183.59,1178.3,1172.67,1166.76,1160.64,1154.41,1148.2,1142.11,1136.28,1130.81,1125.79,1121.3,1117.39,1114.08,1111.34,1109.12,1107.37,1106.04,1105.09,1104.49,1104.21,1104.21,1104.43,1104.84,1105.37,1106.01,1106.71,1107.45,1108.21,1108.97,1109.73,1110.48,1111.23,1111.96,1112.66,1113.29,1113.86,1114.36,1114.8,1115.18,1115.52,1115.83    ";
            str13 = "507.151,507.968,508.989,510.24,511.732,513.46,515.4,517.521,519.791,522.186,524.692,527.3,530.008,532.818,535.735,538.766,541.916,545.188,548.579,552.081,555.683,559.385,563.192,567.123,571.206,575.464,579.918,584.579,589.447,594.517,599.775,605.203,610.778,616.472,622.258,628.112,634.01,639.932,645.862,651.784,657.694,663.592,669.491,675.405,681.357,687.368,693.46,699.651,705.955,712.369,718.88,725.46,732.079,738.708,745.328,751.927,758.497,765.032,771.529,777.98,784.38,790.716,796.977,803.15,809.229,815.218,821.133,826.999,832.842,838.678,844.514,850.343,856.155,861.936,867.676,873.367,879.006,884.593,890.122,895.582,900.959,906.234,911.387,916.403,921.269,925.976,930.522,934.918,939.183,943.341,947.401,951.356,955.178,958.828,962.276,965.514,968.554,971.419,974.125,976.669,979.026,981.154,983.01,984.554,985.751,986.562,986.929,986.783,986.045,984.637,982.491,979.541,975.707,970.891,964.971,957.809,949.268,939.214,927.531,914.124,898.939,881.978,863.319,843.128,821.661,799.256,776.327,753.338,730.786,709.169,688.954,670.532,654.187,640.066,628.171,618.382,610.491,604.246,599.387,595.673,592.887,590.844,589.387,588.386,587.738,587.368,587.229,587.295,587.554,587.995,588.6,589.342,590.192,591.124,592.123,593.185,594.317,595.524,596.805,598.154,599.56,601.01,602.492,603.992,605.494,606.984,608.455,609.915,611.38,612.868,614.387,615.93,617.484,619.03,620.552,622.036,623.468,624.832,626.114,627.301,628.381,629.343,630.178,630.881,631.454,631.909,632.277,632.602,632.944,633.37,633.94,634.702,635.689,636.916,638.392,640.111,642.058,644.199,646.493,648.891,651.353,653.857,656.397,658.977,661.601,664.268,666.971,669.704,672.465,675.254,678.071,680.907,683.75,686.585,689.397,692.174,694.901,697.557,700.113,702.534,704.785,706.833,708.656,710.243,711.599,712.738     ";
            str14 = "490.794,490.493,490.12,489.665,489.128,488.517,487.854,487.169,486.49,485.838,485.218,484.624,484.04,483.457,482.871,482.286,481.711,481.151,480.608,480.079,479.56,479.054,478.566,478.103,477.67,477.271,476.909,476.589,476.319,476.103,475.945,475.841,475.784,475.766,475.778,475.814,475.872,475.952,476.063,476.219,476.439,476.743,477.143,477.646,478.252,478.96,479.771,480.693,481.741,482.925,484.257,485.737,487.363,489.132,491.039,493.082,495.261,497.576,500.031,502.63,505.38,508.289,511.359,514.589,517.978,521.525,525.238,529.133,533.231,537.556,542.125,546.946,552.017,557.332,562.88,568.658,574.669,580.918,587.403,594.108,600.999,608.035,615.173,622.379,629.629,636.91,644.22,651.569,658.98,666.47,674.044,681.677,689.315,696.891,704.348,711.654,718.813,725.855,732.823,739.755,746.668,753.555,760.387,767.117,773.687,780.036,786.102,791.824,797.148,802.029,806.432,810.325,813.672,816.434,818.569,820.032,820.788,820.804,820.05,818.503,816.141,812.951,808.918,804.025,798.242,791.528,783.831,775.098,765.279,754.347,742.302,729.178,715.051,700.028,684.245,667.869,651.098,634.163,617.321,600.845,584.998,570.013,556.079,543.321,531.806,521.547,512.514,504.647,497.869,492.088,487.202,483.1,479.669,476.799,474.389,472.349,470.601,469.079,467.727,466.499,465.358,464.276,463.231,462.204,461.175,460.131,459.064,457.977,456.886,455.818,454.804,453.87,453.033,452.302,451.682,451.177,450.79,450.527,450.384,450.354,450.423,450.571,450.782,451.041,451.334,451.655,451.999,452.36,452.731,453.102,453.462,453.803,454.12,454.416,454.698,454.973,455.245,455.515,455.778,456.021,456.233,456.406,456.535,456.621,456.666,456.668,456.623,456.526,456.384,456.207,456.014,455.822,455.639,455.462,455.277,455.064,454.807,454.502,454.157,453.794,453.439,453.118,452.847,452.63,452.463,452.336  ";
            str21 = "306.905,307.502,308.24,309.136,310.197,311.416,312.775,314.248,315.814,317.458,319.177,320.977,322.863,324.841,326.909,329.061,331.285,333.57,335.906,338.289,340.719,343.203,345.752,348.386,351.13,354.01,357.047,360.249,363.607,367.098,370.696,374.379,378.137,381.971,385.888,389.888,393.96,398.083,402.228,406.376,410.522,414.68,418.878,423.151,427.528,432.026,436.644,441.367,446.183,451.085,456.077,461.172,466.38,471.7,477.121,482.621,488.18,493.781,499.421,505.1,510.823,516.587,522.383,528.197,534.02,539.852,545.716,551.643,557.673,563.833,570.134,576.571,583.129,589.796,596.566,603.446,610.445,617.572,624.828,632.201,639.664,647.181,654.707,662.208,669.658,677.05,684.387,691.683,698.954,706.214,713.472,720.725,727.951,735.112,742.165,749.072,755.808,762.362,768.722,774.867,780.755,786.316,791.451,796.037,799.942,803.041,805.228,806.41,806.507,805.431,803.09,799.391,794.263,787.669,779.627,770.206,759.527,747.751,735.076,721.729,707.965,694.058,680.292,666.945,654.273,642.489,631.748,622.141,613.699,606.408,600.229,595.114,591.012,587.871,585.629,584.216,583.551,583.555,584.148,585.256,586.819,588.791,591.141,593.846,596.881,600.213,603.793,607.57,611.498,615.543,619.685,623.916,628.231,632.626,637.095,641.627,646.199,650.778,655.32,659.78,664.127,668.35,672.465,676.496,680.466,684.38,688.226,691.982,695.623,699.134,702.507,705.744,708.849,711.829,714.688,717.432,720.064,722.585,724.994,727.28,729.427,731.417,733.231,734.867,736.33,737.639,738.81,739.855,740.775,741.571,742.249,742.825,743.327,743.786,744.233,744.685,745.145,745.596,746.009,746.357,746.619,746.784,746.854,746.834,746.731,746.548,746.285,745.936,745.495,744.96,744.338,743.643,742.899,742.127,741.34,740.537,739.7,738.806,737.845,736.83,735.804,734.828,733.955,733.219,732.628,732.169          ";
            str22 = "607.06,607.712,608.509,609.464,610.582,611.852,613.251,614.747,616.307,617.903,619.516,621.135,622.754,624.372,625.992,627.62,629.263,630.929,632.628,634.365,636.147,637.975,639.848,641.764,643.723,645.73,647.79,649.91,652.089,654.322,656.596,658.902,661.235,663.596,665.991,668.419,670.873,673.34,675.803,678.252,680.69,683.127,685.581,688.065,690.588,693.152,695.752,698.384,701.047,703.74,706.462,709.205,711.957,714.71,717.462,720.22,722.999,725.812,728.667,731.562,734.485,737.42,740.346,743.245,746.103,748.92,751.708,754.492,757.303,760.169,763.102,766.102,769.153,772.239,775.347,778.473,781.622,784.803,788.027,791.296,794.608,797.953,801.313,804.672,808.017,811.347,814.668,817.997,821.351,824.75,828.204,831.717,835.28,838.874,842.478,846.079,849.678,853.292,856.95,860.681,864.508,868.439,872.462,876.554,880.687,884.84,889.008,893.196,897.415,901.675,905.973,910.304,914.663,919.056,923.497,928.01,932.611,937.301,942.067,946.883,951.727,956.593,961.493,966.46,971.533,976.749,982.134,987.699,993.439,999.337,1005.37,1011.5,1017.7,1023.94,1030.19,1036.42,1042.63,1048.8,1054.93,1061.01,1067.03,1072.98,1078.84,1084.57,1090.16,1095.57,1100.79,1105.8,1110.59,1115.12,1119.39,1123.38,1127.09,1130.52,1133.67,1136.55,1139.16,1141.5,1143.56,1145.33,1146.81,1147.98,1148.87,1149.48,1149.82,1149.91,1149.76,1149.38,1148.77,1147.96,1146.94,1145.72,1144.32,1142.73,1140.94,1138.94,1136.73,1134.29,1131.6,1128.64,1125.38,1121.79,1117.85,1113.55,1108.87,1103.81,1098.41,1092.69,1086.73,1080.59,1074.37,1068.16,1062.09,1056.28,1050.83,1045.84,1041.38,1037.46,1034.06,1031.14,1028.66,1026.6,1024.94,1023.63,1022.65,1021.94,1021.44,1021.12,1020.92,1020.82,1020.79,1020.78,1020.78,1020.76,1020.71,1020.61,1020.45,1020.23,1019.92,1019.54,1019.12,1018.69,1018.28,1017.93,1017.64,1017.41      ";
            str23 = "443.303,444.113,445.119,446.349,447.819,449.525,451.444,453.538,455.766,458.093,460.5,462.983,465.553,468.229,471.027,473.953,476.997,480.137,483.351,486.623,489.954,493.359,496.865,500.504,504.313,508.325,512.571,517.064,521.797,526.739,531.839,537.049,542.335,547.688,553.117,558.636,564.245,569.925,575.647,581.38,587.11,592.847,598.612,604.436,610.338,616.327,622.4,628.549,634.764,641.038,647.363,653.729,660.125,666.541,672.971,679.409,685.856,692.309,698.76,705.197,711.598,717.944,724.219,730.409,736.505,742.5,748.39,754.183,759.896,765.562,771.213,776.874,782.55,788.229,793.891,799.518,805.1,810.634,816.121,821.552,826.913,832.179,837.327,842.333,847.184,851.876,856.417,860.827,865.134,869.363,873.53,877.633,881.651,885.548,889.29,892.849,896.21,899.372,902.337,905.109,907.688,910.061,912.2,914.063,915.598,916.755,917.489,917.763,917.538,916.766,915.381,913.294,910.406,906.606,901.783,895.824,888.614,880.039,869.982,858.335,845.009,829.958,813.207,794.871,775.16,754.382,732.918,711.207,689.719,668.929,649.29,631.201,614.975,600.806,588.761,578.783,570.713,564.326,559.369,555.592,552.771,550.718,549.281,548.338,547.797,547.585,547.648,547.94,548.421,549.057,549.818,550.686,551.657,552.736,553.931,555.245,556.667,558.174,559.732,561.311,562.889,564.458,566.019,567.582,569.151,570.729,572.315,573.909,575.515,577.141,578.8,580.5,582.242,584.014,585.79,587.536,589.215,590.796,592.262,593.61,594.843,595.969,596.988,597.902,598.712,599.428,600.072,600.678,601.292,601.965,602.749,603.685,604.808,606.139,607.692,609.473,611.483,613.71,616.138,618.738,621.48,624.332,627.264,630.255,633.287,636.343,639.409,642.469,645.517,648.548,651.566,654.572,657.571,660.565,663.549,666.514,669.435,672.276,674.992,677.534,679.858,681.931,683.735,685.27,686.551,687.604           ";
            str24 = "459.715,459.425,459.064,458.634,458.144,457.61,457.058,456.506,455.966,455.438,454.917,454.4,453.891,453.395,452.917,452.451,451.985,451.506,451.009,450.498,449.987,449.493,449.032,448.616,448.252,447.943,447.687,447.481,447.323,447.208,447.134,447.096,447.09,447.117,447.18,447.286,447.444,447.663,447.946,448.293,448.705,449.183,449.731,450.352,451.052,451.834,452.706,453.676,454.758,455.964,457.306,458.794,460.432,462.222,464.161,466.25,468.488,470.878,473.428,476.146,479.038,482.104,485.334,488.712,492.22,495.85,499.611,503.529,507.646,511.999,516.615,521.5,526.644,532.028,537.64,543.473,549.533,555.824,562.346,569.083,576.001,583.06,590.211,597.417,604.653,611.911,619.205,626.561,634.01,641.58,649.276,657.072,664.908,672.707,680.396,687.935,695.318,702.576,709.753,716.892,724.022,731.141,738.222,745.212,752.046,758.654,764.977,770.965,776.575,781.769,786.51,790.762,794.49,797.657,800.226,802.161,803.428,804.001,803.856,802.966,801.295,798.795,795.42,791.132,785.917,779.779,772.733,764.782,755.907,746.068,735.214,723.309,710.352,696.392,681.535,665.939,649.807,633.382,616.924,600.708,584.999,570.04,556.038,543.148,531.47,521.044,511.858,503.855,496.948,491.036,486.014,481.784,478.249,475.318,472.903,470.924,469.309,467.992,466.913,466.008,465.218,464.488,463.773,463.045,462.286,461.492,460.665,459.816,458.965,458.138,457.363,456.666,456.064,455.56,455.151,454.833,454.607,454.478,454.454,454.541,454.734,455.019,455.381,455.803,456.269,456.769,457.291,457.83,458.381,458.943,459.519,460.106,460.703,461.308,461.921,462.549,463.197,463.869,464.565,465.282,466.017,466.761,467.504,468.234,468.936,469.602,470.223,470.798,471.327,471.813,472.26,472.676,473.069,473.443,473.798,474.125,474.41,474.637,474.801,474.906,474.965,474.997,475.015,475.028,475.039,475.049    ";

            /*
            str11 = "364.655, 364.882, 365.174, 365.548, 366.018, 366.587, 367.245, 367.966, 368.719, 369.477, 370.228, 370.976, 371.737, 372.527, 373.357, 374.225, 375.124, 376.042, 376.976, 377.931, 378.917, 379.95, 381.039, 382.182, 383.374, 384.603, 385.863, 387.155, 388.484, 389.861, 391.296, 392.805, 394.405, 396.112, 397.935, 399.874, 401.923, 404.072, 406.313, 408.647, 411.077, 413.615, 416.269, 419.05, 421.967, 425.023, 428.224, 431.571, 435.059, 438.683, 442.432, 446.291, 450.248, 454.291, 458.416, 462.631, 466.954, 471.418, 476.061, 480.92, 486.019, 491.367, 496.953, 502.756, 508.755, 514.939, 521.306, 527.862, 534.618, 541.582, 548.756, 556.144, 563.747, 571.567, 579.601, 587.848, 596.3, 604.951, 613.79, 622.806, 631.975, 641.262, 650.625, 660.028, 669.449, 678.885, 688.343, 697.831, 707.349, 716.88, 726.399, 735.877, 745.284, 754.6, 763.813, 772.915, 781.903, 790.774, 799.522, 808.137, 816.599, 824.88, 832.949, 840.785, 848.384, 855.758, 862.93, 869.918, 876.724, 883.336, 889.731, 895.896, 901.829, 907.544, 913.061, 918.399, 923.562, 928.55, 933.351, 937.959, 942.373, 946.597, 950.639, 954.505, 958.194, 961.703, 965.023, 968.147, 971.072, 973.796, 976.318, 978.633, 980.731, 982.602, 984.234, 985.621, 986.766, 987.677, 988.36, 988.813, 989.023, 988.963, 988.606, 987.928, 986.907, 985.527, 983.763, 981.581, 978.934, 975.76, 971.986, 967.538, 962.35, 956.37, 949.558, 941.879, 933.286, 923.712, 913.069, 901.259, 888.195, 873.82, 858.129, 841.172, 823.061, 803.963, 784.093, 763.704, 743.076, 722.505, 702.296, 682.752, 664.159, 646.773, 630.794, 616.354, 603.514, 592.27, 582.568, 574.323, 567.431, 561.779, 557.243, 553.687, 550.972, 548.959, 547.524, 546.56, 545.982, 545.72, 545.724, 545.955, 546.388, 547.001, 547.78, 548.713, 549.792, 551.016, 552.385, 553.898, 555.547, 557.319, 559.201, 561.185, 563.264, 565.435, 567.693, 570.033, 572.454, 574.957, 577.545, 580.222, 582.987, 585.835, 588.758, 591.738, 594.755, 597.775, 600.758, 603.647, 606.377, 608.885, 611.119, 613.049, 614.676, 616.019";
            str12 = "585.611, 586.833, 588.343, 590.169, 592.323, 594.791, 597.54, 600.521, 603.694, 607.025, 610.499, 614.111, 617.865, 621.767, 625.823, 630.034, 634.396, 638.902, 643.541, 648.306, 653.188, 658.184, 663.294, 668.519, 673.862, 679.329, 684.93, 690.683, 696.612, 702.749, 709.121, 715.749, 722.635, 729.764, 737.102, 744.606, 752.236, 759.963, 767.777, 775.68, 783.685, 791.807, 800.055, 808.425, 816.899, 825.448, 834.038, 842.632, 851.197, 859.703, 868.129, 876.46, 884.687, 892.803, 900.807, 908.704, 916.514, 924.267, 932.001, 939.744, 947.507, 955.279, 963.037, 970.754, 978.416, 986.024, 993.584, 1001.1, 1008.56, 1015.95, 1023.27, 1030.5, 1037.66, 1044.77, 1051.82, 1058.83, 1065.78, 1072.69, 1079.53, 1086.32, 1093.05, 1099.71, 1106.31, 1112.83, 1119.27, 1125.64, 1131.95, 1138.21, 1144.44, 1150.65, 1156.84, 1163, 1169.12, 1175.17, 1181.14, 1187.03, 1192.87, 1198.69, 1204.51, 1210.34, 1216.16, 1221.96, 1227.69, 1233.35, 1238.92, 1244.42, 1249.86, 1255.23, 1260.55, 1265.79, 1270.97, 1276.09, 1281.16, 1286.18, 1291.15, 1296.07, 1300.93, 1305.73, 1310.49, 1315.2, 1319.88, 1324.51, 1329.08, 1333.57, 1337.94, 1342.15, 1346.15, 1349.9, 1353.39, 1356.57, 1359.44, 1361.92, 1363.97, 1365.5, 1366.44, 1366.71, 1366.28, 1365.08, 1363.11, 1360.31, 1356.66, 1352.11, 1346.64, 1340.24, 1332.92, 1324.73, 1315.73, 1306.03, 1295.78, 1285.14, 1274.31, 1263.53, 1253.01, 1242.96, 1233.55, 1224.93, 1217.18, 1210.35, 1204.45, 1199.45, 1195.28, 1191.86, 1189.08, 1186.85, 1185.09, 1183.73, 1182.72, 1182.01, 1181.57, 1181.38, 1181.4, 1181.59, 1181.91, 1182.32, 1182.8, 1183.33, 1183.9, 1184.5, 1185.12, 1185.75, 1186.38, 1186.97, 1187.53, 1188.05, 1188.54, 1189.01, 1189.49, 1189.97, 1190.47, 1190.97, 1191.46, 1191.92, 1192.34, 1192.72, 1193.04, 1193.32, 1193.57, 1193.79, 1194, 1194.22, 1194.46, 1194.73, 1195.04, 1195.37, 1195.71, 1196.02, 1196.27, 1196.42, 1196.47, 1196.42, 1196.33, 1196.22, 1196.14, 1196.09, 1196.08, 1196.06, 1195.99, 1195.84, 1195.6, 1195.26, 1194.87, 1194.46, 1194.07, 1193.73, 1193.44, 1193.22";
            str13 = "577.695, 577.706, 577.717, 577.728, 577.739, 577.75, 577.761, 577.772, 577.783, 577.794, 577.805, 577.816, 577.827, 577.838, 577.849, 577.86, 577.871, 577.882, 577.893, 577.904, 577.915, 577.926, 577.937, 577.948, 577.959, 577.97, 577.981, 577.992, 578.003, 578.014, 578.025, 578.036, 578.047, 578.058, 578.069, 578.08, 578.091, 578.102, 578.113, 578.124, 578.135, 578.146, 578.157, 578.168, 578.179, 578.19, 578.201, 578.212, 578.223, 578.234, 578.245, 578.256, 578.267, 578.278, 578.289, 578.3, 578.311, 578.322, 578.333, 578.344, 578.355, 578.366, 578.377, 578.388, 578.399, 578.41, 578.421, 578.432, 578.443, 578.454, 578.465, 578.476, 578.487, 578.498, 578.509, 578.52, 578.531, 578.542, 578.553, 578.564, 578.575, 578.586, 578.597, 578.608, 578.619, 578.63, 578.641, 578.652, 578.663, 578.674, 578.685, 578.696, 578.707, 578.718, 578.729, 578.74, 578.751, 578.762, 578.773, 578.784, 578.795, 578.806, 578.817, 578.828, 578.839, 578.85, 578.861, 578.872, 578.883, 578.894, 578.905, 578.916, 578.927, 578.938, 578.949, 578.96, 578.971, 578.982, 578.993, 579.004, 579.015, 579.026, 579.037, 579.048, 579.059, 579.07, 579.081, 579.092, 579.103, 579.114, 579.125, 579.136, 579.147, 579.158, 579.169, 579.18, 579.191, 579.202, 579.213, 579.224, 579.235, 579.246, 579.257, 579.268, 579.279, 579.29, 579.301, 579.312, 579.323, 579.334, 579.345, 579.356, 579.367, 579.378, 579.389, 579.4, 579.411, 579.422, 579.433, 579.444, 579.455, 579.466, 579.477, 579.488, 579.499, 579.51, 579.521, 579.532, 579.543, 579.554, 579.565, 579.576, 579.587, 579.598, 579.609, 579.62, 579.631, 579.642, 579.653, 579.664, 579.675, 579.686, 579.697, 579.708, 579.719, 579.73, 579.741, 579.752, 579.763, 579.774, 579.785, 579.796, 579.807, 579.818, 579.829, 579.84, 579.851, 579.862, 579.873, 579.884, 579.895, 579.906, 579.917, 579.928, 579.939, 579.95, 579.961, 579.972, 579.983, 579.994, 580.005, 580.016, 580.027, 580.038, 580.049, 580.06, 580.071, 580.082, 580.093, 580.104, 580.115, 580.126, 580.137, 580.148, 580.159, 580.17, 580.181, 580.192, 580.203, 580.214, 580.225, 580.236, 580.247, 580.258, 580.269, 580.28, 580.291, 580.302, 580.313, 580.324, 580.335, 580.346, 580.357, 580.368, 580.379, 580.39, 580.401, 580.412, 580.423, 580.434, 580.445, 580.456, 580.467, 580.478, 580.489, 580.5, 580.511, 580.522, 580.533, 580.544, 580.555, 580.566, 580.577, 580.588, 580.599, 580.61, 580.621, 580.632, 580.643, 580.654, 580.665, 580.676, 580.687, 580.698, 580.709, 580.72, 580.731, 580.742, 580.753, 580.764, 580.775, 580.786, 580.797, 580.808, 580.819, 580.83, 580.841, 580.852, 580.863, 580.874, 580.885, 580.896, 580.907, 580.918, 580.929, 580.94, 580.951, 580.962, 580.973, 580.984, 580.995, 581.006, 581.017, 581.028, 581.039, 581.05, 581.061, 581.072, 581.083, 581.094, 581.105, 581.116, 581.127, 581.138, 581.149, 581.16, 581.171, 581.182, 581.193, 581.204, 581.215, 581.226, 581.237, 581.248, 581.259, 581.27, 581.281, 581.292, 581.303, 581.314, 581.325, 581.336, 581.347, 581.358, 581.369, 581.38, 581.391, 581.402, 581.413, 581.424, 581.435, 581.446, 581.457, 581.468, 581.479, 581.49, 581.501, 581.512, 581.523, 581.534, 581.545, 581.556, 581.567, 581.578, 581.589, 581.6, 581.611, 581.622, 581.633, 581.644, 581.655, 581.666, 581.677, 581.688, 581.699, 581.71, 581.721, 581.732, 581.743, 581.754, 581.765, 581.776, 581.787, 581.798, 581.809, 581.82, 581.831, 581.842, 581.853, 581.864, 581.875, 581.886, 581.897, 581.908, 581.919, 581.93, 581.941, 581.952, 581.963";
            str14 = "655.351, 655.083, 654.801, 654.511, 654.217, 653.93, 653.661, 653.423, 653.228, 653.079, 652.975, 652.91, 652.882, 652.892, 652.947, 653.054, 653.219, 653.439, 653.711, 654.033, 654.411, 654.856, 655.381, 655.996, 656.705, 657.506, 658.395, 659.369, 660.435, 661.61, 662.924, 664.408, 666.091, 667.989, 670.104, 672.424, 674.928, 677.593, 680.402, 683.343, 686.419, 689.642, 693.033, 696.617, 700.409, 704.416, 708.625, 713.008, 717.529, 722.154, 726.858, 731.632, 736.474, 741.387, 746.372, 751.428, 756.564, 761.803, 767.177, 772.725, 778.47, 784.416, 790.548, 796.839, 803.261, 809.798, 816.438, 823.165, 829.955, 836.777, 843.595, 850.385, 857.136, 863.847, 870.525, 877.171, 883.779, 890.339, 896.839, 903.26, 909.579, 915.765, 921.786, 927.611, 933.226, 938.629, 943.831, 948.842, 953.667, 958.298, 962.72, 966.914, 970.865, 974.563, 978.002, 981.18, 984.09, 986.724, 989.066, 991.089, 992.761, 994.048, 994.928, 995.394, 995.448, 995.099, 994.344, 993.168, 991.537, 989.405, 986.719, 983.421, 979.451, 974.743, 969.222, 962.814, 955.448, 947.062, 937.609, 927.051, 915.367, 902.556, 888.648, 873.717, 857.883, 841.314, 824.224, 806.858, 789.485, 772.383, 755.818, 740.031, 725.222, 711.544, 699.094, 687.922, 678.033, 669.399, 661.96, 655.637, 650.331, 645.936, 642.344, 639.453, 637.173, 635.427, 634.149, 633.285, 632.788, 632.617, 632.736, 633.124, 633.781, 634.741, 636.073, 637.861, 640.169, 642.991, 646.222, 649.665, 653.085, 656.274, 659.107, 661.56, 663.687, 665.573, 667.305, 668.946, 670.534, 672.082, 673.594, 675.068, 676.505, 677.908, 679.28, 680.622, 681.927, 683.185, 684.383, 685.511, 686.569, 687.568, 688.523, 689.45, 690.355, 691.238, 692.094, 692.916, 693.696, 694.43, 695.113, 695.74, 696.3, 696.783, 697.181, 697.491, 697.721, 697.889, 698.02, 698.141, 698.276, 698.438, 698.623, 698.812, 698.975, 699.084, 699.121, 699.086, 698.991, 698.853, 698.685, 698.49, 698.264, 697.996, 697.677, 697.302, 696.879, 696.421, 695.952, 695.501, 695.091, 694.743, 694.461, 694.245, 694.085, 693.97";

            str21 = "230.232, 230.387, 230.589, 230.844, 231.154, 231.514, 231.916, 232.35, 232.806, 233.279, 233.763, 234.255, 234.75, 235.243, 235.729, 236.205, 236.673, 237.14, 237.616, 238.108, 238.624, 239.167, 239.735, 240.324, 240.929, 241.548, 242.182, 242.838, 243.524, 244.252, 245.026, 245.853, 246.734, 247.673, 248.672, 249.732, 250.851, 252.027, 253.254, 254.523, 255.825, 257.148, 258.489, 259.853, 261.258, 262.723, 264.269, 265.904, 267.63, 269.436, 271.308, 273.232, 275.197, 277.198, 279.235, 281.311, 283.439, 285.63, 287.898, 290.253, 292.703, 295.255, 297.92, 300.715, 303.651, 306.731, 309.945, 313.273, 316.695, 320.199, 323.784, 327.46, 331.236, 335.117, 339.097, 343.165, 347.307, 351.513, 355.777, 360.097, 364.466, 368.873, 373.304, 377.745, 382.193, 386.655, 391.144, 395.675, 400.25, 404.861, 409.486, 414.1, 418.678, 423.199, 427.651, 432.022, 436.309, 440.509, 444.621, 448.64, 452.562, 456.379, 460.087, 463.685, 467.183, 470.594, 473.93, 477.191, 480.366, 483.436, 486.379, 489.184, 491.855, 494.404, 496.848, 499.199, 501.457, 503.618, 505.675, 507.632, 509.495, 511.273, 512.967, 514.573, 516.086, 517.503, 518.832, 520.087, 521.277, 522.401, 523.446, 524.39, 525.212, 525.895, 526.43, 526.816, 527.058, 527.171, 527.173, 527.083, 526.913, 526.663, 526.317, 525.846, 525.218, 524.403, 523.374, 522.116, 520.613, 518.855, 516.83, 514.523, 511.915, 508.978, 505.675, 501.961, 497.787, 493.107, 487.888, 482.109, 475.762, 468.85, 461.381, 453.37, 444.847, 435.86, 426.48, 416.804, 406.949, 397.051, 387.251, 377.701, 368.547, 359.925, 351.946, 344.691, 338.202, 332.492, 327.541, 323.308, 319.737, 316.76, 314.308, 312.315, 310.72, 309.471, 308.52, 307.824, 307.345, 307.047, 306.898, 306.87, 306.939, 307.088, 307.308, 307.593, 307.941, 308.344, 308.796, 309.288, 309.824, 310.414, 311.075, 311.825, 312.671, 313.605, 314.603, 315.634, 316.668, 317.682, 318.667, 319.621, 320.547, 321.45, 322.332, 323.199, 324.06, 324.925, 325.802, 326.687, 327.563, 328.4, 329.168, 329.845, 330.417, 330.888";
            str22 = "478.897, 479.776, 480.852, 482.147, 483.67, 485.413, 487.351, 489.452, 491.686, 494.03, 496.466, 498.986, 501.581, 504.247, 506.985, 509.797, 512.689, 515.664, 518.726, 521.877, 525.118, 528.447, 531.859, 535.343, 538.886, 542.483, 546.139, 549.877, 553.73, 557.734, 561.92, 566.302, 570.88, 575.635, 580.542, 585.57, 590.688, 595.869, 601.092, 606.345, 611.627, 616.948, 622.319, 627.752, 633.248, 638.8, 644.394, 650.011, 655.632, 661.237, 666.801, 672.299, 677.701, 682.981, 688.126, 693.137, 698.036, 702.861, 707.651, 712.433, 717.215, 721.989, 726.737, 731.446, 736.103, 740.702, 745.24, 749.715, 754.133, 758.502, 762.829, 767.115, 771.355, 775.539, 779.66, 783.717, 787.716, 791.665, 795.577, 799.457, 803.311, 807.142, 810.958, 814.768, 818.58, 822.399, 826.221, 830.037, 833.84, 837.629, 841.408, 845.186, 848.972, 852.775, 856.598, 860.442, 864.304, 868.173, 872.036, 875.88, 879.694, 883.472, 887.216, 890.934, 894.638, 898.341, 902.05, 905.762, 909.462, 913.134, 916.766, 920.354, 923.906, 927.436, 930.956, 934.47, 937.974, 941.462, 944.922, 948.349, 951.737, 955.086, 958.393, 961.653, 964.856, 967.986, 971.021, 973.942, 976.725, 979.352, 981.795, 984.02, 985.983, 987.633, 988.917, 989.787, 990.205, 990.141, 989.574, 988.488, 986.866, 984.694, 981.96, 978.656, 974.781, 970.342, 965.364, 959.891, 953.994, 947.771, 941.343, 934.843, 928.405, 922.153, 916.187, 910.591, 905.427, 900.743, 896.565, 892.902, 889.743, 887.058, 884.797, 882.901, 881.306, 879.954, 878.802, 877.819, 876.988, 876.291, 875.708, 875.214, 874.78, 874.376, 873.983, 873.59, 873.198, 872.812, 872.435, 872.066, 871.694, 871.302, 870.878, 870.415, 869.917, 869.394, 868.857, 868.311, 867.754, 867.177, 866.568, 865.916, 865.215, 864.475, 863.717, 862.97, 862.256, 861.579, 860.919, 860.246, 859.527, 858.743, 857.896, 856.999, 856.07, 855.125, 854.167, 853.191, 852.181, 851.118, 849.981, 848.757, 847.442, 846.043, 844.579, 843.076, 841.561, 840.062, 838.601, 837.196, 835.869, 834.642, 833.537, 832.566, 831.735, 831.037";
            str23 = "81.265, 81.297, 81.329, 81.362, 81.394, 81.426, 81.458, 81.490, 81.522, 81.554, 81.586, 81.618, 81.650, 81.682, 81.714, 81.746, 81.778, 81.810, 81.842, 81.874, 81.906, 81.938, 81.970, 82.002, 82.034, 82.066, 82.098, 82.130, 82.162, 82.194, 82.226, 82.258, 82.290, 82.322, 82.354, 82.386, 82.418, 82.450, 82.482, 82.514, 82.546, 82.578, 82.610, 82.642, 82.674, 82.706, 82.738, 82.770, 82.802, 82.834, 82.866, 82.898, 82.930, 82.962, 82.994, 83.026, 83.058, 83.090, 83.122, 83.154, 83.186, 83.218, 83.250, 83.282, 83.314, 83.346, 83.378, 83.410, 83.442, 83.474, 83.506, 83.538, 83.570, 83.602, 83.634, 83.666, 83.698, 83.730, 83.762, 83.794, 83.826, 83.858, 83.890, 83.922, 83.954, 83.986, 84.018, 84.050, 84.082, 84.114, 84.146, 84.178, 84.210, 84.242, 84.274, 84.306, 84.338, 84.370, 84.402, 84.434, 84.466, 84.498, 84.530, 84.562, 84.594, 84.626, 84.658, 84.690, 84.722, 84.754, 84.786, 84.818, 84.850, 84.882, 84.914, 84.946, 84.978, 85.010, 85.042, 85.074, 85.106, 85.138, 85.170, 85.202, 85.234, 85.266, 85.298, 85.330, 85.362, 85.394, 85.426, 85.458, 85.490, 85.522, 85.554, 85.586, 85.618, 85.650, 85.682, 85.714, 85.746, 85.778, 85.810, 85.842, 85.874, 85.906, 85.938, 85.970, 86.002, 86.034, 86.066, 86.098, 86.130, 86.162, 86.194, 86.226, 86.258, 86.290, 86.322, 86.354, 86.386, 86.418, 86.450, 86.482, 86.514, 86.546, 86.578, 86.610, 86.642, 86.674, 86.706, 86.738, 86.770, 86.802, 86.834, 86.866, 86.898, 86.930, 86.962, 86.994, 87.026, 87.058, 87.090, 87.122, 87.154, 87.186, 87.218, 87.250, 87.282, 87.314, 87.346, 87.378, 87.410, 87.442, 87.474, 87.506, 87.538, 87.570, 87.602, 87.634, 87.666, 87.698, 87.730, 87.762, 87.794, 87.826, 87.858, 87.890, 87.922, 87.954, 87.986, 88.018, 88.050, 88.082, 88.114, 88.146, 88.178, 88.210, 88.242, 88.274, 88.306, 88.338, 88.370, 88.402, 88.434, 88.466, 88.498, 88.530, 88.562, 88.594, 88.626, 88.658, 88.690, 88.722, 88.754, 88.786, 88.818, 88.850, 88.882, 88.914, 88.946, 88.978, 89.010, 89.042, 89.074, 89.106, 89.138, 89.170, 89.202, 89.234, 89.266, 89.298, 89.330, 89.362, 89.394, 89.426, 89.458, 89.490, 89.522, 89.554, 89.586, 89.618, 89.650, 89.682, 89.714, 89.746, 89.778, 89.810, 89.842, 89.874, 89.906, 89.938, 89.970, 90.002, 90.034, 90.066, 90.098, 90.130, 90.162, 90.194, 90.226, 90.258, 90.290, 90.322, 90.354, 90.386, 90.418, 90.450, 90.482, 90.514, 90.546, 90.578, 90.610, 90.642, 90.674, 90.706, 90.738, 90.770, 90.802, 90.834, 90.866, 90.898, 90.930, 90.962, 90.994, 91.026, 91.058, 91.090, 91.122, 91.154, 91.186, 91.218, 91.250, 91.282, 91.314, 91.346, 91.378, 91.410, 91.442, 91.474, 91.506, 91.538, 91.570, 91.602, 91.634, 91.666, 91.698, 91.730, 91.762, 91.794, 91.826, 91.858, 91.890, 91.922, 91.954, 91.986, 92.018, 92.050, 92.082, 92.114, 92.146, 92.178, 92.210, 92.242, 92.274, 92.306, 92.338, 92.370, 92.402, 92.434, 92.466, 92.498, 92.530, 92.562, 92.594, 92.626, 92.658, 92.690, 92.722, 92.754, 92.786, 92.818, 92.850, 92.882, 92.914, 92.946, 92.978, 93.010, 93.042, 93.074, 93.106, 93.138, 93.170, 93.202, 93.234, 93.266, 93.298, 93.330, 93.362, 93.394, 93.426, 93.458, 93.490, 93.522, 93.554, 93.586, 93.618, 93.650, 93.682, 93.714, 93.746, 93.778, 93.810, 93.842, 93.874, 93.906, 93.938, 93.970, 94.002, 94";
            str24 = "671.282, 671.404, 671.553, 671.736, 671.958, 672.216, 672.503, 672.805, 673.107, 673.395, 673.661, 673.899, 674.108, 674.288, 674.449, 674.602, 674.761, 674.938, 675.138, 675.361, 675.609, 675.891, 676.216, 676.595, 677.027, 677.505, 678.016, 678.553, 679.128, 679.765, 680.503, 681.377, 682.411, 683.612, 684.963, 686.438, 688.004, 689.634, 691.317, 693.056, 694.868, 696.774, 698.791, 700.933, 703.201, 705.592, 708.098, 710.712, 713.426, 716.236, 719.139, 722.125, 725.177, 728.272, 731.384, 734.504, 737.64, 740.821, 744.085, 747.465, 750.973, 754.603, 758.338, 762.16, 766.058, 770.028, 774.067, 778.17, 782.323, 786.507, 790.7, 794.88, 799.03, 803.136, 807.186, 811.171, 815.085, 818.923, 822.688, 826.379, 829.997, 833.538, 836.99, 840.343, 843.585, 846.712, 849.723, 852.619, 855.404, 858.075, 860.623, 863.04, 865.322, 867.473, 869.502, 871.417, 873.216, 874.877, 876.366, 877.642, 878.676, 879.453, 879.981, 880.285, 880.393, 880.328, 880.091, 879.658, 878.986, 878.024, 876.729, 875.069, 873.031, 870.608, 867.79, 864.549, 860.838, 856.597, 851.763, 846.289, 840.154, 833.362, 825.947, 817.961, 809.474, 800.57, 791.352, 781.938, 772.468, 763.098, 753.99, 745.3, 737.157, 729.655, 722.842, 716.728, 711.298, 706.524, 702.378, 698.836, 695.875, 693.466, 691.574, 690.151, 689.139, 688.47, 688.077, 687.904, 687.912, 688.078, 688.39, 688.842, 689.421, 690.116, 690.912, 691.803, 692.787, 693.863, 695.024, 696.253, 697.525, 698.814, 700.098, 701.37, 702.632, 703.893, 705.167, 706.459, 707.762, 709.056, 710.308, 711.481, 712.549, 713.507, 714.373, 715.181, 715.968, 716.761, 717.564, 718.361, 719.12, 719.81, 720.405, 720.9, 721.304, 721.637, 721.92, 722.173, 722.407, 722.627, 722.831, 723.011, 723.156, 723.248, 723.275, 723.229, 723.111, 722.926, 722.687, 722.411, 722.123, 721.854, 721.63, 721.467, 721.362, 721.291, 721.221, 721.115, 720.939, 720.66, 720.244, 719.658, 718.88, 717.909, 716.774, 715.535, 714.268, 713.049, 711.929, 710.929, 710.042, 709.25, 708.537, 707.895, 707.324, 706.826";
            */
        }

    }

    QStringList strList11 = str11.split(",");
    QStringList strList12 = str12.split(",");
    QStringList strList13 = str13.split(",");
    QStringList strList14 = str14.split(",");

    QStringList strList21 = str21.split(",");
    QStringList strList22 = str22.split(",");
    QStringList strList23 = str23.split(",");
    QStringList strList24 = str24.split(",");



    QString strTem("%1,%2;0,%3,%4,%5,%6;1,%7,%8,%9,%10");
    QVariantList strFLDataList;

    if(bNegtive)
    {
        QString strTemp = "40, 40.4, 40.8, 41.2, 41.6, 42, 42.4, 42.8, 43.2, 43.6, 44, 44.4, 44.8, 45.2, 45.6, 46, 46.4, 46.8, 47.2, 47.6, 48, 48.4, 48.8, 49.2, 49.6, 50, 50.4, 50.8, 51.2, 51.6, 52, 52.4, 52.8, 53.2, 53.6, 54, 54.4, 54.8, 55.2, 55.6, 56, 56.4, 56.8, 57.2, 57.6, 58, 58.4, 58.8, 59.2, 59.6, 60, 60.4, 60.8, 61.2, 61.6, 62, 62.4, 62.8, 63.2, 63.6, 64, 64.4, 64.8, 65.2, 65.6, 66, 66.4, 66.8, 67.2, 67.6, 68, 68.4, 68.8, 69.2, 69.6, 70, 70.4, 70.8, 71.2, 71.6, 72, 72.4, 72.8, 73.2, 73.6, 74, 74.4, 74.8, 75.2, 75.6, 76, 76.4, 76.8, 77.2, 77.6, 78, 78.4, 78.8, 79.2, 79.6, 80, 80.4, 80.8, 81.2, 81.6, 82, 82.4, 82.8, 83.2, 83.6, 84, 84.4, 84.8, 85.2, 85.6, 86, 86.4, 86.8, 87.2, 87.6, 88, 88.4, 88.8, 89.2, 89.6, 90";
        QStringList strTempList = strTemp.split(",");
        for (int i = 0; i < 126; i++)
        {
            strFLDataList.push_back(strTem.arg(i).arg(strTempList.at(i)).arg(strList11.at(i)).arg(strList12.at(i)).arg(strList13.at(i)).arg(strList14.at(i)).arg(strList21.at(i)).arg(strList22.at(i)).arg(strList23.at(i)).arg(strList24.at(i)));
        }
    }
    else
    {
        QString strTemp = "40,40.2,40.4,40.6,40.8,41,41.2,41.4,41.6,41.8,42,42.2,42.4,42.6,42.8,43,43.2,43.4,43.6,43.8,44,44.2,44.4,44.6,44.8,45,45.2,45.4,45.6,45.8,46,46.2,46.4,46.6,46.8,47,47.2,47.4,47.6,47.8,48,48.2,48.4,48.6,48.8,49,49.2,49.4,49.6,49.8,50,50.2,50.4,50.6,50.8,51,51.2,51.4,51.6,51.8,52,52.2,52.4,52.6,52.8,53,53.2,53.4,53.6,53.8,54,54.2,54.4,54.6,54.8,55,55.2,55.4,55.6,55.8,56,56.2,56.4,56.6,56.8,57,57.2,57.4,57.6,57.8,58,58.2,58.4,58.6,58.8,59,59.2,59.4,59.6,59.8,60,60.2,60.4,60.6,60.8,61,61.2,61.4,61.6,61.8,62,62.2,62.4,62.6,62.8,63,63.2,63.4,63.6,63.8,64,64.2,64.4,64.6,64.8,65,65.2,65.4,65.6,65.8,66,66.2,66.4,66.6,66.8,67,67.2,67.4,67.6,67.8,68,68.2,68.4,68.6,68.8,69,69.2,69.4,69.6,69.8,70,70.2,70.4,70.6,70.8,71,71.2,71.4,71.6,71.8,72,72.2,72.4,72.6,72.8,73,73.2,73.4,73.6,73.8,74,74.2,74.4,74.6,74.8,75,75.2,75.4,75.6,75.8,76,76.2,76.4,76.6,76.8,77,77.2,77.4,77.6,77.8,78,78.2,78.4,78.6,78.8,79,79.2,79.4,79.6,79.8,80,80.2,80.4,80.6,80.8,81,81.2,81.4,81.6,81.8,82,82.2,82.4,82.6,82.8,83,83.2,83.4,83.6,83.8,84,84.2,84.4,84.6,84.8,85";
        QStringList strTempList = strTemp.split(",");
        for (int i = 0; i < 226; i++)
        {
            strFLDataList.push_back(strTem.arg(i).arg(strTempList.at(i).toFloat()*100).arg(strList11.at(i)).arg(strList12.at(i)).arg(strList13.at(i)).arg(strList14.at(i)).arg(strList21.at(i)).arg(strList22.at(i)).arg(strList23.at(i)).arg(strList24.at(i)));
        }
    }

    return strFLDataList;

}

void CCmdManager::ForTest(int iMachineID , QList<bool> bPnList)
{
    //下发时序应答
    {
        int iMethodID = Method_timing_file;
        int iResult = 0;

        QList<CCmdBase*> objList = m_cmdObjMap.values(iMethodID);
        for(int i=0; i<objList.size(); i++)
        {
            CCmdBase* pObj = objList.at(i);
            qDebug()<<Q_FUNC_INFO<<__LINE__<<pObj->GetClassName();
            pObj->receiveMachineCmdReplay(iMachineID, iMethodID, iResult, QVariant());
            qDebug()<<Q_FUNC_INFO<<__LINE__;
        }
    }

    //pcr
    if(1)
    {
        int iMethodID = Method_pcr_info;
        int iResult = 0;
        QList<QVariantMap> list;

        for(int i=0; i<400; i++)
        {
            QVariantMap paramsMap;
            paramsMap["RunStep"] = i;
            paramsMap["RunCir"] = i;
            paramsMap["TargetTemp"] = qrand() % 100;;
            paramsMap["Module1Temp"] = qrand() % 200;
            paramsMap["Module2Temp"] = qrand() % 300;
            list<<paramsMap;
        }

        QList<CCmdBase*> objList = m_cmdObjMap.values(iMethodID);
        for(int i=0; i<objList.size(); i++)
        {
            CCmdBase* pObj = objList.at(i);

            for(int j=0; j<list.size(); j++)
                pObj->receiveMachineCmdReplay(iMachineID, iMethodID, iResult, list.at(j));
        }
    }

    //lysis
    if(1)
    {
        int iMethodID = Method_ht_info;
        int iResult = 0;
        QStringList strHTList;
        strHTList << "1,4837,0;2,4598,0;3,4750,0;4,0,0";
        strHTList << "1,5246,0;2,5031,0;3,5056,0;4,0,0";
        strHTList << "1,5625,0;2,5429,0;3,5342,0;4,0,0";
        strHTList << "1,5978,0;2,5796,0;3,5615,0;4,0,0";
        strHTList << "1,6303,0;2,6145,0;3,5871,0;4,0,0";
        strHTList << "1,6614,0;2,6476,0;3,6119,0;4,0,0";
        strHTList << "1,6905,0;2,6780,0;3,6364,0;4,0,0";
        strHTList << "1,7178,0;2,7078,0;3,6598,0;4,0,0";
        strHTList << "1,7448,0;2,7355,0;3,6816,0;4,0,0";
        strHTList << "1,7693,0;2,7626,0;3,7034,0;4,0,0";
        strHTList << "1,7933,0;2,7879,0;3,7243,0;4,0,0";
        strHTList << "1,8166,0;2,8125,0;3,7448,0;4,0,0";

        QStringList oneList = strHTList;
        for(int i=0; i<10; i++)
        {
            strHTList.append(oneList);
        }

        QList<CCmdBase*> objList = m_cmdObjMap.values(iMethodID);
        for(int i=0; i<objList.size(); i++)
        {
            CCmdBase* pObj = objList.at(i);

            for(int j=0; j<strHTList.size(); j++)
                pObj->receiveMachineCmdReplay(iMachineID, iMethodID, iResult, QVariant(strHTList.at(j)));
        }
    }

    //gas
    if(1)
    {
        int iMethodID = Method_pressure_info;
        int iResult = 0;
        QList<QVariantList> dGasList;
        for(int i=0; i<100; i++)
        {
            QVariantList dList;
            dList << 10000 + i*100 << 30000 + i*100;
            dGasList << dList;
        }
        QList<CCmdBase*> objList = m_cmdObjMap.values(iMethodID);
        for(int i=0; i<objList.size(); i++)
        {
            CCmdBase* pObj = objList.at(i);

            for(int j=0; j<dGasList.size(); j++)
            {
                Delay_MSec(20);
                pObj->receiveMachineCmdReplay(iMachineID, iMethodID, iResult, QVariant(dGasList.at(j)));
            }
        }
    }

    //FL
    if(1)
    {
        int iMethodID = Method_fl_data;
        int iResult = 0;

        QVariantList strFLDataList;
        strFLDataList = _Makedata(bPnList);
#if 0
        strFLDataList.push_back("0,0;0,325.228,410.468,409.773,421.775;1,200.420,347.695,380.843,421.823" );
        strFLDataList.push_back("1,0;0,326.615,409.594,407.256,419.783;1,199.514,346.016,371.838,411.965" );
        strFLDataList.push_back("2,0;0,326.366,410.540,403.488,415.203;1,196.867,343.770,366.923,407.665" );
        strFLDataList.push_back("3,0;0,326.795,411.600,409.250,420.308;1,196.713,342.517,372.002,415.138" );
        strFLDataList.push_back("4,0;0,326.037,410.605,408.302,420.753;1,196.920,341.118,370.449,412.212" );
        strFLDataList.push_back("5,0;0,327.428,411.234,409.473,421.262;1,197.805,341.433,371.122,412.754" );
        strFLDataList.push_back("6,0;0,327.579,412.217,409.470,421.470;1,197.248,341.458,370.007,411.114" );
        strFLDataList.push_back("7,0;0,328.083,412.264,409.434,422.086;1,196.802,339.998,371.775,413.477" );
        strFLDataList.push_back( "8,0;0,327.908,411.705,408.952,420.084;1,200.813,342.050,389.714,435.756");
        strFLDataList.push_back( "9,0;0,327.282,412.629,409.179,420.169;1,202.115,343.845,400.654,449.804");

        strFLDataList.push_back("10,0;0,328.431,412.676,408.483,418.867;1,203.215,344.496,410.552,463.110");
        strFLDataList.push_back("11,0;0,328.940,413.027,408.020,418.873;1,203.773,344.313,412.538,465.469");
        strFLDataList.push_back("12,0;0,327.440,411.902,408.941,418.958;1,204.448,344.072,414.733,467.734");
        strFLDataList.push_back("13,0;0,328.622,412.215,407.936,419.127;1,202.961,342.676,411.918,464.280");
        strFLDataList.push_back("14,0;0,329.951,413.110,408.852,419.931;1,204.283,341.364,422.935,477.775");
        strFLDataList.push_back("15,0;0,329.111,413.237,409.781,421.492;1,204.762,340.741,423.928,479.221");
        strFLDataList.push_back("16,0;0,328.877,412.874,408.958,419.718;1,204.635,340.767,425.953,481.772");
        strFLDataList.push_back("17,0;0,329.958,414.804,409.162,421.059;1,206.555,342.822,430.971,490.024");
        strFLDataList.push_back("18,0;0,329.315,415.142,408.522,420.145;1,207.299,344.038,436.028,496.136");
        strFLDataList.push_back("19,0;0,330.924,416.086,408.166,419.963;1,209.310,353.648,435.029,496.480");
        strFLDataList.push_back("20,0;0,329.941,418.412,408.981,420.846;1,210.047,354.842,439.994,503.882");
        strFLDataList.push_back("21,0;0,330.445,421.661,410.142,422.904;1,211.025,357.363,438.681,501.684");
        strFLDataList.push_back("22,0;0,331.627,426.925,410.351,424.564;1,211.218,361.001,436.744,502.182");
        strFLDataList.push_back("23,0;0,333.213,435.882,411.162,429.827;1,212.263,367.772,438.158,506.210");
        strFLDataList.push_back("24,0;0,335.181,448.779,412.412,434.621;1,212.393,377.722,434.068,503.273");
        strFLDataList.push_back("25,0;0,337.355,466.583,413.768,440.606;1,215.604,390.948,430.632,502.934");
        strFLDataList.push_back("26,0;0,342.019,489.281,416.020,451.034;1,215.445,404.560,420.384,493.775");
        strFLDataList.push_back("27,0;0,346.752,512.650,420.527,465.316;1,220.018,420.995,424.852,504.554");
        strFLDataList.push_back("28,0;0,354.155,535.118,422.549,475.124;1,226.417,436.628,425.727,511.114");
        strFLDataList.push_back("29,0;0,361.577,557.489,425.885,483.424;1,232.749,452.245,422.722,511.635");
        strFLDataList.push_back("30,0;0,371.668,582.154,427.368,491.659;1,238.561,470.414,427.054,521.196");
        strFLDataList.push_back("31,0;0,381.073,607.599,429.034,501.022;1,246.727,490.441,430.951,529.489");
        strFLDataList.push_back("32,0;0,390.030,634.129,430.201,506.635;1,252.541,508.758,432.299,535.761");
        strFLDataList.push_back("33,0;0,399.843,661.706,434.426,518.272;1,257.745,527.799,431.430,537.005");
        strFLDataList.push_back("34,0;0,409.188,688.613,437.121,527.427;1,264.397,547.156,437.094,549.972");
        strFLDataList.push_back("35,0;0,417.469,712.165,438.450,537.324;1,270.280,566.767,442.396,561.608");
        strFLDataList.push_back("36,0;0,426.293,735.198,440.293,547.757;1,274.126,584.438,446.533,571.705");
        strFLDataList.push_back("37,0;0,434.527,758.234,444.531,560.556;1,277.456,599.434,450.451,582.476");
        strFLDataList.push_back("38,0;0,440.753,777.412,445.079,572.957;1,278.136,610.798,437.008,571.894");
        strFLDataList.push_back("39,0;0,449.420,798.875,448.130,588.428;1,281.954,623.645,437.515,580.542");

        strFLDataList.push_back("40,0;0,456.024,818.287,450.314,603.626;1,282.196,637.551,439.529,587.796");
        strFLDataList.push_back("41,0;0,460.694,835.786,451.880,615.935;1,285.690,651.145,444.410,603.731");
        strFLDataList.push_back("42,0;0,466.152,856.500,454.651,632.708;1,289.033,662.088,463.900,635.733");
        strFLDataList.push_back("43,0;0,470.551,871.593,457.013,645.400;1,289.685,674.909,466.729,646.253");
        strFLDataList.push_back("44,0;0,476.933,890.025,458.843,660.668;1,291.297,686.203,464.827,651.864");

#endif

        QList<CCmdBase*> objList = m_cmdObjMap.values(iMethodID);
        for(int i=0; i<objList.size(); i++)
        {
            CCmdBase* pObj = objList.at(i);
            qDebug()<<Q_FUNC_INFO<<__LINE__;
            for(int i=0; i<strFLDataList.size(); i++)//
            {
                QString oneCycleData = strFLDataList.at(i).toString();
                QStringList oneCycleList = oneCycleData.split(";");
                QStringList head = oneCycleList.at(0).split(",");
                head[1] = "0";
                oneCycleList[0] = head.join(",");
                oneCycleData = oneCycleList.join(";");
                QVariant qVarData = oneCycleData;

                if(G_RegisterMode)
                {
                    if(!_RegisterFLMode(iMachineID, qVarData))
                        continue;
                }

                pObj->receiveMachineCmdReplay(iMachineID, iMethodID, iResult, qVarData);

                Delay_MSec(100);
            }
            qDebug()<<Q_FUNC_INFO<<__LINE__;
        }
    }

    //Melting
    SRunningInfoStruct &sRunInfo = CRunTest::GetInstance()->GetRunInfoStruct(iMachineID);
    if(bHrmTecType(sRunInfo.iTecIndex))
    {
        int iMethodID = Method_fl_data;
        int iResult = 0;

        QVariantList strMeltFLDataList;
        strMeltFLDataList = _MakeMeltingdata(false);
#if 0
        strFLDataList.push_back("0,4000;0,363.767,580.566,459.525,657.843;1,229.198,474.739,466.167,669.884");
        strFLDataList.push_back("1,4020;0,363.435,582.145,459.783,655.579;1,229.793,476.835,468.290,672.341");
        strFLDataList.push_back("2,4040;0,366.006,584.954,460.037,654.268;1,229.954,478.665,469.107,671.133");
        strFLDataList.push_back("3,4060;0,364.289,587.456,459.446,654.356;1,230.350,480.789,469.486,671.035");
        strFLDataList.push_back("4,4080;0,364.887,591.301,460.390,653.997;1,230.669,481.805,469.928,671.420");
        strFLDataList.push_back("5,4100;0,366.279,594.218,459.895,654.972;1,231.648,485.309,471.029,672.296");
        strFLDataList.push_back("6,4120;0,366.446,595.934,459.542,653.069;1,231.667,486.799,472.027,672.459");
        strFLDataList.push_back("7,4140;0,367.892,600.469,460.122,652.822;1,232.590,488.909,471.743,672.562");
        strFLDataList.push_back("8,4160;0,369.525,603.180,460.553,654.062;1,232.703,491.610,473.527,673.953");
        strFLDataList.push_back("9,4180;0,369.690,605.929,461.454,652.419;1,233.090,493.408,474.294,673.130");

        strFLDataList.push_back( "10,4200;0,370.428,610.313,461.535,653.184;1,233.762,495.732,474.070,673.952");
        strFLDataList.push_back( "11,4220;0,371.147,613.464,462.135,652.634;1,233.869,498.473,475.833,673.422");
        strFLDataList.push_back( "12,4240;0,371.123,617.718,461.851,653.376;1,235.244,502.243,475.590,674.679");
        strFLDataList.push_back( "13,4260;0,371.971,620.114,461.486,651.467;1,235.226,503.808,475.426,674.783");
        strFLDataList.push_back( "14,4280;0,372.860,625.130,462.752,652.583;1,235.548,506.186,476.374,674.432");
        strFLDataList.push_back( "15,4300;0,374.600,629.844,461.886,652.389;1,236.377,509.314,477.243,674.819");
        strFLDataList.push_back( "16,4320;0,374.827,633.499,462.685,652.448;1,237.150,513.092,477.859,674.134");
        strFLDataList.push_back( "17,4340;0,376.101,638.138,463.182,653.977;1,236.747,514.503,478.151,674.217");
        strFLDataList.push_back( "18,4360;0,377.452,642.971,464.172,653.528;1,237.713,518.292,478.471,675.173");
        strFLDataList.push_back( "19,4380;0,377.732,648.134,463.789,654.047;1,237.525,522.090,479.870,676.210");

        strFLDataList.push_back("20,4400;0,378.650,652.537,463.442,653.870;1,238.647,524.609,479.587,675.259");
        strFLDataList.push_back("21,4420;0,379.345,658.100,464.088,654.567;1,239.255,527.549,479.806,675.751");
        strFLDataList.push_back("22,4440;0,380.228,662.095,465.282,655.068;1,239.380,531.664,481.347,676.000");
        strFLDataList.push_back("23,4460;0,382.457,668.186,465.672,654.887;1,240.351,534.470,480.974,675.571");
        strFLDataList.push_back("24,4480;0,382.618,673.678,466.167,656.396;1,240.560,539.395,481.699,676.430");
        strFLDataList.push_back("25,4500;0,385.400,678.381,466.405,657.191;1,242.015,542.615,482.811,677.889");
        strFLDataList.push_back("26,4520;0,386.155,684.682,467.150,658.572;1,242.358,546.627,483.597,678.285");
        strFLDataList.push_back("27,4540;0,386.657,689.929,467.561,658.586;1,242.469,547.917,483.599,678.232");
        strFLDataList.push_back("28,4560;0,387.068,695.596,466.930,659.773;1,243.121,554.312,484.199,680.283");
        strFLDataList.push_back("29,4580;0,390.507,702.273,468.309,662.542;1,243.685,555.980,483.369,678.807");


        strFLDataList.push_back( "30,4600;0,391.241,706.759,468.255,661.044;1,245.225,560.848,483.784,678.921");
        strFLDataList.push_back( "31,4620;0,392.799,714.681,469.778,663.539;1,245.630,564.828,485.530,680.247");
        strFLDataList.push_back( "32,4640;0,393.664,721.883,469.655,664.941;1,246.662,570.550,485.240,682.612");
        strFLDataList.push_back( "33,4660;0,394.483,727.247,469.967,666.345;1,246.867,574.653,485.926,681.908");
        strFLDataList.push_back( "34,4680;0,397.650,736.596,470.994,668.906;1,248.681,580.269,487.798,684.094");
        strFLDataList.push_back( "35,4700;0,399.662,745.632,471.800,672.718;1,249.242,585.390,488.047,686.278");
        strFLDataList.push_back( "36,4720;0,401.231,751.557,471.305,673.900;1,250.785,589.590,489.399,688.087");
        strFLDataList.push_back( "37,4740;0,404.432,759.169,472.532,676.345;1,251.859,596.165,489.340,689.878");
        strFLDataList.push_back( "38,4760;0,405.533,767.805,472.857,680.126;1,252.962,601.933,490.180,691.601");
        strFLDataList.push_back( "39,4780;0,407.761,775.594,474.091,683.487;1,254.017,605.392,490.767,692.266");


        strFLDataList.push_back("40,4800;0,411.397,782.689,474.214,685.752;1,255.674,611.613,490.905,694.042");
        strFLDataList.push_back("41,4820;0,413.151,791.766,475.003,689.015;1,257.533,617.516,491.486,696.688");
        strFLDataList.push_back("42,4840;0,415.042,798.279,477.268,691.898;1,258.754,621.530,492.498,697.735");
        strFLDataList.push_back("43,4860;0,418.213,807.771,477.513,694.937;1,260.027,627.030,493.027,700.691");
        strFLDataList.push_back("44,4880;0,421.981,816.631,477.230,700.087;1,260.377,632.002,493.195,702.244");
        strFLDataList.push_back("45,4900;0,424.468,826.216,479.427,703.466;1,262.336,639.623,494.745,704.881");
        strFLDataList.push_back("46,4920;0,427.297,833.778,478.408,706.731;1,263.864,645.072,495.774,708.256");
        strFLDataList.push_back("47,4940;0,430.122,841.946,478.088,712.072;1,265.017,649.064,495.790,709.841");
        strFLDataList.push_back("48,4960;0,435.489,851.502,480.697,717.686;1,267.149,655.279,495.805,713.282");
        strFLDataList.push_back("49,4980;0,437.487,860.885,481.604,723.163;1,269.573,662.340,496.772,715.962");


        strFLDataList.push_back("50,5000;0,442.249,869.373,482.012,726.349;1,270.957,666.344,496.396,718.698" );
        strFLDataList.push_back("51,5020;0,445.608,876.165,482.811,730.862;1,273.388,672.518,497.938,720.840" );
        strFLDataList.push_back("52,5040;0,449.730,884.144,483.122,735.736;1,274.960,679.396,499.607,725.361" );
        strFLDataList.push_back("53,5060;0,454.488,894.131,484.542,741.221;1,276.633,682.842,499.213,728.433" );
        strFLDataList.push_back("54,5080;0,458.081,901.740,484.923,745.806;1,279.120,688.631,499.994,730.665" );
        strFLDataList.push_back("55,5100;0,462.219,909.478,484.980,751.871;1,281.971,695.099,501.219,736.273" );
        strFLDataList.push_back("56,5120;0,466.409,916.276,485.804,755.982;1,283.305,698.801,500.995,738.053" );
        strFLDataList.push_back("57,5140;0,470.932,925.435,486.369,762.184;1,284.068,702.559,501.559,739.518" );
        strFLDataList.push_back("58,5160;0,475.180,931.172,487.550,765.967;1,287.886,706.358,502.634,743.589" );
        strFLDataList.push_back("59,5180;0,479.182,938.338,488.027,771.355;1,289.839,711.697,503.524,744.935" );



        strFLDataList.push_back("60,5200;0,483.975,947.695,487.830,776.055;1,291.966,717.822,504.415,750.760"  );
        strFLDataList.push_back("61,5220;0,490.436,955.230,488.732,783.637;1,295.576,722.489,504.551,754.879"  );
        strFLDataList.push_back("62,5240;0,496.521,963.310,489.230,790.886;1,297.391,728.006,505.285,759.219"  );
        strFLDataList.push_back("63,5260;0,501.079,971.624,491.332,796.092;1,299.992,730.700,505.310,760.320"  );
        strFLDataList.push_back("64,5280;0,509.633,980.294,490.365,803.520;1,302.179,735.686,505.970,766.247"  );
        strFLDataList.push_back("65,5300;0,513.283,985.066,490.823,808.868;1,305.552,741.300,506.404,769.426"  );
        strFLDataList.push_back("66,5320;0,521.089,992.724,491.710,815.985;1,309.791,746.357,507.405,774.080"  );
        strFLDataList.push_back("67,5340;0,525.610,1000.494,491.414,821.813;1,313.137,749.689,507.312,778.016" );
        strFLDataList.push_back("68,5360;0,534.794,1010.197,493.387,830.024;1,317.072,754.859,507.785,780.953" );
        strFLDataList.push_back("69,5380;0,540.268,1016.375,492.627,836.992;1,319.472,758.110,508.360,787.531" );



        strFLDataList.push_back( "70,5400;0,547.628,1024.807,494.549,844.512;1,323.786,762.011,509.811,790.325" );
        strFLDataList.push_back( "71,5420;0,555.091,1030.177,494.780,851.078;1,326.616,768.576,510.561,795.770" );
        strFLDataList.push_back( "72,5440;0,563.093,1036.871,494.619,856.904;1,330.159,770.851,510.444,798.902" );
        strFLDataList.push_back( "73,5460;0,571.323,1045.569,494.896,863.597;1,335.024,776.022,511.313,803.379" );
        strFLDataList.push_back( "74,5480;0,577.260,1052.135,495.747,870.558;1,338.452,780.574,511.175,806.685" );
        strFLDataList.push_back( "75,5500;0,586.530,1058.618,497.044,877.285;1,342.946,783.866,512.186,812.677" );
        strFLDataList.push_back( "76,5520;0,596.616,1066.350,496.920,884.252;1,347.229,787.763,512.283,815.130" );
        strFLDataList.push_back( "77,5540;0,604.020,1072.798,497.986,890.284;1,351.596,791.860,513.336,819.377" );
        strFLDataList.push_back( "78,5560;0,613.454,1080.359,498.666,897.966;1,355.425,795.563,512.882,822.852" );
        strFLDataList.push_back( "79,5580;0,621.047,1086.067,498.478,902.956;1,359.527,799.382,513.945,826.669" );



        strFLDataList.push_back( "80,5600;0,630.291,1093.528,498.996,910.089;1,363.926,803.384,515.018,830.198");
        strFLDataList.push_back( "81,5620;0,641.400,1099.179,499.220,915.545;1,368.797,807.786,514.403,833.707");
        strFLDataList.push_back( "82,5640;0,651.513,1107.494,501.193,923.755;1,373.625,811.236,515.013,837.695");
        strFLDataList.push_back( "83,5660;0,660.606,1113.439,500.366,929.629;1,377.818,814.499,514.109,840.990");
        strFLDataList.push_back( "84,5680;0,669.374,1119.020,501.519,933.847;1,382.575,817.762,515.546,843.995");
        strFLDataList.push_back( "85,5700;0,678.288,1126.412,501.583,939.493;1,387.180,822.392,516.376,847.545");
        strFLDataList.push_back( "86,5720;0,687.821,1132.290,502.279,944.263;1,389.836,826.425,517.179,850.126");
        strFLDataList.push_back( "87,5740;0,697.846,1138.118,503.315,949.572;1,394.811,830.330,516.901,853.055");
        strFLDataList.push_back( "88,5760;0,707.216,1144.516,502.949,954.342;1,400.222,834.721,517.287,856.008");
        strFLDataList.push_back( "89,5780;0,716.876,1150.370,503.214,959.072;1,405.284,836.562,516.985,858.133");


        strFLDataList.push_back("90,5800;0,725.255,1156.775,504.018,964.036;1,409.636,841.965,518.369,860.845" );
        strFLDataList.push_back("91,5820;0,737.967,1162.904,504.119,968.390;1,413.445,845.301,518.555,864.409" );
        strFLDataList.push_back("92,5840;0,746.470,1169.939,504.738,972.105;1,419.779,847.973,519.434,866.420" );
        strFLDataList.push_back("93,5860;0,753.694,1175.325,505.797,975.220;1,423.483,853.276,519.627,868.203" );
        strFLDataList.push_back("94,5880;0,763.886,1182.173,506.375,979.318;1,428.093,857.533,520.686,869.838" );
        strFLDataList.push_back("95,5900;0,774.891,1187.793,506.586,982.315;1,432.135,858.296,518.740,870.604" );
        strFLDataList.push_back("96,5920;0,781.504,1193.119,506.875,985.680;1,436.747,864.932,520.436,873.410" );
        strFLDataList.push_back("97,5940;0,792.177,1198.578,507.203,988.058;1,441.034,868.412,521.325,876.598" );
        strFLDataList.push_back("98,5960;0,798.954,1202.423,507.052,989.628;1,445.527,872.349,520.870,877.532" );
        strFLDataList.push_back("99,5980;0,809.280,1210.537,508.629,992.234;1,447.999,875.758,521.551,877.925" );



        strFLDataList.push_back("100,6000;0,817.086,1216.363,508.618,994.969;1,453.496,880.062,522.490,880.737" );
        strFLDataList.push_back("101,6020;0,825.402,1223.022,510.183,996.824;1,456.677,883.460,522.865,881.378" );
        strFLDataList.push_back("102,6040;0,834.400,1228.421,510.353,997.031;1,461.015,887.489,523.430,880.937" );
        strFLDataList.push_back("103,6060;0,842.888,1234.341,510.259,997.177;1,464.378,891.438,524.485,883.308" );
        strFLDataList.push_back("104,6080;0,850.484,1239.208,511.448,997.069;1,468.097,894.698,525.019,880.731" );
        strFLDataList.push_back("105,6100;0,854.961,1242.633,510.918,995.906;1,469.967,897.926,524.762,880.626" );
        strFLDataList.push_back("106,6120;0,864.103,1250.934,512.099,996.695;1,473.833,901.095,524.895,880.509" );
        strFLDataList.push_back("107,6140;0,870.342,1256.766,513.182,995.376;1,477.825,905.886,526.625,881.784" );
        strFLDataList.push_back("108,6160;0,876.218,1259.348,512.775,992.524;1,480.613,910.420,527.280,880.458" );
        strFLDataList.push_back("109,6180;0,885.452,1267.123,514.589,992.558;1,484.024,913.136,527.264,880.382" );


        strFLDataList.push_back( "110,6200;0,891.947,1271.839,515.205,990.392;1,487.307,916.976,529.690,878.511");
        strFLDataList.push_back( "111,6220;0,895.730,1274.409,514.594,985.420;1,490.751,921.263,528.884,876.618");
        strFLDataList.push_back( "112,6240;0,904.181,1282.921,516.239,983.425;1,492.396,923.919,528.986,875.335");
        strFLDataList.push_back( "113,6260;0,908.716,1285.626,515.485,976.756;1,494.117,926.344,528.970,871.669");
        strFLDataList.push_back( "114,6280;0,911.588,1290.740,516.920,973.478;1,497.092,931.369,529.600,869.724");
        strFLDataList.push_back( "115,6300;0,919.637,1297.907,517.490,969.004;1,499.398,934.804,530.152,865.297");
        strFLDataList.push_back( "116,6320;0,925.820,1300.732,518.357,959.287;1,501.559,937.361,531.763,862.831");
        strFLDataList.push_back( "117,6340;0,927.691,1305.431,518.382,952.194;1,505.060,942.202,533.152,861.618");
        strFLDataList.push_back( "118,6360;0,934.754,1311.150,519.598,942.171;1,506.304,944.889,532.633,853.539");
        strFLDataList.push_back( "119,6380;0,939.418,1315.256,519.513,931.991;1,507.952,948.831,533.211,850.059");


        strFLDataList.push_back("120,6400;0,943.775,1319.940,521.050,920.984;1,510.007,951.862,533.990,843.744");
        strFLDataList.push_back("121,6420;0,946.310,1324.579,521.157,908.699;1,510.338,954.673,535.221,835.819");
        strFLDataList.push_back("122,6440;0,951.583,1328.947,521.769,893.396;1,514.130,959.224,535.082,829.097");
        strFLDataList.push_back("123,6460;0,955.341,1334.443,522.272,878.397;1,514.560,961.942,535.964,819.934");
        strFLDataList.push_back("124,6480;0,958.962,1338.359,523.223,861.743;1,516.853,964.259,537.364,811.457");
        strFLDataList.push_back("125,6500;0,962.465,1343.104,523.769,843.722;1,519.151,968.470,537.797,802.367");
        strFLDataList.push_back("126,6520;0,965.803,1347.115,524.990,826.338;1,518.948,972.412,538.160,792.683");
        strFLDataList.push_back("127,6540;0,969.610,1353.204,526.159,806.795;1,518.854,974.081,538.665,782.157");
        strFLDataList.push_back("128,6560;0,972.254,1353.597,525.826,788.233;1,522.002,977.989,540.325,772.132");
        strFLDataList.push_back("129,6580;0,973.665,1357.115,526.230,769.460;1,522.300,979.388,540.104,762.790");

        strFLDataList.push_back("130,6600;0,977.779,1360.590,527.509,751.866;1,524.139,982.469,542.396,750.923");
        strFLDataList.push_back("131,6620;0,978.726,1364.192,527.711,734.914;1,525.424,985.552,542.870,742.669");
        strFLDataList.push_back("132,6640;0,983.619,1366.364,528.521,719.807;1,526.094,987.161,542.288,733.520");
        strFLDataList.push_back("133,6660;0,981.730,1368.408,529.558,705.220;1,526.208,989.213,544.377,725.873");
        strFLDataList.push_back("134,6680;0,985.907,1369.590,529.804,692.262;1,526.601,991.062,544.565,718.692");
        strFLDataList.push_back("135,6700;0,987.588,1370.510,531.265,682.376;1,528.338,991.834,545.340,714.180");
        strFLDataList.push_back("136,6720;0,988.100,1369.182,532.077,672.448;1,527.831,993.410,546.581,709.017");
        strFLDataList.push_back("137,6740;0,987.782,1368.667,532.968,663.828;1,527.331,991.793,546.462,704.411");
        strFLDataList.push_back("138,6760;0,989.728,1367.659,533.803,656.125;1,527.963,991.110,546.152,697.886");
        strFLDataList.push_back("139,6780;0,988.555,1362.819,533.345,650.441;1,526.959,991.390,548.620,696.905");

        strFLDataList.push_back( "140,6800;0,990.696,1360.325,535.192,647.591;1,527.971,990.035,549.214,693.867");
        strFLDataList.push_back( "141,6820;0,990.558,1357.021,536.152,641.612;1,526.697,986.924,549.448,690.643");
        strFLDataList.push_back( "142,6840;0,990.979,1352.155,536.127,639.676;1,526.715,984.017,550.662,688.989");
        strFLDataList.push_back( "143,6860;0,989.398,1344.345,537.249,636.779;1,527.326,981.926,551.109,688.459");
        strFLDataList.push_back( "144,6880;0,987.277,1336.022,538.224,634.711;1,525.670,976.830,551.568,687.118");
        strFLDataList.push_back( "145,6900;0,987.487,1329.469,538.960,633.569;1,525.886,973.396,553.067,687.428");
        strFLDataList.push_back( "146,6920;0,985.957,1319.983,539.587,632.878;1,524.167,968.450,554.350,687.727");
        strFLDataList.push_back( "147,6940;0,983.657,1307.188,537.910,631.061;1,523.383,961.182,553.983,684.699");
        strFLDataList.push_back( "148,6960;0,980.948,1299.081,540.858,631.550;1,521.329,956.002,555.030,687.305");
        strFLDataList.push_back( "149,6980;0,977.536,1286.418,540.555,631.048;1,520.358,949.241,555.531,687.809");

        strFLDataList.push_back( "150,7000;0,974.803,1274.010,541.339,631.717;1,518.472,942.512,557.107,687.006");
        strFLDataList.push_back( "151,7020;0,972.675,1262.639,542.114,632.418;1,515.016,933.608,556.675,687.914");
        strFLDataList.push_back( "152,7040;0,966.342,1249.874,542.864,633.238;1,513.792,926.179,557.544,689.217");
        strFLDataList.push_back( "153,7060;0,959.768,1238.379,543.184,633.395;1,510.438,920.970,559.229,689.597");
        strFLDataList.push_back( "154,7080;0,952.171,1230.589,545.000,634.339;1,506.950,915.184,560.198,691.352");
        strFLDataList.push_back( "155,7100;0,947.051,1221.317,545.230,636.346;1,504.292,908.946,561.658,691.280");
        strFLDataList.push_back( "156,7120;0,937.230,1212.160,546.447,636.052;1,500.157,902.153,561.159,691.803");
        strFLDataList.push_back( "157,7140;0,927.405,1205.045,548.018,637.346;1,496.101,897.550,561.495,693.279");
        strFLDataList.push_back( "158,7160;0,917.407,1200.229,549.282,639.248;1,490.814,895.296,565.075,695.411");
        strFLDataList.push_back( "159,7180;0,909.470,1195.226,564.336,656.311;1,484.370,890.424,564.925,694.335");

        strFLDataList.push_back("160,7200;0,893.699,1191.497,565.776,656.462;1,478.178,886.392,567.359,698.182");
        strFLDataList.push_back("161,7220;0,881.066,1191.056,568.858,660.141;1,471.522,885.979,569.039,699.947");
        strFLDataList.push_back("162,7240;0,864.279,1184.652,570.208,660.315;1,463.719,882.698,570.528,700.566");
        strFLDataList.push_back("163,7260;0,846.740,1185.679,571.559,663.264;1,455.853,880.454,571.691,700.120");
        strFLDataList.push_back("164,7280;0,828.022,1183.102,574.750,663.529;1,447.708,881.716,574.471,703.008");
        strFLDataList.push_back("165,7300;0,808.045,1183.589,577.342,666.945;1,437.830,880.053,576.135,704.227");
        strFLDataList.push_back("166,7320;0,787.365,1180.667,578.515,667.026;1,427.828,877.476,576.181,705.382");
        strFLDataList.push_back("167,7340;0,764.441,1180.521,580.671,668.432;1,418.175,876.463,580.214,705.348");
        strFLDataList.push_back("168,7360;0,741.998,1180.431,583.314,670.796;1,407.256,876.782,582.524,707.781");
        strFLDataList.push_back("169,7380;0,720.816,1183.119,586.388,672.195;1,395.711,875.827,583.789,708.780");
        strFLDataList.push_back("170,7400;0,701.258,1182.970,588.861,674.901;1,386.757,875.387,585.365,710.422");
        strFLDataList.push_back("171,7420;0,677.444,1180.629,590.822,674.253;1,376.421,874.158,588.304,713.382");
        strFLDataList.push_back("172,7440;0,657.229,1181.495,593.802,676.540;1,366.136,874.753,589.259,713.616");
        strFLDataList.push_back("173,7460;0,640.240,1182.905,596.680,678.576;1,355.275,875.232,592.064,713.430");
        strFLDataList.push_back("174,7480;0,621.994,1182.453,598.850,679.577;1,348.563,874.370,593.967,714.811");
        strFLDataList.push_back("175,7500;0,609.315,1182.969,601.401,680.160;1,340.926,873.112,595.373,715.830");
        strFLDataList.push_back("176,7520;0,595.341,1184.497,605.152,681.986;1,334.663,873.047,597.028,714.912");
        strFLDataList.push_back("177,7540;0,584.612,1183.396,608.008,683.138;1,328.912,872.867,598.576,716.172");
        strFLDataList.push_back("178,7560;0,575.993,1184.219,611.177,685.219;1,324.014,872.431,601.023,717.131");
        strFLDataList.push_back("179,7580;0,569.564,1187.346,614.428,686.942;1,319.847,871.626,602.952,718.669");


        strFLDataList.push_back("180,7600;0,561.401,1186.138,614.967,686.223;1,317.242,871.556,605.471,720.308");
        strFLDataList.push_back("181,7620;0,556.005,1186.943,619.363,687.691;1,314.355,871.749,607.061,720.390");
        strFLDataList.push_back("182,7640;0,552.253,1188.027,621.586,688.274;1,312.988,871.421,610.512,720.275");
        strFLDataList.push_back("183,7660;0,549.845,1188.393,625.591,689.682;1,310.314,871.329,611.397,722.049");
        strFLDataList.push_back("184,7680;0,548.945,1188.900,628.460,690.389;1,309.123,869.668,613.147,722.325");
        strFLDataList.push_back("185,7700;0,546.245,1188.505,630.636,690.968;1,307.775,868.821,614.235,721.291");
        strFLDataList.push_back("186,7720;0,546.593,1189.500,633.925,692.755;1,307.783,868.422,616.427,720.859");
        strFLDataList.push_back("187,7740;0,543.291,1189.188,637.232,693.316;1,307.259,869.076,620.112,722.570");
        strFLDataList.push_back("188,7760;0,545.673,1190.930,640.549,693.207;1,306.230,867.768,621.225,723.278");
        strFLDataList.push_back("189,7780;0,544.279,1191.242,643.874,695.597;1,306.227,867.712,622.660,722.877");
        strFLDataList.push_back("190,7800;0,545.371,1190.868,647.527,694.448;1,306.296,865.508,624.586,722.125");
        strFLDataList.push_back("191,7820;0,543.139,1192.697,650.764,696.141;1,306.893,866.628,626.921,722.323");
        strFLDataList.push_back("192,7840;0,545.960,1192.716,653.472,697.121;1,306.450,865.947,629.084,724.110");
        strFLDataList.push_back("193,7860;0,545.259,1192.925,657.106,696.466;1,307.018,865.414,631.476,724.037");
        strFLDataList.push_back("194,7880;0,548.062,1192.938,660.778,698.123;1,307.228,864.012,634.203,724.051");
        strFLDataList.push_back("195,7900;0,547.823,1193.763,663.775,698.453;1,307.164,861.471,635.916,722.807");
        strFLDataList.push_back("196,7920;0,549.935,1194.826,667.414,697.842;1,307.006,861.163,636.601,723.581");
        strFLDataList.push_back("197,7940;0,550.309,1193.915,669.590,697.696;1,308.377,861.925,640.211,723.043");
        strFLDataList.push_back("198,7960;0,551.217,1193.865,672.334,698.253;1,308.877,861.351,642.426,723.319");
        strFLDataList.push_back("199,7980;0,552.039,1195.431,677.219,699.428;1,309.160,860.870,644.252,722.601");

        strFLDataList.push_back("200,8000;0,555.624,1193.779,679.669,698.175;1,309.938,860.452,646.242,721.712");
        strFLDataList.push_back("201,8020;0,558.317,1195.251,682.658,697.982;1,310.583,859.335,648.379,722.637");
        strFLDataList.push_back("202,8040;0,557.982,1194.734,686.686,696.976;1,309.925,857.717,648.173,720.457");
        strFLDataList.push_back("203,8060;0,560.396,1197.619,690.615,699.632;1,310.916,856.723,650.030,719.682");
        strFLDataList.push_back("204,8080;0,563.167,1197.808,693.920,700.648;1,312.316,856.609,652.973,721.664");
        strFLDataList.push_back("205,8100;0,564.072,1195.274,696.978,698.749;1,313.061,854.797,654.628,719.880");
        strFLDataList.push_back("206,8120;0,568.889,1198.094,699.934,699.851;1,314.381,853.957,659.770,722.211");
        strFLDataList.push_back("207,8140;0,568.882,1197.480,704.037,699.301;1,315.796,853.268,661.969,721.798");
        strFLDataList.push_back("208,8160;0,572.432,1197.899,707.443,699.640;1,317.211,852.646,663.791,720.918");
        strFLDataList.push_back("209,8180;0,574.100,1196.132,709.237,698.451;1,318.403,851.392,665.804,720.855");
        strFLDataList.push_back("210,8200;0,577.789,1195.963,712.513,697.952;1,318.283,850.640,666.517,720.219");
        strFLDataList.push_back("211,8220;0,579.119,1195.851,715.286,698.712;1,319.791,848.827,667.900,720.701");
        strFLDataList.push_back("212,8240;0,582.732,1197.036,719.534,699.447;1,320.055,848.968,671.174,721.547");
        strFLDataList.push_back("213,8260;0,585.936,1195.301,721.188,698.097;1,322.187,845.671,671.024,717.662");
        strFLDataList.push_back("214,8280;0,587.726,1195.550,724.913,697.042;1,322.228,844.653,673.478,718.267");
        strFLDataList.push_back("215,8300;0,591.593,1196.765,728.006,698.294;1,323.705,844.227,674.902,716.751");
        strFLDataList.push_back("216,8320;0,595.348,1196.943,732.473,697.667;1,323.748,840.200,672.839,712.686");
        strFLDataList.push_back("217,8340;0,597.080,1193.546,734.207,695.963;1,324.948,840.023,673.888,711.550");
        strFLDataList.push_back("218,8360;0,601.302,1196.431,738.418,695.959;1,325.423,838.303,675.569,710.645");
        strFLDataList.push_back("219,8380;0,604.003,1195.920,740.397,695.131;1,326.184,836.680,676.292,710.301");




        strFLDataList.push_back("220,8400;0,607.350,1195.452,743.987,695.248;1,327.818,836.878,679.494,710.315");
        strFLDataList.push_back("221,8420;0,610.275,1192.873,745.256,693.277;1,328.418,833.352,681.413,709.476");
        strFLDataList.push_back("222,8440;0,612.474,1194.091,749.157,694.588;1,329.761,832.899,680.500,709.252");
        strFLDataList.push_back("223,8460;0,616.198,1192.490,751.491,693.661;1,331.823,830.705,683.812,706.488");
        strFLDataList.push_back("224,8480;0,620.660,1193.422,755.104,693.693;1,332.072,829.748,684.438,705.022");
        strFLDataList.push_back("225,8500;0,621.665,1191.928,755.949,691.082;1,332.769,827.309,685.584,704.393");
#endif
        QList<CCmdBase*> objList = m_cmdObjMap.values(iMethodID);
        for(int i=0; i<objList.size(); i++)
        {
            CCmdBase* pObj = objList.at(i);
            qDebug()<<Q_FUNC_INFO<<__LINE__;
            for(int i=0; i<strMeltFLDataList.size(); i++)
            {
                QString oneCycleData = strMeltFLDataList.at(i).toString();
                pObj->receiveMachineCmdReplay(iMachineID, iMethodID, iResult, oneCycleData);
                Delay_MSec(50);
            }
            qDebug()<<Q_FUNC_INFO<<__LINE__;
        }
    }

    //START
    {
        int iMethodID = Method_start;
        int iResult = 0;
        QList<CCmdBase*> objList = m_cmdObjMap.values(iMethodID);
        for(int i=0; i<objList.size(); i++)
        {
            CCmdBase* pObj = objList.at(i);
            qDebug()<<Q_FUNC_INFO<<__LINE__<<pObj->GetClassName();
            pObj->receiveMachineCmdReplay(iMachineID, iMethodID, iResult, QVariant());
            qDebug()<<Q_FUNC_INFO<<__LINE__;
        }
    }
}
