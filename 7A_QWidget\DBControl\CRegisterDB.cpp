#include "CRegisterDB.h"
#include <QDebug>
#include <QApplication>

CRegisterDB *CRegisterDB::m_spInstance = nullptr;

CRegisterDB *CRegisterDB::GetInstance()
{
    if(nullptr == m_spInstance)
        m_spInstance = new CRegisterDB;
    return m_spInstance;
}

CRegisterDB::CRegisterDB() : CSqliteDBBase(QApplication::applicationDirPath() + "/db/register.db", "")
{
    QString strCmd = "create table if not exists register ("
                     "id integer not null primary key autoincrement,"
                     "CardID varchar unique,"
                     "SampleID varchar,"
                     "CT varchar,"
                     "Uplift varchar,"
                     "Threshold varchar,"
                     "FL varchar)";
    _ExecuteDB(strCmd);
}

CRegisterDB::~CRegisterDB()
{

}

int CRegisterDB::GetCount()
{
    QString strCmd = "select count(*) from register";
    QList<QStringList> strList;
    if(_QueryDB(strCmd, strList))
        return _GetFirstValue(strList).toInt();

    return 0;
}

bool CRegisterDB::AddData(const QString &strCardID, const QString &strSampleID, const QString &strCT,
                          const QString &strUplift, const QString &strThreshold, const QString &strFL)
{
    QString strCmd = QString("select * from register where CardID = '%1'").arg(strCardID);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd,strList))
        return false;

    if(!strList.isEmpty())
    {
        qDebug()<<"register数据库中已经存在CardID"<<strCardID;
        strCmd = QString("update register set SampleID = '%1', CT = '%2', Uplift = '%3', Threshold = '%4', "
                         "FL = '%5' where CardID = '%6'")
                .arg(strSampleID).arg(strCT).arg(strUplift).arg(strThreshold).arg(strFL).arg(strCardID);
        return _ExecuteDB(strCmd);
    }

    strCmd = QString("insert into register (CardID, SampleID, CT, Uplift, Threshold, FL) "
                     "values ('%1', '%2', '%3', '%4', '%5', '%6')")
            .arg(strCardID).arg(strSampleID).arg(strCT).arg(strUplift).arg(strThreshold).arg(strFL);
    return _ExecuteDB(strCmd);
}

bool CRegisterDB::AddData(QString strCardID, QString strSampleID, QStringList strCTList,
                          QStringList strUpLiftList, QStringList strThresholdList,
                          QList<QString> strFLList)
{
    QString strCmd = QString("select * from register where CardID = '%1'").arg(strCardID);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd,strList))
        return false;

    if(!strList.isEmpty())
    {
        qDebug()<<"register数据库中已经存在CardID"<<strCardID;
        strCmd = QString("update register set SampleID = '%1', CT = '%2', Uplift = '%3', Threshold = '%4', "
                         "FL = '%5' where CardID = '%6'")
                .arg(strSampleID).arg(strCTList.join(";")).arg(strUpLiftList.join(";"))
                .arg(strThresholdList.join(";")).arg(strFLList.join(";")).arg(strCardID);
        return _ExecuteDB(strCmd);
    }

    strCmd = QString("insert into register (CardID, SampleID, CT, Uplift, Threshold, FL) "
                     "values ('%1', '%2', '%3', '%4', '%5', '%6')")
            .arg(strCardID).arg(strSampleID).arg(strCTList.join(";")).arg(strUpLiftList.join(";"))
            .arg(strThresholdList.join(";")).arg(strFLList.join(";"));
    return _ExecuteDB(strCmd);
}

bool CRegisterDB::DelAll()
{
    QString strCmd = "delete from register";
    return _ExecuteDB(strCmd);
}

bool CRegisterDB::GetFLData(const QString &strCardID, QString &strFL)
{
    QString strCmd = QString("select FL from register where CardID = '%1'").arg(strCardID);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd,strList))
        return false;

    strFL = _GetFirstValue(strList);
    return true;
}

bool CRegisterDB::GetCTInfo(const QString &strCardID, QStringList &strInfoList)
{
    QString strCmd = QString("select CT, Uplift, Threshold from register where CardID = '%1'").arg(strCardID);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd,strList))
        return false;

    strInfoList = _GetRowValueList(strList);
    return true;
}
