#ifndef CNEWLABELDATE_H
#define CNEWLABELDATE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-27
  * Description: QLabel-QLabel 新的日期控件  滑动选择
  * -------------------------------------------------------------------------
  * History: 2025-06-23 hxr 把日期时间控件分离出来:(1)由全屏弹窗改为局部小弹窗(2)节省内存
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QBoxLayout>

#include "CPressLabel.h"

class CNewLabelDate : public QWidget
{
    Q_OBJECT
public:
    CNewLabelDate(const QString &strNameText, const QString &strDateText, int iSpacing = 5,
                  QBoxLayout::Direction eDirection = QBoxLayout::LeftToRight, QWidget *parent = nullptr);
    virtual ~CNewLabelDate();

public:
    QString GetDateString() const;
    void SetDateString(const QString &strDateText);

    void ResetNameLabelSize(int iWidth, int iHeight);
    void ResetDateLabelSize(int iWidth, int iHeight);

signals:
    void SignalPressEvent();

private:
    QLabel *m_pNameLabel;
    CPressLabel *m_pDateLabel;
};

class CVNewLabelDate : public CNewLabelDate
{
    Q_OBJECT
public:
    CVNewLabelDate(const QString &strNameText, const QString &strDateText = "", int iSpacing = 5, QWidget *parent = nullptr);
    ~CVNewLabelDate();
};

class CHNewLabelDate : public CNewLabelDate
{
    Q_OBJECT
public:
    CHNewLabelDate(const QString &strNameText, const QString &strDateText = "", int iSpacing = 16, QWidget *parent = nullptr);
    ~CHNewLabelDate();
};

#endif // CNEWLABELDATE_H
