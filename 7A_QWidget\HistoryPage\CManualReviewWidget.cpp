#include "CManualReviewWidget.h"
#include <QPainter>
#include <QBoxLayout>
#include "CProjectDB.h"
#include "CHistoryDB.h"
#include "PublicFunction.h"
#include "PublicConfig.h"
#include <QStringList>

CManualReviewWidget::CManualReviewWidget(QWidget *parent)
    : QWidget(parent)
    , m_strPositive(tr("阳性"))
    , m_strNegative(tr("阴性"))
    , m_strError(tr("无效"))
    , m_strNull(tr("/"))
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setFixedSize(G_QRootSize);
    this->move(G_QRootPoint);
    this->setAttribute(Qt::WA_TranslucentBackground);
    //this->setWindowModality(Qt::WindowModal);
    qRegisterMetaType<stReviewParam>("stReviewParam");
    _InitWidget();
    _InitLayout();
    LoadQSS(this, ":/qss/qss/history/review.qss");

}

void CManualReviewWidget::SetManualReviewParam(const SResultInfoStruct &sResultInfo, const SLotInfoStruct &sLotInfo,const QString& strTextName)
{
    m_sResultInfo = sResultInfo;
    m_sLotInfo = sLotInfo;
    _InitParamValue(sResultInfo,sLotInfo);
    _SetComboBoxValue();
    m_index = -1;
    int nIndex = 0;
    for(int i = 0 ; i < m_ReviewParamList.size(); i++)
    {
        if(m_ReviewParamList.at(i).m_strName == strTextName)
        {
            nIndex = i;
            break;
        }
    }

    if(m_ReviewParamList.at(nIndex).m_index == -1)
    {
        m_pHoleNameCombo->setCurrentIndex(0);
        m_index = 0;
    }
    else
    {
        m_pHoleNameCombo->setCurrentIndex(m_ReviewParamList.at(nIndex).m_index);
        m_index = m_ReviewParamList.at(nIndex).m_index;
    }
    qDebug()<<Q_FUNC_INFO<<"init cardId: "<<sResultInfo.strCardID<<"index: "<< nIndex; //阈值配置
    if(!m_qEventLoop.isRunning())
        m_qEventLoop.exec();
}
void CManualReviewWidget::_InitParamValue(const SResultInfoStruct& sRestltInfo,const SLotInfoStruct& sLotInfo)
{
    m_ReviewParamList.clear();
    QStringList strNameList = sLotInfo.strCurveName.split(";");
    QStringList strCtinfoList = sRestltInfo.strCTInfo.split(";");
    QStringList strResultList =sRestltInfo.strResult_Review.split(";");
    QStringList strReViewCtinfoList = sRestltInfo.strCTInfo_Review.split(";");
    bool bReview = sRestltInfo.strReview == "m";
    int nSize = strNameList.size();
    for(int i = 0 ; i < nSize; i++)
    {
        stReviewParam stReviewParam;
        stReviewParam.m_bNull = ("0" == strNameList.at(i));
        stReviewParam.m_strName = strNameList.at(i);
        if(stReviewParam.m_bNull)
        {
            m_ReviewParamList.push_back(stReviewParam);
            continue;
        }
        if(IsCtrlTarget(strNameList.at(i)))
        {
            stReviewParam.m_bControl = true;
        }

        if(i < strCtinfoList.size())
        {
            stReviewParam.m_strCtInfo = strCtinfoList.at(i).toFloat() < 0 ? "0" : strCtinfoList.at(i);
        }
        if(bReview && i < strResultList.size())
        {
            if(strResultList.at(i) != "/" && !strResultList.at(i).isEmpty())
            {
                stReviewParam.m_bReview = true;
            }
            else
            {
                stReviewParam.m_bReview = false;
            }
        }


        if(bReview && i < strReViewCtinfoList.size())
        {
            if(strReViewCtinfoList.at(i).split(",").size() >= 4)
            {
                stReviewParam.m_strCtInfo_Review = strReViewCtinfoList.at(i);
            }
            if(i < strResultList.size())
            {
                stReviewParam.m_result =strResultList.at(i);
            }
        }
        m_ReviewParamList.push_back(stReviewParam);
    }
}

void CManualReviewWidget::_SetParamResultValue()
{
    QStringList strResulReviewtList,strCTInfo_ReviewList;
    bool bReview = false;
    for(const auto& item : m_ReviewParamList)
    {
        if(item.m_bReview)
        {
            strCTInfo_ReviewList.push_back(item.m_strCtInfo_Review);
            strResulReviewtList.push_back(item.m_result);
            bReview = true;
        }
        else
        {
            strCTInfo_ReviewList.push_back("/");
            strResulReviewtList.push_back("/");
        }
    }
    if(bReview)
    {
        m_sResultInfo.strReview = bReview?"m":"";
        m_sResultInfo.strCTInfo_Review = strCTInfo_ReviewList.join(";");
        m_sResultInfo.strResult_Review = strResulReviewtList.join(";");
    }
    qDebug()<<Q_FUNC_INFO<<"CtInfo_Review: "<<m_sResultInfo.strCTInfo_Review << "result_Review: " << m_sResultInfo.strResult_Review;

}
void CManualReviewWidget::GetManualReviewParam(SResultInfoStruct &sResultInfo, SLotInfoStruct &sLotInfo)
{
    sResultInfo = m_sResultInfo;
    sLotInfo = m_sLotInfo;
}

void CManualReviewWidget::_SlotOnHoleNameChanged(int index)
{
    //"-1;-1;,,,;"
    // 这里可以读取CtInfoReview ,如果不为空说明这个是读取过得
    // 就可以解析值，填写值；
    _ClearData();
    m_index = index;
    _SetParamValue(m_index);
}

void CManualReviewWidget::_ClearData()
{
    m_pThreshouldEdit->clearData();
    m_pBaseLineEdit->clearData();
    m_pCtValueEdit->clear();
    m_pResultEdit->clear();
}



void CManualReviewWidget::_SetParamValue(int index)
{
    stReviewParam stParam;
    for(const auto& item : m_ReviewParamList)
    {
        if(item.m_index == index)
        {
            stParam = item;
        }
    }
    QString strInfo;
    bool bShow = false;
    if(stParam.m_bReview)
    {
        strInfo = stParam.m_strCtInfo_Review;
        QStringList strInfoList = strInfo.split(",");
        if(strInfoList.size() >= 4)
        {
            m_pCtValueEdit->setText(strInfoList.at(2));
        }

        m_pResultEdit->setText(_GetShowResult(stParam.m_result));
        bShow = true;
    }
    else
    {
        strInfo = stParam.m_strCtInfo;
        bShow = false;
    }
    m_pConfirmBtn->setVisible(bShow);
    qDebug()<<Q_FUNC_INFO<<"changed combobox index:  "<<index << "strInfo: " << strInfo; //阈值配置
    QStringList strInfoList = strInfo.split(",");
    if(strInfoList.size()>=4)
    {
        m_pThreshouldEdit->setText(strInfoList.at(0));
        QString strBaseLine =strInfoList.at(3);
        QStringList strBaseLineList = strBaseLine.split("-");
        if(strBaseLineList.size()>=2)
        {
            m_pBaseLineEdit->setText(QString::number(strBaseLineList.at(0).toInt()),QString::number(strBaseLineList.at(1).toInt()));
        }
    }
}

void CManualReviewWidget::_SetComboBoxValue()
{
    m_pHoleNameCombo->clear();
    int index = 0;
    // 插入非内控靶标
    for (auto& item : m_ReviewParamList)
    {
        if(!item.m_bNull&&!item.m_bControl)
        {
            item.m_index = index;
            m_pHoleNameCombo->addItem(item.m_strName);
            index++;
        }
        else
        {
            item.m_index = -1;
        }
    }
    // 插入内控靶标
    for (auto& item : m_ReviewParamList)
    {
        if(!item.m_bNull&&item.m_bControl)
        {
            item.m_index = index;
            m_pHoleNameCombo->addItem(item.m_strName);
            index++;
        }
    }
}

QString CManualReviewWidget::_GetShowResult(const QString &strResult)
{
    return GetResultFormFlag(strResult);
}

void CManualReviewWidget::_SlotAutoThreshouldBtn()
{
    stReviewParam sReviewParam;
    for(const auto& item : m_ReviewParamList)
    {
        if(item.m_index == m_index)
        {
            sReviewParam  = item;
            break;
        }

    }
    QString strInfo = sReviewParam.m_strCtInfo;
    QStringList strInfoList = strInfo.split(",");
    qDebug()<<Q_FUNC_INFO<<"auto Threshould strInfo: " <<strInfo;
    if(strInfoList.size()>=4)
    {
        m_pThreshouldEdit->setText(strInfoList.at(0));
    }
#if 0
    QStringList strCtInfoList = m_sResultInfo.strCTInfo.split(";");
    QString strFLThresText = "10.00";
    int iIndex = m_index;
    if(0 < iIndex && iIndex < strCtInfoList.size())
    {
        QString strCTInfo = strCtInfoList.at(iIndex);
        QStringList strCTInfoList = strCTInfo.split(",");
        if(strCTInfoList.size()>=4 && strCTInfoList.at(0) != "-1")
        {
            strFLThresText = strCTInfoList.at(0);
        }
    }
    m_pThreshouldEdit->setText(strFLThresText);
#endif
}

void CManualReviewWidget::_SlotAutoBaseLineBtn()
{
    stReviewParam sReviewParam;
    for(const auto& item : m_ReviewParamList)
    {
        if(item.m_index == m_index)
        {
            sReviewParam  = item;
            break;
        }

    }
    QString strInfo = sReviewParam.m_strCtInfo;
    QStringList strInfoList = strInfo.split(",");
    qDebug()<<Q_FUNC_INFO<<"auto baseLine strInfo: " <<strInfo;
    if(strInfoList.size()>=4)
    {
        QString strBaseLine =strInfoList.at(3);
        QStringList strBaseLineList = strBaseLine.split("-");
        if(strBaseLineList.size()>=2)
        {
            m_pBaseLineEdit->setText(QString::number(strBaseLineList.at(0).toInt()),QString::number(strBaseLineList.at(1).toInt()));
        }
    }

#if 0
    QStringList strCtInfoList = m_sResultInfo.strCTInfo.split(";");
    QString strBaseLineStart = "5";
    QString strBaseLineEnd = "20";
    int iIndex = m_index;
    if(0 < iIndex && iIndex < strCtInfoList.size())
    {
        QString strCTInfo = strCtInfoList.at(iIndex);
        QStringList strCTInfoList = strCTInfo.split(",");
        if(strCTInfoList.size()>=4 && !strCTInfoList.at(3).isEmpty())
        {
            QStringList strList = strCTInfoList.at(3).split("-");
            if(strList.size() > 2)
            {
                strBaseLineStart = strList.at(0);
                strBaseLineEnd = strList.at(1);
            }
        }
    }
    m_pBaseLineEdit->setText(strBaseLineStart,strBaseLineEnd);
#endif
}

void CManualReviewWidget::_SlotManualCalcBtn()
{
    m_strResult.clear();
    m_strCtInfo.clear();
    m_fCtValue = 0;

    stReviewParam sReviewParam;
    int index = -1;
    for(int i = 0 ; i < m_ReviewParamList.size(); i++)
    {
        if(m_ReviewParamList.at(i).m_index == m_index)
        {
            sReviewParam  = m_ReviewParamList.at(i);
            index = i;
            break;
        }

    }
    if(index == -1)
    {
        return;
    }

    QString strTestTime = m_sResultInfo.strTestTime;
    strTestTime.remove("-").remove(" ").remove(":");

    QString strHole = QString::number(index/4);
    QStringList strColorList = gk_strColorNameList;
    QString strFLID = "";

    QList<qreal> dAllYDataList; //所有Y的值,用以求最大最小值确定坐标范围
    strFLID = m_sResultInfo.strCardID + "+" + strTestTime + "_" + strHole + "-" + strColorList.at(index%4);
    QList<double> qRealDataSrcY;

    QString strRaw;
    CHistoryDB::GetInstance()->GetCtDataRaw(strFLID, strRaw);
    QStringList strOneList = strRaw.split(",");
    for(int j=0; j<strOneList.size(); j++)
    {
        qRealDataSrcY << strOneList.at(j).toDouble();
    }

    QString strConfig;
    {
        QString strThreshould = m_pThreshouldEdit->getText();
        QString strBaseLineStart,strBaseLineEnd;
        m_pBaseLineEdit->getText(strBaseLineStart,strBaseLineEnd);
        //"抬升值;荧光阈值;一阶导数阈值;基线起始点,基线截止点"
        strConfig +=QString(";");
        strConfig +=QString("%1;").arg(strThreshould);
        strConfig +=QString(";");
        strConfig +=QString("%1-%2;").arg(strBaseLineStart).arg(strBaseLineEnd);
        if(CPublicConfig::GetInstance()->GetDynamicUpValue())
        {
            strConfig += "true;";
        }
        else
        {
            strConfig += "false;";
        }
    }
    m_calcLib.CalcCtValue(qRealDataSrcY,strConfig);
    QStringList strReslutList =  m_calcLib.getResultInfoStringList();

    QString strCTInfo;
    m_fCtValue = 0;
    if(4 == strReslutList.size())
    {
        strCTInfo = strReslutList.join(",");
        m_fCtValue = strReslutList.at(2).toFloat();
    }
    else
    {
        QString strCTInfo = "-1,-1,-1,0-0";
        m_fCtValue = 0;
    }
    m_strCtInfo = strCTInfo;
    QStringList strCtList;
    for(int i = 0 ; i < m_ReviewParamList.size(); i++)
    {
        QString strInfo;
        if(m_ReviewParamList.at(i).m_index == m_index)
        {
            strInfo = m_strCtInfo;
        }
        else
        {
            if(m_ReviewParamList.at(i).m_bReview)
            {
                strInfo = m_ReviewParamList.at(i).m_strCtInfo_Review;
            }
            else
            {
                strInfo = m_ReviewParamList.at(i).m_strCtInfo;
            }
        }
        QStringList strInfoList = strInfo.split(",");
        QString strCtValue = "-1";
        if(strInfoList.size() >= 4)
        {
            strCtValue = strInfoList.at(2);
        }
        strCtList.push_back(strCtValue);
    }
    SSampleInfoStruct sSampleInfo;
    CProjectDB::GetInstance()->GetSampleData(m_sLotInfo.strProjectShowName,m_sResultInfo.strSampleID, m_sResultInfo.strTestTime, sSampleInfo);

    // add by chenhao 验证靶标，验证全血
    bool b3PNMethod = true;
    if("Vector-borne Pathogen Panel" == m_sLotInfo.strProjectShowName
            &&(sSampleInfo.iSampleType == 13|| sSampleInfo.iSampleType == 14))
    {
        b3PNMethod = false;
    }

    QStringList strResultList = _GetAmpResult(strCtList.join(";"),m_sLotInfo.strThresholdValue,m_sLotInfo.strProjectShowName,m_sLotInfo.strCurveBRGYLine,m_sResultInfo.strMode,m_sLotInfo.strPQCCutoffValue,b3PNMethod).split(";");

    if(strResultList.size() > index)
    {
        m_strResult = strResultList.at(index);
    }
    else
    {
        m_strResult = "E";
    }

    m_pCtValueEdit->setText(QString::number(m_fCtValue,'f',2));

    m_pResultEdit->setText(_GetShowResult(m_strResult));
    m_pConfirmBtn->setVisible(true);

    QList<qreal> dFitList = getYListFromQPontF( m_calcLib.getDeltaRnCorrectPointFList());
    QString strFit = DoubleList2StringList(dFitList).join(",");
    //QList<qreal> dBaseLineList = getYListFromQPontF(m_calcLib.getBaseLinePointFList());
    //QString strBaseline = DoubleList2StringList(dBaseLineList).join(",");
    //QList<qreal> dSmoothList = getYListFromQPontF(m_calcLib.getSmoothPointFList());
    //QString strSmooth = DoubleList2StringList(dSmoothList).join(",");
    //QList<qreal> dDeltaList = getYListFromQPontF( m_calcLib.getSigmoidRawDataFList(CCalCTLib::fitModelEm::kDelatRnSigmoid));
    //QString strDelta = DoubleList2StringList(dDeltaList).join(",");
    CHistoryDB::GetInstance()->UpdateReviewCTData(strFLID,strFit,
                                                  "", "", "");

    qDebug()<<Q_FUNC_INFO<<"calc strConfig: " <<strConfig << " resultCtInfo: "<< m_strCtInfo;
}
QString CManualReviewWidget::_GetAmpResult(const QString &strCt, const QString& strCtCutoffValue, const QString& strProjectName, const QString& strHoleName,const QString& strQCTestModel,const QString& strQCCtCutoffValue,bool b3PNMethod)
{
    SPNIParamStruct sPNIParam;
    sPNIParam.init();
    sPNIParam.m_strCt = strCt;
    sPNIParam.m_strCtCutoffValue = strCtCutoffValue;
    sPNIParam.m_b3PNMethod = b3PNMethod;
    sPNIParam.m_strProjectName = strProjectName;
    sPNIParam.m_strHoleName = strHoleName;
    sPNIParam.m_strQCTestModel = strQCTestModel;
    sPNIParam.m_strQCCtCutoff = strQCCtCutoffValue;
    return m_CPNICodeResult.GetResultFromCTValue(sPNIParam);
}

void CManualReviewWidget::_SlotCancelBtn()
{
    m_eStandardBtn = QMessageBox::No;
    if(m_qEventLoop.isRunning())
        m_qEventLoop.quit();
    _SetParamResultValue();
    this->close();
}

void CManualReviewWidget::_SlotConfirmBtn()
{
    // 什么时候隐藏
    // 什么时候显示这个应用
    // 自动计算之后要清除嘛,不用退出不保存显示结果
    // 计算之后才有应用
    stReviewParam stReviewParamTemp;
    for(int i = 0 ; i < m_ReviewParamList.size(); i++)
    {
        if(m_ReviewParamList.at(i).m_index == m_index)
        {
            stReviewParam& sReviewParam  = m_ReviewParamList[i];
            sReviewParam.m_strCtInfo_Review = m_strCtInfo;
            sReviewParam.m_result = m_strResult;
            sReviewParam.m_bReview = true;
            stReviewParamTemp = sReviewParam;
            break;
        }
    }
    emit SignalReviewConfirm(stReviewParamTemp);
    qDebug()<<Q_FUNC_INFO<<"save resultCtInfo: "<< stReviewParamTemp.m_strCtInfo_Review << " result: " << stReviewParamTemp.m_result;

}

void CManualReviewWidget::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, 30));
    QWidget::paintEvent(pEvent);
}

void CManualReviewWidget::showEvent(QShowEvent *pEvent)
{
    QWidget::showEvent(pEvent);
}

void CManualReviewWidget::_InitWidget()
{
    //标题
    int iBackWidth = 580;
    if(eLanguage_Italian == gk_iLanguage || eLanguage_Spanish == gk_iLanguage)
        iBackWidth = 625;

    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(iBackWidth, 480);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");

    m_pCHLabelTitleWidget = new CHLabelTitleWidget(tr("人工审核"));

    m_pHoleNameLabel = new QLabel(tr("靶标:"));
    m_pHoleNameLabel->setFixedSize(118, 56);

    QStringList strList =  {tr("PNaseP"), tr("FluA"), tr("FluB"),tr("RSV"),tr("ADV")};
    m_pHoleNameCombo = new QComboBox;
    m_pHoleNameCombo->setFixedSize(250, 56);
    m_pHoleNameCombo->setView(new QListView);
    m_pHoleNameCombo->addItems(strList);
    connect(m_pHoleNameCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &CManualReviewWidget::_SlotOnHoleNameChanged);

    // 阈值
    m_pThreshouldLabel = new QLabel(tr("阈值:"));
    m_pThreshouldLabel->setFixedSize(118, 56);

    m_pThreshouldEdit = new CLineEditSpinBox;
    m_pThreshouldEdit->setFixedSize(250, 56);

    m_pAutoThreshouldBtn = new QPushButton(tr("自动阈值"));
    m_pAutoThreshouldBtn->setFixedSize(130, 56);
    connect(m_pAutoThreshouldBtn,&QPushButton::clicked,this,&CManualReviewWidget::_SlotAutoThreshouldBtn);

    // 基线

    m_pBaseLineLabel = new QLabel(tr("基线区:"));
    //m_pBaseLineLabel->setFixedSize(118, 56);
    m_pBaseLineEdit = new CLineTwoEdit;


    m_pAutoBaseLineBtn = new QPushButton(tr("自动基线"));
    m_pAutoBaseLineBtn->setFixedSize(130, 56);
    connect(m_pAutoBaseLineBtn,&QPushButton::clicked,this,&CManualReviewWidget::_SlotAutoBaseLineBtn);
    // 基线end

    // 手动矫正
    m_pManualCalcBtn = new QPushButton(tr("开始分析"));
    m_pManualCalcBtn->setFixedSize(130, 56);
    connect(m_pManualCalcBtn,&QPushButton::clicked,this,&CManualReviewWidget::_SlotManualCalcBtn);

    m_pCtLabel = new QLabel(tr("Ct:"));
    m_pCtLabel->setFixedSize(40, 56);
    // 250+100  175
    m_pCtValueEdit =  new CLineEdit;
    m_pCtValueEdit->setFixedSize(85, 56);
    m_pCtValueEdit->setReadOnly(true);

    m_pResultLabel = new QLabel(tr("结果:"));
    //m_pResultLabel->setFixedSize(85, 56);

    m_pResultEdit =  new CLineEdit;
    m_pResultEdit->setFixedSize(130, 56);
    m_pResultEdit->setReadOnly(true);

    m_pCancelBtn = new QPushButton(tr("退出"));
    m_pCancelBtn->setFixedSize(150, 56);
    m_pCancelBtn->setObjectName("CancelBtn");
    connect(m_pCancelBtn, &QPushButton::clicked, this, &CManualReviewWidget::_SlotCancelBtn);

    m_pConfirmBtn = new QPushButton(tr("应用"));
    m_pConfirmBtn->setFixedSize(150, 56);
    connect(m_pConfirmBtn, &QPushButton::clicked, this, &CManualReviewWidget::_SlotConfirmBtn);

}

void CManualReviewWidget::_InitLayout()
{
    QGridLayout *pGridLayout = new QGridLayout;
    pGridLayout->setMargin(0);
    pGridLayout->setSpacing(20);
    pGridLayout->addWidget(m_pHoleNameLabel, 0, 0);
    pGridLayout->addWidget(m_pHoleNameCombo, 0, 1, 1, 2);

    pGridLayout->addWidget(m_pThreshouldLabel, 1, 0);
    pGridLayout->addWidget(m_pThreshouldEdit, 1, 1,1,2);
    pGridLayout->addWidget(m_pAutoThreshouldBtn, 1, 3);


    pGridLayout->addWidget(m_pBaseLineLabel, 2, 0);
    pGridLayout->addWidget(m_pBaseLineEdit, 2, 1,1,2);
    pGridLayout->addWidget(m_pAutoBaseLineBtn, 2, 3);

    QHBoxLayout *pResultLayout = new QHBoxLayout;
    pResultLayout->setMargin(0);
    pResultLayout->setSpacing(0);
    pResultLayout->addWidget(m_pCtLabel);
    pResultLayout->addWidget(m_pCtValueEdit);
    pResultLayout->addStretch(1);
    pResultLayout->addWidget(m_pResultLabel);
    pResultLayout->addWidget(m_pResultEdit);
    pGridLayout->addLayout(pResultLayout,3,0,1,3);
    pGridLayout->addWidget(m_pManualCalcBtn, 3, 3);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(60);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pCancelBtn);
    pBtnLayout->addWidget(m_pConfirmBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pBackLayout = new QVBoxLayout;
    pBackLayout->setContentsMargins(24, 12, 24, 24);
    pBackLayout->setSpacing(40);
    pBackLayout->addWidget(m_pCHLabelTitleWidget, 0, Qt::AlignLeft);
    pBackLayout->addLayout(pGridLayout);
    pBackLayout->addStretch(1);
    pBackLayout->addLayout(pBtnLayout);
    m_pBackgroundLabel->setLayout(pBackLayout);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setContentsMargins(24, 24, 24, 24);
    pLayout->addItem(new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum));
    pLayout->addWidget(m_pBackgroundLabel, 0, Qt::AlignRight|Qt::AlignBottom);
    this->setLayout(pLayout);
}

