#ifndef CPOWERPAGE_H
#define CPOWERPAGE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-26
  * Description: 电源管理: 关机; 重启; 注销; 锁屏; 返回
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QGroupBox>
#include <QPushButton>

#include "CLockScreenPage.h"

class CPowerPage : public QWidget
{
    Q_OBJECT
public:
    explicit CPowerPage(QWidget *parent = nullptr);
    ~CPowerPage();

public slots:
    void SlotStartReadVersion();

protected:
    void paintEvent(QPaintEvent *pEvent) override;

signals:
    void SignalLogout();
    void Signal2Show();

private slots:
    void _SlotShutdownBtn();
    void _SlotRebootBtn();
    void _SlotLogoutBtn();
    void _SlotLockScreenBtn();
    void _SlotReturnBtn();
    void _Slot2Show();

private:
    void _ThreadReadI2C();
    int _SetI2C(QString strI2CName);
    void _WriteI2C(char chAction);
    int _ReadI2C(int fd, char* pBuff, int iLen);

private:
    QGroupBox *_CreateGroupBox();

private:
    int m_fd;
    bool m_bThreadRunning;
    bool m_bReadVersion;
    QString m_strVersion;

private:
    QPushButton *m_pShutdownBtn, *m_pRebootBtn, *m_pLogoutBtn, *m_pLockScreenBtn, *m_pReturnBtn;
    QLabel *m_pShutdownLabel, *m_pRebootLabel, *m_pLogoutLabel, *m_pLockScreenLabel, *m_pReturnLabel;

    CLockScreenPage *m_pCLockScreenPage;
};

#endif // CPOWERPAGE_H
