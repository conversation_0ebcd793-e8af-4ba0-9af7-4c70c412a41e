#ifndef  KeyBoard_H
#define  KeyBoard_H

#include "ui_keyboard.h"

#include <QtGui>


enum{
    KEYBOARD_TYPE_NUM,
    KEYBOARD_TYPE_SYMBOL,
    KEYBOARD_TYPE_MAIN,
};

class PingYinSelector;
class KeyBoardButtons;

class KeyBoard : public QWidget, Ui::KeyBoard
{
    Q_OBJECT

public:
    friend class PingYinSelector;
    friend class KeyBoardButtons;

    QLineEdit   *oriEdt;
    QLabel      *titleLbl;
    QTextEdit   *oriTedt;

    KeyBoard(QWidget * parent = 0,
                Qt::WindowFlags fl = Qt::X11BypassWindowManagerHint| Qt::Window |
                Qt::WindowStaysOnTopHint | Qt::FramelessWindowHint| Qt::Popup);

    ~KeyBoard();

    static void SetKeyType(int type);

    void Show();

    void SetHanziText( const QString& text );

    void GotoSymbolKey(int symbolType);

    void GotoPrevKey();

    void GenerateEnterChar();

    void GenerateBackSpaceChar();

    void HideKeyBoard();

    void SetPagingEnabled(const bool&);

    void SetColor(const QString& backgroundColor, const QString& buttonColor,
                  const QString &textColor, const QString& pressedColor);

    void SetFontFamily(const QString& fontFamily);

    void SetFontPixelSize(const int& size);

    void SetPageSize(const int& size);

   // virtual void setVisible(bool visible);

signals:
    void SignalCharacterGenerated(QChar);

    void SignalUpdatePosition(QWidget *widget);

    void SignalEditFinished();

protected:
    bool event(QEvent *e);

    void changeEvent(QEvent *event);

private:
    void SendKeyEvent(QKeyEvent *keyEvent);

    bool HandleKeyboardCharacterInput(int key, int modifier);

    bool HandleKeyboardPinyinInput(int key, int modifier);

    bool HandleKeyboardChineseSymbolInput(int key, int modifier);

    void HandleInput(const QString &input);

    void ReadStyleSheet();

private slots:
    void SlotSetDispText(const QString& text);

    void SlotCapslockClicked();

    void SlotBackSpaceBtnClicked();

    void SlotEscBtnClicked();

    void SlotLanguageBtnClicked();

    void SlotGotoNumBtnClicked();

    void SlotGotoSymbolBtnClicked();

    void SlotFocusChanged(QWidget *oldFocus, QWidget *newFocus);

    void SlotPressedSmMapped(QWidget *);

    void SlotReleaseSmMapped(QWidget *);

    void SlotKeyPressed(int,int);

    void SlotLongPressTimerOut();

    void SlotUpdatePosition(QWidget *widget);

private:
    class PrivateData;
    PrivateData *const md;
};

#endif //  KeyBoard_H
