#include "CMachineCmd.h"
#include <QBoxLayout>
#include <QListView>
#include <QHeaderView>
#include <QStyleFactory>
#include "CMessageBox.h"

CMachineCmd::CMachineCmd(QWidget *parent) : QWidget(parent)
{
    _InitCmdInfo();

    QList<int> iIDList = m_iCmdIndexIDMap.values();
    for(int i=0; i<iIDList.size(); i++)
        Register2Map(iIDList.at(i));

    Register2Map(Method_env_temp);
    Register2Map(Method_notify_flag);
    Register2Map(Method_pcr_set_info_interval);

    _InitWidget();
    _InitLayout();

    for(int i=0; i<gk_iMachineCount; i++)
    {
        m_sUiInfoStructList << _SUiInfoStruct();
    }
}

CMachineCmd::~CMachineCmd()
{
    QList<int> iIDList = m_iCmdIndexIDMap.values();
    for(int i=0; i<iIDList.size(); i++)
        UnRegister2Map(iIDList.at(i));

    UnRegister2Map(Method_env_temp);
    UnRegister2Map(Method_notify_flag);
    UnRegister2Map(Method_pcr_set_info_interval);
}

void CMachineCmd::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    QString strLog = QString("%1# %2 %3").arg(iMachineID + 1).arg(iMethodID).arg(iResult);
    m_pTextBrowser->AppendLog(strLog);

    if(Method_env_temp == iMethodID)
    {
        if(0 != iResult)
        {
            strLog = QString("%1#读取环境失败").arg(iMachineID + 1);
        }
        else
        {
            QVariantList qVarList = qVarData.toList();
            if(!qVarList.isEmpty())
            {
                double iTemp = qVarList.first().toDouble();
                strLog = QString("%1#读取环境成功:%2").arg(iMachineID + 1).arg(iTemp);
            }
        }
    }
    else if(Method_notify_flag == iMethodID)
    {
        if(0 == iResult)
            strLog = QString("%1#设置上传数据成功").arg(iMachineID + 1);
        else
            strLog = QString("%1#设置上传数据失败").arg(iMachineID + 1);
    }
    else if(Method_pcr_set_info_interval == iMethodID)
    {
        if(0 == iResult)
            strLog = QString("%1#设置温控上传间隔成功").arg(iMachineID + 1);
        else
            strLog = QString("%1#设置温控上传间隔失败").arg(iMachineID + 1);
    }

    qDebug()<<strLog;
    m_pTextBrowser->AppendLog(strLog);
}

void CMachineCmd::_SlotSendButton()
{
    QComboBox *pComboBox = dynamic_cast<QComboBox *>(m_pCmdTableWidget->cellWidget(0, 0));
    if(!pComboBox)
        return;
    int index = pComboBox->currentIndex();
    int iCmdID = m_iCmdIndexIDMap.value(index);
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();

    QVariantList qVarList;

    bool bHtst = false;
    if(Method_HTST == iCmdID || Method_HTSP  == iCmdID)
    {
        bHtst = true;
    }
    for(int i=1; i<=3; i++)
    {
        QComboBox *pComboBox = dynamic_cast<QComboBox *>(m_pCmdTableWidget->cellWidget(0, i));
        if(pComboBox)
        {
            if(bHtst){
                qVarList.push_back(CPublicConfig::GetInstance()->GetPyroLysisParamId((pComboBox->currentIndex())));
            }
            else{
                qVarList.push_back(pComboBox->currentIndex());
            }
        }


        CLineEdit *pLineEdit = dynamic_cast<CLineEdit *>(m_pCmdTableWidget->cellWidget(0, i));
        if(pLineEdit)
        {
            qVarList.push_back(pLineEdit->text().toDouble());
        }
    }

    QString strCmd;
    if(qVarList.isEmpty())
        strCmd = GetJsonCmdString(iCmdID);
    else
        strCmd = GetJsonCmdString(iCmdID, qVarList);
    SendJsonCmd(iMachineID, iCmdID, strCmd);

    QString strLog = QString("%1# %2").arg(iMachineID + 1).arg(strCmd);
    m_pTextBrowser->AppendLog(strLog);
}

void CMachineCmd::_SlotCmdComboBoxChanged(int index)
{
    int iRow = 0;
    int iCmdID = m_iCmdIndexIDMap.value(index);
    m_pCmdTableWidget->setCellWidget(iRow, 1, nullptr);
    m_pCmdTableWidget->setCellWidget(iRow, 2, nullptr);
    m_pCmdTableWidget->setCellWidget(iRow, 3, nullptr);

    switch (iCmdID)
    {
    case Method_DELAY:
    case Method_loop:
    case Method_PEXT:
    case Method_PINJ:
    case Method_PETMC:
    case Method_PITEC:
    case Method_PITDC:
    case Method_PFEXT:
    case Method_US_AMP:
    {
        CLineEdit *pLineEdit = new CLineEdit;
        m_pCmdTableWidget->setCellWidget(iRow, 1, pLineEdit);
        m_pCmdTableWidget->setCellWidget(iRow, 2, nullptr);
        m_pCmdTableWidget->setCellWidget(iRow, 3, nullptr);
        break;
    }
    case Method_jump:
    case Method_wait_signal:
    case Method_HTST:
    {
        QStringList strTextList;
        if(Method_jump == iCmdID || Method_wait_signal == iCmdID)
            strTextList = m_strRelList;
        else if(Method_HTST == iCmdID)
            strTextList = m_strHTOpenList;

        QComboBox *pComboBox = new QComboBox;
        pComboBox->setView(new QListView);
        pComboBox->addItems(strTextList);
        m_pCmdTableWidget->setCellWidget(iRow, 1, pComboBox);

        CLineEdit *pLineEdit = new CLineEdit;
        m_pCmdTableWidget->setCellWidget(iRow, 2, pLineEdit);
        m_pCmdTableWidget->setCellWidget(iRow, 3, nullptr);
        break;
    }
    case Method_FLLED:
    case Method_FLADC:
    case Method_FLCST:
    case Method_FLMST:
    case Method_HTSP:
    {
        QStringList strTextList;
        if(Method_FLLED == iCmdID)
            strTextList = m_strLightList;
        else if(Method_FLADC == iCmdID || Method_FLCST == iCmdID || Method_FLMST == iCmdID)
            strTextList = m_strPDList;
        else if(Method_HTSP == iCmdID)
            strTextList = m_strHTCloseList;

        QComboBox *pComboBox = new QComboBox;
        pComboBox->setView(new QListView);
        pComboBox->addItems(strTextList);
        m_pCmdTableWidget->setCellWidget(iRow, 1, pComboBox);
        m_pCmdTableWidget->setCellWidget(iRow, 2, nullptr);
        m_pCmdTableWidget->setCellWidget(iRow, 3, nullptr);
        break;
    }
    case Method_MOVE:
    {
        QComboBox *pComboBox1 = new QComboBox;
        pComboBox1->setView(new QListView);
        pComboBox1->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        pComboBox1->setMaxVisibleItems(10);
        pComboBox1->setStyle(QStyleFactory::create("Windows"));
        pComboBox1->addItems(m_strMotorNameList);
        m_pCmdTableWidget->setCellWidget(iRow, 1, pComboBox1);

        QComboBox *pComboBox2 = new QComboBox;
        pComboBox2->setView(new QListView);
        pComboBox2->addItems(m_strHomeEndList);
        m_pCmdTableWidget->setCellWidget(iRow, 2, pComboBox2);

        CLineEdit *pLineEdit = new CLineEdit;
        m_pCmdTableWidget->setCellWidget(iRow, 3, pLineEdit);
        break;
    }
    default:
        break;
    }
}

void CMachineCmd::_SlotReadEnvTempBtn()
{
    QString strCmd = GetJsonCmdString(Method_env_temp);
    SendJsonCmd(m_pMachineComboBox->GetCurrentIndex(), Method_env_temp, strCmd);
}

void CMachineCmd::_SlotMachineChanged(int iMachineID)
{
    const _SUiInfoStruct &sUiInfo = m_sUiInfoStructList.at(iMachineID);

    m_pVMCheckBox->setChecked(sUiInfo.bVM);
    m_pFLCheckBox->setChecked(sUiInfo.bFL);
    m_pHTCheckBox->setChecked(sUiInfo.bHT);
    m_pPressureCheckBox->setChecked(sUiInfo.bPressure);
    m_pPCRCheckBox->setChecked(sUiInfo.bPCR);
    m_pPCRIntevalLineEdit->setText(sUiInfo.strInterval);
}

void CMachineCmd::_SlotIntervalValueChanged(const QString &strValue)
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_sUiInfoStructList[iMachineID].strInterval = strValue;
}

void CMachineCmd::_SlotVMCheckBox(bool bClicked)
{
    Q_UNUSED(bClicked);
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_sUiInfoStructList[iMachineID].bVM = m_pVMCheckBox->isChecked();
}

void CMachineCmd::_SlotFLCheckBox(bool bClicked)
{
    Q_UNUSED(bClicked);
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_sUiInfoStructList[iMachineID].bFL = m_pFLCheckBox->isChecked();
}

void CMachineCmd::_SlotHTCheckBox(bool bClicked)
{
    Q_UNUSED(bClicked);
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_sUiInfoStructList[iMachineID].bHT = m_pHTCheckBox->isChecked();
}

void CMachineCmd::_SlotPressureCheckBox(bool bClicked)
{
    Q_UNUSED(bClicked);
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_sUiInfoStructList[iMachineID].bPressure = m_pPressureCheckBox->isChecked();
}

void CMachineCmd::_SlotPCRCheckBox(bool bClicked)
{
    Q_UNUSED(bClicked);
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_sUiInfoStructList[iMachineID].bPCR = m_pPCRCheckBox->isChecked();
}

void CMachineCmd::_SlotPCRIntevalBtn()
{
    if(m_pPCRIntevalLineEdit->text().isEmpty())
    {
        ShowInformation(this, tr("提示"), tr("请输入温控数据上传间隔"));
        return;
    }

    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    int iInteval = m_pPCRIntevalLineEdit->text().toInt();
    if(iInteval < 0)
        iInteval = 0;
    QVariantList qVarList = {iInteval};
    QString strCmd = CCmdBase::GetJsonCmdString(Method_pcr_set_info_interval, qVarList);
    QString strLog = QString("%1#温控数据上传间隔:%2").arg(iMachineID + 1).arg(strCmd);
    qDebug()<<strLog;
    m_pTextBrowser->AppendLog(strLog);
    SendJsonCmd(iMachineID, Method_pcr_set_info_interval, strCmd);
}

void CMachineCmd::_SlotNotifyBtn()
{
    QVariantMap qVarMap;
    qVarMap.insert("VM", m_pVMCheckBox->isChecked() ? 1 : 0);
    qVarMap.insert("FL", m_pFLCheckBox->isChecked() ? 1 : 0);
    qVarMap.insert("HT", m_pHTCheckBox->isChecked() ? 1 : 0);
    qVarMap.insert("Pressure", m_pPressureCheckBox->isChecked() ? 1 : 0);
    qVarMap.insert("PCR", m_pPCRCheckBox->isChecked() ? 1 : 0);

    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    QString strCmd = GetJsonCmdString(Method_notify_flag, qVarMap);
    QString strLog = QString("%1#设置上传数据开关:%2").arg(iMachineID + 1).arg(strCmd);
    qDebug()<<strLog;
    m_pTextBrowser->AppendLog(strLog);
    SendJsonCmd(iMachineID, Method_notify_flag, strCmd);
}

void CMachineCmd::_InitCmdInfo()
{
    m_strCmdTextList << tr("整机自检")
                     << tr("电机复位")
                     << tr("走指定步数")
                     << tr("启动PCR")
                     << tr("停止PCR")
                     << tr("等待PCR信号")
                     << tr("开启LED灯")
                     << tr("获取荧光数据")
                     << tr("开启连续采光")
                     << tr("结束连续采光")
                     << tr("启动运动采光")
                     << tr("结束运动采光")
                     << tr("启动加热")
                     << tr("停止加热")
                     << tr("启动超声")
                     << tr("停止超声")
                     << tr("延迟")
                     << tr("循环开始")
                     << tr("循环")
                     << tr("跳转")
                     << tr("开启压力检测")
                     << tr("停止压力检测")
                     << tr("设置超声振幅")
                     << tr("气嘴1接泵/气嘴2接大气")
                     << tr("气嘴2接泵/气嘴1接大气")
                     << tr("泵接大气")
                     << tr("打开散热风扇")
                     << tr("关闭散热风扇");

    m_strParams2List << tr("自定义") << tr("可替换");
    m_strParams3List << tr("自定义") << tr("可替换") << tr("补偿替换");
    m_strParams4List << tr("自定义") << tr("可替换") << tr("复位") << tr("运行");
    m_strParams6List << tr("自定义") << tr("可替换") << tr("复位") << tr("运行") << tr("档位") << tr("替换档位");
    m_strLightCoverList << tr("覆盖(0)") << tr("非覆盖(1)");
    m_strHomeEndList << tr("HOME(0)") << tr("END(1)");

    for(int i=0; i<=10; i++)
        m_strNumber10List.push_back(QString::number(i));
    for(int i=0; i<=4; i++)
        m_strNumber4List.push_back(QString::number(i));

    m_strLightList << tr("两个灯关") << tr("1号灯开") << tr("2号灯开") << tr("1+2灯开");
    m_strPDList << tr("PD1") << tr("PD2") << tr("PD1+PD2");
    m_strHTOpenList = CPublicConfig::GetInstance()->GetPyrolysisStringList(Method_HTST);
    m_strHTCloseList = CPublicConfig::GetInstance()->GetPyrolysisStringList(Method_HTSP);
    m_strRelList << tr("REL(0)") << tr("ABS(1)");
    m_strStopList << tr("硬停") << tr("软停") << tr("硬停补偿") << tr("软停补偿");
    m_strMotorNameList << CPublicConfig::GetInstance()->GetExtractMotorNameList();

    int index = 0;
    m_iCmdIndexIDMap.clear();
    m_iCmdIndexIDMap.insert(index++, Method_MCHK);    //整机自检
    m_iCmdIndexIDMap.insert(index++, Method_SRST);    //电机复位
    m_iCmdIndexIDMap.insert(index++, Method_MOVE);    //走指定步数
    m_iCmdIndexIDMap.insert(index++, Method_pcr_start);  //启动PCR
    m_iCmdIndexIDMap.insert(index++, Method_pcr_stop);   //停止PCR
    m_iCmdIndexIDMap.insert(index++, Method_wait_signal);//等待PCR信号
    m_iCmdIndexIDMap.insert(index++, Method_FLLED); //开启LED灯
    m_iCmdIndexIDMap.insert(index++, Method_FLADC); //获取荧光数据
    m_iCmdIndexIDMap.insert(index++, Method_FLCST); //开启连续采光
    m_iCmdIndexIDMap.insert(index++, Method_FLCSP); //结束连续采光
    m_iCmdIndexIDMap.insert(index++, Method_FLMST); //启动运动采光
    m_iCmdIndexIDMap.insert(index++, Method_FLMSP); //结束运动采光
    m_iCmdIndexIDMap.insert(index++, Method_HTST);  //启动加热
    m_iCmdIndexIDMap.insert(index++, Method_HTSP);  //停止加热
    m_iCmdIndexIDMap.insert(index++, Method_US_USST); //启动超声
    m_iCmdIndexIDMap.insert(index++, Method_US_USSP); //停止超声
    m_iCmdIndexIDMap.insert(index++, Method_DELAY);   //延时
    m_iCmdIndexIDMap.insert(index++, Method_loop_st); //循环开始
    m_iCmdIndexIDMap.insert(index++, Method_loop);    //循环
    m_iCmdIndexIDMap.insert(index++, Method_jump);    //跳转
    m_iCmdIndexIDMap.insert(index++, Method_pressure_on);    //开启压力检测
    m_iCmdIndexIDMap.insert(index++, Method_pressure_stop);  //停止压力检测
    m_iCmdIndexIDMap.insert(index++, Method_US_AMP);         //设置超声振幅
    m_iCmdIndexIDMap.insert(index++, Method_N1CP_N2CA);      //气嘴1接泵/气嘴2接大气
    m_iCmdIndexIDMap.insert(index++, Method_N2CP_N1CA);      //气嘴2接泵/气嘴1接大气
    m_iCmdIndexIDMap.insert(index++, Method_PumpConAir);     //泵接大气
    m_iCmdIndexIDMap.insert(index++, Method_valve_fan_on);   //打开蜡阀风扇
    m_iCmdIndexIDMap.insert(index++, Method_valve_fan_off);  //关闭蜡阀风扇
}

void CMachineCmd::_InitWidget()
{
    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(80, 50);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMachineChanged(int)));

    m_pCmdTableWidget = new QTableWidget;
    m_pCmdTableWidget->setFixedHeight(50);
    m_pCmdTableWidget->setColumnCount(4);
    m_pCmdTableWidget->setRowCount(1);

    QHeaderView *pVerticalHeader = m_pCmdTableWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(50);
    QHeaderView *pHorizontalHeader = m_pCmdTableWidget->horizontalHeader();
    pHorizontalHeader->resizeSection(0, 350);
    pHorizontalHeader->setSectionResizeMode(1, QHeaderView::Stretch);
    pHorizontalHeader->setSectionResizeMode(2, QHeaderView::Stretch);
    pHorizontalHeader->setSectionResizeMode(3, QHeaderView::Stretch);
    pHorizontalHeader->setVisible(false);

    m_pCmdTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pCmdTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pCmdTableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_pCmdTableWidget->setShowGrid(true);
    m_pCmdTableWidget->setFocusPolicy(Qt::NoFocus);

    QString strQSS = "QLineEdit{border-radius: 0px; border: 1px solid #CAD2DC; font-size: 20px;}"
                     "QComboBox{border-radius: 0px; border: 1px solid #CAD2DC; font-size: 20px;}"
                     "QCheckBox::indicator{width: 35px; height: 35px; subcontrol-position:center  center;}";
    m_pCmdTableWidget->setStyleSheet(strQSS);

    QComboBox *pComboBox = new QComboBox;
    pComboBox->setView(new QListView);
    pComboBox->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    pComboBox->setMaxVisibleItems(10);
    pComboBox->setStyle(QStyleFactory::create("Windows"));
    pComboBox->addItems(m_strCmdTextList);
    connect(pComboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotCmdComboBoxChanged(int)));
    m_pCmdTableWidget->setCellWidget(0, 0, pComboBox);
    m_pCmdTableWidget->setCellWidget(0, 1, nullptr);
    m_pCmdTableWidget->setCellWidget(0, 2, nullptr);
    m_pCmdTableWidget->setCellWidget(0, 3, nullptr);

    m_pSendBtn = new QPushButton(tr("发送"));
    m_pSendBtn->setFixedSize(80, 50);
    connect(m_pSendBtn, &QPushButton::clicked, this, &CMachineCmd::_SlotSendButton);

    m_pReadTempBtn = new QPushButton(tr("读取环境温度"));
    m_pReadTempBtn->setFixedSize(230, 50);
    connect(m_pReadTempBtn, &QPushButton::clicked, this, &CMachineCmd::_SlotReadEnvTempBtn);

    m_pPCRIntevalLineEdit = new CLineEdit;
    m_pPCRIntevalLineEdit->setFixedSize(150, 50);
    connect(m_pPCRIntevalLineEdit, &CLineEdit::textChanged, this, &CMachineCmd::_SlotIntervalValueChanged);

    m_pPCRIntevalBtn = new QPushButton(tr("温控上传间隔") + tr("(毫秒)"));
    m_pPCRIntevalBtn->setFixedSize(220, 50);
    connect(m_pPCRIntevalBtn, &QPushButton::clicked, this, &CMachineCmd::_SlotPCRIntevalBtn);

    m_pVMCheckBox = new QCheckBox(tr("时序步骤"));
    m_pVMCheckBox->setChecked(true);
    connect(m_pVMCheckBox, &QCheckBox::clicked, this, &CMachineCmd::_SlotVMCheckBox);

    m_pFLCheckBox = new QCheckBox(tr("荧光"));
    m_pFLCheckBox->setChecked(true);
    connect(m_pFLCheckBox, &QCheckBox::clicked, this, &CMachineCmd::_SlotFLCheckBox);

    m_pHTCheckBox = new QCheckBox(tr("热裂解"));
    m_pHTCheckBox->setChecked(true);
    connect(m_pHTCheckBox, &QCheckBox::clicked, this, &CMachineCmd::_SlotHTCheckBox);

    m_pPressureCheckBox = new QCheckBox(tr("气压"));
    m_pPressureCheckBox->setChecked(true);
    connect(m_pPressureCheckBox, &QCheckBox::clicked, this, &CMachineCmd::_SlotPressureCheckBox);

    m_pPCRCheckBox = new QCheckBox(tr("温控"));
    m_pPCRCheckBox->setChecked(true);
    connect(m_pPCRCheckBox, &QCheckBox::clicked, this, &CMachineCmd::_SlotPCRCheckBox);

    m_pNotifyBtn = new QPushButton(tr("设置上传数据"));
    m_pNotifyBtn->setFixedSize(150, 50);
    connect(m_pNotifyBtn, &QPushButton::clicked, this, &CMachineCmd::_SlotNotifyBtn);

    m_pTextBrowser = new CTextBrowser;
}

void CMachineCmd::_InitLayout()
{
    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(15);
    pTopLayout->addWidget(m_pMachineComboBox);
    pTopLayout->addWidget(m_pCmdTableWidget);
    pTopLayout->addWidget(m_pSendBtn);
    pTopLayout->addSpacing(10);

    QHBoxLayout *pNotifyLayout = new QHBoxLayout;
    pNotifyLayout->setMargin(0);
    pNotifyLayout->setSpacing(20);
    pNotifyLayout->addWidget(m_pVMCheckBox);
    pNotifyLayout->addWidget(m_pFLCheckBox);
    pNotifyLayout->addWidget(m_pHTCheckBox);
    pNotifyLayout->addWidget(m_pPressureCheckBox);
    pNotifyLayout->addWidget(m_pPCRCheckBox);
    pNotifyLayout->addWidget(m_pNotifyBtn);
    pNotifyLayout->addSpacing(20);
    pNotifyLayout->addWidget(m_pPCRIntevalLineEdit);
    pNotifyLayout->addWidget(m_pPCRIntevalBtn);
    pNotifyLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(20);
    pLayout->addLayout(pTopLayout);
    pLayout->addWidget(m_pReadTempBtn, 0, Qt::AlignLeft);
    pLayout->addLayout(pNotifyLayout);
    pLayout->addWidget(m_pTextBrowser);
    this->setLayout(pLayout);
}
