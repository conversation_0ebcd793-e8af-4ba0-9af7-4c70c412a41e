﻿#ifndef _HL7_SEGMENT_COLLECTION_H_
#define _HL7_SEGMENT_COLLECTION_H_

#include <vector>
#include "HL7Segment.h"
class HL7SegmentCollection
{
public:
	HL7SegmentCollection();

	~HL7SegmentCollection();

	/*
	*@brief 在末尾添加段元素
	*/
	void AddSegment(HL7Segment* segment);

	/*
	 *@brief 在index位置插入段元素，合法的index从0开始
	 */
	void AddSegment(HL7Segment* segment, std::size_t index);

	/*
	*@brief 删除指定位置的段元素,合法的index从0开始,并返回是否删除成功
	*/
	bool DeleteSegment(HL7Segment* segment, std::size_t index);

	/*
	*@brief 返回段集合
	*/
	std::vector<HL7Segment*> GetSegmentVector();

	/*
	*@brief 清除所有段
	*/
	void ClearSegment();

private:
	std::vector<HL7Segment*> m_segmentVect;
};

#endif
