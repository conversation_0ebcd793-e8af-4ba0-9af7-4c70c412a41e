#ifndef CMANUALREVIEWWIDGET_H
#define CMANUALREVIEWWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: chenhao
  * Date: 2024-09-24
  * Description: 人工审核窗口
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QComboBox>
#include <QHash>
#include "CLineEdit.h"
#include <QPushButton>
#include <QMessageBox>
#include <QEventLoop>
#include "CHLabelTitleWidget.h"
#include "PublicParams.h"
#include "CLineEditSpinBox.h"
#include "CLineTwoEdit.h"
#include "include/ccalctlib.h"
#include "include/cpniresultlib.h"

struct stReviewParam
{
    stReviewParam()
    {
        clear();
    };
    // 拷贝构造函数
    stReviewParam(const stReviewParam& other)
    {
        m_strName = other.m_strName;
        m_strCtInfo = other.m_strCtInfo;
        m_strCtInfo_Review = other.m_strCtInfo_Review;
        m_result = other.m_result;
        m_index = other.m_index;
        m_bReview = other.m_bReview;
        m_bNull = other.m_bNull;
        m_bControl = other.m_bControl;
    }

    // 赋值运算符重载
    stReviewParam& operator=(const stReviewParam& other)
    {
        if (this != &other) // 自我赋值检查
        {
            m_strName = other.m_strName;
            m_strCtInfo = other.m_strCtInfo;
            m_strCtInfo_Review = other.m_strCtInfo_Review;
            m_result = other.m_result;
            m_index = other.m_index;
            m_bReview = other.m_bReview;
            m_bNull = other.m_bNull;
            m_bControl = other.m_bControl;
        }
        return *this; // 返回自身引用
    }

    void clear()
    {
        m_strName.clear();
        m_strCtInfo.clear();
        m_strCtInfo_Review.clear();
        m_result.clear();
        m_index = -1;
        m_bControl = false;
        m_bReview = false;
        m_bNull = false;
    };
    QString m_strName;
    QString m_strCtInfo;
    QString m_strCtInfo_Review;
    QString m_result;
    int m_index;
    bool m_bControl;
    bool m_bReview;
    bool m_bNull;
};

class CManualReviewWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CManualReviewWidget(QWidget *parent = nullptr);
    void SetManualReviewParam(const SResultInfoStruct& sResultInfo,const SLotInfoStruct& sLotInfo,const QString& strTextName);
    void GetManualReviewParam(SResultInfoStruct& sResultInfo, SLotInfoStruct& sLotInfo);

    QMessageBox::StandardButton GetClickedBtn() const;

signals:
    void SignalReviewConfirm(stReviewParam stReviewParam);

public slots:
    void _SlotOnHoleNameChanged(int index);
    void _SlotAutoThreshouldBtn();
    void _SlotAutoBaseLineBtn();
    void _SlotManualCalcBtn();
    void _SlotCancelBtn();
    void _SlotConfirmBtn();


protected:
    void paintEvent(QPaintEvent* pEvent) override;
    void showEvent(QShowEvent *pEvent) override;

private:
    void _InitWidget();
    void _InitLayout();
    void _InitParamValue(const SResultInfoStruct& sRestltInfo,const SLotInfoStruct& sLotInfo);
    void _SetParamResultValue();
    void _ClearData();
    void _SetParamValue(int index);
    void _SetComboBoxValue();
    QString _GetShowResult(const QString& strResult);
    QString _GetAmpResult(const QString &strCt, const QString& strCtCutoffValue, const QString& strProjectName, const QString& strHoleName,const QString& strQCTestModel,const QString& strQCCtCutoffValue,bool b3PNMethod = true);
private:


    CCalCTLib m_calcLib;
    SResultInfoStruct m_sResultInfo;
    SLotInfoStruct m_sLotInfo;
    int m_index{-1};
    QString m_strResult;
    QString m_strCtInfo;
    float m_fCtValue;
    QList<stReviewParam> m_ReviewParamList;

    QEventLoop m_qEventLoop;
    QMessageBox::StandardButton m_eStandardBtn;

    QLabel *m_pBackgroundLabel;
    CHLabelTitleWidget *m_pCHLabelTitleWidget;
    QLabel *m_pHoleNameLabel;
    QComboBox *m_pHoleNameCombo;

    QLabel *m_pThreshouldLabel,*m_pBaseLineLabel;
    CLineEditSpinBox * m_pThreshouldEdit;
    CLineTwoEdit* m_pBaseLineEdit;
    QLabel *m_pCtLabel,*m_pResultLabel;
    CLineEdit* m_pCtValueEdit,*m_pResultEdit;// 手动计算的有结果，没人工审核的不填上去；

    // 后续增加微调 spin 按钮
    QPushButton *m_pAutoThreshouldBtn, *m_pAutoBaseLineBtn;
    QPushButton *m_pManualCalcBtn;
    const QString m_strPositive, m_strNegative, m_strError, m_strNull;
    QPushButton *m_pCancelBtn, *m_pConfirmBtn;
    CPNICodeResult m_CPNICodeResult;
};

#endif // CHISTORYPAGE_H
