#ifndef GLOBALFUNCTION_H
#define GLOBALFUNCTION_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-10-27
  * Description: 全局函数
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QDir>
#include <QLabel>
#include <QDebug>
#include <QTableWidget>
#include "PublicParams.h"

/*******************************************************************
* @brief: 设置背景颜色
* @param: QWidget QColor
* @return: void
* @author: hxr
*******************************************************************/
void SetWidgetBackColor(QWidget *pWidget, QColor qColor);

/*******************************************************************
* @brief: 设置label贴图带缩放
* @param:
* @return:
* @author: hxr
*******************************************************************/
void SetLabelBackImage(QLabel *pLabel, QString strImagePath);

/*******************************************************************
* @brief: 读取配置文件保存的语言类型,默认中文
* @param: void
* @return: int
* @author: hxr
*******************************************************************/
int GetLanguange();

/*******************************************************************
* @brief: qDebug重定向
* @param:
* @return: void
* @author: hxr
*******************************************************************/
void OutputMessage(QtMsgType type, const QMessageLogContext &context, const QString &msg);

/*******************************************************************
* @brief: 不阻塞式延迟
* @param: 毫秒
* @return:  return
* @author: hxr
*******************************************************************/
void Delay_MSec(uint iMSecTime);

/*******************************************************************
* @brief: 执行system函数,只在linux下运行,windows下不存在的命令强行运行会卡住一段时间
* @param: strCmd
* @return: int
* @author: hxr
*******************************************************************/
int System(const QString& strCmd);

/*******************************************************************
* @brief: 设置样式表
* @param: pWidget: 控件; strPath: 样式表文件地址
* @return: void
* @author: hxr
*******************************************************************/
void LoadQSS(QWidget *pWidget, const QString &strPath);

/*******************************************************************
* @brief: 创建目录
* @param: strDir: 目录地址
* @return: bool
* @author: hxr
*******************************************************************/
bool CreateDir(const QString &strDir);

/*******************************************************************
* @brief: 拷贝文件
* @param: strSrcPath: 源路径; strDestPath: 目标路径
* @return: bool
* @author: hxr
*******************************************************************/
bool CopyQFile(const QString &strSrcPath, const QString &strDestPath);

/*******************************************************************
* @brief: 拷贝文件
* @param: strSrcPath: 源路径; dir: 目标文件夹
* @return: bool
* @author: hxr
*******************************************************************/
bool CopyQFileDir(const QString &strSrcPath, const QDir &dir);

/*******************************************************************
* @brief: 移动文件
* @param: strSrcPath: 源路径; strDestPath: 目标路径
* @return: bool
* @author: hxr
*******************************************************************/
bool MoveQFile(const QString &strSrcPath, const QString &strDestPath);

/*******************************************************************
* @brief: 移动文件
* @param: strSrcPath: 源路径; dir: 目标文件夹
* @return: bool
* @author: hxr
*******************************************************************/
bool MoveQFile(const QString &strSrcPath, const QDir &dir);

/*******************************************************************
* @brief: 读文件
* @param: strFileName: 文件地址; strFileContext: 读出的文件内容,引用;
* @param: flag: 文件打开方式,默认只读方式打开
* @return: bool
* @author: hxr
*******************************************************************/
bool ReadFile(const QString &strFileName, QString& strFileContext,
              QIODevice::OpenModeFlag flag = QIODevice::ReadOnly);

/*******************************************************************
* @brief: 写文件
* @param: strFileName: 文件地址; strFileContext: 要写入的内容;
* @param: flag: 文件打开方式,默认只写方式打开
* @return: bool
* @author: hxr
*******************************************************************/
bool WriteFile(const QString &strFileName, const QString &strFileContext,
               QIODevice::OpenModeFlag flag = QIODevice::WriteOnly);

/*******************************************************************
* @brief: 写文件
* @param: strFileName: 文件地址; byteFileContext: 要写入的内容;
* @param: flag: 文件打开方式,默认只写方式打开
* @return: bool
* @author: hxr
*******************************************************************/
bool WriteFile(const QString &strFileName, const QByteArray &byteFileContext,
               QIODevice::OpenModeFlag flag = QIODevice::WriteOnly);

/*******************************************************************
* @brief: 写文件
* @param: strFileName: 文件地址; byteFileContext: 要写入的内容List;
* @param: flag: 文件打开方式,默认只写方式打开
* @return: bool
* @author: hxr
*******************************************************************/
bool WriteFile(const QString &strFileName, const QList<QByteArray> &byteDataList,
               QIODevice::OpenModeFlag flag = QIODevice::WriteOnly);

/*******************************************************************
* @brief: 读json文件
* @param: strFileName: 文件地址; qVarMap: 读出的内容,QJsonObject to map,引用;
* @return: bool
* @author: hxr
*******************************************************************/
bool ReadJsonFile(const QString &strFilePath, QVariantMap &qVarMap);

/*******************************************************************
* @brief: 写json文件
* @param: strFileName: 文件地址; qVarMap: 写入的内容,QJsonObject to map;
* @return: bool
* @author: hxr
*******************************************************************/
bool WriteJsonFile(const QString &strFilePath, const QVariantMap &qVarMap);

/*******************************************************************
* @brief: 判断1个QStringList是否有内容为空
* @param: QStringList
* @return: bool
* @author: hxr
*******************************************************************/
bool GIsStringListHasEmptry(const QStringList &strList);

/*******************************************************************
* @brief: QTableWidget排序
* @param: QTableWidget
* @return: void
* @author: hxr
*******************************************************************/
void ResortTableWidget(QTableWidget *pTableWidget);


QString GetUDiskDir();
QString GetUDiskUpdateDir();

/*******************************************************************
* @brief: 判断U盘是否存在
* @param:
* @return: bool
* @author: hxr
*******************************************************************/
bool UDiskExist(QWidget *pWidget);

/*******************************************************************
* @brief: 判断U盘和U盘中的文件是否存在
* @param: strFilePath
* @return: bool
* @author: hxr
*******************************************************************/
bool IsUDiskAndUpdateFileExist(const QString &strFilePath, QWidget *pWidget);

/*******************************************************************
* @brief: 判断U盘是否存在,存在即创建文件夹
* @param: strDir
* @return: bool
* @author: hxr
*******************************************************************/
bool UDiskExistAndCreateDir(const QString &strDir, QWidget *pWidget);

/*******************************************************************
* @brief: 判断密码是否合规,长度>=6,包含数字 大小写
* @param: strPassword
* @return: int
* @author: hxr
*******************************************************************/
int CheckPasswordIsLegal(const QString &strPassword);

/*******************************************************************
* @brief: 判断靶标是否为内控
* @param: strTarget
* @return: bool
* @author: hxr
*******************************************************************/
bool IsCtrlTarget(const QString &strTarget);

QStringList DoubleVector2StringList(const QVector<double> &dDataVec);
QStringList DoubleList2StringList(const QList<double> &dDataList);

QString DeleteSpecialCharacters(QString strOldString);

void ExportEndUmountUSB();

QString GetAppVersion();

QString RunCmdPipe(QString strCmd);
QString RunQProcess(QString strCmd);
bool GetHasNetwork(QString strDevName);
QStringList GetIPInfoList(QString strDevName);
QString GetGateway(QString strDevName);
bool IsValidIPv4(const QString &strIP);

/**
 * @brief getCRC16  获取CRC16
 * @param data  计算的数据
 * @param len  数据的长度
 * @param oldCRC16  上一个CRC16的值，用于循环计算大文件的CRC16。第一个数据的CRC16则传入0x0。
 * @return
 */
unsigned short GetCRC16(const char *pData, unsigned long ulLen,unsigned long ulOldCRC16 = 0);
quint16 GetSmallByte(quint16 qDate);

/*******************************************************************
* @brief: 获取结果：阴性、阳性、通过、不通过、无效
* @param: strTarget
* @return: bool
* @author: chenhao
*******************************************************************/
QString GetResultFormFlag(const QString &strFlag);



/*******************************************************************
* @brief: 获取TecIndex索引 // 这个可以不用，只有两个地方用到
* @param: strTecName 时序名称
* @return: int TecIndex索引
* @author: chenhao
*******************************************************************/
int GetTecIndex(const QString& strTecName);

/*******************************************************************
* @brief: 是否pcr时序类型
* @param: int indexTec 测试模式索引
* @return: bool
* @author: chenhao
*******************************************************************/
bool  bPCRTecType(int indexTec);

/*******************************************************************
* @brief: 是否Hrm时序类型
* @param: int indexTec 测试模式索引
* @return: bool
* @author: chenhao
*******************************************************************/
bool  bHrmTecType(int indexTec);

/*******************************************************************
* @brief: 是否HrmTD时序类型
* @param: int indexTec 测试模式索引
* @return: bool
* @author: chenhao
*******************************************************************/
bool  bHrmTDTecType(int indexTec);

/*******************************************************************
* @brief: 是否显示归一化曲线、阴性不显示，阳性显示
* @param: strResult 测试结果，strTestMode测试模式 bControl 是否质控
* @return: bool
* @author: chenhao
*******************************************************************/
bool bShowNmzaCure(const QString &strResult,const QString& strTestMode,bool bControl);

/*******************************************************************
* @brief: 是否质控测试
* @param: strTarget
* @return: bool
* @author: chenhao
*******************************************************************/
bool IsQCTest(const QString &strModel);

/*******************************************************************
* @brief: 是否阳性质控测试
* @param: strQCTestModel
* @return: bool
* @author: chenhao
*******************************************************************/
bool IsPositiveQcTest(const QString& strQCTestModel);

/*******************************************************************
* @brief: 是否阴性质控测试
* @param: strQCTestModel
* @return: bool
* @author: chenhao
*******************************************************************/
bool IsNegativeQcTest(const QString& strQCTestModel);

/*******************************************************************
* @brief: 获取测试结果
* @param: strModel 测试模式，strResult 测试结果， strHoleName孔道名称
* @return: QString 测试结论
* @author: chenhao
*******************************************************************/
QString GetTestResultText(const QString& strModel,const QString& strResult,const QString& strHoleName, bool& bResult);


std::vector<double> getYVectorFromQPontF(const QList<QPointF> &qSrc);
std::vector<double> getXVectorFromQPontF(const QList<QPointF> &qSrc);
void getXYVectorFromQPontF(const QList<QPointF> &qSrc, std::vector<double> &dXVector, std::vector<double> &dYVector);
QList<qreal> getYListFromQPontF(const QList<QPointF> &qSrc);
QList<qreal> getXListFromQPontF(const QList<QPointF> &qSrc);
void getXYListFromQPontF(const QList<QPointF> &qSrc, QList<qreal> & qXList, QList<qreal> & qYList);

void GetListMinMaxValue(const QList<qreal> &qDataList, double &dMin, double &dMax);

void _GetTmFormMeltInfo(const QString& strMeltInfo,QStringList& strTmList,QStringList& strRmList,QStringList &strYmList,QString &strThreashould);

void _GetCtInfoList(const QString& strCtInfo,QStringList& strCtList);

void _GetTmRmList(const QString& strMeltInfo,QStringList& strTm_RmList);

void _GetCtResultList(bool bReview,const QStringList &strTargetList,const QString &strCtInfo, const QString &strCtReviewInfo, const QString &strResultInfo, const QString &strResultReviewInfo,QStringList& strCtList,QStringList& strResultList);

QString GetMeltingResult(const QString& strProjectName,const QStringList& strHoleNameList,const QStringList& strCtInfoList, const QStringList& strCtResultList, const QStringList& strMeltInfoList,const QStringList& strTmWildValue,const QStringList& strTmRangeValue);

bool bMeltingResultShowCtInfo(const QString& strProjectName,const QStringList& strHoleNameList,int nIndex);

//获取靶标输出信息: 1.历史导出 2.pdf记录 3.打印
QStringList GetTargetOutputInfoList(SResultInfoStruct sResultInfo, SLotInfoStruct sLotInfo);

QString GetLoginLogString(ELOGINTYPE eLogType, QString strUserName, QString strMachineCode = "");

#endif // GLOBALFUNCTION_H
