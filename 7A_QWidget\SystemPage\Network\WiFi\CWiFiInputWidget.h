﻿#ifndef CWIFIINPUTWIDGET_H
#define CWIFIINPUTWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-01-16
  * Description: WiFi input user and password window
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QGroupBox>
#include <QPushButton>
#include "CLabelLineEdit.h"
#include "CHLabelTitleWidget.h"

class CWiFiInputWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CWiFiInputWidget(QWidget *parent = nullptr);

    void SetWiFiName(QString strWiFiName);

protected:
    void paintEvent(QPaintEvent* pEvent) override;

signals:
    void SignalConnect(QString strName, QString strPwd);

private slots:
    void _SlotCancelBtn();
    void _SlotConfirmBtn();

private:
    QGroupBox *_CreateGroupBox();

private:
    CHLabelTitleWidget *m_pTitleWidget;
    CLabelLineEdit *m_pNameLineEdit;
    CLabelLineEdit *m_pPwdLineEdit;
    QPushButton *m_pCancelBtn, *m_pConfirmBtn;
};

#endif // CWIFIINPUTWIDGET_H
