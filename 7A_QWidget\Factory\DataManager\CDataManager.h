#ifndef CDATAMANAGER_H
#define CDATAMANAGER_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-01-22
  * Description: 数据管理
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QStackedWidget>
#include "CHBtnTitleWidget.h"

class CPressureInfo;
class CFaultCodeManager;
class CFaultLog;
class CProjectManager;
class CDataExport;
class CRunLog;

class CDataManager : public QWidget
{
    Q_OBJECT
public:
    explicit CDataManager(QWidget *parent = nullptr);
    ~CDataManager();

    void GotoRunLog();

private slots:
    void _SlotTitleChanged(int index);

private:
    void _InitWidget();

private:
    CHBtnTitleWidget *m_pCHBtnTitle;
    QStackedWidget *m_pStackedWidget;

    CPressureInfo *m_pCPressureInfo;
    CFaultCodeManager *m_pCFaultCodeManager;
    CFaultLog *m_pCFaultLog;
    CRunLog *m_pCRunLog;
    CProjectManager *m_pCProjectManager;
    CDataExport *m_pCDataExport;
};

#endif // CDATAMANAGER_H
