﻿#include "CEth1Widget.h"
#include <QDebug>
#include <QProcess>
#include <QButtonGroup>

#include "CMessageBox.h"
#include "CConfigJson.h"
#include "PublicParams.h"
#include "PublicConfig.h"
#include "PublicFunction.h"

CEth1Widget::CEth1Widget(QWidget *parent) : QWidget(parent) , m_bWiFi<PERSON>pen(false) , m_strTipsText((tr("提示")))
{
    m_pGetAutoIPTimer = new QTimer;
    connect(m_pGetAutoIPTimer, &QTimer::timeout, this, &CEth1Widget::_SlotGetAutoIPTimer);

    _InitWidget();
    _InitLayout();

    //修改字段名称
    QJsonObject qRootObj = CConfigJson::GetInstance()->GetConfigJsonObject();
    if(qRootObj.contains("NetWork"))
    {
        QJsonObject qOldObj = qRootObj.value("NetWork").toObject();
        qRootObj.remove("NetWork");
        qRootObj.remove("WIFI");

        QJsonObject qNetworkObj;
        qNetworkObj.insert("wifi_or_eth", qOldObj.value("wifi_or_network"));
        qNetworkObj.insert("wifi_name", qOldObj.value("Name"));
        qNetworkObj.insert("wifi_pwd", qOldObj.value("Password"));
        qNetworkObj.insert("eth_auto", qOldObj.value("console_ip_auto"));
        qNetworkObj.insert("ip", qOldObj.value("console_ip"));
        qNetworkObj.insert("netmask", qOldObj.value("console_mask"));
        qNetworkObj.insert("gateway", qOldObj.value("console_geteway"));
        qRootObj.insert("Network", qNetworkObj);
        CConfigJson::GetInstance()->SetConfigJsonObject(qRootObj);
    }

    _ReadCfg();
}

void CEth1Widget::showEvent(QShowEvent *pEvent)
{
    if(false == m_bWiFiOpen)
        _ResetUI();

    QWidget::showEvent(pEvent);
}

void CEth1Widget::_ResetUI()
{
    // no operation only update ui
    QJsonObject qNetworkObj = CConfigJson::GetInstance()->GetConfigJsonObject("Network");
    bool bEthAuto = qNetworkObj.value("eth_auto").toBool();
    m_pIPWidget->setEnabled(!bEthAuto);
    m_pNetmaskWidget->setEnabled(!bEthAuto);
    m_pGatewayWidget->setEnabled(!bEthAuto);
    m_pAutoRadioBtn->setCheckable(true);
    m_pManualRadioBtn->setCheckable(true);

    if(bEthAuto)
    {
        m_pAutoRadioBtn->setChecked(true);
        return;
    }

    m_pManualRadioBtn->setChecked(true);
    QString strIP = qNetworkObj.value("ip").toString();
    m_pIPWidget->SetValueList(strIP.split("."));
    QString strNetmask = qNetworkObj.value("netmask").toString();
    m_pNetmaskWidget->SetValueList(strNetmask.split("."));
    QString strGateway = qNetworkObj.value("gateway").toString();
    m_pGatewayWidget->SetValueList(strGateway.split("."));
}

void CEth1Widget::_ReadCfg()
{
    QJsonObject qNetworkObj = CConfigJson::GetInstance()->GetConfigJsonObject("Network");
    if(eNetwork_Eth != qNetworkObj.value("wifi_or_eth").toInt())
    {
        System("ifconfig eth0 down");
        System("ifconfig eth1 down");
        return;
    }

    if(true == qNetworkObj.value("eth_auto").toBool())
    {
        m_pAutoRadioBtn->setChecked(true);
        _SlotAutoBtn();
        return;
    }

    m_pManualRadioBtn->setChecked(true);
    _SlotManualBtn();

    QString strIP = qNetworkObj.value("ip").toString();
    m_pIPWidget->SetValueList(strIP.split("."));
    QString strNetmask = qNetworkObj.value("netmask").toString();
    m_pNetmaskWidget->SetValueList(strNetmask.split("."));
    QString strGateway = qNetworkObj.value("gateway").toString();
    m_pGatewayWidget->SetValueList(strGateway.split("."));
    if(!IsValidIPv4(strIP) || !IsValidIPv4(strNetmask) || !IsValidIPv4(strGateway))
        return;

    QString strCmd1 = QString("ifconfig eth1 %1 netmask %2 &").arg(strIP).arg(strNetmask);
    System(strCmd1);
    QString strCmd2 = QString("route add default gw %1 eth1").arg(strGateway);
    System(strCmd2);
    qDebug()<<"程序启动读取配置,手动设置网络,保存:"<<strCmd1<<strCmd2;
}

void CEth1Widget::SlotWiFiOpen(bool bWiFiOpen)
{
    m_bWiFiOpen = bWiFiOpen;
    m_pGetAutoIPTimer->stop();
    m_pIPWidget->ClearEdit();
    m_pNetmaskWidget->ClearEdit();
    m_pGatewayWidget->ClearEdit();

    if(!bWiFiOpen)
    {
        _ResetUI();
        return;
    }

    m_pIPWidget->setEnabled(false);
    m_pNetmaskWidget->setEnabled(false);
    m_pGatewayWidget->setEnabled(false);

    m_pAutoRadioBtn->setCheckable(false);
    m_pAutoRadioBtn->setChecked(false);
    m_pManualRadioBtn->setCheckable(false);
    m_pManualRadioBtn->setChecked(false);
    update();
}

void CEth1Widget::SlotWlanIPInfoList(QStringList strWlanList)
{
    if(strWlanList.size() < 3 || !m_bWiFiOpen)
        return;

    m_pIPWidget->SetValueList(strWlanList.at(0).split("."));
    m_pNetmaskWidget->SetValueList(strWlanList.at(1).split("."));
    m_pGatewayWidget->SetValueList(strWlanList.at(2).split("."));
}

void CEth1Widget::_SlotAutoBtn()
{
    if(m_bWiFiOpen)
    {
        ShowInformation(this->parentWidget(), m_strTipsText, tr("WiFi已打开，如需操作，请先关闭WiFi"));
        return;
    }

    QJsonObject qNetworkObj;
    qNetworkObj.insert("wifi_or_eth", eNetwork_Eth);
    qNetworkObj.insert("eth_auto", true);
    CConfigJson::GetInstance()->IncrementInsertJsonObject("Network", qNetworkObj);
    RUN_LOG("网络设置：自动IP");

    m_pIPWidget->ClearEdit();
    m_pNetmaskWidget->ClearEdit();
    m_pGatewayWidget->ClearEdit();

    m_pIPWidget->setEnabled(false);
    m_pNetmaskWidget->setEnabled(false);
    m_pGatewayWidget->setEnabled(false);

    System("ifconfig wlan0 down");
    System("ifconfig eth0 down");
    System("ifconfig eth1 up");
    System("killall udhcpc");
    qDebug()<<"eth1自动获取IP";
    System("udhcpc -i eth1 &");

    m_pGetAutoIPTimer->start(1000);
}

void CEth1Widget::_SlotGetAutoIPTimer()
{
    QString strData = RunQProcess("ethtool eth1");
    if(!strData.contains("Link detected: yes", Qt::CaseInsensitive))
        return;

    qDebug()<<"eth1 is ready";
    QStringList strInfoList = GetIPInfoList("eth1");
    if(strInfoList.isEmpty())
        return;

    if(strInfoList.size() >= 3)
    {
        m_pIPWidget->SetValueList(strInfoList.at(0).split("."));
        m_pNetmaskWidget->SetValueList(strInfoList.at(1).split("."));
    }

    m_pGetAutoIPTimer->stop();
    QString strGateway = GetGateway("eth1");
    //获取网关有个异步轮询过程,轮询结束有可能状态已变
    if(!m_pAutoRadioBtn->isChecked() || m_bWiFiOpen)
        return;

    m_pGatewayWidget->SetValueList(strGateway.split("."));
}

void CEth1Widget::_SlotManualBtn()
{
    if(m_bWiFiOpen)
    {
        ShowInformation(this->parentWidget(), m_strTipsText, tr("WiFi已打开，如需操作，请先关闭WiFi"));
        return;
    }

    m_pGetAutoIPTimer->stop();

    QJsonObject qNetworkObj;
    qNetworkObj.insert("wifi_or_eth", eNetwork_Eth);
    qNetworkObj.insert("eth_auto", false);
    CConfigJson::GetInstance()->IncrementInsertJsonObject("Network", qNetworkObj);
    RUN_LOG("网络设置：手动IP");

    m_pIPWidget->setEnabled(true);
    m_pNetmaskWidget->setEnabled(true);
    m_pGatewayWidget->setEnabled(true);

    System("ifconfig wlan0 down");
    System("ifconfig eth0 down");
    System("ifconfig eth1 up"); //当eth1 become ready,会自动获取到ip
    System("killall udhcpc");
}

void CEth1Widget::_SlotSaveBtn()
{
    if(m_bWiFiOpen)
    {
        ShowInformation(this->parentWidget(), m_strTipsText, tr("WiFi已打开，如需操作，请先关闭WiFi"));
        return;
    }

    if(!m_pAutoRadioBtn->isChecked() && !m_pManualRadioBtn->isChecked())
    {
        ShowInformation(this->parentWidget(), m_strTipsText, tr("请先选择一种连接方式"));
        return;
    }

    QString strIP = m_pIPWidget->GetValueList().join(".");
    QString strNetmask = m_pNetmaskWidget->GetValueList().join(".");
    QString strGateway = m_pGatewayWidget->GetValueList().join(".");

    if(m_pManualRadioBtn->isChecked())
    {
        if(!IsValidIPv4(strIP) || !IsValidIPv4(strNetmask) || !IsValidIPv4(strGateway))
        {
            ShowInformation(this->parentWidget(), m_strTipsText, tr("手动设置有线网络，请输入正确的内容"));
            return;
        }

        QString strCmd1 = QString("ifconfig eth1 %1 netmask %2").arg(strIP).arg(strNetmask);
        System(strCmd1);

        QString strCmd2 = QString("route add default gw %1 eth1").arg(strGateway);
        System(strCmd2);
        qDebug()<<"手动设置网络:"<<strCmd1<<strCmd2;
    }

    QJsonObject qNetworkObj;
    qNetworkObj.insert("wifi_or_eth", eNetwork_Eth);
    qNetworkObj.insert("eth_auto", m_pAutoRadioBtn->isChecked());
    if(IsValidIPv4(strIP) && IsValidIPv4(strNetmask) && IsValidIPv4(strGateway))
    {
        qNetworkObj.insert("ip", strIP);
        qNetworkObj.insert("netmask", strNetmask);
        qNetworkObj.insert("gateway", strGateway);
    }
    CConfigJson::GetInstance()->IncrementInsertJsonObject("Network", qNetworkObj);
    RUN_LOG("网络设置: 保存有线设置");
    ShowSuccess(this->parentWidget(), m_strTipsText, tr("有线设置保存成功"));
}

void CEth1Widget::_InitWidget()
{
    m_pTitleWidget = new CHLabelTitleWidget(tr("有线设置"));

    int iLabelWidth = 96, iEditWidth = 92 , iHeight = 56;
    if(eLanguage_English == gk_iLanguage)
        iLabelWidth = 230;
    else if(eLanguage_Spanish == gk_iLanguage)
        iLabelWidth = 250;
    else if(eLanguage_German == gk_iLanguage)
        iLabelWidth = 255;
    else if(eLanguage_Italian == gk_iLanguage)
        iLabelWidth = 265;

    m_pIPWidget = new CIPLabelLineEdit(tr("IP地址"));
    m_pIPWidget->ResetLabelSize(iLabelWidth, iHeight);
    m_pIPWidget->ResetLineEditSize(iEditWidth, iHeight);

    m_pNetmaskWidget = new CIPLabelLineEdit(tr("子网掩码"));
    m_pNetmaskWidget->ResetLabelSize(iLabelWidth, iHeight);
    m_pNetmaskWidget->ResetLineEditSize(iEditWidth, iHeight);

    m_pGatewayWidget = new CIPLabelLineEdit(tr("默认网关"));
    m_pGatewayWidget->ResetLabelSize(iLabelWidth, iHeight);
    m_pGatewayWidget->ResetLineEditSize(iEditWidth, iHeight);

    m_pConnectLabel = new QLabel(tr("连接方式"));
    m_pConnectLabel->setFixedSize(iLabelWidth, iHeight);

    m_pAutoRadioBtn = new QRadioButton(tr("自动"));
    connect(m_pAutoRadioBtn, &QRadioButton::clicked, this, &CEth1Widget::_SlotAutoBtn);

    m_pManualRadioBtn = new QRadioButton(tr("手动"));
    connect(m_pManualRadioBtn, &QRadioButton::clicked, this, &CEth1Widget::_SlotManualBtn);

    //    QButtonGroup *pRadioGroup = new QButtonGroup;
    //    pRadioGroup->addButton(m_pAutoRadioBtn);
    //    pRadioGroup->addButton(m_pManualRadioBtn);
    //    pRadioGroup->setExclusive(false);

    if(eLanguage_English == gk_iLanguage)
    {
        m_pAutoRadioBtn->setFixedSize(160, 35);
        m_pManualRadioBtn->setFixedSize(160, 35);
    }

    m_pSaveBtn = new QPushButton(tr("保存"));
    m_pSaveBtn->setFixedSize(144, 56);
    connect(m_pSaveBtn, &QPushButton::clicked, this, &CEth1Widget::_SlotSaveBtn);
}

void CEth1Widget::_InitLayout()
{
    QVBoxLayout *pIPLayout = new QVBoxLayout;
    pIPLayout->setContentsMargins(18, 0, 0, 0);
    pIPLayout->setSpacing(20);
    pIPLayout->addWidget(m_pIPWidget);
    pIPLayout->addWidget(m_pNetmaskWidget);
    pIPLayout->addWidget(m_pGatewayWidget);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(0);
    pBtnLayout->addSpacing(18);
    pBtnLayout->addWidget(m_pConnectLabel);
    pBtnLayout->addSpacing(10);
    pBtnLayout->addWidget(m_pAutoRadioBtn);
    pBtnLayout->addSpacing(80);
    pBtnLayout->addWidget(m_pManualRadioBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pTitleWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(20);
    pLayout->addLayout(pIPLayout);
    pLayout->addSpacing(20);
    pLayout->addLayout(pBtnLayout);
    pLayout->addSpacing(25);
    pLayout->addWidget(m_pSaveBtn, 0, Qt::AlignHCenter);
    this->setLayout(pLayout);
}
