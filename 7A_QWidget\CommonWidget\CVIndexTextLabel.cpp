#include "CVIndexTextLabel.h"
#include <QVBoxLayout>
#include <QVariant>
#include <QStyle>
#include "PublicParams.h"

CVIndexTextLabel::CVIndexTextLabel(const QString &strIndexText, const QString &strDataText, QWidget *parent)
    : QWidget(parent)
{
    m_pIndexLabel = new QLabel(strIndexText);
    m_pIndexLabel->setFixedSize(40, 40);
    m_pIndexLabel->setObjectName("VIndexLabel");
    m_pIndexLabel->setAlignment(Qt::AlignCenter);

    m_pTextLabel = new QLabel(strDataText);
    m_pTextLabel->setObjectName("VTextLabel");

    if(eLanguage_English == gk_iLanguage
            || eLanguage_Spanish == gk_iLanguage
            || eLanguage_German == gk_iLanguage)
    {
        m_pTextLabel->setWordWrap(true);
        m_pTextLabel->setAlignment(Qt::AlignTop | Qt::AlignHCenter);
        m_pTextLabel->setMargin(1);
    }

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addSpacing(1);
    pLayout->addWidget(m_pIndexLabel, 0, Qt::AlignHCenter);
    pLayout->addSpacing(8);
    pLayout->addWidget(m_pTextLabel);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}

void CVIndexTextLabel::SetOjbName(const QString &strIndexObjName, const QString &strTextObjName)
{
    m_pIndexLabel->setObjectName(strIndexObjName);
    m_pTextLabel->setObjectName(strTextObjName);
}

void CVIndexTextLabel::SetPropertyText(const QString &strPropertyName, const QString &strPropertyData)
{
    m_pIndexLabel->setProperty(strPropertyName.toLocal8Bit().constData(), strPropertyData);
    m_pTextLabel->setProperty(strPropertyName.toLocal8Bit().constData(), strPropertyData);

    m_pIndexLabel->style()->polish(m_pIndexLabel);
    m_pTextLabel->style()->polish(m_pTextLabel);
}
