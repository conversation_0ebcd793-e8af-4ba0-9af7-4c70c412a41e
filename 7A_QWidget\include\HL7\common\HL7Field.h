﻿#ifndef _HL7_FIELD_H_
#define _HL7_FIELD_H_


#include "HL7ComponentCollection.h"
#include "HL7RepetitionCollection.h"
#include "../interface/IObjectBase.h"
class HL7Field
{
public:
	HL7Field();
	~HL7Field();
	/*
	*@brief 返回组件集合
	*/
	HL7ComponentCollection GetHL7ComponentCollection();


	/*
	*@brief 返回消息字符串
	*/
	std::string GetFieldString();

	void GetFieldString(char** fieldString);
	/*
	*@brief 构建消息，输入参数为按顺序排列的成分集合和重复字段集合
	*/
	void Build(HL7ComponentCollection componentCollection,
		HL7RepetitionCollection repetitionCollection);

	/*
	*@brief 分解消息为成分、重复字段
	*/
	void Parse(const char* fieldStr, EncodingCharacters encodingChars);

	/*
	*@brief 返回重复字段集合
	*/
	HL7RepetitionCollection GetHL7RepetitionCollection();


	void SetFieldString(std::string fieldStr);
private:
	HL7ComponentCollection m_componentCollection;
	HL7RepetitionCollection m_repetitionCollection;
	std::string m_fieldStr;

	std::string m_componentSeparator;
	std::string m_repetitionSeparator;
};
#endif

