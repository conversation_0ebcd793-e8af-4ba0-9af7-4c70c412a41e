#include "CAddEditUserWidget.h"
#include <QPainter>
#include <QBoxLayout>

#include "CUserDB.h"
#include "CMessageBox.h"
#include "PublicParams.h"
#include "PublicConfig.h"
#include "PublicFunction.h"

QString CAddEditUserWidget::m_strDisableText = "-1";

CAddEditUserWidget::CAddEditUserWidget(QWidget *parent)
    : QWidget(parent)
    , m_bAddUser(true)
    , m_strTipsText(tr("提示"))
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setFixedSize(1684, 958);
    this->setAttribute(Qt::WA_TranslucentBackground);
    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroupBox(), 0, Qt::AlignHCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);

    LoadQSS(this, ":/qss/qss/system/user.qss");
}

void CAddEditUserWidget::SetData(bool bAddUser, const QStringList &strList)
{
    m_pNameLineEdit->SetLineEditText("");
    m_pPwdLineEdit->SetLineEditText("");
    m_pRePwdLineEdit->SetLineEditText("");
    m_pStatusComboBox->SetCurrentIndex(-1);

    m_bAddUser = bAddUser;
    if(m_bAddUser)
    {
        m_pStatusComboBox->setVisible(true);
        m_pNameLineEdit->setEnabled(true);
        m_pStatusComboBox->SetCurrentIndex(0);
        m_pCHLabelTitleWidget->ResetTitle(tr("新增"));
        return;
    }

    m_pNameLineEdit->setEnabled(false);
    if(strList.size() >= 3)
    {
        m_pCHLabelTitleWidget->ResetTitle(tr("编辑"));
        m_pNameLineEdit->SetLineEditText(strList.at(0));
        m_pPwdLineEdit->SetLineEditText(strList.at(1));
        m_pRePwdLineEdit->SetLineEditText(strList.at(1));
        if(m_strDisableText == strList.at(2))
            m_pStatusComboBox->SetCurrentIndex(1);
        else
            m_pStatusComboBox->SetCurrentIndex(0);

        QString strUser = CPublicConfig::GetInstance()->GetLoginUser();
        m_pStatusComboBox->setVisible(strUser != strList.at(0));
    }
}

void CAddEditUserWidget::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, gk_iBackTransparency));
    QWidget::paintEvent(pEvent);
}

void CAddEditUserWidget::showEvent(QShowEvent *pEvent)
{
    QWidget::showEvent(pEvent);
}

void CAddEditUserWidget::_SlotCancelBtn()
{
    this->close();
}

void CAddEditUserWidget::_SlotConfirmBtn()
{
    QString strName = m_pNameLineEdit->GetLineEditText();
    if(strName.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("用户名不能为空"));
        return;
    }

    QString strPwd = m_pPwdLineEdit->GetLineEditText();
    QString strRePwd = m_pRePwdLineEdit->GetLineEditText();

    if(strPwd != strRePwd)
    {
        ShowInformation(this, m_strTipsText, tr("两次输入密码不一致"));
        return;
    }

    int iLegal = CheckPasswordIsLegal(strPwd);
    if(0 != iLegal)
    {
        ShowInformation(this, m_strTipsText, tr("密码长度必须大于等于6且包含数字和大小写字母"));
        return;
    }

    if(m_bAddUser)
    {
        if(CUserDB::GetInstance()->IsUserExistInDB(strName))
        {
            ShowInformation(this, m_strTipsText, tr("新增失败，用户名已存在"));
            return;
        }
    }

    QString strStatus = m_strDisableText;
    if(0 == m_pStatusComboBox->GetCurrentIndex())
        strStatus = "0";
    QStringList strList = {strName, strPwd, strStatus};
    emit SignalConfirm(m_bAddUser, strList);

    this->close();
}

QGroupBox *CAddEditUserWidget::_CreateGroupBox()
{
    m_pCHLabelTitleWidget = new CHLabelTitleWidget(tr("新增"));

    int iLabelWidth = 110, iHeight = 56;
    if(eLanguage_English == gk_iLanguage)
        iLabelWidth = 205;
    else if(eLanguage_Spanish == gk_iLanguage)
        iLabelWidth = 170;
    else if(eLanguage_German == gk_iLanguage)
        iLabelWidth = 170;
    else if(eLanguage_Italian == gk_iLanguage)
        iLabelWidth = 160;

    m_pNameLineEdit = new CHLabelLineEdit(tr("用户名"));
    m_pNameLineEdit->ResetLabelSize(iLabelWidth, iHeight);
    m_pNameLineEdit->ResetLineEditSize(390, iHeight);

    m_pPwdLineEdit = new CHLabelLineEdit(tr("密码"));
    m_pPwdLineEdit->ResetLabelSize(iLabelWidth, iHeight);
    m_pPwdLineEdit->ResetLineEditSize(390, iHeight);

    m_pRePwdLineEdit = new CHLabelLineEdit(tr("确认密码"));
    m_pRePwdLineEdit->ResetLabelSize(iLabelWidth, iHeight);
    m_pRePwdLineEdit->ResetLineEditSize(390, iHeight);

    QStringList strStatusList = {tr("激活"), tr("禁用")};
    m_pStatusComboBox = new CHLabelComboBox(tr("状态"), strStatusList);
    m_pStatusComboBox->ResetLabelSize(iLabelWidth, iHeight);
    m_pStatusComboBox->ResetComboBoxSize(390, iHeight);

    m_pCancelBtn = new QPushButton(tr("取消"));
    m_pCancelBtn->setFixedSize(150, 56);
    m_pCancelBtn->setObjectName("CancelBtn");
    connect(m_pCancelBtn, &QPushButton::clicked, this, &CAddEditUserWidget::_SlotCancelBtn);

    m_pConfirmBtn = new QPushButton(tr("确认"));
    m_pConfirmBtn->setFixedSize(150, 56);
    connect(m_pConfirmBtn, &QPushButton::clicked, this, &CAddEditUserWidget::_SlotConfirmBtn);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(40);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pCancelBtn);
    pBtnLayout->addWidget(m_pConfirmBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setContentsMargins(24, 15, 24, 24);
    pLayout->setSpacing(30);
    pLayout->addWidget(m_pCHLabelTitleWidget, 0, Qt::AlignLeft);
    pLayout->addWidget(m_pNameLineEdit, 0, Qt::AlignHCenter);
    pLayout->addWidget(m_pPwdLineEdit, 0, Qt::AlignHCenter);
    pLayout->addWidget(m_pRePwdLineEdit, 0, Qt::AlignHCenter);
    pLayout->addWidget(m_pStatusComboBox, 0, Qt::AlignHCenter);
    pLayout->addStretch(1);
    pLayout->addLayout(pBtnLayout);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(680, 510);
    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}
