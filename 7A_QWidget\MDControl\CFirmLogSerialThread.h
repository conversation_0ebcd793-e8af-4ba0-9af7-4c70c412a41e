#ifndef CFIRMLOGSERIALTHREAD_H
#define CFIRMLOGSERIALTHREAD_H

#include <QDate>
#include <QFile>
#include <QObject>
#include <QThread>
#include <QSerialPort>

class CFirmLogSerialThread : public QObject
{
    Q_OBJECT
public:
    static CFirmLogSerialThread* GetInstance();

    void ResetCom(QString strSerialName);
    bool IsOpenSuccess();

signals:
    void SignalInitThread();
    void SignalResetSerial(QString strSerialName);
    void SignalLog(bool bOpen,const QString &strLog);

private slots:
    void SlotInitThread();
    void SlotReadSerial();
    void SlotResetSerial(QString strSerialName);

private:
    explicit CFirmLogSerialThread(QObject *parent = nullptr);
    ~CFirmLogSerialThread();

    void _InitSerial(QString strSerialName);
    void _WriteFirmLog(QByteArray& logByte);

private:
    static CFirmLogSerialThread* m_spInstance;

    QThread* m_pThread;
    QString m_strSerialName;
    QSerialPort* m_pSerialPort;
    bool m_bOpenSerialPort;

    QFile* m_pLogFile;
    QDate m_currentDate;
    QString m_strLogPath;
};

#endif // CFIRMLOGSERIALTHREAD_H
