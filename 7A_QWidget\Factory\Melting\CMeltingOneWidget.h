#ifndef CMELTINGONEWIDGET_H
#define CMELTINGONEWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-02-21
  * Description: 一台机器的熔解
  * -------------------------------------------------------------------------
  * History:
  *1.优化熔解曲线的算法，设置为三方库形式！
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QCheckBox>

#include "CLabelComboBox.h"
#include "CLabelLineEdit.h"
#include "CSetChartXYRange.h"
#include <QPlainTextEdit>
#include "include/cmeltingcalclib.h"

class QCustomPlot;
class QCPItemLine;

// 定义组合窗口类
class CComboLineEdit : public QWidget {
    Q_OBJECT
public:
    CComboLineEdit(QWidget* parent = nullptr)
        : QWidget(parent), m_pTmFirstEdit(new CLineEdit(this)), m_pTmSecondEdit(new CLineEdit(this)) {
        initWidget();
    }
    ~CComboLineEdit() {

    }

    void  setText(const QString& text1,const QString& text2)
    {
        m_pTmFirstEdit->setText(text1);  // 设置文本
        m_pTmSecondEdit->setText(text2);  // 设置文本
    }

    void getText(QString& text1,QString& text2)
    {
        text1 = m_pTmFirstEdit->text();
        text2 = m_pTmSecondEdit->text();
    }
    QString getTmRangeText()
    {
        return  QString("%1&%2").arg(m_pTmFirstEdit->text()).arg(m_pTmSecondEdit->text());
    }
private:
    void initWidget()
    {
        int iHeight = 35;
        m_pTmSplitLabel = new QLabel("—");
        m_pTmFirstEdit->setAlignment(Qt::AlignCenter);
        m_pTmFirstEdit->setInputMethodHints(Qt::ImhDigitsOnly);
        m_pTmFirstEdit->setFixedSize(55, iHeight);
        m_pTmFirstEdit->setText("40.0");

        m_pTmSecondEdit->setAlignment(Qt::AlignCenter);
        m_pTmSecondEdit->setInputMethodHints(Qt::ImhDigitsOnly);
        m_pTmSecondEdit->setFixedSize(55, iHeight);
        m_pTmSecondEdit->setText("90.0");
        QHBoxLayout *pTmEditLayout = new QHBoxLayout;
        pTmEditLayout->setMargin(0);
        pTmEditLayout->setSpacing(0);
        pTmEditLayout->addWidget(m_pTmFirstEdit);
        pTmEditLayout->addWidget(m_pTmSplitLabel);
        pTmEditLayout->addWidget(m_pTmSecondEdit);
        //this->setFixedSize(180,35);
        this->setLayout(pTmEditLayout);
    }
private:
    QLabel *m_pTmSplitLabel{nullptr};
    CLineEdit* m_pTmFirstEdit{nullptr};
    CLineEdit* m_pTmSecondEdit{nullptr};
};

class CMeltingOneWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CMeltingOneWidget(int iMachineID, QWidget *parent = nullptr);
    ~CMeltingOneWidget();

    void SetCardID(const QString &strCardID);
    void ClearData();
    void InitTestInfo();

    void ParseFLCmd(const QVariant &qVarData);
    void ParsePCRSignalCmd(const QVariant &qVarData);
    void ParseStartCmd(int iResult);

signals:
    void SignalShowCurrentTestCardID(int);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual void hideEvent(QHideEvent *pEvent) override;

private slots:
    void _SlotSetXYRange(const QStringList &strList);
    void _SlotHistoryComboBoxChanged(int index);
    void _SlotHoleComboBoxChanged(int iHole);
    void _SlotTestChecked(bool bChecked);
    void _SlotYmRmChecked(bool bChecked);

    void _SlotShowBtn();
    void _SlotSavePdfBtn();

    void _SlotDataDisplayTypeComboBoxChanged(int);
private:
    bool _UpdateCalcParam(int Hole);
    void ClearLineEidt();
    void _UpdateCurve();
    void _MeltingCalc(QList<double>& dTempVec);
    void _updateResult(int nHole,const QList<QString>& strInfoList);
    void _ShowResult(QList<double>& dTempVec,QList<double>& dFLList,int nColor);
    QList<double> moving_average(const QList<double>& dSrcDataList, qreal window_size);
    void _ShowCurrentHoleFLData(int iHole);
    double _GetDerivatePoint(const QVector<double> &dTempVec, const QList<double> &dFLList);
    void _SetPlotXYRange(const QVector<double> &x, const QVector<double> &y);
    void _CalcTmRmResultValue(int iResult);
    QString _GetResult(double dTm);

    void _SaveMelting2Xlsx();
    void _SetTmRmValue(int iDataType,int nColor, const QStringList& strTmList,const QStringList& strRmList,const QStringList& strYmList);
private:
    void _InitWidget();
    void _InitLayout();
    void _InitCustomPlot();
    void _AddGraph(QCustomPlot* pCustomPlot, QColor penColor, QColor pointColor, int iChart, QString strChartName);

private:
    const int m_iMachineID;
    QString m_strNewCardID; // 卡盒ID+测试时间
    bool m_bHasCalcResult;
    QVector<double> m_dTempVec;
    //按上传的循环数保存,用map不能用vector,因为循环数不固定且某个循环可能重复上传
    QList<QMap<double, double>> m_dRawFLDataList;  //原始数据
    QList<QMap<double, double>> m_dDerFLDataList;  //导数

private:

    QCheckBox *m_pTest{nullptr}; // 是否显示测试数据
    QCheckBox *m_pYmRm{nullptr}; // 是否显示Ym/Rm数据
    CLabelComboBox *m_pHoleComboBox{nullptr};
    QComboBox *m_pColorComboBox{nullptr};

    QPushButton *m_pSavePDFBtn{nullptr};

    QPushButton *m_pShowBtn{nullptr};
    // 数据输出类型；
    QComboBox* m_pDataDisplayType{nullptr};
    //左侧区域
    QList<QLabel *> m_pLabelList;
    // 阈值不清零，设置一个默认阈值！
    QList<CLineEdit*> m_pLineEditList;
    CLabelLineEdit* m_pAmplitudethreshold{nullptr};

    QList<CComboLineEdit*> m_CComboLineEditList;

    //设置完默认阈值需要从新计算，如果有多个峰值，得显示在一个label区域
    QPlainTextEdit  * m_pResultLineEdit{nullptr};
    CLabelLineEdit *m_pSmoothLineEdit{nullptr};
    CMeltingCalcLib  m_MeltingCalc;
    QCustomPlot *m_pCustomPlot{nullptr};
    CSetChartXYRange *m_pCSetXY{nullptr};

    QList<QCPItemLine *> m_pDotLineFirstList;
    QList<QCPItemLine *> m_pDotLineSecondList;

    bool m_bShow;
    bool m_bReplot;
};

#endif // CMELTINGONEWIDGET_H
