#include "CRunLog.h"
#include <QDebug>
#include <QDateTime>
#include <QBoxLayout>

#include "PublicConfig.h"

CRunLog::CRunLog(QWidget *parent) : QWidget(parent)
{
    _InitWidget();
    _InitLayout();

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalRunLog, this, &CRunLog::SlotSaveRunLog);
}

void CRunLog::SlotSaveRunLog(const QString &strLog)
{
    QString strCurrentTime = QDateTime::currentDateTime().toString("[yyyy-MM-dd hh:mm:ss]");
    m_pTextBrowser->append(strCurrentTime + " " + strLog);
}

void CRunLog::_InitWidget()
{
    m_pTextBrowser = new QTextBrowser;
    m_pTextBrowser->document()->setMaximumBlockCount(150);

    m_pClearBtn = new QPushButton(tr("清空"));
    m_pClearBtn->setFixedSize(120, 50);
    connect(m_pClearBtn, &QPushButton::clicked, this, [this](){m_pTextBrowser->clear();});
}

void CRunLog::_InitLayout()
{
    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pTextBrowser);
    pLayout->addSpacing(20);
    pLayout->addWidget(m_pClearBtn, 0, Qt::AlignCenter);
    pLayout->addSpacing(15);
    this->setLayout(pLayout);
}
