﻿/*****************************************************
  * Copyright: 万孚生物
  * Author: 刘青
  * Date: 2020-10-11
  * Description: 荧光数据记录数据库
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/
#ifndef CHISTORYDB_H
#define CHISTORYDB_H

#include <QObject>
#include "CSqliteDBBase.h"

class CHistoryDB : public QObject , public CSqliteDBBase
{
    Q_OBJECT
public:
    static CHistoryDB* GetInstance();

    int GetTestDBColumnCount();
    //
    int GetTestDataCount();
    bool AddTestData(QString strFLID, QString strSNCOde, QString strBeginTime, QString strEndTestTime,
                        int iCycleCount, QList<qreal> dFLDataList,QList<qreal> dFLStandard,QList<qreal> dFLCross, QString strRemarks);

    // 插入温度或者熔解FL数据
    bool AddMeltingData(QString strFLDataID,
                        int iCycleCount, QString strPeak, QString strMelting, QString strRemarks = "");

    bool GetMeltingData(QString strFLDataID, int &iCycleCount, QString &strPeakValue,
                        QString &strMelting);
    bool GetMeltingDoubleData(QString strFLDataID, QList<qreal> &dMeltingList);
    bool GetMeltingDataFromCardIDAddTestTime(const QString& strCardIDAndTime, QList<QList<double>> &dMeltingList);

    QStringList getLastTestFLID(int iCount);
    QStringList getLastMeltingFLID(int iCount);
    bool GetStandardDataFromFLID(QString strFLID, int &iCycleCount, QList<qreal> &dFLDataList);
    bool GetTestDataFromFLID(QString strFLID, int &iCycleCount, QList<qreal> &dFLDataList);

    bool GetTestDataFromCardIDAddTestTime(const QString& strCardIDAndTime, QList<QList<double>> &dFLDataList);

    bool AddCTData(const QString &strFLID, int iCycle, const QString &strRaw, const QString &strFit,
                   const QString &strBaseline, const QString &strSmooth, const QString &strDelta);

    bool UpdateReviewCTData(const QString &strFLID, const QString &strFitReview,
                   const QString &strBaselineReview, const QString &strSmoothReview, const QString &strDeltaReview);

    bool GetCTDelta(const QString &strCardID, QString &strDelta,bool bReview);

    bool GetCTDeltaFormFLID(const QString &strFlid, QStringList &dFLFitList, QStringList &dFLFitReviewList);

    bool GetCTDeltaDataCount(const QString &strCardID, int& iCycleCount);

    bool GetCtDataRaw(const QString &strCardID,QString &strCtRaw);

    bool AddHrmData(const QString &strFLID, int iCycle,const QString &strTemp, const QString &strRaw, const QString &strSmooth, const QString &strDevFirst);

    bool GetHrmDelta(const QString &strCardID, QStringList &strDevFirstList);

    bool AddMeltingTestData(QString strFLID, QString strSNCOde, int iCycleCount,QString strMelting, QString strRemarks);
private:
    void _InitHistoryTable();
    void _InitMeltingTable();
    void _InitCTDataTable();   
    void _InitHrmDataTable();

private:
    CHistoryDB();
    ~CHistoryDB();

private:
    static CHistoryDB* m_pInstance;
    int m_iDatabaseColumnCount;

};

#endif // CHISTORYDB_H
