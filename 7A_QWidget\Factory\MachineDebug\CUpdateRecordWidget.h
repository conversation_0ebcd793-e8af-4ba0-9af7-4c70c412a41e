#ifndef CUPDATERECORDWIDGET_H
#define CUPDATERECORDWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-02-11
  * Description: 软件更新记录
  * -------------------------------------------------------------------------
  * History:升级
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QTextBrowser>

class CUpdateRecordWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CUpdateRecordWidget(QWidget *parent = nullptr);

signals:

private:
    QTextBrowser *m_pTextBrowser;
};

#endif // CUPDATERECORDWIDGET_H
