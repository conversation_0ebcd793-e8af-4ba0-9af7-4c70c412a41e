#include "CPyrolysisDebug.h"
#include <QListView>
#include <QBoxLayout>
#include <QDebug>


CPyrolysisDebug::CPyrolysisDebug(QWidget *parent) : QWidget(parent)
{
    _initParam();
    _InitWidget();

}

void CPyrolysisDebug::_initParam()
{
    m_strCmdList << tr("启动加热") <<tr("停止加热");
    m_htIndexIDMap = {
        {0, Method_HTST},
        {1, Method_HTSP}
    };
    m_strHTOpenList = CPublicConfig::GetInstance()->GetPyrolysisStringList(Method_HTST);
    m_strHTCloseList = CPublicConfig::GetInstance()->GetPyrolysisStringList(Method_HTSP);
}

void CPyrolysisDebug::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    Q_UNUSED(iMachineID);
    Q_UNUSED(iMethodID);
    Q_UNUSED(iResult);
    Q_UNUSED(qVarData);
}


void CPyrolysisDebug::_SlotSendBtn()
{

    QString strID = QString::number(m_htIndexIDMap.value(m_pCmdComboBox->currentIndex()));
    QString strParamTem =  QString::number(CPublicConfig::GetInstance()->GetPyroLysisParamId(m_pParamComboBox->currentIndex()));

    QVariantList qVarList;
    {
        qVarList<<CPublicConfig::GetInstance()->GetPyroLysisParamId(m_pParamComboBox->currentIndex());
         if(Method_HTST == strID.toInt()){
            if(m_pParamLineEdit)
            {
               qVarList<<m_pParamLineEdit->text().toDouble();
            }

         }
    }
    QString strCmd;
    if(qVarList.isEmpty())
        strCmd = GetJsonCmdString(strID.toInt());
    else
        strCmd = GetJsonCmdString(strID.toInt(), qVarList);
    SendJsonCmd(m_pMachineComboBox->GetCurrentIndex(), strID.toInt(), strCmd);
}

void CPyrolysisDebug::_SlotCmdComboBoxChanged(int index)
{
    //int iCmdID = _GetCmdIDByIndex(index);
        m_pParamComboBox->clear();
     if(Method_HTST  == m_htIndexIDMap.value(index))
     {
        m_pParamComboBox->addItems(m_strHTOpenList);
     }
     else if(Method_HTSP == m_htIndexIDMap.value(index))
     {
        m_pParamComboBox->addItems(m_strHTCloseList);
     }
}

void CPyrolysisDebug::_InitWidget()
{
    m_pMachineComboBox = new CLabelComboBox(tr("机器:"));
    m_pMachineComboBox->SetComboBoxList(gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(80, 50);

    m_pCmdComboBox = new QComboBox;
    m_pCmdComboBox->setView(new QListView);
    m_pCmdComboBox->setFixedSize(200, 50);
    connect(m_pCmdComboBox, SIGNAL(currentIndexChanged(const int)), this, SLOT(_SlotCmdComboBoxChanged(int)));

    m_pParamComboBox = new QComboBox;
    m_pParamComboBox->setView(new QListView);
    m_pParamComboBox->setFixedSize(300, 50);
    //connect(m_pParamComboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotCmdComboBoxChanged(int)));


    m_pCmdComboBox->clear();
    m_pCmdComboBox->addItems(m_strCmdList);
    m_pParamComboBox->setCurrentIndex(0);


    m_pParamLineEdit = new CLineEdit;
    m_pParamLineEdit->setFixedSize(120, 50);
    m_pParamLineEdit->setAlignment(Qt::AlignCenter);

    m_pSendBtn = new QPushButton(tr("发送"));
    m_pSendBtn->setFixedSize(80, 50);
    connect(m_pSendBtn, &QPushButton::clicked, this, &CPyrolysisDebug::_SlotSendBtn);

    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(20);
    pTopLayout->addWidget(m_pMachineComboBox);
    pTopLayout->addWidget(m_pCmdComboBox);
    pTopLayout->addWidget(m_pParamComboBox);
    pTopLayout->addWidget(m_pParamLineEdit);
    pTopLayout->addWidget(m_pSendBtn);
    pTopLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setContentsMargins(20, 0, 0, 0);
    pLayout->addLayout(pTopLayout);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}
