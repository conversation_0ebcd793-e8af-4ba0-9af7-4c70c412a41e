#include "CPLCDevItemWidget.h"
#include <QStyle>
#include <QVariant>
#include <QBoxLayout>
#include "PublicConfig.h"

CPLCDevItemWidget::CPLCDevItemWidget(int iMachineID, const SDevParamsStruct &sDevParams, QWidget *parent)
    : QWidget(parent)
    , m_iMachineID(iMachineID)
    , m_sDevParams(sDevParams)
    , m_eStatus(eDeviceDisconnect)
{
    this->setFixedSize(sDevParams.iItemWidth, sDevParams.iItemHeight);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroupBox());
    pLayout->addStretch(1);
    this->setLayout(pLayout);

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalSetDevStatus, this, &CPLCDevItemWidget::SlotSetDevStatus);

    SlotSetDevStatus(iMachineID, m_eStatus);
}

void CPLCDevItemWidget::SlotSetDevStatus(int iMachineID, DeviceStatus eStatus)
{
    if(m_iMachineID != iMachineID)
        return;

    m_eStatus = eStatus;
    QString strPropertyText, strTipsText;
    switch (eStatus)
    {
    case eDeviceDisconnect:
        strPropertyText = "disconnect";
        strTipsText = tr("离线中");
        break;
    case eDeviceSelfTest:
        strPropertyText = "self_test";
        strTipsText = tr("自检中");
        break;
    case eDeviceIdle:
        strPropertyText = "idle";
        strTipsText = tr("空闲中");
        break;
    case eDeviceTesting:
        strPropertyText = "testing";
        strTipsText = tr("测试中");
        break;
    case eDeviceTestDone:
        strPropertyText = "test_done";
        strTipsText = tr("测试完成");
        break;
    case eDeviceTestStopped:
        strPropertyText = "test_stopped";
        strTipsText = tr("测试停止");
        break;
    case eDeviceTestStopping:
        strPropertyText = "test_stopping";
        strTipsText = tr("停止中");
        break;
    case eDeviceTestFail:
        strPropertyText = "test_fail";
        strTipsText = tr("测试失败");
        break;
    case eDeviceFault:
        strPropertyText = "fault";
        strTipsText = tr("故障中");
        break;
    case eDeviceProcessing:
        strPropertyText = "processing";
        strTipsText = tr("处理中");
        break;
    case eDeviceReset:
        strPropertyText = "reset";
        strTipsText = tr("复位中");
        break;
    default:
        break;
    }

    m_pTitleLabel->setProperty("status", strPropertyText);
    m_pTitleLabel->style()->polish(m_pTitleLabel);

    m_pIndexLabel->setProperty("status", strPropertyText);
    m_pIndexLabel->style()->polish(m_pIndexLabel);

    m_pTextLabel->setProperty("status", strPropertyText);
    m_pTextLabel->style()->polish(m_pTextLabel);
    m_pTextLabel->setText(strTipsText);
}

void CPLCDevItemWidget::mousePressEvent(QMouseEvent *pEvent)
{
    emit SignalShowDetailWidget(m_iMachineID);

    QWidget::mousePressEvent(pEvent);
}

QGroupBox *CPLCDevItemWidget::_CreateGroupBox()
{
    int iHeight = m_sDevParams.iTitleHeight;

    m_pIndexLabel = new QLabel(QString("%1").arg(m_iMachineID + 1));
    m_pIndexLabel->setFixedSize(m_sDevParams.iIndexWidth, iHeight);
    m_pIndexLabel->setObjectName("IndexLabel");
    m_pIndexLabel->setAlignment(Qt::AlignCenter);
    m_pIndexLabel->setProperty("status", "idle");

    m_pTextLabel = new QLabel(tr("空闲中"));
    m_pTextLabel->setFixedHeight(iHeight);
    m_pTextLabel->setObjectName("StatusLabel");
    m_pTextLabel->setProperty("status", "idle");

    m_pTitleLabel = new QLabel;
    m_pTitleLabel->setFixedSize(m_sDevParams.iItemWidth, iHeight);
    m_pTitleLabel->setObjectName("TitleLabel");
    m_pTitleLabel->setWindowOpacity(0.14);
    m_pTitleLabel->setProperty("status", "idle");

    m_pImageLabel = new QLabel;
    m_pImageLabel->setFixedSize(m_sDevParams.iDevImageWidth, m_sDevParams.iDevImageHeight);
    m_pImageLabel->setAlignment(Qt::AlignCenter);
    m_pImageLabel->setObjectName("PLCLabel");

    QHBoxLayout *pTitleLayout = new QHBoxLayout;
    pTitleLayout->setMargin(0);
    pTitleLayout->setSpacing(0);
    pTitleLayout->addWidget(m_pIndexLabel);
    pTitleLayout->addSpacing(15);
    pTitleLayout->addWidget(m_pTextLabel);
    pTitleLayout->addStretch(1);
    m_pTitleLabel->setLayout(pTitleLayout);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pTitleLabel);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pImageLabel, 0, Qt::AlignHCenter);
    pLayout->addStretch(1);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(m_sDevParams.iItemWidth, m_sDevParams.iItemHeight);
    pGroupBox->setObjectName("PLCItemGroupBox");
    pGroupBox->setLayout(pLayout);

    return pGroupBox;
}
