#include "CPyrolysis.h"
#include "CPyrolysisCurve.h"
#include "CPyrolysisDebug.h"
#include "CPyrolysisCalibration.h"
#include <QTime>
#include <QDebug>
#include <QVBoxLayout>

CPyrolysis::CPyrolysis(QWidget *parent) : QWidget(parent)
{
    _InitWidget();
}

CPyrolysis::~CPyrolysis()
{

}

void CPyrolysis::_SlotTitleChanged(int index)
{
    m_pStackedWidget->setCurrentIndex(index);
}

void CPyrolysis::_InitWidget()
{
    QStringList strList = {tr("热裂解曲线图"), tr("热裂解指令"),tr("热裂解校准")};
    m_pHBtnTitle = new CHBtnTitleWidget(strList);
    m_pHBtnTitle->SetTitleIndex(0);
    connect(m_pHBtnTitle, &CHBtnTitleWidget::SignalTitleChanged, this, &CPyrolysis::_SlotTitleChanged);

    m_pPyrolysisCurve = new CPyrolysisCurve;
    m_pPyrolysisDebug = new CPyrolysisDebug;
    m_CPyrolysisCalibration = new CPyrolysisCalibration();

    m_pStackedWidget = new QStackedWidget;
    m_pStackedWidget->addWidget(m_pPyrolysisCurve);
    m_pStackedWidget->addWidget(m_pPyrolysisDebug);
    m_pStackedWidget->addWidget(m_CPyrolysisCalibration);
    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pHBtnTitle);
    pLayout->addSpacing(20);
    pLayout->addWidget(m_pStackedWidget);
    this->setLayout(pLayout);
}
