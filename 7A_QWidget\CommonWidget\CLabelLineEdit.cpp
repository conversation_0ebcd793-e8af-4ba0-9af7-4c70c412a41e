#include "CLabelLineEdit.h"
#include <QBoxLayout>

CLabelLineEdit::CLabelLineEdit(const QString &strLabelText, const QString &strEditText, int iSpacing,
                               QBoxLayout::Direction eDirection, QWidget *parent)
    : QWidget(parent)
{
    m_pLabel = new QLabel(strLabelText);

    m_pLineEdit = new CLineEdit(strEditText);
    m_pLineEdit->setFixedSize(303, 56);

    connect(m_pLineEdit, &QLineEdit::textEdited, this, &CLabelLineEdit::SignalTextEdited);
    connect(m_pLineEdit, &QLineEdit::textChanged, this, &CLabelLineEdit::SignalTextChanged);
    connect(m_pLineEdit, &QLineEdit::editingFinished, this, &CLabelLineEdit::SignalEditingFinished);

    QBoxLayout *pLayout = nullptr;
    if(QBoxLayout::Direction::LeftToRight == eDirection)
        pLayout = new QHBoxLayout;
    else
        pLayout = new QVBoxLayout;

    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    //pLayout->addStretch(1);
    pLayout->addWidget(m_pLabel);
    pLayout->addSpacing(iSpacing);
    pLayout->addWidget(m_pLineEdit);
    //pLayout->addStretch(1);
    this->setLayout(pLayout);
}

CLabelLineEdit::~CLabelLineEdit()
{

}

void CLabelLineEdit::ResetLabelSize(int iWidth, int iHeight)
{
    m_pLabel->setFixedSize(iWidth, iHeight);
}

void CLabelLineEdit::ResetLineEditSize(int iWidth, int iHeight)
{
    m_pLineEdit->setFixedSize(iWidth, iHeight);
}

void CLabelLineEdit::SetLineEidtAlignment(Qt::Alignment qAlig)
{
    m_pLineEdit->setAlignment(qAlig);
}

void CLabelLineEdit::SetLineEditText(const QString &strEditText)
{
    m_pLineEdit->setText(strEditText);
}

void CLabelLineEdit::SetLabelFixedSize(int iWidth, int iHeight)
{
    m_pLabel->setFixedSize(iWidth, iHeight);
}

void CLabelLineEdit::SetLineEditFixedSize(int iWidth, int iHeight)
{
    m_pLineEdit->setFixedSize(iWidth, iHeight);
}

void CLabelLineEdit::SetLineEditInputMethod(Qt::InputMethodHint eInputMethod)
{
    m_pLineEdit->setInputMethodHints(eInputMethod);
}

void CLabelLineEdit::SetLineEditFocus()
{
    m_pLineEdit->setFocus();
}

QString CLabelLineEdit::GetLineEditText() const
{
    return m_pLineEdit->text();
}

CVLabelLineEdit::CVLabelLineEdit(const QString &strLabelText, const QString &strEditText,
                                 int iSpacing, QWidget *parent)
    : CLabelLineEdit(strLabelText, strEditText, iSpacing, QBoxLayout::Direction::TopToBottom, parent)
{

}

CVLabelLineEdit::~CVLabelLineEdit()
{

}

CHLabelLineEdit::CHLabelLineEdit(const QString &strLabelText, const QString &strEditText,
                                 int iSpacing, QWidget *parent)
    : CLabelLineEdit(strLabelText, strEditText, iSpacing, QBoxLayout::Direction::LeftToRight, parent)
{

}

CHLabelLineEdit::~CHLabelLineEdit()
{

}
