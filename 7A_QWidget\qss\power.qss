QLabel
{
   color: #6B788F;
   font-size: 24px;
   font-family: "Source Han Sans CN";
}
QLabel#TitleIconLabel
{
   border-radius: 3px;
   background-color: #3D78E5;
}
QLabel#TitleTextLabel
{
   color: #353E4E;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
}

QLabel#InfoLabel
{
   color: red;
   font-size: 24px;
   font-family: "Source Han Sans CN";
}
QPushButton:focus{outline: none;}

QPushButton#ShutdownBtn
{
   border-radius: 24px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #8490FF, stop: 1 #3D78E5);
   image: url(:/image/ico/power/shutdown.png);
}
QPushButton#ShutdownBtn:pressed
{
   border-radius: 24px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #6670c7, stop: 1 #3361b8);
   image: url(:/image/ico/power/shutdown.png);
}

QPushButton#RebootBtn
{
   border-radius: 24px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #8490FF, stop: 1 #3D78E5);
   image: url(:/image/ico/power/reboot.png);
}

QPushButton#RebootBtn:pressed
{
   border-radius: 24px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #6670c7, stop: 1 #3361b8);
   image: url(:/image/ico/power/reboot.png);
}

QPushButton#LogoutBtn
{
   border-radius: 24px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #8490FF, stop: 1 #3D78E5);
   image: url(:/image/ico/power/logout.png);
}

QPushButton#LogoutBtn:pressed
{
   border-radius: 24px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #6670c7, stop: 1 #3361b8);
   image: url(:/image/ico/power/logout.png);
}

QPushButton#LockScreenBtn
{
   border-radius: 24px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #8490FF, stop: 1 #3D78E5);
   image: url(:/image/ico/power/lock.png);
}

QPushButton#LockScreenBtn:pressed
{
   border-radius: 24px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #6670c7, stop: 1 #3361b8);
   image: url(:/image/ico/power/lock.png);
}

QPushButton#ReturnBtn
{
   border-radius: 24px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #8490FF, stop: 1 #3D78E5);
   image: url(:/image/ico/power/cancel.png);
}

QPushButton#ReturnBtn:pressed
{
   border-radius: 24px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #6670c7, stop: 1 #3361b8);
   image: url(:/image/ico/power/cancel.png);
}

QPushButton#CancelBtn
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 2px solid #3D78E5;
   background-color: #FFF;
}

QPushButton#CancelBtn:pressed
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 4px solid #3D78E5;
   background-color: #FFF;
}

QPushButton#ConfirmBtn
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #8490FF, stop: 1 #3D78E5);
}
QPushButton#ConfirmBtn:pressed
{
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #6670c7, stop: 1 #3361b8);
}

QPushButton#SeeBtn
{
   background-color: #FFF;
   border: 0px;
}

QLineEdit
{
   color: #6B788F;
   font-size: 24px;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   padding-left: 20px;
   border: 0px solid #cad2dc;
   background-color: #FFF;
}
QLineEdit:focus{ padding-left: 20px; }

QGroupBox
{
   border-radius: 32px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
       stop: 0 #DEE8FB, stop: 1 #FFF);
}

