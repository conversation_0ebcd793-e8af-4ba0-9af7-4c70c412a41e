#ifndef CQCPAGE_H
#define CQCPAGE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-21
  * Description: 质控页
  * -------------------------------------------------------------------------
  * History: 2024-08-13 去除设备组的概念, 由一拖一到一拖八
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QGroupBox>
#include <QPushButton>

#include "CQCDevGroupWidget.h"

class CQCPage : public QWidget
{
    Q_OBJECT
public:
    explicit CQCPage(QWidget *parent = nullptr);

protected:
    virtual void hideEvent(QHideEvent *pEvent) override;

signals:
    void SignalCreateQCTest(int iMachineID,QString strQCModel);

private slots:
    void _SlotItemBtnClicked();
    void _SlotGroupSelected();
    void _SlotPostiveBtn();
    void _SlotNegativeBtn();

private:
    bool GetSelectedMachineID(int &iMachineID);

private:
    QPushButton *_CreateActBtn(const QString &strIconPath, const QString &strBtnText);
    QGroupBox *_CreateUpGroupBox();
    QGroupBox *_CreateDownGroupBox();

private:
    int m_iDevNum, m_iItemNum;
    QList<CQCDevItemBtn *> m_pDevItemBtnList;
    QList<CQCDevGroupWidget *> m_pDevGroupWidgetList;
    QLabel *m_pIcon1Label, *m_pTitle1Label;
    QLabel *m_pIcon2Label, *m_pTitle2Label;
    QPushButton *m_pPositiveBtn, *m_pNegativeBtn;
};

#endif // CQCPAGE_H
