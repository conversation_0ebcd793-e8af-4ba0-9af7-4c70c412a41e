#ifndef CCALIBRATIONWIDGET_H
#define CCALIBRATIONWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-15
  * Description: 仪器校准
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QStackedWidget>
#include "CCaliDeviceWidget.h"
#include "CCaliHistoryWidget.h"
#include "SystemPage/CSysFirstTitleWidget.h"
#include "SystemPage/CSysSecondTitleWidget.h"

class CCalibrationWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CCalibrationWidget(QWidget *parent = nullptr);

signals:
    void SignalReturn();

private slots:
    void _SlotTitleChanged(int iTitle);

private:
    void _InitWidget();
    void _InitLayout();

private:
    CSysFirstTitleWidget *m_pCSysTtileLabelWidget;
    QLabel *m_pBackgroundLabel;

    CSysSecondTitleWidget *m_pCSysSecondTitleWidget;
    QStackedWidget *m_pStackedWidget;

    CCaliDeviceWidget *m_pCCaliDeviceWidget;
    CCaliHistoryWidget *m_pCCaliHistoryWidget;
};

#endif // CCALIBRATIONWIDGET_H
