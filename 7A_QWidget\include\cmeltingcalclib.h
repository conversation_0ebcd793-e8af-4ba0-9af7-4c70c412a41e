#ifndef CMELTINGCALC_H
#define CMELTINGCALC_H

#include <QList>
#include<QString>

#if defined(CMELTINGCALCLIB_LIBRARY)
#define CMELTINGCALCLIB_EXPORT Q_DECL_EXPORT
#else
#  define CMELTINGCALCLIB_EXPORT Q_DECL_IMPORT
#endif


class CMeltingCalcLib
{
public:
    CMeltingCalcLib();

    // meltingCalc
    void meltingCalc(const QList<double> &dTempVec, const QList<double> &dFLList,QString strConfig);

    // 这个可以不用了
    double _GetDerivateList(const QVector<double> &dTempVec, const QList<double> &dFLList);

    // 获取一阶导数
    QList<QPointF> getDerivatePointList();

    // 获得平滑数据
    QList<QPointF> getSmoothPointList();

    QString getTmRmStrResult();

    QList<double> moving_average(const QList<double>& dSrcDataList,qreal window_size);

    QString getDebugOutput();

private:
    void _derivateRawCurve(const QList<QPointF> smoothList);

    void _smoothRawCurve(const QList<double>& dTempVec,const QList<double>& dRawData);

    void parseParam(const QString& strParam);

    void _findResult(const QList<QPointF>& pointList,int len,float threshold);

    void _findMaxValueIndex(int len, const std::vector<double>& firDevVec,std::vector<int>& MaxValueIndex);

    float _GetTmValue(int TmIndex, const std::vector<double> &firDevVec,const QList<QPointF>& Pointf);

    float _GetRmValue(int TmIndex,const std::vector<int>& ReverseMaxValueIndex,const QList<QPointF>& firDevVec);

    void clear();


private:
    struct stTmRmYm
    {
        float Tm = 0;
        float Rm = 0;
        float Ym;
    };

    // 配置
    float m_threshould{0};
    int m_len{1};
    float m_limitLeft{0};
    float m_limitRight{100};

    QList<QPointF> m_fRawListPoint;

    // 返回数据
    // 导数
    QList<QPointF> m_fDerivateListPoint;
    // 平滑数据
    QList<QPointF> m_fSmoothListPoint;

    // 结果队列
    // Tm1 与 Rm1
    //pair 与 QList
    QList<stTmRmYm> m_ListTmRmYm;
    //QList<std::pair<float,float>> m_TmRmResult;
};

#endif // CMeltingCalcLib_H
