QLabel#TitleIconLabel
{
   border-radius: 3px;
   background-color: #3D78E5;
}
QLabel#TitleTextLabel
{
   color: #353E4E;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
}
QLabel#WhiteTextLabel
{
    color: #FFF;
    font-size: 24px;
    font-weight: 400;
    font-family: "Source Han Sans CN";
}
QLabel#UserLabel
{
    color: #FFF;
    font-size: 24px;
    font-weight: 450;
    font-family: "Source Han Sans CN";
}
QLabel[select=false]#MainTitleLabel
{
    color: #FFF;
    font-size: 24px;
    font-weight: 400;
    font-family: "Source Han Sans CN";
}

QLabel[select=true]#MainTitleLabel
{
    color: #FFF;
    font-size: 24px;
    font-weight: bold;
    font-family: "Source Han Sans CN";
}

QLabel#RunLogLabel
{
    color: #FFF;
    font-size: 20px;
    font-weight: 400;
    font-family: "Source Han Sans CN";
}

QLabel#PowerLabel
{
    color: #FFF;
    font-size: 18px;
    font-weight: 400;
    font-family: "Source Han Sans CN";
}

QPushButton:focus{outline: none;}

QPushButton#HomeBtn
{
    color: #FFF;
    font-size: 22px;
    font-family:"Source Han Sans CN";
    background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
        stop: 0 #5591FF, stop: 1 #8490FF);
    border: 0px solid #0068b7;
    border-radius: 16px;
    image: url(:/image/ico/main/home.png);
}
QPushButton#HomeBtn:disabled
{
    color: #FFF;
    font-size: 22px;
    font-family:"Source Han Sans CN";
    background-color: #FFF;
    border: 0px solid #0068b7;
    border-radius: 16px;
    image: url(:/image/ico/main/home_disable.png);
}

QPushButton#QCBtn
{
    color: #FFF;
    font-size: 22px;
    font-family:"Source Han Sans CN";
    background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
        stop: 0 #5591FF, stop: 1 #8490FF);
    border: 0px solid #0068b7;
    border-radius: 16px;
    image: url(:/image/ico/main/QC.png);
}
QPushButton#QCBtn:disabled
{
    color: #FFF;
    font-size: 22px;
    font-family:"Source Han Sans CN";
    background-color: #FFF;
    border: 0px solid #0068b7;
    border-radius: 16px;
    image: url(:/image/ico/main/QC_disable.png);
}
QPushButton#HistoryBtn
{
    color: #FFF;
    font-size: 22px;
    font-family:"Source Han Sans CN";
    background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
        stop: 0 #5591FF, stop: 1 #8490FF);
    border: 0px solid #0068b7;
    border-radius: 16px;
    image: url(:/image/ico/main/history.png);
}
QPushButton#HistoryBtn:disabled
{
    color: #FFF;
    font-size: 22px;
    font-family:"Source Han Sans CN";
    background-color: #FFF;
    border: 0px solid #0068b7;
    border-radius: 16px;
    image: url(:/image/ico/main/history_disable.png);
}
QPushButton#SystemBtn
{
    color: #FFF;
    font-size: 22px;
    font-family:"Source Han Sans CN";
    background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
        stop: 0 #5591FF, stop: 1 #8490FF);
    border: 0px solid #0068b7;
    border-radius: 16px;
    image: url(:/image/ico/main/system.png);
}
QPushButton#SystemBtn:disabled
{
    color: #FFF;
    font-size: 22px;
    font-family:"Source Han Sans CN";
    background-color: #FFF;
    border: 0px solid #0068b7;
    border-radius: 16px;
    image: url(:/image/ico/main/system_disable.png);
}
QPushButton#PowerBtn
{
    border-radius: 16px;
    background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
        stop: 0 #5591FF, stop: 1 #8490FF);
}

QGroupBox#TitleGroupBox
{
    border-radius: 0px;
    background-color: transparent;
}

QGroupBox#MainGroupBox
{
    background-color: #F3F8FF;
    border-bottom-left-radius: 40px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
    border: 0px solid #529BFF;
}
QStackedWidget
{
    background-color: transparent;
    border-radius: 0px;
    border: 0px solid #529BFF;
}
