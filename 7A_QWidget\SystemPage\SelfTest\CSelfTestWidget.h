#ifndef CSELFTESTWIDGET_H
#define CSELFTESTWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-15
  * Description: 自检
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QStackedWidget>

#include "CSelfTestDeviceWidget.h"
#include "CSelfTestHistoryWidget.h"
#include "SystemPage/CSysFirstTitleWidget.h"
#include "SystemPage/CSysSecondTitleWidget.h"

class CSelfTestWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CSelfTestWidget(QWidget *parent = nullptr);

signals:
    void SignalReturn();

private slots:
    void _SlotTitleChanged(int iTitle);

private:
    void _InitWidget();
    void _InitLayout();

private:
    CSysFirstTitleWidget *m_pCSysTtileLabelWidget;
    QLabel *m_pBackgroundLabel;

    CSysSecondTitleWidget *m_pCSysSecondTitleWidget;
    QStackedWidget *m_pStackedWidget;

    CSelfTestDeviceWidget *m_pCSelfTestDeviceWidget;
    CSelfTestHistoryWidget *m_pCSelfTestHistoryWidget;
};

#endif // CSELFTESTWIDGET_H
