#include "CCaliDeviceWidget.h"
#include <QBoxLayout>
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "DBControl/CDevInfoDB.h"

CCaliDeviceWidget::CCaliDeviceWidget(QWidget *parent) : QWidget(parent)
{
    m_iDevNum = 1;
    m_iItemNum = 8;
    CPublicConfig::GetInstance()->GetDevItemNum(m_iDevNum, m_iItemNum);

    _Init_1x8();

    Register2Map(Method_MCHK);
}

CCaliDeviceWidget::~CCaliDeviceWidget()
{
    UnRegister2Map(Method_MCHK);
}

void CCaliDeviceWidget::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    Q_UNUSED(qVarData);

    if(iMachineID < 0 || iMachineID >= m_pDevItemList.size())
        return;

    if(Method_MCHK != iMethodID)
        return;

    m_pDevItemList.at(iMachineID)->ReceiveCaliResult(iResult);
}

void CCaliDeviceWidget::showEvent(QShowEvent *pEvent)
{
    QMap<int, QStringList> iDateMap = CDevInfoDB::GetInstance().GetAllCalibrateDate();
    for(int i=0; i<m_pDevItemList.size(); i++)
    {
        QStringList strList = iDateMap.value(m_pDevItemList.at(i)->GetMachineID());
        if(strList.size() >= 2)
            m_pDevItemList.at(i)->UpdateDateLabel(strList.at(0), strList.at(1));
    }

    QWidget::showEvent(pEvent);
}

void CCaliDeviceWidget::_Init_1x8()
{
    m_pReturnBtn = new QPushButton(tr("返回"));
    m_pReturnBtn->setFixedSize(150, 56);
    m_pReturnBtn->setObjectName("CancelBtn");
    connect(m_pReturnBtn, &QPushButton::clicked, this, &CCaliDeviceWidget::SignalReturn);

    QString strQssPath;
    SDevParamsStruct sDevParams;

    if(1 == m_iItemNum || 2 == m_iItemNum)
    {
        sDevParams.iItemWidth = 630;
        sDevParams.iItemHeight = 550;

        sDevParams.iTitleHeight = 80;
        sDevParams.iIndexWidth = 100;

        sDevParams.iActIconSize = 34;
        sDevParams.iActBtnWidth = 200;
        sDevParams.iActBtnHeight = 56;

        sDevParams.iBottonSpacing = 20;

        strQssPath = ":/qss/qss/home/<USER>";
    }
    else if(2 == m_iItemNum)
    {
        sDevParams.iItemWidth = 630;
        sDevParams.iItemHeight = 550;

        sDevParams.iTitleHeight = 80;
        sDevParams.iIndexWidth = 100;

        sDevParams.iActIconSize = 34;
        sDevParams.iActBtnWidth = 200;
        sDevParams.iActBtnHeight = 56;

        sDevParams.iBottonSpacing = 20;

        strQssPath = ":/qss/qss/home/<USER>";
    }
    else if(3 == m_iItemNum)
    {
        sDevParams.iItemWidth = 430;
        sDevParams.iItemHeight = 470;

        sDevParams.iTitleHeight = 60;
        sDevParams.iIndexWidth = 75;

        sDevParams.iActBtnWidth = 160;
        sDevParams.iActBtnHeight = 44;

        sDevParams.iBottonSpacing = 20;

        strQssPath = ":/qss/qss/home/<USER>";
    }
    else if(4 == m_iItemNum)
    {
        sDevParams.iItemWidth = 340;
        sDevParams.iItemHeight = 380;

        sDevParams.iTitleHeight = 54;
        sDevParams.iIndexWidth = 66;

        sDevParams.iActBtnWidth = 160;
        sDevParams.iActBtnHeight = 44;

        sDevParams.iBottonSpacing = 20;

        strQssPath = ":/qss/qss/home/<USER>";
    }
    else if(5 == m_iItemNum || 6 == m_iItemNum || 7 == m_iItemNum || 8 == m_iItemNum)
    {
        sDevParams.iItemWidth = 340;
        sDevParams.iItemHeight = 320;

        sDevParams.iTitleHeight = 54;
        sDevParams.iIndexWidth = 66;

        sDevParams.iActBtnWidth = 160;
        sDevParams.iActBtnHeight = 44;

        sDevParams.iBottonSpacing = 15;

        strQssPath = ":/qss/qss/home/<USER>";
    }

    for(int i=0; i<m_iItemNum; i++)
    {
        CCaliDevItemWidget *pItemWidget = new CCaliDevItemWidget(i, sDevParams);
        m_pDevItemList.push_back(pItemWidget);
    }

    QBoxLayout *pTopLayout = nullptr;
    if(m_iItemNum <= 4)
    {
        pTopLayout = new QHBoxLayout;
        pTopLayout->setMargin(0);
        pTopLayout->setSpacing(0);
        pTopLayout->addStretch(1);
        for(int i=0; i<m_pDevItemList.size(); i++)
        {
            pTopLayout->addWidget(m_pDevItemList.at(i));
            pTopLayout->addStretch(1);
        }
    }
    else
    {
        QHBoxLayout *pUpLayout = new QHBoxLayout;
        pUpLayout->setMargin(0);
        pUpLayout->setSpacing(0);
        pUpLayout->addSpacing(45);
        pUpLayout->addWidget(m_pDevItemList.at(0));
        pUpLayout->addStretch(1);
        pUpLayout->addWidget(m_pDevItemList.at(1));
        pUpLayout->addStretch(1);
        pUpLayout->addWidget(m_pDevItemList.at(2));
        pUpLayout->addStretch(1);
        pUpLayout->addWidget(m_pDevItemList.at(3));
        pUpLayout->addSpacing(45);

        QHBoxLayout *pDownLayout = new QHBoxLayout;
        pDownLayout->setMargin(0);
        pDownLayout->setSpacing(0);
        pDownLayout->addSpacing(45);
        if(5 == m_pDevItemList.size())
        {
            pDownLayout->addWidget(m_pDevItemList.at(4));
            pDownLayout->addStretch(1);
        }
        else if(6 == m_pDevItemList.size())
        {
            pDownLayout->addWidget(m_pDevItemList.at(4));
            pDownLayout->addSpacing(62);
            pDownLayout->addWidget(m_pDevItemList.at(5));
            pDownLayout->addStretch(1);
        }
        else if(7 == m_pDevItemList.size())
        {
            pDownLayout->addWidget(m_pDevItemList.at(4));
            pDownLayout->addSpacing(62);
            pDownLayout->addWidget(m_pDevItemList.at(5));
            pDownLayout->addSpacing(62);
            pDownLayout->addWidget(m_pDevItemList.at(6));
            pDownLayout->addStretch(1);
        }
        else
        {
            pDownLayout->addWidget(m_pDevItemList.at(4));
            pDownLayout->addStretch(1);
            pDownLayout->addWidget(m_pDevItemList.at(5));
            pDownLayout->addStretch(1);
            pDownLayout->addWidget(m_pDevItemList.at(6));
            pDownLayout->addStretch(1);
            pDownLayout->addWidget(m_pDevItemList.at(7));
            pDownLayout->addSpacing(45);
        }

        pTopLayout = new QVBoxLayout;
        pTopLayout->setMargin(0);
        pTopLayout->setSpacing(0);
        pTopLayout->addLayout(pUpLayout);
        pTopLayout->addSpacing(35);
        pTopLayout->addLayout(pDownLayout);
    }

    LoadQSS(this, strQssPath);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addLayout(pTopLayout);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pReturnBtn, 0, Qt::AlignHCenter);
    this->setLayout(pLayout);
}
