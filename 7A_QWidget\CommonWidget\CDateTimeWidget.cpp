#include "CDateTimeWidget.h"
#include <QDate>
#include <QEvent>
#include <QDebug>
#include <QPainter>
#include <QBoxLayout>
#include <QScrollBar>

#include "PublicParams.h"
#include "PublicFunction.h"

static QString Get2Length(int iData)
{
    QString strData = QString::number(iData);
    if(strData.length() < 2)
        strData.push_front("0");
    return strData;
}

CTimeWidget::CTimeWidget(const QString &strTitle, const QList<int> &iDataList, QWidget *parent) : QWidget(parent)
{
    m_iDataList = iDataList;
    this->setFixedSize(130, 400);

    m_pTitleLabel = new QLabel(strTitle);
    m_pTitleLabel->setFixedSize(130, 50);
    m_pTitleLabel->setAlignment(Qt::AlignCenter);

    m_pListWidget = new QListWidget;
    m_pListWidget->setFixedSize(130, 350);
    m_pListWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pListWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_pListWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    connect(m_pListWidget, &QListWidget::itemClicked, this, &CTimeWidget::_SlotListItemClicked);

    for(int i=0; i<iDataList.size(); i++)
    {
        QListWidgetItem *pItem = new QListWidgetItem;
        pItem->setSizeHint(QSize(130, 50));
        pItem->setTextAlignment(Qt::AlignCenter);
        pItem->setText(QString::number(iDataList.at(i)));
        m_pListWidget->addItem(pItem);
    }

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pTitleLabel);
    pLayout->addWidget(m_pListWidget);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}

void CTimeWidget::SetDataList(const QList<int> &iDataList)
{
    if(iDataList == m_iDataList)
        return;

    m_iDataList = iDataList;
    m_pListWidget->clear();
    for(int i=0; i<m_iDataList.size(); i++)
    {
        QListWidgetItem *pItem = new QListWidgetItem;
        pItem->setSizeHint(QSize(130, 50));
        pItem->setTextAlignment(Qt::AlignCenter);
        pItem->setText(QString::number(m_iDataList.at(i)));
        m_pListWidget->addItem(pItem);
    }
}

void CTimeWidget::SetCurrentData(int iData)
{
    m_pListWidget->setCurrentRow(m_iDataList.indexOf(iData));
}

void CTimeWidget::SetObjName(const QString &strLabelName, const QString &strListWidgetName)
{
    m_pTitleLabel->setObjectName(strLabelName);
    m_pListWidget->setObjectName(strListWidgetName);
}

int CTimeWidget::GetCurrentData() const
{
    if(!m_pListWidget->currentItem())
    {
        if(m_iDataList.isEmpty())
            return 1;
        else
            return m_iDataList.first();
    }

    return m_pListWidget->currentItem()->text().toInt();
}

void CTimeWidget::_SlotListItemClicked(QListWidgetItem *pItem)
{
    if(!pItem)
        return;

    emit SignalDataChanged(pItem->text().toInt());
}


/* 日期-时间  */
CDateTimeWidget::CDateTimeWidget(QWidget *parent)
    : CDateTimeWidget(QDateTime::currentDateTime(), parent)
{

}

CDateTimeWidget::CDateTimeWidget(const QString &strDateTime, QWidget *parent)
    : CDateTimeWidget(QDateTime::fromString(strDateTime, "yyyy-MM-dd hh:mm:ss"), parent)
{

}

CDateTimeWidget::CDateTimeWidget(const QString &strDate, const QString &strTime, QWidget *parent)
    : CDateTimeWidget(QDateTime::fromString(strDate + " " + strTime, "yyyy-MM-dd hh:mm:ss"), parent)
{

}

CDateTimeWidget::CDateTimeWidget(const QDate &qDate, const QTime &qTime, QWidget *parent)
    : CDateTimeWidget(QDateTime(qDate, qTime), parent)
{

}

CDateTimeWidget::CDateTimeWidget(const QDateTime &qDateTime, QWidget *parent) : QWidget(parent)
{
    m_bOnlyDateModel = false;
    m_iBigMonthList << 1 << 3 << 5 << 7 << 8 << 10 << 12;
    m_iSmallMonthList << 2 << 4 << 6 <<9 << 11;

    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);   
    this->setAttribute(Qt::WA_TranslucentBackground);
    this->setFixedSize(parent ? parent->size() : G_QRootSize);
    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroupBox());
    pLayout->addStretch(1);
    this->setLayout(pLayout);
    LoadQSS(this, ":/qss/qss/time.qss");

    SetDateTime(qDateTime);
}

void CDateTimeWidget::SetDateTime(const QString &strDateTime)
{
    if(m_bOnlyDateModel)
        SetDateTime(QDateTime::fromString(strDateTime, "yyyy-MM-dd"));
    else
        SetDateTime(QDateTime::fromString(strDateTime, "yyyy-MM-dd hh:mm:ss"));
}

void CDateTimeWidget::SetDateTime(const QDateTime &qDateTime)
{
    m_qDateTime = qDateTime;
    if(!m_qDateTime.isValid())
        m_qDateTime = QDateTime::currentDateTime();

    QDate qDate = m_qDateTime.date();
    m_pDateLabel->setText(qDate.toString("yyyy-MM-dd"));
    m_pYearWidget->SetCurrentData(qDate.year());
    m_pMonthWidget->SetCurrentData(qDate.month());
    m_pDayWidget->SetCurrentData(qDate.day());

    QTime qTime = m_qDateTime.time();
    m_pTimeLabel->setText(qTime.toString("hh:mm:ss"));
    m_pHourWidget->SetCurrentData(qTime.hour());
    m_pMinuteWidget->SetCurrentData(qTime.minute());
    m_pSecondWidget->SetCurrentData(qTime.second());

    _ShowDateOrTime(true);
}

void CDateTimeWidget::GetDateTime(QString &strDateTime) const
{
    int iYear = m_pYearWidget->GetCurrentData();
    QString strMonth = Get2Length(m_pMonthWidget->GetCurrentData());
    QString strDay = Get2Length(m_pDayWidget->GetCurrentData());
    QString strHour = Get2Length(m_pHourWidget->GetCurrentData());
    QString strMinute = Get2Length(m_pMinuteWidget->GetCurrentData());
    QString strSecond = Get2Length(m_pSecondWidget->GetCurrentData());

    if(m_bOnlyDateModel)
        strDateTime = QString("%1-%2-%3").arg(iYear).arg(strMonth).arg(strDay);
    else
        strDateTime = QString("%1-%2-%3 %4:%5:%6").arg(iYear).arg(strMonth).arg(strDay).arg(strHour).arg(strMinute).arg(strSecond);
}

void CDateTimeWidget::GetDateTime(QDateTime &qDateTime) const
{
    QString strDateTime;
    GetDateTime(strDateTime);

    if(m_bOnlyDateModel)
        qDateTime = QDateTime::fromString(strDateTime, "yyyy-MM-dd");
    else
        qDateTime = QDateTime::fromString(strDateTime, "yyyy-MM-dd hh:mm:ss");
}

void CDateTimeWidget::SetOnlyDateModel()
{
    m_bOnlyDateModel = true;
    m_pDateLabel->setVisible(false);
    m_pTimeLabel->setVisible(false);

    m_pGroupBox->setFixedHeight(510);
}

bool CDateTimeWidget::eventFilter(QObject *pWatched, QEvent *pEvent)
{
    if(pWatched->inherits("QLabel"))
    {
        if(QEvent::MouseButtonPress == pEvent->type())
        {
            QLabel *pLabel = dynamic_cast<QLabel *>(pWatched);
            QString strObjName = pLabel->objectName();
            if("DateLabel" == strObjName)
                _ShowDateOrTime(true);
            else if("TimeLabel" == strObjName)
                _ShowDateOrTime(false);
        }
    }

    return QWidget::eventFilter(pWatched, pEvent);
}

void CDateTimeWidget::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, gk_iBackTransparency));
    QWidget::paintEvent(pEvent);
}

void CDateTimeWidget::_SlotYearChanged(int iYear)
{
    int iCurrentMonth = m_pMonthWidget->GetCurrentData();
    if(2 == iCurrentMonth)
    {
        int iMinDay = 1;
        int iMaxDay = 28;
        if(QDate::isLeapYear(iYear))
            iMaxDay = 29;
        _UpdateDayWidget(iMinDay, iMaxDay);
    }
    _SlotUpdateDateText();
}

//大月1-31天;小月:闰年(1-29),非闰年(1-28)
void CDateTimeWidget::_SlotMonthChanged(int iMonth)
{
    //大月
    if(m_iBigMonthList.contains(iMonth))
    {
        _UpdateDayWidget(1, 31);
    }
    else
    {
        //小月
        int iMinDay = 1;
        int iMaxDay = 30;
        if(2 == iMonth)
        {
            if(QDate::isLeapYear(m_pYearWidget->GetCurrentData()))
                iMaxDay = 29;
            else
                iMaxDay = 28;
        }
        _UpdateDayWidget(iMinDay, iMaxDay);
    }
    _SlotUpdateDateText();
}

void CDateTimeWidget::_SlotUpdateDateText()
{
    int iYear = m_pYearWidget->GetCurrentData();
    QString strMonth = Get2Length(m_pMonthWidget->GetCurrentData());
    QString strDay = Get2Length(m_pDayWidget->GetCurrentData());
    QString strDate = QString("%1-%2-%3").arg(iYear).arg(strMonth).arg(strDay);
    m_pDateLabel->setText(strDate);
}

void CDateTimeWidget::_SlotUpdateTimeText()
{
    QString strHour = Get2Length(m_pHourWidget->GetCurrentData());
    QString strMinute = Get2Length(m_pMinuteWidget->GetCurrentData());
    QString strSecond = Get2Length(m_pSecondWidget->GetCurrentData());
    QString strTime = QString("%1:%2:%3").arg(strHour).arg(strMinute).arg(strSecond);
    m_pTimeLabel->setText(strTime);
}

void CDateTimeWidget::_SlotComfirmBtn()
{
    QString strDateTime;
    GetDateTime(strDateTime);
    emit SignalDateTime(strDateTime);
    this->close();
}

void CDateTimeWidget::_SlotCancleBtn()
{
    this->close();
}

void CDateTimeWidget::_SlotClearBtn()
{
    emit SignalDateTime("");
    this->close();
}

void CDateTimeWidget::_UpdateDayWidget(int iMinDay, int iMaxDay)
{
    QList<int> iDayList;
    for(int i=iMinDay; i<=iMaxDay; i++)
        iDayList << i;

    int iCurrentDay = m_pDayWidget->GetCurrentData();
    m_pDayWidget->SetDataList(iDayList);
    if(iCurrentDay > iMaxDay)
        iCurrentDay = iMaxDay;
    m_pDayWidget->SetCurrentData(iCurrentDay);
}

void CDateTimeWidget::_ShowDateOrTime(bool bDate)
{
    m_pDateLabel->setEnabled(!bDate);
    m_pYearWidget->setVisible(bDate);
    m_pMonthWidget->setVisible(bDate);
    m_pDayWidget->setVisible(bDate);

    m_pTimeLabel->setEnabled(bDate);
    m_pHourWidget->setVisible(!bDate);
    m_pMinuteWidget->setVisible(!bDate);
    m_pSecondWidget->setVisible(!bDate);
}

QGroupBox *CDateTimeWidget::_CreateGroupBox()
{
    m_pDateLabel = new QLabel;
    m_pDateLabel->setFixedSize(177, 56);
    m_pDateLabel->setObjectName("DateLabel");
    m_pDateLabel->installEventFilter(this);
    m_pDateLabel->setEnabled(false);
    m_pDateLabel->setAlignment(Qt::AlignCenter);

    m_pTimeLabel = new QLabel;
    m_pTimeLabel->setFixedSize(177, 56);
    m_pTimeLabel->setObjectName("TimeLabel");
    m_pTimeLabel->installEventFilter(this);
    m_pTimeLabel->setEnabled(true);
    m_pTimeLabel->setAlignment(Qt::AlignCenter);

    QList<int> iYearList;
    //int iCurrentYear = QDate::currentDate().year();
    for(int i=1824; i<=2099; i++)
        iYearList << i;
    m_pYearWidget = new CTimeWidget(tr("年"), iYearList);
    connect(m_pYearWidget, &CTimeWidget::SignalDataChanged, this, &CDateTimeWidget::_SlotYearChanged);

    QList<int> iMonthList;
    for(int i=1; i<=12; i++)
        iMonthList << i;
    m_pMonthWidget = new CTimeWidget(tr("月"), iMonthList);
    connect(m_pMonthWidget, &CTimeWidget::SignalDataChanged, this, &CDateTimeWidget::_SlotMonthChanged);

    QList<int> iDayList;
    for(int i=1; i<=31; i++)
        iDayList << i;
    m_pDayWidget = new CTimeWidget(tr("日"), iDayList);
    connect(m_pDayWidget, &CTimeWidget::SignalDataChanged, this, &CDateTimeWidget::_SlotUpdateDateText);

    QList<int> iHourList;
    for(int i=0; i<=23; i++)
        iHourList << i;
    m_pHourWidget = new CTimeWidget(tr("时"), iHourList);
    m_pHourWidget->setVisible(false);
    connect(m_pHourWidget, &CTimeWidget::SignalDataChanged, this, &CDateTimeWidget::_SlotUpdateTimeText);

    QList<int> iMinuteList;
    for(int i=0; i<=59; i++)
        iMinuteList << i;
    m_pMinuteWidget = new CTimeWidget(tr("分"), iMinuteList);
    m_pMinuteWidget->setVisible(false);
    connect(m_pMinuteWidget, &CTimeWidget::SignalDataChanged, this, &CDateTimeWidget::_SlotUpdateTimeText);

    m_pSecondWidget = new CTimeWidget(tr("秒"), iMinuteList);
    m_pSecondWidget->setVisible(false);
    connect(m_pSecondWidget, &CTimeWidget::SignalDataChanged, this, &CDateTimeWidget::_SlotUpdateTimeText);

    m_pTipLabel = new QLabel(tr("选择时间"));
    m_pTipLabel->setFixedHeight(35);
    m_pTipLabel->setObjectName("TitleLabel");

    m_pConfirmBtn = new QPushButton(tr("确定"));
    m_pConfirmBtn->setFixedSize(130, 56);
    connect(m_pConfirmBtn, &QPushButton::clicked, this, &CDateTimeWidget::_SlotComfirmBtn);

    m_pCancleBtn = new QPushButton(tr("取消"));
    m_pCancleBtn->setFixedSize(130, 56);
    m_pCancleBtn->setObjectName("CancelBtn");
    connect(m_pCancleBtn, &QPushButton::clicked, this, &CDateTimeWidget::_SlotCancleBtn);

    m_pClearBtn = new QPushButton(tr("清空"));
    m_pClearBtn->setFixedSize(130, 56);
    connect(m_pClearBtn, &QPushButton::clicked, this, &CDateTimeWidget::_SlotClearBtn);

    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(0);
    pTopLayout->addStretch(1);
    pTopLayout->addWidget(m_pDateLabel);
    pTopLayout->addSpacing(20);
    pTopLayout->addWidget(m_pTimeLabel);
    pTopLayout->addStretch(1);

    QHBoxLayout *pMidLayout = new QHBoxLayout;
    pMidLayout->setMargin(0);
    pMidLayout->setSpacing(0);
    pMidLayout->addStretch(1);
    pMidLayout->addWidget(m_pYearWidget);
    pMidLayout->addWidget(m_pMonthWidget);
    pMidLayout->addWidget(m_pDayWidget);
    pMidLayout->addWidget(m_pHourWidget);
    pMidLayout->addWidget(m_pMinuteWidget);
    pMidLayout->addWidget(m_pSecondWidget);
    pMidLayout->addStretch(1);

    QHBoxLayout *pBottomLayout = new QHBoxLayout;
    pBottomLayout->setMargin(0);
    pBottomLayout->setSpacing(20);
    pBottomLayout->addStretch(1);
    pBottomLayout->addWidget(m_pCancleBtn);
    pBottomLayout->addWidget(m_pClearBtn);
    pBottomLayout->addWidget(m_pConfirmBtn);
    pBottomLayout->addStretch(1);

    QVBoxLayout *pMainLayout = new QVBoxLayout;
    pMainLayout->setMargin(0);
    pMainLayout->setSpacing(0);
    pMainLayout->addSpacing(18);
    pMainLayout->addLayout(pTopLayout);
    pMainLayout->addStretch(1);
    pMainLayout->addLayout(pMidLayout);
    pMainLayout->addSpacing(20);
    pMainLayout->addLayout(pBottomLayout);
    pMainLayout->addSpacing(16);

    m_pGroupBox = new QGroupBox;
    m_pGroupBox->setFixedSize(470, 580);
    m_pGroupBox->setLayout(pMainLayout);
    return m_pGroupBox;
}
