#!/bin/bash

cd /root

#unzip -o auto.zip
rm auto.tar
gunzip auto.tar.gz
tar xvf auto.tar

chmod -R 777 ./*

if [ $? -ne 0 ]
then
        echo $?
        echo "ERROR......"
	reboot
        exit 0;
else
        echo "OVER 0"
fi

#cp /usr/share/zoneinfo/UTC /etc/localtime
cp fonts/* /usr/share/fonts/
cp lib/lib* /usr/lib
cp S99_7C /etc/init.d/

if [ $? -ne 0 ]
then
        echo $?
        echo "ERROR......"
	reboot
        exit 1;
else
        echo "OVER 1"
fi

reboot
