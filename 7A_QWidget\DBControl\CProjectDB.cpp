#include "CProjectDB.h"
#include <QDateTime>
#include <QUuid>
#include <QDebug>

#include "PublicConfig.h"
#include "PublicParams.h"

CProjectDB *CProjectDB::m_spInatance = nullptr;

CProjectDB *CProjectDB::GetInstance()
{
    if(nullptr == m_spInatance)
        m_spInatance = new CProjectDB;
    return m_spInatance;
}

CProjectDB::CProjectDB() : CSqliteDBBase(CPublicConfig::GetInstance()->GetProjectDBPath(), gk_strProjectDBConnect)
{
    _InitHistroyTable();
    _InitSampleTable();
    _InitCardTable();

    if(0)
    {
        //添加模拟数据
        for(int i=0; i<100000; i++)
        {
            QDateTime dateTime = QDateTime::currentDateTime().addSecs(-(6000000-i*60));
            QString strTestTime = dateTime.toString("yyyy-MM-dd hh:mm:ss");

            SResultInfoStruct sResult;
            sResult.strSampleID = "S" + QString(strTestTime).remove(" ").remove("-").remove(":");
            sResult.strCardID = "C" + QString(strTestTime).remove(" ").remove("-").remove(":");
            sResult.strProjectName = "2019-nCoV/FluA/FluB/RSV";
            sResult.strTestTime = strTestTime;
            sResult.strResult = "P,E,P,P;P,E,N,N;";
            sResult.strCTValue = "30,-1,22,25;31,-1,-1,-1;";
            sResult.strOperator = "Admin";
            sResult.strMode = "T";
            sResult.iTestProject = 0;
            sResult.strTmValue = "26.8,26.8,26.8;26.8,26.8,26.8;26.8,26.8,26.8;26.8,26.8,26.8;26.8,26.8,26.8;26.8,26.8,26.8;26.8,26.8,26.8;26.8,26.8,26.8;";//得拆开一列保存tm，一列保存rm;
            sResult.strHrmResult = "P,E,P,P;P,E,N,N;";
            sResult.iStatus = eTestDone;
            sResult.iMachineID = qrand() % 8;
            AddHistoryData(sResult);

            SSampleInfoStruct sSample;
            sSample.strSampleID = sResult.strSampleID;
            sSample.strTestTime = sResult.strTestTime;
            sSample.strSamplingDate = dateTime.toString("yyyy-MM-dd");
            sSample.strGender = "M";
            sSample.strBirthday = dateTime.addYears(-(qrand() % 20)).toString("yyyy-MM-dd");
            sSample.strAge = "20 Y";
            sSample.strTelephone = sResult.strTestTime;
            AddSampleData(sSample);

            SCardInfoStruct sCard;
            sCard.strCardID = sResult.strCardID;
            sCard.strTestTime = sResult.strTestTime;
            sCard.strCardLot = QUuid::createUuid().toString();
            sCard.strProject = sResult.strProjectName;
            sCard.strCardMFG = "2024-5-11";
            sCard.strCardEXP = "2025-5-11";
            AddCardData(sCard);
        }
    }
}

CProjectDB::~CProjectDB()
{

}

bool CProjectDB::GetHistoryIDByTestTime(const QString &strTestTime, int &iHistoryID)
{
    QString strCmd = QString("select id from history where TestTime = '%1'").arg(strTestTime);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    iHistoryID = _GetFirstValue(strList).toInt();
    return true;
}

/*******************************************************************
* @brief: 读取所有历史ID
* @param:
* @return:
* @author: hongxirong
*******************************************************************/
bool CProjectDB::GetAllHistoryID(QList<int> &iIDList)
{
    QString strAdd;
    QString strUser = CPublicConfig::GetInstance()->GetLoginUser();
    if(gk_strAdminUserName == strUser)
        strAdd = "where Operator not in ('factory', 'maintian', 'FLY')";
    else if(gk_strFactoryUserName == strUser)
        strAdd.clear();
    else if(gk_strFlyUserName == strUser)
        strAdd.clear();
    else if(gk_strMaintianUserName == strUser)
        strAdd.clear();
    else
        strAdd = QString("where Operator = '%1'").arg(strUser);

    QString strCmd = QString("select id from history ") + strAdd;
    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    for(int i=0; i<strList.size(); i++)
    {
        if(!strList.at(i).isEmpty())
            iIDList.push_back(strList.at(i).at(0).toInt());
    }
    return true;
}

/*******************************************************************
* @brief: history表 list 2 struct
* @param:
* @return:
* @author: hongxirong
*******************************************************************/
void CProjectDB::_HistoryList2Struct(const QStringList &strList, SResultInfoStruct &sResult)
{
    if(strList.size() < 24)
        return;

    sResult.iHistoryID = strList.at(0).toInt();
    sResult.strSampleID = strList.at(1);
    sResult.strCardID = strList.at(2);
    sResult.strProjectName = strList.at(3);
    sResult.strTestTime = strList.at(4);
    sResult.strResult = strList.at(5);
    sResult.strResult_Review = strList.at(6);
    sResult.strCTValue = strList.at(7);
    sResult.strCTValue_Review = strList.at(8);
    sResult.strCTInfo = strList.at(9);
    sResult.strCTInfo_Review = strList.at(10);
    sResult.strHrmResult = strList.at(11);
    sResult.strHrmResult_Review = strList.at(12);
    sResult.strTmValue = strList.at(13);
    sResult.strRmValue = strList.at(14);
    sResult.strTmValue_Review = strList.at(15);
    sResult.strMeltingInfo = strList.at(16);
    sResult.strMeltingInfo_Review = strList.at(17);
    sResult.iTestProject = strList.at(18).toInt();
    sResult.strOperator = strList.at(19);
    sResult.strMode = strList.at(20);
    sResult.iStatus = strList.at(21).toInt();
    sResult.strReview = strList.at(22);
    sResult.iMachineID = strList.at(23).toInt();
}

/*******************************************************************
* @brief: sample表 list 2 struct
* @param:
* @return:
* @author: hongxirong
* 20250414 hxr 根据项目确定样本类型
*******************************************************************/
void CProjectDB::_SampleList2Struct(const QString &strProject, const QStringList &strList, SSampleInfoStruct &sSample)
{
    if(strList.size() < 9)
        return;

    //int iSampleID = strList.at(0); //表格id
    sSample.strSampleID = strList.at(1);
    sSample.strTestTime = strList.at(2);
    sSample.iSampleType = strList.at(3).toInt();
    sSample.strSampleType = CPublicConfig::GetInstance()->GetSampleTypeString(sSample.iSampleType);
    sSample.strSamplingDate = strList.at(4);
    sSample.strName = strList.at(5);
    sSample.strGender = CPublicConfig::GetInstance()->GetGenderShowString(strList.at(6));
    sSample.strBirthday = strList.at(7);
    sSample.strAge = CPublicConfig::GetInstance()->GetAgeShowString(strList.at(8));
    sSample.strTelephone = strList.at(9);
}

/*******************************************************************
* @brief: card表 list 2 struct
* @param:
* @return:
* @author: hongxirong
*******************************************************************/
void CProjectDB::_CardList2Struct(const QStringList &strList, SCardInfoStruct &sCard)
{
    if(strList.size() < 6)
        return;
    //int iCardID = strList.at(0); //表格id
    sCard.strCardID = strList.at(1);
    sCard.strTestTime = strList.at(2);
    sCard.strCardLot = strList.at(3);
    sCard.strCardMFG = strList.at(4);
    sCard.strCardEXP = strList.at(5);
}

int CProjectDB::GetHistoryAllDataCount()
{
    QString strAdd;
    QString strUser = CPublicConfig::GetInstance()->GetLoginUser();
    if(gk_strAdminUserName == strUser)
        strAdd = "where Operator not in ('factory', 'maintian', 'FLY')";
    else if(gk_strFactoryUserName == strUser)
        strAdd.clear();
    else if(gk_strFlyUserName == strUser)
        strAdd.clear();
    else if(gk_strMaintianUserName == strUser)
        strAdd.clear();
    else
        strAdd = QString("where Operator = '%1'").arg(strUser);

    QString strCmd = "select count(*) from history " + strAdd;
    QList<QStringList> strList;
    if(_QueryDB(strCmd, strList))
        return _GetFirstValue(strList).toInt();
    return 0;
}

/*******************************************************************
* @brief: 添加历史数据
* @param: STestResultStruct
* @return: int 返回插入后此行的ID
* @author: hongxirong
*
**
*******************************************************************/
int CProjectDB::AddHistoryData(const SResultInfoStruct &sResult)
{
    QString strCmd = QString("insert into history (SampleID, CardID, ProjectName, TestTime, "
                             "Result, Result_Review, CTValue, CTValue_Review,CTInfo,CTInfoReview, "
                             "MeltingResult, MeltingResult_Review, MeltingTmValue, MeltingRmValue, MeltingTmValue_Review,MeltingInfo,MeltingInfoReview ,TestProject,"
                             "Operator, Mode, Status, Review, MachineID) "
                             "values ('%1', '%2', '%3', '%4', "
                             "'%5', '%6', '%7', '%8','%9','%10', "
                             "'%11', '%12', '%13','%14', '%15', '%16','%17','%18',"
                             "'%19', '%20', '%21', '%22', '%23')")
            .arg(sResult.strSampleID).arg(sResult.strCardID).arg(sResult.strProjectName).arg(sResult.strTestTime)
            .arg(sResult.strResult).arg(sResult.strResult_Review)
            .arg(sResult.strCTValue).arg(sResult.strCTValue_Review)
            .arg(sResult.strCTInfo).arg(sResult.strCTInfo_Review)
            .arg(sResult.strHrmResult).arg(sResult.strHrmResult_Review)
            .arg(sResult.strTmValue).arg(sResult.strRmValue).arg(sResult.strTmValue_Review).arg(sResult.strMeltingInfo).arg(sResult.strMeltingInfo_Review).arg(sResult.iTestProject)
            .arg(sResult.strOperator).arg(sResult.strMode).arg(sResult.iStatus)
            .arg(sResult.strReview).arg(sResult.iMachineID);
    if(!_ExecuteDB(strCmd))
        return -1;

    strCmd = "select last_insert_rowid() from history";
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    int iHistoryID = _GetFirstValue(strList).toInt();
    emit SignalAddHistoryID(iHistoryID);
    emit SignalRefreshHistory();
    return iHistoryID;
}

bool CProjectDB::DeleteHistoryData(int iHistoryID)
{
    QString strCmd = QString("delete from history where id = %1").arg(iHistoryID);
    bool bDel = _ExecuteDB(strCmd);
    if(bDel)
        emit SignalDelHistoryID(iHistoryID);
    emit SignalRefreshHistory();
    return bDel;
}

bool CProjectDB::UpdateHistroyTestStatus(int iHistoryID, int iStatus)
{
    bool bResult = false;
    QString strCmd = QString("update history set Status='%1' where id=%2").arg(iStatus).arg(iHistoryID);
    bResult = _ExecuteDB(strCmd);
    emit SignalRefreshHistory();
    return bResult;
}

/*******************************************************************
* @brief: 更新历史数据,测试后更新: 结果; CT; 状态.
* @param: STestResultStruct
* @return: bool
* @author: hongxirong
*******************************************************************/
bool CProjectDB::UpdateHistroyTestData(const SResultInfoStruct &sResult)
{
    bool bResult = false;
    QString strCmd = QString("update history set Result='%1', CTValue='%2',CTInfo = '%3',CTInfoReview = '%4', Status='%5' where id = %6")
            .arg(sResult.strResult).arg(sResult.strCTValue).arg(sResult.strCTInfo).arg(sResult.strCTInfo_Review).arg(sResult.iStatus).arg(sResult.iHistoryID);
    bResult = _ExecuteDB(strCmd);
    emit SignalRefreshHistory();
    return bResult;
}

/*******************************************************************
* @brief: 更新审核数据
* @param: STestResultStruct Result_Review; CTValue_Review
* @return: bool
* @author: hongxirong
*******************************************************************/
bool CProjectDB::UpdateHistoryReviewData(const SResultInfoStruct &sResult)
{
    bool bResult = false;
    QString strCmd = QString("update history set Result_Review='%1', CTValue_Review='%2', CTInfoReview = '%3', MeltingResult_Review = '%4',MeltingTmValue_Review = '%5', MeltingInfoReview = '%6', Review = '%7' where id = %8")
            .arg(sResult.strResult_Review).arg(sResult.strCTValue_Review).arg(sResult.strCTInfo_Review).arg(sResult.strHrmResult_Review)
            .arg(sResult.strTmValue_Review).arg(sResult.strMeltingInfo_Review).arg(sResult.strReview).arg(sResult.iHistoryID);
    bResult = _ExecuteDB(strCmd);
    emit SignalRefreshHistory();
    return bResult;
}
/*******************************************************************
* @brief: 更新历史数据,测试后更新: 结果; CT; 状态.
* @param: STestResultStruct
* @return: bool
* @author: hongxirong
*
***
*******************************************************************/
bool CProjectDB::UpdateHistroyMeltingTestData(const SResultInfoStruct &sResult)
{
     bool bResult = false;

    QString strCmd = QString("update history set MeltingResult='%1', MeltingTmValue='%2',MeltingRmValue='%3',MeltingInfo = '%4', MeltingInfoReview = '%5', Status='%6' where id = %7")
            .arg(sResult.strHrmResult).arg(sResult.strTmValue).arg(sResult.strRmValue).arg(sResult.strMeltingInfo).arg(sResult.strMeltingInfo_Review).arg(sResult.iStatus).arg(sResult.iHistoryID);
    bResult = _ExecuteDB(strCmd);
    emit SignalRefreshHistory();
    return bResult;
}
/*******************************************************************
* @brief: 获取历史数据
* @param: iHistoryID
* @return:
* @author: hongxirong
*******************************************************************/
bool CProjectDB::GetHistoryData(int iHistoryID, SResultInfoStruct &sResult)
{
    QString strCmd = QString("select * from history where id = %1").arg(iHistoryID);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    QStringList strRowList = _GetRowValueList(strList);
    _HistoryList2Struct(strRowList, sResult);
    return true;
}

bool CProjectDB::GetHistoryData(const QString &strCardID, const QString &strTestTime, SResultInfoStruct &sResult)
{
    if(strCardID.isEmpty() || strTestTime.isEmpty())
        return false;

    QString strCmd = QString("select * from history indexed by idx_cardIdTestTime where CardID = '%1' and TestTime = '%2'")
            .arg(strCardID).arg(strTestTime);

    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    QStringList strRowList = _GetRowValueList(strList);
    _HistoryList2Struct(strRowList, sResult);
    return true;
}

/*******************************************************************
* @brief: 按页查找
* @param: limit iPage * iOffset, iOffset
* @return:
* @author: hongxirong
*******************************************************************/
bool CProjectDB::GetHistoryPageData(int iPage, int iOffSet, QList<SResultInfoStruct> &sResultList)
{
    QString strAdd;
    QString strUser = CPublicConfig::GetInstance()->GetLoginUser();
    if(gk_strAdminUserName == strUser)
        strAdd = "where Operator not in ('factory', 'maintian', 'FLY')";
    else if(gk_strFactoryUserName == strUser)
        strAdd.clear();
    else if(gk_strFlyUserName == strUser)
        strAdd.clear();
    else if(gk_strMaintianUserName == strUser)
        strAdd.clear();
    else
        strAdd = QString("where Operator = '%1'").arg(strUser);

    QString strCmd = QString("select * from history %3 order by id desc limit %1,%2")
            .arg(iPage * iOffSet).arg(iOffSet).arg(strAdd);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    sResultList.clear();
    for(int i=0; i<strList.size(); i++)
    {
        SResultInfoStruct s;
        _HistoryList2Struct(strList.at(i), s);
        sResultList.push_back(s);
    }
    return true;
}

bool CProjectDB::IsCardIDUsed(const QString &strCardID)
{
    QString strCmd = QString("select * from history where CardID = '%1'").arg(strCardID);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    if(strList.isEmpty())
        return false;
    else
        return true;
}

bool CProjectDB::GetHistorySearchID(const SHistroySearchStruct &sSearchStruct, QList<int> &iIDList)
{
    QString strCmd = _GetSearchCmd(sSearchStruct, "id");
    strCmd += " order by id desc";
    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    for(int i=0; i<strList.size(); i++)
    {
        if(!strList.at(i).isEmpty())
            iIDList.push_back(strList.at(i).at(0).toInt());
    }
    return true;
}

int CProjectDB::GetHistorySearchCount(const SHistroySearchStruct &sSearchStruct)
{
    QString strCmd = _GetSearchCmd(sSearchStruct, "count(*)");
    QList<QStringList> strList;
    if(_QueryDB(strCmd, strList))
        return _GetFirstValue(strList).toInt();
    return 0;
}

bool CProjectDB::GetHistorySearchPageData(const SHistroySearchStruct &sSearchStruct,
                                          int iPage, int iOffSet, QList<SResultInfoStruct> &sResultList)
{
    QString strCmd = _GetSearchCmd(sSearchStruct, "*");
    strCmd += QString(" order by id desc limit %1,%2").arg(iPage * iOffSet).arg(iOffSet);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    sResultList.clear();
    for(int i=0; i<strList.size(); i++)
    {
        SResultInfoStruct s;
        _HistoryList2Struct(strList.at(i), s);
        sResultList.push_back(s);
    }
    return true;
}

QString CProjectDB::_GetSearchCmd(const SHistroySearchStruct &sSearchStruct, const QString &strSearch)
{
    bool bFindSample = false;
    QString strFindSample = "select SampleID from sample where ";
    if(!sSearchStruct.strSampleID.isEmpty())
    {
        bFindSample = true;
        strFindSample += QString("SampleID = '%1'").arg(sSearchStruct.strSampleID);
    }
    if(!sSearchStruct.strName.isEmpty())
    {
        if(bFindSample)
            strFindSample += " and ";
        bFindSample = true;
        strFindSample += QString("Name = '%1'").arg(sSearchStruct.strName);
    }
    if(!sSearchStruct.strTelephone.isEmpty())
    {
        if(bFindSample)
            strFindSample += " and ";
        bFindSample = true;
        strFindSample += QString("Telephone = '%1'").arg(sSearchStruct.strTelephone);
    }
    if(!sSearchStruct.strGender.isEmpty())
    {
        if(bFindSample)
            strFindSample += " and ";
        bFindSample = true;
        strFindSample += QString("Gender = '%1'").arg(sSearchStruct.strGender);
    }
    if(!sSearchStruct.strAge.isEmpty())
    {
        if(bFindSample)
            strFindSample += " and ";
        bFindSample = true;
        strFindSample += QString("Age = '%1'").arg(sSearchStruct.strAge);
    }
    if(!sSearchStruct.strBirthday.isEmpty())
    {
        if(bFindSample)
            strFindSample += " and ";
        bFindSample = true;
        strFindSample += QString("Birthday = '%1'").arg(sSearchStruct.strBirthday);
    }
    if(!sSearchStruct.strSampleType.isEmpty())
    {
        if(bFindSample)
            strFindSample += " and ";
        bFindSample = true;
        strFindSample += QString("SampleType = '%1'").arg(sSearchStruct.strSampleType);
    }

    bool bFindHistory = false;
    QString strFindHistory = QString("select %1 from history where ").arg(strSearch);

    QString strAdd;
    QString strUser = CPublicConfig::GetInstance()->GetLoginUser();
    if(gk_strAdminUserName == strUser)
    {
        bFindHistory = true;
        strAdd = "Operator != 'factory'";
    }
    else if(gk_strFactoryUserName == strUser)
    {
        bFindHistory = false;
        strAdd.clear();
    }
    else
    {
        bFindHistory = true;
        strAdd = QString("Operator = '%1'").arg(strUser);
    }
    strFindHistory += strAdd;


    if(!sSearchStruct.strStartDate.isEmpty() || !sSearchStruct.strEndDate.isEmpty())
    {
        QString strStartDate = sSearchStruct.strStartDate;
        QString strEndDate = sSearchStruct.strEndDate;
        if(strStartDate.isEmpty())
            strStartDate = "1970-01-01";
        if(strEndDate.isEmpty())
            strEndDate = QDate::currentDate().toString("yyyy-MM-dd");

        QString strMinDate = qMin(strStartDate, strEndDate);
        QString strMaxDate = qMax(strStartDate, strEndDate);
        strMinDate += " 00:00:00";
        strMaxDate += " 23:59:59";

        if(bFindHistory)
            strFindHistory += " and ";
        bFindHistory = true;
        strFindHistory += QString("TestTime between '%1' and '%2'").arg(strMinDate).arg(strMaxDate);
    }
    //    if(!sSearchStruct.strStartDate.isEmpty() && !sSearchStruct.strEndDate.isEmpty())
    //    {
    //        bFindHistory = true;
    //        strFindHistory += QString("TestTime > datetime('%1') and TestTime < date('%2')")
    //                .arg(sSearchStruct.strStartDate).arg(sSearchStruct.strEndDate);
    //    }
    if(!sSearchStruct.strCardID.isEmpty())
    {
        if(bFindHistory)
            strFindHistory += " and ";
        bFindHistory = true;
        strFindHistory += QString("CardID = '%1'").arg(sSearchStruct.strCardID);
    }
    if(!sSearchStruct.strProject.isEmpty())
    {
        if(bFindHistory)
            strFindHistory += " and ";
        bFindHistory = true;
        strFindHistory += QString("ProjectName = '%1'").arg(sSearchStruct.strProject);
    }
    if(!sSearchStruct.strTestType.isEmpty())
    {
        if(bFindHistory)
            strFindHistory += " and ";
        bFindHistory = true;

        //strFindHistory += QString("Mode = '%1'").arg(sSearchStruct.strTestType);
        //20250408 普通测试:T 质控测试:PQC NQC
        if("T" == sSearchStruct.strTestType)
            strFindHistory += QString("Mode = 'T'");
        else if("Q" == sSearchStruct.strTestType)
            strFindHistory += QString("Mode != 'T'");
    }

    if(bFindSample)
    {
        if(bFindHistory)
            strFindHistory += " and ";
        bFindHistory = true;
        strFindHistory += QString("SampleID in (%1)").arg(strFindSample);
    }
    return strFindHistory;
}

bool CProjectDB::AddSampleData(const SSampleInfoStruct &sSample)
{
    QString strDBGender = CPublicConfig::GetInstance()->GetGenderDBString(sSample.strGender);
    QString strDBAge = CPublicConfig::GetInstance()->GetAgeDBString(sSample.strAge);

    QString strCmd = QString("insert into sample (SampleID, TestTime, SampleType, SamplingDate, Name, "
                             "Gender, Birthday, Age, Telephone) "
                             "values ('%1', '%2', '%3', '%4', '%5', '%6', '%7', '%8', '%9')")
            .arg(sSample.strSampleID).arg(sSample.strTestTime).arg(sSample.iSampleType)
            .arg(sSample.strSamplingDate).arg(sSample.strName).arg(strDBGender)
            .arg(sSample.strBirthday).arg(strDBAge).arg(sSample.strTelephone);
    return _ExecuteDB(strCmd);
}

bool CProjectDB::GetSampleData(const QString &strProject, const QString &strSampleID, const QString &strTestTime,
                               SSampleInfoStruct &sSample)
{
    if(strSampleID.isEmpty() || strTestTime.isEmpty())
        return false;

    QString strCmd = QString("select * from sample where SampleID = '%1' and TestTime = '%2'")
            .arg(strSampleID).arg(strTestTime);

    strCmd = QString("select * from sample indexed by idx_sampleidtime where SampleID = '%1' and TestTime = '%2'")
            .arg(strSampleID).arg(strTestTime);

    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    QStringList strRowList = _GetRowValueList(strList);
    _SampleList2Struct(strProject, strRowList, sSample);
    return true;
}


bool CProjectDB::AddCardData(const SCardInfoStruct &sCard)
{
    QString strCmd = QString("insert into card (CardID, TestTime, CardLot, CardMFG, CardEXP) "
                             "values ('%1', '%2', '%3', '%4', '%5')")
            .arg(sCard.strCardID).arg(sCard.strTestTime).arg(sCard.strCardLot)
            .arg(sCard.strCardMFG).arg(sCard.strCardEXP);
    return _ExecuteDB(strCmd);
}


bool CProjectDB::GetCardData(const QString &strCardID, const QString &strTestTime, SCardInfoStruct &sCard)
{
    if(strCardID.isEmpty() || strTestTime.isEmpty())
        return false;

    QString strCmd = QString("select * from card where CardID = '%1' and TestTime = '%2'")
            .arg(strCardID).arg(strTestTime);

    strCmd = QString("select * from card indexed by idx_cardidtime where CardID = '%1' and TestTime = '%2'")
            .arg(strCardID).arg(strTestTime);

    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    QStringList strRowList = _GetRowValueList(strList);
    _CardList2Struct(strRowList, sCard);
    return true;
}

bool CProjectDB::IsCardUsed(const QString &strCardID, const QString &strCardLot)
{
    QString strCmd = QString("select id from card where CardID = '%1' and CardLot = '%2'").arg(strCardID).arg(strCardLot);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return strList.isEmpty() ? false : true;
}

bool CProjectDB::ClearDB()
{
    _DeleteTable("history");
    _DeleteTable("sample");
    _DeleteTable("card");

    emit SignalRefreshHistory();

    return true;
}

void CProjectDB::_InitHistroyTable()
{
    QString strCmd = "create table if not exists history ("
                     "id integer not null primary key autoincrement,"
                     "SampleID varchar,"
                     "CardID varchar,"
                     "ProjectName varchar,"
                     "TestTime varchar,"
                     "Result varchar,"
                     "Result_Review varchar,"
                     "CTValue varchar,"
                     "CTValue_Review varchar,"
                     "CTInfo varchar,"
                     "CTInfoReview varchar,"
                     "MeltingResult varchar,"
                     "MeltingResult_Review varchar,"
                     "MeltingTmValue varchar,"
                     "MeltingRmValue varchar,"
                     "MeltingTmValue_Review varchar,"
                     "MeltingInfo varchar,"
                     "MeltingInfoReview varchar,"
                     "TestProject varchar,"
                     "Operator varchar,"
                     "Mode varchar,"
                     "Status varchar,"
                     "Review varchar,"
                     "MachineID varchar)";
    _ExecuteDB(strCmd);
    QString strIndex = "create index idx_cardIdTestTime on history (CardID, TestTime)";
    _ExecuteDB(strIndex);

}

void CProjectDB::_InitSampleTable()
{
    QString strCmd = "create table if not exists sample ("
                     "id integer not null primary key autoincrement,"
                     "SampleID varchar,"
                     "TestTime varchar,"
                     "SampleType varchar,"
                     "SamplingDate varchar,"
                     "Name varchar,"
                     "Gender varchar,"
                     "Birthday varchar,"
                     "Age varchar,"
                     "Telephone varchar)";
    _ExecuteDB(strCmd);

    QString strIndex = "create index idx_sampleidtime on sample (SampleID, TestTime)";
    _ExecuteDB(strIndex);
}

void CProjectDB::_InitCardTable()
{
    QString strCmd = "create table if not exists card ("
                     "id integer not null primary key autoincrement,"
                     "CardID varchar,"
                     "TestTime varchar,"
                     "CardLot varchar,"
                     "CardMFG varchar,"
                     "CardEXP varchar)";
    _ExecuteDB(strCmd);

    QString strIndex = "create index idx_cardidtime on card (CardID, TestTime)";
    _ExecuteDB(strIndex);

    //SELECT * FROM sqlite_master WHERE type = 'index';
}
