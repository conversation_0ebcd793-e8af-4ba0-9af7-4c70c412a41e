#include "CHistoryPage.h"
#include <QDateTime>
#include <QBoxLayout>

#include "CLogDB.h"
#include "CProjectDB.h"
#include "CMessageBox.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "CPrintThread.h"
#include "CLotInfoDB.h"
#include "CHL7MsgThread.h"
#include "CLisTcpClient.h"

CHistoryPage::CHistoryPage(QWidget *parent)
    : QWidget(parent)
    , m_bAutoReflash(true)
    , m_bLising(false)
    , m_bExporting(false)
    , m_bPrinting(false)
    , m_bHasQuery(false)
    , m_iOnePageLines(5)
    , m_iSelectedSize(0)
    , m_strTipText(tr("提示"))
{
    m_iBtnWidth = 144;
    m_iCancalBtnWidth = 144;
    m_iGotoBtnWidth = 72;
    if(eLanguage_German == gk_iLanguage)
    {
        m_iBtnWidth = 155;
        m_iCancalBtnWidth = 155;
        m_iGotoBtnWidth = 100;
    }

    _InitWidget();
    _InitLayout();

    m_pCHistorySearchWidget = new CHistorySearchWidget(this);
    connect(m_pCHistorySearchWidget, &CHistorySearchWidget::SignalSearchConfirm, this, &CHistoryPage::SlotSearchConfirm);
    m_pCHistorySearchWidget->setVisible(false);

    m_pCHistoryDetailWidget = new CHistoryDetailWidget(tr("历史详情"), true); //详情全屏显示

    LoadQSS(this, ":/qss/qss/history/history.qss");

    _ShowAll();

    //connect(CProjectDB::GetInstance(), &CProjectDB::SignalAddHistoryID, this, &CHistoryPage::SlotAddHistoryID);
    //connect(CProjectDB::GetInstance(), &CProjectDB::SignalDelHistoryID, this, &CHistoryPage::SlotDelHistoryID);
    //connect(CProjectDB::GetInstance(), &CProjectDB::SignalUpdateHistoryID, this, &CHistoryPage::SlotUpdateItemByID);
    connect(CProjectDB::GetInstance(), &CProjectDB::SignalRefreshHistory, this, &CHistoryPage::SlotRefreshHistory);

    connect(&CHL7MsgThread::GetInstace(), &CHL7MsgThread::SignalUploadError, this, &CHistoryPage::SlotLisUploadError);
    connect(&CHL7MsgThread::GetInstace(), &CHL7MsgThread::SignalUploadLeftNum, this, &CHistoryPage::SlotLisUploadLeftNum);

    connect(CPrintThread::GetInstance(), &CPrintThread::SignalPrintLeftNum, this, &CHistoryPage::SlotPrintLeftNum);

    connect(this, &CHistoryPage::SignalExportProgress, this, &CHistoryPage::_SlotExportProgress);
    connect(this, &CHistoryPage::SignalExportError, this, &CHistoryPage::_SlotExportError);
    connect(this, &CHistoryPage::SignalExportEnd, this, &CHistoryPage::_SlotExportEnd);

    connect(this, &CHistoryPage::SignalDeleteProgress, this, &CHistoryPage::_SlotDeleteProgress);
    connect(this, &CHistoryPage::SignalDeleteEnd, this, &CHistoryPage::_SlotDeleteEnd);

    m_pProgressBar = new CProgressBar(tr("提示"), tr("进度")); //删除全屏显示

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalAppStartEnd, this, &CHistoryPage::SlotAppStartEnd);
}

void CHistoryPage::SlotAppStartEnd()
{
    m_pCHistoryDetailWidget->setParent((QWidget*)gk_pMainWindow);
    m_pCHistoryDetailWidget->setVisible(false);
   // qDebug()<<Q_FUNC_INFO<<"1111111111111111111111111111111111111"<<m_pCHistoryDetailWidget->parent()->objectName();
}

void CHistoryPage::SlotRefreshHistory()
{
    m_bAutoReflash = true;

    if(this->isVisible())
    {
        _RefreshHistoryHandle();
    }
}

void CHistoryPage::SlotUpdateItemByID(int iHistoryID)
{
    for(int i=0; i<m_pListWidget->count(); i++)
    {
        QListWidgetItem *pItem = m_pListWidget->item(i);
        if(nullptr == pItem)
            continue;

        CHistoryItemWidget *pWidget = dynamic_cast<CHistoryItemWidget *>(m_pListWidget->itemWidget(pItem));
        if(nullptr != pWidget)
        {
            if(iHistoryID == pWidget->GetHistoryID())
            {
                qDebug()<<"历史更新,iHistoryID:"<<iHistoryID;
                pWidget->SetHistoryID(iHistoryID);
                break;
            }
        }
    }
}

void CHistoryPage::SlotAddHistoryID(int iHistoryID)
{
    m_bAutoReflash = true;
    //m_iTotalLines++;
    //m_iHistoryIDMap.insert(iHistoryID, m_pSelectAllCheckBox->isChecked());
}

void CHistoryPage::SlotDelHistoryID(int iHistoryID)
{
    m_bAutoReflash = true;
    //m_iTotalLines--;
    //m_iHistoryIDMap.remove(iHistoryID);
}

void CHistoryPage::SlotSearchConfirm(const SHistroySearchStruct &sSearchStruct)
{
    if(sSearchStruct.strStartDate.isEmpty() && sSearchStruct.strEndDate.isEmpty()
            && sSearchStruct.strSampleID.isEmpty() && sSearchStruct.strCardID.isEmpty()
            && sSearchStruct.strName.isEmpty() && sSearchStruct.strTelephone.isEmpty()
            && sSearchStruct.strGender.isEmpty() && sSearchStruct.strAge.isEmpty()
            && sSearchStruct.strBirthday.isEmpty() && sSearchStruct.strProject.isEmpty()
            && sSearchStruct.strTestType.isEmpty() &&sSearchStruct.strSampleType.isEmpty())
    {
        qDebug()<<"查询条件为空,不进行查找";
        return;
    }

    m_bHasQuery = true;
    m_sSearchStruct = sSearchStruct;
    m_iCurrentPage = 0;
    m_iSelectedSize = 0;
    m_pSelectAllCheckBox->setChecked(false);
    _ReGetDataPageLines();
    _UpdateSelectedInfo();
    _UpdatePageBtn();
    _ShowCurrentPageQueryData();
}

void CHistoryPage::SlotPrintLeftNum(int iLeftNum)
{
    if(!m_bPrinting)
        return;

    if(iLeftNum < 0)
    {
        m_bPrinting = false;
        m_pPrintInfoLabel->clear();
        m_pPrintBtn->setText(tr("打印"));
        m_pPrintBtn->setFixedSize(m_iBtnWidth, 56);
        ShowSuccess(this, m_strTipText, tr("打印完成"));
        return;
    }

    int iAll = m_pPrintInfoLabel->property("num").toInt();
    QString strInfo = QString("%1/%2").arg(iAll - iLeftNum).arg(iAll);
    m_pPrintInfoLabel->setText(tr("正在打印 %1 条").arg(strInfo));
}

void CHistoryPage::SlotLisUploadLeftNum(int iLeftNum)
{
    if(!m_bLising)
        return;

    if(iLeftNum < 0)
    {
        m_bLising = false;
        m_pLisInfoLabel->clear();
        m_pLisBtn->setText(tr("LIS"));
        ShowSuccess(this, m_strTipText, tr("LIS上传完成"));
        return;
    }

    int iAll = m_pLisInfoLabel->property("num").toInt();
    QString strInfo = QString("%1/%2").arg(iAll - iLeftNum).arg(iAll);
    m_pLisInfoLabel->setText(tr("LIS正在上传 %1 条").arg(strInfo));
}

void CHistoryPage::SlotLisUploadError(QString strError)
{
    if(!m_bLising)
        return;

    m_bLising = false;
    m_pLisInfoLabel->clear();
    m_pLisBtn->setText(tr("LIS"));

    ShowWarning(this, m_strTipText, strError);
}

void CHistoryPage::showEvent(QShowEvent *pEvent)
{
    int iUserLevel = CPublicConfig::GetInstance()->GetLoginLevel();
    if(iUserLevel <= eUser_Admin)
        m_pDeleteBtn->setVisible(false);
    else
        m_pDeleteBtn->setVisible(true);

    QString strUser = CPublicConfig::GetInstance()->GetLoginUser();
    if(m_strCurrentUser != strUser)
    {
        m_strCurrentUser = strUser;
        m_bAutoReflash = true;

        m_bHasQuery = false;
        m_sSearchStruct.Clear();
        m_iCurrentPage = 0;
        m_iSelectedSize = 0;
        m_pSelectAllCheckBox->setChecked(false);
    }

    if(m_bAutoReflash)
    {
        _RefreshHistoryHandle();
    }

    QWidget::showEvent(pEvent);
}

void CHistoryPage::hideEvent(QHideEvent *pEvent)
{
    QWidget::hideEvent(pEvent);
}

void CHistoryPage::_SlotSelectAllCheckedBox()
{
    if(m_pSelectAllCheckBox->isChecked())
    {
        for(auto it=m_iHistoryIDMap.constBegin(); it!= m_iHistoryIDMap.constEnd(); it++)
            m_iHistoryIDMap[it.key()] = true;

        m_iSelectedSize = m_iTotalLines;
    }
    else
    {
        for(auto it=m_iHistoryIDMap.constBegin(); it!= m_iHistoryIDMap.constEnd(); it++)
            m_iHistoryIDMap[it.key()] = false;

        m_iSelectedSize = 0;
    }

    for(int i=0; i<m_pListWidget->count(); i++)
    {
        QListWidgetItem *pItem = m_pListWidget->item(i);
        if(nullptr == pItem)
            continue;
        CHistoryItemWidget *pWidget = dynamic_cast<CHistoryItemWidget *>(m_pListWidget->itemWidget(pItem));
        if(nullptr != pWidget)
            pWidget->SetChecked(m_pSelectAllCheckBox->isChecked());
    }

    _UpdateSelectedInfo();
}

void CHistoryPage::_SlotItemChecked(int iHistoryID, bool bChecked)
{
    m_iHistoryIDMap[iHistoryID] = bChecked;

    if(!bChecked)
    {
        m_pSelectAllCheckBox->setChecked(false);
        m_iSelectedSize--;
        if(m_iSelectedSize <= 0)
            m_iSelectedSize = 0;
    }
    else
    {
        m_iSelectedSize++;
        if(m_iSelectedSize >= m_iHistoryIDMap.size())
            m_iSelectedSize = m_iHistoryIDMap.size();
    }

    _UpdateSelectedInfo();
}

void CHistoryPage::_SlotListWidgetItemClicked(QListWidgetItem *pItem)
{
    if(nullptr == pItem)
        return;

    CHistoryItemWidget *pWidget = (CHistoryItemWidget *)m_pListWidget->itemWidget(pItem);
    if(nullptr == pWidget)
        return;

    int iID = pWidget->GetHistoryID();
    bool bChecked = pWidget->GetChecked();
    pWidget->SetChecked(!bChecked);

    _SlotItemChecked(iID, !bChecked);
}

void CHistoryPage::_SlotListWidgetItemDoubleClicked(QListWidgetItem *pItem)
{
    if(nullptr == pItem)
        return;

    CHistoryItemWidget *pWidget = (CHistoryItemWidget *)m_pListWidget->itemWidget(pItem);
    if(nullptr == pWidget)
        return;

    int iID = pWidget->GetHistoryID();
    m_pCHistoryDetailWidget->SetHistoryID(iID);
    m_pCHistoryDetailWidget->raise();
    m_pCHistoryDetailWidget->show();
    m_pCHistoryDetailWidget->activateWindow();
}

void CHistoryPage::_SlotGotoPageBtn()
{
    QString strPage = m_pGotoLineEdit->text();
    if(strPage.isEmpty())
    {
        ShowInformation(this, tr("提示"), tr("请输入要跳转到的页数"));
        return;
    }

    int iPage = strPage.toInt();
    if(iPage <= 0)
        iPage = 1;
    if(iPage >= m_iTotalPages)
        iPage = m_iTotalPages;
    m_pGotoLineEdit->setText(QString::number(iPage));

    m_iCurrentPage = iPage - 1;
    if(m_bHasQuery)
        _ShowCurrentPageQueryData();
    else
        _ShowCurrentPageAllData();

    _UpdatePageBtn();
}

void CHistoryPage::_SlotPrePageBtn()
{
    if(m_iCurrentPage <= 0)
        return;

    m_pPrePageBtn->setEnabled(false);
    m_iCurrentPage--;
    if(m_bHasQuery)
        _ShowCurrentPageQueryData();
    else
        _ShowCurrentPageAllData();
    m_pPrePageBtn->setEnabled(true);

    _UpdatePageBtn();
}

void CHistoryPage::_SlotNextPageBtn()
{
    if(m_iCurrentPage + 1 >= m_iTotalPages)
        return;

    m_pNextPageBtn->setEnabled(false);
    m_iCurrentPage++;
    if(m_bHasQuery)
        _ShowCurrentPageQueryData();
    else
        _ShowCurrentPageAllData();
    m_pNextPageBtn->setEnabled(true);

    _UpdatePageBtn();
}

void CHistoryPage::_SlotDetailBtn()
{
    QList<int> iIDList = _GetSelectedHistoryID();
    if(iIDList.isEmpty())
    {
        ShowInformation(this, tr("查看详情"), tr("请先选择"));
        return;
    }

    m_pCHistoryDetailWidget->SetHistoryID(iIDList.last());
    m_pCHistoryDetailWidget->raise();
    m_pCHistoryDetailWidget->show();
    m_pCHistoryDetailWidget->activateWindow();
}

void CHistoryPage::_SlotQueryBtn()
{
    m_pCHistorySearchWidget->show();
}

void CHistoryPage::_SlotShowAllBtn()
{
    _ShowAll();
}

void CHistoryPage::_SlotLisBtn()
{
    if(!m_bLising)
    {
        if(false == CLisTcpClient::GetInstace().GetConnect())
        {
            ShowInformation(this, m_strTipText, tr("LIS未连接，请检查"));
            return;
        }

        QList<int> iIDList = _GetSelectedHistoryID();
        if(iIDList.isEmpty())
        {
            ShowInformation(this, tr("LIS"), tr("请先选择"));
            return;
        }

        int iBtnType = ShowQuestion(this, m_strTipText, tr("确定上传所选数据吗"));
        if(QMessageBox::Yes != iBtnType)
            return;

        m_bLising = true;
        m_pLisBtn->setText(tr("取消LIS"));
        m_pLisInfoLabel->setProperty("num", iIDList.size());
        CHL7MsgThread::GetInstace().AddHistoryIDList(iIDList);
        QString strInfo = QString("1/%1").arg(iIDList.size());
        m_pLisInfoLabel->setText(tr("LIS正在上传 %1 条").arg(strInfo));
        return;
    }
    else
    {
        int iBtnType = ShowQuestion(this, m_strTipText, tr("确定取消LIS上传吗"));
        if(QMessageBox::Yes != iBtnType)
            return;

        m_bLising = false;
        m_pLisBtn->setText(tr("LIS"));
        m_pLisInfoLabel->clear();
        CHL7MsgThread::GetInstace().CancelUpload();
    }
}

void CHistoryPage::_SlotExportBtn()
{
    if(!m_bExporting)
    {
        if(!UDiskExist(this))
            return;

        QList<int> iIDList = _GetSelectedHistoryID();
        if(iIDList.isEmpty())
        {
            ShowInformation(this, m_strTipText, tr("请先选择"));
            return;
        }

        int iBtnType = ShowQuestion(this, m_strTipText, tr("确定导出所选数据吗"));
        if(QMessageBox::Yes != iBtnType)
            return;

        QString strUser = CPublicConfig::GetInstance()->GetLoginUser();
        QString strLog = tr("导出%1条历史数据").arg(iIDList.size());
        CLogDB::instance().AddOperationLog(strUser, strLog, CLogDB::eExportLog);

        m_bExporting = true;
        m_pExportBtn->setText(tr("取消导出"));
        m_pExportBtn->setFixedSize(m_iCancalBtnWidth, 56);

        std::thread exportThread(&CHistoryPage::_Thread2Export, this, iIDList);
        exportThread.detach();

        QString strInfo = QString("1/%1").arg(iIDList.size());
        m_pExportInfoLabel->setText(tr("正在导出 %1 条").arg(strInfo));
    }
    else
    {
        int iBtnType = ShowQuestion(this, m_strTipText, tr("确定取消导出吗"));
        if(QMessageBox::Yes != iBtnType)
            return;

        QString strUser = CPublicConfig::GetInstance()->GetLoginUser();
        QString strLog = tr("取消导出历史数据");
        CLogDB::instance().AddOperationLog(strUser, strLog, CLogDB::eExportLog);

        m_bExporting = false;
        m_pExportBtn->setText(tr("导出"));
        m_pExportBtn->setFixedSize(m_iBtnWidth, 56);
        m_pExportInfoLabel->clear();
    }
}

//20250219 hxr admin下只导出pdf factory导出pdf xlsx csv
void CHistoryPage::_Thread2Export(QList<int> iHistoryIDList)
{
    //根据登录用户区分导出内容
    int iUseLevel = CPublicConfig::GetInstance()->GetLoginLevel();

    QString strCurrentTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    QString strExportDir = CPublicConfig::GetInstance()->GetUDiskExportDir();
    qDebug()<<"历史导出文件夹:"<<strExportDir;
    QDir qExportdir(strExportDir);
    CreateDir(strExportDir);

    //CSV
    QString strCsvName = QString("history_%1.csv").arg(strCurrentTime);
    QList<QByteArray> qCsvDataList;
    QByteArray qBOM;
    qBOM[0] = 0xEF;
    qBOM[1] = 0xBB;
    qBOM[2] = 0xBF;
    QString strTitle = tr("样本编号") + "," + tr("试剂卡编号") + "," + tr("项目") + ","
            + tr("测试时间") + "," + tr("测试结果") + "," + tr("操作者") + ","
            + tr("测试类型") + "," + tr("检测模块") + "," + tr("样本类型") + ","
            + tr("采样日期") + "," + tr("姓名") + "," + tr("性别") + ","
            + tr("生日") + "," + tr("年龄") + ", " + tr("电话") + ","
            + tr("试剂卡批次") + "," + tr("试剂卡生产日期") + "," + tr("试剂卡有效日期") + "\n";
    QByteArray qHead = qBOM + strTitle.toLocal8Bit();
    qCsvDataList << qHead;

    for(int iID=0; iID<iHistoryIDList.size(); iID++)
    {
        if(!m_bExporting)
        {
            break;
        }

        QDir qUDiskDir(GetUDiskDir());
        if(!qUDiskDir.exists())
        {
            m_bExporting = false;
            emit SignalExportError();
            break;
        }

        SResultInfoStruct sResultInfo;
        CProjectDB::GetInstance()->GetHistoryData(iHistoryIDList.at(iID), sResultInfo);

        QString strTestTime = sResultInfo.strTestTime;
        QString strSampleID = sResultInfo.strSampleID;
        QString strCardID = sResultInfo.strCardID;

        SSampleInfoStruct sSampleInfo;
        CProjectDB::GetInstance()->GetSampleData(sResultInfo.strProjectName, strSampleID, strTestTime, sSampleInfo);

        SCardInfoStruct sCardInfo;
        CProjectDB::GetInstance()->GetCardData(strCardID, strTestTime, sCardInfo);

        SLotInfoStruct sLotInfo;
        CLotInfoDB::GetInstance()->GetLotInfoByShowName(sResultInfo.strProjectName, sLotInfo);

        QStringList strOutInfoList = GetTargetOutputInfoList(sResultInfo, sLotInfo);
        QString strResult = strOutInfoList.join(" ");

        QString strMode = CPublicConfig::GetInstance()->GetTestTypeShowString(sResultInfo.strMode);
        QString strData = sResultInfo.strSampleID + "," + sResultInfo.strCardID + "," + sResultInfo.strProjectName + ","
                + sResultInfo.strTestTime + "," + strResult + "," + sResultInfo.strOperator + ","
                + strMode + "," + QString("%1#").arg(sResultInfo.iMachineID + 1) + "," + sSampleInfo.strSampleType + ","
                + sSampleInfo.strSamplingDate + "," + sSampleInfo.strName +  "," + sSampleInfo.strGender + ","
                + sSampleInfo.strBirthday + "," + sSampleInfo.strAge + "," + sSampleInfo.strTelephone + ","
                + sCardInfo.strCardLot + "," + sCardInfo.strCardMFG + "," + sCardInfo.strCardEXP + "\n";
        qCsvDataList << strData.toLocal8Bit();

        QString strDateTime = strTestTime;
        strDateTime = strDateTime.remove("-").remove(":").remove(" ");
        qDebug()<<Q_FUNC_INFO<<"export:"<<strDateTime;
        if(iUseLevel >= eUser_Maintain)
        {
            //XLSX
            QDir qXlsxDir(CPublicConfig::GetInstance()->GetXlsxDir());
            QFileInfoList qXlsxInfoList = qXlsxDir.entryInfoList(QDir::Files);
            for(int m=0; m<qXlsxInfoList.size(); m++)
            {
                QString strFilePath = qXlsxInfoList.at(m).absoluteFilePath();
                QString strBaseName = qXlsxInfoList.at(m).baseName();
                if(strBaseName.startsWith(strDateTime))
                    CopyQFileDir(strFilePath, qExportdir);
            }
        }

        //PDF
        QDir qPdfDir(CPublicConfig::GetInstance()->GetPdfDir());
        QFileInfoList qPdfInfoList = qPdfDir.entryInfoList(QDir::Files);
        for(int m=0; m<qPdfInfoList.size(); m++)
        {
            QString strFilePath = qPdfInfoList.at(m).absoluteFilePath();
            QString strBaseName = qPdfInfoList.at(m).baseName();
            if(strBaseName.startsWith(strDateTime))
                CopyQFileDir(strFilePath, qExportdir);
        }

        emit SignalExportProgress(QString("%1/%2").arg(iID + 1).arg(iHistoryIDList.size()));
    }

    if(iUseLevel >= eUser_Maintain)
    {
        WriteFile(strCsvName, qCsvDataList);
        MoveQFile(strCsvName, qExportdir);
    }

    emit SignalExportEnd();
}

void CHistoryPage::_SlotExportProgress(QString strProgress)
{
    if(!m_bExporting)
        return;

    m_pExportInfoLabel->setText(tr("正在导出 %1 条").arg(strProgress));
}

void CHistoryPage::_SlotExportError()
{
    ShowWarning(this, m_strTipText, tr("历史数据导出失败，请检查U盘"));

    m_bExporting = false;
    m_pExportInfoLabel->clear();
    m_pExportBtn->setText(tr("导出"));
    m_pExportBtn->setFixedSize(m_iBtnWidth, 56);
}

void CHistoryPage::_SlotExportEnd()
{
    if(!m_bExporting)
        return;

    m_bExporting = false;
    m_pExportInfoLabel->clear();
    m_pExportBtn->setText(tr("导出"));
    m_pExportBtn->setFixedSize(m_iBtnWidth, 56);
    ExportEndUmountUSB();

    ShowSuccess(this, m_strTipText, tr("导出完成"));
}

void CHistoryPage::_SlotDeleteBtn()
{
    QList<int> iIDList = _GetSelectedHistoryID();
    if(iIDList.isEmpty())
    {
        ShowInformation(this, m_strTipText, tr("请先选择"));
        return;
    }

    int iBtnType = ShowQuestion(this, m_strTipText, tr("数据删除不可恢复，确定删除所选数据吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    qDebug()<<"删除历史:"<<iIDList;

    if(iIDList.size() <= 3)
    {
        _Fun2Delete(iIDList);
        return;
    }

    m_pProgressBar->SetTitle(tr("删除"));
    m_pProgressBar->SetRange(0, iIDList.size());
    m_pProgressBar->SetValue(0);
    m_pProgressBar->show();

    std::thread delthread(&CHistoryPage::_Fun2Delete, this, iIDList);
    delthread.detach();
}

void CHistoryPage::_Fun2Delete(QList<int> iHistoryIDList)
{
    for(int i=0; i<iHistoryIDList.size(); i++)
    {
        int iID = iHistoryIDList.at(i);
        qDebug()<<"delete history id:"<<iID;
        CProjectDB::GetInstance()->DeleteHistoryData(iID);
        emit SignalDeleteProgress(i+1);
    }
    emit SignalDeleteEnd();
}

void CHistoryPage::_RefreshHistoryHandle()
{
    m_bAutoReflash = false;

    QTime t1 = QTime::currentTime();

    QMap<int, bool> iIDMap = m_iHistoryIDMap;
    _ReGetDataPageLines();
    for(auto it=iIDMap.constBegin(); it!=iIDMap.constEnd(); it++)
    {
        if(it.value())
        {
            if(m_iHistoryIDMap.contains(it.key()))
                m_iHistoryIDMap[it.key()] = true;
        }
    }

    _UpdateSelectedInfo();
    _UpdatePageBtn();

    if(m_bHasQuery)
        _ShowCurrentPageQueryData();
    else
        _ShowCurrentPageAllData();

    qDebug()<<Q_FUNC_INFO<<"#####################:"<<t1.msecsTo(QTime::currentTime());
}

void CHistoryPage::_SlotDeleteProgress(int index)
{
    m_pProgressBar->SetValue(index);
}

void CHistoryPage::_SlotDeleteEnd()
{
    m_pProgressBar->close();
    ShowSuccess(this, m_strTipText, tr("删除完成"));

    _ShowAll();
}

void CHistoryPage::_SlotPrintBtn()
{
    if(!CPrintThread::GetInstance()->GetPrinterStatus())
    {
        ShowInformation(this, m_strTipText, tr("打印机未连接"));
        return;
    }

    if(!m_bPrinting)
    {
        QList<int> iIDList = _GetSelectedHistoryID();
        if(iIDList.isEmpty())
        {
            ShowInformation(this, m_strTipText, tr("请先选择"));
            return;
        }

        int iBtnType = ShowQuestion(this, m_strTipText, tr("确定打印所选数据吗"));
        if(QMessageBox::Yes != iBtnType)
            return;

        m_bPrinting = true;
        m_pPrintBtn->setText(tr("取消打印"));
        m_pPrintBtn->setFixedSize(m_iCancalBtnWidth, 56);
        m_pPrintInfoLabel->setProperty("num", iIDList.size());

        for(int i=0; i<iIDList.size(); i++)
            CPrintThread::GetInstance()->AddPrintHistoryID(iIDList.at(i));

        QString strInfo = QString("1/%1").arg(iIDList.size());
        m_pPrintInfoLabel->setText(tr("正在打印 %1 条").arg(strInfo));
    }
    else
    {
        int iBtnType = ShowQuestion(this, m_strTipText, tr("确定取消打印吗"));
        if(QMessageBox::Yes != iBtnType)
            return;

        m_bPrinting = false;
        m_pPrintBtn->setText(tr("打印"));
        m_pPrintBtn->setFixedSize(m_iBtnWidth, 56);
        m_pPrintInfoLabel->clear();
        CPrintThread::GetInstance()->CancelPrint();
    }
}

void CHistoryPage::_ShowAll()
{
    m_bHasQuery = false;
    m_sSearchStruct.Clear();
    m_iCurrentPage = 0;
    m_iSelectedSize = 0;
    m_pSelectAllCheckBox->setChecked(false);
    _ReGetDataPageLines();
    _UpdateSelectedInfo();
    _UpdatePageBtn();
    _ShowCurrentPageAllData();
}

void CHistoryPage::_ReGetDataPageLines()
{    
    QList<int> iHistoryIDList;
    if(m_bHasQuery)
        CProjectDB::GetInstance()->GetHistorySearchID(m_sSearchStruct, iHistoryIDList);
    else
        CProjectDB::GetInstance()->GetAllHistoryID(iHistoryIDList);

    m_iHistoryIDMap.clear();
    for(int i=0; i<iHistoryIDList.size(); i++)
        m_iHistoryIDMap.insert(iHistoryIDList.at(i), false);

    m_iTotalLines = m_iHistoryIDMap.size();
    m_iTotalPages = m_iTotalLines / m_iOnePageLines;
    m_iLeftLines = m_iTotalLines % m_iOnePageLines;
    if(0 != m_iLeftLines)
        m_iTotalPages++;
    qDebug()<<"历史数据总数:"<<m_iTotalLines<<",页数:"<<m_iTotalPages<<",当前页数:"<<m_iCurrentPage<<",是否查询:"<<m_bHasQuery;
}

void CHistoryPage::_ShowCurrentPageAllData()
{
    QTime qStart = QTime::currentTime();
    QList<SResultInfoStruct> sResultList;
    CProjectDB::GetInstance()->GetHistoryPageData(m_iCurrentPage, m_iOnePageLines, sResultList);
    QTime qMid = QTime::currentTime();
    _UpdateListWidgetData(sResultList);
    qDebug()<<Q_FUNC_INFO<<"查询耗时:"<<qStart.msecsTo(qMid)<<"显示当前页耗时:"<<qStart.msecsTo(QTime::currentTime());
}

void CHistoryPage::_ShowCurrentPageQueryData()
{
    QTime qStart = QTime::currentTime();
    QList<SResultInfoStruct> sResultList;
    CProjectDB::GetInstance()->GetHistorySearchPageData(m_sSearchStruct, m_iCurrentPage, m_iOnePageLines, sResultList);
    QTime qMid = QTime::currentTime();
    _UpdateListWidgetData(sResultList);
    qDebug()<<Q_FUNC_INFO<<"查询耗时:"<<qStart.msecsTo(qMid)<<"显示当前页耗时:"<<qStart.msecsTo(QTime::currentTime());
}

void CHistoryPage::_UpdateListWidgetData(const QList<SResultInfoStruct> &sResultList)
{
    for(int i=0; i<m_pListWidget->count(); i++)
    {
        QListWidgetItem *pItem = m_pListWidget->item(i);
        if(nullptr == pItem)
            continue;

        if(i < sResultList.size())
        {
            pItem->setHidden(false);
            CHistoryItemWidget *pWidget = dynamic_cast<CHistoryItemWidget *>(m_pListWidget->itemWidget(pItem));
            if(nullptr != pWidget)
            {
                pWidget->SetHistoryResult(sResultList.at(i));
                pWidget->SetChecked(m_iHistoryIDMap.value(sResultList.at(i).iHistoryID, false));
            }
        }
        else
        {
            pItem->setHidden(true);
        }
    }
}

void CHistoryPage::_UpdateSelectedInfo()
{
    QString strSelect = QString("<font color=red>%1</font>").arg(m_iSelectedSize);
    if(false == m_bHasQuery)
        m_pSelectInfoLabel->setText(tr("已选中 %1 条，总计 %2 条").arg(strSelect).arg(m_iTotalLines));
    else
        m_pSelectInfoLabel->setText(tr("已选中 %1 条，查询总计 %2 条").arg(strSelect).arg(m_iTotalLines));
}

QList<int> CHistoryPage::_GetSelectedHistoryID()
{
    QList<int> iIDList;
    for(auto it=m_iHistoryIDMap.constBegin(); it!= m_iHistoryIDMap.constEnd(); it++)
    {
        if(true == it.value())
            iIDList.push_back(it.key());
    }
    return iIDList;
}

void CHistoryPage::_UpdatePageBtn()
{
    m_pPageLabel->setText(QString("%1/%2").arg(m_iCurrentPage + 1).arg(m_iTotalPages));
    if(m_iCurrentPage <= 0)
    {
        m_pPrePageBtn->setEnabled(false);
        m_pNextPageBtn->setEnabled(true);
    }
    else if(m_iCurrentPage >= m_iTotalPages - 1)
    {
        m_pPrePageBtn->setEnabled(true);
        m_pNextPageBtn->setEnabled(false);
    }
    else
    {
        m_pPrePageBtn->setEnabled(true);
        m_pNextPageBtn->setEnabled(true);
    }

    if(m_iTotalLines <= m_iOnePageLines)
    {
        m_pPageLabel->setText("1/1");
        m_pPrePageBtn->setEnabled(false);
        m_pNextPageBtn->setEnabled(false);
    }
}

void CHistoryPage::_InitWidget()
{
    m_pCHLabelTitleWidget = new CHLabelTitleWidget(tr("历史记录"));

    m_pSelectAllCheckBox = new QCheckBox;
    m_pSelectAllCheckBox->setFixedSize(44, 44);
    connect(m_pSelectAllCheckBox, &QCheckBox::clicked, this, &CHistoryPage::_SlotSelectAllCheckedBox);

    m_pSelectInfoLabel = new QLabel;
    m_pSelectInfoLabel->setObjectName("TopLabel");

    m_pGotoLabel1 = new QLabel(tr("跳转至"));
    m_pGotoLabel1->setObjectName("TopLabel");

    m_pGotoLineEdit = new CLineEdit;
    m_pGotoLineEdit->setFixedSize(72, 40);
    m_pGotoLineEdit->setObjectName("GotoLineEdit");
    m_pGotoLineEdit->setAlignment(Qt::AlignCenter);

    m_pGotoLabel2 = new QLabel(tr("页"));
    m_pGotoLabel2->setObjectName("TopLabel");

    m_pGotoBtn = new QPushButton(tr("跳转"));
    m_pGotoBtn->setFixedSize(m_iGotoBtnWidth, 40);
    m_pGotoBtn->setObjectName("GotoBtn");
    connect(m_pGotoBtn, &QPushButton::clicked, this, &CHistoryPage::_SlotGotoPageBtn);

    m_pPrePageBtn = new QPushButton;
    m_pPrePageBtn->setFixedSize(40, 40);
    m_pPrePageBtn->setObjectName("PrePageBtn");
    connect(m_pPrePageBtn, &QPushButton::clicked, this, &CHistoryPage::_SlotPrePageBtn);

    m_pPageLabel = new QLabel("0/0");
    m_pPageLabel->setObjectName("PageLabel");

    m_pNextPageBtn = new QPushButton;
    m_pNextPageBtn->setFixedSize(40, 40);
    m_pNextPageBtn->setObjectName("NextPageBtn");
    connect(m_pNextPageBtn, &QPushButton::clicked, this, &CHistoryPage::_SlotNextPageBtn);

    m_pListWidget = new QListWidget;
    m_pListWidget->setFixedSize(1636, 640);
    m_pListWidget->setObjectName("HistoryListWidget");
    m_pListWidget->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pListWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    for(int i=0; i<5; i++)
    {
        QListWidgetItem *pItem = new QListWidgetItem(m_pListWidget);
        pItem->setSizeHint(QSize(1636, 125));
        CHistoryItemWidget *pWidget = new CHistoryItemWidget;
        connect(pWidget, &CHistoryItemWidget::SignalItemChecked, this, &CHistoryPage::_SlotItemChecked);
        m_pListWidget->setItemWidget(pItem, pWidget);
    }
    connect(m_pListWidget, &QListWidget::itemClicked, this, &CHistoryPage::_SlotListWidgetItemClicked);
    connect(m_pListWidget, &QListWidget::itemDoubleClicked, this, &CHistoryPage::_SlotListWidgetItemDoubleClicked);

    m_pLisInfoLabel = new QLabel;
    m_pExportInfoLabel = new QLabel;
    m_pPrintInfoLabel = new QLabel;

    m_pDetailBtn = new QPushButton(tr("详情"));
    m_pDetailBtn->setFixedSize(m_iBtnWidth, 56);
    connect(m_pDetailBtn, &QPushButton::clicked, this, &CHistoryPage::_SlotDetailBtn);

    m_pQueryBtn = new QPushButton(tr("查询"));
    m_pQueryBtn->setFixedSize(m_iBtnWidth, 56);
    if(eLanguage_Italian == gk_iLanguage)
        m_pQueryBtn->setFixedSize(150, 56);
    connect(m_pQueryBtn, &QPushButton::clicked, this, &CHistoryPage::_SlotQueryBtn);

    m_pShowAllBtn = new QPushButton(tr("刷新"));
    m_pShowAllBtn->setFixedSize(m_iBtnWidth, 56);
    connect(m_pShowAllBtn, &QPushButton::clicked, this, &CHistoryPage::_SlotShowAllBtn);

    m_pLisBtn = new QPushButton(tr("LIS"));
    m_pLisBtn->setFixedSize(m_iBtnWidth, 56);
    connect(m_pLisBtn, &QPushButton::clicked, this, &CHistoryPage::_SlotLisBtn);

    m_pExportBtn = new QPushButton(tr("导出"));
    m_pExportBtn->setFixedSize(m_iBtnWidth, 56);
    connect(m_pExportBtn, &QPushButton::clicked, this, &CHistoryPage::_SlotExportBtn);

    m_pPrintBtn = new QPushButton(tr("打印"));
    m_pPrintBtn->setFixedSize(m_iBtnWidth, 56);    
    connect(m_pPrintBtn, &QPushButton::clicked, this, &CHistoryPage::_SlotPrintBtn);
    if(eLanguage_Chinese == gk_iLanguage)
        m_pPrintBtn->setVisible(false);

    m_pDeleteBtn = new QPushButton(tr("删除"));
    m_pDeleteBtn->setFixedSize(m_iBtnWidth, 56);
    connect(m_pDeleteBtn, &QPushButton::clicked, this, &CHistoryPage::_SlotDeleteBtn);

    m_pBackgroundLabel = new QLabel(this);
    m_pBackgroundLabel->setGeometry(0, 0, 1684, 960);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");
}

void CHistoryPage::_InitLayout()
{
    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(12);
    pTopLayout->addSpacing(24);
    pTopLayout->addWidget(m_pSelectAllCheckBox);
    pTopLayout->addWidget(m_pSelectInfoLabel);
    pTopLayout->addStretch(1);
    pTopLayout->addWidget(m_pGotoLabel1);
    pTopLayout->addWidget(m_pGotoLineEdit);
    pTopLayout->addWidget(m_pGotoLabel2);
    pTopLayout->addWidget(m_pGotoBtn);
    pTopLayout->addSpacing(20);
    pTopLayout->addWidget(m_pPrePageBtn);
    pTopLayout->addSpacing(4);
    pTopLayout->addWidget(m_pPageLabel);
    pTopLayout->addSpacing(4);
    pTopLayout->addWidget(m_pNextPageBtn);

    QHBoxLayout *pInfoLayout = new QHBoxLayout;
    pInfoLayout->setMargin(0);
    pInfoLayout->setSpacing(24);
    pInfoLayout->addStretch(1);
    pInfoLayout->addWidget(m_pLisInfoLabel);
    pInfoLayout->addWidget(m_pExportInfoLabel);
    pInfoLayout->addWidget(m_pPrintInfoLabel);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(30);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pDetailBtn);
    pBtnLayout->addWidget(m_pQueryBtn);
    pBtnLayout->addWidget(m_pShowAllBtn);
    pBtnLayout->addWidget(m_pLisBtn);
    pBtnLayout->addWidget(m_pExportBtn);
    pBtnLayout->addWidget(m_pPrintBtn);
    pBtnLayout->addWidget(m_pDeleteBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setContentsMargins(24, 12, 24, 0);
    pLayout->addWidget(m_pCHLabelTitleWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(25);
    pLayout->addLayout(pTopLayout);
    pLayout->addSpacing(25);
    pLayout->addWidget(m_pListWidget);
    pLayout->addStretch(1);
    pLayout->addLayout(pInfoLayout);
    pLayout->addSpacing(20);
    pLayout->addLayout(pBtnLayout);
    pLayout->addSpacing(30);
    m_pBackgroundLabel->setLayout(pLayout);
}
