#include "CMotorInfoDB.h"
#include "PublicConfig.h"
#include <QDebug>

CMotorInfoDB *CMotorInfoDB::m_spInstance = nullptr;

bool CMotorInfoDB::addMotorChildTiming(QString strName, QString strContent)
{
    QString strCmd = QString("SELECT * FROM motor_child_table WHERE cmd_name = '%1'").arg(strName);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd,strList))
        return false;

    if(!strList.isEmpty())
    {
        qDebug()<<"add motor_child_table 数据库中已经存在 update"<<strName;
        strCmd = QString("UPDATE motor_child_table SET content='%1' WHERE cmd_name='%2'")
                    .arg(strContent).arg(strName);
        return _ExecuteDB(strCmd);
    }

    strCmd = QString("INSERT INTO motor_child_table (cmd_name,content) VALUES ('%1','%2')")
            .arg(strName).arg(strContent);
    return _ExecuteDB(strCmd);
}

bool CMotorInfoDB::deleteMotorChildTimingFromName(QString strName)
{
    return _deleteDataByParam("motor_child_table","cmd_name",strName);
}

QStringList CMotorInfoDB::getAllMotorChildTimingNames()
{
    return _getColumnData("cmd_name","motor_child_table");
}

QString CMotorInfoDB::getMotorChildContentFromName(QString strName)
{
    return _getOneDataByParam("content","motor_child_table","cmd_name",strName);
}

bool CMotorInfoDB::deleteAllMotorChildTiming()
{
    return _DeleteTable("motor_child_table");
}

bool CMotorInfoDB::addMotorComposeTiming(QString strCmdID, QString strCmdName, QString strContent)
{
    QString strExec = QString("SELECT * FROM motor_compose_table WHERE cmd_id = '%1'").arg(strCmdID);
    QList<QStringList> strList;
    if(!_QueryDB(strExec,strList))
        return false;

    if(!strList.isEmpty())
    {
        qDebug()<<"add motor id 数据库中已经存在 update"<<strCmdID;
        strExec = QString("UPDATE motor_compose_table SET content='%1',cmd_name='%2' WHERE cmd_id='%3'")
                .arg(strContent).arg(strCmdName).arg(strCmdID);
        return _ExecuteDB(strExec);
    }

    strExec = QString("INSERT INTO motor_compose_table (cmd_id,cmd_name,content,cmd_text) VALUES ('%1','%2','%3','%4')")
            .arg(strCmdID).arg(strCmdName).arg(strContent).arg("");
    return _ExecuteDB(strExec);
}

bool CMotorInfoDB::addMotorComposeTiming(QString strCmdID, QString strCmdName, QString strContent, QString strCmdText)
{
    QString strExec = QString("SELECT * FROM motor_compose_table WHERE cmd_id = '%1'").arg(strCmdID);
    QList<QStringList> strList;
    if(!_QueryDB(strExec,strList))
        return false;

    if(!strList.isEmpty())
    {
        qDebug()<<"add motor id 数据库中已经存在 update"<<strCmdID;
        strExec = QString("UPDATE motor_compose_table SET content='%1' , cmd_text='%2' WHERE cmd_id='%3'")
                .arg(strContent).arg(strCmdText).arg(strCmdID);
        return _ExecuteDB(strExec);
    }

    strExec = QString("INSERT INTO motor_compose_table (cmd_id,cmd_name,content,cmd_text) VALUES ('%1','%2','%3','%4')")
            .arg(strCmdID).arg(strCmdName).arg(strContent).arg(strCmdText);
    return _ExecuteDB(strExec);
}

bool CMotorInfoDB::deleteMotorComposeTimingFromName(QString strName)
{
    return _deleteDataByParam("motor_compose_table","cmd_name",strName);
}

QString CMotorInfoDB::findCmdNameFromMotorComposeCmdID(QString strCmdID)
{
    return _getOneDataByParam("cmd_name","motor_compose_table","cmd_id",strCmdID);
}

QString CMotorInfoDB::findCmdIDFromMotorComposeCmdName(QString strName)
{
    return _getOneDataByParam("cmd_id","motor_compose_table","cmd_name",strName);
}

QString CMotorInfoDB::findCmdTextFromMotorComposeCmdID(QString strCmdID)
{
    return _getOneDataByParam("cmd_text","motor_compose_table","cmd_id",strCmdID);
}

QString CMotorInfoDB::findContentFromMotorComposeCmdName(QString strName)
{
    return _getOneDataByParam("content","motor_compose_table","cmd_name",strName);
}

QStringList CMotorInfoDB::getAllMotorComposeTimingNames()
{
    return _getColumnData("cmd_name","motor_compose_table","cmd_id");
}

QStringList CMotorInfoDB::getAllMotorComposeTimingIDNames()
{
    QStringList strIDNameList;
    QString strCmd = QString("SELECT cmd_id,cmd_name FROM motor_compose_table ORDER BY cmd_id");
    QList<QStringList> strList;
    if(_QueryDB(strCmd,strList))
    {
        for(int i=0; i<strList.size(); i++)
        {
            QStringList one = strList.at(i);
            if(one.size() < 2)
                continue;
            strIDNameList<<QString(one.at(0) + "_" + one.at(1));
        }
    }
    return strIDNameList;
}

QMap<QString,QString> CMotorInfoDB::getAllMotorComposeCmdTextIDMap()
{
    QMap<QString,QString> strMap;
    QString strCmd = QString("SELECT cmd_id,cmd_text FROM motor_compose_table");
    QList<QStringList> strList;
    if(_QueryDB(strCmd,strList))
    {
        for(int i=0; i<strList.size(); i++)
        {
            QStringList one = strList.at(i);
            if(one.size() < 2)
                continue;

            QString id   = one.at(0);
            QString text = one.at(1);
            if(text.isEmpty())
                strMap.insert(id,id);
            else
                strMap.insert(id, text + "_" + id);
        }
    }
    return strMap;
}

bool CMotorInfoDB::deleteAllMotorComposeTiming()
{
    return _DeleteTable("motor_compose_table");
}

bool CMotorInfoDB::addTimingChildTiming(QString strName, QString strContent)
{
    QString strCmd = QString("SELECT * FROM timing_child_table WHERE cmd_name = '%1'").arg(strName);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd,strList))
        return false;

    if(!strList.isEmpty())
    {
        qDebug()<<"add timing_child_table 数据库中已经存在 update"<<strName;
        strCmd = QString("UPDATE timing_child_table SET content='%1' WHERE cmd_name='%2'")
                .arg(strContent).arg(strName);
        return _ExecuteDB(strCmd);
    }

    strCmd = QString("INSERT INTO timing_child_table (cmd_name,content) VALUES ('%1','%2')")
            .arg(strName).arg(strContent);
    return _ExecuteDB(strCmd);
}

bool CMotorInfoDB::deleteTimingChildTimingFromName(QString strName)
{
    return _deleteDataByParam("timing_child_table","cmd_name",strName);
}

QStringList CMotorInfoDB::getAllTimingChildTimingNames()
{
    return _getColumnData("cmd_name","timing_child_table");
}

QString CMotorInfoDB::getTimingChildContentFromName(QString strName)
{
    return _getOneDataByParam("content","timing_child_table","cmd_name",strName);
}

bool CMotorInfoDB::deleteAllTimingChildTiming()
{
    return _DeleteTable("timing_child_table");
}

QStringList CMotorInfoDB::getAllTimingComposeTimingNames()
{
    return _getColumnData("cmd_name","timing_child_table");
}

QList<QStringList> CMotorInfoDB::readAllTimingList()
{
    QString strCmd = "select * from timing_child_table";
    QList<QStringList> strList;
    _QueryDB(strCmd,strList);
    return strList;
}

bool CMotorInfoDB::addTimingComposeTiming(QString strID, QString strName, QString strContent)
{
    QString strCmd = QString("SELECT * FROM timing_compose_table WHERE cmd_name = '%1'").arg(strName);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd,strList))
        return false;

    if(!strList.isEmpty())
    {
        qDebug()<<"add timing_compose_table 数据库中已经存在 update"<<strName;
        strCmd = QString("UPDATE timing_compose_table SET content='%1' WHERE cmd_name='%2'")
                .arg(strContent).arg(strName);
        return _ExecuteDB(strCmd);
    }

    strCmd = QString("INSERT INTO timing_compose_table (cmd_id,cmd_name,content) VALUES ('%1','%2','%3')")
            .arg(strID).arg(strName).arg(strContent);
    return _ExecuteDB(strCmd);
}

bool CMotorInfoDB::deleteTimingComposeTimingFromName(QString strName)
{
    return _deleteDataByParam("timing_compose_table","cmd_name",strName);
}

QString CMotorInfoDB::findTimingCmdNameFromMotorComposeCmdID(QString strCmdID)
{
    return _getOneDataByParam("cmd_name","timing_compose_table","cmd_id",strCmdID);
}

QString CMotorInfoDB::findTimingCmdIDFromMotorComposeCmdName(QString strCmdName)
{
    return _getOneDataByParam("cmd_id","timing_compose_table","cmd_name",strCmdName);
}

QString CMotorInfoDB::findTimingContentFromMotorComposeCmdName(QString strCmdName)
{
    return _getOneDataByParam("content","timing_compose_table","cmd_name",strCmdName);
}

bool CMotorInfoDB::deleteAllTimingComposeTiming()
{
    return _DeleteTable("timing_compose_table");
}

bool CMotorInfoDB::addMotorCompensate(QStringList strList)
{
    if(5 != strList.count())
        return false;

    QString strCmd = QString("INSERT INTO motor_compensate (motor_index,compensate_index,name,value,remarks) "
                             "VALUES ('%1','%2','%3','%4','%5')")
            .arg(strList.at(0)).arg(strList.at(1)).arg(strList.at(2)).arg(strList.at(3)).arg(strList.at(4));
    return _ExecuteDB(strCmd);
}

QStringList CMotorInfoDB::getCompensateNameFromMotorIndex(QString strMotorIndex)
{
    QStringList strNameList;
    if(strMotorIndex.isEmpty())
        return strNameList;

    QString strCmd = QString("SELECT name FROM motor_compensate WHERE motor_index = '%1'").arg(strMotorIndex);
    QList<QStringList> strList;
    if(_QueryDB(strCmd,strList))
        strNameList = _GetColumnValueList(strList);
    return strNameList;
}

QList<QStringList> CMotorInfoDB::getCompensateDataFromMotorIndex(QString strMotorIndex)
{
    QList<QStringList> strList;
    if(strMotorIndex.isEmpty())
        return strList;

    QString strCmd = QString("SELECT compensate_index,name,value FROM motor_compensate WHERE motor_index = '%1'").arg(strMotorIndex);
    _QueryDB(strCmd,strList);
    return strList;
}

bool CMotorInfoDB::deleteAllMotorCompenstateFromMotorIndex(QString strMotorIndex)
{
    return _deleteDataByParam("motor_compensate","motor_index",strMotorIndex);
}

QString CMotorInfoDB::getCompensateStringData()
{
    QString strStringData = "";
    QString strMotorIndex;
    QStringList strMotorIndexList;
    for(int i=0; i<5; i++)
    {
        QStringList strReturnDataList;
        strMotorIndex = QString::number(i);
        QList<QStringList> strList = getCompensateDataFromMotorIndex(strMotorIndex);
        for(int j=0; j<strList.size(); j++)
        {
            strReturnDataList.push_back(strList.at(j).join(","));
        }
        QString strMotorOne = QString::number(i) + "#" + strReturnDataList.join(";");
        strMotorIndexList.push_back(strMotorOne);
    }
    strStringData = strMotorIndexList.join("$");
    strStringData.remove(strStringData.length()-1, 1);

    return strStringData;
}

QStringList CMotorInfoDB::getAllCompensateType()
{
    return _getColumnData("type","motor_compensate_type");
}

QString CMotorInfoDB::getV1hFromType(QString strType)
{
    return _getOneDataByParam("v1h","motor_compensate_type","type",strType);
}

QString CMotorInfoDB::getContextFromType(QString strType)
{
    return _getOneDataByParam("context","motor_compensate_type","type",strType);
}

bool CMotorInfoDB::addCompensateType(QStringList strDataList)
{
    if(4 != strDataList.count())
        return false;

    QString type = strDataList.at(0);
    QString v1h = strDataList.at(1);
    QString context = strDataList.at(2);
    QString remarks = strDataList.at(3);

    QString strCmd = QString("SELECT context FROM motor_compensate_type WHERE type = '%1'").arg(type);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd,strList))
        return false;

    if(!strList.isEmpty())
    {
        qDebug()<<"add motor_compensate_type 数据库中已经存在 update"<<strDataList[0];
        strCmd = QString("UPDATE motor_compensate_type SET v1h='%1',context='%2' WHERE type='%3'")
                    .arg(v1h).arg(context).arg(type);
        return _ExecuteDB(strCmd);
    }

    strCmd = QString("INSERT INTO motor_compensate_type (type,v1h,context,remarks) VALUES ('%1','%2','%3','%4')")
            .arg(type).arg(v1h).arg(context).arg(remarks);
    return _ExecuteDB(strCmd);
}

bool CMotorInfoDB::deleteFromType(QString strType)
{
    return _deleteDataByParam("motor_compensate_type","type",strType);
}

bool CMotorInfoDB::addMotorNormalData(QStringList strDataList)
{
    if(5 != strDataList.count())
        return false;

    QString index = strDataList.at(0);
    QString type = strDataList.at(1);
    QString param = strDataList.at(2);
    QString value = strDataList.at(3);
    QString remarks = strDataList.at(4);

    QString strCmd = QString("SELECT * FROM motor_normal_data WHERE motor_index = '%1'"
                             " AND type = '%2' AND param = '%3'").arg(index).arg(type).arg(param);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd,strList))
        return false;

    if(!strList.isEmpty())
    {
        qDebug()<<"update motor_normal_data"<<strDataList;
        strCmd = QString("UPDATE motor_normal_data SET value='%1' WHERE motor_index='%2'"
                         " AND type='%3' AND param='%4'").arg(value).arg(index).arg(type).arg(param);
        return _ExecuteDB(strCmd);
    }

    strCmd = QString("INSERT INTO motor_normal_data (motor_index,type,param,value,remarks) "
                     "VALUES ('%1','%2','%3','%4','%5')").arg(index).arg(type).arg(param).arg(value).arg(remarks);
    return _ExecuteDB(strCmd);
}

int CMotorInfoDB::getOneDataFromMotor(QString strMotorIndex, QString strType)
{
    int iValue = 0;
    if(strType.isEmpty())
        return iValue;

    QString strCmd = QString("SELECT value FROM motor_normal_data "
                             "WHERE motor_index = '%1' AND type = '%2'").arg(strMotorIndex).arg(strType);
    QList<QStringList> strList;
    if(_QueryDB(strCmd,strList))
        iValue = _GetFirstValue(strList).toInt();
    return iValue;
}

QStringList CMotorInfoDB::getSixParamFromMotor(QString strMotorIndex, QString strParam)
{
    QStringList strSixList;
    if(strMotorIndex.isEmpty() || strParam.isEmpty())
        return strSixList;

    QString strCmd = QString("SELECT value FROM motor_normal_data "
                             "WHERE motor_index = '%1' AND param = '%2'").arg(strMotorIndex).arg(strParam);
    QList<QStringList> strList;
    if(_QueryDB(strCmd,strList))
        strSixList = _GetFirstValue(strList).split(",");
    return strSixList;
}

QList<QStringList> CMotorInfoDB::readAllMethod()
{
    QString strCmd = "select * from method_table";
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return strList;
}

bool CMotorInfoDB::saveOneMethod(const QStringList &strOneMethod)
{
    if(3 != strOneMethod.size())
        return false;

    QString strID = strOneMethod.at(0);
    QString strName = strOneMethod.at(1);
    QString strText = strOneMethod.at(2);

    QString strCmd = QString("SELECT * FROM method_table WHERE cmd_id = '%1'").arg(strID);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd,strList))
        return false;

    if(!strList.isEmpty())
    {
        qDebug()<<"add method_table 数据库中已经存在 update"<<strID;
        strCmd = QString("UPDATE method_table SET cmd_name='%1' AND cmd_text='%2' WHERE cmd_id='%3'")
                .arg(strName).arg(strText).arg(strID);
        return _ExecuteDB(strCmd);
    }

    strCmd = QString("INSERT INTO method_table (cmd_id,cmd_name,cmd_text) VALUES ('%1','%2','%3')")
            .arg(strID).arg(strName).arg(strText);
    return _ExecuteDB(strCmd);
}

bool CMotorInfoDB::saveAllMethod(const QList<QStringList> &strAllMethod)
{
    for(int i=0; i<strAllMethod.size(); i++)
        saveOneMethod(strAllMethod.at(i));
    return true;
}

CMotorInfoDB::CMotorInfoDB()
    : CSqliteDBBase(CPublicConfig::GetInstance()->GetMotorInfoDBPath(),gk_strMotorInfoDBConnect)
{
    initDataBase();

    QString strCmd = "select * from sqlite_master where type = 'table' and name = 'motor_compose_table' and sql like '%cmd_text%'";
    QList<QStringList> strList;
    if(_QueryDB(strCmd, strList))
    {
        if(strList.isEmpty())
        {
            strCmd = "alter table motor_compose_table add column 'cmd_text' varchar";
            _ExecuteDB(strCmd);

            _updateMethodText();
        }
    }
}

CMotorInfoDB::~CMotorInfoDB()
{

}

void CMotorInfoDB::_updateMethodText()
{
    QString strCmd = "select cmd_id from motor_compose_table";
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);

    QStringList strSQLList;
    QStringList strIDList = _GetColumnValueList(strList);
    qDebug()<<"更新指令说明:"<<strIDList;
    for(int i=0; i<strIDList.size(); i++)
    {
        QString id = strIDList.at(i);
        QString strText = CPublicConfig::GetInstance()->GetMotorMethodCHTextByID(id.toInt());
        strCmd = QString("update motor_compose_table set cmd_text = '%1' where cmd_id = '%2'").arg(strText).arg(id);
        strSQLList.push_back(strCmd);
    }
    _ExecuteDB(strSQLList);
}

QString CMotorInfoDB::_getOneDataByParam(QString strSelect, QString strTableName, QString strParamName, QString strParamValue)
{
    QString strData;
    if(strSelect.isEmpty() || strTableName.isEmpty() || strParamName.isEmpty())
        return strData;

    QString strCmd = QString("SELECT %1 FROM %2 WHERE %3 = '%4'")
            .arg(strSelect).arg(strTableName).arg(strParamName).arg(strParamValue);
    QList<QStringList> strList;
    if(_QueryDB(strCmd,strList))
        strData = _GetFirstValue(strList);
    return strData;
}

QStringList CMotorInfoDB::_getColumnData(QString strSelect, QString strTableName,QString strOrder)
{
    QStringList strColumnList;
    if(strSelect.isEmpty() || strTableName.isEmpty())
        return strColumnList;

    QString strCmd = QString("SELECT %1 FROM %2").arg(strSelect).arg(strTableName);
    if(!strOrder.isEmpty())
        strCmd += QString(" ORDER BY %1").arg(strOrder);
    QList<QStringList> strList;
    if(_QueryDB(strCmd,strList))
        strColumnList = _GetColumnValueList(strList);
    return strColumnList;
}

bool CMotorInfoDB::_deleteDataByParam(QString strTableName, QString strParamName, QString strParamValue)
{
    QString strCmd = QString("DELETE FROM %1 WHERE %2 = '%3'")
            .arg(strTableName).arg(strParamName).arg(strParamValue);
    return _ExecuteDB(strCmd);
}

bool CMotorInfoDB::_addDBData(const CMotorInfoDB::SDBAddDataStruct &addStruct)
{
    Q_UNUSED(addStruct);
#if 0
    QString strCmd = QString("SELECT * FROM motor_normal_data WHERE motor_index = '%1'"
                             " AND type = '%2' AND param = '%3'").arg(index).arg(type).arg(param);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd,strList))
        return false;

    if(!strList.isEmpty())
    {
        qDebug()<<QString("update %1")<<addStruct.strTableName;
        strCmd = QString("UPDATE motor_normal_data SET value='%1' WHERE motor_index='%2'"
                         " AND type='%3' AND param='%4'").arg(value).arg(index).arg(type).arg(param);
        return _ExecuteDB(strCmd);
    }

    QString name = addStruct.addMap.keys().join(",");
    QStringList valueList = addStruct.addMap.values();
    strCmd = QString("INSERT INTO motor_normal_data (%1) VALUES").arg(name);
    for(int i=0; i<valueList.size(); i++)
    {
        valueList[i] = QString("'%1'").arg(valueList.at(i));
    }

    strCmd = QString("INSERT INTO motor_normal_data (motor_index,type,param,value,remarks) "
                     "VALUES ('%1','%2','%3','%4','%5')").arg(index).arg(type).arg(param).arg(value).arg(remarks);
    return _ExecuteDB(strCmd);
#endif
    return false;
}

CMotorInfoDB *CMotorInfoDB::GetInstance()
{
    if(nullptr == m_spInstance)
        m_spInstance = new CMotorInfoDB;
    return m_spInstance;
}

void CMotorInfoDB::initDataBase()
{
    //motor_child_table
    QString strCmd = "CREATE TABLE IF NOT EXISTS motor_child_table ("
                     "id INTEGER PRIMARY KEY AUTOINCREMENT,"
                     "cmd_name VARCHAR,"
                     "content VARCHAR)";
    _ExecuteDB(strCmd);

    //motor_compensate
    strCmd = "CREATE TABLE IF NOT EXISTS motor_compensate ("
             "id INTEGER PRIMARY KEY AUTOINCREMENT,"
             "motor_index VARCHAR,"
             "compensate_index VARCHAR,"
             "name VARCHAR,"
             "value VARCHAR,"
             "remarks VARCHAR)";
    _ExecuteDB(strCmd);

    //motor_compensate_type
    strCmd  = "CREATE TABLE IF NOT EXISTS motor_compensate_type ("
              "id INTEGER PRIMARY KEY AUTOINCREMENT,"
              "type VARCHAR,"
              "v1h VARCHAR,"
              "context VARCHAR,"
              "remarks VARCHAR)";
    _ExecuteDB(strCmd);

    //motor_compose_table
    strCmd = "CREATE TABLE IF NOT EXISTS motor_compose_table ("
             "id INTEGER PRIMARY KEY AUTOINCREMENT,"
             "cmd_id VARCHAR,"
             "cmd_name VARCHAR,"
             "content VARCHAR)";
    _ExecuteDB(strCmd);


    //motor_normal_data
    strCmd = "CREATE TABLE IF NOT EXISTS motor_normal_data ("
             "id INTEGER PRIMARY KEY AUTOINCREMENT,"
             "motor_index VARCHAR,"
             "type VARCHAR,"
             "param VARCHAR,"
             "value VARCHAR,"
             "remarks VARCHAR)";
    _ExecuteDB(strCmd);

    //timing_child_table
    strCmd = "CREATE TABLE IF NOT EXISTS timing_child_table ("
             "id INTEGER PRIMARY KEY AUTOINCREMENT,"
             "cmd_name VARCHAR,"
             "content VARCHAR)";
    _ExecuteDB(strCmd);

    //timing_compose_table
    strCmd = "CREATE TABLE IF NOT EXISTS timing_compose_table ("
             "id INTEGER PRIMARY KEY AUTOINCREMENT,"
             "cmd_id VARCHAR,"
             "cmd_name VARCHAR,"
             "content VARCHAR)";
    _ExecuteDB(strCmd);

    // 指令ID 中文描述
    strCmd = "CREATE TABLE IF NOT EXISTS method_table ("
             "id INTEGER PRIMARY KEY AUTOINCREMENT,"
             "cmd_id VARCHAR,"
             "cmd_name VARCHAR,"
             "cmd_text VARCHAR)";
    _ExecuteDB(strCmd);
}

QString CMotorInfoDB::_GetNewContent(QString strOldContent)
{
    //read motor compose
    QStringList strMotorList;
    QList<QStringList> strDataList;
    QString strSelect = QString("SELECT cmd_id,cmd_name FROM motor_compose_table ORDER BY cmd_id");
    if(!_QueryDB(strSelect,strDataList))
        return "";

    for(int i=0; i<strDataList.size(); i++)
    {
        strMotorList.push_back(strDataList.at(0).join("_"));
    }

    QStringList strList = strOldContent.split(SPLIT_BETWEEN_CMD);
    for(int i=0; i<strList.size(); i++)
    {
        QStringList one = strList.at(i).split(SPLIT_IN_CMD);
        if(one.isEmpty())
            continue;
        int cmdIndex = one.at(0).toInt();
        int cmdID = CPublicConfig::GetInstance()->GetCmdIDFromIndex(cmdIndex);
        one[0] = QString::number(cmdID);
        if(66 == cmdIndex)
        {
           if(one.size() >= 2)
           {
               int motorIndex = one.at(1).toInt();
               if(motorIndex >= 0 && motorIndex < strMotorList.size())
                   one[1] = strMotorList[motorIndex];
           }
        }
        strList[i] = one.join(SPLIT_IN_CMD);
    }

    return strList.join(SPLIT_BETWEEN_CMD);
}

void CMotorInfoDB::convertTimingChildDBIndex2ID()
{
    //read
    QList<QStringList> strList;
    QString strCmd = QString("SELECT cmd_name,content FROM timing_child_table");
    if(!_QueryDB(strCmd,strList))
        return;

    //convert
    for(int i=0; i<strList.size(); i++)
    {
        QStringList one = strList.at(i);
        if(one.size() < 2)
            continue;
        qDebug()<<"时序转换前:"<<one.at(0)<<one.at(1);
        QString content = _GetNewContent(one.at(1));
        one[1] = content;
        qDebug()<<"时序转换后:"<<one.at(0)<<one.at(1);
        strList[i] = one;
    }

    //update
    QStringList strCmdList;
    for(int i=0; i<strList.size(); i++)
    {
        QStringList one = strList.at(i);
        if(one.size() < 2)
            continue;
        QString name = one.at(0);
        QString content = one.at(1);
        strCmd = QString("UPDATE timing_child_table SET content='%1' WHERE cmd_name='%2'")
                .arg(content).arg(name);
        strCmdList.push_back(strCmd);
    }
    _ExecuteDB(strCmdList);
}

QMap<QString, QString> CMotorInfoDB::readAllCmdNameContent()
{
    QString strCmd = "select * from motor_child_table";
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);

    QMap<QString, QString> map;

    for(int i=0; i<strList.size(); i++)
    {
        QStringList one = strList.at(i);
        if(one.size() < 3)
            continue;
        QString cmdName = one.at(1);
        QString cmdContent = one.at(2);

        if(map.contains(cmdName))
            qDebug()<<Q_FUNC_INFO<<"指令名称重复"<<cmdName<<cmdContent;
        map[cmdName] = cmdContent;
    }
    return map;
}

QMap<QString, QStringList> CMotorInfoDB::readAllMotorComposeInfo()
{
    QString strCmd = "select * from motor_compose_table";
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);

    QMap<QString, QStringList> map;
    for(int i=0; i<strList.size(); i++)
    {
        QStringList one = strList.at(i);
        if(one.size() < 3)
            continue;

        one.pop_front();
        QString cmdID = one.at(0);
        one.pop_front();
        map[cmdID] = one;
    }

    return map;
}

QList<QStringList> CMotorInfoDB::readAllMotorCompensate()
{
    QString strCmd = "select * from motor_compensate";
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return strList;
}

QList<QStringList> CMotorInfoDB::readAllNormalData()
{
    QString strCmd = "select * from motor_normal_data";
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return strList;
}
