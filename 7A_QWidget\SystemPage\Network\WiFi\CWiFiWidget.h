﻿#ifndef CWIFIWIDGET_H
#define CWIFIWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-01-16
  * Description: WIFI UI
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QTimer>
#include <QWidget>
#include <QListWidget>
#include <QPushButton>
#include <QRadioButton>
#include <QListWidgetItem>

#include "CLabelLineEdit.h"
#include "CHLabelTitleWidget.h"
#include "CWiFiInputWidget.h"
#include "CWiFiItemWidget.h"

class CWiFiWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CWiFiWidget(QWidget *parent = nullptr);

public slots:
    void SlotAppStartEnd();
    void SlotScanMap(QMap<QString, QString> strScanMap);
    void SlotWiFiConnectEnd(bool bConnectOK, QString strName, QString strPwd);

protected:
    void showEvent(QShowEvent *pEvent);
    void hideEvent(QHideEvent *pEvent);

signals:
    void SignalWiFiAbort();
    void SignalWiFiOpen(bool bOpen);
    void SignalWiFiIPInfoList(QStringList);

private slots:
    void _SlotWiFiOpenBtn();
    void _SlotWiFiCloseBtn();
    void _SlotListWidgetItemClicked(QListWidgetItem *pClickedItem);
    void _SlotWiFiAbort();
    void _SlotReConnectTimer();
    void _SlotScanTimer();

    void _SlotShowInputWidget(QString strName);
    void _SlotConnectWiFi(QString strName, QString strPwd);
    void _SlotDisconnectWiFi(QString strName);

    void _SlotReadCfg();

private:
    void _Thread2CheckWiFi();
    void _Thread2CheckEth();
    void _ConnectWiFi(QString strName, QString strPwd);
    void _HideListWidgetItem(int iStartIndex);
    void _SetListWidgetItemStatus(QString strName, WiFiStatus eStatus);
    void _SetAllUnconnect();

private:
    void _InitWidget();
    void _InitLayout();

private:
    bool m_bShow;
    bool m_bConnecting;
    bool m_bManualOpen;
    bool m_bManualClose;
    bool m_bScanning;
    qint64 m_iAbortTimes;
    int m_iReConnectTimes;
    QTimer *m_pReConnectTimer;
    QTimer *m_pScanTimer;

    QString m_strTipsText;
    QString m_strCurrentName;
    QString m_strCurrentPwd;
    QList<QStringList> m_strScanInfoList;

private:
    CHLabelTitleWidget *m_pTitleWidget;
    QRadioButton *m_pOpenBtn, *m_pCloseBtn;
    QLabel *m_pScanLabel;
    QListWidget *m_pListWidget;
    CWiFiInputWidget *m_pInputWidget;
};

#endif // CWIFIWIDGET_H
