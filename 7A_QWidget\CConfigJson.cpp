#include "CConfigJson.h"

#include <QDebug>
#include <QJsonArray>
#include <QJsonObject>
#include <QJsonDocument>
#include <QApplication>
#include "PublicFunction.h"

CConfigJson *CConfigJson::m_spInstance = nullptr;

CConfigJson *CConfigJson::GetInstance()
{
    if(nullptr == m_spInstance)
        m_spInstance = new CConfigJson;
    return m_spInstance;
}

QJsonObject CConfigJson::GetConfigJsonObject()
{
    return _ReadJsonObject(m_strConfigPath);
}

QJsonObject CConfigJson::GetConfigJsonObject(const QString &strKey)
{
    return _ReadJsonObject(m_strConfigPath, strKey);
}

QVariant CConfigJson::GetConfigValue(const QString &strKey)
{
    QVariant varValue;
    QJsonObject rootObj = _ReadJsonObject(m_strConfigPath);
    if(rootObj.contains(strKey))
        varValue = rootObj.value(strKey).toVariant();
    return varValue;
}

QVariant CConfigJson::GetConfigValue(const QString &strFirstKey, const QString &strSecondKey)
{
    QVariant varValue;
    QJsonObject rootObj = _ReadJsonObject(m_strConfigPath);
    if(!rootObj.contains(strFirstKey))
        return varValue;

    QJsonObject firstObj = rootObj.value(strFirstKey).toObject();
    if(firstObj.contains(strSecondKey))
        varValue = firstObj.value(strSecondKey).toVariant();
    return varValue;
}

void CConfigJson::SetConfigValue(const QString &strKey, const QVariant &varValue)
{
    QJsonObject rootObj = _ReadJsonObject(m_strConfigPath);
    rootObj.insert(strKey, QJsonValue::fromVariant(varValue));
    _WriteJsonObject(m_strConfigPath, rootObj);
}

void CConfigJson::SetConfigValue(const QString &strFirstKey, const QString &strSecondKey, const QVariant &varValue)
{
    QJsonObject rootObj = _ReadJsonObject(m_strConfigPath);
    if(rootObj.contains(strFirstKey))
    {
        QJsonObject firstObj = rootObj.value(strFirstKey).toObject();
        firstObj.insert(strSecondKey, QJsonValue::fromVariant(varValue));
        rootObj.insert(strFirstKey, firstObj);
    }
    else
    {
        QJsonObject firstObj;
        firstObj.insert(strSecondKey, QJsonValue::fromVariant(varValue));
        rootObj.insert(strFirstKey, firstObj);
    }
    _WriteJsonObject(m_strConfigPath, rootObj);
}

void CConfigJson::SetConfigJsonObject(const QJsonObject &qRootObject)
{
    _WriteJsonObject(m_strConfigPath, qRootObject);
}

void CConfigJson::SetConfigJsonObject(const QString &strKey, const QJsonObject &qJsonObject)
{
    QJsonObject rootObj = _ReadJsonObject(m_strConfigPath);
    rootObj.insert(strKey, qJsonObject);
    _WriteJsonObject(m_strConfigPath, rootObj);
}

void CConfigJson::IncrementInsertJsonObject(const QString &strKey, const QJsonObject &qJsonObject)
{
     QJsonObject rootObj = _ReadJsonObject(m_strConfigPath);
     if (rootObj.contains(strKey))
     {
         QJsonObject existingObj = rootObj.value(strKey).toObject();
         for (auto it = qJsonObject.begin(); it != qJsonObject.end(); ++it)
         {
             existingObj.insert(it.key(), it.value());
         }
         rootObj.insert(strKey, existingObj);
     }
     else
     {
         rootObj.insert(strKey, qJsonObject);
     }
     _WriteJsonObject(m_strConfigPath, rootObj);
}

QVariant CConfigJson::GetSystemValue(const QString &strKey)
{
    QVariant varValue;
    QJsonObject rootObj = _ReadJsonObject(m_strSystemPath);
    if(rootObj.contains(strKey))
        varValue = rootObj.value(strKey).toVariant();
    return varValue;
}

void CConfigJson::SetSystemValue(const QString &strKey, const QVariant &varValue)
{
    QJsonObject rootObj = _ReadJsonObject(m_strSystemPath);
    rootObj.insert(strKey, QJsonValue::fromVariant(varValue));
    _WriteJsonObject(m_strSystemPath, rootObj);
}

CConfigJson::CConfigJson()
{
    m_strConfigPath = QApplication::applicationDirPath() + "/Resources/config.json";
    m_strSystemPath = QApplication::applicationDirPath() + "/Resources/system.json";
}

CConfigJson::~CConfigJson()
{

}

QJsonObject CConfigJson::_ReadJsonObject(const QString &strFilePath)
{
    QJsonObject rootObj;
    QFile file(strFilePath);
    if(!file.open(QIODevice::ReadOnly))
    {
        qDebug()<<"配置文件打开失败:"<<strFilePath<<file.errorString();
        return rootObj;
    }

    QByteArray byteJson = file.readAll();
    file.close();

    QJsonParseError err;
    QJsonDocument doc = QJsonDocument::fromJson(byteJson, &err);
    if(QJsonParseError::NoError != err.error)
        return rootObj;

    rootObj = doc.object();
    return rootObj;
}

QJsonObject CConfigJson::_ReadJsonObject(const QString &strFilePath, const QString &strKey)
{
    QJsonObject rootObj = _ReadJsonObject(strFilePath);

    QJsonObject resObj;
    if(rootObj.contains(strKey))
        resObj = rootObj.value(strKey).toObject();
    return resObj;
}

bool CConfigJson::_WriteJsonObject(const QString &strFilePath, const QJsonObject &qRootObj)
{
    QFile file(strFilePath);
    if(!file.open(QIODevice::WriteOnly))
    {
        qDebug()<<"配置文件打开失败:"<<strFilePath<<file.errorString();
        return false;
    }

    QJsonDocument doc(qRootObj);
    QByteArray byteJson = doc.toJson(QJsonDocument::Compact);
    //qDebug()<<Q_FUNC_INFO<<byteJson;
    file.resize(0);
    file.write(byteJson);
    file.close();
	System("sync");
    return true;
}
