#ifndef CCHOPPER_H
#define CCHOPPER_H
/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-12-05
  * Description: 斩波器
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QPushButton>
#include "CCmdBase.h"
#include "CLabelComboBox.h"

class CMotorChopper : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CMotorChopper(QWidget *parent = nullptr);
    ~CMotorChopper();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

public slots:
    void SlotSoftTypeChanged(int iSoftType);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;

private slots:
    void _SlotBtnClicked();

private:
    void _InitWidget();
    QList<int> _GetValueFromData(uint iData);

private:
    QList<CLabelComboBox *> m_pComboBoxList;
    CLabelComboBox *m_pMachineComboBox;
};

#endif // CCHOPPER_H
