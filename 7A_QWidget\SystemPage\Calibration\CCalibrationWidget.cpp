#include "CCalibrationWidget.h"
#include <QBoxLayout>
#include "PublicFunction.h"

CCalibrationWidget::CCalibrationWidget(QWidget *parent) : QWidget(parent)
{
    _InitWidget();
    _InitLayout();

    LoadQSS(this, ":/qss/qss/system/calibration.qss");
}

void CCalibrationWidget::_SlotTitleChanged(int iTitle)
{
    m_pStackedWidget->setCurrentIndex(iTitle);
}

void CCalibrationWidget::_InitWidget()
{
    m_pCSysTtileLabelWidget = new CSysFirstTitleWidget(tr("系统设置"), tr("仪器校准"));
    connect(m_pCSysTtileLabelWidget, &CSysFirstTitleWidget::SignalTitlePress, this, &CCalibrationWidget::SignalReturn);

    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(1684, 904);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");

    m_pCSysSecondTitleWidget = new CSysSecondTitleWidget({tr("设备校准"), tr("校准历史")});
    connect(m_pCSysSecondTitleWidget, &CSysSecondTitleWidget::SignalSecondTitle, this, &CCalibrationWidget::_SlotTitleChanged);

    m_pCCaliDeviceWidget = new CCaliDeviceWidget;
    connect(m_pCCaliDeviceWidget, &CCaliDeviceWidget::SignalReturn, this, &CCalibrationWidget::SignalReturn);

    m_pCCaliHistoryWidget = new CCaliHistoryWidget;
    connect(m_pCCaliHistoryWidget, &CCaliHistoryWidget::SignalReturn, this, &CCalibrationWidget::SignalReturn);

    m_pStackedWidget = new QStackedWidget;
    m_pStackedWidget->setFixedSize(1636, 800);
    m_pStackedWidget->addWidget(m_pCCaliDeviceWidget);
    m_pStackedWidget->addWidget(m_pCCaliHistoryWidget);
}

void CCalibrationWidget::_InitLayout()
{
    QVBoxLayout *pBackLayout = new QVBoxLayout;
    pBackLayout->setContentsMargins(24, 15, 24, 24);
    pBackLayout->addWidget(m_pCSysSecondTitleWidget, 0, Qt::AlignLeft);
    pBackLayout->addStretch(1);
    pBackLayout->addWidget(m_pStackedWidget, 0, Qt::AlignHCenter);
    m_pBackgroundLabel->setLayout(pBackLayout);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pCSysTtileLabelWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pBackgroundLabel);
    this->setLayout(pLayout);
}
