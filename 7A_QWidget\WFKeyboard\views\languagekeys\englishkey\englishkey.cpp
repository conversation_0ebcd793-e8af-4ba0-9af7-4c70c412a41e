#include "englishkey.h"

#include "common/keyboardtoolbutton/keyboardtoolbutton.h"

EnglishKey::English<PERSON>ey(const QString& name):
    LanguageBase<PERSON><PERSON>(name)
{
    InitButtons();
}

void EnglishKey::InitButtons()
{
    QMap<int, QList<KeyBoardToolButton*> > map;
    QList<KeyBoardToolButton*> firstButtons, secondButtons, thirdButtons;

    firstButtons.append(new KeyBoardToolButton("\x0071","\x0051","",""));
    firstButtons.append(new KeyBoardToolButton("\x0077","\x0057","",""));
    firstButtons.append(new KeyBoardToolButton("\x0065","\x0045","",""));
    firstButtons.append(new KeyBoardToolButton("\x0072","\x0052","",""));
    firstButtons.append(new KeyBoardToolButton("\x0074","\x0054","",""));
    firstButtons.append(new KeyBoardToolButton("\x0079","\x0059","",""));
    firstButtons.append(new KeyBoardToolButton("\x0075","\x0055","",""));
    firstButtons.append(new KeyBoardToolButton("\x0069","\x0049","",""));
    firstButtons.append(new KeyBoardToolButton("\x006F","\x004F","",""));
    firstButtons.append(new KeyBoardToolButton("\x0070","\x0050","",""));

    secondButtons.append(new KeyBoardToolButton("\x0061","\x0041","",""));
    secondButtons.append(new KeyBoardToolButton("\x0073","\x0053","",""));
    secondButtons.append(new KeyBoardToolButton("\x0064","\x0044","",""));
    secondButtons.append(new KeyBoardToolButton("\x0066","\x0046","",""));
    secondButtons.append(new KeyBoardToolButton("\x0067","\x0047","",""));
    secondButtons.append(new KeyBoardToolButton("\x0068","\x0048","",""));
    secondButtons.append(new KeyBoardToolButton("\x006A","\x004A","",""));
    secondButtons.append(new KeyBoardToolButton("\x006B","\x004B","",""));
    secondButtons.append(new KeyBoardToolButton("\x006C","\x004C","",""));

    thirdButtons.append(GetLeftCapsLockBtn());
    thirdButtons.append(new KeyBoardToolButton("\x007A","\x005A","",""));
    thirdButtons.append(new KeyBoardToolButton("\x0078","\x0058","",""));
    thirdButtons.append(new KeyBoardToolButton("\x0063","\x0043","",""));
    thirdButtons.append(new KeyBoardToolButton("\x0076","\x0056","",""));
    thirdButtons.append(new KeyBoardToolButton("\x0062","\x0042","",""));
    thirdButtons.append(new KeyBoardToolButton("\x006E","\x004E","",""));
    thirdButtons.append(new KeyBoardToolButton("\x006D","\x004D","",""));
    thirdButtons.append(GetRightCapsLockBtn());

    map.insert(0,firstButtons);
    map.insert(1,secondButtons);
    map.insert(2,thirdButtons);

    SetButtonsMap(map);
    SetTranslate("CHS","ENG","Math","Space");
}
