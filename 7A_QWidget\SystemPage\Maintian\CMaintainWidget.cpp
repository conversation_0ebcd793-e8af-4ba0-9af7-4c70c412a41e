#include "CMaintainWidget.h"
#include <QTimer>
#include <QBoxLayout>
#include "PublicConfig.h"
#include "CConfigJson.h"
#include "CMessageBox.h"

CMaintainWidget::CMaintainWidget(QWidget *parent) : QWidget(parent)
{
    Register2Map(Method_can_id);

    _InitWidget();
    _InitLayout();

    _ReadCfg();
}

CMaintainWidget::~CMaintainWidget()
{
    UnRegister2Map(Method_can_id);
}

void CMaintainWidget::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    QString strLog = QString("%1# %2 %3").arg(iMachineID + 1).arg(iMethodID).arg(iResult);
    m_pTextBrowser->AppendLog(strLog);

    if(Method_can_id == iMethodID)
    {
        if(0 != iResult)
        {
            strLog = tr("操作CanID失败");
        }
        else
        {
            if(qVarData.isNull())
            {
                strLog = tr("设置CanID成功");
            }
            else
            {
                QVariantList qVarList = qVarData.toList();
                if(!qVarList.isEmpty())
                {
                    int iCanID = qVarList.first().toInt();
                    strLog = tr("读取CanID成功:") + QString::number(iCanID);
                    m_pCanIDLineEdit->setText(QString::number(iCanID));
                }
                else
                {
                    strLog = tr("读取CanID失败");
                }
            }
        }
    }

    qDebug()<<strLog;
    m_pTextBrowser->AppendLog(strLog);
}

void CMaintainWidget::showEvent(QShowEvent *pEvent)
{
    QWidget::showEvent(pEvent);
}

void CMaintainWidget::_SlotReadCanIDBtn()
{
    QString strCmd = GetJsonCmdString(Method_can_id);
    QString strLog = tr("读取CanID:") + strCmd;
    qDebug()<<strLog;
    m_pTextBrowser->AppendLog(strLog);
    SendJsonCmd(0xFF, Method_can_id, strCmd);
}

void CMaintainWidget::_SlotSetCanIDBtn()
{
    QString strData = m_pCanIDLineEdit->text();
    if(strData.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("请先填写CanID"));
        return;
    }

    int iCanID = strData.toInt();
    if(iCanID < 0 || iCanID >= gk_iMachineCount)
    {
        ShowInformation(this, m_strTipsText, tr("CanID填写异常，请重新填写"));
        return;
    }

    QVariantList qVarList = {iCanID};
    QString strCmd = GetJsonCmdString(Method_can_id, qVarList);
    QString strLog = tr("设置CanID:") + strCmd;
    qDebug()<<strLog;
    m_pTextBrowser->AppendLog(strLog);
    SendJsonCmd(0xFF, Method_can_id, strCmd);
}

void CMaintainWidget::_SlotDynamicPwdCheckBox(bool bClicked)
{
    QString strLog = tr("是否启用动态密码:%1").arg(bClicked);
    RUN_LOG(strLog);
    m_pTextBrowser->AppendLog(strLog);
    CPublicConfig::GetInstance()->SetDynamicPassword(bClicked);
    CConfigJson::GetInstance()->SetConfigValue("Login", "dynamic_password", bClicked);
}

void CMaintainWidget::_SlotShowRawCurveCheckBox(bool bClicked)
{
    QString strLog = tr("是否显示原始曲线:%1").arg(bClicked);
    RUN_LOG(strLog);
    m_pTextBrowser->AppendLog(strLog);
    CPublicConfig::GetInstance()->SetShowRawCurve(bClicked);
    CConfigJson::GetInstance()->SetConfigValue("ShowRawCurve", bClicked);
}

void CMaintainWidget::_ReadCfg()
{
    QJsonObject qRootObj = CConfigJson::GetInstance()->GetConfigJsonObject();

    QJsonObject qLoginObj = qRootObj.value("Login").toObject();
    bool bDynamicPwd = true;
    if(qLoginObj.contains("dynamic_password"))
        bDynamicPwd = qLoginObj.value("dynamic_password").toBool();
    m_pDynamicPwdCheckBox->setChecked(bDynamicPwd);
    RUN_LOG(QString("启用动态密码:%1").arg(bDynamicPwd));
    CPublicConfig::GetInstance()->SetDynamicPassword(bDynamicPwd);

    bool bShowRawCurve = false;
    if(qRootObj.contains("ShowRawCurve"))
        bShowRawCurve = qRootObj.value("ShowRawCurve").toBool();
    RUN_LOG(QString("是否显示原始曲线:%1").arg(bShowRawCurve));
    CPublicConfig::GetInstance()->SetShowRawCurve(bShowRawCurve);
    m_pShowRawCurveCheckBox->setChecked(bShowRawCurve);
}

void CMaintainWidget::_InitWidget()
{
    m_pCSysTtileLabelWidget = new CSysFirstTitleWidget(tr("系统设置"), tr("运维模式"));
    connect(m_pCSysTtileLabelWidget, &CSysFirstTitleWidget::SignalTitlePress, this, &CMaintainWidget::SignalReturn);

    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(1684, 904);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");

    m_pCanIDLineEdit = new CLineEdit;
    m_pCanIDLineEdit->setFixedSize(120, 56);
    m_pCanIDLineEdit->setPlaceholderText("CanID");

    m_pReadCanIDBtn = new QPushButton(tr("读取CanID"));
    m_pReadCanIDBtn->setFixedSize(200, 56);
    connect(m_pReadCanIDBtn, &QPushButton::clicked, this, &CMaintainWidget::_SlotReadCanIDBtn);

    m_pSetCanIDBtn = new QPushButton(tr("设置CanID"));
    m_pSetCanIDBtn->setFixedSize(200, 56);
    connect(m_pSetCanIDBtn, &QPushButton::clicked, this, &CMaintainWidget::_SlotSetCanIDBtn);

    m_pDynamicPwdCheckBox = new QCheckBox(tr("动态密码"));
    connect(m_pDynamicPwdCheckBox, &QCheckBox::clicked, this, &CMaintainWidget::_SlotDynamicPwdCheckBox);

    m_pShowRawCurveCheckBox = new QCheckBox(tr("显示原始曲线"), this);
    connect(m_pShowRawCurveCheckBox, &QCheckBox::clicked, this, &CMaintainWidget::_SlotShowRawCurveCheckBox);

    m_pTextBrowser = new CTextBrowser;
    m_pTextBrowser->setFixedSize(1684, 720);
}

void CMaintainWidget::_InitLayout()
{
    QHBoxLayout *pCanIDLayout = new QHBoxLayout;
    pCanIDLayout->setMargin(0);
    pCanIDLayout->setSpacing(30);
    pCanIDLayout->addSpacing(24);
    pCanIDLayout->addWidget(m_pCanIDLineEdit);
    pCanIDLayout->addWidget(m_pReadCanIDBtn);
    pCanIDLayout->addWidget(m_pSetCanIDBtn);
    pCanIDLayout->addSpacing(150);
    pCanIDLayout->addWidget(m_pDynamicPwdCheckBox);
    pCanIDLayout->addSpacing(60);
    pCanIDLayout->addWidget(m_pShowRawCurveCheckBox);
    pCanIDLayout->addStretch(1);

    QVBoxLayout *pBackLayout = new QVBoxLayout;
    pBackLayout->setMargin(0);
    pBackLayout->setSpacing(0);
    pBackLayout->addSpacing(24);
    pBackLayout->addLayout(pCanIDLayout);
    pBackLayout->addSpacing(30);
    pBackLayout->addWidget(m_pTextBrowser);
    m_pBackgroundLabel->setLayout(pBackLayout);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pCSysTtileLabelWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pBackgroundLabel);
    this->setLayout(pLayout);
}
