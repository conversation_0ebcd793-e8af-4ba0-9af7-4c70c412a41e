#include <QSignalMapper>
#include "characterbasekey.h"
#include "../keyboard.h"

class CharacterBaseKey::PrivateData
{
public:
    PrivateData(KeyBoard *kb, QStackedWidget *wgt)
        : keyBoard(kb)
        , swgt(wgt)
        , sm(NULL)
    {

    }

public:
    KeyBoard *keyBoard;
    QStackedWidget *swgt;
    QSignalMapper *sm;
};

CharacterBaseKey::CharacterBaseKey(KeyBoard *kb, QStackedWidget *swgt)
    : QWidget(kb)
    , md(new PrivateData(kb, swgt))
{
    md->sm = new QSignalMapper(this);
    connect(md->sm, SIGNAL(mapped(const QString &)), SLOT(SlotSetChar(const QString &)));

    if (md->swgt)
    {
        md->swgt->addWidget(this);
    }
}

CharacterBaseKey::~CharacterBaseKey()
{
    delete md->sm;
    md->sm = NULL;

    delete md;
}

void CharacterBaseKey::SlotSetChar(const QString &text)
{
    GetKeyBoard()->SetHanziText(text);
}

void CharacterBaseKey::ConnectMapper(QToolButton *btn, bool justConnect, const QString &s)
{
    connect(btn, SIGNAL(clicked()), md->sm, SLOT(map()));

    if (!justConnect)
    {
        SetMapping(btn, s);
    }
}

void CharacterBaseKey::SetMapping(QToolButton *btn, const QString &s)
{
    if (s.isNull())
        md->sm->setMapping(btn, btn->text());
    else
        md->sm->setMapping(btn, s);
}

void CharacterBaseKey::SlotReturnBtnClicked()
{
    md->keyBoard->GotoPrevKey();
}

void CharacterBaseKey::SlotEnterBtnClicked()
{
    md->keyBoard->GenerateEnterChar();
}

void CharacterBaseKey::SlotBackSpaceBtnClicked()
{
    md->keyBoard->GenerateBackSpaceChar();
}

KeyBoard *CharacterBaseKey::GetKeyBoard() const
{
    return md->keyBoard;
}
