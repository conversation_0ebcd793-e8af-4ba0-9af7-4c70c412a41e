#include "CSysSecondTitleWidget.h"
#include <QVariant>
#include <QBoxLayout>

CSysSecondTitleWidget::CSysSecondTitleWidget(const QStringList &strTextList, int iLabelWidth, QWidget *parent)
    : QWidget(parent)
{
    for(int i=0; i<strTextList.size(); i++)
    {
        QPushButton *pBtn = new QPushButton(strTextList.at(i));
        pBtn->setFixedHeight(56);
        pBtn->setSizePolicy(QSizePolicy::Expanding,QSizePolicy::Preferred);
        pBtn->setObjectName("SysTitleBtn");
        pBtn->setProperty("index", i);
        connect(pBtn, &QPushButton::clicked, this, &CSysSecondTitleWidget::_SlotBtnClicked);
        m_pBtnList.push_back(pBtn);
    }

    m_pLabel = new QLabel;
    m_pLabel->setFixedSize(iLabelWidth, 1);
    m_pLabel->setObjectName("LineLabel");

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(25);
    for(int i=0; i<m_pBtnList.size(); i++)
        pBtnLayout->addWidget(m_pBtnList.at(i));
    pBtnLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addLayout(pBtnLayout);
    pLayout->addWidget(m_pLabel);
    pLayout->addStretch(1);
    this->setLayout(pLayout);

    if(m_pBtnList.size() > 0)
        m_pBtnList.first()->clicked();
}

void CSysSecondTitleWidget::_SlotBtnClicked()
{
    QPushButton *pBtn = dynamic_cast<QPushButton *>(sender());
    if(nullptr == pBtn)
        return;

    int index = pBtn->property("index").toInt();
    for(int i=0; i<m_pBtnList.size(); i++)
    {
        if(index == m_pBtnList.at(i)->property("index").toInt())
            m_pBtnList.at(i)->setEnabled(false);
        else
            m_pBtnList.at(i)->setEnabled(true);
    }
    emit SignalSecondTitle(index);
}
