#ifndef _EAR_U08_COMMAND_RESPONSE_H
#define _EAR_U08_COMMAND_RESPONSE_H
#include "../macros.h"
#include "../interface/IEAR_U08_COMMAND_RESPONSE.h"
#include "../segment/ECD.h"
#include "../segment/ECR.h"

class EAR_U08_COMMAND_RESPONSE : public IEAR_U08_COMMAND_RESPONSE
{

public:

	EAR_U08_COMMAND_RESPONSE();
	~EAR_U08_COMMAND_RESPONSE();

	DECLARE_OBJECTBASE
	BEGIN_IMPL_QUERYIF(EAR_U08_COMMAND_RESPONSE)
		IMPL_QUERYIF_BYPATH(IF_OBJECTBASE, IEAR_U08_COMMAND_RESPONSE, IObjectBase)
		END_IMPL_QUERYIF()

	void Free();

	virtual bool GetECD(IECD* ecd);

	virtual void SetECD(IECD* ecd);

	virtual bool GetECR(IECR* ecr);

	virtual void SetECR(IECR* ecr);

	virtual void GetString(char** str);

private:

	ECD* m_ecd;
	ECR* m_ecr;
};

#endif
