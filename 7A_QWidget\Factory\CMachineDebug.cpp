#include "CMachineDebug.h"
#include <QTime>
#include <QDebug>
#include <QVBoxLayout>

#include "MachineDebug/CMachineCmd.h"
#include "MachineDebug/CSoftUpdate.h"
#include "MachineDebug/CUpdateRecordWidget.h"
#include "MachineDebug/CMachineLogConfig.h"
#include "MachineDebug/CMedianUS.h"

CMachineDebug::CMachineDebug(QWidget *parent) : QWidget(parent)
{
    _InitWidget();
}

CMachineDebug::~CMachineDebug()
{

}

void CMachineDebug::_SlotTitleChanged(int index)
{
    m_pStackedWidget->setCurrentIndex(index);
}

void CMachineDebug::_InitWidget()
{
    QStringList strList = {tr("单指令"), tr("升级"), tr("日志"), tr("超声")};
    m_pCHBtnTitle = new CHBtnTitleWidget(strList);
    m_pCHBtnTitle->SetTitleIndex(0);
    connect(m_pCHBtnTitle, &CHBtnTitleWidget::SignalTitleChanged, this, &CMachineDebug::_SlotTitleChanged);

    m_pCMachineCmd = new CMachineCmd;
    m_pCSoftUpdate = new CSoftUpdate;
    m_pCMachineLogConfig = new CMachineLogConfig;
    m_pCMachineUS = new CMedianUS;

    m_pStackedWidget = new QStackedWidget;
    m_pStackedWidget->addWidget(m_pCMachineCmd);
    m_pStackedWidget->addWidget(m_pCSoftUpdate);
    m_pStackedWidget->addWidget(m_pCMachineLogConfig);
    m_pStackedWidget->addWidget(m_pCMachineUS);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pCHBtnTitle);
    pLayout->addSpacing(20);
    pLayout->addWidget(m_pStackedWidget);
    this->setLayout(pLayout);
}
