#include "CAgeWidget.h"
#include <QBoxLayout>
#include <QListView>
#include <QStyleFactory>

CAgeWidget::CAgeWidget(int iSpacing1, int iSpacing2,
                       QBoxLayout::Direction eDirection, QWidget *parent)
    : QWidget(parent)
{
    m_pLabel = new QLabel(tr("年龄"));

    m_pLineEdit = new CLineEdit;
    m_pLineEdit->setFixedSize(184, 56);

    m_pComboBox = new QComboBox;
    m_pComboBox->setView(new QListView);
    m_pComboBox->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_pComboBox->setMaxVisibleItems(10);
    m_pComboBox->setStyle(QStyleFactory::create("Windows"));
    QStringList strList = {tr("岁"), tr("月"), tr("天")};
    m_pComboBox->addItems(strList);
    m_pComboBox->setFixedSize(107, 56);

    QHBoxLayout *pValueLayout = new QHBoxLayout;
    pValueLayout->setMargin(0);
    pValueLayout->setSpacing(0);
    //pValueLayout->addStretch(1);
    pValueLayout->addWidget(m_pLineEdit);
    pValueLayout->addSpacing(iSpacing2);
    pValueLayout->addWidget(m_pComboBox);
    //pValueLayout->addStretch(1);

    QBoxLayout *pLayout = nullptr;
    if(QBoxLayout::Direction::LeftToRight == eDirection)
        pLayout = new QHBoxLayout;
    else
        pLayout = new QVBoxLayout;

    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    //pLayout->addStretch(1);
    pLayout->addWidget(m_pLabel);
    pLayout->addSpacing(iSpacing1);
    pLayout->addLayout(pValueLayout);
    //pLayout->addStretch(1);
    this->setLayout(pLayout);
}

CAgeWidget::~CAgeWidget()
{

}

void CAgeWidget::ResetLabelSize(int iWidth, int iHeight)
{
    m_pLabel->setFixedSize(iWidth, iHeight);
}

void CAgeWidget::ResetLineEditSize(int iWidth, int iHeight)
{
    m_pLineEdit->setFixedSize(iWidth, iHeight);
}

void CAgeWidget::ResetComboBoxSize(int iWidth, int iHeight)
{
    m_pComboBox->setFixedSize(iWidth, iHeight);
}

QString CAgeWidget::GetAge() const
{
    int iAgeValue = m_pLineEdit->text().toInt();
    if(iAgeValue <= 0)
        return "";

    return QString("%1 %2").arg(iAgeValue).arg(m_pComboBox->currentText());
}

void CAgeWidget::SetAge(const QString &strAge)
{
    QStringList strList = strAge.split(" ");
    if(strList.size() < 2)
    {
        m_pLineEdit->clear();
        m_pComboBox->setCurrentIndex(0);
        return;
    }

    int iAgeValue = strList.at(0).toInt();
    if(iAgeValue > 0)
        m_pLineEdit->setText(strList.at(0));
    else
        m_pLineEdit->clear();
    m_pComboBox->setCurrentText(strList.at(1));
}

CVAgeWidget::CVAgeWidget(int iSpacing1, int iSpacing2, QWidget *parent)
    : CAgeWidget(iSpacing1, iSpacing2, QBoxLayout::Direction::TopToBottom, parent)
{

}

CVAgeWidget::~CVAgeWidget()
{

}

CHAgeWidget::CHAgeWidget(int iSpacing1, int iSpacing2, QWidget *parent)
    : CAgeWidget(iSpacing1, iSpacing2, QBoxLayout::Direction::LeftToRight, parent)
{

}

CHAgeWidget::~CHAgeWidget()
{

}
