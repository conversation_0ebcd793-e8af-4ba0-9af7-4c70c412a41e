#include "CMdxMainWidget.h"
#include <QPixmap>
#include <QBoxLayout>
#include <QDateTime>

#include "CCmdBase.h"
#include "COperationUnit.h"
#include "CMessageBox.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "CScanCodeThread.h"
#include "MDControl/CLisTcpClient.h"

CMdxMainWidget::CMdxMainWidget(QWidget *parent) : QWidget(parent)
{
    this->setFixedSize(G_QRootSize);
    QPalette pale = this->palette();
    pale.setBrush(this->backgroundRole(), QBrush(QPixmap(":/image/ico/main/background.png")));
    this->setPalette(pale);
    this->setAutoFillBackground(true);

    _InitWidget();
    _InitLayout();

    LoadQSS(this, ":/qss/qss/main.qss");

    m_pCurrentTimer = new QTimer(this);
    connect(m_pCurrentTimer, &QTimer::timeout, this, &CMdxMainWidget::_SlotUpdateCurrentTime);
    m_pCurrentTimer->start(1000);

    m_pUDiskTimer = new QTimer(this);
    connect(m_pUDiskTimer, &QTimer::timeout, this, &CMdxMainWidget::_SlotUpdateUDiskTime);
    m_pUDiskTimer->start(1000);

    m_pTitleBtnList.at(0)->clicked();

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalRunLog, this, &CMdxMainWidget::SlotSetRunLog);

    m_bWiFiState = false;
    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalWiFiStatus, this, &CMdxMainWidget::_SlotWiFiState);

    m_bEth1State = false;
    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalEth1Status, this, &CMdxMainWidget::_SlotEth1State);

    m_bLisState = false;
    connect(&CLisTcpClient::GetInstace(), &CLisTcpClient::SignalLisState, this, &CMdxMainWidget::_SlotLisState);
    _SlotLisState(CLisTcpClient::GetInstace().GetConnect());
}

CMdxMainWidget::~CMdxMainWidget()
{
    m_pCPowerPage->deleteLater();
}

void CMdxMainWidget::Set2MainPage()
{
    m_pRunLogLabel->clear();

    m_pTitleBtnList.at(0)->clicked();
}

void CMdxMainWidget::showEvent(QShowEvent *pEvent)
{
    m_pRunLogLabel->clear();
    m_pUserNameLabel->setText(CPublicConfig::GetInstance()->GetLoginUser());

    QWidget::showEvent(pEvent);
}

void CMdxMainWidget::_SlotWiFiState(bool bConnect)
{
    if(m_bWiFiState == bConnect)
        return;

    qDebug()<<"WiFi状态变化:"<<m_bWiFiState<<bConnect;
    m_bWiFiState = bConnect;
    if(m_bWiFiState)
        m_pWifiLabel->setPixmap(QPixmap(":/image/ico/main/wifi.png"));
    else
        m_pWifiLabel->setPixmap(QPixmap(":/image/ico/main/wifi_close.png"));

    if(!m_bWiFiState && !m_bEth1State)
    {
        CLisTcpClient::GetInstace().SetConnect(false);
        m_pLisLabel->setPixmap(QPixmap(":/image/ico/main/lis_close.png"));
    }
}

void CMdxMainWidget::_SlotEth1State(bool bConnect)
{
    if(m_bEth1State == bConnect)
        return;

    qDebug()<<"Eth状态变化:"<<m_bEth1State<<bConnect;
    m_bEth1State = bConnect;
    if(m_bEth1State)
        m_pEth0Label->setPixmap(QPixmap(":/image/ico/main/eth0.png"));
    else
        m_pEth0Label->setPixmap(QPixmap(":/image/ico/main/eth0_close.png"));

    if(!m_bWiFiState && !m_bEth1State)
    {
        CLisTcpClient::GetInstace().SetConnect(false);
        m_pLisLabel->setPixmap(QPixmap(":/image/ico/main/lis_close.png"));
    }
}

void CMdxMainWidget::_SlotLisState(bool bConnect)
{
    if(m_bLisState == bConnect)
        return;

    bool bNetwork = m_bWiFiState || m_bEth1State;
    qDebug()<<"Lis状态变化:"<<m_bLisState<<bConnect<<",eth:"<<m_bEth1State<<",wifi:"<<m_bWiFiState<<",network:"<<bNetwork;
    m_bLisState = bConnect;
    if(m_bLisState)
        m_pLisLabel->setPixmap(QPixmap(":/image/ico/main/lis.png"));
    else
        m_pLisLabel->setPixmap(QPixmap(":/image/ico/main/lis_close.png"));

#ifdef Q_OS_WIN
    return;
#endif

    if(!bNetwork)
        m_pLisLabel->setPixmap(QPixmap(":/image/ico/main/lis_close.png"));
}

void CMdxMainWidget::SlotSetRunLog(const QString &strLog)
{
    if(CPublicConfig::GetInstance()->GetLoginLevel() >= eUser_Factory)
    {
        if(m_pRunLogLabel)
            m_pRunLogLabel->setText(strLog);
    }
}

void CMdxMainWidget::_SlotUpdateCurrentTime()
{
    QString strFormat = CPublicConfig::GetInstance()->GetTimeFormatString();
    QString strCurrentTime = QDateTime::currentDateTime().toString(strFormat);
    m_pTimerLabel->setText(strCurrentTime);
}

void CMdxMainWidget::_SlotUpdateUDiskTime()
{
    QDir dir(GetUDiskDir());
    if(dir.exists())
    {
        m_pUDiskTimer->stop();
        //Delay_MSec(1500);
        m_pUDiskTimer->start(1000);
    }

    m_pUDiskLabel->setVisible(dir.exists());
}

void CMdxMainWidget::_SlotTitleBtn()
{
    //防止连点出现多次弹窗
    static QElapsedTimer timer;
    if(timer.isValid() && timer.elapsed() < 500)
        return;
    timer.start();

    QPushButton *pBtn = dynamic_cast<QPushButton *>(sender());
    if(nullptr == pBtn)
        return;

    int index = pBtn->property("index").toInt();
    if(index > 0 && 1 == m_pCHomePage->GetCurrentPage())
    {
        int iBtnType = ShowQuestion((QWidget*)gk_pMainWindow, tr("提示"), tr("离开此页面将取消测试，确定取消吗"));
        if(QMessageBox::Yes != iBtnType)
            return;

        int iMachineID = m_pCHomePage->GetCurrentMachineID();
        QString strCmd = CCmdBase::GetJsonCmdString(Method_stop_identify);
        COperationUnit::GetInstance()->SendJsonText(iMachineID, Method_stop_identify, strCmd);

        CScanCodeThread::GetInstance()->StopScan();
        m_pCHomePage->Set2DevicePage();
    }

    for(int i=0; i<m_pTitleBtnList.size(); i++)
    {
        if(index == i)
        {
            m_pTitleBtnList.at(i)->setEnabled(false);
            m_pTitleLabelList.at(i)->setProperty("select", true);
            m_pTitleLabelList.at(i)->style()->polish(m_pTitleLabelList.at(i));
        }
        else
        {
            m_pTitleBtnList.at(i)->setEnabled(true);
            m_pTitleLabelList.at(i)->setProperty("select", false);
            m_pTitleLabelList.at(i)->style()->polish(m_pTitleLabelList.at(i));
        }
    }

    m_pStackWidget->setCurrentIndex(index);
}

void CMdxMainWidget::_SlotPowerBtn()
{
    //防止连点出现多次弹窗
    static QElapsedTimer timer;
    if(timer.isValid() && timer.elapsed() < 500)
        return;
    timer.start();

    if(1 == m_pCHomePage->GetCurrentPage())
    {
        int iBtnType = ShowQuestion((QWidget*)gk_pMainWindow, tr("提示"), tr("离开此页面将取消测试，确定取消吗"));
        if(QMessageBox::Yes != iBtnType)
            return;

        m_pCHomePage->Set2DevicePage();
    }

    m_pCPowerPage->raise();
    m_pCPowerPage->showNormal();
    m_pCPowerPage->activateWindow();
}

void CMdxMainWidget::_SlotLogout()
{
    emit SignalLogout();
    m_pCSystemPage->GotoMainPage();
}

void CMdxMainWidget::_SlotGotoRunLogPage()
{
    m_pRunLogLabel->clear();
    if(CPublicConfig::GetInstance()->GetLoginLevel() >= eUser_Factory)
    {
        emit m_pTitleBtnList.at(3)->clicked();
        m_pCSystemPage->GotoRunLogPage();
    }
}

void CMdxMainWidget::_SlotCreateQCTest(int iMachineID,QString strQCModel)
{
    emit m_pTitleBtnList.at(0)->clicked();
    m_pCHomePage->CreateQCTest(iMachineID,strQCModel);
}

QPushButton *CMdxMainWidget::_CreatePowerBtn()
{
    QLabel *pIconLabel = new QLabel;
    pIconLabel->setFixedSize(32, 32);
    pIconLabel->setPixmap(QPixmap(":/image/ico/main/power.png"));

    QLabel *pTextLabel = new QLabel(tr("关机"));
    pTextLabel->setObjectName("PowerLabel");

    int iWidth = 100, iHeight = 80;
    if(eLanguage_English == gk_iLanguage)
    {
        iWidth = 110;
        iHeight = 80;
    }
    else if(eLanguage_German == gk_iLanguage || eLanguage_Italian == gk_iLanguage)
    {
        iWidth = 120;
        iHeight = 85;
    }

    QPushButton *pBtn = new QPushButton;
    pBtn->setFixedSize(iWidth, iHeight);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(pIconLabel, 0, Qt::AlignHCenter);
    pLayout->addSpacing(5);
    pLayout->addWidget(pTextLabel, 0, Qt::AlignHCenter);
    pLayout->addStretch(1);
    pBtn->setLayout(pLayout);
    return pBtn;
}

QGroupBox *CMdxMainWidget::_CreateLeftGroupBox()
{
    m_pLogoLabel = new QLabel;
    m_pLogoLabel->setFixedSize(117, 30);
    m_pLogoLabel->setPixmap(QPixmap(":/image/ico/main/logo.png"));

    QStringList strTitleTextList = {tr("主页"), tr("质控"), tr("历史"), tr("系统")};
    QStringList strBtnObjNameList = {"HomeBtn", "QCBtn", "HistoryBtn", "SystemBtn"};

    for(int i=0; i<4; i++)
    {
        QPushButton *pBtn = new QPushButton;
        pBtn->setFixedSize(60, 60);
        pBtn->setProperty("index", i);
        pBtn->setObjectName(strBtnObjNameList.at(i));
        connect(pBtn, &QPushButton::clicked, this, &CMdxMainWidget::_SlotTitleBtn);
        m_pTitleBtnList.push_back(pBtn);

        QLabel *pLabel = new QLabel(strTitleTextList.at(i));
        pLabel->setFixedHeight(36);
        pLabel->setProperty("index", i);
        pLabel->setProperty("select", false);
        pLabel->setObjectName("MainTitleLabel");
        m_pTitleLabelList.push_back(pLabel);
    }

    m_pPowerBtn = _CreatePowerBtn();
    m_pPowerBtn->setObjectName("PowerBtn");
    connect(m_pPowerBtn, &QPushButton::clicked, this, &CMdxMainWidget::_SlotPowerBtn);

    QVBoxLayout *pLeftLayout = new QVBoxLayout;
    pLeftLayout->setMargin(0);
    pLeftLayout->setSpacing(0);
    pLeftLayout->addSpacing(30);
    pLeftLayout->addWidget(m_pLogoLabel, 0, Qt::AlignHCenter);
    pLeftLayout->addSpacing(85);
    for(int i=0; i<m_pTitleBtnList.size(); i++)
    {
        pLeftLayout->addWidget(m_pTitleBtnList.at(i), 0, Qt::AlignHCenter);
        pLeftLayout->addSpacing(8);
        pLeftLayout->addWidget(m_pTitleLabelList.at(i), 0, Qt::AlignHCenter);
        pLeftLayout->addSpacing(60);
    }
    pLeftLayout->addSpacing(150);
    pLeftLayout->addWidget(m_pPowerBtn, 0, Qt::AlignHCenter);
    pLeftLayout->addStretch(1);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(180, 1016);
    pGroupBox->setObjectName("TitleGroupBox");
    pGroupBox->setLayout(pLeftLayout);
    return pGroupBox;
}

QGroupBox *CMdxMainWidget::_CreateRightGroupBox()
{
    m_pCHomePage = new CHomePage;
    m_pCHistoryPage = new CHistoryPage;
    m_pCQCPage = new CQCPage;
    m_pCSystemPage = new CSystemPage;

    connect(m_pCQCPage, &CQCPage::SignalCreateQCTest, this, &CMdxMainWidget::_SlotCreateQCTest);

    m_pStackWidget = new QStackedWidget;
    m_pStackWidget->setFixedSize(1684, 960);

    m_pStackWidget->addWidget(m_pCHomePage);
    m_pStackWidget->addWidget(m_pCQCPage);
    m_pStackWidget->addWidget(m_pCHistoryPage);
    m_pStackWidget->addWidget(m_pCSystemPage);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pStackWidget, 0, Qt::AlignCenter);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(1740, 1016);
    pGroupBox->setObjectName("MainGroupBox");
    pGroupBox->setLayout(pLayout);

    return pGroupBox;
}

void CMdxMainWidget::_InitWidget()
{
    m_pCPowerPage = new CPowerPage(this);
    connect(m_pCPowerPage, &CPowerPage::SignalLogout, this, &CMdxMainWidget::_SlotLogout);
    m_pCPowerPage->setVisible(false);

    m_pUserIconLabel = new QLabel;
    m_pUserIconLabel->setFixedSize(32, 32);
    m_pUserIconLabel->setPixmap(QPixmap(":/image/ico/main/user.png"));

    m_pUserNameLabel = new QLabel;
    m_pUserNameLabel->setFixedHeight(32);
    m_pUserNameLabel->setObjectName("UserLabel");

    m_pRunLogLabel = new CPressLabel;
    m_pRunLogLabel->setFixedSize(950, 32);
    m_pRunLogLabel->setObjectName("RunLogLabel");
    connect(m_pRunLogLabel, &CPressLabel::SignalPressEvent, this, &CMdxMainWidget::_SlotGotoRunLogPage);

    m_pUDiskLabel = new QLabel;
    m_pUDiskLabel->setFixedSize(32, 32);
    m_pUDiskLabel->setPixmap(QPixmap(":/image/ico/main/udisk.png"));

    m_pEth0Label = new QLabel;
    m_pEth0Label->setFixedSize(32, 32);
    m_pEth0Label->setPixmap(QPixmap(":/image/ico/main/eth0_close.png"));

    m_pWifiLabel = new QLabel;
    m_pWifiLabel->setFixedSize(32, 32);
    m_pWifiLabel->setPixmap(QPixmap(":/image/ico/main/wifi_close.png"));

    m_pLisLabel = new QLabel;
    m_pLisLabel->setFixedSize(32, 32);
    m_pLisLabel->setPixmap(QPixmap(":/image/ico/main/lis_close.png"));

    m_pPrinterLabel = new QLabel;
    m_pPrinterLabel->setFixedSize(32, 32);
    m_pPrinterLabel->setPixmap(QPixmap(":/image/ico/main/printer_close.png"));

    m_pTimerLabel = new QLabel;
    m_pTimerLabel->setFixedHeight(32);
    m_pTimerLabel->setObjectName("WhiteTextLabel");
}

void CMdxMainWidget::_InitLayout()
{
    QHBoxLayout *pDownLayout = new QHBoxLayout;
    pDownLayout->setMargin(0);
    pDownLayout->setSpacing(0);
    pDownLayout->addSpacing(204);
    pDownLayout->addWidget(m_pUserIconLabel);
    pDownLayout->addSpacing(10);
    pDownLayout->addWidget(m_pUserNameLabel);
    pDownLayout->addSpacing(20);
    pDownLayout->addWidget(m_pRunLogLabel);
    pDownLayout->addStretch(1);
    pDownLayout->addWidget(m_pUDiskLabel);
    pDownLayout->addSpacing(20);
    pDownLayout->addWidget(m_pEth0Label);
    pDownLayout->addSpacing(20);
    pDownLayout->addWidget(m_pWifiLabel);
    pDownLayout->addSpacing(20);
    pDownLayout->addWidget(m_pLisLabel);
    pDownLayout->addSpacing(25);
    pDownLayout->addWidget(m_pTimerLabel);
    pDownLayout->addSpacing(20);

    QHBoxLayout *pUpLayout = new QHBoxLayout;
    pUpLayout->setMargin(0);
    pUpLayout->setSpacing(0);
    pUpLayout->addWidget(_CreateLeftGroupBox());
    pUpLayout->addWidget(_CreateRightGroupBox());

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addLayout(pUpLayout);
    pLayout->addStretch(1);
    pLayout->addLayout(pDownLayout);
    pLayout->addSpacing(15);
    this->setLayout(pLayout);
}
