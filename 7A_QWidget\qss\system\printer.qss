QLabel
{
    color: #353E4E;
    font-size: 24px;
    font-weight: 400;
    font-family: "Source Han Sans CN";
}

QLabel#LineLabel
{
   background-color: #D6DAEC;
}

QLabel#BackgroundLabel
{
   border-radius: 32px;
   background-color: #FFF;
}

/*一级标题*/
QLabel#SysTitleLabel1
{
    color: #6B788F;
    font-size: 24px;
    font-weight: 400;
    font-family: "Source Han Sans CN";
}

/*一级标题*/
QLabel#SysTitleLabel2
{
    color: #353E4E;
    font-size: 24px;
    font-weight: 500;
    font-family: "Source Han Sans CN";
}

QTextEdit
{
   color: #6B788F;
   font-size: 22px;
   font-family: "Source Han Sans CN";
   padding-left: 20px;
   border-radius: 24px;
   background-color: #F3F8FF;
}

QLineEdit
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    padding-left: 20px;
    border-radius: 28px;
    border: 0px solid #A1ABBB;
    background-color: #F3F8FF;
}
QScrollBar:vertical
{
    width: 30px;
    background: #F0F0F0;
    padding-top: 30px;
    padding-bottom: 30px;
}

QScrollBar::handle:vertical
{
    width: 30px;
    background: #B6B6B6;
    min-height: 35px;
}
