#include "CLogDB.h"

#include <QDateTime>
#include "PublicConfig.h"

CLogDB &CLogDB::instance()
{
    static CLogDB logDB;
    return logDB;
}

bool CLogDB::RealAllFaultLogData(QList<QStringList> &strList)
{
    QString strCmd = "select * from faultlog";
    return _QueryDB(strCmd, strList);
}

int CLogDB::GetFaultLogAllRecordCount()
{
    QString strCmd = "select count(*) from faultlog";
    QList<QStringList> strList;
    if(_QueryDB(strCmd, strList))
        return _GetFirstValue(strList).toInt();

    return 0;
}

int CLogDB::GetFaultLogQueryRecordCount(const QString &strType, const QString &strParam, bool bLike)
{
    if(strType.isEmpty() || strParam.isEmpty())
        return 0;

    QString strCmd = QString("select count(*) from faultlog ");
    if(!bLike)
    {
        strCmd += QString("where %1 = '%2'").arg(strType).arg(strParam);
    }
    else
    {
        strCmd += QString("where %1 like '%%2%'").arg(strType).arg(strParam);
    }
    QList<QStringList> strList;
    if(_QueryDB(strCmd, strList))
        return _GetFirstValue(strList).toInt();

    return 0;
}

bool CLogDB::AddFaultLog(const QStringList &strDataList)
{
    if(9 != strDataList.size())
    {
        m_strLastError = "添加数组内容长度不为9";
        return false;
    }

    QString strCmd = QString("insert into faultlog(Date,FaultCode,FaultLevel,Machine,FaultUnit,"
                             "FaultDescribe,FactoryDescribe,FaultHandle,Remarks)"
                             " values('%1','%2','%3','%4','%5','%6','%7','%8','%9')")
            .arg(strDataList.at(0)).arg(strDataList.at(1)).arg(strDataList.at(2))
            .arg(strDataList.at(3)).arg(strDataList.at(4)).arg(strDataList.at(5))
            .arg(strDataList.at(6)).arg(strDataList.at(7)).arg(strDataList.at(8));
    return _ExecuteDB(strCmd);
}

bool CLogDB::GetOnePageFaultLog(int iPage, int iOffset, QList<QStringList> &strReadList)
{
    if(iPage < 0 || iOffset < 0)
    {
        m_strLastError = "传入参数错误";
        return false;
    }
    QString strCmd = QString("select * from faultlog order by id desc limit %1,%2").arg(iPage * iOffset).arg(iOffset);
    return _QueryDB(strCmd, strReadList);
}

bool CLogDB::GetQueryOnePageFaultLog(const QString &strType, const QString &strParam,
                                     int iPage, int iOffset,
                                     bool bLike, QList<QStringList> &strReadList)
{
    if(strType.isEmpty() || strParam.isEmpty())
        return false;

    QString strCmd = QString("select * from faultlog ");
    if(!bLike)
    {
        strCmd += QString("where %1 = '%2' ").arg(strType).arg(strParam);
    }
    else
    {
        strCmd += QString("where %1 like '%%2%' ").arg(strType).arg(strParam);
    }
    strCmd += QString("order by id desc limit %1,%2").arg(iPage * iOffset).arg(iOffset);
    return _QueryDB(strCmd, strReadList);
}

bool CLogDB::DeleteAllFaultLogAndResetID()
{
    QStringList strCmdList = {"delete from faultlog", "delete from sqlite_sequence where name ='faultlog'"};
    return _ExecuteDB(strCmdList);
}

bool CLogDB::ReadFaultLogLevel4(QList<QStringList> &strList)
{
    QString strCmd = "select * from faultlog where FaultLevel >= '4' order by id desc";
    return _QueryDB(strCmd, strList);
}

bool CLogDB::AddLoginLog(int iLogType, QString strUser, QString strLogText, QString strMachineCode)
{
    QString strDate = QDate::currentDate().toString("yyyy-MM-dd");
    QString strTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    QString strCmd = QString("insert into login(Date, Type, User, Time, Code, Log) values('%1', '%2', '%3', '%4', '%5', '%6')")
            .arg(strDate).arg(iLogType).arg(strUser).arg(strTime).arg(strMachineCode).arg(strLogText);
    return _ExecuteDB(strCmd);
}

bool CLogDB::ReadAllLoginLog(QList<QStringList> &strReadList)
{
    QString strCmd = QString("select * from login");
    return _QueryDB(strCmd, strReadList);
}

bool CLogDB::ReadLoginLog(QString strBeginTime, QString strEndTime, QList<QStringList> &strReadList)
{
    QString strCmd = QString("select * from login where Date between '%1' and '%2'").arg(strBeginTime).arg(strEndTime);
    return _QueryDB(strCmd, strReadList);
}

bool CLogDB::AddOperationLog(QString strUser, QString strLog, EOperationLogType eLogType)
{
    QString strDateTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    QString strCmd = QString("insert into operation(User, Time, Log, Type) values('%1', '%2', '%3', '%4')")
            .arg(strUser).arg(strDateTime).arg(strLog).arg(eLogType);
    bool bExe = _ExecuteDB(strCmd);
    emit SignalRefreshOperationLog();
    return bExe;
}

bool CLogDB::ReadAllOperationLog(QList<QStringList> &strList)
{
    QString strCmd = "select * from operation";
    return _QueryDB(strCmd, strList);
}

bool CLogDB::ReadAllOperationLog(QString strUser, bool bReview, QList<QStringList> &strList)
{
    QString strAdd;
    if(gk_strAdminUserName == strUser)
        strAdd = "where User not in ('factory', 'maintian', 'FLY')";
    else if(gk_strFactoryUserName == strUser)
        strAdd.clear();
    else if(gk_strFlyUserName == strUser)
        strAdd.clear();
    else if(gk_strMaintianUserName == strUser)
        strAdd.clear();
    else
        strAdd = QString("where User = '%1'").arg(strUser);

    if(false == bReview)
    {
        if(strAdd.isEmpty())
            strAdd += "where Type != '2'";
        else
            strAdd += " and Type != '2'";
    }

    QString strCmd = "select * from operation " + strAdd + " order by id desc";
    return _QueryDB(strCmd, strList);
}

int CLogDB::GetOperationLogAllCount()
{
    QString strCmd = "select count(*) from operation";
    QList<QStringList> strList;
    if(_QueryDB(strCmd, strList))
        return _GetFirstValue(strList).toInt();

    return 0;
}

int CLogDB::GetOperationLogQueryCount(QString strType, QString strParam, bool bLike)
{
    if(strType.isEmpty() || strParam.isEmpty())
        return 0;

    QString strCmd = QString("select count(*) from operation ");
    if(!bLike)
    {
        strCmd += QString("where %1 = '%2'").arg(strType).arg(strParam);
    }
    else
    {
        strCmd += QString("where %1 like '%%2%'").arg(strType).arg(strParam);
    }
    QList<QStringList> strList;
    if(_QueryDB(strCmd, strList))
        return _GetFirstValue(strList).toInt();

    return 0;
}

bool CLogDB::GetOnePageOperationLog(int iPage, int iOffset, QList<QStringList> &strReadList)
{
    if(iPage < 0 || iOffset < 0)
    {
        m_strLastError = "传入参数错误";
        return false;
    }
    QString strCmd = QString("select * from operation order by id desc limit %1,%2").arg(iPage * iOffset).arg(iOffset);
    return _QueryDB(strCmd, strReadList);
}

bool CLogDB::GetQueryOnePageOperationLog(QString strType, QString strParam, int iPage, int iOffset,
                                         bool bLike, QList<QStringList> &strReadList)
{
    if(strType.isEmpty() || strParam.isEmpty())
        return false;

    QString strCmd = QString("select * from operation ");
    if(!bLike)
    {
        strCmd += QString("where %1 = '%2' ").arg(strType).arg(strParam);
    }
    else
    {
        strCmd += QString("where %1 like '%%2%' ").arg(strType).arg(strParam);
    }
    strCmd += QString("order by id desc limit %1,%2").arg(iPage * iOffset).arg(iOffset);
    return _QueryDB(strCmd, strReadList);
}

CLogDB::CLogDB()
    : CSqliteDBBase(CPublicConfig::GetInstance()->GetLogDBPath(), gk_strLogDBConnect)
{
    _ExecuteDB("drop table loginlog"); //旧表移除

    _InitFaultLogTable();
    _InitLoginLogTable();
    _InitOperationLogTable();
}

void CLogDB::_InitFaultLogTable()
{
    QString strCmd = "create table if not exists faultlog ("
                     "id integer not null primary key autoincrement,"
                     "Date varchar,"
                     "FaultCode varchar,"
                     "FaultLevel varchar,"
                     "Machine varchar,"
                     "FaultUnit varchar,"
                     "FaultDescribe varchar,"
                     "FactoryDescribe varchar,"
                     "FaultHandle varchar,"
                     "Remarks varchar)";
    _ExecuteDB(strCmd);
}

void CLogDB::_InitLoginLogTable()
{
    QString strCmd = "create table if not exists login ("
                     "id integer not null primary key autoincrement,"
                     "Date varchar,"
                     "Type varchar,"
                     "User varchar,"
                     "Time varchar,"
                     "Code varchar,"
                     "Log varchar)";
    _ExecuteDB(strCmd);
}

void CLogDB::_InitOperationLogTable()
{
    QString strCmd = "create table if not exists operation ("
                     "id integer not null primary key autoincrement,"
                     "User varchar,"
                     "Time varchar,"
                     "Log varchar,"
                     "Type varchar,"  //Login;Export;Review
                     "Remarks varchar)";
    _ExecuteDB(strCmd);
}

CLogDB::~CLogDB()
{

}
