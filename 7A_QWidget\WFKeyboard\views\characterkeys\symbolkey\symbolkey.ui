<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Symbolkey</class>
 <widget class="QWidget" name="Symbolkey">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>808</width>
    <height>296</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>10</number>
   </property>
   <item row="1" column="0">
    <widget class="QToolButton" name="toolButton_00">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="text">
      <string notr="true">,</string>
     </property>
    </widget>
   </item>
   <item row="1" column="1">
    <widget class="QToolButton" name="toolButton_01">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string notr="true">.</string>
     </property>
    </widget>
   </item>
   <item row="1" column="2">
    <widget class="QToolButton" name="toolButton_02">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string notr="true">?</string>
     </property>
    </widget>
   </item>
   <item row="1" column="3">
    <widget class="QToolButton" name="toolButton_03">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string notr="true">!</string>
     </property>
    </widget>
   </item>
   <item row="1" column="4">
    <widget class="QToolButton" name="toolButton_04">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string notr="true">-</string>
     </property>
    </widget>
   </item>
   <item row="1" column="5">
    <widget class="QToolButton" name="toolButton_05">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string notr="true">...</string>
     </property>
    </widget>
   </item>
   <item row="1" column="6">
    <widget class="QToolButton" name="toolButton_PageUp">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="text">
      <string notr="true"/>
     </property>
     <property name="icon">
      <iconset>
       <normalon>:/svg/pre.svg</normalon>
      </iconset>
     </property>
     <property name="iconSize">
      <size>
       <width>35</width>
       <height>35</height>
      </size>
     </property>
    </widget>
   </item>
   <item row="2" column="0">
    <widget class="QToolButton" name="toolButton_06">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string notr="true">@</string>
     </property>
    </widget>
   </item>
   <item row="2" column="1">
    <widget class="QToolButton" name="toolButton_07">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string notr="true">:</string>
     </property>
    </widget>
   </item>
   <item row="2" column="2">
    <widget class="QToolButton" name="toolButton_08">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string notr="true">;</string>
     </property>
    </widget>
   </item>
   <item row="2" column="3">
    <widget class="QToolButton" name="toolButton_09">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string notr="true">&amp;&amp;</string>
     </property>
    </widget>
   </item>
   <item row="2" column="4">
    <widget class="QToolButton" name="toolButton_10">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string notr="true">^</string>
     </property>
    </widget>
   </item>
   <item row="2" column="5">
    <widget class="QToolButton" name="toolButton_11">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string notr="true">~</string>
     </property>
    </widget>
   </item>
   <item row="2" column="6">
    <widget class="QLabel" name="label_CurPage">
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>22</pointsize>
      </font>
     </property>
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Plain</enum>
     </property>
     <property name="text">
      <string notr="true">1/3</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item row="3" column="0">
    <widget class="QToolButton" name="toolButton_12">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string notr="true">'</string>
     </property>
    </widget>
   </item>
   <item row="3" column="1">
    <widget class="QToolButton" name="toolButton_13">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string notr="true">&quot;</string>
     </property>
    </widget>
   </item>
   <item row="3" column="2">
    <widget class="QToolButton" name="toolButton_14">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string notr="true">(</string>
     </property>
    </widget>
   </item>
   <item row="3" column="3">
    <widget class="QToolButton" name="toolButton_15">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string notr="true">)</string>
     </property>
    </widget>
   </item>
   <item row="3" column="4">
    <widget class="QToolButton" name="toolButton_16">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string notr="true">*</string>
     </property>
    </widget>
   </item>
   <item row="3" column="5">
    <widget class="QToolButton" name="toolButton_17">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string notr="true">#</string>
     </property>
    </widget>
   </item>
   <item row="3" column="6">
    <widget class="QToolButton" name="toolButton_PageDown">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="text">
      <string notr="true"/>
     </property>
     <property name="icon">
      <iconset>
       <normalon>:/svg/next.svg</normalon>
      </iconset>
     </property>
     <property name="iconSize">
      <size>
       <width>35</width>
       <height>35</height>
      </size>
     </property>
    </widget>
   </item>
   <item row="4" column="0" colspan="2">
    <widget class="QToolButton" name="toolButton_Return">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="text">
      <string notr="true"/>
     </property>
     <property name="icon">
      <iconset>
       <disabledon>:/svg/back.svg</disabledon>
      </iconset>
     </property>
     <property name="iconSize">
      <size>
       <width>40</width>
       <height>40</height>
      </size>
     </property>
    </widget>
   </item>
   <item row="4" column="2" colspan="3">
    <widget class="QToolButton" name="toolButton_Space">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="text">
      <string>空    格</string>
     </property>
    </widget>
   </item>
   <item row="4" column="5">
    <widget class="QToolButton" name="toolButton_BackSpace">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="text">
      <string notr="true"/>
     </property>
     <property name="iconSize">
      <size>
       <width>24</width>
       <height>24</height>
      </size>
     </property>
    </widget>
   </item>
   <item row="4" column="6">
    <widget class="QToolButton" name="toolButton_Hide">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="text">
      <string notr="true"/>
     </property>
     <property name="icon">
      <iconset>
       <normalon>:/svg/esc.svg</normalon>
      </iconset>
     </property>
     <property name="iconSize">
      <size>
       <width>36</width>
       <height>36</height>
      </size>
     </property>
    </widget>
   </item>
   <item row="0" column="0" colspan="7">
    <widget class="QFrame" name="frame">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>60</height>
      </size>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>10</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QToolButton" name="toolButton_CHS">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="font">
         <font>
          <family>文泉驿微米黑</family>
          <pointsize>30</pointsize>
         </font>
        </property>
        <property name="focusPolicy">
         <enum>Qt::NoFocus</enum>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="text">
         <string>中文</string>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="autoRaise">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QToolButton" name="toolButton_ENG">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="font">
         <font>
          <family>文泉驿微米黑</family>
          <pointsize>30</pointsize>
         </font>
        </property>
        <property name="focusPolicy">
         <enum>Qt::NoFocus</enum>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="text">
         <string>英文</string>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="autoRaise">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QToolButton" name="toolButton_Math">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="font">
         <font>
          <family>文泉驿微米黑</family>
          <pointsize>30</pointsize>
         </font>
        </property>
        <property name="focusPolicy">
         <enum>Qt::NoFocus</enum>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="text">
         <string>数学</string>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
