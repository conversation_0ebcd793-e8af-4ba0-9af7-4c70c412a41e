﻿#include "CFtpDB.h"
#include <QFile>
#include <QApplication>
#include "PublicConfig.h"

CFtpDB *CFtpDB::m_spInstance = nullptr;

CFtpDB *CFtpDB::GetInstance()
{
    if(NULL == m_spInstance)
        m_spInstance = new CFtpDB;
    return m_spInstance;
}

CFtpDB::CFtpDB() : CSqliteDBBase(QApplication::applicationDirPath() + "/db/ftp.db", "ftp_connect")
{
    QString strCmd = "create table if not exists uploadfile ("
                     "id integer not null primary key autoincrement,"
                     "fileName varchar)";
    _ExecuteDB(strCmd);
}

CFtpDB::~CFtpDB()
{

}

void CFtpDB::AddFtpUploadFile(QString strFileName)
{
    if(strFileName.isEmpty() || !QFile::exists(strFileName))
        return;

    if(!CPublicConfig::GetInstance()->GetFtpAutoUpload())
        return;

    QString strCmd = QString("insert into uploadfile(fileName) values('%1')").arg(strFileName);
    _ExecuteDB(strCmd);
}

void CFtpDB::DeleteFtpUploadFile(QString strFileName)
{
    if(strFileName.isEmpty())
        return;

    QString strCmd = QString("delete from uploadfile where fileName = '%1'").arg(strFileName);
    _ExecuteDB(strCmd);
}

void CFtpDB::ReadFtpUploadFile(QStringList &strFileList)
{
    QString strCmd = QString("select fileName from uploadfile");
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);

    strFileList = _GetColumnValueList(strList);
}
