#include "CHistoryItemWidget.h"
#include <QPixmap>
#include <QBoxLayout>
#include <QGridLayout>

#include "CLotInfoDB.h"
#include "CProjectDB.h"
#include "PublicConfig.h"
#include "PublicFunction.h"

CHistoryItemWidget::CHistoryItemWidget(QWidget *parent) : QWidget(parent)
{
    _InitWidget();
    _InitLayout();
}

bool CHistoryItemWidget::GetChecked() const
{
    return m_pCheckBox->isChecked();
}

void CHistoryItemWidget::SetChecked(bool bChecked)
{
    m_pCheckBox->setChecked(bChecked);
}

void CHistoryItemWidget::SetHistoryResult(const SResultInfoStruct &sResultInfo)
{
    m_sResultInfo.Clear();
    m_sSampleInfo.Clear();
    m_sCardInfo.Clear();
    m_sLotInfo.Clear();

    m_sResultInfo = sResultInfo;
    CProjectDB::GetInstance()->GetSampleData(m_sResultInfo.strProjectName, m_sResultInfo.strSampleID, m_sResultInfo.strTestTime, m_sSampleInfo);
    CProjectDB::GetInstance()->GetCardData(m_sResultInfo.strCardID, m_sResultInfo.strTestTime, m_sCardInfo);
    CLotInfoDB::GetInstance()->GetLotInfoByShowName(m_sResultInfo.strProjectName, m_sLotInfo);

    m_pSampleIDLabel->SetValueLabelText(m_sSampleInfo.strSampleID);
    m_pCardIDLabel->SetValueLabelText(m_sCardInfo.strCardID);
    m_pTestTypeLabel->SetValueLabelText(CPublicConfig::GetInstance()->GetTestTypeShowString(m_sResultInfo.strMode));

    QString strDev = QString("%1# ").arg(m_sResultInfo.iMachineID + 1);
    m_pTestStatusLabel->SetValueLabelText(strDev + CPublicConfig::GetInstance()->GetStatusShowString(m_sResultInfo.iStatus));
    m_pTestTimeLabel->SetValueLabelText(m_sResultInfo.strTestTime);
    m_pNameLabel->SetValueLabelText(m_sSampleInfo.strName);
    m_pReviewLabel->setVisible(false);
    m_pProjectLabel->SetValueLabelText(m_sResultInfo.strProjectName);
    if(eTestDone != m_sResultInfo.iStatus)
    {
        m_pIconLabel->setVisible(false);
        return;
    }


    // 下面开始更新结果

    bool bHrmTest =  bHrmTecType(m_sResultInfo.iTestProject);
    bool bReview = (sResultInfo.strReview == "y");
    QString strDBCurveName = m_sLotInfo.strCurveName;
    if(strDBCurveName.endsWith(";"))
    {
        int len = strDBCurveName.length();
        strDBCurveName = strDBCurveName.mid(0, len - 1);
    }
    QStringList strCurveNameList = strDBCurveName.split(";");
    QStringList  strResultList;
    QString strDBResult;
    if(bHrmTest)
    {
        strDBResult = bReview ?m_sResultInfo.strHrmResult_Review : m_sResultInfo.strHrmResult;
    }
    else
    {
        QStringList strCTTmList;
        // 这里应该是审核之后的阴阳性
        // 调试看一下结果
        _GetCtResultList(bReview,strCurveNameList,sResultInfo.strCTInfo,sResultInfo.strCTInfo_Review, sResultInfo.strResult,sResultInfo.strResult_Review,strCTTmList,strResultList);
    }

    qDebug()<<"CHistoryItemWidget::SetHistoryResult"<<m_sResultInfo.iTestProject<<strDBResult;
    if(strResultList.size() <= 1 && !bHrmTest)
    {
        m_pIconLabel->setVisible(false);
        return;
    }

    qDebug()<<"CHistoryItemWidget::SetHistoryResult"<<strCurveNameList<<strResultList;


    int iMin = qMin(strCurveNameList.size(), strResultList.size());
    strCurveNameList = strCurveNameList.mid(0, iMin);
    strResultList = strResultList.mid(0, iMin);

    if(bHrmTest)
    {
        m_pIconLabel->setVisible(false);
        m_pProjectLabel->SetValueLabelText(m_sResultInfo.strProjectName + ": "+ strDBResult);
        return;
    }

    if(IsQCTest(m_sResultInfo.strMode))
    {

        bool bPass = false;
        QString strInfo;
        strInfo = QObject::tr("测试结果：") + GetTestResultText(sResultInfo.strMode,sResultInfo.strResult, m_sLotInfo.strCurveName,bPass);
        QFont font;
        font.setFamily("Source Han Sans CN");
        font.setPixelSize(20);
        strInfo = "(" + strInfo + ")";

        QString strRaw = m_sResultInfo.strProjectName + strInfo;
        QFontMetrics met(font);
        QString strElideText = met.elidedText(strRaw, Qt::ElideRight, 600);
        if(!bPass)
        {
            int index= strElideText.indexOf("(");
            strElideText.insert(index, "  <font color=red>");
            strElideText += "</font>";
        }
        m_pProjectLabel->SetValueLabelText(strElideText);
        m_pIconLabel->setVisible(true);
        if(bPass)
        {
            SetLabelBackImage(m_pIconLabel, ":/image/ico/history/qc-pass.png");
        }
        else
        {
            SetLabelBackImage(m_pIconLabel, ":/image/ico/history/qc-fail.png");
        }


    }
    else {
        if(!bHrmTest)
        {
            bool bAllNegative = true;
            bool bOnePositive = false;
            QString strInfo;
            for(int i=0; i<iMin; i++)
            {
                QString strName = strCurveNameList.at(i);
                if(IsCtrlTarget(strName) || "0" == strName || strName.isEmpty())
                    continue;

                if("P" == strResultList.at(i))
                {
                    bOnePositive = true;
                    bAllNegative = false;
                    strInfo += QString("%1;").arg(strName);
                }
                else if("E" == strResultList.at(i))
                {
                    bAllNegative = false;
                }
            }

            if(bOnePositive)
            {
                QFont font;
                font.setFamily("Source Han Sans CN");
                font.setPixelSize(20);

                strInfo = "(" + tr("阳性：") + strInfo + ")";

                QString strRaw = m_sResultInfo.strProjectName + strInfo;
                QFontMetrics met(font);
                QString strElideText = met.elidedText(strRaw, Qt::ElideRight, 600);
                int index= strElideText.indexOf("(");
                strElideText.insert(index, "  <font color=red>");
                strElideText += "</font>";
                m_pProjectLabel->SetValueLabelText(strElideText);
                m_pIconLabel->setVisible(true);
                SetLabelBackImage(m_pIconLabel, ":/image/ico/history/positive.png");
                return;
            }

            qDebug()<<strDBCurveName<<bAllNegative<<bOnePositive;
            if(bAllNegative)
            {
                m_pProjectLabel->SetValueLabelText(m_sResultInfo.strProjectName);
                m_pIconLabel->setVisible(true);
                SetLabelBackImage(m_pIconLabel, ":/image/ico/history/negative.png");
            }
            else
            {
                m_pProjectLabel->SetValueLabelText(m_sResultInfo.strProjectName);
                m_pIconLabel->setVisible(false);
            }
        }
        else
        {
            // bHrmTest 这些是Hrm显示的结果
            m_pIconLabel->setVisible(false);
            m_pProjectLabel->SetValueLabelText(m_sResultInfo.strProjectName + ": "+ strDBResult);
            return;
            //m_pIconLabel->setVisible(true);
            //SetLabelBackImage(m_pIconLabel, ":/image/ico/history/positive.png");
        }
    }
}

void CHistoryItemWidget::SetHistoryID(int iHistoryID)
{
    SResultInfoStruct sResultInfo;
    CProjectDB::GetInstance()->GetHistoryData(iHistoryID, sResultInfo);
    SetHistoryResult(sResultInfo);
}

int CHistoryItemWidget::GetHistoryID() const
{
    return m_sResultInfo.iHistoryID;
}

void CHistoryItemWidget::_SlotCheckBox()
{
    emit SignalItemChecked(m_sResultInfo.iHistoryID, m_pCheckBox->isChecked());
}

void CHistoryItemWidget::_ResetLabelText(CLabelLabel *pLabel, QString strText, int iWidth)
{
    QFont font;
    font.setFamily("Source Han Sans CN");
    font.setPixelSize(20);
    QFontMetrics met(font);
    QString strNewText = met.elidedText(strText, Qt::ElideRight, iWidth);
    pLabel->SetValueLabelText(strNewText);

    pLabel->SetValueLabelText(strText);
}

void CHistoryItemWidget::_InitWidget()
{
    m_pCheckBox = new QCheckBox;
    m_pCheckBox->setFixedSize(44, 44);
    connect(m_pCheckBox, &QCheckBox::clicked, this, &CHistoryItemWidget::_SlotCheckBox);

    int iHeight = 30;
    int iSpacing = 0;
    int iTestWidth = 220;
    if(eLanguage_English == gk_iLanguage)
    {
        iSpacing = 5;
        iTestWidth = 280;
    }
    else if(eLanguage_Spanish == gk_iLanguage)
    {
        iSpacing = 5;
        iTestWidth = 270;
    }
    else if(eLanguage_German == gk_iLanguage)
    {
        iSpacing = 5;
        iTestWidth = 270;
    }
    else if(eLanguage_Italian == gk_iLanguage)
    {
        iSpacing = 5;
        iTestWidth = 295;
    }

    m_pSampleIDLabel = new CLabelLabel(tr("样本编号："), "", iSpacing);
    m_pSampleIDLabel->setFixedSize(320, iHeight);

    m_pCardIDLabel = new CLabelLabel(tr("试剂卡编号："), "", iSpacing);
    m_pCardIDLabel->setFixedSize(320, iHeight);

    m_pTestTypeLabel = new CLabelLabel(tr("测试类型："), "", iSpacing);
    m_pTestTypeLabel->setFixedSize(iTestWidth, iHeight);

    m_pTestStatusLabel = new CLabelLabel(tr("测试状态："), "", iSpacing);
    m_pTestStatusLabel->setFixedSize(iTestWidth, iHeight);

    m_pTestTimeLabel = new CLabelLabel(tr("时间："), "", iSpacing);
    m_pTestTimeLabel->setFixedSize(270, iHeight);

    m_pNameLabel = new CLabelLabel(tr("姓名："), "", iSpacing);
    m_pNameLabel->setFixedSize(270, iHeight);

    m_pProjectLabel = new CLabelLabel(tr("项目："), "", iSpacing);
    m_pProjectLabel->setFixedSize(700, iHeight);
    m_pProjectLabel->SetValueLabelFixedSize(600, iHeight);

    m_pReviewLabel = new CLabelLabel(tr("审核项："), "", iSpacing);
    m_pReviewLabel->setFixedSize(300, iHeight);
    m_pReviewLabel->setVisible(false);

    m_pIconLabel = new QLabel;
    m_pIconLabel->setFixedSize(40, 40);
    SetLabelBackImage(m_pIconLabel, ":/image/ico/QC/nagetive.png");
    m_pIconLabel->setVisible(false);
}

void CHistoryItemWidget::_InitLayout()
{
    int iSpacing = 70;
    if(eLanguage_Spanish == gk_iLanguage)
        iSpacing = 55;
    else if(eLanguage_Italian == gk_iLanguage)
        iSpacing = 60;

    QHBoxLayout *pHLayout1 = new QHBoxLayout;
    pHLayout1->setMargin(0);
    pHLayout1->setSpacing(0);
    pHLayout1->addWidget(m_pSampleIDLabel);
    pHLayout1->addSpacing(iSpacing);
    pHLayout1->addWidget(m_pTestTypeLabel);
    pHLayout1->addSpacing(iSpacing);
    pHLayout1->addWidget(m_pTestTimeLabel);
    pHLayout1->addSpacing(iSpacing);
    pHLayout1->addWidget(m_pNameLabel);
    pHLayout1->addStretch(1);

    QHBoxLayout *pHLayout2 = new QHBoxLayout;
    pHLayout2->setMargin(0);
    pHLayout2->setSpacing(0);
    pHLayout2->addWidget(m_pCardIDLabel);
    pHLayout2->addSpacing(iSpacing);
    pHLayout2->addWidget(m_pTestStatusLabel);
    pHLayout2->addSpacing(iSpacing);
    pHLayout2->addWidget(m_pProjectLabel);
    pHLayout2->addStretch(1);

    QVBoxLayout *pVLayout = new QVBoxLayout;
    pVLayout->setMargin(0);
    pVLayout->setSpacing(0);
    pVLayout->addStretch(1);
    pVLayout->addLayout(pHLayout1);
    pVLayout->addSpacing(10);
    pVLayout->addLayout(pHLayout2);
    pVLayout->addStretch(1);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addSpacing(24);
    pLayout->addWidget(m_pCheckBox, 0, Qt::AlignVCenter);
    pLayout->addSpacing(32);
    pLayout->addLayout(pVLayout);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pIconLabel);
    pLayout->addSpacing(50);
    this->setLayout(pLayout);
}
