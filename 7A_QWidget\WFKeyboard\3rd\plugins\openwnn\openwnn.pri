INCLUDEPATH =  KeyboardModule/Plugins/openwnn/wnnEngine/include
INCLUDEPATH += KeyboardModule/Plugins/openwnn/wnnDictionary/include
SOURCES += \
    KeyboardModule/Plugins/openwnn/wnnDictionary/engine/ndapi.c \
    KeyboardModule/Plugins/openwnn/wnnDictionary/engine/ndbdic.c \
    KeyboardModule/Plugins/openwnn/wnnDictionary/engine/ndcommon.c \
    KeyboardModule/Plugins/openwnn/wnnDictionary/engine/ndfdic.c \
    KeyboardModule/Plugins/openwnn/wnnDictionary/engine/ndldic.c \
    KeyboardModule/Plugins/openwnn/wnnDictionary/engine/ndrdic.c \
    KeyboardModule/Plugins/openwnn/wnnDictionary/engine/neapi.c \
    KeyboardModule/Plugins/openwnn/wnnDictionary/engine/necode.c \
    KeyboardModule/Plugins/openwnn/wnnDictionary/engine/nj_str.c \
    KeyboardModule/Plugins/openwnn/wnnEngine/WnnJpnDic.c \
    KeyboardModule/Plugins/openwnn/wnnEngine/openwnndictionary.cpp \
    KeyboardModule/Plugins/openwnn/wnnEngine/openwnnenginejajp.cpp \
    KeyboardModule/Plugins/openwnn/wnnEngine/openwnnclauseconverterjajp.cpp \
    KeyboardModule/Plugins/openwnn/wnnEngine/kanaconverter.cpp \
    KeyboardModule/Plugins/openwnn/wnnEngine/composingtext.cpp \
    KeyboardModule/Plugins/openwnn/wnnEngine/letterconverter.cpp \
    KeyboardModule/Plugins/openwnn/wnnEngine/romkan.cpp \
    KeyboardModule/Plugins/openwnn/wnnEngine/romkanfullkatakana.cpp \
    KeyboardModule/Plugins/openwnn/wnnEngine/romkanhalfkatakana.cpp \
    KeyboardModule/Plugins/openwnn/wnnEngine/wnnlookuptable.cpp \
    $$PWD/openwnninputmethod.cpp

HEADERS += \
    KeyboardModule/Plugins/openwnn/wnnDictionary/include/nj_dic.h \
    KeyboardModule/Plugins/openwnn/wnnDictionary/include/nj_err.h \
    KeyboardModule/Plugins/openwnn/wnnDictionary/include/nj_ext.h \
    KeyboardModule/Plugins/openwnn/wnnDictionary/include/nj_lib.h \
    KeyboardModule/Plugins/openwnn/wnnDictionary/include/njd.h \
    KeyboardModule/Plugins/openwnn/wnnDictionary/include/njx_lib.h \
    KeyboardModule/Plugins/openwnn/wnnEngine/predef_table.h \
    KeyboardModule/Plugins/openwnn/wnnEngine/include/openwnndictionary.h \
    KeyboardModule/Plugins/openwnn/wnnEngine/include/openwnnenginejajp.h \
    KeyboardModule/Plugins/openwnn/wnnEngine/include/openwnnclauseconverterjajp.h \
    KeyboardModule/Plugins/openwnn/wnnEngine/include/wnnword.h \
    KeyboardModule/Plugins/openwnn/wnnEngine/include/kanaconverter.h \
    KeyboardModule/Plugins/openwnn/wnnEngine/include/composingtext.h \
    KeyboardModule/Plugins/openwnn/wnnEngine/include/strsegment.h \
    KeyboardModule/Plugins/openwnn/wnnEngine/include/letterconverter.h \
    KeyboardModule/Plugins/openwnn/wnnEngine/include/romkan.h \
    KeyboardModule/Plugins/openwnn/wnnEngine/include/romkanfullkatakana.h \
    KeyboardModule/Plugins/openwnn/wnnEngine/include/romkanhalfkatakana.h \
    KeyboardModule/Plugins/openwnn/wnnEngine/include/wnnlookuptable.h \
    $$PWD/openwnninputmethod_p.h
