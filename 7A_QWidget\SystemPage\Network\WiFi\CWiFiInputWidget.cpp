﻿#include "CWiFiInputWidget.h"
#include <QPainter>
#include <QBoxLayout>
#include "CMessageBox.h"
#include "PublicParams.h"
#include "PublicFunction.h"

CWiFiInputWidget::CWiFiInputWidget(QWidget *parent) : QWidget(parent)
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setFixedSize(1684, 958);
    this->setAttribute(Qt::WA_TranslucentBackground);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroupBox(), 0, Qt::AlignHCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);

    LoadQSS(this, ":/qss/qss/system/network.qss");
}

void CWiFiInputWidget::SetWiFiName(QString strWiFiName)
{
    m_pNameLineEdit->SetLineEditText(strWiFiName);
}

void CWiFiInputWidget::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, gk_iBackTransparency));
    QWidget::paintEvent(pEvent);
}

void CWiFiInputWidget::_SlotCancelBtn()
{
    m_pNameLineEdit->SetLineEditText("");
    m_pPwdLineEdit->SetLineEditText("");
    this->close();
}

void CWiFiInputWidget::_SlotConfirmBtn()
{
    QString strName = m_pNameLineEdit->GetLineEditText();
    QString strPwd = m_pPwdLineEdit->GetLineEditText();
    if(strName.isEmpty())
    {
        QWidget *pWidget = nullptr;
        if(this->parentWidget())
            pWidget = this->parentWidget()->parentWidget();
        ShowInformation(pWidget, tr("提示"), tr("请输入WiFi名称"));
        return;
    }

    emit SignalConnect(strName, strPwd);
    this->close();
    m_pNameLineEdit->SetLineEditText("");
    m_pPwdLineEdit->SetLineEditText("");
}

QGroupBox *CWiFiInputWidget::_CreateGroupBox()
{
    m_pTitleWidget = new CHLabelTitleWidget(tr("连接WiFi"));

    int iLabelWidth = 110, iEditWidth = 280;
    if(eLanguage_English == gk_iLanguage)
        iLabelWidth = 170;
    else if(eLanguage_Spanish == gk_iLanguage)
        iLabelWidth = 140;
    else if(eLanguage_German == gk_iLanguage)
        iLabelWidth = 120;
    else if(eLanguage_Italian == gk_iLanguage)
        iLabelWidth = 120;

    QString strQss = "QLineEdit{padding-left: 20px;}";
    m_pNameLineEdit = new CHLabelLineEdit(tr("WiFi名称："));
    m_pNameLineEdit->ResetLabelSize(iLabelWidth, 56);
    m_pNameLineEdit->ResetLineEditSize(iEditWidth, 56);
    m_pNameLineEdit->setFixedWidth(iLabelWidth + iEditWidth +16);
    m_pNameLineEdit->setStyleSheet(strQss);
    m_pNameLineEdit->setEnabled(false);

    m_pPwdLineEdit = new CHLabelLineEdit(tr("WiFi密码："));
    m_pPwdLineEdit->ResetLabelSize(iLabelWidth, 56);
    m_pPwdLineEdit->ResetLineEditSize(iEditWidth, 56);
    m_pPwdLineEdit->setFixedWidth(iLabelWidth + iEditWidth + 16);
    m_pPwdLineEdit->setStyleSheet(strQss);

    m_pCancelBtn = new QPushButton(tr("取消"));
    m_pCancelBtn->setFixedSize(144, 56);
    m_pCancelBtn->setObjectName("CancelBtn");
    connect(m_pCancelBtn, &QPushButton::clicked, this, &CWiFiInputWidget::_SlotCancelBtn);

    m_pConfirmBtn = new QPushButton(tr("确认"));
    m_pConfirmBtn->setFixedSize(144, 56);
    m_pConfirmBtn->setObjectName("ConfirmBtn");
    connect(m_pConfirmBtn, &QPushButton::clicked, this, &CWiFiInputWidget::_SlotConfirmBtn);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(0);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pCancelBtn);
    pBtnLayout->addSpacing(30);
    pBtnLayout->addWidget(m_pConfirmBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setContentsMargins(24, 10, 24, 24);
    pLayout->addWidget(m_pTitleWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(15);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pNameLineEdit, 0, Qt::AlignHCenter);
    pLayout->addSpacing(30);
    pLayout->addWidget(m_pPwdLineEdit, 0, Qt::AlignHCenter);
    pLayout->addStretch(1);
    pLayout->addLayout(pBtnLayout);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(596, 460);
    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}
