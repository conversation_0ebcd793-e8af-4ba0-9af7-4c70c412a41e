#ifndef CMOTORPOSITION_H
#define CMOTORPOSITION_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-01-15
  * Description: 位置调试 - 电机工装调试流程
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QTimer>
#include <QPushButton>
#include <QCheckBox>
#include <QGroupBox>
#include <QTextEdit>
#include <QComboBox>
#include <QVBoxLayout>
#include <QSettings>
#include <QDir>
#include <QApplication>

#include "CCmdBase.h"
#include "CLabelLabel.h"
#include "CLabelLineEdit.h"
#include "CLabelComboBox.h"

class CMotorPosition : public QWidget, public CCmdBase
{
    Q_OBJECT

public:
    explicit CMotorPosition(QWidget *parent = nullptr);
    ~CMotorPosition();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

private slots:
    void _SlotPositionMoveClicked();
    void _SlotCompensateAdd();
    void _SlotCompensateReduce();
    void _SlotResetCompensate();
    void _SlotModuleFinished();
    void _SlotClearRecord();
    void _SlotMachineReset();

private:
    void _InitWidget();
    void _InitMotorModules();
    void _InitModuleGroup(int iModuleIndex);
    void _UpdateCompensateDisplay(int iModuleIndex, int iPositionIndex);
    void _UpdateButtonStates();
    void _UpdatePositionButtonStates(int iModuleIndex);
    void _AddDebugRecord(const QString &strRecord);
    void _EnableModuleGroup(int iModuleIndex, bool bEnabled);
    void _SendIDSequence(const QList<int> &listIDSequence, int iMotorID, int iCompensate);
    void _SendResetCommand();
    void _ExecutePositionAction(int iModuleIndex, int iPositionIndex);
    void _ExecuteCrossModuleAction(int iModuleIndex, int iPositionIndex);
    void _SendGetCompensateCommand(int iMotorID);
    void _SendSetCompensateCommand(int iMotorID);
    void _ValidateCompensateStep(int iModuleIndex, const QString &strValue);
    void _LoadConfigFromFile();
    void _RegisterCommandsFromConfig();
    void _UnregisterCommandsFromConfig();
    QList<int> _ParseSequenceString(const QString &strSequence);
    void _LoadModulePositions(const QString &strSection, int iModuleIndex);

private:
    // 移除固定的模块枚举，改为动态模块管理

    struct SMotorPosition
    {
        QString strActionName;      // 动作名称
        QList<int> listIDSequence;  // id执行序列
        int iMotorID;               // 电机ID
        int iCompensateIndex;       // 补偿索引
        int iDefaultCompensate;     // 默认补偿值
        int iCurrentCompensate;     // 当前补偿值
        QPushButton *pMoveBtn;      // 运动按钮
    };

    struct SMotorModule
    {
        QString strModuleName;
        QList<SMotorPosition> listPositions;
        QGroupBox *pGroupBox;
        QComboBox *pCompensateStepComboBox;  // 补偿步长选择框
        QPushButton *pAddBtn;
        QPushButton *pReduceBtn;
        QPushButton *pResetBtn;
        QPushButton *pFinishBtn;
        CLabelLabel *pCurrentPosLabel;  // 当前选中位置显示
        CLabelLabel *pCompensateLabel;  // 补偿值显示
        int iCurrentPositionIndex;      // 当前选中的位置索引
        bool bFinished;
    };

private:
    QString m_strConfigPath;
    QSettings *m_pSettings;
    CLabelComboBox *m_pMachineComboBox;
    QTextEdit *m_pRecordTextEdit;
    QPushButton *m_pMachineResetBtn;
    
    QList<SMotorModule> m_MotorModules;
    QStringList m_strModuleNames;
    QStringList m_strModuleKeys;
    int m_iCurrentModuleIndex;
    int m_iCurrentSequenceIndex;  // 当前执行的ID序列索引
    QList<int> m_currentSequence; // 当前执行的ID序列
    QTimer *m_pResponseTimer;
    QTimer *m_pSequenceTimer;     // 序列执行定时器
    
    // 跨模块操作相关
    bool m_bWaitingForReset;      // 是否正在等待复位完成
    bool m_bCrossModuleAction;    // 是否正在进行跨模块操作
    int m_iPendingModuleIndex;    // 等待切换到的模块索引
    int m_iPendingPositionIndex;  // 等待执行的位置索引
    bool m_bModuleFinishedReset;  // 是否是模块完成后的复位
    int m_iOriginalModuleIndex;   // 跨模块操作前的原始模块索引
    
    // 补偿值获取相关
    bool m_bWaitingForCompensate; // 是否正在等待补偿值回复
    int m_iCompensateRequestModuleIndex;   // 请求补偿值的模块索引
    int m_iCompensateRequestPositionIndex; // 请求补偿值的位置索引
    
    // 补偿数据管理
    QList<int> m_listCurrentCompensateData; // 当前电机的完整补偿数据数组
    QStringList m_listCurrentCompensateNames; // 当前电机的完整补偿名称数组
    int m_iCurrentCompensateMotorID;         // 当前补偿数据对应的电机ID
    bool m_bWaitingForSetCompensate;         // 是否正在等待设置补偿值回复
};

#endif // CMOTORPOSITION_H 