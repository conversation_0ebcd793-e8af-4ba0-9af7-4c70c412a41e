#include "CHrmOneCalibrate.h"
#include <QHBoxLayout>
#include <QVBoxLayout>

CLabel2LineEdit::CLabel2LineEdit(const QString &strTitle, QWidget *parent)
    : QWidget(parent)
    , m_strTitle(strTitle)
{
    _InitWidget();
}

CLabel2LineEdit::~CLabel2LineEdit()
{

}

QString CLabel2LineEdit::GetValue1() const
{
    return m_pLineEdit1->text();
}

QString CLabel2LineEdit::GetValue2() const
{
    return m_pLineEdit2->text();
}

void CLabel2LineEdit::_InitWidget()
{
    m_pLabel = new QLabel(m_strTitle);
    m_pLabel->setFixedSize(80, 50);
    m_pLabel->setAlignment(Qt::AlignVCenter | Qt::AlignRight);

    m_pLineEdit1 = new CLineEdit;
    m_pLineEdit1->setFixedSize(100, 50);
    m_pLineEdit1->setAlignment(Qt::AlignCenter);
    m_pLineEdit1->setInputMethodHints(Qt::ImhDigitsOnly);

    m_pLineEdit2 = new CLineEdit;
    m_pLineEdit2->setFixedSize(100, 50);
    m_pLineEdit2->setAlignment(Qt::AlignCenter);
    m_pLineEdit2->setInputMethodHints(Qt::ImhDigitsOnly);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->addWidget(m_pLabel);
    pLayout->addWidget(m_pLineEdit1);
    pLayout->addSpacing(8);
    pLayout->addWidget(m_pLineEdit2);
    this->setLayout(pLayout);
}

CLysisOneCalibrate::CLysisOneCalibrate(const QString &strGroup, QWidget *parent)
    : QWidget(parent)
    , m_strGroup(strGroup)
{
    _InitWidget();
  //  setFixedWidth(250);

    QString strQss = "QLabel{font-size: 18px;}";
    setStyleSheet(strQss);
}

CLysisOneCalibrate::~CLysisOneCalibrate()
{

}

QStringList CLysisOneCalibrate::GetSrcDstList() const
{
    QStringList strValueList;
    QStringList strSrcList, strDstList;

    if(!m_pFirst->GetValue1().isEmpty() && !m_pFirst->GetValue2().isEmpty())
    {
        strSrcList.push_back(m_pFirst->GetValue1());
        strDstList.push_back(m_pFirst->GetValue2());
    }

    if(!m_pSecond->GetValue1().isEmpty() && !m_pSecond->GetValue2().isEmpty())
    {
        strSrcList.push_back(m_pSecond->GetValue1());
        strDstList.push_back(m_pSecond->GetValue2());
    }

    if(!m_pThird->GetValue1().isEmpty() && !m_pThird->GetValue2().isEmpty())
    {
        strSrcList.push_back(m_pThird->GetValue1());
        strDstList.push_back(m_pThird->GetValue2());
    }

    if(!m_pForth->GetValue1().isEmpty() && !m_pForth->GetValue2().isEmpty())
    {
        strSrcList.push_back(m_pForth->GetValue1());
        strDstList.push_back(m_pForth->GetValue2());
    }

    if(!strSrcList.isEmpty())
    {
        QString src = strSrcList.join(",");
        QString dst = strDstList.join(",");
        strValueList << src << dst;
    }
    return strValueList;
}

void CLysisOneCalibrate::_InitWidget()
{
    QLabel *pLabel1 = new QLabel(m_strGroup);
    pLabel1->setFixedHeight(40);

    QLabel *pDstLabel = new QLabel(tr("目标温度"));
    pDstLabel->setFixedSize(100, 40);
    pDstLabel->setAlignment(Qt::AlignCenter);

    QLabel *pSrcLabel = new QLabel(tr("实测温度"));
    pSrcLabel->setFixedSize(100, 40);
    pSrcLabel->setAlignment(Qt::AlignCenter);

    m_pFirst  = new CLabel2LineEdit(tr("第一组:"));
    m_pSecond = new CLabel2LineEdit(tr("第二组:"));
    m_pThird  = new CLabel2LineEdit(tr("第三组:"));
    m_pForth  = new CLabel2LineEdit(tr("第四组:"));

    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->addStretch(1);
    pTopLayout->addWidget(pLabel1);
    pTopLayout->addSpacing(80);

    QHBoxLayout *pLabelLayout = new QHBoxLayout;
    pLabelLayout->setMargin(0);
    pLabelLayout->addStretch(1);
    pLabelLayout->addWidget(pDstLabel);
    pLabelLayout->addSpacing(8);
    pLabelLayout->addWidget(pSrcLabel);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addLayout(pTopLayout);
    pLayout->addLayout(pLabelLayout);
    pLayout->addWidget(m_pFirst);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pSecond);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pThird);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pForth);

    this->setLayout(pLayout);
}
