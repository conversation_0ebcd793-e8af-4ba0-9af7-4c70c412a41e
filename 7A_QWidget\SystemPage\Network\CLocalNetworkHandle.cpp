#include "CLocalNetworkHandle.h"
#include <QProcess>
#include <QHostAddress>
#include <QNetworkInterface>
#include <QNetworkAccessManager>

#include "PublicFunction.h"
#include "PublicParams.h"
#include "PublicConfig.h"
#include <QTimer>
#include "CMessageBox.h"
#include "CConfigJson.h"
#include "PublicConfig.h"
//#include "MDControl/CNetworkStateThread.h"

CLocalNetworkHandle::CLocalNetworkHandle(QObject *parent)
{
    m_bAutoConnect = false;
    m_bWifiOpenState = false;

    //无法知道ifconfig eth0 up什么时候才能becomes ready

#ifdef Q_OS_LINUX
    m_strIPV4Name = "ens33";
#endif

#ifdef __arm__
    m_strIPV4Name = "eth1";
#endif
    m_strIPV4Name = "eth1";
     //_ReadConfigFile();
     m_pReGetIPTimer = new QTimer(this);
     connect(m_pReGetIPTimer,&QTimer::timeout,this,&CLocalNetworkHandle::_SlotReGetIP);
    // m_pReGetIPTimer->start(1000);
    //m_pSaveButton->setEnabled(false);
}
#if 0
void CLocalNetworkHandle::showEvent(QShowEvent *pEvent)
{

    //#ifdef __arm__
    //    PlatformInputContextBase->SetParentWidget((QWidget*)gk_pMainWindow);
    //#endif
    if(m_bWifiOpenState)
    {
        /*
        m_pAutoModeButton->setCheckable(false);
        m_pManualModeButton->setCheckable(false);
        m_pAutoModeButton->setChecked(false);
        m_pManualModeButton->setChecked(false);
        */
    }
    else
    {
        QMap<QString,QVariant> strDataMap;
        if(CConfigJson::GetInstance().ReadJsonMap("Console_Info", strDataMap))
        {
            QString strIP = strDataMap.value("console_ip").toString();
            if(strIP.isEmpty())
                strIP = "***********0";

            QString strNetmask = strDataMap.value("console_mask").toString();
            if(strNetmask.isEmpty())
                strNetmask = "*************";

            QString strGateway = strDataMap.value("console_geteway").toString();
            if(strGateway.isEmpty())
                strGateway = "***********";

            if(!m_bAutoConnect)
            {
                m_pIPWidget->SetIPText(strIP);
                m_pMaskWidget->SetIPText(strNetmask);
                m_pGatewayWidget->SetIPText(strGateway);
            }
        }

        if(m_bAutoConnect)
            m_pAutoModeButton->setChecked(true);
        else
            m_pManualModeButton->setChecked(true);
    }

    QWidget::showEvent(pEvent);
}
#endif
void CLocalNetworkHandle::ManualConnect()
{
    /*
    if(m_bWifiOpenState)
    {
        ShowInformation(NULL, tr("System Message"), tr("WiFi is turned on. If you want to operate, please turn off WiFi first"));
        return;
    }*/
    m_pReGetIPTimer->stop();
    m_bAutoConnect = false;

    QString strCmd1 = "ifconfig wlan0 down";
    system(strCmd1.toStdString().c_str());

    QString strCmd2 = QString("ifconfig %1 up").arg(m_strIPV4Name);
    system(strCmd2.toStdString().c_str());
    qDebug()<<"手动设置网络:"<<strCmd1<<strCmd2;

    QJsonObject Network;
    Network.insert("wifi_or_network", eNetwork_Eth);
    Network.insert("console_ip_auto", false);
    CConfigJson::GetInstance()->IncrementInsertJsonObject("NetWork", Network);
    RUN_LOG("网络设置: 开启手动Ip");
    //_CheckEth0NetworkState();

}

void CLocalNetworkHandle::AutoConnect()
{
    //m_bWifiOpenState = false;
    QString strDownWlanCmd = "ifconfig wlan0 down";
    system(strDownWlanCmd.toStdString().c_str());

    //有时启动eth0很慢,用QProcess执行不会返回 becomes ready
    QString strUpEth0Cmd = QString("ifconfig %1 up").arg(m_strIPV4Name);
    system(strUpEth0Cmd.toStdString().c_str());

    bool bEth0Exist = GetEth0NetworkState();
    // 监控线程
    qDebug()<<"自动设置网络:eth1是否存在:"<<bEth0Exist;
    qDebug()<<"自动设置网络:"<<strDownWlanCmd<<strUpEth0Cmd;

    m_networkInfoStructMap.clear();

    m_bAutoConnect = true;
    m_sIPInfoStruct.bAuto = true;

    QJsonObject Network;
    Network.insert("wifi_or_network", eNetwork_Eth);
    Network.insert("console_ip_auto", true);
    CConfigJson::GetInstance()->IncrementInsertJsonObject("NetWork", Network);
    RUN_LOG("网络设置: 自动Ip");
    _SlotReGetIP();
    _ReGetNetworkInfoAndUpdateUi();
    m_pReGetIPTimer->start(1000);
}

void CLocalNetworkHandle::_SlotReGetIP()
{
    bool bEth0Exist = GetEth0NetworkState();
    if(!bEth0Exist) //3.eth1不存在
    {
        return;
    }

    if(!m_bAutoConnect || m_bWifiOpenState) //3.eth0没启动，不是自动连接，wifi已经打开
    {
        qDebug()<< "bEth0Exist: "<<bEth0Exist<< "m_bAutoConnect: "<<m_bAutoConnect << "m_bWifiOpenState"<<m_bWifiOpenState;
        m_pReGetIPTimer->stop();
        return;
    }

    //4.已经获取到eth0 ip信息
    if(m_networkInfoStructMap.contains(m_strIPV4Name))
    {
        qDebug()<< "m_networkInfoStructMap.contains(m_strIPV4Name):  true";
        m_pReGetIPTimer->stop();
        return;
    }

    QString strCmd = "udhcpc -n &";
    QProcess process;
    process.start(strCmd);
    Delay_MSec(1500);

    qDebug()<<"_SlotReGetIP:"<<strCmd;
    qDebug()<<__FUNCTION__<<__LINE__<<strCmd;

    _ReGetNetworkInfoAndUpdateUi();

    process.close();
}

void CLocalNetworkHandle::SlotWiFiOpenState(bool bOpenState)
{
    if(bOpenState)
    {
        m_bWifiOpenState = true;
        m_pReGetIPTimer->stop();
    }
    else
    {
        m_bWifiOpenState = false;
    }
    //_CheckEth0NetworkState();
}

void CLocalNetworkHandle::SaveInfo(const IPInfoStruct& sIpInfo)
{
    /*
    if(m_bWifiOpenState)
    {
        ShowInformation(NULL, tr("System Message"), tr("WiFi is turned on. If you want to operate, please turn off WiFi first"));
        return;
    }*/

    QString strIP = sIpInfo.strIP;
    QString strNetmask = sIpInfo.strMask;
    QString strGateway = sIpInfo.strGeteway;
    m_bAutoConnect = sIpInfo.bAuto;
    if(!m_bAutoConnect)
    {
        if(strIP.isEmpty()|| strNetmask.isEmpty() || strGateway.isEmpty())
        {
            ShowInformation(NULL, tr("System Message"), tr("Please enter the correct content"));
            //return;
        }
    }

    QJsonObject Network;
    Network.insert("wifi_or_network", eNetwork_Eth);
    Network.insert("console_ip_auto", m_bAutoConnect);
    Network.insert("console_ip", strIP);
    Network.insert("console_mask", strNetmask);
    Network.insert("console_geteway", strGateway);
    CConfigJson::GetInstance()->IncrementInsertJsonObject("NetWork", Network);
    RUN_LOG("网络设置: 保存Ip设置");

    if(!m_bAutoConnect)
    {
        QString strCmd1 = QString("ifconfig %1 %2 netmask %3 &").arg(m_strIPV4Name).arg(strIP).arg(strNetmask);
        qDebug()<<"手动设置网络,保存:"<<strCmd1;
        system(strCmd1.toStdString().c_str());

        QString strCmd2 = QString("route add default gw %1 %2").arg(strGateway).arg(m_strIPV4Name);
        qDebug()<<"手动设置网络,保存:"<<strCmd2;
        system(strCmd2.toStdString().c_str());
    }

    m_sIPInfoStruct.bAuto = m_bAutoConnect;
    m_sIPInfoStruct.strIP = strIP;
    m_sIPInfoStruct.strMask = strNetmask;
    m_sIPInfoStruct.strGeteway = strGateway;
    //_CheckEth0NetworkState();
}

void CLocalNetworkHandle::_CheckEth0NetworkState()
{
    bool ConnectTemp = GetEth0NetworkState();
    qDebug()<<"CLocalNetworkHandle,当前网络状态:"<<ConnectTemp<<",上次:"<<m_bConnect;
    m_bConnect = ConnectTemp;
    emit CPublicConfig::GetInstance()->SignalEth1Status(m_bConnect);
}


void CLocalNetworkHandle::_ReadConfigFile(bool bAutoIp,int LocalOrwifi,const QString& strIp,const QString& strNetMask,const QString& strGetWay)
{
    m_sIPInfoStruct.Clear();        
    // 有线或者网络
    m_bAutoConnect = bAutoIp; //手动ip 或者 自动ip
    // 是否自动连接 或者手动连接
    m_sIPInfoStruct.bAuto = m_bAutoConnect;


    if(eNetwork_Eth != LocalOrwifi)
    {
        system(QString("ifconfig %1 down").arg(m_strIPV4Name).toStdString().c_str());
        return;
    }
    // 自动连接
    if(m_bAutoConnect)
    {
        AutoConnect();
        return;
    }

    // 手动ip
    m_sIPInfoStruct.strIP = strIp;
    m_sIPInfoStruct.strMask = strNetMask;
    m_sIPInfoStruct.strGeteway = strGetWay;

    QString strCmd1 = QString("ifconfig %1 %2 netmask %3 &").arg(m_strIPV4Name).arg(strIp).arg(strNetMask);
    qDebug()<<"[CLocalNetworkHandle::ReadInitInfo]:"<<strCmd1;
    system(strCmd1.toStdString().c_str());

    QString strCmd2 = QString("route add default gw %1 %2").arg(strGetWay).arg(m_strIPV4Name);
    qDebug()<<"[CLocalNetworkHandle::ReadInitInfo]:"<<strCmd2;
    system(strCmd2.toStdString().c_str());
    // _CheckEth0NetworkState();
}

IPInfoStruct CLocalNetworkHandle::GetIPInfoStruct()
{
    return m_sIPInfoStruct;
}

void CLocalNetworkHandle::SetIPInfoStruct(IPInfoStruct &sIpInfo)
{
    m_sIPInfoStruct.strIP = sIpInfo.strIP;
    m_sIPInfoStruct.strMask = sIpInfo.strMask;
    m_sIPInfoStruct.strGeteway = sIpInfo.strGeteway;
}

//wifi连接后也要更新显示ip等信息
void CLocalNetworkHandle::SlotSetWifiConnectState(bool bWifiConnect)
{
    if(!bWifiConnect)
    {
        return;
    }
    qDebug()<<"CLocalNetworkHandle,WIFI已连接，获取IP信息";
    _ReGetNetworkInfoAndUpdateUi();
}



//重新获取网络信息并更新UI
void CLocalNetworkHandle::_ReGetNetworkInfoAndUpdateUi()
{
    m_networkInfoStructMap.clear();

    _GetNetworkInfo();
    _GetGateway();

    SNetworkInfoStruct infoStruct;
    if(m_bWifiOpenState)
        infoStruct = m_networkInfoStructMap.value("wlan0");
    else
        infoStruct = m_networkInfoStructMap.value(m_strIPV4Name);

    m_sIPInfoStruct.strIP = infoStruct.strIP;
    m_sIPInfoStruct.strMask = infoStruct.strNetmask;
    m_sIPInfoStruct.strGeteway = infoStruct.strGateway;
    emit SignalChangeIpInfo(m_sIPInfoStruct);
}

//读取IP,子网掩码等网络信息
void CLocalNetworkHandle::_GetNetworkInfo()
{
    QList<QNetworkInterface> interfaceList = QNetworkInterface::allInterfaces();
    qDebug() << "interfaceList size:" << interfaceList.size();
    foreach(QNetworkInterface interfaceItem, interfaceList)
    {
        qDebug() << "Interface Name:" << interfaceItem.name();
        qDebug() << "Hardware Address (MAC):" << interfaceItem.hardwareAddress();

        if(interfaceItem.flags().testFlag(QNetworkInterface::IsUp) &&
                interfaceItem.flags().testFlag(QNetworkInterface::IsRunning))
        {
            QList<QNetworkAddressEntry> addressEntryList=interfaceItem.addressEntries();
            foreach(QNetworkAddressEntry addressEntryItem, addressEntryList)
            {
                if(QAbstractSocket::IPv4Protocol == addressEntryItem.ip().protocol())
                {
                    SNetworkInfoStruct infoStruct;
                    infoStruct.strInterfaceName = interfaceItem.name();
                    infoStruct.strHardwareAddress = interfaceItem.hardwareAddress();
                    infoStruct.strIP = addressEntryItem.ip().toString();
                    infoStruct.strNetmask = addressEntryItem.netmask().toString();
                    infoStruct.strBroadcast = addressEntryItem.broadcast().toString();

                    qDebug()<<"Adapter Name:"<<infoStruct.strInterfaceName;
                    qDebug()<<"Adapter Address:"<<infoStruct.strHardwareAddress;
                    qDebug()<<"IP Address:"<<infoStruct.strIP;
                    qDebug()<<"IP Mask:"<<infoStruct.strNetmask;
                    qDebug()<<"broadcast"<<infoStruct.strBroadcast;

                    m_networkInfoStructMap.insert(infoStruct.strInterfaceName,infoStruct);
                }
            }
        }
    }
}

void CLocalNetworkHandle::_GetGateway()
{
    QProcess process;
    process.start("route -n");
    process.waitForStarted();
    process.waitForFinished();
    QString strResult = process.readAllStandardOutput();
    qDebug()<<"获取网关 route -n:"<<strResult;

    QString strName;
    QString strGateway;
    QStringList strList = strResult.split('\n');
    qDebug()<<"网关信息 第一步:"<<strList;
    for(int i=0;i<strList.size();i++)
    {
        QStringList oneList = strList.at(i).split(' ');
        oneList.removeAll("");
        if(oneList.size() < 3)
            continue;

        if(oneList.contains("UG",Qt::CaseInsensitive))
        {
            if(oneList.contains(m_strIPV4Name,Qt::CaseInsensitive))
                strName = m_strIPV4Name;
            else if(oneList.contains("wlan0",Qt::CaseInsensitive))
                strName = "wlan0";
            else
                continue;

            strGateway = oneList[1];
            qDebug()<<"网关信息:"<<strName<<strGateway;
            m_networkInfoStructMap[strName].strGateway = strGateway;
        }
    }
}

bool CLocalNetworkHandle::GetEth0NetworkState()
{
    QList<QNetworkInterface> interfaceList = QNetworkInterface::allInterfaces();

    qDebug()<<"GetEth0NetworkState size:"<<interfaceList.size();

    bool bNetwork = false;
    for(const auto& interfaceItem : interfaceList)
    {
        if(interfaceItem.flags().testFlag(QNetworkInterface::IsRunning))
        {
            qDebug()<<"QNetworkInterface::IsRunning";
            QList<QNetworkAddressEntry> addressEntryList=interfaceItem.addressEntries();
            qDebug()<<"addressEntryList size :" << addressEntryList.size();

            foreach(QNetworkAddressEntry addressEntryItem, addressEntryList)
            {
                if(addressEntryItem.ip().protocol()==QAbstractSocket::IPv4Protocol)
                {
                    if(interfaceItem.name() == m_strIPV4Name)
                    {
                        return true;
                    }
                }
            }
        }

    }
    return  bNetwork;
}


