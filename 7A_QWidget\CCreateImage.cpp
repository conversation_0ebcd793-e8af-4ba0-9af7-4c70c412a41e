#include "CCreateImage.h"
#include <QDebug>
#include <QTimer>

#include "CLotInfoDB.h"
//#include "CHistoryDB.h"
#include "CProjectDB.h"
#include "PublicFunction.h"
#include "HistoryPage/CHistoryDetailWidget.h"



CCreateImage *CCreateImage::GetInstance()
{
    static CCreateImage stCreateImage;
    return  &stCreateImage;
}

void CCreateImage::SaveHistoryDetailImage(const SRunningInfoStruct &sRunInfo, bool bReview)
{
    // 这里最好 setHistoryId一下  加载当前界面数据才可以截图呀
    SLotInfoStruct sLotInfo;
    CLotInfoDB::GetInstance()->GetLotInfoByShowName(sRunInfo.sResultInfo.strProjectName, sLotInfo);
    m_HistoryDetail->SetHistoryIdHandle(sRunInfo.sResultInfo,sRunInfo.sSampleInfo,sRunInfo.sCardInfo,sLotInfo,true);
    // 这里将按钮和审核的内容去掉
    m_HistoryDetail->SavePdfImage(sRunInfo,bReview);
}

CCreateImage::CCreateImage()
{
    m_HistoryDetail = new CHistoryDetailWidget(tr("测试详情"),true);
}

CCreateImage::~CCreateImage()
{
    delete  m_HistoryDetail;
    m_HistoryDetail = nullptr;
}



