﻿#include "CLisWidget.h"

#include "CMessageBox.h"
#include "CConfigJson.h"
#include "PublicParams.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "MDControl/CLisTcpClient.h"

CLisWidget::CLisWidget(QWidget *parent) : QWidget(parent)
{
    _InitWidget();
    _InitLayout();

    _ReadCfg();
    CLisTcpClient::GetInstace().SetIPAndPort(m_strIP, m_iPort);
}

void CLisWidget::showEvent(QShowEvent *pEvent)
{
    _ReadCfg();
    QWidget::showEvent(pEvent);
}

void CLisWidget::_ReadCfg()
{
    QJsonObject qLisObj = CConfigJson::GetInstance()->GetConfigJsonObject("Lis");
    m_strIP = qLisObj.value("ip").toString();
    m_pIPWidget->SetValueList(m_strIP.split("."));

    if(qLisObj.contains("port"))
    {
        m_iPort = qLisObj.value("port").toInt();
        m_pPortWidget->SetLineEditText(QString::number(m_iPort));
    }

    bool bTwoWay = false;
    if(qLisObj.contains("two-way"))
    {
        bTwoWay = qLisObj.value("two-way").toBool();
    }
    m_pTwoWayCheckBox->setChecked(bTwoWay);

    CPublicConfig::GetInstance()->SetLisTwoWay(bTwoWay);
    RUN_LOG(QString("是否启用双向LIS：%1").arg(bTwoWay));
}

void CLisWidget::_SlotLisTwoWayCheck(bool bChecked)
{
    Q_UNUSED(bChecked);

    bool bLisTwoWay = m_pTwoWayCheckBox->isChecked();

    QJsonObject qLisObj;
    qLisObj.insert("two-way", bLisTwoWay);
    CConfigJson::GetInstance()->IncrementInsertJsonObject("Lis", qLisObj);

    CPublicConfig::GetInstance()->SetLisTwoWay(bLisTwoWay);
    RUN_LOG(QString("是否启用双向LIS：%1").arg(bLisTwoWay));
}

void CLisWidget::_SlotSaveBtn()
{
    QString strTipsText;
    QString strIP = m_pIPWidget->GetValueList().join(".");
    if(!IsValidIPv4(strIP))
    {
        ShowInformation(this->parentWidget(), strTipsText, tr("LIS设置，请输入正确的IP地址"));
        return;
    }
    QString strPort = m_pPortWidget->GetLineEditText();
    int iPort = strPort.toInt();
    if(strPort.isEmpty() || iPort < 0 || iPort > 65535)
    {
        ShowInformation(this->parentWidget(), strTipsText, tr("LIS设置，请输入正确的端口"));
        return;
    }

    bool bLisTwoWay = m_pTwoWayCheckBox->isChecked();

    QJsonObject qLisObj;
    qLisObj.insert("ip", strIP);
    qLisObj.insert("port", iPort);
    qLisObj.insert("two-way", bLisTwoWay);
    CConfigJson::GetInstance()->SetConfigJsonObject("Lis", qLisObj);
    ShowSuccess(this->parentWidget(), strTipsText, tr("LIS设置保存成功"));

    CPublicConfig::GetInstance()->SetLisTwoWay(bLisTwoWay);
    RUN_LOG(QString("是否启用双向LIS：%1").arg(bLisTwoWay));

    if(m_strIP != strIP || m_iPort != iPort)
    {
        m_strIP = strIP;
        m_iPort = iPort;
        CLisTcpClient::GetInstace().SetIPAndPort(m_strIP, m_iPort);
        CLisTcpClient::GetInstace().ConnectServer();
    }
}

void CLisWidget::_InitWidget()
{
    int iLabelWidth = 96, iEditWidth = 92 , iHeight = 56;
    if(eLanguage_English == gk_iLanguage)
        iLabelWidth = 125;
    else if(eLanguage_Spanish == gk_iLanguage)
        iLabelWidth = 150;
    else if(eLanguage_German == gk_iLanguage)
        iLabelWidth = 145;
    else if(eLanguage_Italian == gk_iLanguage)
        iLabelWidth = 155;

    m_pTitleWidget = new CHLabelTitleWidget(tr("LIS设置"));

    m_pTwoWayCheckBox = new QCheckBox(tr("双向LIS"));
    m_pTwoWayCheckBox->setChecked(false);
    connect(m_pTwoWayCheckBox, &QCheckBox::clicked, this, &CLisWidget::_SlotLisTwoWayCheck);

    m_pIPWidget = new CIPLabelLineEdit(tr("IP地址"));
    m_pIPWidget->ResetLabelSize(iLabelWidth, iHeight);
    m_pIPWidget->ResetLineEditSize(iEditWidth, iHeight);

    m_pPortWidget = new CHLabelLineEdit(tr("端口"), "", 10);
    m_pPortWidget->ResetLabelSize(iLabelWidth, iHeight);
    m_pPortWidget->ResetLineEditSize(iEditWidth, iHeight);
    m_pPortWidget->SetLineEidtAlignment(Qt::AlignCenter);
    m_pPortWidget->setFixedSize(iLabelWidth + iEditWidth + 10, iHeight);

    m_pSaveBtn = new QPushButton(tr("保存"));
    m_pSaveBtn->setFixedSize(144, 56);
    connect(m_pSaveBtn, &QPushButton::clicked, this, &CLisWidget::_SlotSaveBtn);
}

void CLisWidget::_InitLayout()
{
    QVBoxLayout *pLisLayout = new QVBoxLayout;
    pLisLayout->setContentsMargins(18, 0, 0, 0);
    pLisLayout->setSpacing(0);
    pLisLayout->addWidget(m_pIPWidget, 0, Qt::AlignLeft);
    pLisLayout->addSpacing(20);
    pLisLayout->addWidget(m_pPortWidget, 0, Qt::AlignLeft);
    pLisLayout->addSpacing(20);
    pLisLayout->addWidget(m_pTwoWayCheckBox, 0, Qt::AlignLeft);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pTitleWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(20);
    pLayout->addLayout(pLisLayout);
    pLayout->addSpacing(25);
    pLayout->addWidget(m_pSaveBtn, 0, Qt::AlignHCenter);
    this->setLayout(pLayout);
}
