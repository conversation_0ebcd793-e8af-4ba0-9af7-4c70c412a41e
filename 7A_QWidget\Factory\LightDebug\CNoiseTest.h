#ifndef CNOISETEST_H
#define CNOISETEST_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: system
  * Date: 2024-12-19
  * Description: 底噪测试
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QTableWidget>
#include "CLabelComboBox.h"
#include <QPushButton>
#include "CLightOneTiming.h"
#include "CBusyProgressBar.h"
#include "CCmdBase.h"

class CNoiseTest : public QWidget, public CCmdBase
{
    Q_OBJECT
public:
    explicit CNoiseTest(QWidget* parent = nullptr);
    ~CNoiseTest();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant& qVarData) override;
    void SetFluorescenceType(int iTypeIndex);  // 设置荧光类型：0-荧光片，1-染料

private slots:
    void _SlotReadNoiseBtn();
    void _SlotTimingEnd();
    void _ReceiveMDTData(const QVariant& qVarData);

private:
    void _InitWidget();
    void _SetTableItem(int iRow, int iCol, const QString& strText);
    bool _SaveNoiseDataToExcel();

    CLabelComboBox* m_pMachineComboBox;
    QPushButton* m_pReadBtn;
    QTableWidget* m_pTableWidget;
    CLightOneTiming* m_pLightOneTiming;
    
    CBusyProgressBar* m_pCBusyProgressBar;
    bool bNoiseTested;

    double m_dNoiseData[2][4]; // [孔][通道]
    int m_iFluorescenceType;
};

#endif // CNOISETEST_H 