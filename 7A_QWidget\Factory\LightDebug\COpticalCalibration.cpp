#include "COpticalCalibration.h"
#include <QTimer>
#include <QDebug>
#include <QListView>
#include <QBoxLayout>
#include <QHeaderView>
#include "CMessageBox.h"
#include "PublicParams.h"

COpticalCalibration::COpticalCalibration(QWidget *parent) : QWidget(parent)
{
    QTime t1 = QTime::currentTime();

    Register2Map(Method_FLMDT);
    Register2Map(Method_FLGAINSET);

    m_pLightOneTiming = new CLightOneTiming();
    connect(m_pLightOneTiming, &CLightOneTiming::SignalTimingEnd, this, &COpticalCalibration::_SlotTimingEnd);
    connect(m_pLightOneTiming, &CLightOneTiming::SignalPreTimingEnd, this, &COpticalCalibration::_SlotPreTimingEnd);
    connect(m_pLightOneTiming, &CLightOneTiming::SignalTimingStopped, this, &COpticalCalibration::_SlotTimingStopped);
    m_pCBusyProgressBar = new CBusyProgressBar(gk_strTipsText, tr("正在进行增益校准"));
    m_pCBusyProgressBar->SetCancelButtonVisible(true);
    connect(m_pCBusyProgressBar, &CBusyProgressBar::SignalCancel, this, &COpticalCalibration::_SlotStopCalibration);
    _InitWidget();
    minGain = 16;
    maxGain = 255;
    // 初始化目标值数组
    memset(m_targetValue, 0, sizeof(m_targetValue));
    // 初始化精细搜索相关变量
    m_isFineSearching = false;
    m_fineSearchIndex = 0;
    memset(m_fineSearchResults, 0, sizeof(m_fineSearchResults));
    ClearAllData();

    qDebug()<<"光学增益校准页面构造时间:"<<t1.msecsTo(QTime::currentTime());
}

COpticalCalibration::~COpticalCalibration()
{
    UnRegister2Map(Method_FLMDT);
    UnRegister2Map(Method_FLGAINSET);
}

void COpticalCalibration::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if (m_isCalibrating) {
        if(Method_FLMDT == iMethodID)
            ReceiveMDTData(qVarData);
        else if(iMethodID == Method_FLGAINSET) {
            if(iResult == 0) {
                // 启动采光流程
                m_pLightOneTiming->startTiming(m_pMachineComboBox->GetCurrentIndex(), 1);
            } 
        }
    }
}

void COpticalCalibration::ReceiveMDTData(const QVariant &qVarData)
{
    QVariantList qVarList = qVarData.toList();
    if(qVarList.size() < 5)
        return;

    int iHole = qVarList.at(0).toInt(); // 0; 1
    if(iHole < 0 || iHole >= gk_iHoleCount)
        return;

    for (int i = 1; i < 5; i++) {
        m_lightTestValue[iHole].value[i-1] = qVarList.at(i).toInt();
    }

    qDebug()<<"hole1 Fam:"<<m_lightTestValue[0].value[m_currentColor];
    qDebug()<<"hole2 Fam:"<<m_lightTestValue[1].value[m_currentColor];
    
}

void COpticalCalibration::ClearAllData()
{
    m_isCalibrating = false;
    m_isFineSearching = false;
    m_fineSearchIndex = 0;
    memset(m_holeGain, 0, sizeof(m_holeGain));
    memset(m_lightTestValue, 0, sizeof(m_lightTestValue));
    memset(m_fineSearchResults, 0, sizeof(m_fineSearchResults));
    for (int i = 0; i < 2; i++) {
        m_fineSearchGains[i].clear();
    }

    for (int i = 0; i < gk_iBGYRCount; i++) {
        for (int j = 1; j < 6; j++) {
            _SetTableItem(i, j, "");
        }
    }
}


void COpticalCalibration::_setGain(int index, int hole1Gain, int hole2Gain)
{
    QVariantList qVarList;
    int data = 32;

    qDebug()<<"hole1 gain:"<<hole1Gain;
    qVarList.push_back(index);
    qVarList.push_back(hole1Gain);
    qVarList.push_back(hole2Gain);

    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    
    QString strCmd = GetJsonCmdString(Method_FLGAINSET, qVarList);
    SendJsonCmd(iMachineID, Method_FLGAINSET, strCmd);
}

void COpticalCalibration::_SlotPreTimingEnd(void)
{
    // 前置时序完成，开始实际的校准操作
    qDebug() << "前置时序完成，开始校准操作";
    // 这里可以进行实际的校准操作
    startGainCalibration();
}

void COpticalCalibration::_SlotTimingEnd(void)
{
    // 处理校准数据
    int hole1Value = m_lightTestValue[0].value[m_currentColor];
    int hole2Value = m_lightTestValue[1].value[m_currentColor];

    int diff1 = qAbs(hole1Value - m_targetValue[m_currentColor]);
    int diff2 = qAbs(hole2Value - m_targetValue[m_currentColor]);

    // 如果正在进行精细搜索
    if (m_isFineSearching) {
        // 记录当前测试结果
        if (m_fineSearchIndex < 5) {
            m_fineSearchResults[0][m_fineSearchIndex].gain = m_fineSearchGains[0][m_fineSearchIndex];
            m_fineSearchResults[0][m_fineSearchIndex].value = hole1Value;
            m_fineSearchResults[0][m_fineSearchIndex].diff = diff1;

            m_fineSearchResults[1][m_fineSearchIndex].gain = m_fineSearchGains[1][m_fineSearchIndex];
            m_fineSearchResults[1][m_fineSearchIndex].value = hole2Value;
            m_fineSearchResults[1][m_fineSearchIndex].diff = diff2;

            qDebug() << "精细搜索第" << m_fineSearchIndex << "组结果: hole1=" << hole1Value
                     << "(diff=" << diff1 << ") hole2=" << hole2Value << "(diff=" << diff2 << ")";
        }

        m_fineSearchIndex++;

        // 检查是否还有更多增益需要测试
        if (m_fineSearchIndex < m_fineSearchGains[0].size() && m_fineSearchIndex < m_fineSearchGains[1].size()) {
            // 继续测试下一组增益
            int hole1Gain = m_fineSearchGains[0][m_fineSearchIndex];
            int hole2Gain = m_fineSearchGains[1][m_fineSearchIndex];
            qDebug() << "精细搜索第" << m_fineSearchIndex << "组增益: hole1=" << hole1Gain << " hole2=" << hole2Gain;
            _setGain(m_currentColor, hole1Gain, hole2Gain);
            return;
        } else {
            // 精细搜索完成，分析结果
            qDebug() << "精细搜索完成，开始分析结果";

            int bestHole1Index = -1, bestHole2Index = -1;
            int minHole1Diff = INT_MAX, minHole2Diff = INT_MAX;

            // 找到各自孔的最佳增益
            for (int i = 0; i < m_fineSearchIndex && i < 5; i++) {
                if (m_fineSearchResults[0][i].diff < minHole1Diff) {
                    minHole1Diff = m_fineSearchResults[0][i].diff;
                    bestHole1Index = i;
                }
                if (m_fineSearchResults[1][i].diff < minHole2Diff) {
                    minHole2Diff = m_fineSearchResults[1][i].diff;
                    bestHole2Index = i;
                }
            }

            if (bestHole1Index >= 0 && bestHole2Index >= 0) {
                int bestHole1Value = m_fineSearchResults[0][bestHole1Index].value;
                int bestHole2Value = m_fineSearchResults[1][bestHole2Index].value;
                int bestHole1Gain = m_fineSearchResults[0][bestHole1Index].gain;
                int bestHole2Gain = m_fineSearchResults[1][bestHole2Index].gain;

                int holeDiff = qAbs(bestHole1Value - bestHole2Value);

                qDebug() << "最佳结果: hole1=" << bestHole1Value << "(gain=" << bestHole1Gain << ",diff=" << minHole1Diff << ")"
                         << " hole2=" << bestHole2Value << "(gain=" << bestHole2Gain << ",diff=" << minHole2Diff << ")"
                         << " 孔间差值=" << holeDiff;

                // 检查是否满足条件：两个孔的荧光值差值小于30，且各自与目标值差值小于50
                if (holeDiff < 30 && minHole1Diff < 50 && minHole2Diff < 50) {
                    _SetTableItem(m_currentColor, 1, QString::number(bestHole1Value));
                    _SetTableItem(m_currentColor, 2, QString::number(bestHole2Value));
                    _SetTableItem(m_currentColor, 3, QString::number(bestHole1Gain));
                    _SetTableItem(m_currentColor, 4, QString::number(bestHole2Gain));
                    _SetTableItem(m_currentColor, 5, tr("成功"));
                    qDebug() << "精细搜索找到合适的增益值，校准成功";
                } else {
                    _SetTableItem(m_currentColor, 1, QString::number(bestHole1Value));
                    _SetTableItem(m_currentColor, 2, QString::number(bestHole2Value));
                    _SetTableItem(m_currentColor, 3, QString::number(bestHole1Gain));
                    _SetTableItem(m_currentColor, 4, QString::number(bestHole2Gain));
                    _SetTableItem(m_currentColor, 5, tr("失败"));
                    qDebug() << "精细搜索未找到满足条件的增益值，校准失败";
                    ShowError(nullptr, gk_strTipsText, tr("精细搜索未找到满足条件的增益值"));
                }
            } else {
                _SetTableItem(m_currentColor, 5, tr("失败"));
                qDebug() << "精细搜索结果分析失败";
                ShowError(nullptr, gk_strTipsText, tr("精细搜索结果分析失败"));
            }

            m_isFineSearching = false;
            stopCalibration();
            return;
        }
    }

    if (diff1 <= 5 && diff2 <= 5) {
        // 达到目标，停止校准
        // 更新表格显示
        _SetTableItem(m_currentColor, 1, QString::number(hole1Value));
        _SetTableItem(m_currentColor, 2, QString::number(hole2Value));
        _SetTableItem(m_currentColor, 3, QString::number(m_holeGain[0].currentGain[m_currentColor]));
        _SetTableItem(m_currentColor, 4, QString::number(m_holeGain[1].currentGain[m_currentColor]));
        _SetTableItem(m_currentColor, 5, tr("成功"));
        qDebug()<<"增益自动校准成功，执行后置时序";
        // 执行后置时序
        stopCalibration();
    } else {
        //停止查找
        if (m_holeGain[0].minGain[m_currentColor] == m_holeGain[0].currentGain[m_currentColor] ||
            m_holeGain[1].minGain[m_currentColor] == m_holeGain[1].currentGain[m_currentColor]) {
            if (diff1 <= 50 && diff2 <= 50)
            {
                qDebug() << "二分查找达到边界，差值在50以内，开始精细搜索";
                // 开始精细搜索
                _startFineSearch();
            }
            else
            {
                _SetTableItem(m_currentColor, 1, QString::number(hole1Value));
                _SetTableItem(m_currentColor, 2, QString::number(hole2Value));
                _SetTableItem(m_currentColor, 3, QString::number(m_holeGain[0].currentGain[m_currentColor]));
                _SetTableItem(m_currentColor, 4, QString::number(m_holeGain[1].currentGain[m_currentColor]));
                _SetTableItem(m_currentColor, 5, tr("失败"));
                qDebug()<<"增益自动校准失败，差值超过50，执行后置时序";
                ShowError(nullptr, gk_strTipsText, tr("增益自动校准失败，差值超过50"));
                // 即使失败也要执行后置时序
                stopCalibration();
            }
            return;
        }


        // 调整增益继续二分查找
        if (hole1Value < m_targetValue[m_currentColor]) {
            m_holeGain[0].minGain[m_currentColor] = m_holeGain[0].currentGain[m_currentColor];
        } else {
            m_holeGain[0].maxGain[m_currentColor] = m_holeGain[0].currentGain[m_currentColor];
        }


        if (hole2Value < m_targetValue[m_currentColor]) {
            m_holeGain[1].minGain[m_currentColor] = m_holeGain[1].currentGain[m_currentColor];
        }
        else {
            m_holeGain[1].maxGain[m_currentColor] = m_holeGain[1].currentGain[m_currentColor]; 
        }
        
        
        m_holeGain[0].currentGain[m_currentColor] = (m_holeGain[0].minGain[m_currentColor] + m_holeGain[0].maxGain[m_currentColor]) / 2;
        m_holeGain[1].currentGain[m_currentColor] = (m_holeGain[1].minGain[m_currentColor] + m_holeGain[1].maxGain[m_currentColor]) / 2;
        m_isCalibrating = true;
        
        // 设置当前增益
        _setGain(m_currentColor, m_holeGain[0].currentGain[m_currentColor], m_holeGain[1].currentGain[m_currentColor]);
    }
}


void COpticalCalibration::_SlotMachineChanged(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

}

void COpticalCalibration::_SlotFamValueChange(const QString &strValue)
{
    m_targetValue[0] = strValue.toInt();
}

void COpticalCalibration::_SlotHexValueChange(const QString &strValue)
{
    m_targetValue[1] = strValue.toInt();
}

void COpticalCalibration::_SlotRoxValueChange(const QString &strValue)
{
    m_targetValue[2] = strValue.toInt();
}

void COpticalCalibration::_SlotCY5ValueChange(const QString &strValue)
{
    m_targetValue[3] = strValue.toInt();
}

void COpticalCalibration::startGainCalibration()
{
    m_holeGain[0].minGain[m_currentColor] = minGain;
    m_holeGain[0].maxGain[m_currentColor] = maxGain;
    m_holeGain[1].minGain[m_currentColor] = minGain;
    m_holeGain[1].maxGain[m_currentColor] = maxGain;

    m_holeGain[0].currentGain[m_currentColor] = (m_holeGain[0].minGain[m_currentColor] + m_holeGain[0].maxGain[m_currentColor]) / 2;
    m_holeGain[1].currentGain[m_currentColor] = (m_holeGain[1].minGain[m_currentColor] + m_holeGain[1].maxGain[m_currentColor]) / 2;
    m_isCalibrating = true;
    // 设置当前增益
    _setGain(m_currentColor, m_holeGain[0].currentGain[m_currentColor], m_holeGain[1].currentGain[m_currentColor]);
}

void COpticalCalibration::stopCalibration()
{
    m_isCalibrating = false;
    m_pCBusyProgressBar->hide();
    // 执行后置时序
    m_pLightOneTiming->startPostTiming(m_pMachineComboBox->GetCurrentIndex());
}

void COpticalCalibration::_SlotTimingStopped(void)
{
    qDebug() << "时序被停止";
    m_isCalibrating = false;
    m_pCBusyProgressBar->hide();
}

void COpticalCalibration::_SlotStopCalibration(void)
{
    qDebug() << "用户点击停止校准";
    // 停止当前时序
    m_pLightOneTiming->stopTiming();
}

void COpticalCalibration::_startFineSearch()
{
    qDebug() << "开始精细搜索，当前增益: hole1=" << m_holeGain[0].currentGain[m_currentColor]
             << " hole2=" << m_holeGain[1].currentGain[m_currentColor];

    m_isFineSearching = true;
    m_fineSearchIndex = 0;

    // 为两个孔分别生成±2范围内的5个增益值
    for (int hole = 0; hole < 2; hole++) {
        m_fineSearchGains[hole].clear();
        int baseGain = m_holeGain[hole].currentGain[m_currentColor];

        for (int offset = -2; offset <= 2; offset++) {
            int testGain = baseGain + offset;
            // 确保增益在有效范围内
            if (testGain >= minGain && testGain <= maxGain) {
                m_fineSearchGains[hole].append(testGain);
            }
        }
        qDebug() << "hole" << hole << "精细搜索增益列表:" << m_fineSearchGains[hole];
    }

    // 开始第一个增益的测试
    if (!m_fineSearchGains[0].isEmpty() && !m_fineSearchGains[1].isEmpty()) {
        int hole1Gain = m_fineSearchGains[0][m_fineSearchIndex];
        int hole2Gain = m_fineSearchGains[1][m_fineSearchIndex];
        qDebug() << "精细搜索第" << m_fineSearchIndex << "组增益: hole1=" << hole1Gain << " hole2=" << hole2Gain;
        _setGain(m_currentColor, hole1Gain, hole2Gain);
    } else {
        qDebug() << "精细搜索增益列表为空，结束搜索";
        m_isFineSearching = false;
        // 使用当前增益作为最终结果
        _SetTableItem(m_currentColor, 5, tr("失败"));
        stopCalibration();
    }
}

void COpticalCalibration::_SlotFamCalibrateBtn()
{
    m_currentColor = LIGHT_COLOR_FAM_INDEX;
    qDebug()<<"_SlotFamCalibrateBtn is called";

    m_pCBusyProgressBar->show();
    // 先执行前置时序
    m_pLightOneTiming->startPreTiming(m_pMachineComboBox->GetCurrentIndex());
}

void COpticalCalibration::_SlotHexCalibrateBtn()
{
    m_currentColor = LIGHT_COLOR_HEX_INDEX;
    qDebug()<<"_SlotHexCalibrateBtn is called";
    m_pCBusyProgressBar->show();
    // 先执行前置时序
    m_pLightOneTiming->startPreTiming(m_pMachineComboBox->GetCurrentIndex());
}

void COpticalCalibration::_SlotRoxCalibrateBtn()
{
    m_currentColor = LIGHT_COLOR_ROX_INDEX;
    qDebug()<<"_SlotRoxCalibrateBtn is called";
    m_pCBusyProgressBar->show();
    // 先执行前置时序
    m_pLightOneTiming->startPreTiming(m_pMachineComboBox->GetCurrentIndex());
}

void COpticalCalibration::_SlotCY5CalibrateBtn()
{
    m_currentColor = LIGHT_COLOR_CY5_INDEX;
    qDebug()<<"_SlotCY5CalibrateBtn is called";
    m_pCBusyProgressBar->show();
    // 先执行前置时序
    m_pLightOneTiming->startPreTiming(m_pMachineComboBox->GetCurrentIndex());
}

void COpticalCalibration::_SetTableItem(int iRow, int iCol, QString strText)
{
    QTableWidgetItem *pItem = m_pTableWidget->item(iRow, iCol);
    if(nullptr != pItem)
    {
        pItem->setText(strText);
        return;
    }
    pItem = new QTableWidgetItem;
    pItem->setText(strText);
    pItem->setTextAlignment(Qt::AlignCenter);
    m_pTableWidget->setItem(iRow, iCol, pItem);
}

void COpticalCalibration::_SetTableWidget(int iRow, int iCol, int iWidth, int iHeight, QWidget *pUIWidget)
{
    QWidget *pCellWidget = new QWidget;
    pCellWidget->setFixedSize(iWidth, iHeight);
    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(pUIWidget, 0, Qt::AlignVCenter);
    pLayout->addStretch(1);
    pCellWidget->setLayout(pLayout);
    m_pTableWidget->setCellWidget(iRow, iCol, pCellWidget);
}

void COpticalCalibration::_InitWidget()
{
    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(80, 50);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMachineChanged(int)));

    //m_pStackedWidget = new QStackedWidget;

    // 新增四个增益值输入框
    m_pFamValueLineEdit = new CLabelLineEdit(tr("FAM校准值"));
    m_pHexValueLineEdit = new CLabelLineEdit(tr("HEX校准值"));
    m_pRoxValueLineEdit = new CLabelLineEdit(tr("ROX校准值"));
    m_pCY5ValueLineEdit = new CLabelLineEdit(tr("CY5校准值"));

    // 设置输入框固定尺寸
    m_pFamValueLineEdit->SetLineEditFixedSize(80, 40);
    m_pHexValueLineEdit->SetLineEditFixedSize(80, 40);
    m_pRoxValueLineEdit->SetLineEditFixedSize(80, 40);
    m_pCY5ValueLineEdit->SetLineEditFixedSize(80, 40);

    m_pFamValueLineEdit->SetLineEditInputMethod(Qt::ImhDigitsOnly);
    m_pHexValueLineEdit->SetLineEditInputMethod(Qt::ImhDigitsOnly);
    m_pRoxValueLineEdit->SetLineEditInputMethod(Qt::ImhDigitsOnly);
    m_pCY5ValueLineEdit->SetLineEditInputMethod(Qt::ImhDigitsOnly);

    connect(m_pFamValueLineEdit, &CLabelLineEdit::SignalTextChanged,
            this, &COpticalCalibration::_SlotFamValueChange);
    connect(m_pHexValueLineEdit, &CLabelLineEdit::SignalTextChanged,
            this, &COpticalCalibration::_SlotHexValueChange);
    connect(m_pRoxValueLineEdit, &CLabelLineEdit::SignalTextChanged,
            this, &COpticalCalibration::_SlotRoxValueChange);
    connect(m_pCY5ValueLineEdit, &CLabelLineEdit::SignalTextChanged,
            this, &COpticalCalibration::_SlotCY5ValueChange);

    m_targetValue[0] = 1500; // FAM
    m_targetValue[1] = 1500; // HEX
    m_targetValue[2] = 1500; // ROX
    m_targetValue[3] = 1500; // CY5
    m_pFamValueLineEdit->SetLineEditText(QString::number(m_targetValue[0]));
    m_pHexValueLineEdit->SetLineEditText(QString::number(m_targetValue[1]));
    m_pRoxValueLineEdit->SetLineEditText(QString::number(m_targetValue[2]));
    m_pCY5ValueLineEdit->SetLineEditText(QString::number(m_targetValue[3]));
    
    // 新增四个校准按钮
    m_pFamCalibrateBtn = new QPushButton(tr("FAM校准"));
    m_pHexCalibrateBtn = new QPushButton(tr("HEX校准"));
    m_pRoxCalibrateBtn = new QPushButton(tr("ROX校准"));
    m_pCY5CalibrateBtn = new QPushButton(tr("CY5校准"));

    // 设置按钮固定尺寸
    m_pFamCalibrateBtn->setFixedSize(100, 40);
    m_pHexCalibrateBtn->setFixedSize(100, 40);
    m_pRoxCalibrateBtn->setFixedSize(100, 40);
    m_pCY5CalibrateBtn->setFixedSize(100, 40);

    // 连接按钮的点击信号到对应的槽函数
    connect(m_pFamCalibrateBtn, &QPushButton::clicked, this, &COpticalCalibration::_SlotFamCalibrateBtn);
    connect(m_pHexCalibrateBtn, &QPushButton::clicked, this, &COpticalCalibration::_SlotHexCalibrateBtn);
    connect(m_pRoxCalibrateBtn, &QPushButton::clicked, this, &COpticalCalibration::_SlotRoxCalibrateBtn);
    connect(m_pCY5CalibrateBtn, &QPushButton::clicked, this, &COpticalCalibration::_SlotCY5CalibrateBtn);

    QStringList strTitleList;
    strTitleList << tr("通道") << tr("孔1荧光值") << tr("孔2荧光值") << tr("孔1校准值") << tr("孔2校准值") << tr("结果") <<"";

    m_pTableWidget = new QTableWidget(this);
    m_pTableWidget->setColumnCount(strTitleList.size());
    m_pTableWidget->setHorizontalHeaderLabels(strTitleList);
    m_pTableWidget->setRowCount(4);
    m_pTableWidget->setFixedSize(1000, 290);

    QHeaderView* pVerticalHeader = m_pTableWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(60);
    QHeaderView* pHorizontalHeader = m_pTableWidget->horizontalHeader();
    pHorizontalHeader->resizeSection(0, 100);
    pHorizontalHeader->resizeSection(1, 150);
    pHorizontalHeader->resizeSection(2, 150);
    pHorizontalHeader->resizeSection(3, 150);
    pHorizontalHeader->resizeSection(4, 150);
    pHorizontalHeader->resizeSection(5, 150);
    pHorizontalHeader->resizeSection(6, 150);

    m_pTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pTableWidget->setSelectionMode(QAbstractItemView::NoSelection);
    m_pTableWidget->setShowGrid(true);
    m_pTableWidget->setFocusPolicy(Qt::NoFocus);
    m_pTableWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTableWidget->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);

    _SetTableItem(0, 0, tr("FAM"));
    _SetTableItem(1, 0, tr("HEX"));
    _SetTableItem(2, 0, tr("ROX"));
    _SetTableItem(3, 0, tr("CY5"));

    _SetTableWidget(0, 6, 150, 60, m_pFamCalibrateBtn);
    _SetTableWidget(1, 6, 150, 60, m_pHexCalibrateBtn);
    _SetTableWidget(2, 6, 150, 60, m_pRoxCalibrateBtn);
    _SetTableWidget(3, 6, 150, 60, m_pCY5CalibrateBtn);

    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(20);
    pTopLayout->addWidget(m_pMachineComboBox);
    pTopLayout->addWidget(m_pFamValueLineEdit);
    //pTopLayout->addWidget(m_pFamCalibrateBtn);
    pTopLayout->setSpacing(20);
    pTopLayout->addWidget(m_pHexValueLineEdit);
    //pTopLayout->addWidget(m_pHexCalibrateBtn);
    pTopLayout->setSpacing(20);
    pTopLayout->addWidget(m_pRoxValueLineEdit);
    //pTopLayout->addWidget(m_pRoxCalibrateBtn);
    pTopLayout->setSpacing(20);
    pTopLayout->addWidget(m_pCY5ValueLineEdit);
    //pTopLayout->addWidget(m_pCY5CalibrateBtn);
    pTopLayout->addStretch(1); // 添加伸缩项实现左对齐

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(10);
    pLayout->addLayout(pTopLayout);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pTableWidget, 0, Qt::AlignCenter);
    pLayout->addStretch(1); // 添加伸缩项实现左对齐

    this->setLayout(pLayout);
}
