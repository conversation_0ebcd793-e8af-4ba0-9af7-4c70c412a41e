QLabel
{
   color: #353E4E;
   font-size: 24px;
   font-family: "Source Han Sans CN";   
   background-color: transparent;
}
QLabel#DateLabel
{
   border: 0px solid #CAD2DC;
   border-radius: 28px;
   background-color: #F3F8FF;
}
QLabel#TimeLabel
{
   border: 0px solid #CAD2DC;
   border-radius: 28px;
   background-color: #F3F8FF;
}
QLabel#DateLabel:disabled
{
   border: 2px solid #0064CF;
   border-radius: 28px;
   background-color: #F3F8FF;
}
QLabel#TimeLabel:disabled
{
   border: 2px solid #0064CF;
   border-radius: 28px;
   background-color: #F3F8FF;
}

QLabel#TitleLabel
{
   color: #0064CF;
}

QPushButton
{
   color: #FFF;
   font-size: 22px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #8490FF, stop: 1 #3D78E5);
}
QPushButton:pressed
{
   color: #FFF;
   font-size: 22px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #6670c7, stop: 1 #3361b8);
}
QPushButton:focus{outline: none;}

QPushButton#CancelBtn
{
   color: #3D78E5;
   font-size: 22px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 2px solid #3D78E5;
   background-color: #FFF;
}

QPushButton#CancelBtn:hover
{
   color: #3D78E5;
   font-size: 22px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 3px solid #3D78E5;
   background-color: #FFF;
}

QPushButton#CancelBtn:pressed
{
   color: #3D78E5;
   font-size: 22px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 4px solid #3D78E5;
   background-color: #FFF;
}

QListWidget
{
    color: #353E4E;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    border-radius: 0px;
    border: 0px solid #CAD2DC;
    background-color: #FFF;
    outline: none;
}
QListWidget::Item:selected
{
    background-color: #0064CF;
    color: #FFF;
}

QGroupBox
{
    border: 0px solid #CAD2DC;
    border-radius: 32px;
    font-size: 18px;
    font-family:"Source Han Sans CN";
    background-color: #FFF;
}

QScrollBar:vertical
{
    width: 28px;
    background: #F0F0F0;
    padding-top: 30px;
    padding-bottom: 30px;
}

QScrollBar::handle:vertical
{
    width: 28px;
    background: #B6B6B6;
    min-height: 35px;
}

