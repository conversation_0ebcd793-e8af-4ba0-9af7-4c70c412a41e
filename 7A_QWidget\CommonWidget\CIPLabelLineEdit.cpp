#include "CIPLabelLineEdit.h"
#include <QHBoxLayout>

CIPLabelLineEdit::CIPLabelLineEdit(const QString &strName, const QStringList &strList, QWidget *parent)
    : QWidget(parent)
    , m_strName(strName)
    , m_strValueList(strList)
{
    _InitWidget();
}

CIPLabelLineEdit::~CIPLabelLineEdit()
{

}

QStringList CIPLabelLineEdit::GetValueList() const
{
    QStringList strList;
    for(int i=0; i<m_pLineEditList.size(); i++)
        strList<<m_pLineEditList.at(i)->text();
    return strList;
}

void CIPLabelLineEdit::SetValueList(const QStringList &strList)
{
    if(strList.size() >= 4)
    {
        for(int i=0; i<4; i++)
        {
            m_pLineEditList[i]->setText(strList.at(i));
        }
    }
}

void CIPLabelLineEdit::SetPlaceholderText(const QStringList &strList)
{
    if(strList.size() >= 4)
    {
        for(int i=0; i<4; i++)
        {
            m_pLineEditList[i]->setPlaceholderText(strList.at(i));
        }
    }
}

void CIPLabelLineEdit::SetPointLabelVisable(bool bVisable)
{
    for(int i=0; i<m_pPointLabelList.size(); i++)
    {
        m_pPointLabelList.at(i)->setVisible(bVisable);
    }
}

void CIPLabelLineEdit::ResetLabelSize(int iWidth, int iHeight)
{
    m_pLabel->setFixedSize(iWidth, iHeight);
}

void CIPLabelLineEdit::ResetLineEditSize(int iWidth, int iHeight)
{
    for(int i=0; i<m_pLineEditList.size(); i++)
        m_pLineEditList.at(i)->setFixedSize(iWidth, iHeight);
}

void CIPLabelLineEdit::SetLineTextEnable(bool bEnable)
{
    for(int i=0; i<m_pLineEditList.size(); i++)
        m_pLineEditList.at(i)->setEnabled(bEnable);

}

void CIPLabelLineEdit::ClearEdit()
{
    for(int i=0; i<m_pLineEditList.size(); i++)
        m_pLineEditList.at(i)->clear();

}

void CIPLabelLineEdit::_InitWidget()
{
    m_pLabel = new QLabel(m_strName);
   // m_pLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
  //  m_pLabel->setFixedSize(90,60);

    for(int i=0; i<3; i++)
    {
        QLabel *pObj = new QLabel;
        pObj->setFixedSize(6, 6);
        pObj->setStyleSheet("QLabel{background-color:#888}");
        m_pPointLabelList.push_back(pObj);
    }

    for(int i=0; i<4; i++)
    {
        CLineEdit *pObj = new CLineEdit;
        pObj->setInputMethodHints(Qt::ImhDigitsOnly);
        pObj->setAlignment(Qt::AlignCenter);
        pObj->setFixedSize(70, 60);
        m_pLineEditList.push_back(pObj);
    }

    SetValueList(m_strValueList);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(10);
    pLayout->addWidget(m_pLabel);
    pLayout->addWidget(m_pLineEditList.at(0));
    pLayout->addWidget(m_pPointLabelList.at(0), 0, Qt::AlignBottom);
    pLayout->addWidget(m_pLineEditList.at(1));
    pLayout->addWidget(m_pPointLabelList.at(1), 0, Qt::AlignBottom);
    pLayout->addWidget(m_pLineEditList.at(2));
    pLayout->addWidget(m_pPointLabelList.at(2), 0, Qt::AlignBottom);
    pLayout->addWidget(m_pLineEditList.at(3));
    this->setLayout(pLayout);
}

