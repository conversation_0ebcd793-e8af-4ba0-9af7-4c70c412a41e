#include "CDateTime.h"
#include <QSize>
#include <QPainter>
#include <QVBoxLayout>
#include <QDateTime>

#include "PublicParams.h"
#include "PublicFunction.h"

CTimeObject::CTimeObject(int iMinValue, int iMaxValue, int iCurrentValue,
                         const QString &strName, QWidget *parent)
    : QWidget(parent)
    , m_iMinValue(iMinValue)
    , m_iMaxValue(iMaxValue)
    , m_iCurrentValue(iCurrentValue)
    , m_strName(strName)
{
    _InitWidget();
}

CTimeObject::~CTimeObject()
{

}

int CTimeObject::GetValue() const
{
    return m_pLineEdit->text().toInt();
}

void CTimeObject::SetValue(int iValue)
{
    m_iCurrentValue = iValue;
    m_pLineEdit->setText(QString::number(iValue));
}

void CTimeObject::SetRange(int iMinValue, int iMaxValue)
{
    m_iMinValue = iMinValue;
    m_iMaxValue = iMaxValue;
}

void CTimeObject::_SlotAddBtn()
{
    m_iCurrentValue++;
    if(m_iCurrentValue > m_iMaxValue)
        m_iCurrentValue = m_iMinValue;
    emit SignalValueChanged(m_iCurrentValue);
    m_pLineEdit->setText(QString::number(m_iCurrentValue));
}

void CTimeObject::_SlotSubBtn()
{     
    m_iCurrentValue--;
    if(m_iCurrentValue < m_iMinValue)
        m_iCurrentValue = m_iMaxValue;
    emit SignalValueChanged(m_iCurrentValue);
    m_pLineEdit->setText(QString::number(m_iCurrentValue));
}

void CTimeObject::_InitWidget()
{
    QSize qSize(80,50);

    m_pLabel = new QLabel(m_strName);
    m_pLabel->setAlignment(Qt::AlignCenter);
    m_pLabel->setFixedSize(qSize);

    m_pAddBtn = new QPushButton;
    m_pAddBtn->setObjectName("AddBtn");
    m_pAddBtn->setFixedSize(qSize);
    m_pAddBtn->setAutoRepeat(true);
    m_pAddBtn->setAutoRepeatInterval(100);
    connect(m_pAddBtn, &QPushButton::clicked, this, &CTimeObject::_SlotAddBtn);

    m_pLineEdit = new CLineEdit(QString::number(m_iCurrentValue));
    m_pLineEdit->setAlignment(Qt::AlignCenter);
    m_pLineEdit->setFixedSize(qSize);
    m_pLineEdit->setEnabled(false);

    m_pSubBtn = new QPushButton;
    m_pSubBtn->setObjectName("SubBtn");
    m_pSubBtn->setFixedSize(qSize);
    m_pSubBtn->setAutoRepeat(true);
    m_pSubBtn->setAutoRepeatInterval(100);
    connect(m_pSubBtn, &QPushButton::clicked, this, &CTimeObject::_SlotSubBtn);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->addWidget(m_pLabel, 0, Qt::AlignHCenter);
    pLayout->addSpacing(5);
    pLayout->addWidget(m_pAddBtn, 0, Qt::AlignHCenter);
    pLayout->addSpacing(15);
    pLayout->addWidget(m_pLineEdit, 0, Qt::AlignHCenter);
    pLayout->addSpacing(15);
    pLayout->addWidget(m_pSubBtn, 0, Qt::AlignHCenter);
    setLayout(pLayout);
}

CDateTime::CDateTime(QWidget *parent) : QWidget(parent)
{
    m_iBigMonthList<<1<<3<<5<<7<<8<<10<<12;
    m_iSmallMonthList<<2<<4<<6<<9<<11;

    _InitWidget();
}

CDateTime::~CDateTime()
{

}

void CDateTime::SetDate(const QString &strDate)
{
    QDate qDate = QDate::fromString(strDate, "yyyy-MM-dd");
    if(!qDate.isValid() || qDate.isNull())
        qDate = QDate::currentDate();

    m_pYearWidget->SetValue(qDate.year());
    m_pMonthWidget->SetValue(qDate.month());
    m_pDayWidget->SetValue(qDate.day());
}

QString CDateTime::GetDate() const
{
    QString strDate = QString("%1-%2-%3")
            .arg(m_pYearWidget->GetValue())
            .arg(m_pMonthWidget->GetValue())
            .arg(m_pDayWidget->GetValue());
    return strDate;
}

void CDateTime::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, gk_iBackTransparency));
    QWidget::paintEvent(pEvent);
}

void CDateTime::_SlotConfirmBtn()
{
    QDate qDate = QDate(m_pYearWidget->GetValue(),
                        m_pMonthWidget->GetValue(),
                        m_pDayWidget->GetValue());
    if(!qDate.isValid())
        qDate = QDate::currentDate();
    QString strDate = qDate.toString("yyyy-MM-dd");
    emit SignalConfirmDate(strDate);
    this->close();
}

void CDateTime::_SlotCancelBtn()
{
    this->close();
}

void CDateTime::_SlotYearChanged(int iYear)
{
    int iMonth = m_pMonthWidget->GetValue();
    if(2 != iMonth)
        return;

    int iMin = 1;
    int iMax = 28;
    bool bLeapYear = QDate::isLeapYear(iYear);
    if(bLeapYear)
        iMax = 29;
    m_pDayWidget->SetRange(iMin, iMax);
    if(m_pDayWidget->GetValue() > iMax)
        m_pDayWidget->SetValue(iMax);
}

//大月1-31天;小月:闰年(1-29),非闰年(1-28)
void CDateTime::_SlotMonthChanged(int iMonth)
{
    if(m_iBigMonthList.contains(iMonth))
    {
        m_pDayWidget->SetRange(1,31);
        return;
    }

    int iYear = m_pYearWidget->GetValue();
    bool bLeapYear = QDate::isLeapYear(iYear);

    if(m_iSmallMonthList.contains(iMonth))
    {
        int iMin = 1;
        int iMax = 30;
        if(2 == iMonth)
        {
            if(bLeapYear)
                iMax = 29;
            else
                iMax = 28;
        }
        m_pDayWidget->SetRange(iMin, iMax);
        if(m_pDayWidget->GetValue() > iMax)
            m_pDayWidget->SetValue(iMax);
    }
}

void CDateTime::_InitWidget()
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setAttribute(Qt::WA_TranslucentBackground);
    this->setFixedSize(1494, 798);
    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroup(), 0, Qt::AlignCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
    LoadQSS(this,":/qss/qss/default.qss");
}

QGroupBox *CDateTime::_CreateGroup()
{
    QGroupBox *pGroupBox = new QGroupBox(this);
    pGroupBox->setWindowOpacity(1);
    pGroupBox->setFixedSize(500, 400);

    int iYear = QDate::currentDate().year();
    int iMonth = QDate::currentDate().month();
    int iDay = QDate::currentDate().day();
    m_pYearWidget = new CTimeObject(1970, 2099, iYear, tr("年"));
    m_pMonthWidget = new CTimeObject(1, 12, iMonth, tr("月"));
    m_pDayWidget = new CTimeObject(1, 31, iDay, tr("日"));
    connect(m_pYearWidget, &CTimeObject::SignalValueChanged, this, &CDateTime::_SlotYearChanged);
    connect(m_pMonthWidget, &CTimeObject::SignalValueChanged, this, &CDateTime::_SlotMonthChanged);
    //确定当前月份有几天
    _SlotYearChanged(iYear);
    _SlotMonthChanged(iMonth);

    m_pConfirmBtn = new QPushButton(tr("确认"));
    m_pConfirmBtn->setFixedSize(120, 50);
    connect(m_pConfirmBtn, &QPushButton::clicked, this, &CDateTime::_SlotConfirmBtn);

    m_pCancelBtn = new QPushButton(tr("取消"));
    m_pCancelBtn->setFixedSize(120, 50);
    connect(m_pCancelBtn, &QPushButton::clicked, this, &CDateTime::_SlotCancelBtn);

    QHBoxLayout *pTimeLayout = new QHBoxLayout;
    pTimeLayout->setSpacing(15);
    pTimeLayout->addStretch(1);
    pTimeLayout->addWidget(m_pYearWidget);
    pTimeLayout->addWidget(m_pMonthWidget);
    pTimeLayout->addWidget(m_pDayWidget);
    pTimeLayout->addStretch(1);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setSpacing(20);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pConfirmBtn);
    pBtnLayout->addWidget(m_pCancelBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->addStretch(1);
    pLayout->addLayout(pTimeLayout);
    pLayout->addSpacing(20);
    pLayout->addLayout(pBtnLayout);
    pLayout->addStretch(1);

    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}
