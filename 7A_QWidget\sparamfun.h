﻿#ifndef SPARAMFUN_H
#define SPARAMFUN_H

#include <vector>

// Function to correct outliers in a vector using MAD尖刺平滑处理
std::vector<double> correctOutliersWithMAD(std::vector<double> data, double multiplier = 5);
//滑动窗口z-score算法 - 尖刺
std::vector<double> correctOutliersWithZScore(std::vector<double> data,
                                              int windowSize = 5, double bandwidth = 1);
// Hampel标识符方法-尖峰
void hampelFilter(std::vector<double>& data, int windowSize = 5, double n_sigma = 3);
// Function to calculate LOESS for a series of points 平滑处理
std::vector<double> Loess(const std::vector<double>& xs, const std::vector<double>& ys,
                          const std::vector<double>& smoothing_xs, double bandwidth = 1) ;

#endif // SPARAMFUN_H
