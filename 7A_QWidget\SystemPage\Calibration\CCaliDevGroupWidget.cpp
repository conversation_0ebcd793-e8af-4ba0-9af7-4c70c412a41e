#include "CCaliDevGroupWidget.h"
#include <QBoxLayout>
#include "PublicConfig.h"

CCaliDevGroupWidget::CCaliDevGroupWidget(const QString &strDevName, const SDevParamsStruct &sDevParams,
                                         const QList<int> &iMachineIDList, QWidget *parent)
    : QWidget(parent)
    , m_strDevName(strDevName)
    , m_sDevParams(sDevParams)
    , m_iMachineIDList(iMachineIDList)
{
    this->setFixedSize(m_sDevParams.iGroupWidth, m_sDevParams.iGroupHeight);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroupBox());
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}

QGroupBox *CCaliDevGroupWidget::_CreateGroupBox()
{
    QHBoxLayout *pItemLayout = new QHBoxLayout;
    pItemLayout->setMargin(0);
    pItemLayout->setSpacing(24);
    pItemLayout->addStretch(1);
    for(int i=0; i<m_iMachineIDList.size(); i++)
    {
        CCaliDevItemWidget *pDevItemWidget = new CCaliDevItemWidget(m_iMachineIDList.at(i), m_sDevParams);
        m_pDevItemWidgetList.push_back(pDevItemWidget);
        pItemLayout->addWidget(pDevItemWidget);
    }
    pItemLayout->addStretch(1);

    m_pTitleWidget = new CHLabelTitleWidget(m_strDevName);

    QHBoxLayout *pTitleLayout = new QHBoxLayout;
    pTitleLayout->setMargin(0);
    pTitleLayout->setSpacing(0);
    pTitleLayout->addSpacing(24);
    pTitleLayout->addWidget(m_pTitleWidget);
    pTitleLayout->addStretch(1);

    int iDevGroupNum = -1, iDevItemNum = -1;
    CPublicConfig::GetInstance()->GetDevItemNum(iDevGroupNum, iDevItemNum);

    QBoxLayout *pLayout = nullptr;
    if(3 == iDevGroupNum && 4 == iDevItemNum) //改水平布局
    {
        pLayout = new QHBoxLayout;
        pLayout->setMargin(0);
        pLayout->setSpacing(0);

        QLabel *pLabel = new QLabel;
        pLabel->setWordWrap(true);
        pLabel->setAlignment(Qt::AlignTop);
        pLabel->setObjectName("VTitleTextLabel");
        pLabel->setText(m_strDevName.split("", QString::SkipEmptyParts).join("\n"));
        pLayout->addSpacing(24);
        pLayout->addWidget(pLabel, 0, Qt::AlignVCenter);
        pLayout->addStretch(1);
        pLayout->addLayout(pItemLayout);
        pLayout->addSpacing(24);
    }
    else
    {
        pLayout = new QVBoxLayout;
        pLayout->setMargin(0);
        pLayout->setSpacing(0);
        pLayout->addSpacing(16);
        pLayout->addLayout(pTitleLayout);
        pLayout->addStretch(1);
        pLayout->addLayout(pItemLayout);
        pLayout->addStretch(1);
    }

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(m_sDevParams.iGroupWidth, m_sDevParams.iGroupHeight);
    pGroupBox->setObjectName("PLCDevGroupBox");
    pGroupBox->setLayout(pLayout);

    return pGroupBox;
}
