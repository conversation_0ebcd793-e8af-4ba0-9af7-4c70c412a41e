#ifndef CMOTORCALIBRATE_H
#define CMOTORCALIBRATE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: sfni
  * Date: 2024-09-04
  * Description: 电机校准
  * -------------------------------------------------------------------------
  *
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QPushButton>

#include "CCmdBase.h"
#include "CLineEdit.h"
#include "CLabelComboBox.h"
#include "CTextBrowser.h"

typedef struct{
    int index;
    QVector<double> flValueVector;
}sFLMotorCalibrateCtrl_t;

class CMotorCalibrate : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CMotorCalibrate(QWidget *parent = nullptr);
    ~CMotorCalibrate();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

public slots:


protected:
    enum eCalibrateState{
        CALIBRATE_IDLE = 0,
        CALIBRATE_FL_MOTOR_RESET,
        CALIBRATE_WAIT_JSON_REPLY,
        CALIBRATE_PCR_MOTOR_PRESS,
        CALIBRATE_LED_ON,
        CALIBRATE_SEND_FLADC,
        CALIBRATE_FL_MOVE,
        CALIBRATE_LEN_OFF,
        CALIBRATE_PCR_MOTOR_RESET,
        CALIBRATE_DATA_ANALYSIS,
    };

private slots:
    void _SlotFLMotorCalibrateBtn();

private:
    void _InitWidget();
    void _FLMotorCalibrateHandle();

private:
    QTimer* m_Timer;

    eCalibrateState _calibrateState;
    CLabelComboBox *m_pMachineComboBox;
    QPushButton *m_pFLMotorCalibrateButton;
    CTextBrowser *m_pTextBrowser;

    sFLMotorCalibrateCtrl_t _flCalibrateCtrl;
};

#endif // CMOTORCALIBRATE_H
