#include "algorithmdescrypt.h"

// 初始换位表
int AlgorithmDesCrypt::m_siTableIP[64] = {
    58, 50, 42, 34, 26, 18, 10, 2,
    60, 52, 44, 36, 28, 20, 12, 4,
    62, 54, 46, 38, 30, 22, 14, 6,
    64, 56, 48, 40, 32, 24, 16, 8,
    57, 49, 41, 33, 25, 17,  9, 1,
    59, 51, 43, 35, 27, 19, 11, 3,
    61, 53, 45, 37, 29, 21, 13, 5,
    63, 55, 47, 39, 31, 23, 15, 7
};

// PC1换位表（64—>56）
int AlgorithmDesCrypt::m_siTablePC1[56] = {
    57, 49, 41, 33, 25, 17,  9,
    1, 58, 50, 42, 34, 26, 18,
    10,  2, 59, 51, 43, 35, 27,
    19, 11,  3, 60, 52, 44, 36,
    63, 55, 47, 39, 31, 23, 15,
    7, 62, 54, 46, 38, 30, 22,
    14,  6, 61, 53, 45, 37, 29,
    21, 13,  5, 28, 20, 12,  4
};

// PC2换位表（56—>48）
int AlgorithmDesCrypt::m_siTablePC2[48] = {
    14, 17, 11, 24,  1,  5,
    3, 28, 15,  6, 21, 10,
    23, 19, 12,  4, 26,  8,
    16,  7, 27, 20, 13,  2,
    41, 52, 31, 37, 47, 55,
    30, 40, 51, 45, 33, 48,
    44, 49, 39, 56, 34, 53,
    46, 42, 50, 36, 29, 32
};

// 扩展换位表
int AlgorithmDesCrypt::m_siTableExtension[48] = {
    32,  1,  2,  3,  4,  5,
    4,  5,  6,  7,  8,  9,
    8,  9, 10, 11, 12, 13,
    12, 13, 14, 15, 16, 17,
    16, 17, 18, 19, 20, 21,
    20, 21, 22, 23, 24, 25,
    24, 25, 26, 27, 28, 29,
    28, 29, 30, 31, 32,  1
};

// 循环移位表
int AlgorithmDesCrypt::m_siTableMove[16] = {
    1, 1, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 1
};

// S盒换位表
int AlgorithmDesCrypt::m_siTableSBox[8][4][16] = {
    {
        {14,  4, 13, 1,  2, 15, 11,  8,  3, 10,  6, 12,  5,  9, 0,  7},
        { 0, 15,  7, 4, 14,  2, 13,  1, 10,  6, 12, 11,  9,  5, 3,  8},
        { 4,  1, 14, 8, 13,  6,  2, 11, 15, 12,  9,  7,  3, 10, 5,  0},
        {15, 12,  8, 2,  4,  9,  1,  7,  5, 11,  3, 14, 10,  0, 6, 13}
    },
    {
        {15,  1,  8, 14,  6, 11,  3,  4,  9, 7,  2, 13, 12, 0,  5, 10},
        { 3, 13,  4,  7, 15,  2,  8, 14, 12, 0,  1, 10,  6, 9, 11,  5},
        { 0, 14,  7, 11, 10,  4, 13,  1,  5, 8, 12,  6,  9, 3,  2, 15},
        {13,  8, 10,  1,  3, 15,  4,  2, 11, 6,  7, 12,  0, 5, 14,  9}
    },
    {
        {10,  0,  9, 14, 6,  3, 15,  5,  1, 13, 12,  7, 11,  4,  2,  8},
        {13,  7,  0,  9, 3,  4,  6, 10,  2,  8,  5, 14, 12, 11, 15,  1},
        {13,  6,  4,  9, 8, 15,  3,  0, 11,  1,  2, 12,  5, 10, 14,  7},
        { 1, 10, 13,  0, 6,  9,  8,  7,  4, 15, 14,  3, 11,  5,  2, 12}
    },
    {
        { 7, 13, 14, 3,  0,  6,  9, 10,  1, 2, 8,  5, 11, 12,  4, 15},
        {13,  8, 11, 5,  6, 15,  0,  3,  4, 7, 2, 12,  1, 10, 14,  9},
        {10,  6,  9, 0, 12, 11,  7, 13, 15, 1, 3, 14,  5,  2,  8,  4},
        { 3, 15,  0, 6, 10,  1, 13,  8,  9, 4, 5, 11, 12,  7,  2, 14}
    },
    {
        { 2, 12,  4,  1,  7, 10, 11,  6,  8,  5,  3, 15, 13, 0, 14,  9},
        {14, 11,  2, 12,  4,  7, 13,  1,  5,  0, 15, 10,  3, 9,  8,  6},
        { 4,  2,  1, 11, 10, 13,  7,  8, 15,  9, 12,  5,  6, 3,  0, 14},
        {11,  8, 12,  7,  1, 14,  2, 13,  6, 15,  0,  9, 10, 4,  5,  3}
    },
    {
        {12,  1, 10, 15, 9,  2,  6,  8,  0, 13,  3,  4, 14,  7,  5, 11},
        {10, 15,  4,  2, 7, 12,  9,  5,  6,  1, 13, 14,  0, 11,  3,  8},
        { 9, 14, 15,  5, 2,  8, 12,  3,  7,  0,  4, 10,  1, 13, 11,  6},
        { 4,  3,  2, 12, 9,  5, 15, 10, 11, 14,  1,  7,  6,  0,  8, 13}
    },
    {
        { 4, 11,  2, 14, 15, 0,  8, 13,  3, 12, 9,  7,  5, 10, 6,  1},
        {13,  0, 11,  7,  4, 9,  1, 10, 14,  3, 5, 12,  2, 15, 8,  6},
        { 1,  4, 11, 13, 12, 3,  7, 14, 10, 15, 6,  8,  0,  5, 9,  2},
        { 6, 11, 13,  8,  1, 4, 10,  7,  9,  5, 0, 15, 14,  2, 3, 12}
    },
    {
        {13,  2,  8, 4,  6, 15, 11,  1, 10,  9,  3, 14,  5,  0, 12,  7},
        { 1, 15, 13, 8, 10,  3,  7,  4, 12,  5,  6, 11,  0, 14,  9,  2},
        { 7, 11,  4, 1,  9, 12, 14,  2,  0,  6, 10, 13, 15,  3,  5,  8},
        { 2,  1, 14, 7,  4, 10,  8, 13, 15, 12,  9,  0,  3,  5,  6, 11}
    }
};

// P盒换位表
int AlgorithmDesCrypt::m_siTablePBox[32] = {
    16, 7, 20, 21, 29, 12, 28, 17,  1, 15, 23, 26,  5, 18, 31, 10,
    2, 8, 24, 14, 32, 27,  3,  9, 19, 13, 30,  6, 22, 11,  4, 25
};

AlgorithmDesCrypt::AlgorithmDesCrypt()
{
    m_strKey = QString("Wondfoco"); //密钥 Wondfofc    Wondfoad
    m_strSrc.clear();               // 明文0清空
    m_strVec = QString("Wondfoco"); // CBC模式加密向量值
}

void AlgorithmDesCrypt::SetKey(const QString &strKey)
{
    m_strKey = strKey; //
    m_strSrc.clear();  // 明文0清空
//    m_strVec = strKey; // CBC模式加密向量值
}

void AlgorithmDesCrypt::SetSrc(const QString &src)
{
    m_strSrc = src;
}

QString AlgorithmDesCrypt::encrypt(bool *ok)
{
    // 明文判断
    if (m_strSrc.isEmpty())
    {
        if (nullptr != ok) *ok = false;
        qWarning() << Q_FUNC_INFO << __LINE__ << "明文为空!";
        return "";
    }

    // 密钥操作
    if (8 != m_strKey.count())
    {
        if (nullptr != ok) *ok = false;
        qWarning() << Q_FUNC_INFO << __LINE__ << "密钥长度不足8位，" << m_strKey << m_strKey.count();
        return "";
    }
    _CreateSubkey(); // 子密钥生成

    QList<int> iDecList = _Encrypt();
    // 十进制转换成16进制
    QString pwd;
    for (int i = 0; i < iDecList.count(); ++i)
    {
        pwd += QString::number(iDecList.at(i), 16).toUpper();
    }
#ifndef QT_NO_DEBUG
    qDebug() << Q_FUNC_INFO << __LINE__ << "生成的原始密文为:" << pwd << pwd.count();
#endif

    // 密文分组8组
    int iCount = iDecList.count();
    int iDecArray[8] = {0, 0, 0, 0, 0, 0, 0, 0};
    for (int i = 0; i < iCount;)
    {
        iDecArray[0] += iDecList.at(i++);
        iDecArray[1] += iDecList.at(i++);
        iDecArray[2] += iDecList.at(i++);
        iDecArray[3] += iDecList.at(i++);
        iDecArray[4] += iDecList.at(i++);
        iDecArray[5] += iDecList.at(i++);
        iDecArray[6] += iDecList.at(i++);
        iDecArray[7] += iDecList.at(i++);
    }
    pwd.clear();
    for (int i = 0; i < 8; ++i)
    {
        pwd += QString::number(iDecArray[i] % 16, 16).toUpper();
    }
#ifndef QT_NO_DEBUG
    qDebug() << Q_FUNC_INFO << __LINE__ << "修改后的激活码为:" << pwd << pwd.count();
#endif

    if (nullptr != ok) *ok = true;
    return pwd;
}

void AlgorithmDesCrypt::_CreateSubkey()
{
    int bKey[64]; // 二进制密钥
    for (int i = 0; i < 8; ++i)
    {
        int t[8] = {0, 0, 0, 0, 0, 0, 0, 0};
        int k = m_strKey.at(i).unicode();
        for (int j = 0; k != 0; ++j)
        {
            t[j] = k % 2;
            k = k / 2;
        }
        for (int j = 0; j < 8; ++j)
            bKey[i * 8 + j] = t[7 - j];
    }
#ifndef QT_NO_DEBUG
    // 打印转换后的二进制密钥
    QString strKeyOrigianl;
    for (int i = 0; i < 64; ++i)
        strKeyOrigianl += QString("%1,").arg(bKey[i]);
    qDebug() << Q_FUNC_INFO << __LINE__ << "二进制密钥:" << strKeyOrigianl;
#endif

    // 二进制密钥通过PC1转换成56位密钥
    int bKeyPC1[56];
    for (int i = 0; i < 56; ++i)
        bKeyPC1[i] = bKey[m_siTablePC1[i] - 1];
    // 56位密钥拆分成A、B部分
    int bKeyA[28], bKeyB[28];
    for (int i = 0; i < 28; ++i)
    {
        bKeyA[i] = bKeyPC1[i];
        bKeyB[i] = bKeyPC1[i + 28];
    }
    // 密钥循环左移位16次生成 16 * 48位子密钥
    for (int i = 0; i < 16; ++i)
    {
        // 移位只有两种：1 | 2
        if (1 == m_siTableMove[i])
        {
            int n = bKeyA[0];
            for (int j = 0; j < 27; ++j)
                bKeyA[j] = bKeyA[j + 1];
            bKeyA[27] = n;
            n = bKeyB[0];
            for (int j = 0; j < 27; ++j)
                bKeyB[j] = bKeyB[j + 1];
            bKeyB[27] = n;
        }
        else
        {
            int m = bKeyA[0];
            int n = bKeyA[1];
            for (int j = 0; j < 26; ++j)
                bKeyA[j] = bKeyA[j + 2];
            bKeyA[26] = m;
            bKeyA[27] = n;
            m = bKeyB[0];
            n = bKeyB[1];
            for (int j = 0; j < 26; ++j)
                bKeyB[j] = bKeyB[j + 2];
            bKeyB[26] = m;
            bKeyB[27] = n;
        }
        // 重新组合移位后的密钥
        int bKeyCon[56];
        for (int j = 0; j < 28; j++)
        {
            bKeyCon[j] = bKeyA[j];
            bKeyCon[j + 28] = bKeyB[j];
        }
        // 通过PC2获得移位后的子密钥
        for (int j = 0; j < 48; ++j)
            m_iSubkey[i][j] = bKeyCon[m_siTablePC2[j] - 1];
    }
#ifndef QT_NO_DEBUG
    // 打印 48bit * 16 位密钥
    qDebug() << Q_FUNC_INFO << __LINE__ << "打印 48bit * 16 子密钥:";
    for (int i = 0; i < 16; ++i)
    {
        QString text;
        for (int j = 0; j < 48; ++j)
            text += QString("%1,").arg(m_iSubkey[i][j]);
        qDebug() << text;
    }
#endif
}

QList<int> AlgorithmDesCrypt::_Encrypt()
{
    // 加密向量
    int bTextOut[14][64]; //存放初始化向量和所有经过DES算法处理的分组的二进制
    int bResult[13][64]; // 存放加密后的二进制密文
    for (int i = 0; i < 8; ++i)
    {
        int t[8] = {0, 0, 0, 0, 0, 0, 0, 0};
        int n = m_strVec.at(i).unicode();
        for (int j = 0; n != 0; ++j)
        {
            t[j] = n % 2;
            n = n / 2;
        }
        for (int j = 0; j < 8; ++j)
            bTextOut[0][i * 8 + j] = t[7 - j];
    }
#ifndef QT_NO_DEBUG
    // 打印加密向量二进制
    qDebug() << Q_FUNC_INFO << __LINE__ << "打印 64bit 二进制向量:";
    QString strVec;
    for (int i = 0; i < 64; ++i)
        strVec += QString("%1,").arg(bTextOut[0][i]);
    qDebug() << strVec;
#endif

    // 本算法使用CBC模式加密
    // 明文分组,8个字符一组
    const int srcCount = m_strSrc.count();
    int iRemainder = srcCount % 8; // 最后一组余数
    int iGroup = (srcCount - 1) / 8 + 1; // 分组
    qDebug() << Q_FUNC_INFO << __LINE__ << "需要加密的明文:" << m_strSrc << srcCount << iGroup << iRemainder;
    for (int i = 0; i < iGroup; ++i)
    {
        // 取得分组明文
        int iGroupText[8];
        if (i == (iGroup - 1) && iRemainder != 0) // 最后一组存在余数,需要用空格填充
        {
            for (int j = 0; j < iRemainder; ++j)
                iGroupText[j] = m_strSrc.at(i * 8 + j).unicode();
            for (int j = iRemainder; j < 8; ++j)
                iGroupText[j] = QChar(' ').unicode();
        }
        else
        {
            for (int j = 0; j < 8; ++j)
                iGroupText[j] = m_strSrc.at(i * 8 + j).unicode();
        }
#ifndef QT_NO_DEBUG
        //打印分组明文
        QString strPlain;
        for (int j = 0; j < 8; ++j)
            strPlain += QString("%1,").arg(iGroupText[j]);
        qDebug() << Q_FUNC_INFO << __LINE__ << "当前分组明文:" << i + 1 << strPlain;
#endif

        // 分组明文转换成 64bit 二进制
        int bText[64];
        for (int j = 0; j < 8; ++j)
        {
            int t[8] = {0, 0, 0, 0, 0, 0, 0, 0};
            int n = iGroupText[j];
            for (int k = 0; n != 0; ++k)
            {
                t[k] = n % 2;
                n = n / 2;
            }
            for (int k = 0; k < 8; ++k)
                bText[j * 8 + k] = t[7 - k];
        }
#ifndef QT_NO_DEBUG
        //打印 64bit 二进制分组明文
        strPlain.clear();
        for (int j = 0; j < 64; ++j)
            strPlain += QString("%1,").arg(bText[j]);
        qDebug() << Q_FUNC_INFO << __LINE__ << "当前分组明文 64bit:" << i + 1 << strPlain;
#endif

        // 当前组 64bit 二进制明文 异或 输出二进制
        for (int j = 0; j < 64; ++j)
            bText[j] = bText[j] ^ bTextOut[i][j];
#ifndef QT_NO_DEBUG
        strPlain.clear();
        for (int j = 0; j < 64; ++j)
            strPlain += QString("%1,").arg(bTextOut[i][j]);
        qDebug() << Q_FUNC_INFO << __LINE__ << "当前分组输出二进制:" << i + 1 << strPlain;
        strPlain.clear();
        for (int j = 0; j < 64; ++j)
            strPlain += QString("%1,").arg(bText[j]);
        qDebug() << Q_FUNC_INFO << __LINE__ << "当前分组异或明文二进制:" << i + 1 << strPlain;
#endif

        // 对分组明文进行DES加密
        // 1.IP变换
        int bTextIp[64];
        for (int j = 0; j < 64; ++j)
            bTextIp[j] = bText[m_siTableIP[j] - 1];
        // 2.拆分L0、R0
        int bTextL0[32], bTextR0[32];
        for (int j = 0; j < 32; ++j)
        {
            bTextL0[j] = bTextIp[j];
            bTextR0[j] = bTextIp[j + 32];
        }
#ifndef QT_NO_DEBUG
        // L0、R0打印
        strPlain.clear();
        for (int j = 0; j < 32; ++j)
            strPlain += QString("%1,").arg(bTextL0[j]);
        qDebug() << Q_FUNC_INFO << __LINE__ << "L0:" << strPlain;
        strPlain.clear();
        for (int j = 0; j < 32; ++j)
            strPlain += QString("%1,").arg(bTextR0[j]);
        qDebug() << Q_FUNC_INFO << __LINE__ << "R0:" << strPlain;
#endif

        // 16 次迭代
        for (int j = 0; j < 16; ++j)
        {
            // R0 扩展到 48位
            int bR0Extension[48];
            for (int k = 0; k < 48; ++k)
                bR0Extension[k] = bTextR0[m_siTableExtension[k] - 1];
            // 48位R0与密钥异或
            int bR0ExKey[48];
            for (int k = 0; k < 48; ++k)
                bR0ExKey[k] = bR0Extension[k] ^ m_iSubkey[j][k];
            // S盒移表处理
            int bR0KeySbox[8];
            for (int k = 0; k < 8; ++k)
            {
                int m = bR0ExKey[k * 6 + 0] * 2 + bR0ExKey[k * 6 + 5];
                int n = bR0ExKey[k * 6 + 1] * 8
                        + bR0ExKey[k * 6 + 2] * 4
                        + bR0ExKey[k * 6 + 3] * 2
                        + bR0ExKey[k * 6 + 4];
                bR0KeySbox[k] = m_siTableSBox[k][m][n];
            }
            // S盒移表数据转换成 32bit 二进制
            int bSboxValue[32];
            for (int k = 0; k < 8; ++k)
            {
                int t[4] = {0, 0, 0, 0};
                int n = bR0KeySbox[k];
                for (int l = 3; n != 0; --l)
                {
                    t[l] = n % 2;
                    n = n / 2;
                }
                for (int l = 0; l < 4; ++l)
                    bSboxValue[k * 4 + l] = t[l];
            }
            // 二进制S盒数据进行P盒转换
            int bPboxValue[32];
            for (int k = 0; k < 32; ++k)
                bPboxValue[k] = bSboxValue[m_siTablePBox[k] - 1];
            // 获得迭代右部数据:P盒数据与左部数据异或
            int bR0[32];
            for (int k = 0; k < 32; ++k)
                bR0[k] = bTextL0[k] ^ bPboxValue[k];
            // 重新写入左右部数据
            for (int k = 0; k < 32; ++k)
            {
                bTextL0[k] = bTextR0[k];
                bTextR0[k] = bR0[k];
            }
        }
        // 交换L0、R0
        for (int j = 0; j < 32; ++j)
        {
            int n = bTextL0[j];
            bTextL0[j] = bTextR0[j];
            bTextR0[j] = n;
        }
#ifndef QT_NO_DEBUG
        // 16次迭代后进行交换的L0、R0
        strPlain.clear();
        for (int j = 0; j < 32; ++j)
            strPlain += QString("%1,").arg(bTextL0[j]);
        qDebug() << Q_FUNC_INFO << __LINE__ << "16次后交换，L0:" << strPlain;
        strPlain.clear();
        for (int j = 0; j < 32; ++j)
            strPlain += QString("%1,").arg(bTextR0[j]);
        qDebug() << Q_FUNC_INFO << __LINE__ << "16次后交换，R0:" << strPlain;
#endif

        // 组合L0、R0
        int bTextLR[64];
        for (int j = 0; j < 32; ++j)
        {
            bTextLR[j] = bTextL0[j];
            bTextLR[j + 32] = bTextR0[j];
        }
        // 进行初始换位的逆过程
        for (int j = 0; j < 64; ++j)
            bTextOut[i + 1][m_siTableIP[j] - 1] = bTextLR[j];
        // 结果取得
        for (int j = 0; j < 64; ++j)
            bResult[i][j] = bTextOut[i + 1][j];
    }

    // 二进制密文转换成十进制密文
    QList<int> iDecList;
    for (int i = 0; i < iGroup; ++i)
    {
        for (int j = 0; j < 16; ++j)
        {
            int n = bResult[i][j * 4 + 0] * 8
                    + bResult[i][j * 4 + 1] * 4
                    + bResult[i][j * 4 + 2] * 2
                    + bResult[i][j * 4 + 3];
            iDecList << n;
        }
    }
    return iDecList;
}
