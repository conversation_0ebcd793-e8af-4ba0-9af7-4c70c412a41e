﻿#ifndef _HL7_COMPONENT_H_
#define _HL7_COMPONENT_H_
#include "../StringUtil.h"
#include "../interface/base.h"
#include "../interface/IObjectBase.h"
using namespace Utility;
class HL7Component
{
public:
	HL7Component();
	~HL7Component();

	/*
	*@brief 返回成分、子成分集合
	*/
	std::vector<std::string> GetHL7SubComponentCollection();

	/*
	*@brief 返回成分字符串
	*/
	std::string GetComponentString();

	/*
	*@brief 构建成分，输入参数为按顺序排列的子成分集合
	*/
	void Build(std::vector<std::string> subComponentCollection);

	/*
	*@brief 如有有子成分则分解为子成分，如果没有则无需分解
	*/
	void Parse(const char* componentStr, EncodingCharacters encodingChars);


	void SetComponentStr(std::string componentStr);

private:
	std::vector<std::string> m_subComponentCollection;

	std::string m_componentStr;

	std::string m_subComponentSeperator;
};

#endif
