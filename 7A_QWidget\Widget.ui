<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Widget</class>
 <widget class="QWidget" name="Widget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>759</width>
    <height>425</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget#KeyBoard{   background-color:#ECF5FC;}QStackedWidget{   /*background-color: red;*/}QLineEdit{   background-color: #fff;   color:#353E4E;   font-size:30px;   font-family:&quot;Source Han Sans CN&quot;;   border-radius:3px;}QTextEdit{   background-color:#fff;   color:#353E4E;   font-size:30px;   font-family:\&quot;Source Han Sans CN\&quot;;   border-radius:3px;}QLabel{   background-color:#fff;   color:#353E4E;   font-family:\&quot;Source Han Sans CN\&quot;;   font-size:30px;}QListView{   background-color:#fff;   color:#353E4E;   font-family:\&quot;Source Han Sans CN\&quot;;   font-size:25px;   border-radius:3px;}QToolButton{   background-color:#fff;   color:#353E4E;   font-family:\&quot;Source Han Sans CN\&quot;;   font-size:30px;   border-radius:3px;}QToolButton:pressed{   background-color:#fff;   color:#868482;   font-size:30px;   border-radius:3px;}QToolButton:hover{}QToolButton#capsLockBtn{   image: url(:/images/icon_small_normal.png);}QToolButton#capsLockBtn:checked{   image: url(:/images/icon_big.png);}QToolButton#toolButton_Return{   /*image: url(:/images/back1.png);*/}QToolButton#toolButton_Return:pressed{   /*image: url(:/images/back2.png);*/}QToolButton#toolButton_Hide{   /*image: url(:/svg/esc.svg);*/}QToolButton#toolButton_Hide:pressed{   /*image: url(:/svg/esc.svg);*/}QToolButton#toolButton_backspace{   /*image: url(:/svg/backspace.svg);*/}QToolButton#toolButton_backspace:pressed{   /*image: url(:/svg/backspace.svg);*/}QToolButton#toolButton_hide{   /*image: url(:/svg/esc.svg);*/}QToolButton#toolButton_hide:pressed{   /*image: url(:/svg/esc.svg);*/}QToolButton#toolButton_PageUp{   /*image: url(:/images/ch_up1.png);*/}QToolButton#toolButton_PageUp:pressed{   /*image: url(:/image/keypad/ch_up2.png);*/}QToolButton#toolButton_PageDown{   /*image: url(:/images/ch_down1.png);*/}QToolButton#toolButton_PageDown:pressed{   /*image: url(:/images/ch_down2.png);*/}QToolButton#toolButton_prevPage{   image: url(:/images/br_left.png);}QToolButton#toolButton_prevPage:disabled{   image: none;}QToolButton#toolButton_nextPage{   image: url(:/images/br_right.png);}QToolButton#toolButton_nextPage:disabled{   image: none;}</string>
  </property>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>110</x>
     <y>40</y>
     <width>551</width>
     <height>61</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel
{
   color: #FFF;   
   font-size: 26px;
   font-weight: bold;
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
      stop: 0 #6670c7, stop: 1 #3361b8);
}</string>
   </property>
   <property name="text">
    <string>---------------＞</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit">
   <property name="geometry">
    <rect>
     <x>120</x>
     <y>190</y>
     <width>201</width>
     <height>51</height>
    </rect>
   </property>
   <property name="text">
    <string>ABC123汉字</string>
   </property>
  </widget>
  <widget class="QGroupBox" name="groupBox">
   <property name="geometry">
    <rect>
     <x>430</x>
     <y>140</y>
     <width>201</width>
     <height>141</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <property name="title">
    <string>GroupBox</string>
   </property>
   <property name="checkable">
    <bool>true</bool>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
