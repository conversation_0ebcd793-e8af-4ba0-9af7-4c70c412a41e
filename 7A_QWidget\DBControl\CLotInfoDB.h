#ifndef CLOTINFODB_H
#define CLOTINFODB_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-01
  * Description: 项目库
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QObject>
#include "PublicParams.h"
#include "CSqliteDBBase.h"

class CLotInfoDB : public QObject, public CSqliteDBBase
{
    Q_OBJECT
public:
    static CLotInfoDB *GetInstance();
    virtual ~CLotInfoDB();

    QStringList GetAllProjectShowName();
    bool GetLotInfoByShowName(const QString &strProjectShowName, SLotInfoStruct &sLotInfo);
    bool SetLotInfoByShowName(const QString &strProjectShowName, SLotInfoStruct &sLotInfo);
    QString GetProjectNameByCode(const QString &strProjectCode);
    QList<QStringList> GetAllCodeNameTarget();
    QString GetCurveNameByProjectName(const QString &strProjectName);
    bool IsProjectNameExist(QString strProjectName);

    QMap<QString, QStringList> GetProjectSampleTypeMap();

private:
    CLotInfoDB();

private:
    static CLotInfoDB *m_spInstance;

    Q_DISABLE_COPY(CLotInfoDB)

};

#endif // CLOTINFODB_H
