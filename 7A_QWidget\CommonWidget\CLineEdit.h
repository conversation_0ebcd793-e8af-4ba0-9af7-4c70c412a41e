#ifndef CLINEEDIT_H
#define CLINEEDIT_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-10-27
  * Description: 子类化QLineEdit,主要为了弹出键盘
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLineEdit>

class CLineEdit : public QLineEdit
{
    Q_OBJECT
public:
    explicit CLineEdit(QWidget *parent = nullptr);
    explicit CLineEdit(const QString &strText, QWidget *parent = nullptr);
    ~CLineEdit();

signals:
    void SignalPressEvent();

protected:
    void mousePressEvent(QMouseEvent *pEvent) override;

};

#endif // CLINEEDIT_H
