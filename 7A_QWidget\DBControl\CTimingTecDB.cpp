#include "CTimingTecDB.h"
#include <QDebug>
#include "PublicConfig.h"

CTimingTecDB &CTimingTecDB::GetInstance()
{
    static CTimingTecDB timingDB;
    return timingDB;
}

bool CTimingTecDB::AddOneTec(const QString &strTecName, const QString &strTecContent)
{
    QString strCmd = QString("select * from tec_table where tec_name = '%1'").arg(strTecName);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    if(strList.isEmpty())
    {
        strCmd = QString("insert into tec_table (tec_name,tec_content) values ('%1','%2')")
                .arg(strTecName).arg(strTecContent);
        return _ExecuteDB(strCmd);
    }

    strCmd = QString("update tec_table set tec_content = '%1' where tec_name = '%2'")
            .arg(strTecContent).arg(strTecName);
    return _ExecuteDB(strCmd);
}

bool CTimingTecDB::DeleteOneTec(const QString &strTecName)
{
    QString strCmd = QString("delete from tec_table where tec_name = '%1'").arg(strTecName);
    return _ExecuteDB(strCmd);
}

QStringList CTimingTecDB::GetTecNameList()
{
    QString strCmd = "select tec_name from tec_table";
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return _GetColumnValueList(strList);
}

QString CTimingTecDB::GetTecContent(const QString &strTecName)
{
    QString strCmd = QString("select tec_content from tec_table where tec_name = '%1'").arg(strTecName);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return _GetFirstValue(strList);
}

bool CTimingTecDB::SaveTecList(const QList<QStringList> &strTecList)
{
    if(strTecList.isEmpty())
        return false;

    QStringList strCmdList;
    for(int i=0; i<strTecList.size(); i++)
    {
        QStringList strOneList = strTecList.at(i);
        if(strOneList.size() < 3)
            continue;

        QString strTecName = strOneList.at(1);
        QString strTecContent = strOneList.at(2);

        strCmdList << QString("insert into tec_table (tec_name,tec_content) values ('%1','%2')")
                      .arg(strTecName).arg(strTecContent);
    }

    return _ExecuteDB(strCmdList);
}

bool CTimingTecDB::AddOneTiming(const QString &strTimingName, const QString &strTecName, const QString &strTimingContent)
{
    if(strTimingName.isEmpty())
        return false;

    QString strCmd = QString("select * from timing_table where timing_name = '%1'").arg(strTimingName);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd,strList))
        return false;

    if(strList.isEmpty())
    {
        strCmd = QString("insert into timing_table (timing_name,tec_name,timing_content) values ('%1','%2','%3')")
                .arg(strTimingName).arg(strTecName).arg(strTimingContent);
        return _ExecuteDB(strCmd);
    }

    strCmd = QString("update timing_table set tec_name = '%1',timing_content = '%2' where timing_name = '%3'")
            .arg(strTecName).arg(strTimingContent).arg(strTimingName);
    return _ExecuteDB(strCmd);
}

bool CTimingTecDB::DeleteOneTiming(const QString &strTimingName)
{
    QString strCmd = QString("delete from timing_table where timing_name = '%1'").arg(strTimingName);
    return _ExecuteDB(strCmd);
}

QStringList CTimingTecDB::GetTimingNameList()
{
    QString strCmd = "select timing_name from timing_table";
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return _GetColumnValueList(strList);
}

QString CTimingTecDB::GetTimingContent(const QString &strTimingName)
{
    QString strCmd = QString("select timing_content from timing_table where timing_name = '%1'").arg(strTimingName);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return _GetFirstValue(strList);
}

bool CTimingTecDB::SaveTimingList(const QList<QStringList> &strTimingList)
{
    if(strTimingList.isEmpty())
        return false;

    QStringList strCmdList;
    for(int i=0; i<strTimingList.size(); i++)
    {
        QStringList strOneList = strTimingList.at(i);
        if(strOneList.size() < 3)
            continue;

        QString strTimingName = strOneList.at(1);
        QString strTecName = "";
        QString strTimingContent = strOneList.at(2);

        strCmdList << QString("insert into timing_table (timing_name,tec_name,timing_content) values ('%1','%2','%3')")
                      .arg(strTimingName).arg(strTecName).arg(strTimingContent);
    }

    return _ExecuteDB(strCmdList);
}

bool CTimingTecDB::IsTimingExist(const QString &strTimingName)
{
    if(strTimingName.isEmpty())
        return false;

    QString strCmd = QString("select * from timing_table where timing_name = '%1'").arg(strTimingName);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);

    if(strList.isEmpty())
        return false;
    else
        return true;
}

QList<QStringList> CTimingTecDB::GetTimingTecNameList()
{
    QString strCmd = "select timing_name, tec_name from timing_table";
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return strList;
}

QStringList CTimingTecDB::GetProjectRunTimingTec(const QString &strProjectName)
{
    QString strCmd = QString("select timing_name, tec_name from item_table where item_name = '%1'").arg(strProjectName);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return _GetRowValueList(strList);
}

CTimingTecDB::CTimingTecDB()
    : CSqliteDBBase(CPublicConfig::GetInstance()->GetTimingDBPath(), gk_strTimingDBConnect)
{
    _InitTecTable();
    _InitTimingTable();
    _InitItemTable();
}

CTimingTecDB::~CTimingTecDB()
{

}

void CTimingTecDB::_InitTecTable()
{
    QString strCreateTable = "create table if not exists tec_table ("
                             "id integer not null primary key autoincrement,"
                             "tec_name varchar,"
                             "tec_content varchar)";
    _ExecuteDB(strCreateTable);
}

void CTimingTecDB::_InitTimingTable()
{
    QString strCreateTable = "create table if not exists timing_table ("
                             "id integer not null primary key autoincrement,"
                             "timing_name varchar,"
                             "tec_name varchar,"
                             "timing_content varchar)";
    _ExecuteDB(strCreateTable);
}

void CTimingTecDB::_InitItemTable()
{
    QString strCreateTable = "create table if not exists item_table ("
                             "id integer not null primary key autoincrement,"
                             "item_name varchar,"
                             "timing_name varchar,"
                             "tec_name varchar)";
    _ExecuteDB(strCreateTable);
}
