#include "bulgariakey.h"

#include "common/keyboardtoolbutton/keyboardtoolbutton.h"

BulgariaKey::<PERSON><PERSON><PERSON>(const QString& name) :
    LanguageBase<PERSON>ey(name)
{
    InitButtons();
}

void BulgariaKey::InitButtons()
{
    QMap<int, QList<KeyBoardToolButton*> > map;
    QList<KeyBoardToolButton*> firstButtons, secondButtons, thirdButtons;

    firstButtons.append(new KeyBoardToolButton("у","У","",""));
    firstButtons.append(new KeyBoardToolButton("е","Е","ѐ",""));
    firstButtons.append(new KeyBoardToolButton("и","И","ѝ",""));
    firstButtons.append(new KeyBoardToolButton("ш","Ш","",""));
    firstButtons.append(new KeyBoardToolButton("щ","Щ","",""));
    firstButtons.append(new KeyBoardToolButton("к","К","",""));
    firstButtons.append(new KeyBoardToolButton("с","С","",""));
    firstButtons.append(new KeyBoardToolButton("д","Д","",""));
    firstButtons.append(new KeyBoardToolButton("з","З","",""));
    firstButtons.append(new KeyBoardToolButton("ц","Ц","",""));
    firstButtons.append(new KeyBoardToolButton("б","Б","",""));

    secondButtons.append(new KeyBoardToolButton("ь","Ь","",""));
    secondButtons.append(new KeyBoardToolButton("я","Я","",""));
    secondButtons.append(new KeyBoardToolButton("а","А","",""));
    secondButtons.append(new KeyBoardToolButton("о","О","",""));
    secondButtons.append(new KeyBoardToolButton("ж","Ж","",""));
    secondButtons.append(new KeyBoardToolButton("г","Г","",""));
    secondButtons.append(new KeyBoardToolButton("т","Т","",""));
    secondButtons.append(new KeyBoardToolButton("н","Н","",""));
    secondButtons.append(new KeyBoardToolButton("в","В","",""));
    secondButtons.append(new KeyBoardToolButton("м","М","",""));
    secondButtons.append(new KeyBoardToolButton("ч","Ч","",""));

    thirdButtons.append(GetLeftCapsLockBtn());
    thirdButtons.append(new KeyBoardToolButton("ю","Ю","",""));
    thirdButtons.append(new KeyBoardToolButton("й","Й","",""));
    thirdButtons.append(new KeyBoardToolButton("ъ","Ъ","",""));
    thirdButtons.append(new KeyBoardToolButton("э","Э","",""));
    thirdButtons.append(new KeyBoardToolButton("ф","Ф","",""));
    thirdButtons.append(new KeyBoardToolButton("х","Х","",""));
    thirdButtons.append(new KeyBoardToolButton("п","П","",""));
    thirdButtons.append(new KeyBoardToolButton("р","Р","",""));
    thirdButtons.append(new KeyBoardToolButton("л","Л","",""));
    thirdButtons.append(GetRightCapsLockBtn());

    map.insert(0,firstButtons);
    map.insert(1,secondButtons);
    map.insert(2,thirdButtons);

    SetButtonsMap(map);
    SetTranslate("Китайски","Английски","математика","пространство");
}

