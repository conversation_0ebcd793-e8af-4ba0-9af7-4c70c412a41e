#include "CSettingsWidget.h"
#include <QListView>
#include <QProcess>
#include <QFileDialog>
#include <QApplication>

#include "DBControl/CProjectDB.h"
#include "CRunTest.h"
#include "CMessageBox.h"
#include "PublicParams.h"
#include "PublicConfig.h"
#include "CConfigJson.h"
#include "COperationUnit.h"
#include "CScanCodeThread.h"
#include "CmdBus/CCmdManager.h"
#include "CReadWriteXlsxThread.h"
#include "DBControl/CRegisterDB.h"
#include "CCheckUserWidget.h"
#include "DBControl/CFtpDB.h"

bool G_RegisterMode = false;

CSettingsWidget::CSettingsWidget(QWidget *parent) : QWidget(parent) , m_strTipsText(tr("提示"))
{
    _InitWidget();
    _InitLayout();

    _ReadCfg();

    connect(this, &CSettingsWidget::SignalUploadEnd, this, &CSettingsWidget::_SlotUploadEnd);

    m_bFtpThreadRun = true;
    std::thread ftpthread(&CSettingsWidget::_Thread2UploadDBFtp, this);
    ftpthread.detach();
}

CSettingsWidget::~CSettingsWidget()
{
    m_bFtpThreadRun = false;
}

void CSettingsWidget::showEvent(QShowEvent *pEvent)
{
    connect(CScanCodeThread::GetInstance(), &CScanCodeThread::SignalScanData, this, &CSettingsWidget::SlotScanData);
    QWidget::showEvent(pEvent);
}

void CSettingsWidget::hideEvent(QHideEvent *pEvent)
{
    disconnect(CScanCodeThread::GetInstance(), &CScanCodeThread::SignalScanData, this, &CSettingsWidget::SlotScanData);

    QWidget::hideEvent(pEvent);
}

void CSettingsWidget::SlotScanData(QByteArray qScanByte)
{
    qDebug()<<Q_FUNC_INFO<<qScanByte.toHex().toUpper()<<qScanByte.data();
    m_pScanLineEdit->setText(QString::fromLocal8Bit(qScanByte));
}

void CSettingsWidget::_SlotSetStatusBtn()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    DeviceStatus eStatus = DeviceStatus(m_pStatusComboBx->currentIndex());
    RUN_LOG(QString("手动设置%1#模组状态:%2").arg(iMachineID + 1).arg(eStatus));
    emit CRunTest::GetInstance()->SignalUpdateItemStatus(iMachineID, eStatus);
}

void CSettingsWidget::_SlotTestDoneBtn()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    RUN_LOG(QString("手动设置%1#模拟测试完成").arg(iMachineID + 1));

    QList<bool> qlist;
    for (int i = 0;i < m_pCheckBGYRList.size(); i++) {
        qlist.push_back(m_pCheckBGYRList.at(i)->isChecked());
    }
    //bool bNegtive = m_pPNCheck->isChecked();
    CCmdManager::GetInstance()->ForTest(iMachineID,qlist);
}

void CSettingsWidget::_SlotCheckCardBox(bool bClicked)
{
    RUN_LOG(QString("测试前检验卡盒:%1").arg(bClicked));
    CPublicConfig::GetInstance()->SetCheckCardExistBeforeStartTest(bClicked);
}

void CSettingsWidget::_SlotDynamicUpValue(bool bClicked)
{
    RUN_LOG(QString("启用动态抬升阈值:%1").arg(bClicked));
    CPublicConfig::GetInstance()->SetDynamicUpValue(bClicked);
    CConfigJson::GetInstance()->SetConfigValue("CalcParam", "IsDynamicUpValue", bClicked);
}

void CSettingsWidget::_SlotCalcParam(bool bClicked)
{
    qDebug()<<"算法参数可输入:"<<bClicked;
    CPublicConfig::GetInstance()->SetCalcParam(bClicked);
}

void CSettingsWidget::_SlotOpenScanBtn()
{
    m_pScanLineEdit->clear();
    CScanCodeThread::GetInstance()->StartScan();
}

void CSettingsWidget::_SlotCloseScanBtn()
{
    CScanCodeThread::GetInstance()->StopScan();
}

void CSettingsWidget::_SlotFaultLogBtn()
{
    int iCode = m_pFaultCodeLineEdit->text().toInt();
    qDebug()<<"模拟故障码:"<<iCode;
    CPublicConfig::GetInstance()->SignalSaveFaultCode(iCode, m_pMachineComboBox->GetCurrentIndex());
}

void CSettingsWidget::_SlotWindPopBtn()
{
    int iTimes = m_pWindPopLineEdit->text().toInt();
    for(int i=0; i<iTimes; i++)
        ShowInformation(this, QString::number(i), QString::number(i));
}

void CSettingsWidget::_SlotBuzzerCheckBox()
{
    if(m_pBuzzerCheckBox->isChecked())
        System("/usr/local/test/testBeep 1");
    else
        System("/usr/local/test/testBeep 0");
}

void CSettingsWidget::_SlotBiwavBtn()
{
    System("aplay fireware/audio/bi.wav");
}

void CSettingsWidget::_SlotRegisterCheckBox(bool bClicked)
{
    Q_UNUSED(bClicked);
    G_RegisterMode = m_pRegisterCheckBox->isChecked();
    RUN_LOG(QString("注册模式:%1").arg(G_RegisterMode));

    CConfigJson::GetInstance()->SetConfigValue("bRegister", G_RegisterMode);
}

void CSettingsWidget::_SlotImportRegisterBtn()
{
    if(!UDiskExist(this))
        return;

    QString strCurrentDir = QApplication::applicationDirPath() + "/";
    QString strFilePath = QFileDialog::getOpenFileName(this, tr("导入注册xlsx"), strCurrentDir, "*.xlsx");
    if(strFilePath.isEmpty() || !QFile::exists(strFilePath))
        return;

    qDebug()<<strFilePath;

    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = strFilePath;
    pXlsxStruct->eOpXlsxType = eReadXlsx;

    FunReadXlsxEndCallBack lambdaFunction = [this](QString strXlsxName, const ReadXlsxDataMap &varReadDataMap)
    {
        QMap<int, QStringList> iReadCodeMap;
        for(auto it=varReadDataMap.constBegin(); it!=varReadDataMap.constEnd(); it++)
        {
            QString strTableName = it.key();
            int iName = strTableName.toInt();
            if(iName < 1 || iName > 60)
                continue;

            QList<QVariantList> oneTabelList = it.value(); //xlsx里的一张表
            qDebug()<<strTableName<<oneTabelList.size();

            QString strCardID, strSampleID;
            QStringList strUpLiftList, strCTList, strThresholdList, strRawList;
            for(int i=0; i<oneTabelList.size(); i++)
            {
                QVariantList oneRowList = oneTabelList.at(i); //表里的一行
                if(oneRowList.size() < 2)
                    continue;

                if(3 == i)
                {
                    strCardID = oneRowList.at(1).toString();
                }
                if(4 == i)
                {
                    strSampleID = oneRowList.at(1).toString();
                }
                if(12 == i)
                {
                    for(int j=1; j<9; j++)
                    {
                        double dCT = oneRowList.at(j).toDouble();
                        //if(4 != j /*&& 5 != j && 1 != j*/)
                        //    dCT = 0;
                        strCTList << QString::number(dCT, 'f', 2);
                    }
                }
                if(11 == i)
                {
                    for(int j=1; j<9; j++)
                    {
                        double dUplift = oneRowList.at(j).toDouble();
                        strUpLiftList << QString::number(dUplift, 'f', 2);
                    }
                }
                if(14 == i)
                {
                    for(int j=1; j<9; j++)
                    {
                        double dThreshold = oneRowList.at(j).toDouble();
                        strThresholdList << QString::number(dThreshold, 'f', 2);
                    }
                }

                if(i >= 20 && i<= 59)
                {
                    QStringList strOneCycle;
                    for(int j=1; j<9; j++)
                    {
                        double dFL = oneRowList.at(j).toDouble();
                        strOneCycle << QString::number(dFL, 'f', 2);
                    }
                    strRawList << strOneCycle.join(",");
                    qDebug()<<"循环数:"<<i-20<<strOneCycle;
                }
            }
            qDebug()<<"表格:"<<strTableName<<"卡盒:"<<strCardID<<"样本:"<<strSampleID<<"CT:"<<strCTList
                   <<"抬升值:"<<strUpLiftList<<"阈值:"<<strThresholdList;
            CRegisterDB::GetInstance()->AddData(strCardID, strSampleID, strCTList.join(";"),
                                                strUpLiftList.join(";"), strThresholdList.join(";"),
                                                strRawList.join(";"));
        }
        ShowSuccess(this, tr("提示"), tr("注册数据导入成功"));

        int iCnt = CRegisterDB::GetInstance()->GetCount();
        m_pRegisterLabel->setText(QString::number(iCnt));
        qDebug()<<"注册表读取结束:"<<strXlsxName<<iReadCodeMap.size()<<QThread::currentThreadId();
    };

    pXlsxStruct->ReadEndCallBack = lambdaFunction;
    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
}

void CSettingsWidget::_SlotCmdBtn()
{
    QString strCmd = m_pCmdLineEdit->text();
    qDebug()<<Q_FUNC_INFO<<strCmd;
    QProcess process;
    process.start(strCmd);
    process.waitForFinished();
    QString strData = process.readAllStandardOutput();
    process.close();
    qDebug()<<strCmd<<strData;
}

void CSettingsWidget::_SlotRunTimeBtn()
{
    QString strRunTime = m_pRunTimeLineEdit->text();
    if(strRunTime.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("输入范围在1到60之间"));
        return;
    }

    int iRunTime = strRunTime.toInt();
    if(iRunTime < 1 || iRunTime > 60)
    {
        ShowInformation(this, m_strTipsText, tr("输入范围在1到60之间"));
        return;
    }

    RUN_LOG(QString("设置测试倒计时:%1分钟").arg(iRunTime));
    CPublicConfig::GetInstance()->SetRunTimeMinute(iRunTime);
    CConfigJson::GetInstance()->SetConfigValue("iRunTime", iRunTime);
}

void CSettingsWidget::_SlotFtpOuterNetBtn()
{
    m_pFtpIPLineEdit->SetLineEditText("************");
    m_pFtpPortLineEdit->SetLineEditText("21");
    m_pFtpUserLineEdit->SetLineEditText("<EMAIL>");
    m_pFtpPwdLineEdit->SetLineEditText("Wondfo2023@");
}

void CSettingsWidget::_SlotFtpInnetNetBtn()
{
    m_pFtpIPLineEdit->SetLineEditText("*************");
    m_pFtpPortLineEdit->SetLineEditText("21");
    m_pFtpUserLineEdit->SetLineEditText("mdxtest");
    m_pFtpPwdLineEdit->SetLineEditText("mdxtest123");
}

void CSettingsWidget::_SlotFtpSaveBtn()
{
    QString strIP = m_pFtpIPLineEdit->GetLineEditText();
    if(!IsValidIPv4(strIP))
    {
        ShowInformation(this, m_strTipsText, tr("ftp信息，请输入正确的IP地址"));
        return;
    }

    QString strPort = m_pFtpPortLineEdit->GetLineEditText();
    int iPort = strPort.toInt();
    if(strPort.isEmpty() || iPort < 0 || iPort > 65535)
    {
        ShowInformation(this, m_strTipsText, tr("ftp信息，请输入正确的端口"));
        return;
    }

    QString strUser = m_pFtpUserLineEdit->GetLineEditText();
    if(strUser.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("ftp信息，请输入用户名"));
        return;
    }

    QString strPwd = m_pFtpPwdLineEdit->GetLineEditText();
    if(strPwd.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("ftp信息，请输入密码"));
        return;
    }

    m_qFtpMutex.lock();
    m_strFtpIP = strIP;
    m_iFtpPort = iPort;
    m_strFtpUser = strUser;
    m_strFtpPwd = strPwd;
    m_qFtpMutex.unlock();
    qDebug()<<"更改ftp信息:"<<m_strFtpIP<<m_iFtpPort<<m_strFtpUser<<m_strFtpPwd;

    QJsonObject qFtpObj = CConfigJson::GetInstance()->GetConfigJsonObject("ftp");
    qFtpObj.insert("ip", strIP);
    qFtpObj.insert("port", iPort);
    qFtpObj.insert("user", strUser);
    qFtpObj.insert("password", strPwd);
    CConfigJson::GetInstance()->SetConfigJsonObject("ftp", qFtpObj);
    ShowSuccess(this, m_strTipsText, tr("ftp信息保存成功"));
}

void CSettingsWidget::_SlotExportDB2USBBtn()
{
    if(!UDiskExist(this))
        return;

    QString strDate = QDate::currentDate().toString("yyyyMMdd");
    QString strSN = CPublicConfig::GetInstance()->GetMachineSN();
    strSN = DeleteSpecialCharacters(strSN);
    QString strExportDir = GetUDiskDir() + QString("7C_DB_%1_%2/").arg(strSN).arg(strDate);
    CreateDir(strExportDir);
    QString strCmd = QString("cp db/* %1").arg(strExportDir);
    qDebug()<<strCmd;
    System(strCmd);
    ExportEndUmountUSB();
    ShowSuccess(this, m_strTipsText, tr("数据库导出完成"));
}

void CSettingsWidget::_SlotImportHistoryDBBtn()
{
    if(!UDiskExist(this))
        return;

    QString strProjectDBPath = GetUDiskUpdateDir() + "project.db";
    QString strHistoryDBPath = GetUDiskUpdateDir() + "History.db";

    if(!QFile::exists(strProjectDBPath) || !QFile::exists(strHistoryDBPath))
    {
        ShowInformation(this, m_strTipsText, tr("U盘中缺少历史数据库文件") + " project.db History.db");
        return;
    }

    int iBtnType = ShowQuestion(this, m_strTipsText, tr("将会覆盖历史测试数据，是否继续"));
    if(QMessageBox::Yes != iBtnType)
        return;

    QString strDestProjectDBPath = QApplication::applicationDirPath() + "/db/project.db";
    if(!CopyQFile(strProjectDBPath, strDestProjectDBPath))
    {
        ShowInformation(this, m_strTipsText, tr("历史数据库导入失败，请检查"));
        return;
    }

    QString strDestHistoryDBPath = QApplication::applicationDirPath() + "/db/History.db";
    if(!CopyQFile(strHistoryDBPath, strDestHistoryDBPath))
    {
        ShowInformation(this, m_strTipsText, tr("历史数据库导入失败，请检查"));
        return;
    }

    ShowSuccess(this, m_strTipsText, tr("历史数据库导入成功，正在重启"));
    System("reboot");
}

void CSettingsWidget::_SlotExportDB2FTPBtn()
{
    bool bLog = m_pFtpLogCheckBox->isChecked();
    bool bDB = m_pFtpDBCheckBox->isChecked();
    bool bPdf = m_pFtpPdfCheckBox->isChecked();
    bool bXlsx = m_pFtpXlsxCheckBox->isChecked();
    if(!bLog && !bDB && !bPdf && !bXlsx)
    {
        ShowInformation(this, m_strTipsText, tr("请先勾选上传数据"));
        return;
    }

    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定要将选中的所有数据上传至ftp吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    _SetFtpWidgetEnable(false);
    RUN_LOG("正在上传数据到ftp");

    std::thread mythread(&CSettingsWidget::_Thread2UploadFtp, this, bLog, bDB, bPdf, bXlsx);
    mythread.detach();
}

void CSettingsWidget::_Thread2UploadFtp(bool bLog, bool bDB, bool bPdf, bool bXlsx)
{
    int iPort;
    QString strIP, strUser, strPwd;
    this->m_qFtpMutex.lock();
    strIP = this->m_strFtpIP;
    iPort = this->m_iFtpPort;
    strUser = this->m_strFtpUser;
    strPwd = this->m_strFtpPwd;
    this->m_qFtpMutex.unlock();

    QString strSN = CPublicConfig::GetInstance()->GetMachineSN();
    strSN = DeleteSpecialCharacters(strSN);
    QString strDate = QDate::currentDate().toString("yyyyMMdd");

    int iRet = -1;
    QString strLogin = QString("-u %1 -p %2").arg(strUser).arg(strPwd);
    if(bDB)
    {
        QString strCmd = QString("ncftpput -m %1 -R %2 upload/%3/ db/")
                .arg(strLogin).arg(strIP).arg(strSN);
        iRet = System(strCmd);
        if(0 != iRet)
        {
            emit SignalUploadEnd(iRet);
            return;
        }

        if(!bLog && !bPdf && !bXlsx)
        {
            emit SignalUploadEnd(iRet);
            return;
        }
    }

    QStringList strDirList;
    if(bLog)
        strDirList << "data/";
    if(bPdf)
        strDirList << "pdf/";
    if(bXlsx)
        strDirList << "xlsx/";

    for(const QString& dir : strDirList)
    {
        QString strCmd = QString("ncftpput -m %1 -R %2 upload/%3/%4/ %5")
                .arg(strLogin).arg(strIP).arg(strSN).arg(strDate).arg(dir);
        iRet = System(strCmd);
        if(0 != iRet)
        {
            emit SignalUploadEnd(iRet);
            return;
        }
    }

    emit SignalUploadEnd(iRet);
}

void CSettingsWidget::_SlotUploadEnd(int iRet)
{
    _SetFtpWidgetEnable(true);

    if(0 == iRet)
    {
        RUN_LOG("数据上传到ftp成功");
        ShowSuccess(this, m_strTipsText, tr("数据上传到ftp成功"));
    }
    else
    {
        RUN_LOG("数据上传到ftp失败");
        ShowWarning(this, m_strTipsText, tr("数据上传到ftp失败"));
    }
}

void CSettingsWidget::_SetFtpWidgetEnable(bool bEnable)
{
    m_pInstallNCFtpBtn->setEnabled(bEnable);
    m_pFtpLogCheckBox->setEnabled(bEnable);
    m_pFtpDBCheckBox->setEnabled(bEnable);
    m_pFtpPdfCheckBox->setEnabled(bEnable);
    m_pFtpXlsxCheckBox->setEnabled(bEnable);
    m_pExportDB2FTPBtn->setEnabled(bEnable);
}

void CSettingsWidget::_Thread2UploadDBFtp()
{    
    System("rm -rf coredump* &");
    System("rm -rf Wondfo-SFW-WonDx1000* &");

    while (this->m_bFtpThreadRun)
    {
        QThread::sleep(10);

        bool bWiFiOK = CPublicConfig::GetInstance()->GetWiFiConnect();
        bool bEthOK = CPublicConfig::GetInstance()->GetEthConnect();
        if(!bWiFiOK && !bEthOK)
            continue;

        QStringList strFileList;
        CFtpDB::GetInstance()->ReadFtpUploadFile(strFileList);
        if(strFileList.isEmpty())
            continue;

        if(strFileList.size() > 4)
            strFileList = strFileList.mid(0, 4);

        qDebug()<<Q_FUNC_INFO<<"upload file nums:"<<strFileList.size();
        QString strSN = CPublicConfig::GetInstance()->GetMachineSN();
        strSN = DeleteSpecialCharacters(strSN);
        QString strDate = QDate::currentDate().toString("yyyyMMdd");

        int iPort;
        QString strIP, strUser, strPwd;
        this->m_qFtpMutex.lock();
        strIP = this->m_strFtpIP;
        iPort = this->m_iFtpPort;
        strUser = this->m_strFtpUser;
        strPwd = this->m_strFtpPwd;
        this->m_qFtpMutex.unlock();

        //ncftpput -m -u mdxtest -p mdxtest123 -R ************* upload/SSH0001/20250101/pdf/ my.pdf
        QString strLogin = QString("-u %1 -p %2").arg(strUser).arg(strPwd);

        for(int i=0; i<strFileList.size(); i++)
        {
            QString strCmd = QString("ncftpput -m %1 -R %2 upload/%3/%4/")
                    .arg(strLogin).arg(strIP).arg(strSN).arg(strDate);

            QString strFileName = strFileList.at(i);
            if(!QFile::exists(strFileName))
            {
                CFtpDB::GetInstance()->DeleteFtpUploadFile(strFileName);
                continue;
            }

            if(strFileName.endsWith(".pdf"))
                strCmd += "pdf/ " + strFileName;
            if(strFileName.endsWith(".xlsx"))
                strCmd += "xlsx/ " + strFileName;

            // no print
            int iRet = -1;
#ifdef Q_OS_LINUX
            iRet = system(strCmd.toStdString().c_str());
#endif
            if(0 == iRet)
            {
                QString strLog = QString("%1已上传到ftp").arg(strFileName);
                RUN_LOG(strLog);
                CFtpDB::GetInstance()->DeleteFtpUploadFile(strFileName);
            }
        }
    }
}

void CSettingsWidget::_SlotInstallNCFtpBtn()
{
    QString strPath = "/usr/bin/ncftp";
    if(QFile::exists(strPath))
    {
        RUN_LOG("ncftp安装成功");
        ShowSuccess(this, m_strTipsText, tr("ncftp安装成功"));
    }
    else
    {
        RUN_LOG("ncftp安装失败");
        ShowError(this, m_strTipsText, tr("ncftp安装失败"));
    }
}

void CSettingsWidget::_SlotFtpAutoUploadCheckBox(bool bClicked)
{
    RUN_LOG(QString("测试结束pdf xlsx是否自动上传ftp:%1").arg(bClicked));
    CPublicConfig::GetInstance()->SetFtpAutoUpload(bClicked);

    QJsonObject qFtpObj = CConfigJson::GetInstance()->GetConfigJsonObject("ftp");
    qFtpObj.insert("upload", bClicked);
    CConfigJson::GetInstance()->SetConfigJsonObject("ftp", qFtpObj);
}

void CSettingsWidget::_ReadCfg()
{
    QJsonObject qRootObj = CConfigJson::GetInstance()->GetConfigJsonObject();

    G_RegisterMode = qRootObj.value("bRegister").toBool();
    m_pRegisterCheckBox->setChecked(G_RegisterMode);
    RUN_LOG(QString("注册模式:%1").arg(G_RegisterMode));

    QJsonObject qCalcParamObj = qRootObj.value("CalcParam").toObject();
    bool IsDynamicUpValue = qCalcParamObj.value("IsDynamicUpValue").toBool();
    m_pDynamicUpValue->setChecked(IsDynamicUpValue);
    RUN_LOG(QString("不启用动态抬升阈值:%1").arg(IsDynamicUpValue));
    CPublicConfig::GetInstance()->SetDynamicUpValue(IsDynamicUpValue);

    int iRunTime = qRootObj.value("iRunTime").toInt();
    if(iRunTime < 1 || iRunTime > 60)
        iRunTime = 16;
    m_pRunTimeLineEdit->setText(QString("%1").arg(iRunTime));
    CPublicConfig::GetInstance()->SetRunTimeMinute(iRunTime);
    //RUN_LOG(QString("设置测试倒计时:%1分钟").arg(iRunTime));

    QJsonObject qFtpObj = qRootObj.value("ftp").toObject();
    bool bFtpUpload = false;
    if(qFtpObj.isEmpty())
    {
        qDebug()<<"使用默认外网ftp信息";
        m_strFtpIP = "************";
        m_iFtpPort = 21;
        m_strFtpUser = "<EMAIL>";
        m_strFtpPwd = "Wondfo2023@";
        bFtpUpload = false;
    }
    else
    {
        m_strFtpIP = qFtpObj.value("ip").toString();
        m_iFtpPort = qFtpObj.value("port").toInt();
        m_strFtpUser = qFtpObj.value("user").toString();
        m_strFtpPwd = qFtpObj.value("password").toString();
        bFtpUpload = qFtpObj.value("upload").toBool();
    }

    m_pFtpIPLineEdit->SetLineEditText(m_strFtpIP);
    m_pFtpPortLineEdit->SetLineEditText(QString::number(m_iFtpPort));
    m_pFtpUserLineEdit->SetLineEditText(m_strFtpUser);
    m_pFtpPwdLineEdit->SetLineEditText(m_strFtpPwd);
    RUN_LOG(QString("测试结束pdf xlsx是否自动上传ftp:%1").arg(bFtpUpload));
    m_pFtpAutoUploadCheckBox->setChecked(bFtpUpload);
    if("************" == m_strFtpIP)
    {
        m_pFtpOuterNetBtn->setChecked(true);
        m_pFtpInnerNetBtn->setChecked(false);
    }
    if("*************" == m_strFtpIP)
    {
        m_pFtpOuterNetBtn->setChecked(false);
        m_pFtpInnerNetBtn->setChecked(true);
    }

    CPublicConfig::GetInstance()->SetFtpAutoUpload(bFtpUpload);
}

void CSettingsWidget::_InitWidget()
{
    QStringList strMachineNameList = CPublicConfig::GetInstance()->GetMachineNameList();
    m_pMachineComboBox = new CLabelComboBox(tr("机器："), strMachineNameList);
    m_pMachineComboBox->ResetComboBoxSize(100, 50);

    QStringList strStatusList = {tr("断线中"), tr("自检中"), tr("空闲中"), tr("测试中"), tr("测试完成"),
                                 tr("测试停止"), tr("停止中"), tr("测试失败"), tr("故障中"), tr("处理中"), tr("复位中")};
    m_pStatusComboBx = new QComboBox;
    m_pStatusComboBx->setView(new QListView);
    m_pStatusComboBx->addItems(strStatusList);
    m_pStatusComboBx->setFixedSize(150, 50);

    m_pSetStatusBtn = new QPushButton(tr("设置状态"));
    m_pSetStatusBtn->setFixedSize(130, 50);
    connect(m_pSetStatusBtn, &QPushButton::clicked, this, &CSettingsWidget::_SlotSetStatusBtn);

    m_pTestDoneBtn = new QPushButton(tr("测试完成"));
    m_pTestDoneBtn->setFixedSize(130, 50);
    connect(m_pTestDoneBtn, &QPushButton::clicked, this, &CSettingsWidget::_SlotTestDoneBtn);

    m_pCheckCardBox = new QCheckBox(tr("测试前检验卡盒"));
    m_pCheckCardBox->setChecked(true);
    connect(m_pCheckCardBox, &QCheckBox::clicked, this, &CSettingsWidget::_SlotCheckCardBox);

    m_pPNCheck = new QCheckBox(tr("模拟未抬升数据"));
    m_pPNCheck->setChecked(false);

    m_pDynamicUpValue = new QCheckBox(tr("不使用动态阈值"));
    //m_pDynamicUpValue->setChecked(true);
    connect(m_pDynamicUpValue, &QCheckBox::clicked, this, &CSettingsWidget::_SlotDynamicUpValue);

    m_pCalcParam = new QCheckBox(tr("calcParam"));
    m_pCalcParam->setChecked(false);
    connect(m_pCalcParam, &QCheckBox::clicked, this, &CSettingsWidget::_SlotCalcParam);

    for (int i = 0 ;i < gk_iHoleCount*gk_iBGYRCount; i++) {
     QCheckBox* pCheckTemp = new QCheckBox(QString("%1%2").arg(gk_strColorNameList.at(i%4)).arg(i/4));
     pCheckTemp->setChecked(false);
     m_pCheckBGYRList.push_back(pCheckTemp);
    }

    m_pOpenScanBtn = new QPushButton(tr("打开扫码"));
    m_pOpenScanBtn->setFixedSize(150, 50);
    connect(m_pOpenScanBtn, &QPushButton::clicked, this, &CSettingsWidget::_SlotOpenScanBtn);

    m_pCloseScanBtn = new QPushButton(tr("关闭扫码"));
    m_pCloseScanBtn->setFixedSize(150, 50);
    connect(m_pCloseScanBtn, &QPushButton::clicked, this, &CSettingsWidget::_SlotCloseScanBtn);

    m_pScanLineEdit = new CLineEdit;
    m_pScanLineEdit->setFixedSize(500, 50);

    m_pFaultCodeLineEdit = new CLineEdit;
    m_pFaultCodeLineEdit->setFixedSize(150, 50);
    m_pFaultCodeLineEdit->setPlaceholderText(tr("输入"));

    m_pFaultLogBtn = new QPushButton(tr("模拟故障码"));
    m_pFaultLogBtn->setFixedSize(150, 50);
    connect(m_pFaultLogBtn, &QPushButton::clicked, this, &CSettingsWidget::_SlotFaultLogBtn);

    m_pWindPopLineEdit = new CLineEdit;
    m_pWindPopLineEdit->setFixedSize(150, 50);
    m_pWindPopLineEdit->setPlaceholderText(tr("输入"));

    m_pWindPopBtn = new QPushButton(tr("模拟弹窗"));
    m_pWindPopBtn->setFixedSize(150, 50);
    connect(m_pWindPopBtn, &QPushButton::clicked, this, &CSettingsWidget::_SlotWindPopBtn);

    m_pBuzzerCheckBox = new QCheckBox(tr("蜂鸣器"));
    m_pBuzzerCheckBox->setChecked(false);
    m_pBuzzerCheckBox->setFixedSize(150, 50);
    connect(m_pBuzzerCheckBox, &QCheckBox::clicked, this, &CSettingsWidget::_SlotBuzzerCheckBox);

    m_pBiwavBtn = new QPushButton(tr("喇叭: 嘀"));
    m_pBiwavBtn->setFixedSize(150, 50);
    connect(m_pBiwavBtn, &QPushButton::clicked, this, &CSettingsWidget::_SlotBiwavBtn);

    m_pRegisterCheckBox = new QCheckBox(tr("注册模式"));
    m_pRegisterCheckBox->setFixedSize(150, 50);
    connect(m_pRegisterCheckBox, &QCheckBox::clicked, this, &CSettingsWidget::_SlotRegisterCheckBox);

    m_pImportRegisterBtn = new QPushButton(tr("导入注册xlsx"));
    m_pImportRegisterBtn->setFixedSize(150, 50);
    connect(m_pImportRegisterBtn, &QPushButton::clicked, this, &CSettingsWidget::_SlotImportRegisterBtn);

    int iCnt = CRegisterDB::GetInstance()->GetCount();
    m_pRegisterLabel = new QLabel(QString::number(iCnt));
    m_pRegisterLabel->setFixedSize(50, 50);

    m_pExportDB2USBBtn = new QPushButton(tr("导出数据到U盘"));
    m_pExportDB2USBBtn->setFixedSize(180, 50);
    connect(m_pExportDB2USBBtn, &QPushButton::clicked, this, &CSettingsWidget::_SlotExportDB2USBBtn);

    m_pImportHistoryDBBtn = new QPushButton(tr("导入历史数据"));
    m_pImportHistoryDBBtn->setFixedSize(160, 50);
    connect(m_pImportHistoryDBBtn, &QPushButton::clicked, this, &CSettingsWidget::_SlotImportHistoryDBBtn);

    m_pExportDB2FTPBtn = new QPushButton(tr("导出数据到FTP"));
    m_pExportDB2FTPBtn->setFixedSize(180, 50);
    connect(m_pExportDB2FTPBtn, &QPushButton::clicked, this, &CSettingsWidget::_SlotExportDB2FTPBtn);

    m_pInstallNCFtpBtn = new QPushButton(tr("安装ftp"));
    m_pInstallNCFtpBtn->setFixedSize(150, 50);
    connect(m_pInstallNCFtpBtn, &QPushButton::clicked, this, &CSettingsWidget::_SlotInstallNCFtpBtn);

    m_pFtpLogCheckBox = new QCheckBox("Log");
    m_pFtpLogCheckBox->setLayoutDirection(Qt::RightToLeft);
    m_pFtpLogCheckBox->setChecked(false);

    m_pFtpDBCheckBox = new QCheckBox("DB");
    m_pFtpDBCheckBox->setLayoutDirection(Qt::RightToLeft);
    m_pFtpDBCheckBox->setChecked(true);

    m_pFtpPdfCheckBox = new QCheckBox("Pdf");
    m_pFtpPdfCheckBox->setLayoutDirection(Qt::RightToLeft);
    m_pFtpPdfCheckBox->setChecked(true);

    m_pFtpXlsxCheckBox = new QCheckBox("Xlsx");
    m_pFtpXlsxCheckBox->setLayoutDirection(Qt::RightToLeft);
    m_pFtpXlsxCheckBox->setChecked(true);

    m_pFtpAutoUploadCheckBox = new QCheckBox(tr("自动上传ftp"));
    m_pFtpAutoUploadCheckBox->setLayoutDirection(Qt::RightToLeft);
    m_pFtpAutoUploadCheckBox->setChecked(true);
    connect(m_pFtpAutoUploadCheckBox, &QCheckBox::clicked, this, &CSettingsWidget::_SlotFtpAutoUploadCheckBox);

    m_pCmdLineEdit = new CLineEdit;
    m_pCmdLineEdit->setFixedSize(150, 50);
    m_pCmdLineEdit->setPlaceholderText("input cmd");

    m_pCmdBtn = new QPushButton("Exec CMD");
    m_pCmdBtn->setFixedSize(150, 50);
    connect(m_pCmdBtn, &QPushButton::clicked, this, &CSettingsWidget::_SlotCmdBtn);

    m_pRunTimeLineEdit = new CLineEdit(this);
    m_pRunTimeLineEdit->setFixedSize(150, 50);
    m_pRunTimeLineEdit->setVisible(false);

    m_pRunTimeBtn = new QPushButton(tr("倒计时/分钟"), this);
    m_pRunTimeBtn->setFixedSize(150, 50);
    connect(m_pRunTimeBtn, &QPushButton::clicked, this, &CSettingsWidget::_SlotRunTimeBtn);    
    m_pRunTimeBtn->setVisible(false);

    if(eLanguage_Spanish == gk_iLanguage)
    {
        m_pSetStatusBtn->setFixedSize(150, 50);
        m_pTestDoneBtn->setFixedSize(150, 50);
        m_pOpenScanBtn->setFixedSize(170, 50);
        m_pCloseScanBtn->setFixedSize(170, 50);
        m_pFaultLogBtn->setFixedSize(170, 50);
        m_pRunTimeBtn->setFixedSize(260, 50);
    }

    if(eLanguage_German == gk_iLanguage)
    {
        m_pTestDoneBtn->setFixedSize(175, 50);
        m_pOpenScanBtn->setFixedSize(165, 50);
        m_pCloseScanBtn->setFixedSize(165, 50);
        m_pInstallNCFtpBtn->setFixedSize(175, 50);
        m_pExportDB2USBBtn->setFixedSize(210, 50);
    }

    if(eLanguage_Italian == gk_iLanguage)
    {
        m_pFaultLogBtn->setFixedSize(160, 50);
        m_pExportDB2USBBtn->setFixedSize(250, 50);
        m_pExportDB2FTPBtn->setFixedSize(220, 50);
        m_pRunTimeBtn->setFixedSize(230, 50);
    }
}

QGroupBox *CSettingsWidget::_CreateFtpGroupBox()
{
    QLabel *pLabel = new QLabel(tr("ftp信息"));
    pLabel->setFixedHeight(40);

    m_pFtpIPLineEdit = new CHLabelLineEdit(tr("IP："), "*************");
    m_pFtpIPLineEdit->ResetLabelSize(80, 50);
    m_pFtpIPLineEdit->ResetLineEditSize(200, 50);
    m_pFtpIPLineEdit->setFixedHeight(50);

    m_pFtpPortLineEdit = new CHLabelLineEdit(tr("端口："), "21");
    m_pFtpPortLineEdit->ResetLabelSize(80, 50);
    m_pFtpPortLineEdit->ResetLineEditSize(200, 50);
    m_pFtpPortLineEdit->setFixedHeight(50);

    m_pFtpUserLineEdit = new CHLabelLineEdit(tr("用户名："), "mdxtest");
    m_pFtpUserLineEdit->ResetLabelSize(80, 50);
    m_pFtpUserLineEdit->ResetLineEditSize(200, 50);

    m_pFtpPwdLineEdit = new CHLabelLineEdit(tr("密码："), "mdxtest123");
    m_pFtpPwdLineEdit->ResetLabelSize(80, 50);
    m_pFtpPwdLineEdit->ResetLineEditSize(200, 50);

    m_pFtpOuterNetBtn = new QRadioButton(tr("外网"));
    connect(m_pFtpOuterNetBtn, &QRadioButton::clicked, this, &CSettingsWidget::_SlotFtpOuterNetBtn);

    m_pFtpInnerNetBtn = new QRadioButton(tr("内网"));
    connect(m_pFtpInnerNetBtn, &QRadioButton::clicked, this, &CSettingsWidget::_SlotFtpInnetNetBtn);

    m_pFtpSaveBtn = new QPushButton(tr("保存"));
    m_pFtpSaveBtn->setFixedSize(150, 50);
    connect(m_pFtpSaveBtn, &QPushButton::clicked, this, &CSettingsWidget::_SlotFtpSaveBtn);

    QHBoxLayout *pRadioLayout = new QHBoxLayout;
    pRadioLayout->setMargin(0);
    pRadioLayout->setSpacing(30);
    pRadioLayout->addStretch(1);
    pRadioLayout->addWidget(m_pFtpOuterNetBtn);
    pRadioLayout->addWidget(m_pFtpInnerNetBtn);
    pRadioLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setContentsMargins(12, 5, 12, 0);
    pLayout->setSpacing(10);
    pLayout->addWidget(pLabel, 0, Qt::AlignLeft);
    pLayout->addWidget(m_pFtpIPLineEdit, 0, Qt::AlignHCenter);
    //pLayout->addWidget(m_pFtpPortLineEdit, 0, Qt::AlignHCenter);
    pLayout->addWidget(m_pFtpUserLineEdit, 0, Qt::AlignHCenter);
    pLayout->addWidget(m_pFtpPwdLineEdit, 0, Qt::AlignHCenter);
    pLayout->addSpacing(10);
    pLayout->addLayout(pRadioLayout);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pFtpSaveBtn, 0, Qt::AlignHCenter);
    pLayout->addStretch(1);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(350, 380);
    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}

void CSettingsWidget::_InitLayout()
{
    QHBoxLayout *pSetStatusLayout = new QHBoxLayout;
    pSetStatusLayout->setMargin(0);
    pSetStatusLayout->setSpacing(20);
    pSetStatusLayout->addWidget(m_pMachineComboBox);
    pSetStatusLayout->addWidget(m_pStatusComboBx);
    pSetStatusLayout->addWidget(m_pSetStatusBtn);
    pSetStatusLayout->addWidget(m_pTestDoneBtn);
    pSetStatusLayout->addStretch(1);

    QHBoxLayout *pCheckBGYRLayout = new QHBoxLayout;
    pCheckBGYRLayout->setMargin(0);
    pCheckBGYRLayout->setSpacing(10);
    for (int i = 0; i < m_pCheckBGYRList.size(); i++)
    {
        pCheckBGYRLayout->addWidget(m_pCheckBGYRList.at(i));
    }
    pCheckBGYRLayout->addStretch(1);

    QHBoxLayout *pCheckLayout = new QHBoxLayout;
    pCheckLayout->setMargin(0);
    pCheckLayout->setSpacing(0);
    pCheckLayout->addWidget(m_pCheckCardBox);
    pCheckLayout->addSpacing(60);
    pCheckLayout->addWidget(m_pPNCheck);
    pCheckLayout->addSpacing(60);
    pCheckLayout->addWidget(m_pDynamicUpValue);
    pCheckLayout->addSpacing(60);
    pCheckLayout->addWidget(m_pCalcParam);
    pCheckLayout->addStretch(1);

    QHBoxLayout *pScanLayout = new QHBoxLayout;
    pScanLayout->setMargin(0);
    pScanLayout->setSpacing(20);
    pScanLayout->addWidget(m_pOpenScanBtn);
    pScanLayout->addWidget(m_pCloseScanBtn);
    pScanLayout->addWidget(m_pScanLineEdit);
    pScanLayout->addStretch(1);

    QHBoxLayout *pFaultLayout = new QHBoxLayout;
    pFaultLayout->setMargin(0);
    pFaultLayout->setSpacing(20);
    pFaultLayout->addWidget(m_pFaultCodeLineEdit);
    pFaultLayout->addWidget(m_pFaultLogBtn);
    pFaultLayout->addWidget(m_pWindPopLineEdit);
    pFaultLayout->addWidget(m_pWindPopBtn);
    pFaultLayout->addStretch(1);

    QHBoxLayout *pBiLayout = new QHBoxLayout;
    pBiLayout->setMargin(0);
    pBiLayout->setSpacing(20);
    pBiLayout->addWidget(m_pBuzzerCheckBox);
    pBiLayout->addWidget(m_pBiwavBtn);
    pBiLayout->addStretch(1);

    QHBoxLayout *pRegisterLayout = new QHBoxLayout;
    pRegisterLayout->setMargin(0);
    pRegisterLayout->setSpacing(20);
    pRegisterLayout->addWidget(m_pRegisterCheckBox);
    pRegisterLayout->addWidget(m_pImportRegisterBtn);
    pRegisterLayout->addWidget(m_pRegisterLabel);
    pRegisterLayout->addStretch(1);

    QVBoxLayout *pLeftLayout = new QVBoxLayout;
    pLeftLayout->setMargin(0);
    pLeftLayout->setSpacing(30);
    pLeftLayout->addLayout(pSetStatusLayout);
    pLeftLayout->addLayout(pCheckBGYRLayout);
    pLeftLayout->addLayout(pCheckLayout);
    pLeftLayout->addLayout(pScanLayout);
    pLeftLayout->addLayout(pFaultLayout);
    pLeftLayout->addLayout(pBiLayout);
    pLeftLayout->addLayout(pRegisterLayout);

    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(0);
    pTopLayout->addLayout(pLeftLayout);
    pTopLayout->addStretch(1);
    pTopLayout->addWidget(_CreateFtpGroupBox());
    pTopLayout->addSpacing(10);

    QHBoxLayout *pDBLayout = new QHBoxLayout;
    pDBLayout->setMargin(0);
    pDBLayout->setSpacing(10);
    pDBLayout->addWidget(m_pExportDB2USBBtn);
    pDBLayout->addSpacing(20);
    pDBLayout->addWidget(m_pImportHistoryDBBtn);
    pDBLayout->addStretch(1);
    pDBLayout->addWidget(m_pInstallNCFtpBtn);
    pDBLayout->addSpacing(20);
    pDBLayout->addWidget(m_pFtpLogCheckBox);
    pDBLayout->addWidget(m_pFtpDBCheckBox);
    pDBLayout->addWidget(m_pFtpPdfCheckBox);
    pDBLayout->addWidget(m_pFtpXlsxCheckBox);
    pDBLayout->addWidget(m_pExportDB2FTPBtn);
    pDBLayout->addSpacing(30);
    pDBLayout->addWidget(m_pFtpAutoUploadCheckBox);
    pDBLayout->addStretch(1);

    QHBoxLayout *pCmdLayout = new QHBoxLayout;
    pCmdLayout->setMargin(0);
    pCmdLayout->setSpacing(20);
    pCmdLayout->addWidget(m_pCmdLineEdit);
    pCmdLayout->addWidget(m_pCmdBtn);
    pCmdLayout->addStretch(1);

    QHBoxLayout *pRunLayout = new QHBoxLayout;
    pRunLayout->setMargin(0);
    pRunLayout->setSpacing(20);
    pRunLayout->addWidget(m_pRunTimeLineEdit);
    pRunLayout->addWidget(m_pRunTimeBtn);
    pRunLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(10);
    pLayout->setSpacing(30);
//    pLayout->addLayout(pSetStatusLayout);
//    pLayout->addLayout(pCheckLayout);
//    pLayout->addLayout(pScanLayout);
//    pLayout->addLayout(pFaultLayout);
//    pLayout->addLayout(pBiLayout);
//    pLayout->addLayout(pRegisterLayout);
    pLayout->addLayout(pTopLayout);
    pLayout->addLayout(pDBLayout);
    pLayout->addLayout(pCmdLayout);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}
