#include "PublicConfig.h"
#include <QDir>
#include <QApplication>
#include "CConfigJson.h"
#include "PublicFunction.h"
#include "CLotInfoDB.h"

CPublicConfig *CPublicConfig::m_spInstance = nullptr;

CPublicConfig::CPublicConfig()
{
    ReadFile(QApplication::applicationDirPath() + "/Resources/all.txt", m_strFullVersion);
    m_strFullVersion.remove('\r').remove('\n').remove('\t');

    m_bShowRawCurve = false;
    m_bLisTwoWay = false;
    m_bAutoReset = true;
    m_bFtpAutoUpload = true;
    m_bWiFiConnect = false;
    m_bEthConnect = false;
    m_iRunTimeMinute = 16;
    m_bDynamicPassword = false;
    m_bDynamicUpValue = false;
    m_bCalcParam = false;
    m_iLoginLevel = -1;
    m_strMachineSN = "7C-101";
    m_strTimeFormat = "yyyy-MM-dd hh:mm:ss";
    m_bQmlKeyboard = false;
    m_bCheckCardExist = true;
    CreateDir(GetPdfDir());
    CreateDir(GetXlsxDir());
    CreateDir(GetResourceDir());
    qRegisterMetaType<QList<QMap<double,double>>> ("QList<QMap<double,double>>");
    qRegisterMetaType<QList<double>> ("QList<double>");
    qRegisterMetaType<SCanBusDataStruct>("SCanBusDataStruct");
    qRegisterMetaType<QMap<int,QStringList>>("QMap<int,QStringList>");
    qRegisterMetaType<QMap<int,QString>>("QMap<int,QString>");

    m_strLogSaveDir = QApplication::applicationDirPath() + "/data/";
    CreateDir(m_strLogSaveDir);
    m_strDBDir = QApplication::applicationDirPath() + "/db/";
    CreateDir(m_strDBDir);

    _InitMethodIDNameMap();
    _InitTimingIDIndexMap();
    _InitMotorMethodIDCHTextMap();
    _InitPyrolysisParam();
    m_iSoftType = Soft_Auto;
    m_strAutoMotorNameList << tr("光学电机") << tr("PCR电机") << tr("顶针阀电机1") << tr("顶针阀电机2")
                           << tr("顶针阀电机3") << tr("顶针阀电机4")
                              // << tr("刺破电机1") << tr("刺破电机2") << tr("刺破电机3") << tr("刺破电机4")
                           << tr("刺破电机1")
                           << tr("气嘴电机") << tr("模组压紧电机X")
                           << tr("超声电机Y") << tr("模组升降电机Z") << tr("柱塞泵电机");
    m_strExtractMotorNameList  << tr("刺破电机1") << tr("刺破电机2") << tr("刺破电机3") << tr("刺破电机4")
                               << tr("顶针阀1") << tr("顶针阀2") << tr("顶针阀3") << tr("顶针阀4")
                               << tr("气嘴电机") << tr("PCR阀1") << tr("PCR阀2") << tr("柱塞泵")
                               << tr("组合电机");
    for(int i=0; i<gk_iMachineCount; i++)
    {
        m_eDeviceStatusList.push_back(eDeviceDisconnect);

        m_strXlsxNameList.push_back("");
        m_strPdfNameList.push_back("");

        m_sPLCVersionList.push_back(SPLCVerStruct());
    }

    m_iDevNum = 1;
    m_iItemNum = 8;
    QString strDevSettings = CConfigJson::GetInstance()->GetSystemValue("DevSettings").toString();
    if(!strDevSettings.isEmpty())
    {
        QStringList strList = strDevSettings.split("x");
        if(strList.size() >= 2)
        {
            m_iDevNum = strList.at(0).toInt();
            if(m_iDevNum < 1)
                m_iDevNum = 1;

            m_iItemNum = strList.at(1).toInt();
            m_iItemNum = qMin(m_iItemNum, 8);
        }
    }

    _InitAllSampleTypeList();
}

CPublicConfig::~CPublicConfig()
{

}

void CPublicConfig::RunLog(QString strLog)
{
    qDebug()<<strLog;
    emit CPublicConfig::GetInstance()->SignalRunLog(strLog);
}

void CPublicConfig::GetDevItemNum(int &iDevNum, int &iItemNum)
{
    iDevNum = m_iDevNum;
    iItemNum = m_iItemNum;
}

void CPublicConfig::SetMachineStatus(int iMachineID, DeviceStatus eStatus)
{
    if(iMachineID < 0 || iMachineID >= m_eDeviceStatusList.size())
        return;

    m_eDeviceStatusList[iMachineID] = eStatus;
}

DeviceStatus CPublicConfig::GetMachineStatus(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= m_eDeviceStatusList.size())
        return eDeviceDisconnect;

    return m_eDeviceStatusList.at(iMachineID);
}

QStringList CPublicConfig::GetMachineNameList()
{
    QStringList strList;
    for(int i=0; i<gk_iMachineCount; i++)
        strList << QString("%1#").arg(i + 1);
    return strList;
}

QString CPublicConfig::GetAgeDBString(const QString &strShowAge)
{
    QString strDBAge;
    QStringList strList = strShowAge.split(" ");
    if(strList.size() >= 2)
    {
        QString strValue = strList.at(0);
        QString strType = strList.at(1);
        if(tr("岁") == strType)
            strDBAge = strValue + " " + "Y";
        else if(tr("月") == strType)
            strDBAge = strValue + " " + "M";
        else if(tr("天") == strType)
            strDBAge = strValue + " " + "D";
    }
    return strDBAge;
}

QString CPublicConfig::GetAgeShowString(const QString &strDBAge)
{
    QString strShowAge;
    QStringList strList = strDBAge.split(" ");
    if(strList.size() >= 2)
    {
        QString strValue = strList.at(0);
        QString strType = strList.at(1);
        if("Y" == strType)
            strShowAge = strValue + " " + tr("岁");
        else if("M" == strType)
            strShowAge = strValue + " " + tr("月");
        else if("D" == strType)
            strShowAge = strValue + " " + tr("天");
    }
    return strShowAge;
}

QString CPublicConfig::GetGenderDBString(const QString &strShowGender)
{
    QString strDBGender;
    if(tr("男") == strShowGender)
        strDBGender = "M";
    else if(tr("女") == strShowGender)
        strDBGender = "F";
    else if(tr("其他") == strShowGender)
        strDBGender = "O";
    return strDBGender;
}

QString CPublicConfig::GetGenderShowString(const QString &strDBGender)
{
    QString strShowGender;
    if("M" == strDBGender)
        strShowGender = tr("男");
    else if("F" == strDBGender)
        strShowGender = tr("女");
    else if("O" == strDBGender)
        strShowGender = tr("其他");
    return strShowGender;
}

QString CPublicConfig::GetTestTypeShowString(const QString &strDBType)
{
    QString strShowType;
    if(IsPositiveQcTest(strDBType))
    {
        strShowType = tr("阳性质控测试");
    }
    else if(IsNegativeQcTest(strDBType))
    {
        strShowType = tr("阴性质控测试");
    }
    else
    {
        strShowType = tr("正常测试");
    }
    return strShowType;
}

QString CPublicConfig::GetTestTypeDBString(const QString &strShowType)
{
    QString strDBMType;
    if(tr("质控测试") == strShowType)
        strDBMType = "Q";
    else if(tr("正常测试") == strShowType)
        strDBMType = "T";
    else
        strDBMType.clear();
    return strDBMType;
}

QString CPublicConfig::GetStatusShowString(int iStatus)
{
    QString strStatus;
    if(eTestRunning == iStatus)
        strStatus = tr("正在测试");
    else if(eTestStop == iStatus)
        strStatus = tr("测试停止");
    else if(eTestCancel == iStatus)
        strStatus = tr("测试取消");
    else if(eTestDone == iStatus)
        strStatus = tr("测试完成");
    else if(eTestFail == iStatus)
        strStatus = tr("测试失败");
    return strStatus;
}

void CPublicConfig::_InitMethodIDNameMap()
{
    QStringList m_strMBMethodNameList;
    m_strMBMethodNameList << "start"<< "stop" << "pause" << "resume"
                          << "status" << "valve_fan_on" << "valve_fan_off"
                          << "sys_info" << "env_temp"<< "ht_info" << "read_motor_cmd" << "set_motor_cmd"
                          << "read_all_cmds" << "reset_all_cmds" << "delete_motor_cmd"
                          << "as_debug" << "mlog" << "mlog_req" << "mlog_data" << "mlog_end" << "mlog_info"
                          << "opt_byte" << "timing_file" << "timing_step" << "notify_flag" << "rtc" << "upgrade_req"
                          << "upgrade_data" << "upgrade_end" << "machine_reset" << "no_reset" << "reboot" << "notify"
                          << "power_off" << "heart_beat"<< "beep_flag"
                          << "beep_cfg" << "erase_flash" << "dev_id" << "fl_data" << "valve_on" << "valve_off"
                          << "pressure_on" << "pressure_stop" << "pressure_info" << "valve2_on" << "valve2_off"
                          << "N1CP_N2CA" << "N2CP_N1CA" << "PumpConAir";
    QStringList m_strMotorMCHKMethodNameList;
    m_strMotorMCHKMethodNameList << "MCHK" << "SRST" << "ETRST" << "ETMTA" << "ETACL" << "ETMTB"
                                 << "ETBCL" << "ETMTC" << "ETMTD" << "ETBTC" << "ETMTF" << "OFRST"
                                 << "OFMTPFH" << "OFMTPSH" << "OFDET" << "FRST" << "FMTSH" << "PCRRST"
                                 << "PCRCLP" << "OFMTFH" << "FDET"
                                 << "TV1RST" << "YV1PR" << "TV2RST" << "YV2PR" << "PM1RST" << "PM1PR"
                                 << "PM2RST" << "PM2PR" << "PM3RST" << "PM3PR" << "NZRST" << "NZPR"
                                 << "PCRVRST" << "PCRVPR" << "MRST" << "MMIX" << "VRST" << "VMTA"
                                 << "VMTH" << "HRST" << "HMTH" << "HMTA" << "HMTM" << "PRST" << "PEXT"
                                 << "PINJ" << "PETMC" << "PITEC" << "PITDC" << "PFEXT";
    QStringList m_strMotorSCMPMethodNameList;
    m_strMotorSCMPMethodNameList << "SCMP" << "GCMP" << "RLCMP" << "SDIR" << "GDIR" << "SPAM" << "GPAM"
                                 << "MOVE" << "GMSL" << "SMSL" << "RLPAM" << "GCID" << "OPB" << "CFFL"
                                 << "STAT" << "GXIO" << "RLCMP" << "ICMP" << "ECMP" << "IPAM" << "EPAM"
                                 << "SREGA" << "RREGA" << "CREGA" << "SRCHOP" << "RRCHOP" << "CRCHOP"
                                 << "CLEARPOS" << "ACTUALPOS" << "ENN" << "GTCOC" << "MSWST" << "MSWFLAG"
                                 << "GMCLK" << "SMCLK" << "RLCFG" << "MOTOR_COMPOSE";

    QStringList m_strPCRMethodNameList;
    m_strPCRMethodNameList << "pcr_start" << "pcr_stop" << "tec_table_req" << "tec_table_data"
                           << "tec_table_end" << "pcr_info" << "set_info_interval" << "pcr_signal"
                           << "version" << "reboot" << "pcr_upgrade_req" << "pcr_upgrade_data"
                           << "pcr_upgrade_end" << "save_env" << "set_tpid" << "get_tpid" << "set_ipid"
                           << "get_ipid" << "set_vpid" << "get_vpid" << "set_treach" << "set_tcalib"
                           << "wait_signal";

    QStringList m_strFLLightMethodNameList;
    m_strFLLightMethodNameList << "FLLED"  << "FLADC" << "FLCST"
                               << "FLCDT" << "FLCSP" << "FLFREQ"
                               << "FLMST" << "FLMSP" << "FLMDT"
                               << "FLGAINSET" << "FLGAINGET"
                               << "fl_upgrade_req" << "fl_upgrade_data" << "fl_upgrade_end"
                               << "fl_version" << "fl_reboot"
                               << "fl_ledi_get" << "fl_ledi_set";

    QStringList m_strHTMethodNameList;
    m_strHTMethodNameList <<"HTST"<<"HTSP"<<"HTSET"<<"HTGET"<<"HTCALC"<<"ht_param" << "HTCHK";

    QStringList m_strUSMethodNameList;
    m_strUSMethodNameList << "USST" << "USSP" << "USPSET" << "USMSET" << "AMP"
                          << "GPWR" << "USREBOOT" << "USPARAM" << "USFTY" << "USVERSION"
                          << "USINFO" << "USAMPOPT" << "USAMPLIST";

    QStringList m_strVTMethodNameList;
    m_strVTMethodNameList << "DELAY" << "loop_st" << "loop" << "jump" << "communication";

    QStringList m_strNetworkNameList;
    m_strNetworkNameList << "net_as_debug" << "net_mlog"  << "net_mlog_req"  << "net_mlog_data"  << "net_mlog_end"
                         << "net_mlog_info"  << "net_rtc"  << "net_upgrade_req"  << "net_upgrade_data"  << "net_upgrade_end"
                         << "net_reboot"  << "net_power_off"  << "net_heart_beat"  << "net_beep_flag"  << "net_beep_cfg"
                         << "net_erase_flash" << "net_pb_version";

    m_strMethodIDNameMap.insert(Method_can_id, "CanID");
    m_strMethodIDNameMap.insert(Method_start_identify, "start_identify");
    m_strMethodIDNameMap.insert(Method_stop_identify, "stop_identify");

    for(int i = 0; i != m_strMBMethodNameList.count(); ++i)
    {
        m_strMethodIDNameMap.insert(i+Method_start, m_strMBMethodNameList[i]);
    }
    for(int i = 0; i != m_strMotorMCHKMethodNameList.count(); ++i)
    {
        m_strMethodIDNameMap.insert(i+Method_MCHK, m_strMotorMCHKMethodNameList[i]);
    }
    m_strMethodIDNameMap.insert(Method_PCRMRST,"PCRMRST");
    m_strMethodIDNameMap.insert(Method_PCRPRTH,"PCRPRTH");
    m_strMethodIDNameMap.insert(Method_RIGHT, "RIGHT");
    m_strMethodIDNameMap.insert(Method_GAP, "GAP");
    m_strMethodIDNameMap.insert(Method_SAP, "SAP");
    for(int i = 0; i != m_strMotorSCMPMethodNameList.count(); ++i)
    {
        m_strMethodIDNameMap.insert(i+Method_SCMP, m_strMotorSCMPMethodNameList[i]);
    }

    for(int i=0; i<m_strPCRMethodNameList.size(); i++)
    {
        if(i < 8)
            m_strMethodIDNameMap.insert(i+Method_pcr_start, m_strPCRMethodNameList[i]);
        else
            m_strMethodIDNameMap.insert(i+Method_pcr_version-8, m_strPCRMethodNameList[i]);
    }

    for(int i=0; i<m_strFLLightMethodNameList.size(); i++)
    {
        m_strMethodIDNameMap.insert(i+Method_FLLED, m_strFLLightMethodNameList[i]);
    }
    for(int i=0; i<m_strHTMethodNameList.size(); i++)
    {
        m_strMethodIDNameMap.insert(i+Method_HTST, m_strHTMethodNameList[i]);
    }
    for(int i=0; i<m_strUSMethodNameList.size(); i++)
    {
        m_strMethodIDNameMap.insert(i+Method_US_USST, m_strUSMethodNameList[i]);
    }
    for(int i = 0; i < m_strVTMethodNameList.size(); ++i)
    {
        m_strMethodIDNameMap.insert(i+Method_DELAY, m_strVTMethodNameList[i]);
    }
    for(int i=0; i<m_strNetworkNameList.size(); i++)
    {
        m_strMethodIDNameMap.insert(i+Method_network_as_debug, m_strNetworkNameList[i]);
    }
}

CPublicConfig* CPublicConfig::GetInstance()
{
    if(nullptr == m_spInstance)
        m_spInstance = new CPublicConfig;
    return m_spInstance;
}

QString CPublicConfig::GetTimeFormatString()
{
    return m_strTimeFormat;
}

QString CPublicConfig::GetUDiskExportDir() const
{
    return GetUDiskDir() + "7C_Export_" + m_strMachineSN + "_"+ QDate::currentDate().toString("yyyyMMdd") + "/";
}

void CPublicConfig::SetMachineSN(const QString &strSN)
{
    m_strMachineSN = strSN;
    if(m_strMachineSN.isEmpty())
        m_strMachineSN = "7C101";
}

QString CPublicConfig::GetMachineSN() const
{
    return m_strMachineSN;
}

void CPublicConfig::SetDynamicPassword(bool bDynamicPassword)
{
    m_bDynamicPassword = bDynamicPassword;
}

bool CPublicConfig::GetDynamicPassword()
{
    return m_bDynamicPassword;
}

void CPublicConfig::SetDynamicUpValue(bool bDynamicUpValue)
{
    m_bDynamicUpValue = bDynamicUpValue;
}

bool CPublicConfig::GetDynamicUpValue()
{
    return m_bDynamicUpValue;
}
void CPublicConfig::SetCalcParam(bool bCalcParam)
{
    m_bCalcParam = bCalcParam;
}

bool CPublicConfig::GetCalcParam()
{
    return m_bCalcParam;
}
void CPublicConfig::SetLoginLevel(int iLevel)
{
    m_iLoginLevel = iLevel;
}

int CPublicConfig::GetLoginLevel() const
{
    return m_iLoginLevel;
}

void CPublicConfig::SetLoginUser(const QString &strUser)
{
    m_strLoginUser = strUser;
}

QString CPublicConfig::GetLoginUser() const
{
    return m_strLoginUser;
}

void CPublicConfig::SetLoginPassword(const QString &strPassword)
{
    m_strLoginPassword = strPassword;
}

QString CPublicConfig::GetLoginPassword()
{
    return m_strLoginPassword;
}

bool CPublicConfig::GetQmlKeyboard()
{
    return m_bQmlKeyboard;
}

QStringList CPublicConfig::GetLogDirList() const
{
    QStringList strLogDirList = {QApplication::applicationDirPath() + "/data/global/",
                                 QApplication::applicationDirPath() + "/data/error/",
                                 QApplication::applicationDirPath() + "/data/fatal/",
                                 QApplication::applicationDirPath() + "/data/warning/",
                                 QApplication::applicationDirPath() + "/data/slave/"};
    return strLogDirList;
}

QString CPublicConfig::GetPdfDir() const
{
    return QApplication::applicationDirPath() + "/pdf/";
}

QString CPublicConfig::GetXlsxDir() const
{
    return QApplication::applicationDirPath() + "/xlsx/";
}

void CPublicConfig::SetLightSNXlsxName(QString strSN)
{
    m_lightSNXlsxName = strSN;
}

QString CPublicConfig::GetLightSNXlsxName()
{
    return m_lightSNXlsxName;
}

QString CPublicConfig::GetResourceDir() const
{
    return QApplication::applicationDirPath() + "/Resources/";
}

QString CPublicConfig::GetLogJsonFilePath() const
{
    return GetResourceDir() + "log.json";
}

QString CPublicConfig::GetLogSaveDir() const
{
    return m_strLogSaveDir;
}

bool CPublicConfig::SetLogSaveDir(QString strDir)
{
    if(strDir.isEmpty())
        return false;

    QDir dir(strDir);
    if(!dir.exists())
        return false;

    m_strLogSaveDir = strDir;
    return true;
}

QString CPublicConfig::GetConfigFilePath() const
{
    return GetResourceDir() + "config.json";
}

QString CPublicConfig::GetMethodNameByID(int iMethodID) const
{
    return m_strMethodIDNameMap.value(iMethodID, "");
}

int CPublicConfig::GetMethodIDByName(QString strMethodName) const
{
    return m_strMethodIDNameMap.key(strMethodName);
}

QStringList CPublicConfig::GetHoleNameList() const
{
    QStringList strList = {tr("孔1"), tr("孔2")};
    return strList;
}

QString CPublicConfig::GetTecDBPath()
{
    return m_strDBDir + "tec.db";
}

QString CPublicConfig::GetHistoryDBPath()
{
    return m_strDBDir + "History.db";
}

QString CPublicConfig::GetMotorInfoDBPath()
{
    return m_strDBDir + "MotorInfo.db";
}

QString CPublicConfig::GetSystemDBPath()
{
    return m_strDBDir + "System.db";
}

QString CPublicConfig::GetLogDBPath()
{
    return m_strDBDir + "log.db";
}

QString CPublicConfig::GetTimingDBPath()
{
    return m_strDBDir + "timing.db";
}

QString CPublicConfig::GetMotorDBPath()
{
    return m_strDBDir + "motor.db";
}

QString CPublicConfig::GetUserDBPath()
{
    return m_strDBDir + "user.db";
}

QString CPublicConfig::GetLotInfoDBPath() const
{
    if(eLanguage_Chinese == gk_iLanguage)
        return m_strDBDir + "lotInfo.db";
    return m_strDBDir + "lotInfo_english.db";
}

QString CPublicConfig::GetProjectDBPath() const
{
    return m_strDBDir + "project.db";
}

int CPublicConfig::GetCmdIDFromIndex(int index)
{
    int iMethodID = m_strTimingIndex2IDMap.value(index);
    return iMethodID;
}

int CPublicConfig::GetTimingIndexByCmdID(int iCmdID)
{
    return m_strTimingIndex2IDMap.key(iCmdID, 0);
}

QString CPublicConfig::GetMotorMethodCHTextByID(int iMethodID)
{
    return m_strMotorIDCHTextMap.value(iMethodID, "");
}

QStringList CPublicConfig::GetMotorNameList()
{
    if(Soft_Auto == m_iSoftType)
        return m_strAutoMotorNameList;
    else if(Soft_Extarct == m_iSoftType)
        return m_strExtractMotorNameList;
    else
        return QStringList();
}

QStringList CPublicConfig::GetExtractMotorNameList()
{
    return m_strExtractMotorNameList;
}

void CPublicConfig::SetSoftTypeChanged(int iSoftType)
{
    m_iSoftType = iSoftType;
    emit SignalSoftTypeChanged(iSoftType);
}

void CPublicConfig::SetCheckCardExistBeforeStartTest(bool bCheck)
{
    m_bCheckCardExist = bCheck;
}

bool CPublicConfig::GetCheckCardExistBeforeStartTest()
{
    return m_bCheckCardExist;
}

void CPublicConfig::_InitTimingIDIndexMap()
{
    int index = 0;
    m_strTimingIndex2IDMap.insert(index++,Method_valve_on);
    m_strTimingIndex2IDMap.insert(index++,Method_valve_off);
    m_strTimingIndex2IDMap.insert(index++,Method_MCHK);
    m_strTimingIndex2IDMap.insert(index++,Method_SRST);

    for(int i=0; i<39; i++)
        m_strTimingIndex2IDMap.insert(index++, Method_OFRST+i);

    m_strTimingIndex2IDMap.insert(index++,Method_MOVE);
    m_strTimingIndex2IDMap.insert(index++,Method_pcr_start);
    m_strTimingIndex2IDMap.insert(index++,Method_pcr_stop);
    m_strTimingIndex2IDMap.insert(index++,Method_wait_signal);
    m_strTimingIndex2IDMap.insert(index++,Method_FLLED);
    m_strTimingIndex2IDMap.insert(index++,Method_FLADC);
    m_strTimingIndex2IDMap.insert(index++,Method_FLCST);
    m_strTimingIndex2IDMap.insert(index++,Method_FLCSP);
    m_strTimingIndex2IDMap.insert(index++,Method_FLMST);
    m_strTimingIndex2IDMap.insert(index++,Method_FLMSP);
    m_strTimingIndex2IDMap.insert(index++,Method_HTST);
    m_strTimingIndex2IDMap.insert(index++,Method_HTSP);
    m_strTimingIndex2IDMap.insert(index++,Method_US_USST);
    m_strTimingIndex2IDMap.insert(index++,Method_US_USSP);
    m_strTimingIndex2IDMap.insert(index++,Method_DELAY);
    m_strTimingIndex2IDMap.insert(index++,Method_loop_st);
    m_strTimingIndex2IDMap.insert(index++,Method_loop);
    m_strTimingIndex2IDMap.insert(index++,Method_jump);
    m_strTimingIndex2IDMap.insert(index++,Method_pressure_on);
    m_strTimingIndex2IDMap.insert(index++,Method_pressure_stop);
    m_strTimingIndex2IDMap.insert(index++,Method_valve2_on);
    m_strTimingIndex2IDMap.insert(index++,Method_valve2_off);
    m_strTimingIndex2IDMap.insert(index++,Method_PFEXT);
    m_strTimingIndex2IDMap.insert(index++,Method_MOTOR_COMPOSE);
    m_strTimingIndex2IDMap.insert(index++,Method_US_AMP);
    m_strTimingIndex2IDMap.insert(index++,Method_N1CP_N2CA);
    m_strTimingIndex2IDMap.insert(index++,Method_N2CP_N1CA);
    m_strTimingIndex2IDMap.insert(index++,Method_PumpConAir);
}

void CPublicConfig::_InitMotorMethodIDCHTextMap()
{
    m_strMotorIDCHTextMap.insert(Method_TV1RST, tr("顶针阀1复位/松开"));
    m_strMotorIDCHTextMap.insert(Method_TV1PR , tr("顶针阀1压紧"));
    m_strMotorIDCHTextMap.insert(Method_TV2RST, tr("顶针阀2复位/松开"));
    m_strMotorIDCHTextMap.insert(Method_NZPR  , tr("顶针阀2气嘴压紧"));
    m_strMotorIDCHTextMap.insert(Method_TV2PR , tr("顶针阀2压紧"));
    m_strMotorIDCHTextMap.insert(Method_TV3RST, tr("顶针阀3复位/松开"));
    m_strMotorIDCHTextMap.insert(Method_TV3PR , tr("顶针阀3压紧"));
    m_strMotorIDCHTextMap.insert(Method_TV4RST, tr("顶针阀4复位/松开"));
    m_strMotorIDCHTextMap.insert(Method_TV4PR , tr("顶针阀4压紧"));

    m_strMotorIDCHTextMap.insert(Method_PM1RST, tr("刺破电机1复位"));
    m_strMotorIDCHTextMap.insert(Method_PM1PR , tr("刺破电机1刺破"));
    m_strMotorIDCHTextMap.insert(Method_PM2RST, tr("刺破电机2复位"));
    m_strMotorIDCHTextMap.insert(Method_PM2PR , tr("刺破电机2刺破"));
    m_strMotorIDCHTextMap.insert(Method_PM3RST, tr("刺破电机3复位"));
    m_strMotorIDCHTextMap.insert(Method_PM3PR , tr("刺破电机3刺破"));
    m_strMotorIDCHTextMap.insert(Method_PM4RST, tr("刺破电机4复位"));
    m_strMotorIDCHTextMap.insert(Method_PM4PR , tr("刺破电机4刺破"));

    m_strMotorIDCHTextMap.insert(Method_HXRST , tr("提取水平压紧电机X复位"));
    m_strMotorIDCHTextMap.insert(Method_HXMTM , tr("提取水平压紧电机X移动到混匀位"));
    m_strMotorIDCHTextMap.insert(Method_HXMTA , tr("提取水平压紧电机X移动到磁珠吸附位"));
    m_strMotorIDCHTextMap.insert(Method_HXMTH , tr("提取水平压紧电机X移动到加热位"));
    m_strMotorIDCHTextMap.insert(Method_HYRST , tr("提取水平超声电机Y复位/裂解腔"));
    m_strMotorIDCHTextMap.insert(Method_HYMTE , tr("提取水平超声电机Y移动到洗脱腔"));
    m_strMotorIDCHTextMap.insert(Method_PRST  , tr("柱塞泵电机复位"));
    m_strMotorIDCHTextMap.insert(Method_PEXT  , tr("柱塞泵电机抽取**ml"));
    m_strMotorIDCHTextMap.insert(Method_PINJ  , tr("柱塞泵电机注射**ml"));

    m_strMotorIDCHTextMap.insert(Method_PRRST  , tr("柱塞泵电机逆向复位"));
    m_strMotorIDCHTextMap.insert(Method_OFCOMP , tr("光学滤片运动检光"));
    m_strMotorIDCHTextMap.insert(Method_VRST   , tr("提取升降电机Z复位/混匀位"));
    m_strMotorIDCHTextMap.insert(Method_VMTA   , tr("提取升降电机Z移动到吸附位"));
    m_strMotorIDCHTextMap.insert(Method_VMTH   , tr("提取升降电机Z移动到加热位"));
    m_strMotorIDCHTextMap.insert(Method_TVPR14 , tr("顶针阀1-4压紧"));
}
void CPublicConfig::_InitPyrolysisParam()
{
    m_strHTOpenList << tr("烘干加热")<<tr("热裂解加热")<< tr("模块1加热") << tr("模块2加热") << tr("模块3加热")<<tr("模块4加热")<<tr("全部模块启动加热");
    m_strHTCloseList << tr("烘干关闭加热")<<tr("热裂解关闭加热")<<tr("模块1关闭加热") << tr("模块2关闭加热") << tr("模块3关闭加热")<<tr("模块4关闭加热")<<tr("全部模块关闭加热");
    m_htParamIndexIdMap = {
        {0,3},
        {1,12},
        {2,1},
        {3,2},
        {4,4},
        {5,8},
        {6,15}
    };
}

QStringList CPublicConfig::GetPyrolysisStringList(int iCmdId)
{
    if(Method_HTST == iCmdId)
    {
        return m_strHTOpenList;
    }
    else if(Method_HTSP == iCmdId)
    {
        return m_strHTCloseList;
    }
    return QStringList();
}

int CPublicConfig::GetPyroLysisParamId(int iIndex)
{
    return  m_htParamIndexIdMap.value(iIndex);
}

int CPublicConfig::GetPyroLysisParamIndexById(int iId)
{
    QHash<int, int>::const_iterator it;
    for (it = m_htParamIndexIdMap.constBegin(); it != m_htParamIndexIdMap.constEnd(); ++it)
    {
        if (it.value() == iId)
        {
            return it.key();
        }
    }
    return -1;
}

void CPublicConfig::SetTestXlsxName(int iMachineID, const QString &strXlsxName)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    m_strXlsxNameList[iMachineID] = GetXlsxDir() + strXlsxName;
}

QString CPublicConfig::GetTestXlsxName(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return "";

    return m_strXlsxNameList.at(iMachineID);
}

void CPublicConfig::SetTestPdfName(int iMachineID, const QString &strPdfName)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    m_strPdfNameList[iMachineID] = GetPdfDir() + strPdfName;
}

QString CPublicConfig::GetTestPdfName(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return "";

    return m_strPdfNameList.at(iMachineID);
}

void CPublicConfig::SetPLCVersionStruct(int iMachineID, const SPLCVerStruct &sVerStruct)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    m_sPLCVersionList[iMachineID] = sVerStruct;
}

SPLCVerStruct CPublicConfig::GetPLCVersionStruct(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return SPLCVerStruct();

    return m_sPLCVersionList.at(iMachineID);
}

void CPublicConfig::SetRunTimeMinute(int iMinute)
{
    if(iMinute <= 0)
        iMinute = 16;
    m_iRunTimeMinute = iMinute;
}

int CPublicConfig::GetRunTimeMinute()
{
    return m_iRunTimeMinute;
}

void CPublicConfig::SetPowerVersion(QString strPowerVersion)
{
    m_strPowerVersion = strPowerVersion;
}

QString CPublicConfig::GetPowerVersion()
{
    return m_strPowerVersion;
}

void CPublicConfig::SetWiFiConnect(bool bConnect)
{
    m_bWiFiConnect = bConnect;
}

bool CPublicConfig::GetWiFiConnect()
{
    return m_bWiFiConnect;
}

void CPublicConfig::SetEthConnect(bool bConnect)
{
    m_bEthConnect = bConnect;
}

bool CPublicConfig::GetEthConnect()
{
    return m_bEthConnect;
}

void CPublicConfig::SetFtpAutoUpload(bool bUpload)
{
    m_bFtpAutoUpload = bUpload;
}

bool CPublicConfig::GetFtpAutoUpload()
{
    return m_bFtpAutoUpload;
}

void CPublicConfig::SetTestDoneAutoReset(bool bReset)
{
    m_bAutoReset = bReset;
}

bool CPublicConfig::GetTestDoneAutoReset()
{
    return m_bAutoReset;
}

void CPublicConfig::InitSampleTypeMap()
{
    m_strProjectSampleTypeMap.clear();

    QMap<QString, QStringList> strKeyMap = CLotInfoDB::GetInstance()->GetProjectSampleTypeMap();
    for(auto it=strKeyMap.constBegin(); it!=strKeyMap.constEnd(); it++)
    {
        QString strProject = it.key();
        QStringList strKeyList = it.value();
        for(int i=0; i<strKeyList.size(); i++)
        {
            if(strKeyList.at(i).isEmpty())
                continue;

            int iKey = strKeyList.at(i).toInt();
            m_strProjectSampleTypeMap[strProject] << m_strAllSampleTypeMap.value(iKey, "");
        }
        qDebug()<<"项目,样本类型:"<<strProject<<strKeyList<<m_strProjectSampleTypeMap.value(strProject);
    }
}

void CPublicConfig::_InitAllSampleTypeList()
{
    m_strAllSampleTypeMap[0] = "";
    m_strAllSampleTypeMap[1] = QObject::tr("咽拭子");
    m_strAllSampleTypeMap[2] = QObject::tr("鼻咽拭子");
    m_strAllSampleTypeMap[3] = QObject::tr("口咽拭子");
    m_strAllSampleTypeMap[4] = QObject::tr("痰液");
    m_strAllSampleTypeMap[5] = QObject::tr("血液");
    m_strAllSampleTypeMap[6] = QObject::tr("尿液");
    m_strAllSampleTypeMap[7] = QObject::tr("宫颈或阴道拭子");
    m_strAllSampleTypeMap[8] = QObject::tr("鼻拭子");
    m_strAllSampleTypeMap[9] = QObject::tr("皮肤或软组织感染拭子");
    m_strAllSampleTypeMap[10]= QObject::tr("直肠拭子");
    m_strAllSampleTypeMap[11]= QObject::tr("肛拭子");
    m_strAllSampleTypeMap[12]= QObject::tr("阴道拭子");
    m_strAllSampleTypeMap[13]= QObject::tr("血浆");
    m_strAllSampleTypeMap[14]= QObject::tr("血清");
    m_strAllSampleTypeMap[15]= QObject::tr("痰液");
    m_strAllSampleTypeMap[16]= QObject::tr("培养物");
    m_strAllSampleTypeMap[17]= QObject::tr("宫颈脱落细胞");
    m_strAllSampleTypeMap[1000] = QObject::tr("质控样品");

}

QStringList CPublicConfig::GetSampleTypeList(QString strProject)
{
    return m_strProjectSampleTypeMap.value(strProject, QStringList());
}
/* "";咽拭子;鼻咽拭子;口咽拭子;痰液;血液;尿液;宫颈或阴道拭子;鼻拭子;皮肤或软组织感染拭子;直肠拭子;肛拭子;质控样品
 * 2019-nCoV/FluA/FluB/RSV            鼻咽拭子;口咽拭子;质控样品 2;3;1000
 * Respiratory Pathogen Panel         鼻咽拭子;口咽拭子;质控样品 2;3;1000
 * Lower Respiratory Pathogen Panel   痰液;质控样品 4;1000
 * CT/NG                              尿液;宫颈或阴道拭子;质控样品 6;7;1000
 * STI Plus                           尿液;宫颈或阴道拭子;质控样品 6;7;1000
 * MRSA/SA                            鼻拭子;皮肤或软组织感染拭子;质控样品 8;9;1000
 * VanA/VanB/CE                       直肠拭子;肛拭子;质控样品 10;11;1000
 * Six Respiratory Pathogen Panel     口咽拭子;质控样品  3;1000;
 * GBS Test                           阴道拭子;直肠拭子;质控样品 12;10;1000
 * Vector-borne Pathogen Panel        血液;血浆;血清;质控样品质控样品 5;13;14;1000
 * HRM/TEST                           鼻咽拭子;口咽拭子;质控样品 2;3;1000
 * TEST                               鼻咽拭子;口咽拭子;质控样品 2;3;1000
*/
QString CPublicConfig::GetSampleTypeString(int iSampleType)
{
    return m_strAllSampleTypeMap.value(iSampleType, "");
}

int CPublicConfig::GetSampleTypeKey(QString strSampleType)
{
    return m_strAllSampleTypeMap.key(strSampleType, 0);
}

void CPublicConfig::SetLisTwoWay(bool bTwoWay)
{
    m_bLisTwoWay = bTwoWay;
}

bool CPublicConfig::GetLisTwoWay()
{
    return m_bLisTwoWay;
}

void CPublicConfig::SetShowRawCurve(bool bShowCurve)
{
    m_bShowRawCurve = bShowCurve;
    emit SignalShowRawCurve(m_bShowRawCurve);
}

bool CPublicConfig::GetShowRawCurve()
{
    return m_bShowRawCurve;
}

QString CPublicConfig::GetFullVersion()
{
    return m_strFullVersion;
}
