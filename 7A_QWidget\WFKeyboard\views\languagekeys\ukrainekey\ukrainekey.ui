<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Ukrainekey</class>
 <widget class="QWidget" name="Ukrainekey">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>784</width>
    <height>160</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QStackedWidget" name="stackedWidget_Normal">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="page_Qwerty_Board">
      <layout class="QVBoxLayout" name="verticalLayout">
       <property name="spacing">
        <number>3</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <layout class="QHBoxLayout" name="layout_Normal_Row02">
         <property name="spacing">
          <number>2</number>
         </property>
         <item>
          <widget class="KeyBordToolButton" name="toolButton1">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">й</string>
           </property>
           <property name="autoRepeat">
            <bool>false</bool>
           </property>
           <property name="autoRepeatInterval">
            <number>100</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton2">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> ц</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton3">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> у</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton4">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">к</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton5">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> е</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton6">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> н</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton7">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> г</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton8">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> ш</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton9">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">щ</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton10">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="text">
            <string notr="true">з</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton11">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">х</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton12">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="text">
            <string notr="true">Ї</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="layout_Normal_Row03">
         <property name="spacing">
          <number>2</number>
         </property>
         <item>
          <widget class="KeyBordToolButton" name="toolButton13">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">ф</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton14">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">i</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton15">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">в</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton16">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">а</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton17">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">п</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton18">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">р</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton19">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">о</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton20">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> л</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton21">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="text">
            <string notr="true">д</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton22">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="text">
            <string notr="true">ж</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton23">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">Є</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="layout_Normal_Row04" stretch="0,10,10,10,10,10,10,10,10,10,10,10,0">
         <property name="spacing">
          <number>2</number>
         </property>
         <item>
          <spacer name="horizontalSpacer_7">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>6</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_capslock">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>100</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>20</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"/>
           </property>
           <property name="icon">
            <iconset>
             <normaloff>:/newImg/newImg/KeyBorder/icon_small_normal.png</normaloff>
             <normalon>:/images/KeyBorder/icon_big.png</normalon>
             <activeoff>:/images/KeyBorder/icon_small_normal.png</activeoff>
             <activeon>:/newImg/newImg/KeyBorder/icon_big.png</activeon>:/newImg/newImg/KeyBorder/icon_small_normal.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>40</width>
             <height>40</height>
            </size>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton24">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> я</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton25">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">ч</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton26">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">с</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton27">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">м</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton28">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> и</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton29">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> т</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton30">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">ь</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton31">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="text">
            <string notr="true">б</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton32">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="text">
            <string notr="true">ю</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_capslock_right">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>100</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>20</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"/>
           </property>
           <property name="icon">
            <iconset>
             <normaloff>:/newImg/newImg/KeyBorder/icon_small_normal.png</normaloff>
             <normalon>:/images/KeyBorder/icon_big.png</normalon>
             <activeoff>:/images/KeyBorder/icon_small_normal.png</activeoff>
             <activeon>:/newImg/newImg/KeyBorder/icon_big.png</activeon>:/newImg/newImg/KeyBorder/icon_small_normal.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>40</width>
             <height>40</height>
            </size>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_8">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>6</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>KeyBordToolButton</class>
   <extends>QToolButton</extends>
   <header>./common/keybordtoolbutton/keybordtoolbutton.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
