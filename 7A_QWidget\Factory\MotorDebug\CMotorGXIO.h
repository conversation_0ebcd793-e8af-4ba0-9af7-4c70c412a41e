#ifndef CMOTORGXIO_H
#define CMOTORGXIO_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-12-05
  * Description: 电机光耦
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QTimer>
#include <QWidget>
#include <QCheckBox>
#include <QPushButton>

#include "CCmdBase.h"
#include "CLabelLabel.h"
#include "CLabelLineEdit.h"
#include "CLabelComboBox.h"

class CMotorGXIO : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CMotorGXIO(QWidget *parent = nullptr);
    ~CMotorGXIO();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

private slots:
    void _SlotReadTimeout();
    void _SlotStartChecked(bool bChecked);

private:
    void _InitWidget();

private:
    class CColorLabel : public QWidget
    {
    public:
        CColorLabel(const QString &strName, bool bRed = true);
        void SetRedColor(bool bRed) const;

    private:
        QLabel *m_pNameLabel, *m_pColorLabel;
    };

private:
    QList<CColorLabel *> m_pLabelList;
    CLabelLineEdit *m_pTimeLineEdit;
    QCheckBox *m_pCheckBox;
    CLabelLabel *m_pValueLabel;
    CLabelComboBox *m_pMachineComboBox;

    QTimer *m_pReadTimer;
};

#endif // CMOTORGXIO_H
