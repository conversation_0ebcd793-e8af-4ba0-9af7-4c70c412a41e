#include "germankey.h"

#include "common/keyboardtoolbutton/keyboardtoolbutton.h"

GermanKey::<PERSON><PERSON><PERSON>(const QString& name):
    LanguageBase<PERSON><PERSON>(name)
{
    InitButtons();
}

void GermanKey::InitButtons()
{
    QMap<int, QList<KeyBoardToolButton*> > map;
    QList<KeyBoardToolButton*> firstButtons, secondButtons, thirdButtons;

    firstButtons.append(new KeyBoardToolButton("\x0071","\x0051","",""));
    firstButtons.append(new KeyBoardToolButton("\x0077","\x0057","",""));
    firstButtons.append(new KeyBoardToolButton("\x0065","\x0045","é,è,ê,ë,ė","É,È,Ê,Ë,Ė"));
    firstButtons.append(new KeyBoardToolButton("\x0072","\x0052","",""));
    firstButtons.append(new KeyBoardToolButton("\x0074","\x0054","",""));
    firstButtons.append(new KeyBoardToolButton("\x0079","\x0059","",""));
    firstButtons.append(new KeyBoardToolButton("\x0075","\x0055","ü,ū,ú,ù,û","Ü,Ū,Ú,Ù,Û"));
    firstButtons.append(new KeyBoardToolButton("\x0069","\x0049","",""));
    firstButtons.append(new KeyBoardToolButton("\x006F","\x004F","ö,ō,ø,œ,õ,ó,ò,ô","Ö,Ō,Ø,Œ,Õ,Ó,Ò,Ô"));
    firstButtons.append(new KeyBoardToolButton("\x0070","\x0050","",""));

    secondButtons.append(new KeyBoardToolButton("\x0061","\x0041","ä,à,á,â,æ,ã,å,ā","Ä,À,Á,Â,Æ,Ã,Å,Ā"));
    secondButtons.append(new KeyBoardToolButton("\x0073","\x0053","\u00DF,\u015B,\u0161","\u015A,\u0160"));
    secondButtons.append(new KeyBoardToolButton("\x0064","\x0044","",""));
    secondButtons.append(new KeyBoardToolButton("\x0066","\x0046","",""));
    secondButtons.append(new KeyBoardToolButton("\x0067","\x0047","",""));
    secondButtons.append(new KeyBoardToolButton("\x0068","\x0048","",""));
    secondButtons.append(new KeyBoardToolButton("\x006A","\x004A","",""));
    secondButtons.append(new KeyBoardToolButton("\x006B","\x004B","",""));
    secondButtons.append(new KeyBoardToolButton("\x006C","\x004C","",""));

    thirdButtons.append(GetLeftCapsLockBtn());
    thirdButtons.append(new KeyBoardToolButton("\x007A","\x005A","",""));
    thirdButtons.append(new KeyBoardToolButton("\x0078","\x0058","",""));
    thirdButtons.append(new KeyBoardToolButton("\x0063","\x0043","",""));
    thirdButtons.append(new KeyBoardToolButton("\x0076","\x0056","",""));
    thirdButtons.append(new KeyBoardToolButton("\x0062","\x0042","",""));
    thirdButtons.append(new KeyBoardToolButton("\x006E","\x004E","\u00F1,\u0144","\u00D1,\u0143"));
    thirdButtons.append(new KeyBoardToolButton("\x006D","\x004D","",""));
    thirdButtons.append(GetRightCapsLockBtn());

    map.insert(0,firstButtons);
    map.insert(1,secondButtons);
    map.insert(2,thirdButtons);

    SetButtonsMap(map);
    SetTranslate("Chinesisch","Englisch","Mathematik","Platz");
}
