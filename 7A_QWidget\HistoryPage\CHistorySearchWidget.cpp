#include "CHistorySearchWidget.h"
#include <QDebug>
#include <QPainter>
#include <QBoxLayout>
#include <QGridLayout>

#include "CLotInfoDB.h"
#include "PublicParams.h"
#include "PublicConfig.h"
#include "PublicFunction.h"

CHistorySearchWidget::CHistorySearchWidget(QWidget *parent) : QWidget(parent)
{
    qRegisterMetaType<SHistroySearchStruct>("SHistroySearchStruct");

    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);    
    this->setAttribute(Qt::WA_TranslucentBackground);
    this->setFixedSize(1684, 960);

    _InitWidget();
    _InitLayout();

    m_pCDateTimeWidget = new CDateTimeWidget(this);
    m_pCDateTimeWidget->SetOnlyDateModel();
    connect(m_pCDateTimeWidget, &CDateTimeWidget::SignalDateTime, this, &CHistorySearchWidget::_SlotConfirmDate);
    m_pCDateTimeWidget->setVisible(false);

    LoadQSS(this, ":/qss/qss/history/search.qss");
}

void CHistorySearchWidget::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, gk_iBackTransparency));
    QWidget::paintEvent(pEvent);
}

void CHistorySearchWidget::showEvent(QShowEvent *pEvent)
{
    QStringList strProjectList = CLotInfoDB::GetInstance()->GetAllProjectShowName();
    strProjectList.push_front("");
    m_pProjetcWidget->SetComboBoxList(strProjectList);

    QWidget::showEvent(pEvent);
}

void CHistorySearchWidget::_SlotProjectChanged(QString strProject)
{
    if(strProject.isEmpty())
    {
        m_pSampleTypeWidget->SetComboBoxList(QStringList());
        return;
    }

    QStringList strSampleList = CPublicConfig::GetInstance()->GetSampleTypeList(strProject);
    strSampleList.push_front("");
    m_pSampleTypeWidget->SetComboBoxList(strSampleList);
}

void CHistorySearchWidget::_SlotCancelBtn()
{
    this->close();
}

void CHistorySearchWidget::_SlotConfirmBtn()
{
    SHistroySearchStruct sSearchStruct;
    sSearchStruct.strStartDate = m_pStartDateWidget->GetDateString();
    sSearchStruct.strEndDate = m_pEndDateWidget->GetDateString();
    sSearchStruct.strSampleID = m_pSampleIDWidget->GetLineEditText().trimmed();

    int iSampleType = m_pSampleTypeWidget->GetCurrentIndex();
    if(iSampleType > 0)
    {
        int iKey = CPublicConfig::GetInstance()->GetSampleTypeKey(m_pSampleTypeWidget->GetCurrentText());
        sSearchStruct.strSampleType = QString::number(iKey);
    }

    sSearchStruct.strCardID = m_pCardIDWidget->GetLineEditText().trimmed();
    sSearchStruct.strName = m_pNameWidget->GetLineEditText().trimmed();
    sSearchStruct.strTelephone = m_pTelWidget->GetLineEditText().trimmed();

    QString strShowGender = m_pGenderWidget->GetCurrentText();
    sSearchStruct.strGender = CPublicConfig::GetInstance()->GetGenderDBString(strShowGender);

    QString strShowAge = m_pAgeWidget->GetAge();
    QStringList strAgeList = strShowAge.split(" ");
    if(!strAgeList.isEmpty())
    {
        QString strValue = strAgeList.at(0);
        if(!strValue.isEmpty())
            sSearchStruct.strAge = CPublicConfig::GetInstance()->GetAgeDBString(strShowAge);
    }

    sSearchStruct.strBirthday = m_pBirthdayWidget->GetDateString();
    sSearchStruct.strProject = m_pProjetcWidget->GetCurrentText();

    QString strShowTestType = m_pTestTypeWidget->GetCurrentText();
    int iTestType = m_pTestTypeWidget->GetCurrentIndex();
    if(1 == iTestType)
        sSearchStruct.strTestType = "T";
    else if(2 == iTestType)
        sSearchStruct.strTestType = "Q";

    this->close();
    _ClearData();

    qDebug()<<"历史查询,"<<"开始日期:"<<sSearchStruct.strStartDate<<"结束日期:"<<sSearchStruct.strEndDate
           <<"样本ID:"<<sSearchStruct.strSampleID<<"卡盒ID:"<<sSearchStruct.strCardID
          <<"姓名:"<<sSearchStruct.strName<<"电话:"<<sSearchStruct.strTelephone
         <<"性别:"<<strShowGender<<sSearchStruct.strGender<<"年龄:"<<strShowAge<<sSearchStruct.strAge
        <<"生日:"<<sSearchStruct.strBirthday<<"项目:"<<sSearchStruct.strProject
       <<"测试类型:"<<strShowTestType<<sSearchStruct.strTestType;
    emit SignalSearchConfirm(sSearchStruct);
}

void CHistorySearchWidget::_SlotShowDateWidget()
{
    CHNewLabelDate *pLabelDate = (CHNewLabelDate*)sender();
    if(nullptr == pLabelDate)
        return;

    m_iDateType = pLabelDate->property("DateType").toInt();
    m_pCDateTimeWidget->SetDateTime(pLabelDate->GetDateString());
    m_pCDateTimeWidget->setVisible(true);
}

void CHistorySearchWidget::_SlotConfirmDate(const QString &strDate)
{
    if(eStartDate == m_iDateType)
        m_pStartDateWidget->SetDateString(strDate);
    else if(eEndDate == m_iDateType)
        m_pEndDateWidget->SetDateString(strDate);
    else if(eBirthDate == m_iDateType)
        m_pBirthdayWidget->SetDateString(strDate);
    else
        return;
}

void CHistorySearchWidget::_ClearData()
{
    m_pStartDateWidget->SetDateString("");
    m_pEndDateWidget->SetDateString("");
    m_pSampleIDWidget->SetLineEditText("");
    m_pSampleTypeWidget->SetCurrentIndex(-1);
    m_pCardIDWidget->SetLineEditText("");
    m_pNameWidget->SetLineEditText("");
    m_pTelWidget->SetLineEditText("");
    m_pGenderWidget->SetCurrentIndex(-1);
    m_pAgeWidget->SetAge("");
    m_pBirthdayWidget->SetDateString("");
    m_pProjetcWidget->SetCurrentIndex(-1);
    m_pTestTypeWidget->SetCurrentIndex(-1);
}

void CHistorySearchWidget::_InitWidget()
{
    int iBackWidth = 1116;
    int iNameWidth = 135;
    int iValueWidth = 340;
    if(eLanguage_English == gk_iLanguage)
    {
        iNameWidth = 155;
        iValueWidth = 330;
    }
    else if(eLanguage_German == gk_iLanguage)
    {
         iNameWidth = 170;
         iValueWidth = 330;
         iBackWidth = 1150;
    }
    else if(eLanguage_Spanish == gk_iLanguage)
    {
        iNameWidth = 240;
        iBackWidth = 1300;
    }
    else if(eLanguage_Italian == gk_iLanguage)
    {
        iNameWidth = 200;
        iBackWidth = 1280;
    }

    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(iBackWidth, 756);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");

    m_pCHLabelTitleWidget = new CHLabelTitleWidget(tr("历史查询"));

    m_pStartDateWidget = new CHNewLabelDate(tr("开始日期"));
    m_pStartDateWidget->ResetNameLabelSize(iNameWidth, 56);
    m_pStartDateWidget->ResetDateLabelSize(iValueWidth, 56);
    m_pStartDateWidget->setProperty("DateType", eStartDate);
    connect(m_pStartDateWidget, &CHNewLabelDate::SignalPressEvent, this, &CHistorySearchWidget::_SlotShowDateWidget);

    m_pEndDateWidget = new CHNewLabelDate(tr("结束日期"));
    m_pEndDateWidget->ResetNameLabelSize(iNameWidth, 56);
    m_pEndDateWidget->ResetDateLabelSize(iValueWidth, 56);
    m_pEndDateWidget->setProperty("DateType", eEndDate);
    connect(m_pEndDateWidget, &CHNewLabelDate::SignalPressEvent, this, &CHistorySearchWidget::_SlotShowDateWidget);

    m_pSampleIDWidget = new CHLabelLineEdit(tr("样本编号"));
    m_pSampleIDWidget->ResetLabelSize(iNameWidth, 56);
    m_pSampleIDWidget->ResetLineEditSize(iValueWidth, 56);

    m_pSampleTypeWidget = new CHLabelComboBox(tr("样本类型"), QStringList());
    m_pSampleTypeWidget->ResetLabelSize(iNameWidth, 56);
    m_pSampleTypeWidget->ResetComboBoxSize(iValueWidth, 56);
    m_pSampleTypeWidget->SetMaxVisibleItems(8);
    m_pSampleTypeWidget->SetComboBoxList(QStringList()); //选了项目才有样本类型

    m_pCardIDWidget = new CHLabelLineEdit(tr("试剂卡编号"));
    m_pCardIDWidget->ResetLabelSize(iNameWidth, 56);
    m_pCardIDWidget->ResetLineEditSize(iValueWidth, 56);

    m_pNameWidget = new CHLabelLineEdit(tr("姓名"));
    m_pNameWidget->ResetLabelSize(iNameWidth, 56);
    m_pNameWidget->ResetLineEditSize(iValueWidth, 56);

    m_pTelWidget = new CHLabelLineEdit(tr("电话"));
    m_pTelWidget->ResetLabelSize(iNameWidth, 56);
    m_pTelWidget->ResetLineEditSize(iValueWidth, 56);

    m_pGenderWidget = new CHLabelComboBox(tr("性别"), {"", tr("男"), tr("女"), tr("其他")});
    m_pGenderWidget->ResetLabelSize(iNameWidth, 56);
    m_pGenderWidget->ResetComboBoxSize(iValueWidth, 56);

    m_pAgeWidget = new CHAgeWidget;
    m_pAgeWidget->ResetLabelSize(iNameWidth, 56);
    m_pAgeWidget->ResetLineEditSize(221, 56);
    m_pAgeWidget->ResetComboBoxSize(107, 56);
    if(eLanguage_English == gk_iLanguage)
    {
        m_pAgeWidget->ResetLineEditSize(168, 56);
        m_pAgeWidget->ResetComboBoxSize(150, 56);
    }
    else if(eLanguage_German == gk_iLanguage)
    {
        m_pAgeWidget->ResetLineEditSize(168, 56);
        m_pAgeWidget->ResetComboBoxSize(150, 56);
    }
    else if(eLanguage_Spanish == gk_iLanguage)
    {
        m_pAgeWidget->ResetLineEditSize(198, 56);
        m_pAgeWidget->ResetComboBoxSize(130, 56);
    }
    else if(eLanguage_Italian == gk_iLanguage)
    {
        m_pAgeWidget->ResetLineEditSize(173, 56);
        m_pAgeWidget->ResetComboBoxSize(155, 56);
    }

    m_pBirthdayWidget = new CHNewLabelDate(tr("生日"));
    m_pBirthdayWidget->ResetNameLabelSize(iNameWidth, 56);
    m_pBirthdayWidget->ResetDateLabelSize(iValueWidth, 56);
    m_pBirthdayWidget->setProperty("DateType", eBirthDate);
    connect(m_pBirthdayWidget, &CHNewLabelDate::SignalPressEvent, this, &CHistorySearchWidget::_SlotShowDateWidget);

    m_pProjetcWidget = new CHLabelComboBox(tr("项目"), {"", "2019-nCoV/FluA/FluB", "2019-nCoV"});
    m_pProjetcWidget->ResetLabelSize(iNameWidth, 56);
    m_pProjetcWidget->ResetComboBoxSize(iValueWidth, 56);
    m_pProjetcWidget->SetMaxVisibleItems(8);
    connect(m_pProjetcWidget, &CHLabelComboBox::SignalCurrentTextChanged, this, &CHistorySearchWidget::_SlotProjectChanged);

    m_pTestTypeWidget = new CHLabelComboBox(tr("测试类型"), {"", tr("正常测试"), tr("质控测试")});
    m_pTestTypeWidget->ResetLabelSize(iNameWidth, 56);
    m_pTestTypeWidget->ResetComboBoxSize(iValueWidth, 56);

    m_pCancelBtn = new QPushButton(tr("取消"));
    m_pCancelBtn->setFixedSize(150, 56);
    m_pCancelBtn->setObjectName("CancelBtn");
    connect(m_pCancelBtn, &QPushButton::clicked, this, &CHistorySearchWidget::_SlotCancelBtn);

    m_pConfirmBtn = new QPushButton(tr("确认"));
    m_pConfirmBtn->setFixedSize(150, 56);
    connect(m_pConfirmBtn, &QPushButton::clicked, this, &CHistorySearchWidget::_SlotConfirmBtn);
}

void CHistorySearchWidget::_InitLayout()
{
    QGridLayout *pGridLayout = new QGridLayout;
    pGridLayout->setMargin(0);
    pGridLayout->setSpacing(40);
    pGridLayout->addWidget(m_pStartDateWidget, 0, 0);
    pGridLayout->addWidget(m_pEndDateWidget, 0, 1);
    pGridLayout->addWidget(m_pProjetcWidget, 1, 0);
    pGridLayout->addWidget(m_pTestTypeWidget, 1, 1);
    pGridLayout->addWidget(m_pSampleIDWidget, 2, 0);
    pGridLayout->addWidget(m_pSampleTypeWidget, 2, 1);
    pGridLayout->addWidget(m_pCardIDWidget, 3, 0);
    pGridLayout->addWidget(m_pNameWidget, 3, 1);
    pGridLayout->addWidget(m_pTelWidget, 4, 0);
    pGridLayout->addWidget(m_pGenderWidget, 4, 1);
    pGridLayout->addWidget(m_pAgeWidget, 5, 0);
    pGridLayout->addWidget(m_pBirthdayWidget, 5, 1);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(60);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pCancelBtn);
    pBtnLayout->addWidget(m_pConfirmBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pBackLayout = new QVBoxLayout;
    pBackLayout->setContentsMargins(24, 12, 24, 24);
    pBackLayout->setSpacing(40);
    pBackLayout->addWidget(m_pCHLabelTitleWidget, 0, Qt::AlignLeft);
    pBackLayout->addLayout(pGridLayout);
    pBackLayout->addStretch(1);
    pBackLayout->addLayout(pBtnLayout);
    m_pBackgroundLabel->setLayout(pBackLayout);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pBackgroundLabel, 0, Qt::AlignCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}
