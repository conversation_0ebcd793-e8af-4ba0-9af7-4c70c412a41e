#ifndef CADDEDITUSERWIDGET_H
#define CADDEDITUSERWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-26
  * Description: 新增用户
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QGroupBox>
#include <QPushButton>

#include "CLabelLineEdit.h"
#include "CLabelComboBox.h"
#include "CHLabelTitleWidget.h"

class CAddEditUserWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CAddEditUserWidget(QWidget *parent = nullptr);

    void SetData(bool bAddUser, const QStringList &strList);

    static QString m_strDisableText; // 为了兼容以前,"-1"表示禁用,其他表示激活

protected:
    void paintEvent(QPaintEvent *pEvent) override;
    void showEvent(QShowEvent *pEvent) override;

signals:
    void SignalConfirm(bool bAddUser, const QStringList &strList);

private slots:
    void _SlotCancelBtn();
    void _SlotConfirmBtn();

private:
    QGroupBox *_CreateGroupBox();

private:
    CHLabelTitleWidget *m_pCHLabelTitleWidget;
    CHLabelLineEdit *m_pNameLineEdit;
    CHLabelLineEdit *m_pPwdLineEdit;
    CHLabelLineEdit *m_pRePwdLineEdit;
    CHLabelComboBox *m_pStatusComboBox;
    QPushButton *m_pCancelBtn, *m_pConfirmBtn;

    bool m_bAddUser;
    QString m_strTipsText;
};

#endif // CADDEDITUSERWIDGET_H
