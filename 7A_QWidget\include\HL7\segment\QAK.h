﻿#ifndef _QAK_H_
#define _QAK_H_
#include "../common/HL7Segment.h"
#include "../interface/IQAK.h"
/************************************************************************/
/* 查询感知信息段     Query Acknowledgement segment		Ch5.1.2			*/
/*QAK 信息段包含与查询回应一起发送的信息。
虽然对增强模态查询的回应要求有QAK信息段，
但是在对任何初始模态查询的回应（信息）中，它可以显示
为放置在（可选） ERR 信息段后的可选信息段。*/
/************************************************************************/
class QAK :
	public HL7Segment, public IQAK
{
public:
	QAK();
	virtual ~QAK();

	DECLARE_OBJECTBASE

	BEGIN_IMPL_QUERYIF(QAK)
		IMPL_QUERYIF_BYPATH(IF_OBJECTBASE, IHL7Segment, IObjectBase)
		IMPL_QUERYIF(IF_HL7SEGMENT, IHL7Segment)
		IMPL_QUERYIF(IF_QAK, IQAK)
		END_IMPL_QUERYIF()

	/*
	 *	\brief 查询标记符,默认取SR，index 0
	 */
	void SetQueryTag(const char* queryTagStr);

	void GetQueryTag(char** queryTagStr);

	/*
	 *	\brief 查询回应状况
	 */
	void SetQueryResponseStatus(const char* responseStatusStr);

	void GetQueryResponseStatus(char** responseStatusStr);

	void SetMessageQueryName(const char* msgQueryNameStr);

	void SetHitCount(const char* hitCountStr);

	void SetPayLoad(const char* payloadStr);

	void SetHitRemain(const char* hitsRemainStr);

	virtual void Parse(const char* segmentStr, EncodingCharacters encodingCharacters);

	QAK& operator=(QAK& qak);
private:
	HL7Field m_queryTag;//查询标记符
	HL7Field m_queryResponseStatus;//查询回应状况
	HL7Field m_messageQueryName;//信息查询名称
	HL7Field m_hitCount;//点击计数
	HL7Field m_payload;//有效载荷
	HL7Field m_hitsRemain;//点击保留
};

REGISTER_CLASS(QAK);

#endif