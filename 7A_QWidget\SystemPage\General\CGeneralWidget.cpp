#include "CGeneralWidget.h"
#include <QDebug>
#include <QThread>
#include <QTimer>
#include <QDateTime>
#include <QGridLayout>
#include <QStyleFactory>

#include "CRunTest.h"
#include "PublicParams.h"
#include "PublicConfig.h"
#include "CMessageBox.h"
#include "CConfigJson.h"
#include "PublicFunction.h"

CGeneralWidget::CGeneralWidget(QWidget *parent) : QWidget(parent)
{
    m_pSoundTimer = new QTimer;
    connect(m_pSoundTimer, &QTimer::timeout, this, &CGeneralWidget::_SlotSoundTimerout);

    this->setFixedSize(1684, 958);
    _InitWidget();
    _InitLayout();

    m_pCDateTimeWidget = new CDateTimeWidget(this);
    connect(m_pCDateTimeWidget, &CDateTimeWidget::SignalDateTime, this, &CGeneralWidget::_SlotConfirmDateTime);
    m_pCDateTimeWidget->setVisible(false);

    _ReadCfg();

    int iLockTime = m_pLockTimeWidget->GetLineEditText().toInt();
    bool bNeverLock = m_pNeverLockCheckBox->isChecked();
    emit CPublicConfig::GetInstance()->SignalLockScreenParams(iLockTime, bNeverLock);
}

CGeneralWidget::~CGeneralWidget()
{

}

void CGeneralWidget::showEvent(QShowEvent *pEvent)
{
    QString strDateTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    m_pTimeLineEdit->setText(strDateTime);

    _ReadCfg();

    QWidget::showEvent(pEvent);
}

void CGeneralWidget::_SlotSelectTimeBtn()
{
    for(int iMachineID=0; iMachineID<gk_iMachineCount; iMachineID++)
    {
        if(CRunTest::GetInstance()->GetRunInfoStruct(iMachineID).bRunning)
        {
            ShowInformation(this, tr("提示"), tr("正在进行测试，不允许修改系统时间，请稍后重试"));
            return;
        }
    }

    m_pCDateTimeWidget->SetDateTime(m_pTimeLineEdit->text());
    m_pCDateTimeWidget->show();
}

void CGeneralWidget::_SlotConfirmDateTime(const QString &strDateTime)
{
    if(strDateTime.isEmpty())
        return;

    for(int iMachineID=0; iMachineID<gk_iMachineCount; iMachineID++)
    {
        if(CRunTest::GetInstance()->GetRunInfoStruct(iMachineID).bRunning)
        {
            ShowInformation(this, tr("提示"), tr("正在进行测试，不允许修改系统时间，请稍后重试"));
            return;
        }
    }

    m_pTimeLineEdit->setText(strDateTime);
    QString strCmd = QString("date -s \"%1\"").arg(strDateTime);
    qDebug()<<"设置时间:"<<strCmd;
    System(strCmd.toStdString().c_str());
    System("hwclock -w");
}

void CGeneralWidget::_SlotBrightnessChanged(int iValue)
{
    QString strEcho = QString("echo %1 > /sys/class/backlight/backlight/brightness &").arg(iValue);
    System(strEcho.toStdString().c_str());
}

void CGeneralWidget::_SlotSoundChanged(int iValue)
{
    Q_UNUSED(iValue);
    m_pSoundTimer->stop();
    m_pSoundTimer->start(200);
}

void CGeneralWidget::_SlotSoundTimerout()
{
    m_pSoundTimer->stop();
    int iSound = m_pSoundSlider->value();
    qDebug()<<Q_FUNC_INFO<<iSound;
    System(QString("amixer sset 'Speaker Driver' %1").arg(iSound));
    System("aplay fireware/audio/bi.wav &");
}

void CGeneralWidget::_ChangeSystemLogo(int iLanguage)
{
    QString strResPath = CPublicConfig::GetInstance()->GetResourceDir();
    QString strBmpPath = strResPath + "english.bmp";
    QString strPngPath = strResPath + "english.png";
    if(eLanguage_Chinese == iLanguage)
    {
        strBmpPath = strResPath + "chinese.bmp";
        strPngPath = strResPath + "chinese.png";
    }
    else if(eLanguage_English == iLanguage)
    {
        strBmpPath = strResPath + "english.bmp";
        strPngPath = strResPath + "english.png";
    }
    else if(eLanguage_Spanish == iLanguage)
    {
        strBmpPath = strResPath + "spanish.bmp";
        strPngPath = strResPath + "spanish.png";
    }
    else if(eLanguage_German == iLanguage)
    {
        strBmpPath = strResPath + "german.bmp";
        strPngPath = strResPath + "german.png";
    }
    else if(eLanguage_Italian == iLanguage)
    {
        strBmpPath = strResPath + "italian.bmp";
        strPngPath = strResPath + "italian.png";
    }

    QString strLogoPath = "/oem/logo.bmp";
    QString strKernelPath = "/oem/logo_kernel.bmp";
    QString strBackPath = "/usr/share/weston/wondfo.png";

    System(QString("cp -f %1 %2").arg(strBmpPath).arg(strLogoPath));
    System(QString("cp -f %1 %2").arg(strBmpPath).arg(strKernelPath));
    System(QString("cp -f %1 %2").arg(strPngPath).arg(strBackPath));
    System("sync");
}

void CGeneralWidget::_SlotSaveBtn()
{
    bool bReboot = false;

    int iLanguage = m_pLanguageComboBox->currentIndex();
    CConfigJson::GetInstance()->SetSystemValue("Language", iLanguage);
    if(gk_iLanguage != iLanguage)
    {
        bReboot = true;
    }
    _ChangeSystemLogo(iLanguage);

    QJsonObject qRootObj = CConfigJson::GetInstance()->GetConfigJsonObject();

    qRootObj.insert("Brightness", m_pBrightnessSlider->value());
    qRootObj.insert("Sound", m_pSoundSlider->value());

    int iLockTime = m_pLockTimeWidget->GetLineEditText().toInt();
    if(iLockTime < 1)
        iLockTime = 1;
    m_pLockTimeWidget->SetLineEditText(QString::number(iLockTime));
    bool bNeverLock = m_pNeverLockCheckBox->isChecked();

    QJsonObject qLockObj = qRootObj.value("LockScreen").toObject();
    qLockObj.insert("LockTime", iLockTime);
    qLockObj.insert("NeverLock", bNeverLock);
    qRootObj.insert("LockScreen", qLockObj);

    emit CPublicConfig::GetInstance()->SignalLockScreenParams(iLockTime, bNeverLock);

    QString strRawDevSettings = CConfigJson::GetInstance()->GetSystemValue("DevSettings").toString();
    QString strNewDevSettings = m_pDevComboBox->currentText();
    CConfigJson::GetInstance()->SetSystemValue("DevSettings", strNewDevSettings);
    if(strRawDevSettings != strNewDevSettings)
        bReboot = true;

    CConfigJson::GetInstance()->SetConfigJsonObject(qRootObj);

    QString strMsgText = tr("设置成功");
    if(bReboot)
        strMsgText = tr("设置成功，重启后生效");
    ShowSuccess(this, tr("常规设置"), strMsgText);
}

void CGeneralWidget::_ReadCfg()
{
    CConfigJson *pCfg = CConfigJson::GetInstance();

    int iLanguage = pCfg->GetSystemValue("Language").toInt();
    m_pLanguageComboBox->setCurrentIndex(iLanguage);

    int iBrightness = 200;
    QVariant qBright = pCfg->GetConfigValue("Brightness");
    if(qBright.isNull())
        pCfg->SetConfigValue("Brightness", 200);
    else
        iBrightness = pCfg->GetConfigValue("Brightness").toInt();
    if(iBrightness < 100)
        iBrightness = 100;
    if(iBrightness > 255)
        iBrightness = 255;
    m_pBrightnessSlider->setValue(iBrightness);

    int iSound = 90;
    QVariant qSound = pCfg->GetConfigValue("Sound");
    if(qSound.isNull())
        pCfg->SetConfigValue("Sound", 90);
    else
        iSound = pCfg->GetConfigValue("Sound").toInt();
    if(iSound < 35)
        iSound = 35;
    if(iSound > 117)
        iSound = 117;
    m_pSoundSlider->setValue(iSound);

    int iLockTime = 60;
    bool bNeverLock = false;
    QVariant qLockVar = pCfg->GetConfigValue("LockScreen");
    if(qLockVar.isNull())
    {
        pCfg->SetConfigValue("LockScreen", "LockTime", iLockTime);
        pCfg->SetConfigValue("LockScreen", "NeverLock", bNeverLock);
    }
    else
    {
        bNeverLock = pCfg->GetConfigValue("LockScreen", "NeverLock").toBool();
        iLockTime = pCfg->GetConfigValue("LockScreen", "LockTime").toInt();
        if(iLockTime < 1)
            iLockTime = 1;
    }
    m_pLockTimeWidget->SetLineEditText(QString::number(iLockTime));
    m_pNeverLockCheckBox->setChecked(bNeverLock);

    QString strDevSettings = CConfigJson::GetInstance()->GetSystemValue("DevSettings").toString();
    if(strDevSettings.isEmpty())
        m_pDevComboBox->setCurrentIndex(7);
    else
        m_pDevComboBox->setCurrentText(strDevSettings);
}

void CGeneralWidget::_InitWidget()
{
    m_pCSysTtileLabelWidget = new CSysFirstTitleWidget(tr("系统设置"), tr("常规设置"));
    connect(m_pCSysTtileLabelWidget, &CSysFirstTitleWidget::SignalTitlePress, this, &CGeneralWidget::SignalReturn);

    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(1684, 904);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");

    //语言设置
    m_pLanguageTitleWidget = new CHLabelTitleWidget(tr("系统语言"));

    QStringList strLangList = {"简体中文", "English", "Español", "Deutsch", "Italiano"};
    m_pLanguageComboBox = new QComboBox(this);
    m_pLanguageComboBox->setFixedSize(400, 56);
    m_pLanguageComboBox->setView(new QListView);
    m_pLanguageComboBox->addItems(strLangList);
    m_pLanguageComboBox->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_pLanguageComboBox->setMaxVisibleItems(10);
    m_pLanguageComboBox->setStyle(QStyleFactory::create("Windows"));

    m_pChineseBtn = new QRadioButton("简体中文", this);
    m_pChineseBtn->setFixedSize(140, 56);
    m_pChineseBtn->setVisible(false);

    m_pEnglishBtn = new QRadioButton("English", this);
    m_pEnglishBtn->setFixedSize(140, 56);
    m_pEnglishBtn->setVisible(false);

    m_pSpanishBtn = new QRadioButton("Español", this);
    m_pSpanishBtn->setFixedWidth(140);
    m_pSpanishBtn->setVisible(false);

    m_pGermanBtn = new QRadioButton("Deutsch", this);
    m_pGermanBtn->setFixedWidth(140);
    m_pGermanBtn->setVisible(false);

    m_pItalianBtn = new QRadioButton("Italiano", this);
    m_pItalianBtn->setFixedWidth(140);
    m_pItalianBtn->setVisible(false);

    //时间设置
    m_pTimeSetTitleWidget = new CHLabelTitleWidget(tr("日期时间"));

    QString strDateTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    m_pTimeLineEdit = new CLineEdit(strDateTime);
    m_pTimeLineEdit->setFixedSize(400, 56);
    m_pTimeLineEdit->setEnabled(false);

    m_pTimeBtn = new QPushButton(tr("选择"));
    int iTimeWidth = 107;
    if(eLanguage_Spanish == gk_iLanguage || eLanguage_German == gk_iLanguage)
        iTimeWidth = 155;
    else if(eLanguage_Italian == gk_iLanguage)
        iTimeWidth = 130;
    m_pTimeBtn->setFixedSize(iTimeWidth, 56);
    connect(m_pTimeBtn, &QPushButton::clicked, this, &CGeneralWidget::_SlotSelectTimeBtn);

    //屏幕亮度
    m_pBrightnessTitleWidget = new CHLabelTitleWidget(tr("屏幕亮度"));

    m_pBrightnessSlider = new QSlider(Qt::Horizontal);
    m_pBrightnessSlider->setFixedSize(400, 22);
    m_pBrightnessSlider->setRange(100, 255);
    m_pBrightnessSlider->setValue(200);
    connect(m_pBrightnessSlider, &QSlider::valueChanged, this, &CGeneralWidget::_SlotBrightnessChanged);

    //声音
    m_pSoundTitleWidget = new CHLabelTitleWidget(tr("音量"));

    m_pSoundSlider = new QSlider(Qt::Horizontal);
    m_pSoundSlider->setFixedSize(400, 22);
    m_pSoundSlider->setRange(35, 117);
    m_pSoundSlider->setValue(50);
    connect(m_pSoundSlider, &QSlider::valueChanged, this, &CGeneralWidget::_SlotSoundChanged);

    //锁屏
    m_pLockTitleWidget = new CHLabelTitleWidget(tr("锁屏设置"));

    m_pLockTimeWidget = new CHLabelLineEdit(tr("自动锁屏时间/分钟"), "3");
    m_pLockTimeWidget->ResetLineEditSize(100, 56);

    m_pNeverLockCheckBox = new QCheckBox(tr("永不锁屏"));

    //连接
    m_pDevTitleWidget = new CHLabelTitleWidget(tr("连接设置"));

    m_pDevComboBox = new QComboBox;
    m_pDevComboBox->setFixedSize(305, 56);
    m_pDevComboBox->setView(new QListView);
    QStringList strDevList = {"1x1", "1x2", "1x3", "1x4", "1x5", "1x6", "1x7", "1x8"};
    m_pDevComboBox->addItems(strDevList);
    m_pDevComboBox->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_pDevComboBox->setMaxVisibleItems(10);
    m_pDevComboBox->setStyle(QStyleFactory::create("Windows"));

    m_pReturnBtn = new QPushButton(tr("返回"));
    m_pReturnBtn->setFixedSize(150, 56);
    m_pReturnBtn->setObjectName("CancelBtn");
    connect(m_pReturnBtn, &QPushButton::clicked, this, &CGeneralWidget::SignalReturn);

    m_pSaveBtn = new QPushButton(tr("保存"));
    m_pSaveBtn->setFixedSize(150, 56);
    connect(m_pSaveBtn, &QPushButton::clicked, this, &CGeneralWidget::_SlotSaveBtn);
}

void CGeneralWidget::_InitLayout()
{
    QHBoxLayout *pLanguageLayout = new QHBoxLayout;;
    pLanguageLayout->setContentsMargins(18, 0, 0, 0);
    pLanguageLayout->setSpacing(0);
    pLanguageLayout->addWidget(m_pLanguageComboBox);
    //pLanguageLayout->addWidget(m_pChineseBtn);
    //pLanguageLayout->addWidget(m_pEnglishBtn);
    pLanguageLayout->addStretch(1);

    QHBoxLayout *pTimeLayout = new QHBoxLayout;
    pTimeLayout->setContentsMargins(18, 0, 0, 0);
    pTimeLayout->setSpacing(0);
    pTimeLayout->addWidget(m_pTimeLineEdit);
    pTimeLayout->addSpacing(18);
    pTimeLayout->addWidget(m_pTimeBtn);
    pTimeLayout->addStretch(1);

    QHBoxLayout *pBrightnessLayout = new QHBoxLayout;
    pBrightnessLayout->setContentsMargins(18, 0, 0, 0);
    pBrightnessLayout->setSpacing(0);
    pBrightnessLayout->addWidget(m_pBrightnessSlider);
    pBrightnessLayout->addStretch(1);

    QHBoxLayout *pSoundLayout = new QHBoxLayout;
    pSoundLayout->setContentsMargins(18, 0, 0, 0);
    pSoundLayout->setSpacing(0);
    pSoundLayout->addWidget(m_pSoundSlider);
    pSoundLayout->addStretch(1);

    QHBoxLayout *pLockLayout = new QHBoxLayout;
    pLockLayout->setContentsMargins(18, 0, 0, 0);
    pLockLayout->setSpacing(0);
    pLockLayout->addWidget(m_pLockTimeWidget);
    pLockLayout->addSpacing(100);
    pLockLayout->addWidget(m_pNeverLockCheckBox);
    pLockLayout->addStretch(1);

    QVBoxLayout *pDevLayout = new QVBoxLayout;
    pDevLayout->setContentsMargins(18, 0, 0, 0);
    pDevLayout->setSpacing(0);
    pDevLayout->addWidget(m_pDevComboBox);
    pDevLayout->addStretch(1);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(60);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pReturnBtn);
    pBtnLayout->addWidget(m_pSaveBtn);
    pBtnLayout->addStretch(1);

    int iSpacing1 = 30, iSpacing2 = 60;

    QVBoxLayout *pLeftLayout = new QVBoxLayout;
    pLeftLayout->setMargin(0);
    pLeftLayout->addSpacing(0);
    pLeftLayout->addWidget(m_pLanguageTitleWidget, 0, Qt::AlignLeft);
    pLeftLayout->addSpacing(iSpacing1);
    pLeftLayout->addLayout(pLanguageLayout);
    pLeftLayout->addSpacing(iSpacing2);
    pLeftLayout->addWidget(m_pBrightnessTitleWidget, 0, Qt::AlignLeft);
    pLeftLayout->addSpacing(iSpacing1);
    pLeftLayout->addLayout(pBrightnessLayout);
    pLeftLayout->addSpacing(iSpacing2);
    pLeftLayout->addWidget(m_pLockTitleWidget, 0, Qt::AlignLeft);
    pLeftLayout->addSpacing(iSpacing1);
    pLeftLayout->addLayout(pLockLayout);
    pLeftLayout->addStretch(1);

    QVBoxLayout *pRightLayout = new QVBoxLayout;
    pRightLayout->setMargin(0);
    pRightLayout->addSpacing(0);
    pRightLayout->addWidget(m_pTimeSetTitleWidget, 0, Qt::AlignLeft);
    pRightLayout->addSpacing(iSpacing1);
    pRightLayout->addLayout(pTimeLayout);
    pRightLayout->addSpacing(iSpacing2);
    pRightLayout->addWidget(m_pSoundTitleWidget, 0, Qt::AlignLeft);
    pRightLayout->addSpacing(iSpacing1);
    pRightLayout->addLayout(pSoundLayout);
    pRightLayout->addSpacing(iSpacing2);
    pRightLayout->addWidget(m_pDevTitleWidget, 0, Qt::AlignLeft);
    pRightLayout->addSpacing(iSpacing1);
    pRightLayout->addLayout(pDevLayout);
    pRightLayout->addStretch(1);

    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(0);
    pTopLayout->addLayout(pLeftLayout);
    pTopLayout->addSpacing(150);
    pTopLayout->addLayout(pRightLayout);
    pTopLayout->addStretch(1);

    QVBoxLayout *pBackLayout = new QVBoxLayout;
    pBackLayout->setContentsMargins(24, 20, 0, 24);
    pBackLayout->setSpacing(0);
    pBackLayout->addLayout(pTopLayout);
    pBackLayout->addStretch(1);
    pBackLayout->addLayout(pBtnLayout);
    m_pBackgroundLabel->setLayout(pBackLayout);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pCSysTtileLabelWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pBackgroundLabel);
    this->setLayout(pLayout);
}
