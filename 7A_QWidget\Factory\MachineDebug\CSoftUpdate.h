#ifndef CSOFTUPDATE_H
#define CSOFTUPDATE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-12-18
  * Description:
  * -------------------------------------------------------------------------
  * History:升级
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QTimer>
#include <QTableWidget>
#include "CCmdBase.h"
#include "CProgressBar.h"
#include "CLabelLineEdit.h"
#include "CLabelComboBox.h"
#include "PublicParams.h"

class CSoftUpdate : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CSoftUpdate(QWidget *parent = nullptr);
    ~CSoftUpdate();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

public slots:
    void SlotPowerVersion(QString strVersion);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual void hideEvent(QHideEvent *pEvent) override;

private slots:
    void _SlotMachineChanged(int);
    void _SlotMedianBtnList();
    void _SlotPCRBtnList();
    void _SlotFLBtnList();
    void _SlotUpdateTimeout();
    void _SlotUpdateApp();
    void _SlotUpdateCalcLib();
    void _SlotUpdateAuto();
    void _SlotPowerQuery();
    void _SlotSystemQuery();

private:
    void _CheckPLCVersion(int iMachineID);

    void _QueryMedian(int iMachineID);
    void _UpdateMedian();
    void _RebootMedian();

    void _QueryPCR(int iMachineID);
    void _UpdatePCR();
    void _RebootPCR();

    void _QueryFL(int iMachineID);
    void _UpdateFL();
    void _RebootFL();

    void _StartUpdateFirm(int iMachineID, const QString &strUpdateFileName);
    void _UpdateFirmReply(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData);

    void _MedainCmdReply(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData);
    void _PCRCmdReply(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData);
    void _FLCmdReply(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData);

private:
    void _InitWidget();
    void _InitLayout();
    void _SetTableItem(int iRow, int iCol, QString strText);
    void _SetTableWidget(int iRow, int iCol, int iWidth, int iHeight, QWidget *pWidget);

private:
    enum UpdateType
    {
        Update_Median = 0,
        Update_PCR = 1,
        Update_FL = 2,
    };

    CProgressBar *m_pProgressBar;

    QComboBox *m_pMachineComboBox;
    QList<QPushButton *> m_pMedianBtnList;
    QList<QPushButton *> m_pPCRBtnList;
    QList<QPushButton *> m_pFLBtnList;

    QTableWidget *m_pTableWidget;

    QLabel *m_pAppNameLabel, *m_pAppVersionLabel;
    QPushButton *m_pAppUpdateBtn;

    QLabel *m_pCalcNameLabel, *m_pCalcVersionLabel;
    QPushButton *m_pCalcUpdateBtn;

    QLabel *m_pAutoNameLabel, *m_pAutoVersionLabel;
    QPushButton *m_pAutoUpdateBtn;

    QLabel *m_pPowerNameLabel, *m_pPowerVersionLabel;
    QPushButton *m_pPowerQueryBtn;

    QLabel *m_pSystemNameLabel, *m_pSystemVerLabel;
    QPushButton *m_pSystemQueryBtn;

    QLabel *m_pBackgroundLabel;

    bool m_bShow;
    int m_iUiMachineID;
    int m_iUpdateType;
    QString m_strCurrentDir;

    QTimer *m_pUpdateTimer;
    int m_iOnePieceLen;
    QByteArray m_byteUpdateData;

    QList<SPLCVerStruct> m_sPLCVerList;
};

#endif // CSOFTUPDATE_H
