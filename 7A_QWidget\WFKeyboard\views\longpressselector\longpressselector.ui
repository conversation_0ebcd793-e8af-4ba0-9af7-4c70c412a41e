<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LongPressSelector</class>
 <widget class="QWidget" name="LongPressSelector">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>776</width>
    <height>35</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>4</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="KeyBoardToolButton" name="alternativekey_0">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>60</width>
       <height>35</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>24</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <widget class="KeyBoardToolButton" name="alternativekey_1">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>60</width>
       <height>35</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>24</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <widget class="KeyBoardToolButton" name="alternativekey_2">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>60</width>
       <height>35</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>24</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <widget class="KeyBoardToolButton" name="alternativekey_3">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>60</width>
       <height>35</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>24</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <widget class="KeyBoardToolButton" name="alternativekey_4">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>60</width>
       <height>35</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>24</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <widget class="KeyBoardToolButton" name="alternativekey_5">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>60</width>
       <height>35</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>24</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <widget class="KeyBoardToolButton" name="alternativekey_6">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>60</width>
       <height>35</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>24</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <widget class="KeyBoardToolButton" name="alternativekey_7">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>60</width>
       <height>35</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>24</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <widget class="KeyBoardToolButton" name="alternativekey_8">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>60</width>
       <height>35</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>24</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <widget class="KeyBoardToolButton" name="alternativekey_9">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>60</width>
       <height>35</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>文泉驿微米黑</family>
       <pointsize>24</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>KeyBoardToolButton</class>
   <extends>QToolButton</extends>
   <header>./common/keyboardtoolbutton/keyboardtoolbutton.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
