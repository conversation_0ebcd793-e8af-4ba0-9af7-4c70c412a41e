#include "CSelfTestHistoryWidget.h"
#include <QDebug>
#include <QBoxLayout>
#include <QHeaderView>
#include "CMessageBox.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "DBControl/CDevInfoDB.h"

CSelfTestHistoryWidget::CSelfTestHistoryWidget(QWidget *parent) : QWidget(parent)
{
    _InitWidget();
    _InitLayout();

    m_bRefresh = false;
    m_bShow = false;
    m_iOnePageLines = 10;
    m_iCurrentPage = 0;
    m_iTotalLines = CDevInfoDB::GetInstance().GetSelftestResultCounts();
    m_iTotalPages = m_iTotalLines / m_iOnePageLines;
    m_iLeftLines = m_iTotalLines % m_iOnePageLines;
    if(0 != m_iLeftLines)
        m_iTotalPages++;
    m_iCurrentPage = 0;
    _ShowCurrentPageData();
}

void CSelfTestHistoryWidget::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;
    m_iCurrentPage = 0;
    m_iTotalLines = CDevInfoDB::GetInstance().GetSelftestResultCounts();
    m_iTotalPages = m_iTotalLines / m_iOnePageLines;
    m_iLeftLines = m_iTotalLines % m_iOnePageLines;
    if(0 != m_iLeftLines)
        m_iTotalPages++;
    m_iCurrentPage = 0;
    _ShowCurrentPageData();
    QWidget::showEvent(pEvent);
}

void CSelfTestHistoryWidget::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;
    QWidget::hideEvent(pEvent);
}

void CSelfTestHistoryWidget::_ShowCurrentPageData()
{
    QList<QStringList> strDataList;
    CDevInfoDB::GetInstance().GetSelftestOnePageData(m_iCurrentPage, m_iOnePageLines, strDataList);
    _UpdateTableWidget(strDataList);
    _UpdatePageUI();
}

void CSelfTestHistoryWidget::_UpdateTableWidget(QList<QStringList> strDataList)
{
    QList<QPushButton*> qBtnList = m_pTableWidget->findChildren<QPushButton*>();
    for(int i=0; i<qBtnList.size(); i++)
    {
        QPushButton *pBtn = qBtnList.at(i);
        disconnect(pBtn, &QPushButton::clicked, this, &CSelfTestHistoryWidget::_SlotExportBtn);
        pBtn->deleteLater();
    }

    m_pTableWidget->clearContents();
    m_pTableWidget->setRowCount(strDataList.size());

    for(int iRow=0; iRow<strDataList.size(); iRow++)
    {
        QStringList strOneList = strDataList.at(iRow);
        //qDebug()<<Q_FUNC_INFO<<strOneList;
        if(strOneList.size() < 6)
            continue;

        int iMachineID = strOneList.at(1).toInt();
        strOneList[1] = QString("%1#").arg(iMachineID+1);

        if("0" == strOneList.at(2))
            strOneList[2] = tr("成功");
        else
            strOneList[2] = tr("失败");

        int iMin = qMin(strOneList.size(), 5);
        for(int iColumn=0; iColumn<iMin; iColumn++)
            _SetTableWidgetItem(iRow, iColumn, strOneList.at(iColumn));

        QPushButton *pBtn = new QPushButton(tr("导出"));
        pBtn->setFixedSize(100, 44);
        pBtn->setObjectName("ExportBtn");
        pBtn->setProperty("path", strOneList.at(5));
        connect(pBtn, &QPushButton::clicked, this, &CSelfTestHistoryWidget::_SlotExportBtn);

        QWidget *pWidget = new QWidget;
        QHBoxLayout *pLayout = new QHBoxLayout;
        pLayout->setMargin(0);
        pLayout->addStretch(1);
        pLayout->addWidget(pBtn, 0, Qt::AlignVCenter);
        pLayout->addStretch(1);
        pWidget->setLayout(pLayout);
        m_pTableWidget->setCellWidget(iRow, 5, pWidget);
    }
}

void CSelfTestHistoryWidget::_SetTableWidgetItem(int iRow, int iColumn, QString strText)
{
    QTableWidgetItem *pItem = new QTableWidgetItem;
    pItem->setText(strText);
    pItem->setTextAlignment(Qt::AlignCenter);
    m_pTableWidget->setItem(iRow, iColumn, pItem);
}

void CSelfTestHistoryWidget::_UpdatePageUI()
{
    if(0 == m_iTotalLines)
        m_pLinesLabel->clear();
    else
        m_pLinesLabel->setText(tr("总共%1条记录").arg(m_iTotalLines));

    m_pPageLabel->setText(QString("%1/%2").arg(m_iCurrentPage + 1).arg(m_iTotalPages));

    if(m_iCurrentPage <= 0)
    {
        m_pPrePageBtn->setEnabled(false);
        m_pNextPageBtn->setEnabled(true);
    }
    else if(m_iCurrentPage >= m_iTotalPages - 1)
    {
        m_pPrePageBtn->setEnabled(true);
        m_pNextPageBtn->setEnabled(false);
    }
    else
    {
        m_pPrePageBtn->setEnabled(true);
        m_pNextPageBtn->setEnabled(true);
    }

    if(m_iTotalLines <= m_iOnePageLines)
    {
        m_pPageLabel->setText("1/1");
        m_pPrePageBtn->setEnabled(false);
        m_pNextPageBtn->setEnabled(false);
    }
}

void CSelfTestHistoryWidget::_SlotGotoPageBtn()
{
    QString strPage = m_pGotoLineEdit->text();
    if(strPage.isEmpty())
    {
        ShowInformation(this, tr("提示"), tr("请输入要跳转到的页数"));
        return;
    }

    int iPage = strPage.toInt();
    if(iPage <= 0)
        iPage = 1;
    if(iPage >= m_iTotalPages)
        iPage = m_iTotalPages;
    m_pGotoLineEdit->setText(QString::number(iPage));

    m_iCurrentPage = iPage - 1;
    _ShowCurrentPageData();
}

void CSelfTestHistoryWidget::_SlotPrePageBtn()
{
    if(m_iCurrentPage <= 0)
        return;

    m_iCurrentPage--;
    _ShowCurrentPageData();
}

void CSelfTestHistoryWidget::_SlotNextPageBtn()
{
    if(m_iCurrentPage + 1 >= m_iTotalPages)
        return;

    m_iCurrentPage++;
    _ShowCurrentPageData();
}

void CSelfTestHistoryWidget::_SlotExportBtn()
{
    QPushButton *pBtn = dynamic_cast<QPushButton*>(sender());
    if(!pBtn)
        return;

    QString strExportDir = CPublicConfig::GetInstance()->GetUDiskExportDir();
    if(!UDiskExistAndCreateDir(strExportDir, this))
        return;

    QString strPath = pBtn->property("path").toString();
    qDebug()<<"导出自检表格:"<<strPath;
    bool bCopy = CopyQFileDir(strPath, QDir(strExportDir));
    if(bCopy)
        ShowSuccess(this, tr("提示"), tr("自检报告已导出"));
    else
        ShowInformation(this, tr("提示"), tr("自检报告导出失败，请检查U盘"));
}

void CSelfTestHistoryWidget::_InitWidget()
{
    m_pTableWidget = new QTableWidget;
    m_pTableWidget->setFixedSize(1636, 652);
    m_pTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pTableWidget->setSelectionMode(QAbstractItemView::NoSelection);
    m_pTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pTableWidget->setWordWrap(true);
    m_pTableWidget->setFocusPolicy(Qt::NoFocus);
    m_pTableWidget->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTableWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTableWidget->setAlternatingRowColors(true);
    m_pTableWidget->setShowGrid(false);

    QStringList strTitleList = {tr("序号"), tr("检测模块"), tr("结果"), tr("详情"), tr("日期"), tr("导出")};
    m_pTableWidget->setColumnCount(strTitleList.size());
    m_pTableWidget->setHorizontalHeaderLabels(strTitleList);
    m_pTableWidget->setRowCount(10);

    QHeaderView *pVerticalHeader = m_pTableWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(60);

    QHeaderView *pHorizontalHeader = m_pTableWidget->horizontalHeader();
    pHorizontalHeader->resizeSection(0, 100);
    pHorizontalHeader->setSectionResizeMode(1, QHeaderView::Stretch);
    pHorizontalHeader->setSectionResizeMode(2, QHeaderView::Stretch);
    pHorizontalHeader->setSectionResizeMode(3, QHeaderView::Stretch);
    pHorizontalHeader->setSectionResizeMode(4, QHeaderView::Stretch);
    pHorizontalHeader->setSectionResizeMode(5, QHeaderView::Stretch);
    pHorizontalHeader->setDisabled(true);

    m_pLinesLabel = new QLabel;
    m_pLinesLabel->setFixedHeight(40);
    m_pLinesLabel->setMinimumWidth(120);

    m_pGotoLabel1 = new QLabel(tr("跳转至"));
    m_pGotoLabel1->setObjectName("GotoLabel");

    m_pGotoLineEdit = new CLineEdit;
    m_pGotoLineEdit->setFixedSize(72, 40);
    m_pGotoLineEdit->setObjectName("GotoLineEdit");
    m_pGotoLineEdit->setAlignment(Qt::AlignCenter);

    m_pGotoLabel2 = new QLabel(tr("页"));
    m_pGotoLabel2->setObjectName("GotoLabel");

    m_pGotoBtn = new QPushButton(tr("跳转"));
    m_pGotoBtn->setFixedSize(72, 40);
    if(eLanguage_German == gk_iLanguage)
        m_pGotoBtn->setFixedSize(100, 40);
    m_pGotoBtn->setObjectName("GotoBtn");
    connect(m_pGotoBtn, &QPushButton::clicked, this, &CSelfTestHistoryWidget::_SlotGotoPageBtn);

    m_pPrePageBtn = new QPushButton;
    m_pPrePageBtn->setFixedSize(32, 32);
    m_pPrePageBtn->setObjectName("PrePageBtn");
    connect(m_pPrePageBtn, &QPushButton::clicked, this, &CSelfTestHistoryWidget::_SlotPrePageBtn);

    m_pPageLabel = new QLabel("0/0");
    m_pPageLabel->setObjectName("PageLabel");

    m_pNextPageBtn = new QPushButton;
    m_pNextPageBtn->setFixedSize(32, 32);
    m_pNextPageBtn->setObjectName("NextPageBtn");
    connect(m_pNextPageBtn, &QPushButton::clicked, this, &CSelfTestHistoryWidget::_SlotNextPageBtn);

    m_pReturnBtn = new QPushButton(tr("返回"));
    m_pReturnBtn->setFixedSize(150, 56);
    m_pReturnBtn->setObjectName("CancelBtn");
    connect(m_pReturnBtn, &QPushButton::clicked, this, &CSelfTestHistoryWidget::SignalReturn);
}

void CSelfTestHistoryWidget::_InitLayout()
{
    QHBoxLayout *pPageLayout = new QHBoxLayout;
    pPageLayout->setMargin(0);
    pPageLayout->setSpacing(12);
    pPageLayout->addStretch(1);
    pPageLayout->addWidget(m_pLinesLabel);
    pPageLayout->addSpacing(30);
    pPageLayout->addWidget(m_pGotoLabel1);
    pPageLayout->addWidget(m_pGotoLineEdit);
    pPageLayout->addWidget(m_pGotoLabel2);
    pPageLayout->addWidget(m_pGotoBtn);
    pPageLayout->addSpacing(20);
    pPageLayout->addWidget(m_pPrePageBtn);
    pPageLayout->addSpacing(4);
    pPageLayout->addWidget(m_pPageLabel);
    pPageLayout->addSpacing(4);
    pPageLayout->addWidget(m_pNextPageBtn);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pTableWidget, 0, Qt::AlignHCenter);
    pLayout->addSpacing(15);
    pLayout->addLayout(pPageLayout);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pReturnBtn, 0, Qt::AlignHCenter);
    this->setLayout(pLayout);
}
