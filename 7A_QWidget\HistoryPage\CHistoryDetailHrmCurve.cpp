#include "CHistoryDetailHrmCurve.h"
#include <QBoxLayout>

#include "CProjectDB.h"
#include "CHistoryDB.h"
#include "CLotInfoDB.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "CMessageBox.h"
#include "CCheckUserWidget.h"

CHistoryDetailHrmCurve::CHistoryDetailHrmCurve(bool bHistoryMode, QWidget *parent)
    : QWidget(parent)
    , m_bHistoryMode(bHistoryMode)
    , m_strPositive(tr("耐药型"))
    , m_strNegative(tr("敏感型"))
    , m_strError(tr("无效"))
    , m_strNull(tr("/"))
{

    m_strColorList << "#0000FF" << "#00FF00" << "#FFD700" << "#FF0000";
    m_strColorList << "#6859c9" << "#5da983" << "#FFDAB9" << "#A52A2A";

    m_strICNameList << "IC" << "SC" << "RNaseP" << "β-actin";
    m_StrChannelNameList << "FAM1" << "CY51"<< "HEX1"<< "ROX1"<< "FAM2"<< "CY52"<< "HEX2"<< "ROX2";

    _InitWidget();
    _InitLayout();
    LoadQSS(this, ":/qss/qss/history/detailHrm.qss");
}

void CHistoryDetailHrmCurve::ClearData()
{
    m_sResultInfo.Clear();
    m_sCardInfo.Clear();
    m_sLotInfo.Clear();
    m_sResultMeltingInfoList.clear();
    _SetManualBtn(m_sResultInfo.strReview == "y",m_sResultInfo);

    m_pPcrCustomPlot->clearGraphs();
    m_pPcrCustomPlot->yAxis->setRange(0, 2000);
    m_pPcrCustomPlot->replot();

    m_pHrmCustomPlot->clearGraphs();
    m_pHrmCustomPlot->yAxis->setRange(0, 2000);
    m_pHrmCustomPlot->replot();

    for(const auto& item :  m_pDotLineFirstList)
    {
        item->setVisible(false);
    }
    for(const auto& item : m_pDotLineSecondList)
    {
        item->setVisible(false);
    }

    m_pTableWidget->clearContents();
    m_pTestResultLabel->clear();
}

void CHistoryDetailHrmCurve::ClearFL()
{
    m_dFLMap.clear();
    m_sResultMeltingInfoList.clear();
    m_dMeltingFLMap.clear();
    m_dMeltingTempMap.clear();

}

void CHistoryDetailHrmCurve::closeBtnHandle()
{
    //复位
    m_bShow = false;

    if(CPublicConfig::GetInstance()->GetShowRawCurve())
    {
        m_pPcrRawRadioBtn->setChecked(true);
        m_pHrmRawRadioBtn->setChecked(true);
    }
    else
    {
        m_pPcrNmzaRadioBtn->setChecked(true);
        m_pHrmPeakRadioBtn->setChecked(true);
    }
}

void CHistoryDetailHrmCurve::SetReviewMode(bool bReview)
{
    if(bReview)
    {
        m_pPcrRawRadioBtn->setVisible(false);
        m_pHrmRawRadioBtn->setVisible(false);

        m_pReviewCalcBtn->setVisible(false);
        m_pReviewPassBtn->setVisible(false);
        m_pTableWidget->setColumnHidden(4, true);
    }
}

void CHistoryDetailHrmCurve::ThresholdLlineShow(bool bShow)
{
    m_threshouldCheckBox->setChecked(bShow);
    emit m_threshouldCheckBox->clicked(bShow);
}

void CHistoryDetailHrmCurve::ShowPCRNmzaData()
{
    m_pPcrRawRadioBtn->setChecked(false);
    m_pPcrNmzaRadioBtn->setChecked(true);
    m_pPcrCustomPlot->yAxis->setLabel(tr("荧光值(dR)"));
    _UpdatePcrNmzaPlot();
}

void CHistoryDetailHrmCurve::ShowHRMPeakData()
{
    m_pHrmRawRadioBtn->setChecked(false);
    m_pHrmPeakRadioBtn->setChecked(true);
    m_pHrmCustomPlot->yAxis->setLabel("-d(Rn)/dT");
    _UpdateHrmPeakPlot();
}

void CHistoryDetailHrmCurve::Reset2RawData()
{
    if(!CPublicConfig::GetInstance()->GetShowRawCurve())
       return;

    m_pPcrRawRadioBtn->setChecked(true);
    m_pPcrNmzaRadioBtn->setChecked(false);
    m_pPcrCustomPlot->yAxis->setLabel(tr("荧光值(R)"));
    _UpdatePcrRawPlot();

    m_pHoleACheckBoxList.at(0)->setChecked(true);
    emit m_pHoleACheckBoxList.at(0)->clicked(true);

    m_pHoleBCheckBoxList.at(0)->setChecked(true);
    emit m_pHoleBCheckBoxList.at(0)->clicked(true);
}

void CHistoryDetailHrmCurve::ShowRawData()
{
    if(!CPublicConfig::GetInstance()->GetShowRawCurve())
       return;

    m_pPcrRawRadioBtn->setChecked(true);
    m_pPcrNmzaRadioBtn->setChecked(false);
    m_pPcrCustomPlot->yAxis->setLabel(tr("荧光值(R)"));
    _UpdatePcrRawPlot();

    m_pHrmRawRadioBtn->setChecked(true);
    m_pHrmPeakRadioBtn->setChecked(false);
    m_pHrmCustomPlot->yAxis->setLabel(tr("荧光值(R)"));
    _UpdateHrmRawPlot();
}

void CHistoryDetailHrmCurve::SwitchTab(int iTab)
{
    m_pPlotStackWidget->setCurrentIndex(iTab);
    if(0 == iTab)
    {
        // 隐藏辅助线复选框
        m_threshouldCheckBox->setVisible(false);
        m_pTabBtnList.at(0)->setChecked(true);
        m_pTabBtnList.at(1)->setChecked(false);
        _ChannelCheckBoxHandle(m_pPcrCustomPlot);
    }
    if(1 == iTab)
    {
        // 显示辅助线复选框
        m_threshouldCheckBox->setVisible(m_pHrmPeakRadioBtn->isChecked());
        m_pTabBtnList.at(0)->setChecked(false);
        m_pTabBtnList.at(1)->setChecked(true);
        _ChannelCheckBoxHandle(m_pHrmCustomPlot);
    }
}

void CHistoryDetailHrmCurve::SwitchHole(int iHole)
{
    m_pPcrCustomPlot->yAxis->setLabel(tr("荧光值(R)"));
    //_UpdatePcrRawPlot();

    if(0 == iHole)
    {
        if(!m_pHoleBCheckBoxList.isEmpty())
        {
            m_pHoleACheckBoxList.at(0)->setChecked(true);
            emit m_pHoleACheckBoxList.at(0)->clicked(true);

            m_pHoleBCheckBoxList.at(0)->setChecked(false);
            emit m_pHoleBCheckBoxList.at(0)->clicked(false);
        }
    }
    else if(1 == iHole)
    {
        if(!m_pHoleACheckBoxList.isEmpty())
        {
            m_pHoleACheckBoxList.at(0)->setChecked(false);
            emit m_pHoleACheckBoxList.at(0)->clicked(false);

            m_pHoleBCheckBoxList.at(0)->setChecked(true);
            emit m_pHoleBCheckBoxList.at(0)->clicked(true);
        }
    }
    else
    {
        m_pHoleACheckBoxList.at(0)->setChecked(true);
        emit m_pHoleACheckBoxList.at(0)->clicked(true);

        m_pHoleBCheckBoxList.at(0)->setChecked(true);
        emit m_pHoleBCheckBoxList.at(0)->clicked(true);
    }
}

void CHistoryDetailHrmCurve::SetHistoryIDHandle(const SCardInfoStruct &sCardInfo, const SResultInfoStruct &sResultInfo, const SLotInfoStruct &sLotInfo)
{
    m_sResultInfo = sResultInfo;
    m_sCardInfo = sCardInfo;
    m_sLotInfo = sLotInfo;

    UpdateTableWidget(sResultInfo,sLotInfo);
    UpdateTestReusltLabel(sResultInfo,sLotInfo);

    if(!CPublicConfig::GetInstance()->GetShowRawCurve())
    {
        m_pHrmPeakRadioBtn->setChecked(true);
        _UpdateHrmPeakPlot();

        m_pPcrNmzaRadioBtn->setChecked(true);
        _UpdatePcrNmzaPlot();

        m_pTableWidget->setColumnHidden(4, true);

        return;
    }

    if(m_pHrmRawRadioBtn->isChecked())
        _UpdateHrmRawPlot();
    if(m_pHrmPeakRadioBtn->isChecked())
        _UpdateHrmPeakPlot();

    if(m_pPcrRawRadioBtn->isChecked())
        _UpdatePcrRawPlot();
    if(m_pPcrNmzaRadioBtn->isChecked())
        _UpdatePcrNmzaPlot();

    m_pTableWidget->setColumnHidden(4, false);
}

void CHistoryDetailHrmCurve::UpdateTestReusltLabel(const SResultInfoStruct &sResultInfo, const SLotInfoStruct &sLotInfo)
{
    Q_UNUSED(sLotInfo);
    _SetManualBtn(m_sResultInfo.strReview == "y",sResultInfo);
    return;

}

void CHistoryDetailHrmCurve::UpdateTableWidget(const SResultInfoStruct &sResultInfo, const SLotInfoStruct &sLotInfo)
{
    QStringList strNameList = sLotInfo.strCurveName.split(";");
    QStringList strWildTmThresholdList = sLotInfo.strWildTypeTmValue.split(";");
    QStringList strTempRangeValue =  sLotInfo.strTempRangeValue.split(";");
    QStringList strTmRangeList = sLotInfo.strTmRange.split(";");
    QStringList strRmThresholdList = sLotInfo.strRmThreshold.split(";");
    QStringList strCtResultList = sResultInfo.strResult.split(";");
    QStringList strCtInfoList = sResultInfo.strCTInfo.split(";");

    QStringList strMeltingInfoList = sResultInfo.strMeltingInfo.split(";");


    //QStringList strResultList = sResultInfo.strHrmResult.split(";");

    // result: "p;p;p;p;p;p;p;p";
    // info: Tm1-Rm1,Tm2-Rm2,yuzhi,fuduzhi;
    // info-review: Tm1-Rm1,Tm2-Rm2,yuzhi,fuduzhi;
    // result-review: "p;/;p;p;p;p;p;p";

    //QStringList strResultListReview = sResultInfo.strHrmResult_Review.split(";");

    QStringList strMeltingInfoListReview = sResultInfo.strMeltingInfo_Review.split(";");
    QString strReview = sResultInfo.strReview;
    m_pTableWidget->setRowCount(strNameList.size());


    QList<sResultMeltingInfo> sResultMeltingInfoList;

    for(int i=0; i<strNameList.size(); i++)
    {
        sResultMeltingInfo sResultMeltingInfo;
        sResultMeltingInfo.m_index = i;
        sResultMeltingInfo.m_strCurveName = strNameList.at(i);
        sResultMeltingInfo.m_strTestMode = sResultInfo.strMode;
        if("0" == strNameList.at(i) || strNameList.at(i).isEmpty())
        {
            sResultMeltingInfo.m_bNull = true;
            sResultMeltingInfoList.push_back(sResultMeltingInfo);
            continue;
        }

        if(IsCtrlTarget(strNameList.at(i)))
        {
            sResultMeltingInfo.m_bControl = true;
        }
        bool bShowCtInfo = false;
        if(bMeltingResultShowCtInfo(sLotInfo.strProjectShowName,strNameList,i)
                || sResultMeltingInfo.m_bControl)
        {
            bShowCtInfo = true;
        }
        QString strThresText = "/";

        if(i < strWildTmThresholdList.size() && i < strTempRangeValue.size())
        {
            QString strTem = strWildTmThresholdList.at(i).trimmed();
            QString strTemRange = strTempRangeValue.at(i).trimmed();
            if(!strTem.isEmpty())
            {
                bool bOk = false;
                float fTm = strTem.toFloat(&bOk);
                if(bOk && fTm >= 10)
                {
                    float fRange = strTemRange.toFloat();
                    strThresText = QString("%1<=Tm<=%2").arg(fTm-fRange).arg(fTm+fRange);
                }
            }
        }
        sResultMeltingInfo.m_strTempRangeValue = bShowCtInfo ? "/" : strThresText;
        sResultMeltingInfo.m_bshowCt = bShowCtInfo;
        QString strResultText = "/";
        //bool bPositive = false;
        bool bReview = false;

        if((strReview == "m" || strReview == "y")
                && i < strMeltingInfoListReview.size())
        {
            QString meltingReviewInfo = strMeltingInfoListReview.at(i);
            // 如果审核了选review的meltingInfo；
            if(!meltingReviewInfo.trimmed().isEmpty())
            {
                bReview = true;
            }
        }

        sResultMeltingInfo.m_strReview = bReview?QString(tr("已审核")):"/";

        //QString stTmList = "/";

        QString strMeltInfo;
        QStringList strTmInfoList,strRmInfoList,strYmInfoList;
        if(bReview && i < strMeltingInfoListReview.size())
        {
            strMeltInfo = strMeltingInfoListReview.at(i);
        }
        else if(i < strMeltingInfoList.size())
        {
            strMeltInfo = strMeltingInfoList.at(i);
        }

        QString strThreashould;
        _GetTmFormMeltInfo(strMeltInfo,strTmInfoList,strRmInfoList,strYmInfoList,strThreashould);

        sResultMeltingInfo.m_strTmValues = bShowCtInfo ? "/" : strTmInfoList.join(",");
        sResultMeltingInfo.m_strYmValues = bShowCtInfo ? "" : strYmInfoList.join(",");

        //解析Ct 所有Ct

        QStringList strCtList;
        QString strCtResult = "/";
        if(i < strCtInfoList.size() && i < strCtResultList.size())
        {
            strCtList =  strCtInfoList.at(i).split(",");
            strCtResult = strCtResultList.at(i);
        }

        sResultMeltingInfo.m_strCtResult = strCtResult;

        sResultMeltingInfo.m_strCtValues = "/";
        if(strCtList.size() >= 4 && bShowCtInfo)
        {            
            if(bHrmTDTecType(sResultInfo.iTestProject))
            {
                float ctValue =  strCtList.at(2).toFloat()+10;
                if(strCtList.at(2).toFloat() >= 2)
                {
                    sResultMeltingInfo.m_strCtValues = strCtList.at(2)+QString("(%1)").arg(ctValue);
                }
                else
                {
                    sResultMeltingInfo.m_strCtValues = strCtList.at(2).toFloat() < 0 ? "0" : strCtList.at(2);
                }

            }
            else
            {
                sResultMeltingInfo.m_strCtValues = strCtList.at(2).toFloat() < 0 ? "0" : strCtList.at(2);
            }
        }
        sResultMeltingInfoList.push_back(sResultMeltingInfo);
    }

    m_sResultMeltingInfoList = sResultMeltingInfoList;
    {
        QList<sResultMeltingInfo> trueItems;
        QList<sResultMeltingInfo> falseItems;
        for (const auto& item : sResultMeltingInfoList) {
            if (item.m_bControl)
            {
                trueItems.append(item);
            } else if(!item.m_bNull)
            {
                falseItems.append(item);
            }
        }
        sResultMeltingInfoList = falseItems + trueItems;
    }
    UpdateTableWidgetHandle(m_pTableWidget,sResultMeltingInfoList);
    // 现在要求是测试结果，测试结果放哪里？多国语言怎么办？
    bool bReview = (strReview == "m" || strReview == "y");
    _UpdateTestResultLabel(bReview ? sResultInfo.strHrmResult_Review : sResultInfo.strHrmResult);
}

void CHistoryDetailHrmCurve::UpdateInfo(const SResultInfoStruct &sResult)
{
    m_sResultInfo = sResult;
    UpdateTestReusltLabel(m_sResultInfo,m_sLotInfo);
    UpdateTableWidget(m_sResultInfo,m_sLotInfo);
}

void CHistoryDetailHrmCurve::SetFLDataMap(const QList<QMap<double, double> > &dFLMap)
{
    m_dFLMap = dFLMap;
    if(m_bHistoryMode)
        return;

    if(!CPublicConfig::GetInstance()->GetShowRawCurve())
        return;

    if(m_pPcrRawRadioBtn->isChecked())
    {
        QList<QList<double>> dFLDataList;
        for(int i=0; i<m_dFLMap.size(); i++)
            dFLDataList << m_dFLMap.at(i).values();

        _SetPlotData(dFLDataList);
    }
}

void CHistoryDetailHrmCurve::SetFLMeltingDataMap(const QList<double> &dTempList, const QList<QMap<double, double> > &dFLMap)
{

    m_dMeltingTempMap = dTempList;
    m_dMeltingFLMap = dFLMap;

    if(m_bHistoryMode)
        return;

    if(m_pHrmRawRadioBtn->isChecked())
    {
        QList<QList<double>> dFLDataList;
        for(int i=0; i<m_dMeltingFLMap.size(); i++)
            dFLDataList << m_dMeltingFLMap.at(i).values();

        QList<double> realTempVec;
        for(int i=0; i<m_dMeltingTempMap.size(); i++)
            realTempVec.push_back(m_dMeltingTempMap.at(i) / 100.0);

        _SetPlotData(realTempVec,dFLDataList);
    }
}

void CHistoryDetailHrmCurve::paintEvent(QPaintEvent *pEvent)
{
    QWidget::paintEvent(pEvent);
}

void CHistoryDetailHrmCurve::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;

    int index = m_pPlotStackWidget->currentIndex();
    if(0 == index)
    {
        m_pPcrCustomPlot->replot();
    }
    if(1 == index)
    {
        m_pHrmCustomPlot->replot();
    }

    if(!CPublicConfig::GetInstance()->GetShowRawCurve())
    {
        m_pPcrRawRadioBtn->setChecked(false);
        m_pPcrRawRadioBtn->setVisible(false);
        m_pPcrNmzaRadioBtn->setChecked(true);
        m_pPcrNmzaRadioBtn->setVisible(true);

        m_pHrmRawRadioBtn->setChecked(false);
        m_pHrmRawRadioBtn->setVisible(false);
        m_pHrmPeakRadioBtn->setChecked(true);
        m_pHrmPeakRadioBtn->setVisible(true);

        m_pReviewCalcBtn->setVisible(false);
        m_pReviewPassBtn->setVisible(false);

        m_pTableWidget->setColumnHidden(4, true);
    }
    else
    {
        m_pTableWidget->setColumnHidden(4, false);
    }

    QWidget::showEvent(pEvent);
}

void CHistoryDetailHrmCurve::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;
    m_pManualReviewWidget->close();
    QWidget::hideEvent(pEvent);
}

void CHistoryDetailHrmCurve::_SlotPcrRawRadioBtn()
{
    if(m_bShow)
    {
        m_pPcrCustomPlot->yAxis->setLabel(tr("荧光值(R)"));
        _UpdatePcrRawPlot();
    }
}

void CHistoryDetailHrmCurve::_SlotPcrNmzaRadioBtn()
{
    if(m_bShow)
    {
        m_pPcrCustomPlot->yAxis->setLabel(tr("荧光值(dR)"));
        _UpdatePcrNmzaPlot();
    }
}


void CHistoryDetailHrmCurve::_SlotHrmPeakRadioBtn()
{
    if(m_bShow)
    {
        m_pHrmCustomPlot->yAxis->setLabel(tr("-d(Rn)/dT"));
        UpdateTableWidget(m_sResultInfo,m_sLotInfo);
        _UpdateHrmPeakPlot();
    }
}

void CHistoryDetailHrmCurve::_SlotHrmRawRadioBtn()
{
    if(m_bShow)
    {
        m_pHrmCustomPlot->yAxis->setLabel(tr("荧光值(R)"));
        _UpdateHrmRawPlot();
    }
}



void CHistoryDetailHrmCurve::_SlotChannelCheckBox()
{
    QCheckBox * pCheckBox = dynamic_cast<QCheckBox*>(sender());
    if(nullptr == pCheckBox)
    {
        return;
    }

    int index = pCheckBox->property("index").toInt();
    QString  strHole = pCheckBox->property("hole").toString();
    bool bCheck = pCheckBox->isChecked();
    if(strHole == "A")
    {
        if(0 == index)
        {
            for(int i=1; i<m_pHoleACheckBoxList.size(); i++)
            {
                m_pHoleACheckBoxList.at(i)->setChecked(bCheck);
            }
        }
        else{
            if(bCheck)
            {
                bool bAllCheck = true;
                for(int i=1; i<m_pHoleACheckBoxList.size(); i++)
                {
                    if(!m_pHoleACheckBoxList.at(i)->isChecked())
                    {
                        bAllCheck = false;
                        break;
                    }
                }
                m_pHoleACheckBoxList.at(0)->setChecked(bAllCheck);
            }
            else
            {
                m_pHoleACheckBoxList.at(0)->setChecked(bCheck);
            }
        }
    }
    else if(strHole == "B")
    {
        if(0 == index)
        {
            for(int i=1; i<m_pHoleBCheckBoxList.size(); i++)
            {
                m_pHoleBCheckBoxList.at(i)->setChecked(bCheck);
            }
        }
        else{

            if(bCheck)
            {
                bool bAllCheck = true;
                for(int i=1; i<m_pHoleBCheckBoxList.size(); i++)
                {
                    if(!m_pHoleBCheckBoxList.at(i)->isChecked())
                    {
                        bAllCheck = false;
                        break;
                    }
                }
                m_pHoleBCheckBoxList.at(0)->setChecked(bAllCheck);
            }
            else
            {
                m_pHoleBCheckBoxList.at(0)->setChecked(bCheck);
            }
        }
    }

    int pPlotindex = m_pPlotStackWidget->currentIndex();
    if(0 == pPlotindex)
    {
        _ChannelCheckBoxHandle(m_pPcrCustomPlot);
    }
    if(1 == pPlotindex)
    {
        _ChannelCheckBoxHandle(m_pHrmCustomPlot);
    }

}
void CHistoryDetailHrmCurve::_SlotTabBtn()
{
    QPushButton *pBtn = dynamic_cast<QPushButton *>(sender());
    if(nullptr == pBtn)
        return;

    int index = pBtn->property("index").toInt();
    m_pPlotStackWidget->setCurrentIndex(index);
    if(0 == index)
    {
        // 隐藏辅助线复选框
        m_threshouldCheckBox->setVisible(false);
        _ChannelCheckBoxHandle(m_pPcrCustomPlot);
    }
    if(1 == index)
    {
        // 显示辅助线复选框
        m_threshouldCheckBox->setVisible(m_pHrmPeakRadioBtn->isChecked());
        _ChannelCheckBoxHandle(m_pHrmCustomPlot);
    }

}

void CHistoryDetailHrmCurve::_SlotReviewBtn()
{
    static QElapsedTimer timer;
    if(timer.isValid() && timer.elapsed() < 500)
        return;
    timer.start();

    int nIndex = m_pTableWidget->currentRow();
    if(nIndex < 0 || nIndex > 7)
    {
        nIndex = 0;
    }

    QWidget *pNameWidget = m_pTableWidget->cellWidget( nIndex, 0);
    if (!pNameWidget)
    {
        return;
    }
    QLabel *label = qobject_cast<QLabel*>(pNameWidget);
    if(!label)
    {
        return;
    }

    //if(m_pManualReviewWidget->isVisible())
    //    m_pManualReviewWidget->close();
    //m_pManualReviewWidget->showNormal();
    m_pManualReviewWidget->SetManualReviewParam(m_sResultInfo,m_sLotInfo,m_sCardInfo, label->text());
    m_pManualReviewWidget->GetManualReviewParam(m_sResultInfo,m_sLotInfo);
    if(m_sResultInfo.strReview == "m")
    {
        if(m_pHrmPeakRadioBtn->isChecked() && m_pPlotStackWidget->currentIndex() == 1)
        {
            UpdateTableWidget(m_sResultInfo,m_sLotInfo);
            _UpdateHrmPeakPlot();
        }

    }
}

void CHistoryDetailHrmCurve::_SlotReviewConfirmBtn()
{
    static QElapsedTimer timer;
    if(timer.isValid() && timer.elapsed() < 500)
        return;
    timer.start();

    bool bReviewConfirm = m_sResultInfo.strReview == "y";
    if(!bReviewConfirm && m_sResultInfo.strReview != "m")
    {
        ShowWarning(nullptr,QString(tr("人工审核")),QString(tr("未改动人工审核")));
        qDebug()<<Q_FUNC_INFO<<"审核结果确认:"<<"未改动审核结果: "<<m_sResultInfo.strReview<<"审核结果:"<<m_sResultInfo.strResult_Review;
        return;
    }

    bool bCheckUser = ShowCheckUser(tr("身份确认"));
    if(false == bCheckUser)
        return;

    if(bReviewConfirm)
    {
        m_sResultInfo.strReview = "n";
        m_sResultInfo.strHrmResult_Review.clear();
        m_sResultInfo.strMeltingInfo_Review.clear();
        UpdateTableWidget(m_sResultInfo,m_sLotInfo);
        qDebug()<<Q_FUNC_INFO<<"审核结果撤销:"<<"自动计算info: "<<m_sResultInfo.strMeltingInfo<<"结果:"<<m_sResultInfo.strHrmResult;
    }
    else
    {
        m_sResultInfo.strReview = "y";
        qDebug()<<Q_FUNC_INFO<<"审核结果确认:"<<"审核info: "<<m_sResultInfo.strMeltingInfo_Review<<"结果:"<<m_sResultInfo.strHrmResult_Review;
    }
    _SetManualBtn(m_sResultInfo.strReview == "y",m_sResultInfo);
    CProjectDB::GetInstance()->UpdateHistoryReviewData(m_sResultInfo);

}

void CHistoryDetailHrmCurve::_SlotReviewConfirm(stHrmReviewParam stReviewParam)
{
    _ChangeTableResult(m_pTableWidget,stReviewParam);
    qDebug()<<Q_FUNC_INFO<<"人工审核应用:"<<stReviewParam.m_strName;
}

void CHistoryDetailHrmCurve::UpdateTableWidgetHandle(QTableWidget *pTableWidget, QList<CHistoryDetailHrmCurve::sResultMeltingInfo> &sInfolist)
{
    int nSize = sInfolist.size();
    pTableWidget->setRowCount(nSize);
    int iTabHeight = 70 + nSize * 68;
    pTableWidget->setFixedSize(580,iTabHeight);

    for(int i = 0 ; i < nSize; i++)
    {
        QWidget *pNameWidget = m_pTableWidget->cellWidget(i, 0);
        if(pNameWidget)
        {
            QLabel *label = qobject_cast<QLabel*>(pNameWidget);
            if (label) {
                label->setText(sInfolist.at(i).m_strCurveName);
                label->repaint();
            };
        }
        else
        {
            QLabel *pNameLabel = new QLabel;
            pNameLabel->setText(sInfolist.at(i).m_strCurveName);
            pNameLabel->setAlignment(Qt::AlignCenter);
            pNameLabel->setObjectName("TableReslutLabel");
            pNameLabel->setWordWrap(true); // 启用换行
            m_pTableWidget->setCellWidget(i, 0, pNameLabel);
        }
        QTableWidgetItem *pThresItem = new QTableWidgetItem;
        pThresItem->setText(sInfolist.at(i).m_strTempRangeValue);
        pThresItem->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(i, 1, pThresItem);



        QTableWidgetItem *pTmValue = new QTableWidgetItem;
        pTmValue->setText(sInfolist.at(i).m_strTmValues);
        pTmValue->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(i, 2, pTmValue);

        QTableWidgetItem *pCTValue = new QTableWidgetItem;
        pCTValue->setText(sInfolist.at(i).m_strCtValues);
        pCTValue->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(i, 3, pCTValue);

        QWidget *pReviewWidget = m_pTableWidget->cellWidget(i, 4);
        if (pReviewWidget)
        {
            QLabel *label = qobject_cast<QLabel*>(pReviewWidget);
            if (label) {
                label->setText(sInfolist.at(i).m_strReview);
                label->repaint();
            };
        }
        else
        {
            QLabel* pResultReviewItemLabel = new QLabel;
            pResultReviewItemLabel->setText(sInfolist.at(i).m_strReview);
            pResultReviewItemLabel->setAlignment(Qt::AlignCenter);
            pResultReviewItemLabel->setObjectName("TableReslutLabel");
            m_pTableWidget->setCellWidget(i, 4 , pResultReviewItemLabel);
        }
    }
}


void CHistoryDetailHrmCurve::_InitWidget()
{

    _InitTableWidget();
    _InitTabBtnWidget();
    _InitPcrDataWidget();
    _InitHrmDataWidget();

    m_pReviewCalcBtn = new QPushButton(tr("人工审核"));
    m_pReviewCalcBtn->setFixedSize(150, 50);
    if(eLanguage_English == gk_iLanguage)
    {
        //m_pThreholdLineEdit->setFixedSize(110, 50);
        m_pReviewCalcBtn->setFixedSize(170, 50);
    }
    connect(m_pReviewCalcBtn,&QPushButton::clicked, this, &CHistoryDetailHrmCurve::_SlotReviewBtn);
    m_pReviewCalcBtn->setVisible(false);

    m_pReviewPassBtn = new QPushButton(tr("审核通过"));
    m_pReviewPassBtn->setFixedSize(150, 50);
    connect(m_pReviewPassBtn,&QPushButton::clicked, this, &CHistoryDetailHrmCurve::_SlotReviewConfirmBtn);
    m_pReviewPassBtn->setVisible(false);

    m_pManualReviewWidget = new CManualHrmReviewWidget;
    connect(m_pManualReviewWidget,&CManualHrmReviewWidget::SignalReviewConfirm,this,&CHistoryDetailHrmCurve::_SlotReviewConfirm);


    m_pTestResultLabel = new QLabel(this);
    m_pTestResultLabel->setFixedSize(540,80);
    m_pTestResultLabel->setObjectName("ProjectResultLabel");
    m_pTestResultLabel->setAlignment(Qt::AlignCenter);
    m_pTestResultLabel->setWordWrap(true); // 启用换行
}

void CHistoryDetailHrmCurve::_InitLayout()
{
    QHBoxLayout *pCalcLayout = new QHBoxLayout;
    pCalcLayout->setMargin(30);
    pCalcLayout->setSpacing(30);

    pCalcLayout->addWidget(m_pReviewCalcBtn);
    pCalcLayout->addStretch(1);
    pCalcLayout->addWidget(m_pReviewPassBtn);

    QVBoxLayout *pReviewLayout = new QVBoxLayout;
    pReviewLayout->setMargin(0);
    pReviewLayout->setSpacing(0);
    pReviewLayout->addWidget(m_pTableWidget);
    pReviewLayout->addSpacing(50);
    pReviewLayout->addWidget(m_pTestResultLabel, 0, Qt::AlignHCenter);
    pReviewLayout->addSpacing(50);
    pReviewLayout->addLayout(pCalcLayout);
    pReviewLayout->addStretch(1);



    m_pPlotStackWidget = new QStackedWidget();

    QHBoxLayout *pHrmRadioBtnLayout = new QHBoxLayout;
    pHrmRadioBtnLayout->setMargin(0);
    pHrmRadioBtnLayout->setSpacing(12);
    pHrmRadioBtnLayout->addStretch(1);
    pHrmRadioBtnLayout->addWidget(m_pHrmRawRadioBtn);
    pHrmRadioBtnLayout->addSpacing(60);
    pHrmRadioBtnLayout->addWidget(m_pHrmPeakRadioBtn);
    pHrmRadioBtnLayout->addStretch(1);

    QVBoxLayout *pHrmPlotLayout = new QVBoxLayout;
    pHrmPlotLayout->setMargin(0);
    pHrmPlotLayout->setSpacing(10);
    pHrmPlotLayout->addLayout(pHrmRadioBtnLayout);
    pHrmPlotLayout->addWidget(m_pHrmCustomPlot);

    QHBoxLayout *pPcrRadioBtnLayout = new QHBoxLayout;
    pPcrRadioBtnLayout->setMargin(0);
    pPcrRadioBtnLayout->setSpacing(12);
    pPcrRadioBtnLayout->addStretch(1);
    pPcrRadioBtnLayout->addWidget(m_pPcrRawRadioBtn);
    pPcrRadioBtnLayout->addSpacing(60);
    pPcrRadioBtnLayout->addWidget(m_pPcrNmzaRadioBtn);
    pPcrRadioBtnLayout->addStretch(1);

    QVBoxLayout *pPcrPlotLayout = new QVBoxLayout;
    pPcrPlotLayout->setMargin(0);
    pPcrPlotLayout->setSpacing(10);
    pPcrPlotLayout->addLayout(pPcrRadioBtnLayout);
    pPcrPlotLayout->addWidget(m_pPcrCustomPlot);
    m_pHrmWidget = new QWidget;
    m_pPcrWidget = new QWidget;
    m_pHrmWidget->setLayout(pHrmPlotLayout);
    m_pPcrWidget->setLayout(pPcrPlotLayout);
    m_pPlotStackWidget->addWidget(m_pPcrWidget);
    m_pPlotStackWidget->addWidget(m_pHrmWidget);



    QVBoxLayout * pPoltLayout = new QVBoxLayout;
    pPoltLayout->setMargin(0);
    pPoltLayout->setSpacing(10);
    pPoltLayout->addWidget(m_pTopTabBackgroundLabel, 0, Qt::AlignHCenter);
    pPoltLayout->addWidget(m_pPlotStackWidget);
    pPoltLayout->addWidget(_InitChannelWidget());

    QHBoxLayout * pDataLayout = new QHBoxLayout;
    pDataLayout->setMargin(0);
    pDataLayout->setSpacing(0);
    pDataLayout->addLayout(pPoltLayout);
    pDataLayout->addStretch(1);
    pDataLayout->addLayout(pReviewLayout);


    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(12);
    //pLayout->addWidget(m_pTestResultLabel, 0, Qt::AlignHCenter);
    pLayout->addLayout(pDataLayout);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}

void CHistoryDetailHrmCurve::_InitTableWidget()
{
    m_pTableWidget = new QTableWidget;
    m_pTableWidget->setFixedSize(580, 614);
    m_pTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pTableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_pTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pTableWidget->setWordWrap(true);
    m_pTableWidget->setFocusPolicy(Qt::NoFocus);
    m_pTableWidget->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTableWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTableWidget->setAlternatingRowColors(true);
    m_pTableWidget->setShowGrid(false);

    QStringList strTitleList = {tr("靶标"), tr("参考值"), tr("Tm"), tr("Ct"), tr("审核")};
    m_pTableWidget->setColumnCount(strTitleList.size());
    m_pTableWidget->setHorizontalHeaderLabels(strTitleList);
    m_pTableWidget->setRowCount(8);

    QHeaderView* pVerticalHeader = m_pTableWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(68);

    QHeaderView* pHorizontalHeader = m_pTableWidget->horizontalHeader();
    pHorizontalHeader->setSectionResizeMode(0, QHeaderView::Stretch);
    pHorizontalHeader->resizeSection(1, 170);
    //pHorizontalHeader->setSectionResizeMode(1, QHeaderView::Stretch);
    pHorizontalHeader->resizeSection(2, 125);
    pHorizontalHeader->resizeSection(3, 110);
    pHorizontalHeader->resizeSection(4, 70);

    if(eLanguage_Italian == gk_iLanguage)
    {
        pHorizontalHeader->resizeSection(2, 450);
        pHorizontalHeader->resizeSection(3, 65);
        pHorizontalHeader->resizeSection(4, 100);
    }
    pHorizontalHeader->setDisabled(true);

}


void CHistoryDetailHrmCurve::_ChannelCheckBoxHandle(QCustomPlot* pCustomPlot)
{
    // 读取有哪些选中了 进行显示曲线  图例显示 虚线显示
    QList<QList<double> > dFLDataList;
    QList<QList<double> > dXDataList;
    int nSize = qMin(m_pHoleACheckBoxList.size(),pCustomPlot->graphCount()+1);
    int pPlotindex = m_pPlotStackWidget->currentIndex();

    for(int i = 1 ; i < nSize ; i++)
    {
        bool bShowDotLine = false;
        if(!m_pHoleACheckBoxList.at(i)->isChecked()  || pCustomPlot->graph(i-1)->name() == "0")
        {
            pCustomPlot->graph(i-1)->setVisible(false);
            //pCustomPlot->legend->item(i-1)->setVisible(false); // 隐藏对应的图例项
            bShowDotLine = false;

        }
        else
        {
            QList<double> tempValueList;
            QList<double> tempKeyList;
            QSharedPointer<QCPGraphDataContainer> data = pCustomPlot->graph(i-1)->data();
            for (auto it = data->constBegin(); it != data->constEnd(); ++it) {
                double key = it->key;
                double value = it->value;
                tempValueList.append(value);
                tempKeyList.append(key);
            }
            dFLDataList.push_back(tempValueList);
            dXDataList.push_back(tempKeyList);
            pCustomPlot->graph(i-1)->setVisible(true);
            //pCustomPlot->legend->item(i-1)->setVisible(false);
            bShowDotLine = m_threshouldCheckBox->isChecked()&&m_pHrmPeakRadioBtn->isChecked()&&pPlotindex==1;
        }
        m_pDotLineFirstList.at(i-1)->setVisible(bShowDotLine);
        m_pDotLineSecondList.at(i-1)->setVisible(bShowDotLine);
    }

    for(int i = 1; i < nSize; i++)
    {
        int index = i-1+4;
        bool bShowDotLine = false;
        if(index >= pCustomPlot->graphCount())
        {
            // 全部隐藏
            for(int i = 0 ; i < m_pHoleBCheckBoxList.size() ; i++)
            {
                m_pHoleBCheckBoxList.at(i)->setVisible(false);
            }
            break;
        }
        else
        {
            m_pHoleBCheckBoxList.at(0)->setVisible(true);
        }
        if(!m_pHoleBCheckBoxList.at(i)->isChecked() || pCustomPlot->graph(index)->name() == "0")
        {
            pCustomPlot->graph(index)->setVisible(false);
            //pCustomPlot->legend->item(index)->setVisible(false); // 隐藏对应的图例项
            bShowDotLine = false;

        }
        else
        {
            QList<double> tempValueList;
            QList<double> tempKeyList;
            QSharedPointer<QCPGraphDataContainer> data = pCustomPlot->graph(index)->data();
            for (auto it = data->constBegin(); it != data->constEnd(); ++it) {
                double key = it->key;
                double value = it->value;
                tempValueList.append(value);
                tempKeyList.append(key);
            }
            dFLDataList.push_back(tempValueList);
            dXDataList.push_back(tempKeyList);
            pCustomPlot->graph(index)->setVisible(true);
            //pCustomPlot->legend->item(index)->setVisible(false); // 隐藏对应的图例项
            bShowDotLine = m_threshouldCheckBox->isChecked()&&m_pHrmPeakRadioBtn->isChecked()&&pPlotindex==1;
        }
        m_pDotLineFirstList.at(index)->setVisible(bShowDotLine);
        m_pDotLineSecondList.at(index)->setVisible(bShowDotLine);
    }
    _ResetYRang(pCustomPlot,dXDataList,dFLDataList);
    _SetThreshouldLineValue();
    pCustomPlot->replot();
}

void CHistoryDetailHrmCurve::_InitTabBtnWidget()
{
    m_pTopTabBackgroundLabel = new QLabel;
    m_pTopTabBackgroundLabel->setObjectName("TopBackgroundLabel");

    QStringList strTextList = {tr("扩增曲线"), tr("熔解曲线")};

    int iBtnWidth = 256;
    if(eLanguage_Spanish == gk_iLanguage || eLanguage_Italian == gk_iLanguage)
        iBtnWidth = 276;

    m_pTopTabBackgroundLabel->setFixedSize(iBtnWidth*2, 50);

    for(int i = 0 ; i < strTextList.size(); i++)
    {
        QPushButton* pBtn = new QPushButton(strTextList.at(i));
        pBtn->setProperty("index",i);
        pBtn->setFixedSize(iBtnWidth,50);
        pBtn->setObjectName("tabBtn");
        pBtn->setCheckable(true);
        pBtn->setAutoExclusive(true);
        if(i == 0)
        {
            pBtn->setChecked(true);
        }
        connect(pBtn, &QPushButton::clicked, this, &CHistoryDetailHrmCurve::_SlotTabBtn);
        m_pTabBtnList.push_back(pBtn);
    }


    QHBoxLayout *pTabButtonLayout = new QHBoxLayout;
    pTabButtonLayout->setMargin(0);
    pTabButtonLayout->setSpacing(0);
    pTabButtonLayout->addStretch(1);
    for(const auto& item : m_pTabBtnList)
    {
        pTabButtonLayout->addWidget(item);
    }
    pTabButtonLayout->addStretch(1);
    m_pTopTabBackgroundLabel->setLayout(pTabButtonLayout);

}

void CHistoryDetailHrmCurve::_InitPcrDataWidget()
{
    m_pPcrCustomPlot = new QCustomPlot;
    m_pPcrCustomPlot->setFixedSize(900, 640);

    QFont font;
    font.setPixelSize(18);
    m_pPcrCustomPlot->legend->setFont(font);
    m_pPcrCustomPlot->legend->setSelectedFont(font);
    m_pPcrCustomPlot->legend->setVisible(false);
    m_pPcrCustomPlot->legend->setSelectableParts(QCPLegend::spItems);
    m_pPcrCustomPlot->legend->setBorderPen(Qt::NoPen);
    m_pPcrCustomPlot->legend->setWrap(4);
    m_pPcrCustomPlot->legend->setFillOrder(QCPLegend::foColumnsFirst);

    m_pPcrCustomPlot->axisRect()->insetLayout()->setInsetAlignment(0, Qt::AlignTop);

    m_pPcrCustomPlot->xAxis->setLabel(tr("循环数"));
    m_pPcrCustomPlot->yAxis->setLabel(tr("荧光值(R)"));

    m_pPcrCustomPlot->xAxis->setLabelFont(font);
    m_pPcrCustomPlot->yAxis->setLabelFont(font);

    m_pPcrCustomPlot->xAxis->setRange(0, 45);
    m_pPcrCustomPlot->xAxis->ticker()->setTickCount(9);
    m_pPcrCustomPlot->xAxis->setSubTicks(false);
    m_pPcrCustomPlot->yAxis->setRange(0, 2000);
    m_pPcrCustomPlot->yAxis->ticker()->setTickCount(9);
    m_pPcrCustomPlot->yAxis->setSubTicks(false);

    for(int i=0; i<m_StrChannelNameList.size(); i++)
    {
        _AddGraph(m_pPcrCustomPlot,QColor(m_strColorList.at(i)), i, m_StrChannelNameList.at(i));
    }

    m_pPcrRawRadioBtn = new QRadioButton(tr("原始数据"));
    m_pPcrRawRadioBtn->setChecked(true);
    connect(m_pPcrRawRadioBtn, &QRadioButton::clicked, this, &CHistoryDetailHrmCurve::_SlotPcrRawRadioBtn);

    m_pPcrNmzaRadioBtn = new QRadioButton(tr("归一化"));
    m_pPcrNmzaRadioBtn->setChecked(false);
    connect(m_pPcrNmzaRadioBtn, &QRadioButton::clicked, this, &CHistoryDetailHrmCurve::_SlotPcrNmzaRadioBtn);


    m_PcrButton_group =  new QButtonGroup();
    m_PcrButton_group->addButton(m_pPcrRawRadioBtn);
    m_PcrButton_group->addButton(m_pPcrNmzaRadioBtn);

}

void CHistoryDetailHrmCurve::_InitHrmDataWidget()
{
    m_pHrmCustomPlot = new QCustomPlot;
    m_pHrmCustomPlot->setFixedSize(900, 640);

    QFont font;
    font.setPixelSize(18);
    m_pHrmCustomPlot->legend->setFont(font);
    m_pHrmCustomPlot->legend->setSelectedFont(font);
    m_pHrmCustomPlot->legend->setVisible(false);
    m_pHrmCustomPlot->legend->setSelectableParts(QCPLegend::spItems);
    m_pHrmCustomPlot->legend->setBorderPen(Qt::NoPen);
    m_pHrmCustomPlot->legend->setWrap(1);
    m_pHrmCustomPlot->legend->setWrap(4);
    m_pHrmCustomPlot->legend->setFillOrder(QCPLegend::foColumnsFirst);

    m_pHrmCustomPlot->axisRect()->insetLayout()->setInsetAlignment(0, Qt::AlignTop);

    m_pHrmCustomPlot->xAxis->setLabel(tr("温度(℃)"));
    //m_pHrmCustomPlot->yAxis->setLabel(tr("荧光值(R)"));
    // 一阶导
    // DeltRn
    m_pHrmCustomPlot->yAxis->setLabel(tr("-d(Rn)/dT"));

    m_pHrmCustomPlot->xAxis->setLabelFont(font);
    m_pHrmCustomPlot->yAxis->setLabelFont(font);

    m_pHrmCustomPlot->xAxis->setRange(0, 45);
    m_pHrmCustomPlot->xAxis->ticker()->setTickCount(9);
    m_pHrmCustomPlot->xAxis->setSubTicks(false);
    m_pHrmCustomPlot->yAxis->setRange(0, 2000);
    m_pHrmCustomPlot->yAxis->ticker()->setTickCount(9);
    m_pHrmCustomPlot->yAxis->setSubTicks(false);

    for(int i=0; i<m_StrChannelNameList.size(); i++)
    {
        _AddGraph(m_pHrmCustomPlot,QColor(m_strColorList.at(i)), i, m_StrChannelNameList.at(i));
    }

    for(int i=0; i<m_StrChannelNameList.size(); i++)
    {
        QCPItemLine* pDotLineFirst = new QCPItemLine(m_pHrmCustomPlot);
        QCPItemLine* pDotLineSecond = new QCPItemLine(m_pHrmCustomPlot);
        QPen pen(Qt::DashLine);
        pen.setWidth(2);
        pen.setColor(m_strColorList.at(i));
        pDotLineFirst->setPen(pen);
        pDotLineFirst->setVisible(false);

        pDotLineSecond->setPen(pen);
        pDotLineSecond->setVisible(false);
        m_pDotLineFirstList.push_back(pDotLineFirst);
        m_pDotLineSecondList.push_back(pDotLineSecond);
    }

    m_pHrmRawRadioBtn = new QRadioButton(tr("原始数据"));
    m_pHrmRawRadioBtn->setChecked(true);
    connect(m_pHrmRawRadioBtn, &QRadioButton::clicked, this, &CHistoryDetailHrmCurve::_SlotHrmRawRadioBtn);

    m_pHrmPeakRadioBtn = new QRadioButton(tr("熔解峰值曲线"));
    m_pHrmPeakRadioBtn->setChecked(false);
    connect(m_pHrmPeakRadioBtn, &QRadioButton::clicked, this, &CHistoryDetailHrmCurve::_SlotHrmPeakRadioBtn);


    m_HrmButton_group =  new QButtonGroup();
    m_HrmButton_group->addButton(m_pHrmRawRadioBtn);
    m_HrmButton_group->addButton(m_pHrmPeakRadioBtn);
}

void CHistoryDetailHrmCurve::_SetThreshouldLineValue()
{
    //int rowCount = m_pTableWidget->rowCount();
    int rowCount = m_sResultMeltingInfoList.size();
    for (int row = 0; row < rowCount; ++row)
    {
        QString YmValues = m_sResultMeltingInfoList.at(row).m_strYmValues;
        QString TmValues =  m_sResultMeltingInfoList.at(row).m_strTmValues;
        QStringList  TmValueList = TmValues.split(",");
        QStringList  YmValueList = YmValues.split(",");

        // 取值，更新线值和 显不显示结果
        float threshouldFirst = 0.0;
        float threshouldSecond = 0.0;
        float fYmFirst = 0.0;
        float fYmSecond = 0.0;
        // 看Tm个数 隐藏点线图
        if(TmValueList.size() >= 1)
        {
            threshouldFirst = TmValueList.at(0).toFloat();
            fYmFirst = YmValueList.at(0).toFloat();

            // 取对应Ym 的值 上下点
            float fRange1  = 0.2*fYmFirst;
            if(fRange1 < 3 && fRange1 > -3)
            {
                fRange1 = 3;
            }
            m_pDotLineFirstList.at(row)->start->setCoords(threshouldFirst, fYmFirst - fRange1);
            m_pDotLineFirstList.at(row)->end->setCoords(threshouldFirst, fYmFirst + fRange1);
        }
        if(TmValueList.size() >= 2)
        {
            threshouldSecond = TmValueList.at(1).toFloat();
            fYmSecond = YmValueList.at(1).toFloat();

            // 取对应Ym 的值 上下点
            float fRange2  = 0.2*fYmSecond;
            if(fRange2 < 3 && fRange2 > -3)
            {
                fRange2 = 3;
            }
            m_pDotLineSecondList.at(row)->start->setCoords(threshouldSecond, fYmSecond - fRange2);
            m_pDotLineSecondList.at(row)->end->setCoords(threshouldSecond, fYmSecond + fRange2);
        }
        if(threshouldFirst < 0.001)
        {
            m_pDotLineFirstList.at(row)->setVisible(false);
        }
        if(threshouldSecond < 0.001)
        {
            m_pDotLineSecondList.at(row)->setVisible(false);
        }

    }
}

QWidget *CHistoryDetailHrmCurve::_InitChannelWidget()
{
    QCheckBox* pCheckBoxTemp = nullptr;
    QWidget *pContainerWidget = new QWidget();
    int fixedWidth = 1000;  // 根据需要修改宽度
    int fixedHeight = 100; // 根据需要修改高度
    pContainerWidget->setFixedSize(fixedWidth, fixedHeight);
    QHBoxLayout *pCheckBoxLayout = new QHBoxLayout(pContainerWidget);
    QString strChannelName;
    QString strChannelColor;

    if( m_strColorList.size() < 8 || m_StrChannelNameList.size() < 8)
    {
        return pContainerWidget;
    }
    for (int j = 1 ; j < 3; j ++)
    {
        for(int i = 0 ; i < 5; i ++)
        {
            int index = 0;
            index = (j-1)*4+i-1;
            if(0 == i)
            {
                strChannelColor = "#353E4E";
                strChannelName = QString(tr("hole-%1")).arg(j);
            }
            else
            {
                strChannelName = m_StrChannelNameList.at(index);
                strChannelColor = m_strColorList.at(index);
            }
            pCheckBoxTemp = new QCheckBox(strChannelName);
            pCheckBoxTemp->setChecked(true);
            pCheckBoxTemp->setProperty("index",QString::number(i));
            pCheckBoxTemp->setProperty("hole",j==1 ? "A" : "B");
            connect(pCheckBoxTemp,&QRadioButton::clicked,this,&CHistoryDetailHrmCurve::_SlotChannelCheckBox);
            j==1 ? m_pHoleACheckBoxList.push_back(pCheckBoxTemp) : m_pHoleBCheckBoxList.push_back(pCheckBoxTemp);
        }
    }


    m_threshouldCheckBox = new QCheckBox(tr("辅助线"));
    m_threshouldCheckBox->setChecked(true);
    m_threshouldCheckBox->setProperty("hole", "C");
    connect(m_threshouldCheckBox,&QRadioButton::clicked,this,&CHistoryDetailHrmCurve::_SlotChannelCheckBox);
    m_threshouldCheckBox->setVisible(false);
    pCheckBoxLayout->setMargin(0);
    pCheckBoxLayout->setSpacing(0);
    pCheckBoxLayout->addStretch(1);


    QGridLayout *pHoleGridLayout = new QGridLayout;
    for (int i = 0 ; i < m_pHoleACheckBoxList.size() ; i++)
    {
        pHoleGridLayout->addWidget(m_pHoleACheckBoxList.at(i), 0, i);
    }

    for (int i = 0 ; i < m_pHoleBCheckBoxList.size() ; i++)
    {
        pHoleGridLayout->addWidget(m_pHoleBCheckBoxList.at(i), 1, i);
    }

    // 设置每列之间的水平间距（可选）
    pHoleGridLayout->setColumnMinimumWidth(0, 20); // 设置第一列的最小宽度为100像素
    pHoleGridLayout->setColumnMinimumWidth(1, 20); // 第二列最小宽度为20像素
    pHoleGridLayout->setColumnMinimumWidth(2, 6); // 第三列最小宽度为20像素
    pHoleGridLayout->setColumnMinimumWidth(3, 6); // 第三列最小宽度为20像素
    pHoleGridLayout->setColumnMinimumWidth(4, 6); // 第三列最小宽度为20像素

    pHoleGridLayout->setHorizontalSpacing(12); // 这一行可以调整所有列之间的间隔
    pCheckBoxLayout->addLayout(pHoleGridLayout);
    pCheckBoxLayout->addStretch(1);
    pCheckBoxLayout->addWidget(m_threshouldCheckBox);
    pCheckBoxLayout->addStretch(1);

    for(int i=0; i<m_StrChannelNameList.size(); i++)
    {
        _SetChannelCheckBox(m_strColorList.at(i),i,m_StrChannelNameList.at(i));
    }
    // 添加布局到容器中
    pContainerWidget->setLayout(pCheckBoxLayout);
    return pContainerWidget;

}

void CHistoryDetailHrmCurve::_ChangeTableResult(QTableWidget *table, const stHrmReviewParam &sResultParam)
{
    if(!sResultParam.m_bReview)
    {
        return;
    }
    int rowCount = table->rowCount();
    for (int row = 0; row < rowCount; ++row) {
        // 获取靶标名称对应上
        QWidget *pNameWidget = table->cellWidget(row, 0);
        if (!pNameWidget)
        {
            return;
        }

        {
            QLabel *label = qobject_cast<QLabel*>(pNameWidget);
            if(label && label->text() == sResultParam.m_strName)
            {

                QString strInfo = sResultParam.m_strMeltingInfo_Review;
                QString strResult;
                if("P" == sResultParam.m_result)
                {
                    strResult = m_strPositive;
                }
                else if("N" == sResultParam.m_result)
                {
                    strResult = m_strNegative;
                }
                else if("E" == sResultParam.m_result)
                {
                    strResult = m_strError;
                }
                else
                {
                    strResult = m_strNull;
                }
                QString strReview = QString(tr("已审核"));
                QString strTm1Text = "/";
                QString strTm2Text = "/";
                QStringList strTmList,strRmList,strYmList;
                QString strThreashould;
                _GetTmFormMeltInfo(strInfo,strTmList,strRmList,strYmList,strThreashould);

                for (int i = 0 ; i < strTmList.size() ; i++) {
                    switch (i) {
                    case 0:
                    {
                        strTm1Text = strTmList.at(i);
                        break;
                    }
                    case 1:
                    {
                        strTm2Text = strTmList.at(i);
                        break;
                    }
                    default:
                        break;
                    }
                }
                QTableWidgetItem* pTm1Item = table->item(row,2);
                QTableWidgetItem* pTm2Item = table->item(row,3);
                if (pTm1Item) {
                    pTm1Item->setText(strTm1Text);
                }
                if (pTm2Item) {
                    pTm2Item->setText(strTm2Text);
                }


                QWidget *pResultWidget = table->cellWidget(row, 4);
                if (pResultWidget)
                {
                    QLabel *label = qobject_cast<QLabel*>(pResultWidget);
                    if (label) {
                        label->setText(strResult);
                        if("P" == sResultParam.m_result)
                        {
                            label->setStyleSheet("color: red;");
                        }
                        else
                        {
                            label->setStyleSheet("color: black;");
                        }
                        label->repaint();
                    };
                }
                QWidget *pReviewWidget = table->cellWidget(row, 5);
                if (pReviewWidget) {
                    QLabel *label = qobject_cast<QLabel*>(pReviewWidget);
                    if (label) {
                        label->setText(strReview);
                        label->repaint();
                    }
                }
            }
        }
    }
}

void CHistoryDetailHrmCurve::_UpdatePcrRawPlot()
{
    // chenhao 这里是只显示hrm的虚线

    QList<QList<double>> dFLDataList;
    if(m_bHistoryMode)
    {
        if(!m_sCardInfo.strCardID.isEmpty())
        {
            QString strTestTime = m_sCardInfo.strTestTime;
            strTestTime.remove("-").remove(" ").remove(":");
            QString strFLID = m_sResultInfo.strCardID + "+" + strTestTime;
            CHistoryDB::GetInstance()->GetTestDataFromCardIDAddTestTime(strFLID, dFLDataList);
            if(dFLDataList.isEmpty())
            {
                qDebug()<<Q_FUNC_INFO<<"查询FLID"<<strFLID<<"数据为空";
            }
        }
        /*
        QStringList strRawList;
        if(!m_sCardInfo.strCardID.isEmpty())
        {
            QString strTestTime = m_sCardInfo.strTestTime;
            strTestTime.remove("-").remove(" ").remove(":");
            QStringList strColorList = gk_strColorNameList;
            QString strFLID = "";
            for (int i = 0;i < gk_iHoleCount;i++)
            {
                for (int j = 0;j < gk_iBGYRCount; j++)
                {
                    strFLID = m_sResultInfo.strCardID + "+" + strTestTime + "_" + QString::number(i) + "-" + strColorList.at(j);
                    QString strRaw;
                    CHistoryDB::GetInstance()->GetCtDataRaw(strFLID, strRaw);
                    strRawList.push_back(strRaw);
                }
            }
        }
        for(int i=0; i<strRawList.size(); i++)
        {
            QList<double> dList;
            QStringList strOneList = strRawList.at(i).split(",");
            for(int j=0; j<strOneList.size(); j++)
            {
                dList << strOneList.at(j).toDouble();
            }
            dFLDataList << dList;
        }*/
    }
    else
    {
        for(int i=0; i<m_dFLMap.size(); i++)
            dFLDataList << m_dFLMap.at(i).values();
    }

    m_pPcrCustomPlot->clearGraphs();
    _SetPlotData(dFLDataList);
}

void CHistoryDetailHrmCurve::_UpdatePcrNmzaPlot()
{
    QStringList strDeltaList;
    int iCycleCount = 0;
    if(!m_sCardInfo.strCardID.isEmpty())
    {
        // 两个一起查，然后再分配 是取审核还是不审核的 数据；
        QString strTestTime = m_sCardInfo.strTestTime;
        strTestTime.remove("-").remove(" ").remove(":");
        QString strFLID = m_sResultInfo.strCardID + "+" + strTestTime;
        QStringList strFit,strFitReview;
        CHistoryDB::GetInstance()->GetCTDeltaFormFLID(strFLID, strFit,strFitReview);

        if(strFit.isEmpty() && strFitReview.isEmpty())
        {
            qDebug()<<Q_FUNC_INFO<<"查询FLID"<<strFLID<<"数据为空";
        }

        QStringList strReviewResultList = m_sResultInfo.strResult_Review.split(";");
        bool bReiew = false;
        for (int i = 0;i < gk_iHoleCount;i++)
        {
            for (int j = 0;j < gk_iBGYRCount; j++)
            {
                int index = j+4*i;
                if((m_sResultInfo.strReview == "m"||m_sResultInfo.strReview == "y")
                        &&  index < strReviewResultList.size())
                {
                    QString strResultReview = strReviewResultList.at(index);
                    if(strResultReview == "/" || strResultReview.isEmpty())
                    {
                        bReiew = false;
                    }
                    else
                    {
                        bReiew = true;
                    }
                }
                if(iCycleCount <= 0)
                {
                    CHistoryDB::GetInstance()->GetCTDeltaDataCount(strFLID,iCycleCount);
                }
                QString strDelta;
                if(bReiew && index < strFitReview.size())
                {
                    strDelta = strFitReview.at(index);
                }
                else if(!bReiew && index < strFit.size())
                {
                    strDelta = strFit.at(index);
                }
                strDeltaList.push_back(strDelta);
            }
        }
        /*
        bool bReiew = false;
        QString strTestTime = m_sCardInfo.strTestTime;
        strTestTime.remove("-").remove(" ").remove(":");
        QStringList strReviewResultList = m_sResultInfo.strResult_Review.split(";");
        QStringList strColorList = gk_strColorNameList;
        QString strFLID = "";
        for (int i = 0;i < gk_iHoleCount;i++)
        {
            for (int j = 0;j < gk_iBGYRCount; j++)
            {
                int index = j+4*i;
                strFLID = m_sResultInfo.strCardID + "+" + strTestTime + "_" + QString::number(i) + "-" + strColorList.at(j);
                if((m_sResultInfo.strReview == "m"||m_sResultInfo.strReview == "y")
                        &&  index < strReviewResultList.size())
                {
                    QString strResultReview = strReviewResultList.at(index);
                    if(strResultReview == "/" || strResultReview.isEmpty())
                    {
                        bReiew = false;
                    }
                    else
                    {
                        bReiew = true;
                    }
                }
                if(iCycleCount <= 0)
                {
                    CHistoryDB::GetInstance()->GetCTDeltaDataCount(strFLID,iCycleCount);
                }
                QString strDelta;
                CHistoryDB::GetInstance()->GetCTDelta(strFLID, strDelta,bReiew);
                strDeltaList.push_back(strDelta);
            }
        }
        */
    }



    QList<QList<double>> dFLDataList;
    for(int i=0; i<strDeltaList.size(); i++)
    {
        QList<double> dList;
        QStringList strOneList = strDeltaList.at(i).split(",");

        if(iCycleCount >= 5 && strOneList.size() >= iCycleCount - 5)
        {
            bool bshowCure = true;
            if( i < m_sResultMeltingInfoList.size())
            {
                bshowCure = bShowNmzaCure(m_sResultMeltingInfoList.at(i).m_strCtResult,m_sResultMeltingInfoList.at(i).m_strTestMode,m_sResultMeltingInfoList.at(i).m_bControl);
            }
            for(int j=0; j<strOneList.size(); j++)
            {    
                if(bshowCure)
                {
                    dList << strOneList.at(j).toDouble();
                }
                else
                {
                    dList << (std::rand() % 50)/100.0;
                }
            }
        }
        else
        {
            for(int j=0; j<iCycleCount; j++)
            {
                dList << (std::rand() % 50)/100.0;
            }
        }
        dFLDataList << dList;
    }

    m_pPcrCustomPlot->clearGraphs();
    _SetPlotData(dFLDataList);

}

void CHistoryDetailHrmCurve::_UpdateHrmRawPlot()
{
    m_threshouldCheckBox->setVisible(false);
    QString strTestTime = m_sCardInfo.strTestTime;
    strTestTime.remove("-").remove(" ").remove(":");
    QString strNewCardID = m_sCardInfo.strCardID + "+" + strTestTime;

    QList<double> dTempDataList;
    QList<QList<double>> dFLDataList;
    QList<double> realTempVec;
    if(m_bHistoryMode)
    {
        if(!m_sCardInfo.strCardID.isEmpty())
        {
            CHistoryDB::GetInstance()->GetMeltingDoubleData(strNewCardID, realTempVec);
            CHistoryDB::GetInstance()->GetMeltingDataFromCardIDAddTestTime(strNewCardID+"_",dFLDataList);
        }

        /*
        for(int i = 0 ; i < gk_iHoleCount; i++)
        {
            for(int j=0; j<gk_iBGYRCount; j++)
            {
                QString strFLID = QString("%1_%2-%3").arg(strNewCardID).arg(i).arg(gk_strColorNameList.at(j));
                QList<double> dFLList;
                CHistoryDB::GetInstance()->GetMeltingDoubleData(strFLID, dFLList);
                dFLDataList.append(dFLList);
            }
        }
        if(dFLDataList.isEmpty())
            return;*/

    }
    else
    {
        for(int i=0; i<m_dMeltingFLMap.size(); i++)
        {
            dFLDataList << m_dMeltingFLMap.at(i).values();
        }
        for(int i=0; i<m_dMeltingTempMap.size(); i++)
            realTempVec.push_back(m_dMeltingTempMap.at(i) / 100.0);
    }
    m_pHrmCustomPlot->clearGraphs();
    _SetPlotData(realTempVec,dFLDataList);

}

void CHistoryDetailHrmCurve::_UpdateHrmPeakPlot()
{

    m_threshouldCheckBox->setVisible(true);
    QString strTestTime = m_sCardInfo.strTestTime;
    strTestTime.remove("-").remove(" ").remove(":");
    QString strNewCardID = m_sCardInfo.strCardID + "+" + strTestTime;

    QList<double> dTempDataList;
    QList<QList<double>> dFLDataList;
    QStringList strFirstDevList;
    if(m_sCardInfo.strCardID.isEmpty())
    {
        return;
    }
    CHistoryDB::GetInstance()->GetMeltingDoubleData(strNewCardID, dTempDataList);
    if(dTempDataList.isEmpty())
    {
        m_pHrmCustomPlot->clearGraphs();
        m_pHrmCustomPlot->replot();
        return;
    }
    CHistoryDB::GetInstance()->GetHrmDelta(strNewCardID, strFirstDevList);

    if(strFirstDevList.isEmpty())
    {
        m_pHrmCustomPlot->clearGraphs();
        m_pHrmCustomPlot->replot();
        return;
    }
    for(int i = 0; i < strFirstDevList.size();i++)
    {
        QList<double> dList;
        QStringList strOneList = strFirstDevList.at(i).split(",");
        for(int j = 0; j < strOneList.size(); j++)
        {
            bool bshowCure = false;
            if( i < m_sResultMeltingInfoList.size())
            {
                bshowCure = m_sResultMeltingInfoList.at(i).m_bshowCt;
            }
            if(bshowCure)
            {
                dList << (std::rand() % 50)/100.0;
            }
            else
            {
                dList << strOneList.at(j).toDouble();
            }

        }
        // 结果修正？ 交由算法合适点；
        dFLDataList<<dList;
    }

    m_pHrmCustomPlot->clearGraphs();
    _SetPlotData(dTempDataList,dFLDataList);

}

void CHistoryDetailHrmCurve::_UpdateTestResultLabel(const QString &strResult)
{
    m_pTestResultLabel->setText(strResult);
}

void CHistoryDetailHrmCurve::_AddGraph(QCustomPlot *pCustomPlot, const QColor &qColor, int iChart, const QString &strChartName)
{
    QPen pen;
    pen.setWidth(3);
    pen.setColor(qColor);
    pCustomPlot->addGraph();
    pCustomPlot->graph(iChart)->setPen(pen);
    pCustomPlot->graph(iChart)->setName(strChartName);
    pCustomPlot->graph(iChart)->setAntialiasedFill(true);
    if(strChartName == "0")
    {
        pCustomPlot->graph(iChart)->setVisible(false);
        //pCustomPlot->legend->item(iChart)->setVisible(false); // 隐藏对应的图例项
    }
    else
    {
        pCustomPlot->graph(iChart)->setVisible(true);
        //pCustomPlot->legend->item(iChart)->setVisible(false); // 隐藏对应的图例项
    }
}

void CHistoryDetailHrmCurve::_SetPlotData(const QList<double> &dTempDataList,const QList<QList<double> > &dFLDataList)
{
    QStringList strNameList = m_sLotInfo.strCurveName.split(";");
    int size = qMin(dFLDataList.size(), strNameList.size());
    qDebug()<<Q_FUNC_INFO<<"fl size:"<<dFLDataList.size()<<"name size:"<<strNameList.size()<<strNameList;

    for(int i=0; i<size; i++)
    {
        QVector<double> y = dFLDataList.at(i).toVector();
        QVector<double> x = dTempDataList.toVector();

        QCPGraph *pGraph = m_pHrmCustomPlot->graph(i);
        if(nullptr == pGraph)
        {
            _AddGraph(m_pHrmCustomPlot,QColor(m_strColorList.at(i)), i, strNameList.at(i));
            _SetChannelCheckBox(m_strColorList.at(i),i,strNameList.at(i));
        }
        m_pHrmCustomPlot->graph(i)->setData(x, y);
    }
    _ChannelCheckBoxHandle(m_pHrmCustomPlot);

}

void CHistoryDetailHrmCurve::_SetPlotData(const QList<QList<double> > &dFLDataList)
{
    QStringList strNameList = m_sLotInfo.strCurveName.split(";");
    int size = qMin(dFLDataList.size(), strNameList.size());
    qDebug()<<Q_FUNC_INFO<<"fl size:"<<dFLDataList.size()<<"name size:"<<strNameList.size()<<strNameList;

    for(int i=0; i<strNameList.size(); i++)
    {
        QCPGraph *pGraph = m_pPcrCustomPlot->graph(i);
        if(nullptr == pGraph)
        {
            _AddGraph(m_pPcrCustomPlot,QColor(m_strColorList.at(i)), i, strNameList.at(i));
            _SetChannelCheckBox(m_strColorList.at(i),i,strNameList.at(i));
        }
    }

    for(int i=0; i<size; i++)
    {
        QVector<double> y = dFLDataList.at(i).toVector();
        QVector<double> x;
        for(int j=0; j<y.size(); j++)
            x.push_back(j+1);

        QCPGraph *pGraph = m_pPcrCustomPlot->graph(i);
        if(nullptr == pGraph)
        {
            _AddGraph(m_pPcrCustomPlot,QColor(m_strColorList.at(i)), i, strNameList.at(i));
            _SetChannelCheckBox(m_strColorList.at(i),i,strNameList.at(i));
        }
        m_pPcrCustomPlot->graph(i)->setData(x, y);

    }
    _ChannelCheckBoxHandle(m_pPcrCustomPlot);

}

void CHistoryDetailHrmCurve::_ResetYRang(const QCustomPlot* pCustomPlot,const QList<QList<double> > &dXList,const QList<QList<double> > &dYList)
{
    if(dYList.isEmpty() || dXList.isEmpty())
        return;

    double dXMin = 0, dXMax = 0;
    for(int i=0; i<dXList.size(); i++)
    {
        for(int j=0; j<dXList.at(i).size(); j++)
        {
            if( 0 == i && 0 == j )
            {
                dXMin = dXList.at(i).at(j);
            }
            dXMin = qMin(dXMin, dXList.at(i).at(j));
            dXMax = qMax(dXMax, dXList.at(i).at(j));
        }
    }

    double x1 = dXMax + (dXMax - dXMin) * 0.01;
    //double y0 = dMin - (dMax - 0) * 0.2;
    double x0 = dXMin;
    if(pCustomPlot == m_pHrmCustomPlot)
    {
        if(x1 <= 85)
        {

            x1 = 85;
        }
    }
    else if(pCustomPlot == m_pPcrCustomPlot)
    {
        if(x1 <= 40)
        {

            x1 = 40;
        }
    }


    if(x0 == x1)
    {
        x0 = 0;
        x1 = 100;
    }

    double dYMin = 0, dYMax = 0;
    for(int i=0; i<dYList.size(); i++)
    {
        for(int j=0; j<dYList.at(i).size(); j++)
        {
            if( 0 == i && 0 == j )
            {
                dYMin = dYList.at(i).at(j);
            }
            dYMin = qMin(dYMin, dYList.at(i).at(j));
            dYMax = qMax(dYMax, dYList.at(i).at(j));
        }
    }

    double y1 = dYMax + (dYMax - 0) * 0.2;
    if(dYMax <= 50)
    {
        y1 = 50;
    }
    //double y0 = dMin - (dMax - 0) * 0.2;
    double y0 = 0;
    if(dYMin < 0)
    {
        y0 = dYMin - (dYMax - 0) * 0.1;
    }

    if(y0 == y1)
    {
        y0 = 0;
        y1 = 2000;
    }

    pCustomPlot->xAxis->setRange(x0, x1);
    pCustomPlot->yAxis->setRange(y0, y1);
}

void CHistoryDetailHrmCurve::_SetChannelCheckBox(const QString &qColor, int iChart, const QString &strChannelName)
{

    int iHole = iChart/4;
    int index = iChart%4;
    QList<QCheckBox *>* pList;
    if(iHole == 0)
    {
        pList = &m_pHoleACheckBoxList;
    }
    else if(iHole == 1)
    {
        pList = &m_pHoleBCheckBoxList;
    }
    else
    {
        return;
    }
    if(index >= pList->size()-1)
    {
        return;
    }
    QString strStyleSheet = QString("QCheckBox { color: %1; }").arg(qColor);
    pList->at(index+1)->setStyleSheet(strStyleSheet);
    pList->at(index+1)->setText(strChannelName);
    if(strChannelName == "0")
    {
        pList->at(index+1)->setVisible(false);
        //pList->at(index+1)->setChecked(false);
    }
    else
    {
        pList->at(index+1)->setVisible(true);
    }
}

void CHistoryDetailHrmCurve::_SetManualBtn(bool bReview,const SResultInfoStruct& sResultInfo)
{
    if(!CPublicConfig::GetInstance()->GetShowRawCurve())
    {
        m_pReviewCalcBtn->setVisible(false);
        m_pReviewPassBtn->setVisible(false);
        return;
    }

    // 暂时不显示人工审核
    m_pReviewCalcBtn->setVisible(false);
    m_pReviewPassBtn->setVisible(false);
    return;

    if(eTestDone != sResultInfo.iStatus)
    {
        m_pReviewCalcBtn->setVisible(false);
        m_pReviewPassBtn->setVisible(false);
        return;
    }
    if(bReview)
    {
        m_pReviewPassBtn->setText(tr("撤销审核"));
        m_pReviewCalcBtn->setVisible(false);
        m_pReviewPassBtn->setVisible(true);
    }
    else
    {
        m_pReviewPassBtn->setText(tr("审核通过"));
        m_pReviewCalcBtn->setVisible(true);
        m_pReviewPassBtn->setVisible(true);
    }
}

