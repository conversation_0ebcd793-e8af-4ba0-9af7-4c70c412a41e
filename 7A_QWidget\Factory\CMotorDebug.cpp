#include "CMotorDebug.h"
#include <QTime>
#include <QDebug>
#include <QVBoxLayout>
#include "MotorDebug/CNormalMotor.h"
#include "MotorDebug/CMotorMethod.h"
#include "MotorDebug/CMotorRegister.h"
#include "MotorDebug/CMotorChopper.h"
#include "MotorDebug/CMotorGXIO.h"
#include "MotorDebug/CMotorConfig.h"
#include "MotorDebug/CMotorCopmensate.h"
#include "MotorDebug/CMotorCalibrate.h"
#include "MotorDebug/CMotorPosition.h"

CMotorDebug::CMotorDebug(QWidget *parent) : QWidget(parent)
{
    _InitWidget();
}

CMotorDebug::~CMotorDebug()
{

}

void CMotorDebug::_SlotTitleChanged(int index)
{
    m_pStackedWidget->setCurrentIndex(index);
}

void CMotorDebug::_InitWidget()
{
    QStringList strList = {tr("通用电机"), tr("单指令"), tr("寄存器"),
                           //tr("斩波器"),
                           tr("电机光耦"),
                           //tr("配置文本"),
                           tr("电机校准"), tr("电机补偿"), tr("位置调试")};
    m_pCHBtnTitle = new CHBtnTitleWidget(strList);
    m_pCHBtnTitle->SetTitleIndex(0);
    connect(m_pCHBtnTitle, &CHBtnTitleWidget::SignalTitleChanged, this, &CMotorDebug::_SlotTitleChanged);

    m_pCNormalMotor = new CNormalMotor;
    m_pCMotorMethod = new CMotorMethod;
    m_pCMotorRegister = new CMotorRegister;
    //m_pCChopper = new CMotorChopper;
    m_pCMotorGXIO = new CMotorGXIO;
    //m_pCMotorConfig = new CMotorConfig;
    m_pCMotorCopmensate = new CMotorCopmensate;
    m_pCMotorCalibrate = new CMotorCalibrate;
    m_pCMotorPosition = new CMotorPosition;

    m_pStackedWidget = new QStackedWidget;
    m_pStackedWidget->addWidget(m_pCNormalMotor);
    m_pStackedWidget->addWidget(m_pCMotorMethod);
    m_pStackedWidget->addWidget(m_pCMotorRegister);
    //m_pStackedWidget->addWidget(m_pCChopper);
    m_pStackedWidget->addWidget(m_pCMotorGXIO);
    //m_pStackedWidget->addWidget(m_pCMotorConfig);
    m_pStackedWidget->addWidget(m_pCMotorCalibrate);
    m_pStackedWidget->addWidget(m_pCMotorCopmensate);
    m_pStackedWidget->addWidget(m_pCMotorPosition);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pCHBtnTitle);
    pLayout->addSpacing(20);
    pLayout->addWidget(m_pStackedWidget);
    this->setLayout(pLayout);
}
