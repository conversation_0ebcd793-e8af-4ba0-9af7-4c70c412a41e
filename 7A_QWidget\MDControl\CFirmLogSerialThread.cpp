﻿#include "CFirmLogSerialThread.h"
#include <QDebug>
#include <QApplication>
#include "PublicConfig.h"

CFirmLogSerialThread* CFirmLogSerialThread::m_spInstance = nullptr;

CFirmLogSerialThread* CFirmLogSerialThread::GetInstance()
{
    if(nullptr == m_spInstance)
        m_spInstance = new CFirmLogSerialThread;
    return m_spInstance;
}

CFirmLogSerialThread::CFirmLogSerialThread(QObject *parent) : QObject(parent)
{
    m_bOpenSerialPort = false;
    connect(this, &CFirmLogSerialThread::SignalInitThread,
            this, &CFirmLogSerialThread::SlotInitThread);

    m_pThread = new QThread;
    this->moveToThread(m_pThread);
    m_pThread->start();

    emit SignalInitThread();
}

CFirmLogSerialThread::~CFirmLogSerialThread()
{

}

void CFirmLogSerialThread::ResetCom(QString strSerialName)
{
    emit SignalResetSerial(strSerialName);
}

bool CFirmLogSerialThread::IsOpenSuccess()
{
    return m_bOpenSerialPort;
}

void CFirmLogSerialThread::SlotInitThread()
{
    m_pSerialPort = nullptr;
    m_currentDate = QDate::currentDate();
    m_strLogPath =  QApplication::applicationDirPath() + "/data/slave/slave_" + m_currentDate.toString("yyyyMMdd") + ".log";
    qDebug()<<"下位机固件日志文件:"<<m_strLogPath;
    m_pLogFile = new QFile(m_strLogPath,this);
    //if (!m_pLogFile->open(QIODevice::WriteOnly | QIODevice::Append))
    //    qDebug()<<"下位机固件日志文件创建失败:"<<m_pLogFile->errorString();

    QString strSerialName;// = CSystemDB::GetInstance().getQStringValueFromKey("serial_port_firm");
    _InitSerial(strSerialName);

    connect(this, &CFirmLogSerialThread::SignalResetSerial, this, &CFirmLogSerialThread::SlotResetSerial);
}

void CFirmLogSerialThread::_InitSerial(QString strSerialName)
{
    if(strSerialName.isEmpty())
        return;

    m_strSerialName = strSerialName;

    QString strLog;

    if(!m_pSerialPort)
    {
        m_pSerialPort = new QSerialPort(this);
    }
    else
    {
        if(m_pSerialPort->isOpen())
            m_pSerialPort->close();
    }


    m_pSerialPort->setPortName(strSerialName);
    if(m_pSerialPort->open(QIODevice::ReadWrite))
    {
        m_pSerialPort->setBaudRate(115200);
        m_pSerialPort->setDataBits(QSerialPort::Data8);
        m_pSerialPort->setParity(QSerialPort::NoParity);
        m_pSerialPort->setFlowControl(QSerialPort::NoFlowControl);
        m_pSerialPort->setStopBits(QSerialPort::OneStop);
        connect(m_pSerialPort, &QSerialPort::readyRead, this, &CFirmLogSerialThread::SlotReadSerial);
        m_bOpenSerialPort = true;
        strLog = "下位机日志串口打开成功:" + strSerialName;
    }
    else
    {
        m_bOpenSerialPort = false;
        strLog = "下位机日志串口打开失败:" + strSerialName + m_pSerialPort->errorString();
    }

    emit SignalLog(m_bOpenSerialPort,strLog);
    qDebug()<<strLog;
}

void CFirmLogSerialThread::SlotReadSerial()
{
    QByteArray temp = m_pSerialPort->readAll();
    if(temp.isEmpty())
        return;

    _WriteFirmLog(temp);
}

void CFirmLogSerialThread::_WriteFirmLog(QByteArray &logByte)
{
    QDate qNowDate = QDate::currentDate();
    if(qNowDate == m_currentDate)
    {
        if(!m_pLogFile->isOpen())
            m_pLogFile->open(QIODevice::WriteOnly | QIODevice::Append);
        m_pLogFile->write(logByte + "\n");
        m_pLogFile->flush();
    }
    else
    {
        m_currentDate = qNowDate;
        m_pLogFile->flush();
        m_pLogFile->close();

        m_strLogPath = QApplication::applicationDirPath() + "/data/slave_" + qNowDate.toString("yyyyMMdd") + ".log";
        m_pLogFile->setFileName(m_strLogPath);
        if (!m_pLogFile->open(QIODevice::WriteOnly | QIODevice::Append))
            qDebug()<<"下位机固件日志文件创建失败:"<<m_pLogFile->errorString();
        else
            m_pLogFile->write(logByte + "\n");
    }
}

void CFirmLogSerialThread::SlotResetSerial(QString strNewSerialName)
{
    if(strNewSerialName.isEmpty())
    {
        if(nullptr != m_pSerialPort)
        {
            m_bOpenSerialPort = false;
            m_pSerialPort->close();
            emit SignalLog(m_bOpenSerialPort,"下位机串口已关闭");
        }
        return;
    }

    if(m_pSerialPort)
        disconnect(m_pSerialPort, &QSerialPort::readyRead, this, &CFirmLogSerialThread::SlotReadSerial);
    _InitSerial(strNewSerialName);
}
