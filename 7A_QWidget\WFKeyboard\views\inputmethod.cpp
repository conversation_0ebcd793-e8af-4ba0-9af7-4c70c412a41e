
#include <QDesktopWidget>
#include <QPlainTextEdit>
#include <QPointer>
#include <QGuiApplication>

#include "keyboard.h"
#include "inputmethod.h"

#define OBJECT_EDIT_NAME "edit_name"

class InputMethod::PrivateData
{
public:
    PrivateData(InputMethod *p)
        : parent(p)
        , currentEditLet(NULL)
        , currentEditTedt(NULL)
        , isFromToolButtonKeyType(false)
        , isLineEdit(true)
        , m_iDesktopWidth(1920)
        , m_iDesktopHeight(1080)
        , m_iKeybaordX(0)
        , m_iKeybaordY(0)
        , m_iKeyboardWidth(800)
        , m_iKeyboardHeight(350)
    {
        keyboard = new KeyBoard();

        QRect rect = QApplication::desktop()->screenGeometry();
        m_iDesktopWidth = rect.width();
        m_iDesktopHeight = rect.height();
    }

    void InitConnect();

    bool IsAllNumber(const QString& text);

    void LineEdit(QObject *obj);

    void TextEdit(QObject *obj);

private:
    void ChangedPosition(QWidget *pInputWidget);

public:
    InputMethod *parent;
    KeyBoard *keyboard;
    QPointer<QLineEdit> currentEditLet;
    QTextEdit* currentEditTedt;
    bool isFromToolButtonKeyType;
    bool isLineEdit;

    int m_iDesktopWidth, m_iDesktopHeight;
    int m_iKeybaordX, m_iKeybaordY;
    int m_iKeyboardWidth, m_iKeyboardHeight;    
};

void InputMethod::PrivateData::InitConnect()
{
    parent->connect(keyboard, SIGNAL(SignalCharacterGenerated(QChar)), parent, SLOT(SlotCharacterGenerated(QChar)));
    parent->connect(keyboard, SIGNAL(SignalEditFinished()), parent, SLOT(SlotEditFinished()));
    parent->connect(parent, SIGNAL(SignalKeyPress(int,int)), keyboard, SLOT(SlotKeyPressed(int,int)) );
}

bool InputMethod::PrivateData::IsAllNumber(const QString& text)
{
    if(text.isEmpty())
        return false;

    foreach (QChar qChar, text)
    {
        if(!qChar.isDigit())
        {
            return false;
        }
    }

    return true;
}

void InputMethod::PrivateData::LineEdit(QObject *obj)
{
    QLineEdit *pLineEdit = qobject_cast<QLineEdit *>(obj);

    if (NULL == pLineEdit)
    {
        qDebug()<<"input Edit 1 ";
        return;
    }

    if (!pLineEdit->isEnabled())
    {
        qDebug()<<"input Edit 2 ";
        return;
    }

    if (pLineEdit->isReadOnly())
    {
        qDebug()<<"input Edit 3 ";
        return;
    }

    if (keyboard->oriEdt == pLineEdit)
    {
        qDebug()<<"input Edit 4 ";
        return;
    }

    currentEditLet = pLineEdit;

    QLineEdit::EchoMode kEchoMode = pLineEdit->echoMode();
    keyboard->oriEdt->setEchoMode(kEchoMode);

    QString strName = pLineEdit->property(OBJECT_EDIT_NAME).toString();
    keyboard->titleLbl->setText(strName);

    QString strText = pLineEdit->text();
    if (/*IsAllNumber(strText)||*/(pLineEdit->property("NumEdit").toBool()))
    {
        keyboard->SetKeyType(KEYBOARD_TYPE_NUM);
    }

    const QValidator *pValidator = pLineEdit->validator();

    keyboard->oriEdt->setValidator(pValidator);
    keyboard->oriEdt->setMaxLength(pLineEdit->maxLength());
    keyboard->oriEdt->setText(strText);

    int iSelectionStart = pLineEdit->selectionStart();
    int iSelectionCount = pLineEdit->selectedText().count();
    if (iSelectionStart > -1 && iSelectionCount> 0)
    {
        keyboard->oriEdt->setSelection(iSelectionStart, iSelectionCount);
    }

    if(pLineEdit->property("ClearOrigin").toBool())
    {
        keyboard->oriEdt->clear();
    }

    if(keyboard->isVisible())
    {
       // keyboard->hide();
    }

    isLineEdit = true;
    keyboard->oriEdt->setFocus();
    keyboard->oriEdt->setVisible(true);
    keyboard->oriTedt->hide();

    ChangedPosition(pLineEdit);

    keyboard->Show();
}

void InputMethod::PrivateData::TextEdit(QObject *obj)
{
    if(keyboard->isVisible())
    {
       return;
    }

    QTextEdit *pTextEdit = qobject_cast<QTextEdit *>(obj);

    if (NULL == pTextEdit)
    {
        qDebug()<<"input Edit 1 ";
        return;
    }

    if (!pTextEdit->isEnabled())
    {
        qDebug()<<"input Edit 2 ";
        return;
    }

    if (pTextEdit->isReadOnly())
    {
        qDebug()<<"input Edit 3 ";
        return;
    }

    if (keyboard->oriTedt == pTextEdit)
    {
        qDebug()<<"input Edit 4 ";
        return;
    }

    currentEditTedt = pTextEdit;

    QString strName = pTextEdit->property(OBJECT_EDIT_NAME).toString();
    keyboard->titleLbl->setText(strName);

    QString strText = pTextEdit->document()->toPlainText();
    if (/*IsAllNumber(strText)||*/(pTextEdit->property("NumEdit").toBool()))
    {
        keyboard->SetKeyType(KEYBOARD_TYPE_NUM);
    }

    keyboard->oriTedt->setPlainText(strText);

    QTextCursor cursor = keyboard->oriTedt->textCursor();
    cursor.movePosition(QTextCursor::End);
    keyboard->oriTedt->setTextCursor(cursor);

    if(pTextEdit->property("ClearOrigin").toBool())
    {
        keyboard->oriTedt->clear();
    }

    isLineEdit = false;
    keyboard->oriTedt->setFocus();
    keyboard->oriTedt->setVisible(true);
    keyboard->oriEdt->hide();

    ChangedPosition(pTextEdit);

    keyboard->Show();
}

void InputMethod::PrivateData::ChangedPosition(QWidget *pInputWidget)
{
    QPoint qGlobalPoint = pInputWidget->mapToGlobal(QPoint(0,0));

    int iNewX = 0;
    int iNewY = 0;

    if(qGlobalPoint.x() + m_iKeyboardWidth < m_iDesktopWidth)
    {
        iNewX = qGlobalPoint.x();
    }
    else
    {
        iNewX = qGlobalPoint.x() + pInputWidget->width() - m_iKeyboardWidth;
    }

    if(qGlobalPoint.y() + pInputWidget->height() + m_iKeyboardHeight > m_iDesktopHeight)
    {
        //iNewY = 10; //直接移动到最顶端
        iNewY = qGlobalPoint.y() - m_iKeyboardHeight - 10; //跟随,上面
    }
    else
    {
        //iNewY = m_iMainWindowHeight - m_iKeyboardHeight; //直接移动到最底端
        iNewY = qGlobalPoint.y() + pInputWidget->height() + 10; //跟随,下面
    }

    qDebug()<<Q_FUNC_INFO<<"mainwindow size:"<<m_iDesktopWidth<<m_iDesktopHeight
           <<"keyboard size:"<<m_iKeyboardWidth<<m_iKeyboardHeight
          <<"qwidget size:"<<pInputWidget->size()
         <<"qwidget global point"<<qGlobalPoint
        <<"x,y:"<<iNewX<<iNewY;

    keyboard->move(iNewX, iNewY);
}

InputMethod::InputMethod()
    : md(new PrivateData(this))
{
    md->InitConnect();

    setObjectName("InputMethod");
}

InputMethod::~InputMethod()
{
    delete md;
}

bool InputMethod::eventFilter(QObject *obj, QEvent *event)
{
    if(event->type() == QEvent::MouseButtonRelease && obj->inherits("QLineEdit"))
    {
        md->LineEdit(obj);
    }
#if QT_VERSION < QT_VERSION_CHECK(5, 0, 0)
    else if(event->type() == QEvent::RequestSoftwareInputPanel && obj->inherits("QTextEdit") && md->currentEditTedt == NULL)
    {
        md->TextEdit(obj);
    }
#else
    else if(event->type() == QEvent::InputMethodQuery && obj->inherits("QTextEdit") && md->currentEditTedt == NULL)
    {
        md->TextEdit(obj);
    }
#endif
    else if(event->type() == QEvent::Hide)
    {
        if (md->keyboard == obj)
        {
            md->keyboard->HideKeyBoard();
        }
    }
    else if(event->type() == QEvent::KeyPress)
    {
        if(!md->isFromToolButtonKeyType)
        {
            if (HandleKeyPress(obj, event))
            {
                return true;
            }
        }
        else
        {
            md->isFromToolButtonKeyType = false;
        }
    }
    else if(event->type() == QEvent::KeyRelease)
    {
        HandleKeyRelease(obj, event);

        return true;
    }

    return QObject::eventFilter(obj, event);
}


void InputMethod::SlotCharacterGenerated(QChar c)
{
    qDebug()<<Q_FUNC_INFO<<c;
    QPointer<QWidget> w = md->isLineEdit ? static_cast<QWidget*>(md->keyboard->oriEdt) : static_cast<QWidget*>(md->keyboard->oriTedt);

    if (!w)
        return;

    md->isFromToolButtonKeyType = true;
    QKeyEvent keyPress(QEvent::KeyPress, c.unicode() , Qt::NoModifier, QString(c));
    QApplication::sendEvent(w, &keyPress);
   // QApplication::sendEvent(md->currentEditLet, &keyPress);
}

void InputMethod::SlotEditFinished()
{
    if(md->currentEditLet && md->keyboard && md->isLineEdit)
    {
        connect(this,SIGNAL(SinglaReturnPressed()),md->currentEditLet,SIGNAL(returnPressed()));
        md->currentEditLet->setText(md->keyboard->oriEdt->text());

        emit SinglaReturnPressed();
        md->keyboard->oriEdt->clear();

        disconnect(this,SIGNAL(SinglaReturnPressed()),md->currentEditLet,SIGNAL(returnPressed()));
    }
    else if(md->currentEditTedt && md->keyboard && !md->isLineEdit)
    {
        md->currentEditTedt->setPlainText(md->keyboard->oriTedt->toPlainText());
        QTextCursor cursor = md->currentEditTedt->textCursor();
        cursor.movePosition(QTextCursor::End);
        md->currentEditTedt->setTextCursor(cursor);
        emit SinglaReturnPressed();
        md->keyboard->oriTedt->clear();
    }

    md->currentEditLet=NULL;
    md->currentEditTedt=NULL;
}

bool InputMethod::HandleKeyPress(QObject *obj, QEvent *event)
{
    if (NULL == obj || NULL == event)
        return false;

    if (!md->keyboard->isVisible())
        return false;

    QKeyEvent *pKeyEvent = static_cast<QKeyEvent *>(event);

#ifdef QT_DEBUG
    qDebug() << Q_FUNC_INFO << __LINE__ << "KeyPress!" << "Object name:" << obj->objectName()
             << "Modifier:" << pKeyEvent->modifiers() << "key:" << pKeyEvent->key()
             << "QChar:" << QChar(pKeyEvent->key()) << "text:" << pKeyEvent->text();;
#endif

    emit SignalKeyPress(pKeyEvent->key(), pKeyEvent->modifiers());
    return true;
}

void InputMethod::HandleKeyRelease(QObject *obj, QEvent *event)
{
    if (NULL == obj || NULL == event)
        return;

    QKeyEvent *pKeyEvent = static_cast<QKeyEvent *>(event);
    emit SignalKeyRelese(pKeyEvent->key());
}

void InputMethod::SetPagingEnabled(const bool& enable)
{
    md->keyboard->SetPagingEnabled(enable);
}

void InputMethod::SetGeometry(int x, int y, int w, int h)
{
    md->m_iKeybaordX = x;
    md->m_iKeybaordY = y;
    md->m_iKeyboardWidth = w;
    md->m_iKeyboardHeight = h;
    md->keyboard->setGeometry(x, y, w, h);
}

void InputMethod::SetColor(const QString& backgroundColor, const QString& buttonColor,
                           const QString& textColor, const QString& pressedColor)
{
    md->keyboard->SetColor(backgroundColor, buttonColor, textColor, pressedColor);
}

void InputMethod::SetFontFamily(const QString &fontFamily)
{
    md->keyboard->SetFontFamily(fontFamily);
}

void InputMethod::SetFontPixelSize(const int& size)
{
    md->keyboard->SetFontPixelSize(size);
}

void InputMethod::SetPageSize(const int& size)
{
    md->keyboard->SetPageSize(size);
}
