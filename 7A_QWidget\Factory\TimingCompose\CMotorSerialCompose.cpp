#include "CMotorSerialCompose.h"
#include <QBoxLayout>
#include <QListView>
#include <QPainter>
#include <QDebug>
#include <QStyleFactory>

#include "PublicParams.h"
#include "PublicFunction.h"

CMotorSerialCompose::CMotorSerialCompose(QWidget *parent) : QWidget(parent)
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setFixedSize(1494, 884);
    this->setAttribute(Qt::WA_TranslucentBackground);
    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroup(), 0, Qt::AlignCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
    LoadQSS(this,":/qss/qss/default.qss");
}

CMotorSerialCompose::~CMotorSerialCompose()
{

}

void CMotorSerialCompose::SetMotorComboBoxList(const QStringList &strList)
{
    m_strMotorTextIDList = strList;
    for(int i=0; i<m_pComboBoxList.size(); i++)
    {
        m_pComboBoxList.at(i)->clear();
        m_pComboBoxList.at(i)->addItems(strList);
    }
}

void CMotorSerialCompose::ShowWithData(const QString &strData)
{
    qDebug() << "电机串行组合数据:" << strData;
    for(int i=0; i<m_pComboBoxList.size(); i++)
    {
        QComboBox *pComboBox = m_pComboBoxList.at(i);
        pComboBox->deleteLater();

        CLineEdit *pLineEdit = m_pLineEditList.at(i);
        pLineEdit->deleteLater();
    }

    m_pComboBoxList.clear();
    m_pLineEditList.clear();

    QStringList strList = strData.split(SPLIT_MOTOR);
    for(int i=0; i<strList.size(); i++)
    {
        QStringList one = strList.at(i).split(SPLIT_IN_CMD);
        if(one.isEmpty())
            continue;
        QString strCmdTextID = one.first();
        int index = _GetCmdTextIDIndex(strCmdTextID);
        one.pop_front(); //后面可能有多个"," 整机复位_257,123,456^258^259,123
        _AddOneLine(index, one.join(SPLIT_IN_CMD));
    }

    this->show();
}

void CMotorSerialCompose::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, gk_iBackTransparency));
    QWidget::paintEvent(pEvent);
}

void CMotorSerialCompose::_SlotBtnList()
{
    QPushButton *pBtn = dynamic_cast<QPushButton *>(sender());
    if(nullptr == pBtn)
        return;

    int index = pBtn->property("index").toInt();
    switch (index)
    {
    case 0: //确认
    {
        QStringList strDataList;
        for(int i=0; i<m_pComboBoxList.size(); i++)
        {
            QString strBoxText = m_pComboBoxList.at(i)->currentText();
            QString strParam = m_pLineEditList.at(i)->text();
            if(strParam.isEmpty())
                strDataList.push_back(strBoxText);
            else
                strDataList.push_back(strBoxText + SPLIT_IN_CMD + strParam);
        }
        emit SignalMotorComposeConfirm(strDataList.join(SPLIT_MOTOR));
        this->close();

        break;
    }
    case 1: //取消关闭
    {
        this->close();
        break;
    }
    case 2: //加一行
    {
        if(m_pComboBoxList.size() < 5)
            _AddOneLine();
        break;
    }
    case 3: //减一行
    {
        if(m_pComboBoxList.size() > 1)
        {
            QComboBox *pComboBox = m_pComboBoxList.takeLast();
            pComboBox->deleteLater();

            CLineEdit *pLineEdit = m_pLineEditList.takeLast();
            pLineEdit->deleteLater();
        }
        break;
    }
    default:
        break;
    }
}

int CMotorSerialCompose::_GetCmdTextIDIndex(const QString &strCmdTextID)
{
    if(!strCmdTextID.contains("_"))
        return m_strMotorTextIDList.indexOf(strCmdTextID);

    QStringList strList = strCmdTextID.split("_");
    if(strList.size() < 2)
        return -1;

    QString strID = strList.last();
    for(int i=0; i<m_strMotorTextIDList.size(); i++)
    {
        QStringList one = m_strMotorTextIDList.at(i).split("_");
        if(one.size() < 2)
            continue;
        if(strID == one.last())
            return i;
    }

    return -1;
}

QGroupBox *CMotorSerialCompose::_CreateGroup()
{
    QGroupBox *pGroupBox = new QGroupBox(this);
    pGroupBox->setWindowOpacity(1);
    pGroupBox->setFixedSize(800, 520);

    m_pBoxLayout = new QVBoxLayout;
    m_pBoxLayout->setMargin(0);
    m_pBoxLayout->setSpacing(10);
    _AddOneLine();

    QStringList strBtnNameList = {tr("确认"), tr("取消"), tr("加一组"), tr("减一组")};
    for(int i=0; i<4; i++)
    {
        QPushButton *pBtn = new QPushButton(strBtnNameList.at(i));
        pBtn->setFixedSize(120, 50);
        pBtn->setProperty("index", i);
        m_pBtnList.push_back(pBtn);
        connect(pBtn, &QPushButton::clicked, this, &CMotorSerialCompose::_SlotBtnList);
    }

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->addStretch(1);
    pBtnLayout->setSpacing(20);
    for(int i=0; i<m_pBtnList.size(); i++)
        pBtnLayout->addWidget(m_pBtnList.at(i));
    pBtnLayout->addStretch(1);

    QVBoxLayout *pMainLayout = new QVBoxLayout;
    pMainLayout->setMargin(0);
    pMainLayout->addStretch(1);
    pMainLayout->addLayout(m_pBoxLayout);
    pMainLayout->addSpacing(30);
    pMainLayout->addLayout(pBtnLayout);
    pMainLayout->addStretch(1);
    pGroupBox->setLayout(pMainLayout);
    return pGroupBox;
}

void CMotorSerialCompose::_AddOneLine(int iCHIndex, const QString &strParam)
{
    QComboBox *pComboBox = new QComboBox;
    pComboBox->setView(new QListView);
    pComboBox->setFixedSize(500, 50);
    pComboBox->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    pComboBox->setMaxVisibleItems(10);
    pComboBox->setStyle(QStyleFactory::create("Windows"));
    pComboBox->addItems(m_strMotorTextIDList);
    pComboBox->setCurrentIndex(iCHIndex);
    m_pComboBoxList.push_back(pComboBox);

    CLineEdit *pLineEdit = new CLineEdit(strParam);
    pLineEdit->setFixedSize(120, 50);
    m_pLineEditList.push_back(pLineEdit);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->addStretch(1);
    pLayout->addWidget(pComboBox);
    pLayout->addSpacing(20);
    pLayout->addWidget(pLineEdit);
    pLayout->addStretch(1);

    m_pBoxLayout->addLayout(pLayout);
}
