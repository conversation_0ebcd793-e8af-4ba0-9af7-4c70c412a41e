#ifndef CFACTORYWIDGET_H
#define CFACTORYWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-15
  * Description: 工厂模式
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QGroupBox>
#include <QStackedWidget>
#include "CVBtnTitleWidget.h"
#include "SystemPage/CSysFirstTitleWidget.h"

class CSetSerialPort;
class CMeltingCurve;
class CTimingCompose;
class CMotorCompose;
class CMotorDebug;
class CMachineDebug;
class CHrmWidget;
class CLightDebug;
class CRealFL;
class CDataManager;
class CSettingsWidget;
class CPyrolysis;
class CRunEnvWidget;

class CFactoryWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CFactoryWidget(QWidget *parent = nullptr);

    void GotoRunLog();

signals:
    void SignalReturn();

private slots:
    void _SlotVTitleChanged(int index);

private:
    QGroupBox *_CreateLeftGroup();
    void _InitWidget();
    void _InitLayout();

private:
    CSysFirstTitleWidget *m_pCSysTtileLabelWidget;
    QLabel *m_pBackgroundLabel;

    CVBtnTitleWidget *m_pCVBtnTitleWidget;
    CSetSerialPort *m_pCSetSerial;
    CMachineDebug *m_pCMachineDebug;
    CMotorDebug *m_pCMotorDebug;
    CMotorCompose *m_pCMotorCompose;
    CTimingCompose *m_pCTimingCompose;
    CRealFL *m_pCRealFL;
    CMeltingCurve *m_pCMeltingCurve;
    CHrmWidget *m_pHrmWidget;
    CLightDebug *m_pCLightDebug;
    CDataManager *m_pCDataManager;
    CPyrolysis* m_pCPyrolysis;
    CSettingsWidget *m_pCSettingsWidget;
    CRunEnvWidget *m_pCRunEnvWidget;
    QStackedWidget *m_pStackedWidget;
};

#endif // CFACTORYWIDGET_H
