#ifndef CSIXPARAMS_H
#define CSIXPARAMS_H

#include <QWidget>
#include <QGroupBox>
#include <QPushButton>

#include "CLabelLineEdit.h"

class CSixParams : public QWidget
{
    Q_OBJECT
public:
    explicit CSixParams(QWidget *parent = nullptr);
    ~CSixParams();

    void Show(int iRow, const QString &strRawParams);

protected:
    void paintEvent(QPaintEvent* pEvent) override;

signals:
    void SignalParamsConfirm(int iRow, const QString &strParams);

private slots:
    void _SlotConfirmBtn();
    void _SlotResetBtn();
    void _SlotCancelBtn();

private:
    QGroupBox *_CreateGroup();

private:
    QList<CLabelLineEdit *> m_pLineEditList;
    QPushButton *m_pConfirmBtn, *m_pResetBtn, *m_pCancelBtn;
    int m_iRow;
};

#endif // CSIXPARAMS_H
