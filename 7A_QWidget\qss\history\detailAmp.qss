QLabel
{
   color: #6B788F;
   font-size: 20px;
   font-family: "Source Han Sans CN";
   border: 0px solid red;
}

QLabel#ProjectResultLabel
{
   font-size: 26px;
}

QLabel#AmpCurveLabel
{
   color: #6B788F;
   font-size: 22px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 25px;
   background-color: rgba(61,120,229,0.04);
}

QLabel#TestResultLabel
{
    color: #3D78E5;
    font-size: 25px;
    font-family: "Source Han Sans CN";
    background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
        stop: 0 #FFF, stop: 0.5 rgba(89,144,255,122), stop: 1 #FFF);
}

QLineEdit
{
   color: #6B788F;
   font-size: 20px;
   font-family: "Source Han Sans CN";
   padding-left: 20px;
   border-radius: 25px;
   background-color: #F4F7FE;
}
QLineEdit:focus{ padding-left: 20px; }

QRadioButton
{
    color: #6B788F;
    font-family: "Source Han Sans CN";
    font-size: 20px;
}
QRadioButton:focus{outline: none;}
QRadioButton::indicator {
    width: 25px;
    height: 25px;
}
QRadioButton::indicator::unchecked {
    image: url(:/image/ico/history/radio-off.png);
}
QRadioButton::indicator::checked {
    image: url(:/image/ico/history/radio-on.png);
}

QCheckBox {
    color: #6B788F;
    font-family: "Source Han Sans CN";
    font-size: 20px;
}

QCheckBox:focus {
    outline: none;
}

QCheckBox::indicator {
    width: 25px;
    height: 25px;
}

QCheckBox::indicator:unchecked {
    image: url(:/image/ico/history/uncheck.png);
}

QCheckBox::indicator:checked {
image: url(:/image/ico/history/check.png);
}




QPushButton
{
   color: #FFF;
   font-size: 22px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 25px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #8490FF, stop: 1 #3D78E5);
}
QPushButton:pressed
{
   color: #FFF;
   font-size: 22px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 25px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #6670c7, stop: 1 #3361b8);
}
QPushButton:focus{outline: none;}


QTableWidget
{
    color: #353E4E;
    font-size: 20px;
    font-family: "Source Han Sans CN";
    selection-background-color: #60C8FF;
    alternate-background-color: #F2F2F2;
    border: 1px solid #D6DFE9;
    border-radius: 0px;
    background-color: #fff;
}

QHeaderView::section
{
    color: #353E4E;
    font-size: 20px;
    font-weight: 500;
    font-family:"Source Han Sans CN";
    border: 0px solid #D6DFE9;
    border-radius: 0px;
    background-color: #EAEDF6;
    height: 58px;
}

QPushButton#tabBtn
{
   color: #6B788F;
   font-size: 22px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 25px;
   background-color: rgba(61,120,229,0.1);
}

QComboBox
{
    font-size: 20px;
    color: #353E4E;
    font-family: "Source Han Sans CN";
    border: none;
    background-color: transparent;
}
QComboBox::drop-down
{
    subcontrol-origin: padding;
    subcontrol-position: top right;
    border-left: 0px solid red;
}
QComboBox::down-arrow
{
    width: 32px;
    height: 32px;
    image: url(:/image/ico/login/commod.png);
    padding: 0px 10px 0px 0px;
}
QComboBox QAbstractItemView
{
    color: #353E4E;
    font-size: 20px;
    font-family: "Source Han Sans CN";
    border: 0px solid darkgray;/*下拉列表的边线的粗细、实虚*/
    border-radius:0px;
    selection-background-color: #248CEB;/*下拉列表选中的行的背景色*/
}
QComboBox QAbstractItemView::item
{
    min-height: 50px;/*下拉列表的行高，也可以看做行距*/
    color: #353E4E;
    font-size: 20px;
    font-family: "Source Han Sans CN";
}

QLabel#TableReslutLabel
{
   color: #353E4E;
   font-size: 20px;
   font-family: "Source Han Sans CN";
}
