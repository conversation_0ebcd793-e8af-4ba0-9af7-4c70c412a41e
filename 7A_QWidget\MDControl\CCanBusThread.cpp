#include "CCanBusThread.h"
#include <QtMath>
#include <QDebug>
#include <QDataStream>

#include "PublicConfig.h"
#include "PublicFunction.h"

static int GetNumber(QByteArray byteData)
{
    if(2 != byteData.size())
        return 0;

    char ch = byteData[0];
    byteData[0] = byteData[1];
    byteData[1] = ch;

    int num = byteData.toHex().toInt(nullptr, 16);
    return num;
}

CCanBusThread *CCanBusThread::m_spIntance = nullptr;

CCanBusThread::CCanBusThread()
{
    m_bOpenCan = false;
    m_bHandling = true;
    m_iSendSeq = 0;
    m_pGetMutex = new QMutex;
    m_pReadMutex = new QMutex;
    m_iEmptySize = 0;
    for(int i=0; i<gk_iMachineCount; i++)
    {
        m_bReceivedList.push_back(false);
        m_iLoseTimesList.push_back(0);
        m_qLastSeqList.push_back(QByteArray());
        m_qLastCRCList.push_back(QByteArray());
        m_qBuffList.push_back(QByteArray());
    }

    connect(this, &CCanBusThread::SignalInitThread, this, &CCanBusThread::_SlotInitThread);
    connect(this, &CCanBusThread::SignalExitThread, this, &CCanBusThread::_SlotExitThread, Qt::BlockingQueuedConnection);
    connect(this, &CCanBusThread::SignalWriteCan, this, &CCanBusThread::_SlotWriteCan);
    connect(this, &CCanBusThread::SignalRemoveSeq, this, &CCanBusThread::_SlotRemoveSeq);
    connect(this, &CCanBusThread::SignalWriteByte2Can, this, &CCanBusThread::_SlotWriteByte2Can);
    connect(this, &CCanBusThread::SignalReopenCan, this, &CCanBusThread::_SlotReopenCan);

    m_pThread = new QThread;
    this->moveToThread(m_pThread);
    m_pThread->start();

    emit SignalInitThread();
}

CCanBusThread *CCanBusThread::GetInstance()
{
    if(nullptr == m_spIntance)
        m_spIntance = new CCanBusThread;
    return m_spIntance;
}

CCanBusThread::~CCanBusThread()
{
    m_bHandling = false;
    printf("%s\n", Q_FUNC_INFO);
}

void CCanBusThread::WriteCan(const SCanBusDataStruct &sSCanBusDataStruct)
{
    emit SignalWriteCan(sSCanBusDataStruct);
}

void CCanBusThread::_SlotWriteCan(SCanBusDataStruct sSCanBusDataStruct)
{
    QByteArray qFrame = _GetSendData(sSCanBusDataStruct, m_iSendSeq);

    qDebug()<<"can add send seq:"<<qFrame.mid(10, 2).toHex().toUpper();
    STSendStruct sSendStruct;
    sSendStruct.iMachineID = sSCanBusDataStruct.quMachineID;
    sSendStruct.iSendTimes = 0;
    sSendStruct.qSendByte = qFrame;
    m_iSendStructMap.insert(m_iSendSeq, sSendStruct);

    if(m_iSendSeq >= 65535)
        m_iSendSeq = 0;
    m_iSendSeq++;
}

QByteArray CCanBusThread::GetReciveCanMsg()
{
    QByteArray qFrame;
    if(!m_pGetMutex->tryLock(1000))
    {
        qDebug()<<Q_FUNC_INFO<<"获取锁失败";
        return qFrame;
    }

    if(m_qReceiveFrameList.isEmpty())
    {
        m_pGetMutex->unlock();
        return qFrame;
    }

    qDebug()<<Q_FUNC_INFO<<m_qReceiveFrameList.size();
    qFrame = m_qReceiveFrameList.takeFirst();
    m_pGetMutex->unlock();

    return qFrame;
}

QList<QByteArray> CCanBusThread::GetReciveCanMsgList()
{
    QList<QByteArray> qFrameList;
    if(!m_pGetMutex->tryLock(1000))
    {
        qDebug()<<Q_FUNC_INFO<<"获取锁失败";
        return qFrameList;
    }

    if(m_qReceiveFrameList.isEmpty())
    {
        m_pGetMutex->unlock();
        return qFrameList;
    }

    int iLen = m_qReceiveFrameList.size();
    qDebug()<<Q_FUNC_INFO<<iLen;
    if(iLen <= 10)
    {
        qFrameList = m_qReceiveFrameList;
        m_qReceiveFrameList.clear();
    }
    else
    {
        qFrameList = m_qReceiveFrameList.mid(0, 10);
        m_qReceiveFrameList = m_qReceiveFrameList.mid(10, iLen - 10);
    }
    m_pGetMutex->unlock();
    return qFrameList;
}

void CCanBusThread::ReOpenCan()
{
    //emit SignalReopenCan();
}

void CCanBusThread::_SlotInitThread()
{
    qDebug()<<Q_FUNC_INFO<<"CAN 线程ID:"<<QThread::currentThreadId();

    QString strMsg;
    m_pCanBusDevice = nullptr;
    m_pCanBusDevice = QCanBus::instance()->createDevice("socketcan", "can0", &strMsg);
    if(nullptr == m_pCanBusDevice)
    {
        m_bOpenCan = false;
        RUN_LOG("can打开失败,socketcan, can0:" + strMsg);
        return;
    }
    qDebug()<<Q_FUNC_INFO<<"can 打开成功: can0 1000*1000";

    m_pCanBusDevice->setConfigurationParameter(QCanBusDevice::BitRateKey, 250 * 1000);
    if(!m_pCanBusDevice->connectDevice())
    {
        qDebug()<<Q_FUNC_INFO<<"can connect error:"<<m_pCanBusDevice->error()<<m_pCanBusDevice->errorString();
        return;
    }

    m_bOpenCan = true;
    connect(m_pCanBusDevice, &QCanBusDevice::stateChanged, this, &CCanBusThread::_SlotCanStateChanged);
    connect(m_pCanBusDevice, &QCanBusDevice::errorOccurred, this, &CCanBusThread::_SlotCanBusError);
    connect(m_pCanBusDevice, &QCanBusDevice::framesReceived, this, &CCanBusThread::_SlotReadCanBus);

    m_pWriteTimer = new QTimer(this);
    connect(m_pWriteTimer, &QTimer::timeout, this, &CCanBusThread::_SlotWriteTimer);
    m_pWriteTimer->start(10);

    m_pReadTimer = new QTimer(this);
    connect(m_pReadTimer, &QTimer::timeout, this, &CCanBusThread::_SlotReadTimer);
    //m_pReadTimer->start(10);

    std::thread mythread(&CCanBusThread::_ThreadHandleReadBuff, this);
    mythread.detach();
}

void CCanBusThread::_SlotExitThread()
{

}

void CCanBusThread::_SlotReopenCan()
{
    m_pCanBusDevice->disconnectDevice();
    Delay_MSec(2000);
    bool ok = m_pCanBusDevice->connectDevice();
    qDebug()<<"重新打开CAN:"<<ok;
}

void CCanBusThread::_SlotCanStateChanged(QCanBusDevice::CanBusDeviceState state)
{
    qDebug()<<"can 状态变化:"<<state<<m_pCanBusDevice->errorString();
}

void CCanBusThread::_SlotCanBusError(QCanBusDevice::CanBusError eError)
{
    RUN_LOG(QString("can error:%1,%2").arg(eError).arg(m_pCanBusDevice->errorString()));
}

void CCanBusThread::_SlotReadCanBus()
{
    QVector<QCanBusFrame> qVec = m_pCanBusDevice->readAllFrames();
    m_pReadMutex->lock();
    m_qBusFrameVector.append(qVec.toList());
    m_pReadMutex->unlock();
}

void CCanBusThread::_ThreadHandleReadBuff()
{
    while(m_bHandling)
    {
        if(!m_qBusFrameVector.isEmpty())
        {
            m_pReadMutex->lock();
            for(int i=0; i<m_qBusFrameVector.size(); i++)
            {
                QCanBusFrame qBusFrame = m_qBusFrameVector.at(i);
                int iCanID = qBusFrame.frameId() >> 8;
                QByteArray payload = qBusFrame.payload();
                //qDebug()<<"can id:"<<iCanID<<payload<<m_iEmptySize;
                if(payload.isEmpty())
                {
                    m_iEmptySize++;
                    if(m_iEmptySize >= 10)
                    {
                        RUN_LOG("重启CAN");
                        System("ip link set can0 down");
                        System("ip link set can0 up");
                        QThread::msleep(100);
                        m_iEmptySize = 0;
                    }
                }
                else
                {
                    m_iEmptySize = 0;
                }

                if(iCanID >= 0 && iCanID < gk_iMachineCount)
                {
                    if(!payload.isEmpty())
                    {
                        m_bReceivedList[iCanID] = true;
                        m_qBuffList[iCanID].append(payload);
                    }
                }
				else
				{
					qDebug()<<"can id异常:"<<iCanID;
				}
            }
            m_qBusFrameVector.clear();
            m_pReadMutex->unlock();
        }

        for(int i=0; i<m_qBuffList.size(); i++)
        {
            if(false == m_bReceivedList.at(i))
                continue;

            m_bReceivedList[i] = false;
            QByteArray &qBuffByte = m_qBuffList[i];
            _ParseReadBuff(qBuffByte);
        }
        QThread::msleep(10);
    }
}

void CCanBusThread::_ParseReadBuff(QByteArray &readBuff)
{
    if(readBuff.length() < 22)
        return;

    while(readBuff.length() >= 22)
    {
        //qDebug()<<Q_FUNC_INFO<<__LINE__<<readBuff.toHex().toUpper()<<readBuff;

        //不能每4个字节遍历找头,当前面有很多无用数据会造成大量耗时
        int headIndex = readBuff.indexOf("@MDx");
        if(headIndex < 0)
        {
            readBuff.clear();
            return;
        }

        //if(readBuff.length() < MAX_PRINT_LEN)
        qDebug()<<"readbuff:"<<readBuff.toHex().toUpper()<<",head index:"<<headIndex;

        int iMachineID = readBuff.mid(headIndex+5,1).toHex().toInt(nullptr,16);

        QByteArray qReadSeq = readBuff.mid(headIndex+10,2);
        int iReadSeq = GetNumber(qReadSeq);
        //qDebug()<<"seq:"<<qReadSeq.toHex().toUpper()<<iReadSeq;

        QByteArray qReadLen = readBuff.mid(headIndex+18,2);
        int iPayloadLen = GetNumber(qReadLen);
        //qDebug()<<"payload len:"<<qReadLen.toHex().toUpper()<<iPayloadLen;
        if(iPayloadLen >= 1080)
        {
            qDebug()<<"payload长度非法:"<<qReadLen.toHex()<<iPayloadLen;
            int iLen = readBuff.size();
            readBuff = readBuff.right(iLen-4-headIndex);
            continue;
        }

        //处理接收长度大于22但没收完的情况
        if(iPayloadLen > 0)
        {
            int len = readBuff.length();
            QByteArray payload1 = readBuff.mid(headIndex+20,len-headIndex-20-2);
            if(payload1.length() < iPayloadLen)
            {
                qDebug()<<"指令未接收完:"<<iPayloadLen<<payload1.length()<<qReadSeq.toHex().toUpper()<<m_iLoseTimesList.at(iMachineID);
                if(m_iLoseTimesList.at(iMachineID) > 14)
                {
                    int iLen = readBuff.size();
                    readBuff = readBuff.right(iLen-4-headIndex);
                    m_iLoseTimesList[iMachineID] = 0;
                    return;
                }
                m_iLoseTimesList[iMachineID]++;
                return;
            }
        }

        QByteArray qReadCRC = readBuff.mid(headIndex+20+iPayloadLen,2);
        int iReadCRC = qReadCRC.toHex().toInt(nullptr,16);
        int iGetCRC = GetCRC16(readBuff.data()+headIndex,iPayloadLen+20,0);
        //qDebug()<<"CRC:"<<qReadCRC.toHex().toUpper()<<iReadCRC<<", calc CRC:"<<iGetCRC;
        if(iReadCRC != iGetCRC)
        {
            RUN_LOG(QString("%1# bad crc").arg(iMachineID + 1));
            qDebug()<<"head:"<<headIndex<<"total frame:"<<readBuff.toHex().toUpper();

            int iLen = readBuff.size();
            readBuff = readBuff.right(iLen-4-headIndex);
            continue;
        }

        m_iLoseTimesList[iMachineID] = 0;
        QByteArray frame = readBuff.mid(headIndex,iPayloadLen+22);
        if(frame.length() < 256)
            qDebug()<<"read frame data:"<<frame<<",seq:"<<iReadSeq;
        else
            qDebug()<<"read frame len:"<<frame.length()<<",seq:"<<iReadSeq;
        if(0x02 == frame[6])
        {
            qDebug()<<QString("can %1# get ack data,seq:%2").arg(iMachineID+1).arg(iReadSeq);
            emit SignalRemoveSeq(iReadSeq);
        }
        else
        {
            SCanBusDataStruct sSCanBusDataStruct;
            sSCanBusDataStruct.quMachineID = frame[5];
            sSCanBusDataStruct.quCmdID = 0x02;
            sSCanBusDataStruct.quObjectID = frame[7];
            sSCanBusDataStruct.quFrameSeq = iReadSeq;
            QByteArray ackArray = _GetSendData(sSCanBusDataStruct,quint16(iReadSeq));
            qDebug()<<QString("%1# can send ack").arg(sSCanBusDataStruct.quMachineID + 1)<<iMachineID<<ackArray;
            emit SignalWriteByte2Can(ackArray);

            if(m_qLastSeqList.at(iMachineID) == qReadSeq && m_qLastCRCList.at(iMachineID) == qReadCRC)
            {
                qDebug()<<"指令重复收到,seq:"<<qReadSeq.toHex().toUpper()<<",crc:"<<qReadCRC.toHex().toUpper();
            }
            else
            {
                if(m_pGetMutex->tryLock(1000))
                {
                    m_qLastSeqList[iMachineID] = qReadSeq;
                    m_qLastCRCList[iMachineID] = qReadCRC;

                    m_qReceiveFrameList.push_back(frame);
                    qDebug()<<Q_FUNC_INFO<<"m_qReceiveFrameList size"<<m_qReceiveFrameList.size();
                    m_pGetMutex->unlock();
                }
                else
                {
                    qDebug()<<Q_FUNC_INFO<<"获取锁失败";
                }
            }
        }

        //qDebug()<<"buff remove "<<readBuff.length()<<headIndex+iPayloadLen+22;
        readBuff = readBuff.remove(0,headIndex+iPayloadLen+22);
        //qDebug()<<"after remove buff:"<<readBuff.toHex().toUpper();
    }
}

void CCanBusThread::_SlotRemoveSeq(int iSeq)
{
    if(m_iSendStructMap.contains(iSeq))
        m_iSendStructMap.remove(iSeq);
}

void CCanBusThread::_SlotWriteTimer()
{
    if(m_iSendStructMap.isEmpty())
        return;

    if(!m_bOpenCan)
    {
        qDebug()<<"can未打开,无法发送数据";
        return;
    }

    QList<int> iDelList;
    for(auto it=m_iSendStructMap.constBegin(); it!=m_iSendStructMap.constEnd(); it++)
    {
        int iSeq = it.key();
        STSendStruct &sSendStruct = m_iSendStructMap[iSeq];
        int iMachineID = sSendStruct.qSendByte.at(5);
        if(0 != sSendStruct.iSendTimes)
        {
            int iMsc = sSendStruct.qSendTime.msecsTo(QDateTime::currentDateTime());
            if(iMsc < 1000)
                continue;

            if(sSendStruct.iSendTimes >= 3)
            {
                emit SignalACKOut(iMachineID);
                RUN_LOG(QString("%1# ack timeout, seq:%2").arg(iMachineID + 1).arg(iSeq));
                iDelList.push_back(iSeq);
                continue;
            }
            qDebug()<<QString("%1# no ack, resend times:%2, seq:%3").arg(iMachineID + 1).arg(sSendStruct.iSendTimes).arg(iSeq);
        }

        sSendStruct.iSendTimes++;
        sSendStruct.qSendTime = QDateTime::currentDateTime();
        _SlotWriteByte2Can(sSendStruct.qSendByte);
    }

    for(int i=0; i<iDelList.size(); i++)
        m_iSendStructMap.remove(iDelList.at(i));
}

void CCanBusThread::_SlotReadTimer()
{
#if 0
    if(!m_qBusFrameVector.isEmpty())
    {
        m_pReadMutex->lock();
        for(int i=0; i<m_qBusFrameVector.size(); i++)
        {
            QCanBusFrame qBusFrame = m_qBusFrameVector.at(i);
            int iCanID = qBusFrame.frameId() >> 8;
            qDebug()<<"can id:"<<iCanID<<qBusFrame.payload()<<qBusFrame.payload().toHex();
            if(iCanID >= 0 && iCanID < gk_iMachineCount)
            {
                m_qBuffList[iCanID].append(qBusFrame.payload());
            }
        }
        m_qBusFrameVector.clear();
        m_pReadMutex->unlock();
    }

    for(int i=0; i<m_qBuffList.size(); i++)
    {
        QByteArray &qBuffByte = m_qBuffList[i];
        _ParseReadBuff(qBuffByte);
    }
#endif
}

void CCanBusThread::_SlotWriteByte2Can(QByteArray qByteWrite)
{
    int iMachineID = qByteWrite.at(5);
    int iSize = qByteWrite.size();
    if(iSize < 1024)
        qDebug()<<QString("%1# write can size: %2").arg(iMachineID + 1).arg(iSize)<<"data:"<<qByteWrite;
    else
        qDebug()<<QString("%1# write can size: %2").arg(iMachineID + 1).arg(iSize);

    int iNums = iSize / 8;
    int iLeft = iSize % 8;

    QList<QByteArray> qWriteList;
    for(int i=0; i<iNums; i++)
        qWriteList.push_back(qByteWrite.mid(i * 8, 8));
    if(iLeft)
        qWriteList.push_back(qByteWrite.right(iLeft));

    QCanBusFrame qBusFrame;
    if(iMachineID >= gk_iMachineCount)
        qBusFrame.setFrameId(0xFF);
    else
        qBusFrame.setFrameId(qPow(2, iMachineID));
    for(int i=0; i<qWriteList.size(); i++)
    {
        qBusFrame.setPayload(qWriteList.at(i));
        if(m_pCanBusDevice)
        {
            if(!m_pCanBusDevice->writeFrame(qBusFrame))
                qDebug()<<QString("%1# write can frame error: %2").arg(iMachineID + 1).arg(m_pCanBusDevice->errorString())<<m_pCanBusDevice->error();
        }
    }
}

QByteArray CCanBusThread::_GetSendData(const SCanBusDataStruct &sSCanBusDataStruct, quint16 quSeq)
{
    //qDebug()<<"can send cmd,iMachineID:"<<sSCanBusDataStruct.quMachineID<<"iMethodID:"<<sSCanBusDataStruct.quMethonID;

    QByteArray qBlockByteArray;
    QDataStream qOutDataStream(&qBlockByteArray,QIODevice::ReadWrite);
    qOutDataStream.setByteOrder(QDataStream::LittleEndian);  // 设置xiao端格式
    qOutDataStream << quint16(0x4D40)
                   << quint16(0x7844)
                   << quint8(0x10)
                   << quint8(sSCanBusDataStruct.quMachineID)
                   << sSCanBusDataStruct.quCmdID
                   << sSCanBusDataStruct.quObjectID
                   << sSCanBusDataStruct.quSourceID
                   << sSCanBusDataStruct.quFormat;
    if(0x00 == sSCanBusDataStruct.quFrameSeq)
        qOutDataStream << quSeq;
    else
        qOutDataStream << sSCanBusDataStruct.quFrameSeq;
    qOutDataStream << sSCanBusDataStruct.quReserve;
    qOutDataStream << quint32(sSCanBusDataStruct.quMethonID);
    qOutDataStream << quint16(sSCanBusDataStruct.qbPayload.count());
    qBlockByteArray += sSCanBusDataStruct.qbPayload;
    qOutDataStream.device()->seek(20+sSCanBusDataStruct.qbPayload.count());
    quint16 iCrc16 = GetCRC16(qBlockByteArray.data(), qBlockByteArray.count(), 0);
    QByteArray byteCRC;
    byteCRC[0] = iCrc16 % 256;
    byteCRC[1] = iCrc16 / 256;
    qOutDataStream << byteCRC.toHex().toUShort(nullptr, 16);

    return qBlockByteArray;
}
