#include "kazakhkey.h"

#include "common/keyboardtoolbutton/keyboardtoolbutton.h"

KazakhKey::Kazakh<PERSON><PERSON>(const QString& name)
    : LanguageBase<PERSON><PERSON>(name)
{
    InitButtons();
}

void KazakhKey::InitButtons()
{
    QMap<int, QList<KeyBoardToolButton*> > map;
    QList<KeyBoardToolButton*> firstButtons, secondButtons, thirdButtons,fourButtons;

    firstButtons.append(new KeyBoardToolButton("ә","Ә","",""));
    firstButtons.append(new KeyBoardToolButton("і","І","",""));
    firstButtons.append(new KeyBoardToolButton("ң","Ң","",""));
    firstButtons.append(new KeyBoardToolButton("ғ","Ғ","",""));
    firstButtons.append(new KeyBoardToolButton("ұ","Ұ","",""));
    firstButtons.append(new KeyBoardToolButton("ү","Ү","",""));
    firstButtons.append(new KeyBoardToolButton("қ","Қ","",""));
    firstButtons.append(new KeyBoardToolButton("ө","Ө","",""));
    firstButtons.append(new KeyBoardToolButton("һ","Һ","",""));
    firstButtons.append(new KeyBoardToolButton("ъ","Ъ","",""));
    firstButtons.append(new KeyBoardToolButton("ё","Ё","",""));

    secondButtons.append(new KeyBoardToolButton("й","Й","",""));
    secondButtons.append(new KeyBoardToolButton("ц","Ц","",""));
    secondButtons.append(new KeyBoardToolButton("у","У","",""));
    secondButtons.append(new KeyBoardToolButton("к","К","",""));
    secondButtons.append(new KeyBoardToolButton("е","Е","",""));
    secondButtons.append(new KeyBoardToolButton("н","Н","",""));
    secondButtons.append(new KeyBoardToolButton("г","Г","",""));
    secondButtons.append(new KeyBoardToolButton("ш","Ш","",""));
    secondButtons.append(new KeyBoardToolButton("щ","Щ","",""));
    secondButtons.append(new KeyBoardToolButton("з","З","",""));
    secondButtons.append(new KeyBoardToolButton("х","Х","",""));

    thirdButtons.append(new KeyBoardToolButton("ф","Ф","",""));
    thirdButtons.append(new KeyBoardToolButton("ы","Ы","",""));
    thirdButtons.append(new KeyBoardToolButton("в","В","",""));
    thirdButtons.append(new KeyBoardToolButton("а","А","",""));
    thirdButtons.append(new KeyBoardToolButton("п","П","",""));
    thirdButtons.append(new KeyBoardToolButton("р","Р","",""));
    thirdButtons.append(new KeyBoardToolButton("о","О","",""));
    thirdButtons.append(new KeyBoardToolButton("л","Л","",""));
    thirdButtons.append(new KeyBoardToolButton("д","Д","",""));
    thirdButtons.append(new KeyBoardToolButton("ж","Ж","",""));
    thirdButtons.append(new KeyBoardToolButton("э","Э","",""));

    fourButtons.append(GetLeftCapsLockBtn());
    fourButtons.append(new KeyBoardToolButton("я","Я","",""));
    fourButtons.append(new KeyBoardToolButton("ч","Ч","",""));
    fourButtons.append(new KeyBoardToolButton("с","С","",""));
    fourButtons.append(new KeyBoardToolButton("м","М","",""));
    fourButtons.append(new KeyBoardToolButton("и","И","",""));
    fourButtons.append(new KeyBoardToolButton("т","Т","",""));
    fourButtons.append(new KeyBoardToolButton("ь","Ь","",""));
    fourButtons.append(new KeyBoardToolButton("б","Б","",""));
    fourButtons.append(new KeyBoardToolButton("ю","Ю","",""));
    fourButtons.append(GetRightCapsLockBtn());

    map.insert(0,firstButtons);
    map.insert(1,secondButtons);
    map.insert(2,thirdButtons);
    map.insert(4,fourButtons);

    SetButtonsMap(map);
    SetTranslate("Қытай тілі","Ағылшынша","математика","бос орын");
}
