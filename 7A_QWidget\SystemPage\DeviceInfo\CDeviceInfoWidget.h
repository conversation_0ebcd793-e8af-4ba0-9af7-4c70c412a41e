#ifndef CDEVICEINFOWIDGET_H
#define CDEVICEINFOWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-15
  * Description: 仪器信息
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QStackedWidget>
#include "CIPCInfoWidget.h"
#include "CPLCInfoWidget.h"
#include "SystemPage/CSysFirstTitleWidget.h"
#include "SystemPage/CSysSecondTitleWidget.h"

class CDeviceInfoWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CDeviceInfoWidget(QWidget *parent = nullptr);

signals:
    void SignalReturn();

private slots:
    void _SlotTitleChanged(int iTitle);

private:
    void _InitWidget();
    void _InitLayout();

private:
    CSysFirstTitleWidget *m_pCSysFirstTtileWidget;
    QLabel *m_pBackgroundLabel;

    CSysSecondTitleWidget *m_pCSysSecondTitleWidget;
    QStackedWidget *m_pStackedWidget;
    CIPCInfoWidget *m_pCIPCInfoWidget;
    CPLCInfoWidget *m_pCPLCInfoWidget;
};

#endif // CDEVICEINFOWIDGET_H
