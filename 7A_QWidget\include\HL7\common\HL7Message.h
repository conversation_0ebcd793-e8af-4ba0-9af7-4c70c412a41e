﻿#include "../interface/base.h"
#include "HL7SegmentCollection.h"
#include "../interface/IObjectList.h"
#include <string>
#include "../interface/IHL7Message.h"

#include "../interface/IDSC.h"
#include "../interface/IDSP.h"
#include "../interface/IECD.h"
#include "../interface/IECR.h"
#include "../interface/IEQP.h"
#include "../interface/IEQU.h"
#include "../interface/IERR.h"
#include "../interface/IINV.h"
#include "../interface/IISD.h"
#include "../interface/IMSA.h"
#include "../interface/IMSH.h"
#include "../interface/IOBR.h"
#include "../interface/IOBX.h"
#include "../interface/IPID.h"
#include "../interface/IQAK.h"
#include "../interface/IQRD.h"
#include "../interface/IQRF.h"
#include "../interface/IROL.h"

#include "../macros.h"

#ifndef _HL7_MESSAGE_H_
#define _HL7_MESSAGE_H_



class DSC;
class DSP;
class ECD;
class ECR;
class EQP;
class EQU;
class ERR;
class INV;
class ISD;
class MSA;
class MSH;
class OBR;
class OBX;
class PID;
class QAK;
class QRD;
class QRF;
class ROL;

class HL7Message : public IHL7Message
{
public:
	HL7Message();

	~HL7Message();

	DECLARE_OBJECTBASE

	BEGIN_IMPL_QUERYIF(HL7Message)
		IMPL_QUERYIF_BYPATH(IF_OBJECTBASE, IHL7Message, IObjectBase)
		END_IMPL_QUERYIF()

	/*
	*@brief 返回段集合
	*/
	HL7SegmentCollection GetHL7SegmentCollection();

	virtual NRET GetAllSegment(IObjectList* objectList);

	/*
	*@brief 返回消息字符串
	*/
	void GetMessageString(char** messageString);

	std::string GetMessageString();

	virtual void Build(){};
	/*
	*@brief 构建消息，输入参数为按顺序排列的字段集合
	*/
	virtual void Build(HL7SegmentCollection& segmentCollection);

	/*
	*@brief 构建消息，输入参数为按顺序排列的字段集合
	*/
	virtual void Build(IObjectList* objectList);

	/*
	*@brief 分解消息为段
	*/
	virtual void Parse(const char* messageStr);

	virtual MessageState GetMessageState();

	/*
	 *	\brief 返回消息类型
	 */
	virtual void GetMessageType(char** messageType);
	/*
	 *	\brief 返回消息触发事件
	 */
	virtual void GetTriggerEvent(char** triggerEvent);

protected:

	void SetMSA(MSA*& m_msa, IMSA* msa);

	void SetMSH(MSH*& m_msh, IMSH* msh);

	void SetDSC(DSC*& m_dsc, IDSC* dsc);

	void SetQAK(QAK*& m_qak, IQAK* qak);

	void SetERR(ERR*& m_err, IERR* err);

	void SetQRF(QRF*& m_qrf, IQRF* qrf);

	void SetQRD(QRD*& m_qrd, IQRD* qrd);

	void SetECD(ECD*& m_ecd, IECD* ecd);

	void SetECR(ECR*& m_ecr, IECR* ecr);

	void SetEQU(EQU*& m_equ, IEQU* equ);

	void SetEQP(EQP*& m_eqp, IEQP* eqp);

	void SetISD(ISD*& m_isd, IISD* isd);

	void SetINV(INV*& m_inv, IINV* inv);

	void SetOBR(OBR*& m_obr, IOBR* obr);

	void SetOBX(OBX*& m_obx, IOBX* obx);

	void SetPID(PID*& m_pid, IPID* pid);

	void SetDSP(DSP*& m_dsp, IDSP* dsp);

	void SetROL(ROL*& m_rol, IROL* rol);



	bool GetMSH(MSH* m_msh, IMSH* msh);

	bool GetMSA(MSA* m_msa, IMSA* msa);

	bool GetERR(ERR* m_err, IERR* err);

	bool GetQAK(QAK* m_qak, IQAK* qak);

	bool GetQRD(QRD* m_qrd, IQRD* qrd);

	bool GetQRF(QRF* m_qrf, IQRF* qrf);

	bool GetDSC(DSC* m_dsc, IDSC* dsc);

	bool GetECD(ECD* m_ecd, IECD* ecd);

	bool GetECR(ECR* m_ecr, IECR* ecr);

	bool GetEQU(EQU* m_equ, IEQU* equ);

	bool GetEQP(EQP* m_eqp, IEQP* eqp);

	bool GetISD(ISD* m_isd, IISD* isd);

	bool GetINV(INV* m_inv, IINV* inv);

	bool GetOBR(OBR* m_obr, IOBR* obr);

	bool GetOBX(OBX* m_obx, IOBX* obx);

	bool GetPID(PID* m_pid, IPID* pid);

	bool GetDSP(DSP* m_dsp, IDSP* dsp);

	bool GetROL(ROL* m_rol, IROL* rol);

protected:
	HL7SegmentCollection m_segmentCollection;

	std::string m_messageStr;
	
	std::string m_segmentSeparator;

	MessageState m_messageState;

	std::string m_messageType;

	std::string m_triggerEvent;
};

#endif
