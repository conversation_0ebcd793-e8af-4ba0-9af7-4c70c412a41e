﻿#ifndef CALCCT_H
#define CALCCT_H

#include "calcCT_global.h"
#include <QByteArray>
#include <QList>
#include <QPointF>
#include <QDebug>
#include <QFile>
#include <QProcess>
#include <math.h>

class CALCCT_EXPORT CalcCT
{
public:
     CalcCT();
    ~CalcCT();

    float CalcCtValue(const QList<qreal> data);

    float CalcCtValue(float iThread);

    QString GetAlgorithmLibraryVersion();

    void SetCtConfig(QString strCtConfig);

    QString GetCtPcrOutput();

    void GetSigmoidFeature(float &fBenchValue,int &iSectionLen);

    bool GetFitModle();

private:

    struct Section{
        Section()
        {
           iSectionLen = 0;
           iSectionStartX = 0;
        }
        int iSectionLen;
        int iSectionStartX;
        QPointF qPointf;
    };

    int m_iSectionLen;
    float m_fBenchValue;

    int fit_algorithm(const QList<QPointF> &raw, QList<QPointF> &fit,int iFitAlgorithm = 0);
    bool read_file(const QString name, QList<QByteArray> &qlb);

    int preprocess_algorithm();
    int  baseLine_algorithm(int iFitAlgorithm=0);
    void calc_line_ab(const QList<QPointF> &data, int start, int end);
    int derivatives(const QList<QPointF> &data, QList<QPointF> &diff1, QList<QPointF> &diff2);
    int find_baseLine_Points(const QList<QPointF> &data);
    void eRrors(const QString &msg);
    int calc_Thread_value();
    int find_Thread_Points(const QList<QPointF> &delta);
    quint8 min_data_index(const QList<QPointF> &data, int start, int end);
    float calc_CT_value();
    bool calc_fit_degree(int iFitAlgorithm=0);

    void ClearAllData();

    int CalcGradient(QList<QPointF> &data);
    void Smooth(QList<QPointF> &Sdata,int iSpan);
    float CalcOffset(const QList<QPointF> &Ddata,const QList<QPointF> &Sdata,int iStart, int iEnd);

private:
    enum{
        POLY,
        SIGMOID
    };

    QList<QPointF> data_raw,data_fit,data_baseLine,
                   data_delta,data_delta_6,data_delta_s,data_sigmoid_fit,
                   dataRaw,dataRaw_fit,data_noise,dataDelta_fit;

    int startPoint,endPoint;    //基线点
    int thread_startPoint,thread_endPoint,thread_endPoint_6,thread_endPoint_s;  //阈值点
    float m_fSigmoidThreaddef,m_f6Threaddef;
    float m_fThreaddef;
    float line_b,line_a;
    int m_iPcrEndPonit;
    bool m_bCanUseSigmoid;

    QString str;

    QStringList m_strCtConfigList;
    int m_iCtConfigLength;
    int m_iFindSigmoidPoint;

    float m_fSigmoidR2,m_f6R2;

    QString strRemark;
};


//CALCCT_EXPORT float calcCtValue(QList<QPointF> *data);

#endif // CALCCT_H
