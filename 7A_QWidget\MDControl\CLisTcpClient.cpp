#include "CLisTcpClient.h"
#include <QDebug>
#include "PublicConfig.h"

CLisTcpClient &CLisTcpClient::GetInstace()
{
    static CLisTcpClient lis;
    return lis;
}

CLisTcpClient::CLisTcpClient()
{
    m_bConnectOK = false;
    m_iPort = 0;
    m_strIP.clear();
    connect(this, &CLisTcpClient::SignalInitThread, this, &CLisTcpClient::_SlotInitThread);
    connect(&m_qThread, &QThread::finished, this, &CLisTcpClient::_SlotExitThread);
    connect(this, &CLisTcpClient::SignalConnectServer, this, &CLisTcpClient::_SlotConnectServer);
    connect(this, &CLisTcpClient::SignalWriteData, this, &CLisTcpClient::_SlotWriteData);

    this->moveToThread(&m_qThread);
    m_qThread.start();
    emit SignalInitThread();
}

CLisTcpClient::~CLisTcpClient()
{
    m_qThread.quit();
    m_qThread.wait();
}

bool CLisTcpClient::GetConnect()
{
    return m_bConnectOK;
}

void CLisTcpClient::SetConnect(bool bConnect)
{
    m_bConnectOK = bConnect;
}

void CLisTcpClient::SetIPAndPort(QString strIP, int iPort)
{
    qDebug()<<"set Lis ip port:"<<strIP<<iPort;
    m_strIP = strIP;
    m_iPort = iPort;
}

void CLisTcpClient::ConnectServer()
{
    emit SignalConnectServer();
}

void CLisTcpClient::WriteData(QByteArray qByteData)
{
    emit SignalWriteData(qByteData);
}

void CLisTcpClient::_SlotWriteData(QByteArray qByteData)
{
    qDebug()<<"Lis write:"<<qByteData;
    m_pSocket->write(qByteData);
}

void CLisTcpClient::_SlotExitThread()
{
    if(m_pCheckTimer)
    {
        m_pCheckTimer->stop();
        m_pCheckTimer->deleteLater();
    }

    printf("CLisTcpClient::_SlotExitThread()\n");
}

void CLisTcpClient::_SlotInitThread()
{
    m_pSocket = new QTcpSocket(this);
    connect(m_pSocket, &QTcpSocket::readyRead, this, &CLisTcpClient::_SlotReadMesg);
    connect(m_pSocket, &QTcpSocket::connected, this, &CLisTcpClient::_SlotConnected);
    connect(m_pSocket, &QTcpSocket::disconnected, this, &CLisTcpClient::_SlotDisconnect);
    connect(m_pSocket,SIGNAL(error(QAbstractSocket::SocketError)), this,SLOT(_SlotError(QAbstractSocket::SocketError)));

    m_pCheckTimer = new QTimer(this);
    connect(m_pCheckTimer, &QTimer::timeout, this, &CLisTcpClient::_SlotCheckTimer);
    m_pCheckTimer->start(3000);
}

void CLisTcpClient::_SlotReadMesg()
{
    QByteArray qByteData = m_pSocket->readAll();
    emit SignalReadData(qByteData);
}

void CLisTcpClient::_SlotConnected()
{
    qDebug()<<"Lis connect ok:"<<m_strIP<<m_iPort;
    m_bConnectOK = true;
    emit SignalLisState(m_bConnectOK);
}

void CLisTcpClient::_SlotDisconnect()
{
    qDebug()<<"Lis disconnect:"<<m_strIP<<m_iPort<<m_pSocket->errorString();
    m_bConnectOK = false;
    emit SignalLisState(m_bConnectOK);
}

void CLisTcpClient::_SlotError(QAbstractSocket::SocketError eCode)
{
    qDebug()<<"Lis error code:"<<eCode<<m_strIP<<m_iPort<<m_pSocket->errorString();
    m_bConnectOK = false;
}

void CLisTcpClient::_SlotCheckTimer()
{
    if(m_bConnectOK)
        return;

    bool bEth1 = CPublicConfig::GetInstance()->GetEthConnect();
    bool bWiFi = CPublicConfig::GetInstance()->GetWiFiConnect();
    bool bNetwork = bEth1 || bWiFi;

#ifdef Q_OS_WIN
    bNetwork = true;
#endif

   // qDebug()<<"Lis reconnect:"<<m_strIP<<m_iPort<<"eth1:"<<bEth1<<"wifi:"<<bWiFi;

    if(m_strIP.isEmpty() || m_iPort < 0 || m_iPort > 65535 || !bNetwork)
    {
        emit SignalLisState(false);
        return;
    }

    m_pSocket->abort();
    m_pSocket->connectToHost(m_strIP, m_iPort);
}

void CLisTcpClient::_SlotConnectServer()
{
    qDebug()<<"Lis connect server:"<<m_strIP<<m_iPort;
    m_pCheckTimer->start(3000);

    if(m_strIP.isEmpty() || m_iPort < 0 || m_iPort > 65535)  
        return;

    m_pSocket->abort();
    m_pSocket->connectToHost(m_strIP, m_iPort);
}
