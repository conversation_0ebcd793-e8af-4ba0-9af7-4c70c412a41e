#ifndef CPASSWORDLINEEDIT_H
#define CPASSWORDLINEEDIT_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-14
  * Description: 密码输入框
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>

class CPasswordLineEdit : public QLineEdit
{
    Q_OBJECT
public:
    explicit CPasswordLineEdit(QWidget *parent = nullptr);

    QString GetPassword() const;

private slots:
    void _SlotSeeBtn();

private:
    void _InitWidget();
    void _InitLayout();

private:
    bool m_bSee;
    QLabel *m_pIconLabel;
    QPushButton *m_pSeeBtn;
};

#endif // CPASSWORDLINEEDIT_H
