#include "CShowLogDetail.h"
#include <QPainter>
#include <QBoxLayout>
#include "PublicParams.h"
#include "PublicFunction.h"

CShowLogDetail::CShowLogDetail(QWidget *parent) : QWidget(parent)
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setAttribute(Qt::WA_TranslucentBackground);
    this->setAttribute(Qt::WA_DeleteOnClose);
    this->setFixedSize(parent ? parent->size() : G_QRootSize);
    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroup(), 0, Qt::AlignCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
    LoadQSS(this, ":/qss/qss/message.qss");
}

void CShowLogDetail::ShowTextList(const QStringList &strList)
{
    m_pTextBrower->clear();
    QString strData;
    for(int i=0; i<strList.size(); i++)
    {
        strData += "<p style='line-height:3px'>";
        strData += strList.at(i);
    }
    m_pTextBrower->setHtml(strData);
    m_pTextBrower->moveCursor(QTextCursor::Start);
    this->showNormal();
}

void CShowLogDetail::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, gk_iBackTransparency));
    QWidget::paintEvent(pEvent);
}

void CShowLogDetail::_SlotCloseButton()
{
    this->close();
}

QGroupBox *CShowLogDetail::_CreateGroup()
{
    QGroupBox *pGroupBox = new QGroupBox(this);
    pGroupBox->setWindowOpacity(1);
    pGroupBox->setFixedSize(770, 530);
    pGroupBox->setObjectName("DetailGroupBox");

    m_pCHLabelTitleWidget = new CHLabelTitleWidget(tr("详情"));

    m_pTextBrower = new QTextBrowser;
    m_pTextBrower->setFixedSize(pGroupBox->width() - 80, pGroupBox->height() - 170);

    m_pCloseButton = new QPushButton(tr("关闭"));
    m_pCloseButton->setFixedSize(140, 56);
    m_pCloseButton->setObjectName("CancelBtn");
    connect(m_pCloseButton, &QPushButton::clicked, this, &CShowLogDetail::_SlotCloseButton);

    QVBoxLayout* pLayout = new QVBoxLayout;
    pLayout->setContentsMargins(24, 10, 24, 24);
    pLayout->addWidget(m_pCHLabelTitleWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pTextBrower, 0, Qt::AlignCenter);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pCloseButton, 0, Qt::AlignCenter);
    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}
