#include "CQmlKeyboard.h"
#include "PublicParams.h"

CQmlKeybaord *CQmlKeybaord::m_spInstance = nullptr;

CQmlKeybaord::CQmlKeybaord(QObject *parent) : QObject(parent)
{

}

CQmlKeybaord::~CQmlKeybaord()
{

}

CQmlKeybaord* CQmlKeybaord::GetInstance()
{
    if(nullptr == m_spInstance)
        m_spInstance = new CQmlKeybaord;
    return m_spInstance;
}

void CQmlKeybaord::hideKeyboard()
{
    emit SignalHideKeyboard();
}

int CQmlKeybaord::getCurrentLanguage()
{
    return gk_iLanguage;
}

void CQmlKeybaord::showQuickWidget()
{
    emit SignalShowQucikWidget();
}

void CQmlKeybaord::setVersion(QString strVersion)
{
    emit SignalSetVersion(strVersion);
}

void CQmlKeybaord::SetKeyboardWidth(int iWidth)
{
    emit SignalSetKeyboardWidth(iWidth);
}

void CQmlKeybaord::SetKeyboardPosition(int x, int y, int iHeight, QLineEdit* pLineEdit)
{
    emit SignalSetKeyboardPosition(x, y, iHeight, pLineEdit);
}

void CQmlKeybaord::SetKeyboardParentWidget(QWidget *pWidget)
{
    emit SignalSetKeyboardParentWidget(pWidget);
}
