﻿#ifndef _HL7_SEGMENT_H_
#define _HL7_SEGMENT_H_

#include <string>
#include "../interface/base.h"
#include "HL7FieldCollection.h"
#include "HL7SegmentFactory.h"
#include "../interface/IHL7Segment.h"
#include "../StringUtil.h"
#include "../macros.h"
struct CE
{
	HL7Component id;
	HL7Component text;
};

struct TQ
{
	HL7Component startTime;
	HL7Component endTime;
};

class HL7Segment : public IHL7Segment
{
public:
	HL7Segment();
	virtual ~HL7Segment();

	/*DECLARE_OBJECTBASE

	BEGIN_IMPL_QUERYIF(HL7Segment)
	IMPL_QUERYIF_BYPATH(IF_OBJECTBASE, IHL7Segment, IObjectBase)
	IMPL_QUERYIF(IF_HL7SEGMENT, IHL7Segment)
	END_IMPL_QUERYIF()*/

	/*
	*@brief 返回字段集合
	*/
	HL7FieldCollection GetHL7FieldCollection();

	/*
	*@brief 返回段字符串
	*/
	virtual std::string GetSegmentString();

	void GetSegmentString(char** segmentStr);

	/*
	*@brief 构建段，输入参数为按顺序排列的字段集合
	*/
	virtual std::string Build(HL7FieldCollection fieldCollection);

	/*
	*@brief 分解段为字段
	*/
	virtual void Parse(const char* segmentStr, EncodingCharacters encodingChars);


	void SetSegmentID(const char* segmentID);

	std::string GetSegmentID();

	void GetSegmentID(char** segmentID);

	//HL7Segment& operator=(HL7Segment& segment);
protected:

	void CopyString(char** str, HL7Field& hl7Field);

	void CopyString(char** str, std::string& srcStr);

	void CopyString(char** str, HL7Component& hl7Component);

	void ParseCEStructField(HL7Field& field, CE& ceStruct);

	void ParseTQStructField(HL7Field& field, TQ& tqStruct);

	void BuildCEStruct(CE& ceStruct, HL7Field& field, int index);

	void BuildTQStruct(TQ& tqStruct, HL7Field& field, int index);

protected:

	HL7FieldCollection m_fieldCollection;

	std::string m_segmentStr;

	std::string m_fieldSeparator;

	std::string m_segmentID;

	std::size_t m_fieldSize;
};

#endif
