#ifndef CHARACTERBASEKEY_H
#define CHARACTERBASEKEY_H

#include <QWidget>

class KeyBoard;
class QToolButton;
class QStackedWidget;
class QSignalMapper;

class CharacterBaseKey : public QWidget
{
    Q_OBJECT
public:
    explicit CharacterBaseKey(KeyBoard *kb, QStackedWidget *swgt);

    ~CharacterBaseKey();

    KeyBoard *GetKeyBoard() const;

public slots:
    virtual void SlotSetChar(const QString &);

    virtual void SlotReturnBtnClicked();

    virtual void SlotEnterBtnClicked();

    virtual void SlotBackSpaceBtnClicked();

protected:
    void ConnectMapper(QToolButton *btn, bool justConnect = false, const QString &s = QString::Null());

    void SetMapping(QToolButton *btn, const QString &s = QString::Null());

private:
    class PrivateData;
    PrivateData *const md;
};

#endif // CHARACTERBASEKEY_H
