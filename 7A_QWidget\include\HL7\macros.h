﻿#ifndef MACROS_H
#define MACROS_H
//#include <winnt.h>
#ifdef WIN32
#include "windows.h"
#if _MSC_VER >=1600
#pragma execution_character_set("utf-8")
#endif
#else
#include<stdlib.h>
#include<stdio.h>
#endif

#define ENABLE_OBJECT_LOG_RELEASE

struct CounterBase
{
	CounterBase(){ m_dwCnt = 0; }
	volatile long m_dwCnt;
	unsigned long AddRef(){
	#ifdef _DEBUG
	#endif
#ifdef WIN32
		return InterlockedIncrement((volatile long*)&m_dwCnt);
#else
		return  __sync_add_and_fetch((volatile long*)&m_dwCnt, 1);
#endif

	};
	void Release(){
#ifdef WIN32
		InterlockedDecrement((volatile long *)&m_dwCnt);
#else
		 __sync_sub_and_fetch((volatile long*)&m_dwCnt, 1);
#endif
	};
};

#define DECLARE_OBJECTBASE\
	virtual unsigned int AddRef(); \
	virtual void Release(); \
	virtual void GetType(ObjectType& objType); \
	virtual IObjectBase* GetParent(); \
	virtual NRET SetParent(IObjectBase* parent); \
		protected:\
		IObjectBase* m_parentObject; \
		ObjectType m_objectType; \
		CounterBase m_counter; \
		public:

#define DECLARE_OBJECTBASE_INTL()\
		protected:\
		IObjectBase* m_parentObject; \
		ObjectType m_objectType; \
		CounterBase m_counter; \
		public:

#ifdef ENABLE_OBJECT_LOG_RELEASE
#define IMPLEMENT_OBJECTBASE(ClassName) \
	unsigned int ClassName::AddRef(){\
		return m_counter.AddRef();\
 	 }; \
	void ClassName::Release(){\
			m_counter.Release(); \
		if (m_counter.m_dwCnt == 0)\
			{\
				delete this; \
			}\
	}\
	void ClassName::GetType(ObjectType& objType)\
		{\
		objType = m_objectType; \
		}\
	IObjectBase* ClassName::GetParent()\
		{\
		if (m_parentObject != 0)\
			m_parentObject->AddRef(); \
			return m_parentObject; \
		}\
	NRET ClassName::SetParent(IObjectBase* parent)\
		{\
		if (m_parentObject != 0)\
			m_parentObject->Release(); \
			m_parentObject = parent; \
		if (m_parentObject != 0)\
			m_parentObject->AddRef(); \
			return N_OK; \
		}
#else
#define IMPLEMENT_OBJECTBASE(ClassName) \
	unsigned int ClassName::AddRef(){\
	return m_counter.AddRef();}; \
		void ClassName::Release(){\
			m_counter.Release(); \
			if (m_counter.m_count == 0)\
			{\
			delete this; \
			}}\
			void ClassName::GetType(ObjectType& objType)\
		{\
		objType = m_objectType; \
		}\
		IObjectBase* ClassName::GetParent()\
		{\
		if (m_parentObject != NULL)\
			m_parentObject->AddRef(); \
			return m_parentObject; \
		}\
		NRET ClassName::SetParent(IObjectBase* parent)\
		{\
		if (m_parentObject != NULL)\
			m_parentObject->Release(); \
			m_parentObject = parent; \
		if (m_parentObject != NULL)\
			m_parentObject->AddRef(); \
			return N_OK; \
		}
#endif

#ifdef ENABLE_OBJECT_LOG_RELEASE
#define IMPLEMENT_OBJECTBASE_INTL(ClassName) \
	unsigned int AddRef(){\
			return m_counter.AddRef();}; \
		void Release(){\
			m_counter.Release(); \
			if (m_counter.m_count == 0)\
			{\
			delete this; \
			}\
		}\
			void GetType(ObjectType& objType)\
		{\
		objType = m_objectType; \
		}\
		IObjectBase* GetParent()\
		{\
		if (m_parentObject != NULL)\
			m_parentObject->AddRef(); \
			return m_parentObject; \
		}\
		NRET SetParent(IObjectBase* parent)\
		{\
		if (m_parentObject != NULL)\
			m_parentObject->Release(); \
			m_parentObject = parent; \
		if (m_parentObject != NULL)\
			m_parentObject->AddRef(); \
			return N_OK; \
		}
#else
#define IMPLEMENT_OBJECTBASE_INTL(ClassName) \
	unsigned int AddRef(){\
			return m_counter.AddRef();}; \
		void Release(){\
			m_counter.Release(); \
			if (m_counter.m_count == 0)\
			{\
			delete this; \
			}}\
		void GetType(ObjectType& objType)\
		{\
		objType = m_objectType; \
		}\
		IObjectBase* GetParent()\
		{\
		if (m_parentObject != NULL)\
			m_parentObject->AddRef(); \
			return m_parentObject; \
		}\
		NRET SetParent(IObjectBase* parent)\
		{\
		if (m_parentObject != NULL)\
			m_parentObject->Release(); \
			m_parentObject = parent; \
		if (m_parentObject != NULL)\
			m_parentObject->AddRef(); \
			return N_OK; \
		}
#endif

#define BEGIN_IMPL_QUERYIF(ClassName)\
	virtual NRET QueryIF(IFID ifid, void** ppvObj)\
		{\
		switch (ifid)\
		{

#define IMPL_QUERYIF(ifid, iftype)\
		case ifid:\
		{\
		AddRef(); \
		*ppvObj = static_cast<iftype*>(this); \
		return N_OK; \
		}

#define IMPL_QUERYIF_BYPATH(ifid, ifsubtype, iftype)\
		case ifid:\
		{\
		AddRef(); \
		*ppvObj = static_cast<iftype*>(static_cast<ifsubtype*>(this)); \
		return N_OK; \
		}

#define END_IMPL_QUERYIF()\
		default:\
		{\
		*ppvObj = NULL; \
		return N_UNKNOW_IF; \
		}\
		}\
		return N_UNKNOW_IF; \
		}



#define DECLARE_MESSAGEBASE \
	IObjectBase* GetSender(); \
	virtual bool IsHandled(); \
	virtual void SetHandled(bool handled); \
	virtual void SetSender(IObjectBase* sender); \
	private:\
	bool m_handled; \
	IObjectBase* m_sender; \

#define IMPLEMENT_MESSAGEBASE(ClassName) \
	IObjectBase* ClassName::GetSender(){ return m_sender; }\
	bool ClassName::IsHandled(){ return m_handled; }\
	void ClassName::SetHandled(bool handled){ m_handled = handled; }\
	void ClassName::SetSender(IObjectBase* sender){ m_sender = sender; }


#define INIT_COMMON_PROPERTY(item, icp)\
		if (item != NULL)\
		{\
		item->m_mirror = icp.mirror; \
		item->m_flip = icp.flip; \
		item->m_visible = icp.visible; \
		item->m_locked = icp.locked; \
		item->m_color = icp.color; \
		item->m_isBorderShown = icp.isBorderShown; \
		item->m_isFill = icp.isFill; \
		item->SetFont(icp.font); \
		item->SetBorderPen(icp.borderPen); \
		item->SetFillBrush(icp.fillBrush); \
		item->m_rotateAngle = icp.rotateAngle; \
		item->m_simpleRotateAngle = 0; \
		item->m_id = icp.id; \
		item->SetAction(icp.action); \
		}

#endif
