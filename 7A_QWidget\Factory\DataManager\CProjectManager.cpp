#include "CProjectManager.h"
#include "DataManager/CModifyParam.h"
#include <QHeaderView>
#include <QBoxLayout>
#include "CMessageBox.h"
#include "CLotInfoDB.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "DataManager/CShowLogDetail.h"


CProjectManager::CProjectManager(QWidget *parent) : QWidget(parent) , m_strTipsText(tr("提示"))
{
    _InitWidget();
    _InitLayout();

    _UpdateTable();
}

CProjectManager::~CProjectManager()
{

}

void CProjectManager::_SlotListBtn()
{
    QPushButton *pBtn = dynamic_cast<QPushButton *>(sender());
    if(nullptr == pBtn)
        return;

    int index = pBtn->property("index").toInt();
    switch(index)
    {
    case 0:
    {
        _ModifyParam();

    }
        break;
    case 1: _ShowDetail(); break;
    case 2: _ImportProject(); break;
    case 3: _ExportProject(); break;
    default: break;
    }
}

void CProjectManager::showEvent(QShowEvent *pEvent)
{
    if(CPublicConfig::GetInstance()->GetCalcParam())
    {
        if(m_pBtnList.size() >= 4)
        {
            m_pBtnList.at(0)->show();
        }
    }
    else
    {
        if(m_pBtnList.size() >= 4)
        {
            m_pBtnList.at(0)->hide();
        }
    }
    QWidget::showEvent(pEvent);
}

void CProjectManager::hideEvent(QHideEvent *pEvent)
{
     QWidget::hideEvent(pEvent);
}
void CProjectManager::_ModifyParam()
{
    int iRow = m_pTableWidget->currentRow();
    if(iRow < 0 )
    {
        ShowInformation(this,m_strTipsText,tr("请先选择一行"));
        return;
    }
    QString strProjectName = m_pTableWidget->item(iRow, 2)->text();

    if(nullptr == m_pModifyDialog)
    {
        m_pModifyDialog = new CModifyParam;
        m_pModifyDialog->hide();
        //connect(m_pModifyDialog, &CModifyParam::signalUpdateSuccess, this, &CProjectManager::_UpdateTable);
    }
    m_pModifyDialog->SetProjectName(strProjectName);
    m_pModifyDialog->raise();
    m_pModifyDialog->show();
    m_pModifyDialog->activateWindow();
}
void CProjectManager::_ShowDetail()
{
    int iRow = m_pTableWidget->currentRow();
    if(iRow < 0)
    {
        ShowInformation(this, m_strTipsText,tr("请先选择一行"));
        return;
    }

    QString strProjectName = m_pTableWidget->item(iRow, 2)->text();
    SLotInfoStruct sLotInfo;
    CLotInfoDB::GetInstance()->GetLotInfoByShowName(strProjectName, sLotInfo);

    QStringList strInfoList;
    strInfoList << tr("货号: %1").arg(sLotInfo.strProjectCode);
    strInfoList << tr("项目名称: %1").arg(sLotInfo.strProjectShowName);
    strInfoList << tr("靶标: %1").arg(sLotInfo.strCurveName);
    strInfoList << tr("样本类型: %1").arg(CPublicConfig::GetInstance()->GetSampleTypeList(strProjectName).join(";"));
    strInfoList << tr("Ct范围: %1").arg(sLotInfo.strThresholdValue);

    strInfoList << tr("荧光串扰系数: %1").arg(sLotInfo.FlInterfereKPCR);
    strInfoList << tr("荧光阈值: %1").arg(sLotInfo.strFlThreshouldValue);
    strInfoList << tr("抬升阈值: %1").arg(sLotInfo.strUpliftThresholdValue);
    strInfoList << tr("一阶导数阈值: %1").arg(sLotInfo.strFirstDevThreshouldValue);
    strInfoList << tr("阳性质控Ct阈值: %1").arg(sLotInfo.strPQCCutoffValue);
    strInfoList << tr("规范化参数: %1").arg(sLotInfo.strStandardizationPCR);
    strInfoList << tr("扩增测试时间: %1").arg(sLotInfo.iTestTime);
    strInfoList << tr("熔解测试时间: %1").arg(sLotInfo.iHrmTime);

    // 添加熔解项目的阈值
    strInfoList << tr("熔解Tm参考值: %1").arg(sLotInfo.strWildTypeTmValue);
    strInfoList << tr("熔解Tm参考值范围: %1").arg(sLotInfo.strTempRangeValue);
    strInfoList << tr("熔解Rm阈值: %1").arg(sLotInfo.strRmThreshold);
    strInfoList << tr("熔解Tm区间阈值: %1").arg(sLotInfo.strTmRange);
    // 修改


    CShowLogDetail *pDetailWidget = new CShowLogDetail(this);
    pDetailWidget->ShowTextList(strInfoList);
}

void CProjectManager::_ImportProject()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("导入项目将会重启，确定导入项目吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    QString strDestPath = CPublicConfig::GetInstance()->GetLotInfoDBPath();
    QString strDBName = QFileInfo(strDestPath).fileName();

    QString strSrcPath = GetUDiskUpdateDir() + strDBName;
    QDir dir(GetUDiskDir());
    if(!dir.exists())
    {
        ShowInformation(this, tr("提示"), tr("请先插入U盘"));
        return;
    }

    if(!QFile::exists(strSrcPath))
    {
        ShowInformation(this, tr("提示"), tr("U盘中文件不存在"));
        return;
    }

    CopyQFile(strSrcPath, strDestPath);

    if(QFile::exists(strDestPath))
    {
        ShowSuccess(this, m_strTipsText, tr("项目导入成功，正在重启"));
        System("reboot");
    }
    else
    {
        ShowError(this, m_strTipsText, tr("项目导入失败"));
    }
}

void CProjectManager::_ExportProject()
{
    if(!UDiskExist(this))
        return;

    CreateDir(GetUDiskUpdateDir());
    QString strSrcPath = CPublicConfig::GetInstance()->GetLotInfoDBPath();
    QFileInfo info(strSrcPath);
    QString strDestPath = GetUDiskUpdateDir() + info.fileName();
    CopyQFile(strSrcPath, strDestPath);

    bool bExist = QFile::exists(strDestPath);

    ExportEndUmountUSB();

    if(bExist)
        ShowSuccess(this, m_strTipsText, tr("项目导出成功"));
    else
        ShowWarning(this, m_strTipsText, tr("项目导出失败"));
}

void CProjectManager::_UpdateTable()
{
    QList<QStringList> strAllList = CLotInfoDB::GetInstance()->GetAllCodeNameTarget();

    m_pTableWidget->clearContents();
    m_pTableWidget->setRowCount(strAllList.size());

    for(int i=0; i<strAllList.size(); i++)
    {
        QStringList strOneList = strAllList.at(i);
        strOneList.push_front(QString::number(i + 1));

        for(int j=0; j<strOneList.size(); j++)
        {
            QTableWidgetItem* pItem = new QTableWidgetItem;
            pItem->setText(strOneList.at(j));
            pItem->setTextAlignment(Qt::AlignCenter);
            m_pTableWidget->setItem(i, j, pItem);
        }
    }
}

void CProjectManager::_InitWidget()
{
    QStringList strTitleList = {tr("序号"), tr("货号"), tr("项目名称"), tr("靶标")};
    m_pTableWidget = new QTableWidget;
    m_pTableWidget->setColumnCount(strTitleList.size());
    m_pTableWidget->setHorizontalHeaderLabels(strTitleList);

    QHeaderView* pVerticalHeader = m_pTableWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(50);
    QHeaderView* pHorizontalHeader = m_pTableWidget->horizontalHeader();
    pHorizontalHeader->resizeSection(0, 80);
    pHorizontalHeader->resizeSection(1, 110);
    pHorizontalHeader->resizeSection(2, 350);
    pHorizontalHeader->setSectionResizeMode(3, QHeaderView::Stretch);

    m_pTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pTableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_pTableWidget->setShowGrid(true);
    m_pTableWidget->setFocusPolicy(Qt::NoFocus);

    m_pModifyDialog = new CModifyParam;
    m_pModifyDialog->hide();

    int iBtnWidth = 100;
    if(eLanguage_Spanish == gk_iLanguage)
        iBtnWidth = 120;
    else if(eLanguage_German == gk_iLanguage)
        iBtnWidth = 140;

    QStringList strBtnNameList = {tr("参数"), tr("详情"), tr("导入"), tr("导出")};
    for(int i=0; i<strBtnNameList.size(); i++)
    {
        QPushButton *pBtn =  new QPushButton(strBtnNameList.at(i));
        {
            if(0 == i)
            {
                pBtn->hide();
            }
        }
        pBtn->setFixedSize(iBtnWidth, 50);
        pBtn->setProperty("index", i);
        m_pBtnList.push_back(pBtn);
        connect(pBtn, &QPushButton::clicked, this, &CProjectManager::_SlotListBtn);
    }
}

void CProjectManager::_InitLayout()
{
    QHBoxLayout *pBottomLayout = new QHBoxLayout;
    pBottomLayout->setMargin(0);
    pBottomLayout->setSpacing(30);
    pBottomLayout->addStretch(1);
    for(int i=0; i<m_pBtnList.size(); i++)
        pBottomLayout->addWidget(m_pBtnList.at(i));
    pBottomLayout->addStretch(1);

    QVBoxLayout *pMainLayout = new QVBoxLayout;
    pMainLayout->setMargin(0);
    pMainLayout->addWidget(m_pTableWidget);
    pMainLayout->addSpacing(10);
    pMainLayout->addLayout(pBottomLayout);
    this->setLayout(pMainLayout);
}
