#include "longpressselector.h"
#include "ui_longpressselector.h"


class LongPressSelector::PrivateData
{
public:
    PrivateData()
    {

    }

    void InitButton();

    void ClearButton();

public:
    Ui::LongPressSelector *ui;

    QButtonGroup buttonBgp;
};

void LongPressSelector::PrivateData::InitButton()
{
    buttonBgp.addButton(ui->alternativekey_0, 0);
    buttonBgp.addButton(ui->alternativekey_1, 1);
    buttonBgp.addButton(ui->alternativekey_2, 2);
    buttonBgp.addButton(ui->alternativekey_3, 3);
    buttonBgp.addButton(ui->alternativekey_4, 4);
    buttonBgp.addButton(ui->alternativekey_5, 5);
    buttonBgp.addButton(ui->alternativekey_6, 6);
    buttonBgp.addButton(ui->alternativekey_7, 7);
    buttonBgp.addButton(ui->alternativekey_8, 8);
    buttonBgp.addButton(ui->alternativekey_9, 9);
}

void LongPressSelector::PrivateData::ClearButton()
{
    for(int i =0 ;i < buttonBgp.buttons().count();i++)
    {
        buttonBgp.buttons().at(i)->setText("");
    }
}

LongPressSelector::LongPressSelector(QWidget *parent)
    : QWidget(parent)
    , md(new PrivateData())
{
    md->ui = new Ui::LongPressSelector;
    md->ui->setupUi(this);

    md->InitButton();
}

LongPressSelector::~LongPressSelector()
{
    delete md;
}


QList<QAbstractButton*>  LongPressSelector::GetAllButtons()
{
    return md->buttonBgp.buttons();
}

void LongPressSelector::SetAlternativeText(QString str)
{
    md->ClearButton();

    QStringList strlist = str.split(",");

    for(int i = 0; i < strlist.count();i++)
    {
        if(i < md->buttonBgp.buttons().count())
        {
            md->buttonBgp.button(i)->setText(strlist.at(i));
        }
    }
}

