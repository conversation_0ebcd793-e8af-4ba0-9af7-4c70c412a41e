#include "turkishkey.h"

#include "common/keyboardtoolbutton/keyboardtoolbutton.h"

TurkishKey::<PERSON><PERSON><PERSON>(const QString& name) :
    LanguageBase<PERSON>ey(name)
{
    InitButtons();
}

void TurkishKey::InitButtons()
{
    QMap<int, QList<KeyBoardToolButton*> > map;
    QList<KeyBoardToolButton*> firstButtons, secondButtons, thirdButtons;

    firstButtons.append(new KeyBoardToolButton("q","Q","",""));
    firstButtons.append(new KeyBoardToolButton("w","W","",""));
    firstButtons.append(new KeyBoardToolButton("e","E","",""));
    firstButtons.append(new KeyBoardToolButton("r","R","",""));
    firstButtons.append(new KeyBoardToolButton("t","T","",""));
    firstButtons.append(new KeyBoardToolButton("y","Y","",""));
    firstButtons.append(new KeyBoardToolButton("u","U","ū,ú,ù,û","Ū,Ú,Ù,Û"));
    firstButtons.append(new KeyBoardToolButton("ı","I","",""));
    firstButtons.append(new KeyBoardToolButton("o","O","ō,ø,õ,ó,ò,œ,ô","Ō,Ø,Õ,Ó,Ò,Œ,Ô"));
    firstButtons.append(new KeyBoardToolButton("p","P","",""));
    firstButtons.append(new KeyBoardToolButton("ğ","Ğ","",""));
    firstButtons.append(new KeyBoardToolButton("ü","Ü","",""));

    secondButtons.append(new KeyBoardToolButton("a","A","â","Â"));
    secondButtons.append(new KeyBoardToolButton("s","S","ß,ś,š","Ś,Š"));
    secondButtons.append(new KeyBoardToolButton("d","D","",""));
    secondButtons.append(new KeyBoardToolButton("f","F","",""));
    secondButtons.append(new KeyBoardToolButton("g","G","",""));
    secondButtons.append(new KeyBoardToolButton("h","H","",""));
    secondButtons.append(new KeyBoardToolButton("j","J","",""));
    secondButtons.append(new KeyBoardToolButton("k","K","",""));
    secondButtons.append(new KeyBoardToolButton("l","L","",""));
    secondButtons.append(new KeyBoardToolButton("ş","Ş","",""));
    secondButtons.append(new KeyBoardToolButton("i","İ","ī,į,í,ì,ï,î","Į,Í,Ì,Ï,Î"));

    thirdButtons.append(GetLeftCapsLockBtn());
    thirdButtons.append(new KeyBoardToolButton("z","Z","",""));
    thirdButtons.append(new KeyBoardToolButton("x","X","",""));
    thirdButtons.append(new KeyBoardToolButton("c","C","ć,č","Ć,Č"));
    thirdButtons.append(new KeyBoardToolButton("v","V","",""));
    thirdButtons.append(new KeyBoardToolButton("b","B","",""));
    thirdButtons.append(new KeyBoardToolButton("n","N","",""));
    thirdButtons.append(new KeyBoardToolButton("m","M","",""));
    thirdButtons.append(new KeyBoardToolButton("ö","Ö","",""));
    thirdButtons.append(new KeyBoardToolButton("ç","Ç","",""));
    thirdButtons.append(GetRightCapsLockBtn());

    map.insert(0,firstButtons);
    map.insert(1,secondButtons);
    map.insert(2,thirdButtons);

    SetButtonsMap(map);
    SetTranslate("Çince","ingilizce","matematik","Uzay");
}

