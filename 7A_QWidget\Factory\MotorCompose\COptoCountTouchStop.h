#ifndef COPTOSTOP_H
#define COPTOSTOP_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-11-28
  * Description:
  * -------------------------------------------------------------------------
  * History: 电机组合-光耦计数触停
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QGroupBox>
#include <QPushButton>
#include "CLabelComboBox.h"
#include "CLabelLineEdit.h"

class COptoCountTouchStop : public QWidget
{
    Q_OBJECT
public:
    explicit COptoCountTouchStop(QWidget *parent = nullptr);

    void Show(int iRow, const QString &strRawParams);

protected:
    void paintEvent(QPaintEvent* pEvent) override;

signals:
    void SignalParamsConfirm(int iRow, const QString &strParams);

private slots:
    void _SlotConfirmBtn();
    void _SlotResetBtn();
    void _SlotCancelBtn();
    void _SlotStopParaChanged(int index);

private:
    QGroupBox *_CreateGroup();

private:
    CLabelComboBox *m_pCountType, *m_pMoveParam, *m_pStopParaType, *m_pCompensate;
    CLabelLineEdit *m_pCountParam, *m_pMoveDir, *m_pNSTEP;
    QPushButton *m_pConfirmBtn, *m_pResetBtn, *m_pCancelBtn;
    int m_iRow;
};

#endif // COPTOSTOP_H
