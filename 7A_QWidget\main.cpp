#include "MainWindow.h"
#include <QTextCodec>
#include <QApplication>
#include <QTranslator>
#include <QSharedMemory>

#include "Log/CMylog.h"
#include "CMyLogThread.h"
#include "PublicFunction.h"
#include "WFKeyboard/wfkeyboard.h"
#include "CConfigJson.h"

int gk_iLanguage = 0;
const QWidget* gk_pMainWindow = nullptr;

static void GFunExit()
{
    printf("app exit and kill 7CPDF\n");
#ifdef Q_OS_WIN
    QProcess::startDetached("taskkill /f /t /im 7CPDF.exe");
#else
    QProcess::startDetached("killall 7CPDF");
#endif
}

static void InstallKeyboardTranslation(QApplication *pApp)
{
    qDebug()<<"当前系统语言:"<<gk_iLanguage;
    QString strKeyboard, strTranslationPath;

    switch(gk_iLanguage)
    {
    case eLanguage_English:
        strKeyboard = "english";
        strTranslationPath = "7CAPP-English.qm";
        break;
    case eLanguage_Spanish:
        strKeyboard = "spanish";
        strTranslationPath = "7CAPP-Spanish.qm";
        break;
    case eLanguage_German:
        strKeyboard = "german";
        strTranslationPath = "7CAPP-German.qm";
        break;
    case eLanguage_Italian:
        strKeyboard = "italian";
        strTranslationPath = "7CAPP-Italian.qm";
        break;
    default:
        strKeyboard = "chinese";
        break;
    }

#ifdef __aarch64__
    WfKeyBoard *keyBoard = new WfKeyBoard();
    keyBoard->SetGeometry(460, 660, 900, 400);
    keyBoard->SetFontFamily("Source Han Sans CN");
    keyBoard->SetFontPixelSize(28);
    keyBoard->SetColor("#ECF5FC", "#fff", "#353E4E", "#868482");
    keyBoard->InstallKeyBoard(pApp, strKeyboard);
    qDebug()<<"输入法版本:"<<keyBoard->Version()<<keyBoard->GetSupportLanguages();
#endif

    if(!strTranslationPath.isEmpty())
    {
        strTranslationPath = QApplication::applicationDirPath() + "/Resources/" + strTranslationPath;
        qDebug()<<"翻译文件:"<<strTranslationPath<<QFile::exists(strTranslationPath);
        QTranslator *qtTranslator = new QTranslator;
        qtTranslator->load(strTranslationPath);
        qApp->installTranslator(qtTranslator);
    }
}

//连接设置保存在system.json,恢复出厂设置不删除
static void ChangeDevSetting()
{
    QVariant qDevVar = CConfigJson::GetInstance()->GetSystemValue("DevSettings");
    if(qDevVar.isNull())
    {
        qDevVar = CConfigJson::GetInstance()->GetConfigValue("DevSettings");
        CConfigJson::GetInstance()->SetSystemValue("DevSettings", qDevVar);
    }
}

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));

#ifdef Q_OS_WIN
    QSharedMemory qMemory(app.applicationName());
    if(!qMemory.create(1))
    {
        qDebug()<<"程序已启动,禁止启动多个,此进程将退出";
        return 0;
    }
#endif

    QString strAppDirPath = QApplication::applicationDirPath() + "/";
#ifdef Q_OS_WIN
    QProcess::execute("taskkill /f /t /im 7CPDF.exe");
    QProcess::startDetached(strAppDirPath + "7CStart.exe");
    QProcess::startDetached(strAppDirPath + "7CPDF.exe");
#else
    QProcess::startDetached(strAppDirPath + "7CStart");
    QProcess::execute("killall 7CPDF");
    QProcess::startDetached(strAppDirPath + "7CPDF");
#endif

    atexit(GFunExit);

    ChangeDevSetting();

    CMylog::GetInstance();

    gk_iLanguage = GetLanguange();

    qInstallMessageHandler(OutputMessage);

    InstallKeyboardTranslation(&app);

    qDebug()<<"主线程ID:"<<QThread::currentThreadId();
    QTime q1 = QTime::currentTime();
    MainWindow mw;
    mw.show();
    gk_pMainWindow = &mw;
    qDebug()<<"软件启动时间:"<<q1.msecsTo(QTime::currentTime());

#ifdef Q_OS_WIN
    QProcess::startDetached("taskkill /f /t /im 7CStart.exe");
#else
    QProcess::startDetached("killall 7CStart");
#endif

    emit CPublicConfig::GetInstance()->SignalAppStartEnd();
    emit CPublicConfig::GetInstance()->SignalReadPowerVersion();

    return app.exec();
}
