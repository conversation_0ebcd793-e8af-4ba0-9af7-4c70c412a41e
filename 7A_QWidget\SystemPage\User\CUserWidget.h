#ifndef CUSERWIDGET_H
#define CUSERWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-03
  * Description: 用户管理
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QPushButton>
#include <QTableWidget>

#include "CAddEditUserWidget.h"
#include "SystemPage/CSysFirstTitleWidget.h"

class CUserWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CUserWidget(QWidget *parent = nullptr);

protected:
    void showEvent(QShowEvent *pEvent) override;

signals:
    void SignalReturn();

private slots:
    void _SlotConfirm(bool bAddUser, const QStringList &strList);
    void _SlotAddBtn();
    void _SlotEditBtn();
    void _SlotDelBtn();

private:
    void _RefrashTable();
    void _LoadAllUser2Table();
    void _LoadOneUser2Table(QString strUser);
    void _AddList2Table(int iRow, QStringList strList);

private:
    void _InitWidget();
    void _InitLayout();

private:
    CSysFirstTitleWidget *m_pCSysTtileLabelWidget;
    QLabel *m_pBackgroundLabel;

    QTableWidget *m_pTableWidget;
    QPushButton *m_pReturnBtn, *m_pAddBtn, *m_pEditBtn, *m_pDelBtn;

    CAddEditUserWidget *m_pCAddEditUserWidget;

    QString m_strTipsText;
    QString m_strLastLoginUser;
};

#endif // CUSERWIDGET_H
