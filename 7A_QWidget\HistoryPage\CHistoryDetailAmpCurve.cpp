#include "CHistoryDetailAmpCurve.h"
#include <QBoxLayout>

#include "CLogDB.h"
#include "CProjectDB.h"
#include "CHistoryDB.h"
#include "CLotInfoDB.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "CMessageBox.h"
#include "CCheckUserWidget.h"
#include <QHash>

CHistoryDetailAmpCurve::CHistoryDetailAmpCurve(bool bHistoryMode, QWidget *parent)
    : QWidget(parent)
    , m_bHistoryMode(bHistoryMode)
    , m_strPositive(tr("阳性"))
    , m_strNegative(tr("阴性"))
    , m_strError(tr("无效"))
    , m_strNull(tr("/"))
{

    m_strColorList << "#0000FF" << "#00FF00" << "#FFD700" << "#FF0000";
    m_strColorList << "#6859c9" << "#5da983" << "#FFDAB9" << "#A52A2A";
    m_strICNameList << "IC" << "SC" << "RNaseP" << "β-actin";
    m_StrChannelNameList << "FAM1" << "CY51"<< "HEX1"<< "ROX1"<< "FAM2"<< "CY52"<< "HEX2"<< "ROX2";
    _InitWidget();
    _InitLayout();

    LoadQSS(this, ":/qss/qss/history/detailAmp.qss");

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalShowRawCurve, this, &CHistoryDetailAmpCurve::SlotShowRawCurve);
}

void CHistoryDetailAmpCurve::SlotShowRawCurve(bool bShowRawCurve)
{
    if(bShowRawCurve)
    {
        m_pRawRadioBtn->setVisible(true);
        m_pReviewCalcBtn->setVisible(true);
        m_pReviewPassBtn->setVisible(true);
        m_pTableWidget->setColumnHidden(4, true);
    }
    else
    {
        m_pRawRadioBtn->setVisible(false);
        m_pRawRadioBtn->setChecked(false);
        m_pNmzaRadioBtn->setChecked(true);
        m_pReviewCalcBtn->setVisible(false);
        m_pReviewPassBtn->setVisible(false);
        m_pTableWidget->setColumnHidden(4, false);
    }
}

void CHistoryDetailAmpCurve::ClearData()
{
    m_sResultInfo.Clear();
    m_sCardInfo.Clear();
    m_sLotInfo.Clear();
    m_sResultCtInfo.clear();
    _SetManualBtn(m_sResultInfo.strReview == "y",m_sResultInfo);
    m_pCustomPlot->clearGraphs();
    m_pCustomPlot->yAxis->setRange(0, 2000);
    m_pCustomPlot->replot();

    m_pTableWidget->clearContents();
    for(const auto& item :  m_pDotLineList)
    {
        item->setVisible(false);
    }
}

void CHistoryDetailAmpCurve::ClearFL()
{
    m_sResultCtInfo.clear();
    m_dFLMap.clear();
}

void CHistoryDetailAmpCurve::closeBtnHandle()
{
    //复位
    m_bShow = false;

    if(CPublicConfig::GetInstance()->GetShowRawCurve())
        m_pRawRadioBtn->setChecked(true);
    else
        m_pNmzaRadioBtn->setChecked(true);
}

void CHistoryDetailAmpCurve::SetReviewMode(bool bReview)
{
    if(bReview)
    {
        m_pRawRadioBtn->setVisible(false);

        m_pReviewCalcBtn->setVisible(false);
        m_pReviewPassBtn->setVisible(false);
        m_pTableWidget->setColumnHidden(4, true);
    }
}

void CHistoryDetailAmpCurve::ThresholdLlineShow(bool bShow)
{
    m_threshouldCheckBox->setChecked(bShow);
    emit m_threshouldCheckBox->clicked(bShow);
}

void CHistoryDetailAmpCurve::ShowNmzaData()
{
    if(!CPublicConfig::GetInstance()->GetShowRawCurve())
    {
        m_pRawRadioBtn->setVisible(false);
    }

    m_pRawRadioBtn->setChecked(false);
    m_pNmzaRadioBtn->setChecked(true);
    m_pCustomPlot->yAxis->setLabel(tr("荧光值(dR)"));
    _UpdateNmzaPlot();
}

void CHistoryDetailAmpCurve::ShowRawData()
{
    if(!CPublicConfig::GetInstance()->GetShowRawCurve())
        return;

    m_pRawRadioBtn->setChecked(true);
    m_pNmzaRadioBtn->setChecked(false);
    m_pCustomPlot->yAxis->setLabel(tr("荧光值(R)"));
    _UpdateRawPlot();
}

void CHistoryDetailAmpCurve::SwitchHole(int iHole)
{
    if(0 == iHole)
    {
        if(!m_pHoleBCheckBoxList.isEmpty())
        {
            m_pHoleACheckBoxList.at(0)->setChecked(true);
            emit m_pHoleACheckBoxList.at(0)->clicked(true);

            m_pHoleBCheckBoxList.at(0)->setChecked(false);
            emit m_pHoleBCheckBoxList.at(0)->clicked(false);
        }
    }
    else if(1 == iHole)
    {
        if(!m_pHoleACheckBoxList.isEmpty())
        {
            m_pHoleACheckBoxList.at(0)->setChecked(false);
            emit m_pHoleACheckBoxList.at(0)->clicked(false);

            m_pHoleBCheckBoxList.at(0)->setChecked(true);
            emit m_pHoleBCheckBoxList.at(0)->clicked(true);
        }
    }
    else
    {
        m_pHoleACheckBoxList.at(0)->setChecked(true);
        emit m_pHoleACheckBoxList.at(0)->clicked(true);

        m_pHoleBCheckBoxList.at(0)->setChecked(true);
        emit m_pHoleBCheckBoxList.at(0)->clicked(true);
    }
}

void CHistoryDetailAmpCurve::SetHistoryIDHandle(const SCardInfoStruct&  sCardInfo, const SResultInfoStruct& sResultInfo,const SLotInfoStruct& sLotInfo)
{
    m_sResultInfo = sResultInfo;
    m_sCardInfo = sCardInfo;
    m_sLotInfo = sLotInfo;

    UpdateTableWidget(sResultInfo,sLotInfo);
    UpdateTestReusltLabel(sResultInfo,sLotInfo);

    if(!CPublicConfig::GetInstance()->GetShowRawCurve())
    {
        m_pNmzaRadioBtn->setChecked(true);
        _UpdateNmzaPlot();
        m_pTableWidget->setColumnHidden(4, true);
        return;
    }

    if(m_pRawRadioBtn->isChecked())
        _UpdateRawPlot();
    if(m_pNmzaRadioBtn->isChecked())
        _UpdateNmzaPlot();

    m_pTableWidget->setColumnHidden(4, false);
}

void CHistoryDetailAmpCurve::UpdateTestReusltLabel(const SResultInfoStruct& sResultInfo,const SLotInfoStruct& sLotInfo)
{
    Q_UNUSED(sLotInfo);
    _SetManualBtn(m_sResultInfo.strReview == "y",sResultInfo);
    return;
}
void CHistoryDetailAmpCurve::_UpdateRawPlot()
{
    _SetThreCheckBoxShow(false);
    QList<QList<double>> dFLDataList;
    if(m_bHistoryMode)
    {
        if(!m_sCardInfo.strCardID.isEmpty())
        {
            QString strTestTime = m_sCardInfo.strTestTime;
            strTestTime.remove("-").remove(" ").remove(":");
            QString strFLID = m_sResultInfo.strCardID + "+" + strTestTime;
            CHistoryDB::GetInstance()->GetTestDataFromCardIDAddTestTime(strFLID, dFLDataList);
            if(dFLDataList.isEmpty())
            {
                qDebug()<<Q_FUNC_INFO<<"查询FLID"<<strFLID<<"数据为空";
            }
        }
        /*
        QStringList strRawList;
        if(!m_sCardInfo.strCardID.isEmpty())
        {
            QString strTestTime = m_sCardInfo.strTestTime;
            strTestTime.remove("-").remove(" ").remove(":");
            QStringList strColorList = gk_strColorNameList;
            QString strFLID = "";
            for (int i = 0;i < gk_iHoleCount;i++)
            {
                for (int j = 0;j < gk_iBGYRCount; j++)
                {
                    strFLID = m_sResultInfo.strCardID + "+" + strTestTime + "_" + QString::number(i) + "-" + strColorList.at(j);
                    QString strRaw;
                    CHistoryDB::GetInstance()->GetCtDataRaw(strFLID, strRaw);
                    strRawList.push_back(strRaw);
                }
            }
        }
        for(int i=0; i<strRawList.size(); i++)
        {
            QList<double> dList;
            QStringList strOneList = strRawList.at(i).split(",");
            for(int j=0; j<strOneList.size(); j++)
            {
                dList << strOneList.at(j).toDouble();
            }
            dFLDataList << dList;
        }
        */
    }
    else
    {
        for(int i=0; i<m_dFLMap.size(); i++)
            dFLDataList << m_dFLMap.at(i).values();
    }
    m_pCustomPlot->clearGraphs();
    _SetPlotData(dFLDataList);
}

void CHistoryDetailAmpCurve::_UpdateNmzaPlot()
{
    _SetThreCheckBoxShow(IsQCTest(m_sResultInfo.strMode) ? false : true);
    QStringList strDeltaList;
    int iCycleCount = 0;
    if(!m_sCardInfo.strCardID.isEmpty())
    {

        // 两个一起查，然后再分配 是取审核还是不审核的 数据；
        QString strTestTime = m_sCardInfo.strTestTime;
        strTestTime.remove("-").remove(" ").remove(":");
        QString strFLID = m_sResultInfo.strCardID + "+" + strTestTime;
        QStringList strFit,strFitReview;
        CHistoryDB::GetInstance()->GetCTDeltaFormFLID(strFLID, strFit,strFitReview);

        if(strFit.isEmpty() && strFitReview.isEmpty())
        {
            qDebug()<<Q_FUNC_INFO<<"查询FLID"<<strFLID<<"数据为空";
        }

        QStringList strReviewResultList = m_sResultInfo.strResult_Review.split(";");
        bool bReiew = false;
        for (int i = 0;i < gk_iHoleCount;i++)
        {
            for (int j = 0;j < gk_iBGYRCount; j++)
            {
                int index = j+4*i;
                if((m_sResultInfo.strReview == "m"||m_sResultInfo.strReview == "y")
                        &&  index < strReviewResultList.size())
                {
                    QString strResultReview = strReviewResultList.at(index);
                    if(strResultReview == "/" || strResultReview.isEmpty())
                    {
                        bReiew = false;
                    }
                    else
                    {
                        bReiew = true;
                    }
                }
                if(iCycleCount <= 0)
                {
                    CHistoryDB::GetInstance()->GetCTDeltaDataCount(strFLID,iCycleCount);
                }
                QString strDelta;
                if(bReiew && index < strFitReview.size())
                {
                    strDelta = strFitReview.at(index);
                }
                else if(!bReiew && index < strFit.size())
                {
                    strDelta = strFit.at(index);
                }
                strDeltaList.push_back(strDelta);
            }
        }

        /*
        bool bReiew = false;
        QString strTestTime = m_sCardInfo.strTestTime;
        strTestTime.remove("-").remove(" ").remove(":");
        QStringList strReviewResultList = m_sResultInfo.strResult_Review.split(";");
        QStringList strColorList = gk_strColorNameList;
        QString strFLID = "";
        for (int i = 0;i < gk_iHoleCount;i++)
        {
            for (int j = 0;j < gk_iBGYRCount; j++)
            {
                int index = j+4*i;
                strFLID = m_sResultInfo.strCardID + "+" + strTestTime + "_" + QString::number(i) + "-" + strColorList.at(j);
                if((m_sResultInfo.strReview == "m"||m_sResultInfo.strReview == "y")
                        &&  index < strReviewResultList.size())
                {
                    QString strResultReview = strReviewResultList.at(index);
                    if(strResultReview == "/" || strResultReview.isEmpty())
                    {
                        bReiew = false;
                    }
                    else
                    {
                        bReiew = true;
                    }
                }

                if(iCycleCount <= 0)
                {
                    CHistoryDB::GetInstance()->GetCTDeltaDataCount(strFLID,iCycleCount);
                }
                QString strDelta;
                CHistoryDB::GetInstance()->GetCTDelta(strFLID, strDelta,bReiew);
                strDeltaList.push_back(strDelta);
            }
        }
        */
    }

    QList<QList<double>> dFLDataList;
    for(int i=0; i<strDeltaList.size(); i++)
    {
        QList<double> dList;
        QStringList strOneList = strDeltaList.at(i).split(",");
        if(iCycleCount >= 5 && strOneList.size() >= iCycleCount - 5)
        {
            bool bshowCure = true;
            if( i < m_sResultCtInfo.size())
            {
                bshowCure = bShowNmzaCure(m_sResultCtInfo.at(i).m_strPCRResult,m_sResultCtInfo.at(i).m_strTestMode,m_sResultCtInfo.at(i).m_bControl);
            }
            for(int j=0; j<strOneList.size(); j++)
            {
                // 如果阴性 则归一化数据为空
                // 质控卡盒 阴性也不显示？               
                if(bshowCure)
                {
                    dList << strOneList.at(j).toDouble();
                }
                else
                {
                    dList << (std::rand() % 50)/100.0;
                }
            }
        }
        else
        {
            for(int j=0; j<iCycleCount; j++)
            {
                dList << (std::rand() % 50)/100.0;
            }

        }

        dFLDataList << dList;
    }
    m_pCustomPlot->clearGraphs();
    _SetPlotData(dFLDataList);
}

void CHistoryDetailAmpCurve::_SetPlotData(const QList<QList<double> > &dFLDataList)
{

    QStringList strNameList = m_sLotInfo.strCurveName.split(";");
    int size = qMin(dFLDataList.size(), strNameList.size());
    qDebug()<<Q_FUNC_INFO<<"fl size:"<<dFLDataList.size()<<"name size:"<<strNameList.size()<<strNameList;

    for(int i=0; i<strNameList.size(); i++)
    {
        QCPGraph *pGraph = m_pCustomPlot->graph(i);
        if(nullptr == pGraph)
        {
            _AddGraph(QColor(m_strColorList.at(i)), i, strNameList.at(i));
            _SetChannelCheckBox(m_strColorList.at(i),i,strNameList.at(i));
        }
    }

    for(int i=0; i<size; i++)
    {
        QVector<double> y = dFLDataList.at(i).toVector();
        QVector<double> x;
        for(int j=0; j<y.size(); j++)
            x.push_back(j+1);

        QCPGraph *pGraph = m_pCustomPlot->graph(i);
        if(nullptr == pGraph)
        {
            _AddGraph(QColor(m_strColorList.at(i)), i, strNameList.at(i));
            _SetChannelCheckBox(m_strColorList.at(i),i,strNameList.at(i));
        }
        m_pCustomPlot->graph(i)->setData(x, y);

    }

    _ChannelCheckBoxHandle();

    //if(m_bShow)
    m_pCustomPlot->replot();
}

void CHistoryDetailAmpCurve::UpdateTableWidget(const SResultInfoStruct& sResultInfo,const SLotInfoStruct& sLotInfo)
{
    _SetManualBtn(m_sResultInfo.strReview == "y",sResultInfo);

    QList<sResultCtInfo> sResultCtInfoList;
    bool bQCTest  = false;
    if(!IsQCTest(sResultInfo.strMode))
    {
        QStringList strTitleList = {tr("靶标"), tr("阈值"), tr("CT"), tr("结果"), tr("审核")};
        m_pTableWidget->setColumnCount(strTitleList.size());
        m_pTableWidget->setHorizontalHeaderLabels(strTitleList); // [1,5](@ref)
        m_pTableWidget->setRowCount(8);
        GetCtResultInfo(sResultInfo,sLotInfo,sResultCtInfoList);
        bQCTest = false;
    }
    else
    {
        bQCTest = true;
        QStringList strTitleList = {tr("靶标"), tr("参考范围"), tr("CT"), tr("质控")};
        m_pTableWidget->setColumnCount(strTitleList.size());
        m_pTableWidget->setHorizontalHeaderLabels(strTitleList); // [1,5](@ref)
        m_pTableWidget->setRowCount(8);
        GetQcTestCtResultInfo(sResultInfo,sLotInfo,sResultCtInfoList);
    }
    m_sResultCtInfo = sResultCtInfoList;
    {
        QList<sResultCtInfo> trueItems;
        QList<sResultCtInfo> falseItems;
        for (const auto& item : sResultCtInfoList) {
            if (item.m_bControl)
            {
                trueItems.append(item);
            }
            else if(!item.m_bNull)
            {
                falseItems.append(item);
            }
        }
        sResultCtInfoList = falseItems + trueItems;
    }

    UpdateTableWidgetHandle(m_pTableWidget,sResultCtInfoList);
    // 更新总结果
    QString strTestResult  = QObject::tr("测试结果：") + GetTestResultText(sResultInfo.strMode,sResultInfo.strResult,sLotInfo.strCurveName,bQCTest);
    _UpdateResultLabel(strTestResult,sResultInfo.strProjectName, m_sResultCtInfo);
}

void CHistoryDetailAmpCurve::_UpdateProjectLabel(const QString& strProjectName,const QList<sResultCtInfo>& sCtInfo)
{
    //针对GBS特殊项目
    if("GBS Test" == strProjectName)
    {
        //m_pProjectResultLabel->setVisible(true);
        QString strCtrlResult;
        QStringList strItemResultList;
        for(int i=0; i<sCtInfo.size(); i++)
        {
            sResultCtInfo sInfo = sCtInfo.at(i);
            qDebug()<<__FUNCTION__<<__LINE__<<sInfo.m_strCurveName<<sInfo.m_strPNIResult;
            if("0" == sInfo.m_strCurveName || sInfo.m_strCurveName.isEmpty())
            {
                continue;
            }
            if(IsCtrlTarget(sInfo.m_strCurveName))
            {
                strCtrlResult = sInfo.m_strPNIResult;
                continue;
            }
            strItemResultList << sInfo.m_strPNIResult;
        }

        QString strText = tr("B族链球菌：");
        bool bAllNegative = true;
        for(int i=0; i<strItemResultList.size(); i++)
        {
            if(m_strPositive == strItemResultList.at(i))
            {
                strText += QString("<font color=red>%1</font>").arg(m_strPositive);
                m_pProjectResultLabel->setText(strText);
                qDebug()<<__FUNCTION__<<__LINE__<<m_pProjectResultLabel->text();
                return;
            }
            if(m_strNegative != strItemResultList.at(i))
                bAllNegative = false;
        }

        if(m_strPositive == strCtrlResult && bAllNegative)
        {
            m_pProjectResultLabel->setText(strText + m_strNegative);
            qDebug()<<__FUNCTION__<<__LINE__<<m_pProjectResultLabel->text();
            return;
        }

        m_pProjectResultLabel->setText(strText + tr("测试异常"));
        return;
    }
}

void CHistoryDetailAmpCurve::_UpdateResultLabel(const QString &strTest,const QString& strProjectName,const QList<sResultCtInfo>& sCtInfo)
{
    m_pProjectResultLabel->clear();
    //m_pProjectResultLabel->setVisible(false);
    if(eTestDone != m_sResultInfo.iStatus)
    {
        return;
    }
    if(IsQCTest(m_sResultInfo.strMode))
    {
        //m_pProjectResultLabel->setVisible(true);
        m_pProjectResultLabel->setText(strTest);
        m_pProjectResultLabel->update();
        return;
    }
    _UpdateProjectLabel(strProjectName,sCtInfo);
    m_pProjectResultLabel->update();
}

void CHistoryDetailAmpCurve::PdfSpecialRequirement( bool bPdfImage)
{
    // 隐藏人工审核， 人工审核结果去掉
    // 加一个标志位 就可以将人工审核去掉
    m_bPdfImage = bPdfImage;

}

void CHistoryDetailAmpCurve::paintEvent(QPaintEvent *pEvent)
{
    QWidget::paintEvent(pEvent);
}

void CHistoryDetailAmpCurve::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;
    m_pCustomPlot->replot();

    if(!CPublicConfig::GetInstance()->GetShowRawCurve())
    {
        m_pRawRadioBtn->setChecked(false);
        m_pRawRadioBtn->setVisible(false);
        m_pNmzaRadioBtn->setChecked(true);
        m_pNmzaRadioBtn->setVisible(true);

        m_pReviewCalcBtn->setVisible(false);
        m_pReviewPassBtn->setVisible(false);

        m_pTableWidget->setColumnHidden(4, true);
    }
    else
    {
        m_pTableWidget->setColumnHidden(4, false);
    }

    QWidget::showEvent(pEvent);
}

void CHistoryDetailAmpCurve::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;
    m_pManualReviewWidget->close();
    QWidget::hideEvent(pEvent);
}

void CHistoryDetailAmpCurve::UpdateTableWidgetHandle(QTableWidget *pTableWidget, QList<CHistoryDetailAmpCurve::sResultCtInfo> &sInfolist)
{
    int nSize = sInfolist.size();
    pTableWidget->setRowCount(nSize);
    int iTabHeight = 60 + nSize * 58;
    pTableWidget->setFixedSize(540,iTabHeight);
    for(int i = 0 ; i < nSize; i++)
    {
        QWidget *pNameWidget = m_pTableWidget->cellWidget(i, 0);
        if(pNameWidget)
        {
            QLabel *label = qobject_cast<QLabel*>(pNameWidget);
            if (label) {
                label->setText(sInfolist.at(i).m_strCurveName);
                label->repaint();
            };
        }
        else
        {
            QLabel *pNameLabel = new QLabel;
            pNameLabel->setText(sInfolist.at(i).m_strCurveName);
            pNameLabel->setAlignment(Qt::AlignCenter);
            pNameLabel->setObjectName("TableReslutLabel");
            pNameLabel->setWordWrap(true); // 启用换行
            m_pTableWidget->setCellWidget(i, 0, pNameLabel);
        }


        QTableWidgetItem *pThresItem = new QTableWidgetItem;
        pThresItem->setText(sInfolist.at(i).m_strThreshould);
        pThresItem->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(i, 1, pThresItem);

        QTableWidgetItem *pCTItem = new QTableWidgetItem;
        pCTItem->setText(sInfolist.at(i).m_strCt);
        pCTItem->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(i, 2, pCTItem);

        QWidget *pResultItemWidget = m_pTableWidget->cellWidget(i, 3);
        if (pResultItemWidget)
        {
            QLabel *label = qobject_cast<QLabel*>(pResultItemWidget);
            if (label) {
                label->setText(sInfolist.at(i).m_strPNIResult);
                if(sInfolist.at(i).m_bPositive)
                {
                    label->setStyleSheet("color: red;");
                }
                else
                {
                    label->setStyleSheet("color: black;");
                }
                label->repaint();
            };
        }
        else
        {
            QLabel* pResultItemLabel = new QLabel;
            pResultItemLabel->setText(sInfolist.at(i).m_strPNIResult);
            if(sInfolist.at(i).m_bPositive)
            {
                pResultItemLabel->setStyleSheet("color: red;");
            }
            else
            {
                pResultItemLabel->setStyleSheet("color: black;");
            }
            pResultItemLabel->setAlignment(Qt::AlignCenter);
            pResultItemLabel->setObjectName("TableReslutLabel");
            m_pTableWidget->setCellWidget(i, 3 , pResultItemLabel);
        }

        if(!sInfolist.at(i).m_bQcTest)
        {
            QWidget *pReviewWidget = m_pTableWidget->cellWidget(i, 4);
            if (pReviewWidget)
            {
                QLabel *label = qobject_cast<QLabel*>(pReviewWidget);
                if (label) {
                    label->setText(sInfolist.at(i).m_strReview);
                    label->repaint();
                };
            }
            else
            {
                QLabel* pResultReviewItemLabel = new QLabel;
                pResultReviewItemLabel->setText(sInfolist.at(i).m_strReview);
                pResultReviewItemLabel->setAlignment(Qt::AlignCenter);
                pResultReviewItemLabel->setObjectName("TableReslutLabel");
                m_pTableWidget->setCellWidget(i, 4 , pResultReviewItemLabel);
            }
        }
    }
}

void CHistoryDetailAmpCurve::GetCtResultInfo(const SResultInfoStruct& sResultInfo,const SLotInfoStruct& sLotInfo,QList<CHistoryDetailAmpCurve::sResultCtInfo> &sInfolist)
{

    QStringList strNameList = sLotInfo.strCurveName.split(";");
    QStringList strCtInfoList = sResultInfo.strCTInfo.split(";");
    QStringList strCtInfoReviewList = sResultInfo.strCTInfo_Review.split(";");
    QStringList strResultList = sResultInfo.strResult.split(";");
    QStringList strReviewResultList = sResultInfo.strResult_Review.split(";");
    QString strReview = sResultInfo.strReview;

    // 如果名称内控 或者SC  IC开头则删除并设置合适宽度；
    // 然后将这个列移动到下面去
    // 写个结构体:

    auto getResult = [](const QString& strRes,QString& strResult,bool& bPositive)
    {
        bPositive = false;
        strResult = GetResultFormFlag(strRes);
        if("P" == strRes)
        {
            bPositive = true;
        }
        if(strResult == "")
        {
            strResult = "/";
        }
    };

    for(int i=0; i<strNameList.size(); i++)
    {
        sResultCtInfo sResultCtInfo;
        sResultCtInfo.m_index = i;
        sResultCtInfo.m_strCurveName = strNameList.at(i);
        sResultCtInfo.m_bQcTest = false;
        sResultCtInfo.m_strTestMode = sResultInfo.strMode;
        if("0" == strNameList.at(i) || strNameList.at(i).isEmpty())
        {
            sResultCtInfo.m_bNull = true;;
            sInfolist.push_back(sResultCtInfo);
            continue;
        }
        if(IsCtrlTarget(strNameList.at(i)))
        {
            sResultCtInfo.m_bControl = true;
        }

        QString strResultText = "/";
        QString strPcrResult;
        bool bPositive = false;
        bool bReiew = false;
        if((strReview == "m" || strReview == "y")
                && i < strReviewResultList.size())
        {
            QString strResultReview = strReviewResultList.at(i);
            if(strResultReview == "/")
            {
                if(i < strResultList.size())
                {
                    strPcrResult = strResultList.at(i);
                    getResult(strResultList.at(i),strResultText,bPositive);
                    bReiew = false;
                }
            }
            else
            {
                strPcrResult = strReviewResultList.at(i);
                getResult(strReviewResultList.at(i),strResultText,bPositive);
                bReiew = true;
            }
        }
        else
        {
            if(i < strResultList.size())
            {
                strPcrResult = strResultList.at(i);
                getResult(strResultList.at(i),strResultText,bPositive);
                bReiew = false;
            }
        }

        sResultCtInfo.m_strReview = bReiew?QString(tr("已审核")):"/";
        if(m_bPdfImage)
        {
            sResultCtInfo.m_strReview = "/";
        }

        sResultCtInfo.m_strPNIResult = strResultText;
        sResultCtInfo.m_bPositive = bPositive;
        sResultCtInfo.m_strPCRResult = strPcrResult;

        sResultCtInfo.m_strThreshould = "/";
        QString strCtInfo;
        if(bReiew && i < strCtInfoReviewList.size())
        {
            strCtInfo = strCtInfoReviewList.at(i);
        }
        else if(i < strCtInfoList.size() && !bReiew)
        {
            strCtInfo = strCtInfoList.at(i);
        }

        QStringList strCTInfoListTemp = strCtInfo.split(",");
        if(strCTInfoListTemp.size() >= 4 && strCTInfoListTemp.at(0) >= 0)
        {
            sResultCtInfo.m_strThreshould = strCTInfoListTemp.at(0);
        }

        sResultCtInfo.m_strCt = "/";
        if(strCTInfoListTemp.size()>=4 && strCTInfoListTemp.at(2) >= 0)
        {
            sResultCtInfo.m_strCt = bPositive ? strCTInfoListTemp.at(2) : "/";
        }
        sInfolist.push_back(sResultCtInfo);
    }
}

void CHistoryDetailAmpCurve::GetQcTestCtResultInfo(const SResultInfoStruct& sResultInfo,const SLotInfoStruct& sLotInfo,QList<CHistoryDetailAmpCurve::sResultCtInfo> &sInfolist)
{
    QStringList strNameList = sLotInfo.strCurveName.split(";");
    QStringList strPQCCutoffList =sLotInfo.strPQCCutoffValue.split(";");
    QStringList strCtInfoList = sResultInfo.strCTInfo.split(";");
    QStringList strResultList = sResultInfo.strResult.split(";");

    auto getResult = [](const QString& strRes,QString& strResult,bool& bPositive)
    {
        bPositive = false;
        strResult = GetResultFormFlag(strRes);
        if("P" == strRes || "F" == strRes || "I" == strRes)
        {
            bPositive = true;
        }
        if(strResult == "")
        {
            strResult = "/";
        }
    };

    for(int i=0; i<strNameList.size(); i++)
    {
        sResultCtInfo sResultCtInfo;
        sResultCtInfo.m_index = i;
        sResultCtInfo.m_strCurveName = strNameList.at(i);
        sResultCtInfo.m_bQcTest = true;
        sResultCtInfo.m_strTestMode = sResultInfo.strMode;
        if("0" == strNameList.at(i) || strNameList.at(i).isEmpty())
        {
            sResultCtInfo.m_bNull = true;;
            sInfolist.push_back(sResultCtInfo);
            continue;
        }
        if(IsCtrlTarget(strNameList.at(i)))
        {
            sResultCtInfo.m_bControl = true;
        }

        QString strResultText = "/";
        bool bPositive = false;
        QString strPcrResult;
        if(i < strResultList.size())
        {
            getResult(strResultList.at(i),strResultText,bPositive);
            strPcrResult = strResultList.at(i);
        }
        sResultCtInfo.m_strPCRResult = strPcrResult;
        sResultCtInfo.m_strReview = "/";
        sResultCtInfo.m_strPNIResult = strResultText;
        sResultCtInfo.m_bPositive = bPositive;

        sResultCtInfo.m_strThreshould = "/";
        QString strCtInfo;
        if(i < strCtInfoList.size())
        {
            strCtInfo = strCtInfoList.at(i);
        }

        QStringList strCTInfoListTemp = strCtInfo.split(",");
        if(i < strPQCCutoffList.size())
        {
            sResultCtInfo.m_strThreshould = strPQCCutoffList.at(i);
            // 这里得考虑 正和负？
            if(IsNegativeQcTest(sResultInfo.strMode) && !IsCtrlTarget(sResultCtInfo.m_strCurveName))
            {
                sResultCtInfo.m_strThreshould = "-";
            }
            sResultCtInfo.m_strThreshould.replace(",","~");
        }

        sResultCtInfo.m_strCt = "/";
        if(strCTInfoListTemp.size()>=4 && strCTInfoListTemp.at(2).toFloat() >= 0)
        {
            sResultCtInfo.m_strCt = strCTInfoListTemp.at(2);
        }
        sInfolist.push_back(sResultCtInfo);
    }
}

void CHistoryDetailAmpCurve::SetFLDataMap(const QList<QMap<double, double> > &dFLMap)
{
    m_dFLMap = dFLMap;

    if(m_bHistoryMode)
        return;

    if(!CPublicConfig::GetInstance()->GetShowRawCurve())
        return;

    if(m_pRawRadioBtn->isChecked())
    {
        QList<QList<double>> dFLDataList;
        for(int i=0; i<m_dFLMap.size(); i++)
            dFLDataList << m_dFLMap.at(i).values();
        _SetPlotData(dFLDataList);
    }
}

void CHistoryDetailAmpCurve::UpdateInfo(const SResultInfoStruct &sResult)
{
    m_sResultInfo = sResult;
    UpdateTestReusltLabel(m_sResultInfo,m_sLotInfo);
    UpdateTableWidget(m_sResultInfo,m_sLotInfo);
}

void CHistoryDetailAmpCurve::_ResetYRang(const QList<QList<double> > &dList)
{
    if(dList.isEmpty())
        return;

    double dMin = 0, dMax = 0;

    for(int i=0; i<dList.size(); i++)
    {
        for(int j=0; j<dList.at(i).size(); j++)
        {
            if( 0 == i && 0 == j )
            {
                dMin = dList.at(i).at(j);
            }
            dMin = qMin(dMin, dList.at(i).at(j));
            dMax = qMax(dMax, dList.at(i).at(j));
        }
    }
    double y1 = dMax + (dMax - 0) * 0.4;
    if(dMax <= 50)
    {
        y1 = 50;
    }
    double y0 = 0;
    if(dMin < 0)
    {
        y0 = dMin - (dMax - 0) * 0.1;
    }

    if(y0 == y1)
    {
        y0 = 0;
        y1 = 2000;
    }
    m_pCustomPlot->yAxis->setRange(y0, y1);
}

void CHistoryDetailAmpCurve::_InitCustomplot()
{
    m_pCustomPlot = new QCustomPlot;
    m_pCustomPlot->setFixedSize(900, 640);

    QFont font;
    font.setPixelSize(18);
    m_pCustomPlot->legend->setFont(font);
    m_pCustomPlot->legend->setSelectedFont(font);
    m_pCustomPlot->legend->setVisible(false);
    m_pCustomPlot->legend->setSelectableParts(QCPLegend::spItems);
    m_pCustomPlot->legend->setBorderPen(Qt::NoPen);
    m_pCustomPlot->legend->setWrap(4);
    m_pCustomPlot->legend->setFillOrder(QCPLegend::foColumnsFirst);

    m_pCustomPlot->axisRect()->insetLayout()->setInsetAlignment(0, Qt::AlignTop);

    m_pCustomPlot->xAxis->setLabel(tr("循环数"));
    m_pCustomPlot->yAxis->setLabel(tr("荧光值(dR)"));

    m_pCustomPlot->xAxis->setLabelFont(font);
    m_pCustomPlot->yAxis->setLabelFont(font);

    m_pCustomPlot->xAxis->setRange(0, 45);
    m_pCustomPlot->xAxis->ticker()->setTickCount(9);
    m_pCustomPlot->xAxis->setSubTicks(false);
    m_pCustomPlot->yAxis->setRange(0, 2000);
    m_pCustomPlot->yAxis->ticker()->setTickCount(9);
    m_pCustomPlot->yAxis->setSubTicks(false);

    for(int i=0; i<m_StrChannelNameList.size(); i++)
    {
        _AddGraph(QColor(m_strColorList.at(i)), i, m_StrChannelNameList.at(i));
    }

    for(int i=0; i<m_StrChannelNameList.size(); i++)
    {
        QCPItemLine* pDotLine = new QCPItemLine(m_pCustomPlot);
        QPen pen(Qt::DashLine);
        pen.setWidth(2);
        pen.setColor(m_strColorList.at(i));
        pDotLine->setPen(pen);
        pDotLine->setVisible(false);
        m_pDotLineList.push_back(pDotLine);

    }
}

void CHistoryDetailAmpCurve::_SetChannelCheckBox(const QString &qColor, int iChart, const QString &strChannelName)
{    
    int iHole = iChart/4;
    int index = iChart%4;
    QList<QCheckBox *>* pList;
    if(iHole == 0)
    {
        pList = &m_pHoleACheckBoxList;
    }
    else if(iHole == 1)
    {
        pList = &m_pHoleBCheckBoxList;
    }
    else
    {
        return;
    }
    if(index >= pList->size()-1)
    {
        return;
    }
    QString strStyleSheet = QString("QCheckBox { color: %1; }").arg(qColor);
    pList->at(index+1)->setStyleSheet(strStyleSheet);
    pList->at(index+1)->setText(strChannelName);
    if(strChannelName == "0")
    {
        pList->at(index+1)->setVisible(false);
        //pList->at(index+1)->setChecked(false);
    }
    else
    {
        pList->at(index+1)->setVisible(true);
    }
}

void CHistoryDetailAmpCurve::_AddGraph(const QColor &qColor, int iChart, const QString &strChartName)
{
    QPen pen;
    pen.setWidth(3);
    pen.setColor(qColor);
    m_pCustomPlot->addGraph();
    m_pCustomPlot->graph(iChart)->setPen(pen);
    m_pCustomPlot->graph(iChart)->setName(strChartName);
    m_pCustomPlot->graph(iChart)->setAntialiasedFill(true);

    if(strChartName == "0")
    {
        m_pCustomPlot->graph(iChart)->setVisible(false);
        //m_pCustomPlot->legend->item(iChart)->setVisible(false); // 隐藏对应的图例项
    }
    else
    {
        m_pCustomPlot->graph(iChart)->setVisible(true);
        //m_pCustomPlot->legend->item(iChart)->setVisible(false); // 显示对应的图例项
    }

}

void CHistoryDetailAmpCurve::_InitTableWidget()
{
    m_pTableWidget = new QTableWidget;
    m_pTableWidget->setFixedSize(540, 524);
    m_pTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pTableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_pTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pTableWidget->setWordWrap(true);
    m_pTableWidget->setFocusPolicy(Qt::NoFocus);
    m_pTableWidget->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTableWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTableWidget->setAlternatingRowColors(true);
    m_pTableWidget->setShowGrid(false);

    QStringList strTitleList = {tr("靶标"), tr("阈值"), tr("CT"), tr("结果"), tr("审核")};
    m_pTableWidget->setColumnCount(strTitleList.size());
    m_pTableWidget->setHorizontalHeaderLabels(strTitleList);
    m_pTableWidget->setRowCount(8);
    QHeaderView* pVerticalHeader = m_pTableWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(58);

    QHeaderView* pHorizontalHeader = m_pTableWidget->horizontalHeader();
    if(eLanguage_Italian == gk_iLanguage)
    {
        pHorizontalHeader->resizeSection(0, 135);
        pHorizontalHeader->resizeSection(1, 80);
        pHorizontalHeader->resizeSection(2, 80);
        pHorizontalHeader->resizeSection(3, 115);
        //pHorizontalHeader->setSectionResizeMode(4, QHeaderView::Stretch);
        pHorizontalHeader->setStretchLastSection(true);
    }
    else if(eLanguage_German == gk_iLanguage)
    {
        pHorizontalHeader->resizeSection(0, 140);
        pHorizontalHeader->resizeSection(1, 100);
        pHorizontalHeader->resizeSection(2, 90);
        pHorizontalHeader->setSectionResizeMode(3, QHeaderView::Stretch);
        pHorizontalHeader->setSectionResizeMode(4, QHeaderView::Stretch);
    }
    else if(eLanguage_Spanish == gk_iLanguage)
    {
        pHorizontalHeader->resizeSection(0, 140);
        pHorizontalHeader->resizeSection(1, 85);
        pHorizontalHeader->resizeSection(2, 85);
        pHorizontalHeader->setSectionResizeMode(3, QHeaderView::Stretch);
        pHorizontalHeader->setSectionResizeMode(4, QHeaderView::Stretch);
    }
    else if(eLanguage_English == gk_iLanguage)
    {
        pHorizontalHeader->resizeSection(0, 140);
        pHorizontalHeader->resizeSection(1, 120);
        pHorizontalHeader->resizeSection(2, 85);
        pHorizontalHeader->setSectionResizeMode(3, QHeaderView::Stretch);
        pHorizontalHeader->setSectionResizeMode(4, QHeaderView::Stretch);
    }
    else
    {
        pHorizontalHeader->setSectionResizeMode(0, QHeaderView::Stretch);
        pHorizontalHeader->setSectionResizeMode(1, QHeaderView::Stretch);
        pHorizontalHeader->resizeSection(2, 80);
        pHorizontalHeader->resizeSection(3, 80);
        pHorizontalHeader->resizeSection(4, 80);
    }
    pHorizontalHeader->setDisabled(false);
}

QWidget *CHistoryDetailAmpCurve::_InitChannelWidget()
{

    QCheckBox* pCheckBoxTemp = nullptr;
    QWidget *pContainerWidget = new QWidget();
    QHBoxLayout *pCheckBoxLayout = new QHBoxLayout(pContainerWidget);
    QString strChannelName;
    QString strChannelColor;

    if( m_strColorList.size() < 8 || m_StrChannelNameList.size() < 8)
    {
        return pContainerWidget;
    }
    for (int j = 1 ; j < 3; j ++)
    {
        for(int i = 0 ; i < 5; i ++)
        {
            int index = 0;
            index = (j-1)*4+i-1;
            if(0 == i)
            {
                strChannelColor = "#353E4E";
                strChannelName = QString(tr("孔-%1")).arg(j);
            }
            else
            {
                strChannelName = m_StrChannelNameList.at(index);
                strChannelColor = m_strColorList.at(index);
            }
            pCheckBoxTemp = new QCheckBox(strChannelName);
            pCheckBoxTemp->setChecked(true);
            pCheckBoxTemp->setProperty("index",QString::number(i));
            pCheckBoxTemp->setProperty("hole",j==1 ? "A" : "B");
            connect(pCheckBoxTemp,&QCheckBox::clicked,this,&CHistoryDetailAmpCurve::_SlotChannelCheckBox);
            j==1 ? m_pHoleACheckBoxList.push_back(pCheckBoxTemp) : m_pHoleBCheckBoxList.push_back(pCheckBoxTemp);
        }
    }

    m_threshouldCheckBox = new QCheckBox(tr("阈值线"));
    m_threshouldCheckBox->setChecked(true);
    m_threshouldCheckBox->setProperty("hole", "C");
    connect(m_threshouldCheckBox,&QCheckBox::clicked,this,&CHistoryDetailAmpCurve::_SlotChannelCheckBox);
    m_threshouldCheckBox->setVisible(false);

    pCheckBoxLayout->setMargin(0);
    pCheckBoxLayout->setSpacing(0);
    pCheckBoxLayout->addStretch(1);



    QGridLayout *pHoleGridLayout = new QGridLayout;
    for (int i = 0 ; i < m_pHoleACheckBoxList.size() ; i++)
    {
        pHoleGridLayout->addWidget(m_pHoleACheckBoxList.at(i), 0, i);
    }

    for (int i = 0 ; i < m_pHoleBCheckBoxList.size() ; i++)
    {
        pHoleGridLayout->addWidget(m_pHoleBCheckBoxList.at(i), 1, i);
    }

    // 设置每列之间的水平间距（可选）
    pHoleGridLayout->setColumnMinimumWidth(0, 20); // 设置第一列的最小宽度为100像素
    pHoleGridLayout->setColumnMinimumWidth(1, 20); // 第二列最小宽度为20像素
    pHoleGridLayout->setColumnMinimumWidth(2, 6); // 第三列最小宽度为20像素
    pHoleGridLayout->setColumnMinimumWidth(3, 6); // 第三列最小宽度为20像素
    pHoleGridLayout->setColumnMinimumWidth(4, 6); // 第三列最小宽度为20像素

    pHoleGridLayout->setHorizontalSpacing(12); // 这一行可以调整所有列之间的间隔
    pCheckBoxLayout->addLayout(pHoleGridLayout);
    pCheckBoxLayout->addStretch(1);
    pCheckBoxLayout->addWidget(m_threshouldCheckBox);
    pCheckBoxLayout->addStretch(1);

    int fixedWidth = 1000;  // 根据需要修改宽度
    int fixedHeight = 100; // 根据需要修改高度
    pContainerWidget->setFixedSize(fixedWidth, fixedHeight);

    for(int i=0; i<m_StrChannelNameList.size(); i++)
    {
        _SetChannelCheckBox(m_strColorList.at(i),i,m_StrChannelNameList.at(i));
    }
    // 添加布局到容器中
    pContainerWidget->setLayout(pCheckBoxLayout);

    return pContainerWidget;
}

void CHistoryDetailAmpCurve::_InitWidget()
{
    _InitCustomplot();
    _InitTableWidget();

    int iTabWidth = 256 , iBtnWidth = 150;
    if(eLanguage_Italian == gk_iLanguage)
        iTabWidth = 276;
    if(eLanguage_Spanish == gk_iLanguage)
        iTabWidth = 276;
    if(eLanguage_German == gk_iLanguage)
        iBtnWidth = 165;

    m_pPcrTabBtn = new QPushButton(tr("扩增曲线"));
    m_pPcrTabBtn->setFixedSize(iTabWidth,50);
    m_pPcrTabBtn->setObjectName("tabBtn");
    m_pPcrTabBtn->setEnabled(false);

    m_pRawRadioBtn = new QRadioButton(tr("原始数据"));
    m_pRawRadioBtn->setChecked(true);
    connect(m_pRawRadioBtn, &QRadioButton::clicked, this, &CHistoryDetailAmpCurve::_SlotRawRadioBtn);

    m_pNmzaRadioBtn = new QRadioButton(tr("归一化"));
    m_pNmzaRadioBtn->setChecked(false);
    connect(m_pNmzaRadioBtn, &QRadioButton::clicked, this, &CHistoryDetailAmpCurve::_SlotNmzaRadioBtn);

    m_PcrButton_group =  new QButtonGroup();
    m_PcrButton_group->addButton(m_pRawRadioBtn);
    m_PcrButton_group->addButton(m_pNmzaRadioBtn);


    m_pReviewCalcBtn = new QPushButton(tr("人工审核"));
    m_pReviewCalcBtn->setFixedSize(iBtnWidth, 50);
    connect(m_pReviewCalcBtn,&QPushButton::clicked, this, &CHistoryDetailAmpCurve::_SlotReviewBtn);

    m_pReviewPassBtn = new QPushButton(tr("审核通过"));
    m_pReviewPassBtn->setFixedSize(iBtnWidth, 50);
    connect(m_pReviewPassBtn,&QPushButton::clicked, this, &CHistoryDetailAmpCurve::_SlotReviewConfirmBtn);

    m_pManualReviewWidget = new CManualReviewWidget;
    connect(m_pManualReviewWidget,&CManualReviewWidget::SignalReviewConfirm,this,&CHistoryDetailAmpCurve::_ReviewConfirm);

    m_pProjectResultLabel = new QLabel(this);
    m_pProjectResultLabel->setFixedSize(520, 50);
    m_pProjectResultLabel->setObjectName("ProjectResultLabel");
    m_pProjectResultLabel->setAlignment(Qt::AlignCenter);
    m_pProjectResultLabel->setWordWrap(true); // 启用换行
}

void CHistoryDetailAmpCurve::_InitLayout()
{
    QHBoxLayout *pCalcLayout = new QHBoxLayout;
    pCalcLayout->setMargin(0);
    pCalcLayout->setSpacing(0);
    pCalcLayout->addSpacing(30);
    pCalcLayout->addWidget(m_pReviewCalcBtn);
    pCalcLayout->addStretch(1);
    pCalcLayout->addWidget(m_pReviewPassBtn);
    pCalcLayout->addSpacing(30);

    QVBoxLayout *pReviewLayout = new QVBoxLayout;
    pReviewLayout->setMargin(0);
    pReviewLayout->setSpacing(0);
    pReviewLayout->addWidget(m_pTableWidget);
    pReviewLayout->addSpacing(50);
    pReviewLayout->addWidget(m_pProjectResultLabel, 0, Qt::AlignHCenter);
    pReviewLayout->addSpacing(50);
    pReviewLayout->addLayout(pCalcLayout);
    pReviewLayout->addStretch(1);

    QHBoxLayout *pRadioBtnLayout = new QHBoxLayout;
    pRadioBtnLayout->setMargin(0);
    pRadioBtnLayout->setSpacing(12);
    pRadioBtnLayout->addStretch(1);
    pRadioBtnLayout->addWidget(m_pRawRadioBtn);
    pRadioBtnLayout->addSpacing(60);
    pRadioBtnLayout->addWidget(m_pNmzaRadioBtn);
    pRadioBtnLayout->addStretch(1);

    QVBoxLayout *pPlotLayout = new QVBoxLayout;
    pPlotLayout->setMargin(0);
    pPlotLayout->setSpacing(10);
    pPlotLayout->addWidget(m_pPcrTabBtn, 0, Qt::AlignHCenter);
    pPlotLayout->addLayout(pRadioBtnLayout);
    pPlotLayout->addWidget(m_pCustomPlot);
    pPlotLayout->addWidget(_InitChannelWidget());


    QHBoxLayout *pDataLayout = new QHBoxLayout;
    pDataLayout->setMargin(0);
    pDataLayout->setSpacing(0);
    pDataLayout->addLayout(pPlotLayout);
    pDataLayout->addStretch(1);
    pDataLayout->addLayout(pReviewLayout);


    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(12);

    pLayout->addLayout(pDataLayout);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}

void CHistoryDetailAmpCurve::_ChannelCheckBoxHandle()
{
    // 读取有哪些选中了 进行显示曲线  图例显示 虚线显示

    QList<QList<double> > dFLDataList;
    int min = qMin(m_pCustomPlot->graphCount()+1,m_pHoleACheckBoxList.size());
    for(int i = 1; i < min; i++)
    {

        if(!m_pHoleACheckBoxList.at(i)->isChecked() || m_pCustomPlot->graph(i-1)->name() == "0")
        {
            m_pCustomPlot->graph(i-1)->setVisible(false);
            //m_pCustomPlot->legend->item(i-1)->setVisible(false); // 隐藏对应的图例项
            m_pDotLineList.at(i-1)->setVisible(false);
        }
        else
        {
            QList<double> tempList;
            QSharedPointer<QCPGraphDataContainer> data = m_pCustomPlot->graph(i-1)->data();
            for (auto it = data->constBegin(); it != data->constEnd(); ++it) {
                double value = it->value; // Y 值（value）
                tempList.append(value);
            }
            dFLDataList.push_back(tempList);
            m_pCustomPlot->graph(i-1)->setVisible(true);
            //m_pCustomPlot->legend->item(i-1)->setVisible(true); // 隐藏对应的图例项
            m_pDotLineList.at(i-1)->setVisible(m_threshouldCheckBox->isChecked()&&m_pNmzaRadioBtn->isChecked());
        }

    }
    for(int i = 1; i < min; i++)
    {
        int index = i-1+4;
        if(index >= m_pCustomPlot->graphCount())
        {
            // 全部隐藏
            for(int i = 0 ; i < m_pHoleBCheckBoxList.size() ; i++)
            {
                m_pHoleBCheckBoxList.at(i)->setVisible(false);
            }
            break;
        }
        else
        {
            m_pHoleBCheckBoxList.at(0)->setVisible(true);
        }
        if(!m_pHoleBCheckBoxList.at(i)->isChecked() || m_pCustomPlot->graph(index)->name() == "0")
        {
            m_pCustomPlot->graph(i-1+4)->setVisible(false);
            //m_pCustomPlot->legend->item(index)->setVisible(false); // 隐藏对应的图例项
            m_pDotLineList.at(i-1+4)->setVisible(false);
        }
        else
        {
            QList<double> tempList;
            QSharedPointer<QCPGraphDataContainer> data = m_pCustomPlot->graph(i-1+4)->data();
            for (auto it = data->constBegin(); it != data->constEnd(); ++it) {
                double value = it->value; // Y 值（value）
                tempList.append(value);
            }
            dFLDataList.push_back(tempList);
            m_pCustomPlot->graph(i-1+4)->setVisible(true);
            //m_pCustomPlot->legend->item(index)->setVisible(false); // 隐藏对应的图例项
            m_pDotLineList.at(i-1+4)->setVisible(m_threshouldCheckBox->isChecked()&&m_pNmzaRadioBtn->isChecked());
        }
    }
    _ResetYRang(dFLDataList);
    _SetThreshouldLineValue();
    m_pCustomPlot->replot();
}

void CHistoryDetailAmpCurve::_HideCheckBox()
{
    for(int i = 1 ; i < m_pHoleACheckBoxList.size() ; i++)
    {
        m_pHoleACheckBoxList.at(i)->setVisible(false);
    }
    for(int i = 1 ; i < m_pHoleBCheckBoxList.size() ; i++)
    {
        m_pHoleBCheckBoxList.at(i)->setVisible(false);
    }
}

void CHistoryDetailAmpCurve::_SetThreshouldLineValue()
{
    int rowCount = m_sResultCtInfo.size();
    for (int row = 0; row < rowCount; ++row)
    {
        float threshould = m_sResultCtInfo.at(row).m_strThreshould.toFloat();
        if(threshould <= 0.001)
        {
            m_pDotLineList.at(row)->setVisible(false);
        }
        m_pDotLineList.at(row)->start->setCoords(m_pCustomPlot->xAxis->range().lower, threshould);
        m_pDotLineList.at(row)->end->setCoords(m_pCustomPlot->xAxis->range().upper, threshould);
    }
}

void CHistoryDetailAmpCurve::_SetThreCheckBoxShow(bool bShow)
{
    m_threshouldCheckBox->setVisible(bShow);
}

void CHistoryDetailAmpCurve::_SlotRawRadioBtn()
{
    if(m_bShow)
    {
        m_pCustomPlot->yAxis->setLabel(tr("荧光值(R)"));
        _UpdateRawPlot();
    }

}

void CHistoryDetailAmpCurve::_SlotNmzaRadioBtn()
{
    if(m_bShow)
    {
        m_pCustomPlot->yAxis->setLabel(tr("荧光值(dR)"));
        UpdateTableWidget(m_sResultInfo,m_sLotInfo);
        _UpdateNmzaPlot();
    }

}

void CHistoryDetailAmpCurve::_SlotChannelCheckBox()
{
    QCheckBox * pCheckBox = dynamic_cast<QCheckBox*>(sender());
    if(nullptr == pCheckBox)
    {
        return;
    }

    int index = pCheckBox->property("index").toInt();
    QString  strHole = pCheckBox->property("hole").toString();
    bool bCheck = pCheckBox->isChecked();
    // "A" "B" "C" 开始做
    if(strHole == "A")
    {
        if(0 == index)
        {
            for(int i=1; i<m_pHoleACheckBoxList.size(); i++)
            {
                m_pHoleACheckBoxList.at(i)->setChecked(bCheck);
            }
        }
        else{

            if(bCheck)
            {
                bool bAllCheck = true;
                for(int i=1; i<m_pHoleACheckBoxList.size(); i++)
                {
                    if(!m_pHoleACheckBoxList.at(i)->isChecked())
                    {
                        bAllCheck = false;
                        break;
                    }
                }
                m_pHoleACheckBoxList.at(0)->setChecked(bAllCheck);
            }
            else
            {
                m_pHoleACheckBoxList.at(0)->setChecked(bCheck);
            }
        }
    }
    else if(strHole == "B")
    {
        if(0 == index)
        {
            for(int i=1; i<m_pHoleBCheckBoxList.size(); i++)
            {
                m_pHoleBCheckBoxList.at(i)->setChecked(bCheck);
            }
        }
        else{

            if(bCheck)
            {
                bool bAllCheck = true;
                for(int i=1; i<m_pHoleBCheckBoxList.size(); i++)
                {
                    if(!m_pHoleBCheckBoxList.at(i)->isChecked())
                    {
                        bAllCheck = false;
                        break;
                    }
                }
                m_pHoleBCheckBoxList.at(0)->setChecked(bAllCheck);
            }
            else
            {
                m_pHoleBCheckBoxList.at(0)->setChecked(bCheck);
            }
        }
    }
    _ChannelCheckBoxHandle();
}

void CHistoryDetailAmpCurve::_SlotReviewBtn()
{
    static QElapsedTimer timer;
    if(timer.isValid() && timer.elapsed() < 500)
        return;
    timer.start();

    int nIndex = m_pTableWidget->currentRow();
    if(nIndex < 0 || nIndex > 7)
    {
        nIndex = 0;
    }
    QWidget *pNameWidget = m_pTableWidget->cellWidget(nIndex, 0);
    if (!pNameWidget)
    {
        return;
    }
    QLabel *label = qobject_cast<QLabel*>(pNameWidget);
    if(!label)
    {
        return;
    }

    if(m_pManualReviewWidget->isVisible())
        m_pManualReviewWidget->close();
    m_pManualReviewWidget->showNormal();
    m_pManualReviewWidget->SetManualReviewParam(m_sResultInfo,m_sLotInfo, label->text());
    m_pManualReviewWidget->GetManualReviewParam(m_sResultInfo,m_sLotInfo);
    if(m_sResultInfo.strReview == "m")
    {
        if(m_pNmzaRadioBtn->isChecked())
        {
            UpdateTableWidget(m_sResultInfo,m_sLotInfo);
            _UpdateNmzaPlot();
        }
    }
}

void CHistoryDetailAmpCurve::_ChangeTableResult(QTableWidget* table,const stReviewParam& sResultParam)
{
    if(!sResultParam.m_bReview)
    {
        return;
    }
    int rowCount = table->rowCount();
    for (int row = 0; row < rowCount; ++row) {
        // 获取靶标名称对应上
        QWidget *pNameWidget = table->cellWidget(row, 0);
        if (!pNameWidget)
        {
            return;
        }

        {
            QLabel *label = qobject_cast<QLabel*>(pNameWidget);

            if(label && label->text() == sResultParam.m_strName)
            {

                QString strInfo = sResultParam.m_strCtInfo_Review;
                QString strResult = GetResultFormFlag(sResultParam.m_result);

                QString strReview = QString(tr("已审核"));
                QString strThreshouldText,strCtText;
                QStringList strInfoList = strInfo.split(",");
                if(strInfoList.size() >= 4)
                {
                    strThreshouldText = strInfoList.at(0);
                    strCtText = strInfoList.at(2);
                }

                QTableWidgetItem* pThresholdItem = table->item(row,1);
                QTableWidgetItem* pCtItem = table->item(row,2);
                QWidget *pResultWidget = table->cellWidget(row, 3);
                if (pThresholdItem) {
                    pThresholdItem->setText(strThreshouldText);
                }
                if (pCtItem) {
                    pCtItem->setText(strCtText);
                }
                if (pResultWidget)
                {
                    QLabel *label = qobject_cast<QLabel*>(pResultWidget);
                    if (label) {
                        label->setText(strResult);
                        if("P" == sResultParam.m_result)
                        {
                            label->setStyleSheet("color: red;");
                        }
                        else
                        {
                            label->setStyleSheet("color: black;");
                        }
                        label->repaint();
                    };
                }
                QWidget *pReviewWidget = table->cellWidget(row, 4);
                if (pReviewWidget) {
                    QLabel *label = qobject_cast<QLabel*>(pReviewWidget);
                    if (label) {
                        label->setText(strReview);
                        label->repaint();
                    }
                }
            }
        }
    }
}
void CHistoryDetailAmpCurve::_SetManualBtn(bool bReview,const SResultInfoStruct& sResultInfo)
{
    if(!CPublicConfig::GetInstance()->GetShowRawCurve())
    {
        m_pReviewCalcBtn->setVisible(false);
        m_pReviewPassBtn->setVisible(false);
        return;
    }

    if(eTestDone != sResultInfo.iStatus || IsQCTest(sResultInfo.strMode))
    {
        m_pReviewCalcBtn->setVisible(false);
        m_pReviewPassBtn->setVisible(false);
        return;
    }
    if(m_bPdfImage)
    {
        m_pReviewPassBtn->setText(tr("审核通过"));
        m_pReviewCalcBtn->setVisible(true);
        m_pReviewPassBtn->setVisible(true);
        return;
    }
    if(bReview)
    {
        m_pReviewPassBtn->setText(tr("撤销审核"));
        m_pReviewCalcBtn->setVisible(false);
        m_pReviewPassBtn->setVisible(true);
    }
    else
    {
        m_pReviewPassBtn->setText(tr("审核通过"));
        m_pReviewCalcBtn->setVisible(true);
        m_pReviewPassBtn->setVisible(true);
    }
}

void CHistoryDetailAmpCurve::_ReviewConfirm(stReviewParam sReviewParam)
{
    QString strRawResult;
    for(int iRow=0; iRow<m_pTableWidget->rowCount(); iRow++)
    {
        QWidget *pNameWidget = m_pTableWidget->cellWidget(iRow, 0);
        if(!pNameWidget)
            continue;

        QLabel *pNameLabel = qobject_cast<QLabel*>(pNameWidget);
        if(!pNameLabel)
            continue;

        if(pNameLabel->text() == sReviewParam.m_strName)
        {
            QWidget *pResultWidget = m_pTableWidget->cellWidget(iRow, 3);
            if(!pResultWidget)
                continue;

            QLabel *pResultLabel = qobject_cast<QLabel*>(pResultWidget);
            if(!pResultLabel)
                continue;

            strRawResult = pResultLabel->text();
            break;
        }
    }
    QString strNewResult = GetResultFormFlag(sReviewParam.m_result);

    _ChangeTableResult(m_pTableWidget,sReviewParam);
    qDebug()<<Q_FUNC_INFO<<"人工审核应用:"<<sReviewParam.m_strName;

    QString strUser = CPublicConfig::GetInstance()->GetLoginUser();
    QString strLog = tr("人工审核，%1的检测结果由%2更改为%3，试剂卡编号：%4")
            .arg(sReviewParam.m_strName).arg(strRawResult).arg(strNewResult).arg(m_sResultInfo.strCardID);
    CLogDB::instance().AddOperationLog(strUser, strLog, CLogDB::eReviewLog);

    qDebug()<<QString("人工审核，%1的检测结果由%2更改为%3，试剂卡编号：%4")
              .arg(sReviewParam.m_strName).arg(strRawResult).arg(strNewResult).arg(m_sResultInfo.strCardID);

    if(!sReviewParam.m_bReview)
        return;

    qDebug()<<Q_FUNC_INFO<<sReviewParam.m_strName<<sReviewParam.m_strCtInfo<<sReviewParam.m_strCtInfo_Review<<sReviewParam.m_result;

    QList<sResultCtInfo> &sCtInfo = m_sResultCtInfo;
    for(int i=0; i<sCtInfo.size(); i++)
    {
       if(sCtInfo.at(i).m_strCurveName == sReviewParam.m_strName)
       {
           QString strResult = GetResultFormFlag(sReviewParam.m_result);
           sCtInfo[i].m_strPNIResult = strResult;
           break;
       }
    }

    _UpdateProjectLabel(m_sResultInfo.strProjectName, sCtInfo);
}

void CHistoryDetailAmpCurve::_SlotReviewConfirmBtn()
{
    static QElapsedTimer timer;
    if(timer.isValid() && timer.elapsed() < 500)
        return;
    timer.start();

    bool bReviewConfirm = m_sResultInfo.strReview == "y";
    if(!bReviewConfirm && m_sResultInfo.strReview != "m")
    {
        ShowWarning(nullptr,QString(tr("人工审核")),QString(tr("未改动人工审核")));
        qDebug()<<Q_FUNC_INFO<<"审核结果确认:"<<"未改动审核结果: "<<m_sResultInfo.strReview<<"审核结果:"<<m_sResultInfo.strResult_Review;
        return;
    }

    bool bCheckUser = ShowCheckUser(tr("身份确认"));
    if(false == bCheckUser)
        return;

    if(bReviewConfirm)
    {
        m_sResultInfo.strReview = "n";
        m_sResultInfo.strResult_Review.clear();
        m_sResultInfo.strCTValue_Review.clear();
        UpdateTableWidget(m_sResultInfo,m_sLotInfo);
        qDebug()<<Q_FUNC_INFO<<"审核结果撤销:"<<"自动计算info: "<<m_sResultInfo.strCTInfo<<"结果:"<<m_sResultInfo.strResult;

        QString strUser = CPublicConfig::GetInstance()->GetLoginUser();
        QString strLog = tr("撤销审核，试剂卡编号：%1").arg(m_sResultInfo.strCardID);
        CLogDB::instance().AddOperationLog(strUser, strLog, CLogDB::eReviewLog);
    }
    else
    {
        m_sResultInfo.strReview = "y";
        qDebug()<<Q_FUNC_INFO<<"审核结果确认:"<<"审核info: "<<m_sResultInfo.strCTInfo_Review<<"结果:"<<m_sResultInfo.strResult_Review;

        QString strUser = CPublicConfig::GetInstance()->GetLoginUser();
        QString strLog = tr("审核通过，试剂卡编号：%1").arg(m_sResultInfo.strCardID);
        CLogDB::instance().AddOperationLog(strUser, strLog, CLogDB::eReviewLog);
    }
    _SetManualBtn(m_sResultInfo.strReview == "y",m_sResultInfo);
    CProjectDB::GetInstance()->UpdateHistoryReviewData(m_sResultInfo);

    // 这里需要 截图并通知pdf
    emit SignalReviewConfirmBtn(m_sResultInfo);
}

