﻿#ifndef CSYSTEMDB_H
#define CSYSTEMDB_H

#include <QObject>
#include <QVariant>
#include "CSqliteDBBase.h"

class CSystemDB : public QObject , public CSqliteDBBase
{
    Q_OBJECT

public: 
    static CSystemDB* GetInstance();

public:
    Q_INVOKABLE QVariant getValueFromKey(QVariant qKey);
    Q_INVOKABLE int getIntValueFromKey(QVariant qKey);
    Q_INVOKABLE float getFloatValueFromKey(QVariant qKey);
    Q_INVOKABLE QString getQStringValueFromKey(QVariant qKey);
    Q_INVOKABLE bool addKeyValue(QString strKey, QVariant strValue);

private:
    CSystemDB();
    virtual ~CSystemDB();

private:
    static CSystemDB* m_spInstance;
};

#endif // CSYSTEMDB_H
