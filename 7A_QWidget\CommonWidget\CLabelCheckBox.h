#ifndef CLABELCHECKBOX_H
#define CLABELCHECKBOX_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-10-26
  * Description: QLabel-QCheckBox组合控件
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QCheckBox>

//setLayoutDirection(Qt::RightToLeft);

class CLabelCheckBox : public QWidget
{
    Q_OBJECT
public:
    CLabelCheckBox(const QString &strText, bool bChecked = false, int iSpacing = 5, QWidget *parent = nullptr);
    ~CLabelCheckBox();

    void SetLabelAlignment(Qt::Alignment qAlig);
    void SetCheckBoxChecked(bool bChecked);
    void SetLabelMinSize(int iWidth, int iHeight);
    void SetLabelFixedSize(int iWidth, int iHeight);
    void SetLabelObjectName(const QString &strObjName);
    void SetCheckBoxObjectName(const QString &strObjName);

public:
    bool GetChecked() const;

signals:
    void SignalClicked(bool);

private:
    void _InitWidget();

private:
    QLabel *m_pLabel;
    QCheckBox *m_pCheckBox;

    int m_iSpacing;
    QString m_strText;
    bool m_bChecked;
};

#endif // CLABELCHECKBOX_H
