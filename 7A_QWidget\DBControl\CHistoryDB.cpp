﻿#include "CHistoryDB.h"
#include <QDebug>
#include "PublicConfig.h"

static int gk_iMaxCycleCount = 45;
CHistoryDB* CHistoryDB::m_pInstance = nullptr;

CHistoryDB::CHistoryDB()
    : CSqliteDBBase(CPublicConfig::GetInstance()->GetHistoryDBPath(), gk_strHistoryDBConnect)
{
    m_iDatabaseColumnCount = 7 + gk_iMaxCycleCount;// 当前列数，包括ID

    _InitHistoryTable();
    _InitMeltingTable();
    _InitCTDataTable();
    _InitHrmDataTable();
}

CHistoryDB::~CHistoryDB()
{

}

int CHistoryDB::GetTestDBColumnCount()
{
    return m_iDatabaseColumnCount;
}

int CHistoryDB::GetTestDataCount()
{
    QString strCmd = QString("select count(*) from history");
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return _GetFirstValue(strList).toInt();
}

bool CHistoryDB::AddMeltingTestData(QString strFLID, QString strTmRmResult,
                                    int iCycleCount, QString strMelting, QString strRemarks)
{
    // 写到melting
    QString strCmd = QString("INSERT INTO melting (FLID,CycleCount,PeakValue,Melting,Remarks) "
                             "VALUES('%1',%2,'%3','%4','%5')")
            .arg(strFLID).arg(iCycleCount).arg(strTmRmResult).arg(strMelting).arg(strRemarks);
    return _ExecuteDB(strCmd);
}

bool CHistoryDB::AddTestData(QString strFLID, QString strSNCOde, QString strBeginTime, QString strEndTestTime,
                             int iCycleCount, QList<qreal> dFLDataList,QList<qreal> dFLStandard,QList<qreal> dFLCross, QString strRemarks)
{
    QStringList strList;
    for(int i=0; i<dFLDataList.size(); i++)
        strList.push_back(QString::number(dFLDataList.at(i)));

    QStringList strStandard;
    for(int i=0; i<dFLStandard.size(); i++)
        strStandard.push_back(QString::number(dFLStandard.at(i)));

    QStringList strCross;
    for(int i=0; i<dFLCross.size(); i++)
        strCross.push_back(QString::number(dFLCross.at(i)));

    QString strCmd = QString("INSERT INTO history (FLID,SNCode,TestBeginTime,TestEndTime,CycleCount,FLRaw,FLStandard,FLCross,FLMark1,FLMark2,Remarks) "
                             "VALUES('%1','%2','%3','%4',%5,'%6','%7','%8','%9','%10','%11')")
            .arg(strFLID).arg(strSNCOde).arg(strBeginTime).arg(strEndTestTime).arg(iCycleCount).arg(strList.join(",")).arg(strStandard.join(",")).arg(strCross.join(",")).arg("").arg("").arg(strRemarks);

    return _ExecuteDB(strCmd);
}

bool CHistoryDB::AddMeltingData(QString strFLDataID, int iCycleCount, QString strPeak, QString strMelting, QString strRemarks)
{
    QString strCmd = QString("INSERT INTO melting (FLID,CycleCount,PeakValue,Melting,Remarks) "
                             "VALUES('%1',%2,'%3','%4','%5')")
            .arg(strFLDataID).arg(iCycleCount).arg(strPeak).arg(strMelting).arg(strRemarks);
    return _ExecuteDB(strCmd);
}

bool CHistoryDB::GetMeltingData(QString strFLDataID, int &iCycleCount, QString &strPeakValue, QString &strMelting)
{
    if(strFLDataID.isEmpty())
        return false;

    QString strCmd = QString("SELECT * FROM melting WHERE FLID = '%1'").arg(strFLDataID);
    QList<QStringList> strDBList;
    _QueryDB(strCmd, strDBList);
    if(strDBList.isEmpty())
        return false;

    QStringList strList = strDBList.at(0);
    if(strList.size() < 5)
        return false;

    iCycleCount = strList.at(2).toInt();
    strPeakValue = strList.at(3);
    strMelting = strList.at(4);

    return true;
}

static QList<qreal> SplitDoubleFromQString(QString strData, QString strSplit)
{
    QList<qreal> dDataList;

    QStringList strMeltingList = strData.split(strSplit);
    for(int i = 0; i < strMeltingList.count(); ++i)
    {
        dDataList.push_back(strMeltingList[i].toDouble());
    }
    return dDataList;
}

bool CHistoryDB::GetMeltingDoubleData(QString strFLDataID, QList<qreal> &dMeltingList)
{
    if(strFLDataID.isEmpty())
        return false;

    QString strCmd = QString("SELECT Melting FROM melting WHERE FLID = '%1'").arg(strFLDataID);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);

    QString strMelt = _GetFirstValue(strList);
    //qDebug()<<Q_FUNC_INFO<<strMelt.split(",").size()<<strMelt;
    dMeltingList = SplitDoubleFromQString(strMelt, ",");
    //qDebug()<<Q_FUNC_INFO<<dMeltingList.size()<<dMeltingList;

    return true;
}

bool CHistoryDB::GetMeltingDataFromCardIDAddTestTime(const QString &strCardIDAndTime, QList<QList<double> > &dMeltingList)
{
    QString strCmd = QString("SELECT Melting FROM melting WHERE FLID like '%1%'").arg(strCardIDAndTime);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    if(strList.isEmpty())
        return false;

    for(int i=0; i<strList.size(); i++)
    {
        QList<qreal> dFLData;
        QStringList oneStrList = strList.at(i);
        if(oneStrList.isEmpty())
        {
            continue;
        }
        dFLData = SplitDoubleFromQString(oneStrList.at(0), ",");
        dMeltingList.push_back(dFLData);
    }
    return true;
}

QStringList CHistoryDB::getLastTestFLID(int iCount)
{
    QStringList strReturnList;
    if(iCount < 1)
        return strReturnList;

    QString strCmd = QString("select FLID from history order by id desc limit %1").arg(8 * iCount);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);

    QStringList strReadList = _GetColumnValueList(strList);
    for(int i=0; i<strReadList.size(); i++)
    {
        QString str;
        QString strFLID = strReadList.at(i);
        if(strFLID.contains("_0-"))
            str = strFLID.split("_0-").first();
        else if(strFLID.contains("_1-"))
            str = strFLID.split("_1-").first();

        if(!strReturnList.contains(str))
            strReturnList.push_back(str);
    }
    return strReturnList;
}

QStringList CHistoryDB::getLastMeltingFLID(int iCount)
{
    QString strCmd = QString("select FLID from melting order by id desc limit %1").arg(9 * iCount);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);

    QStringList strFLIDList;
    QStringList strReadList = _GetColumnValueList(strList);
    for(int i=0; i<strReadList.size(); i++)
    {
        QString str;
        QString strFLID = strReadList.at(i);
        if(strFLID.contains("_0-"))
            str = strFLID.split("_0-").first();
        else if(strFLID.contains("_1-"))
            str = strFLID.split("_1-").first();
        else
            str = strFLID;

        if(strFLIDList.indexOf(str) < 0)
            strFLIDList.push_back(str);
        if(strFLIDList.size() >= iCount)
            break;
    }
    return strFLIDList;
}

bool CHistoryDB::GetTestDataFromFLID(QString strFLID, int &iCycleCount, QList<qreal> &dFLDataList)
{
    QString strCmd = QString("SELECT * FROM history WHERE FLID = '%1'").arg(strFLID);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    if(strList.isEmpty())
        return false;

    QStringList oneList = strList.at(0);
    if(oneList.size()>=7)
    {
        iCycleCount = oneList.at(5).toInt();
        QString strMelt = oneList.at(6);
        dFLDataList = SplitDoubleFromQString(strMelt, ",");
    }
    return true;
}

bool CHistoryDB::GetStandardDataFromFLID(QString strFLID, int &iCycleCount, QList<qreal> &dFLDataList)
{
    QString strCmd = QString("SELECT * FROM history WHERE FLID = '%1'").arg(strFLID);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    if(strList.isEmpty())
        return false;

    QStringList oneList = strList.at(0);
    if(oneList.size() >= 9)
    {
        iCycleCount = oneList.at(5).toInt();
        QString strStandard = oneList.at(7);
        dFLDataList = SplitDoubleFromQString(strStandard, ",");
    }
    return true;
}

bool CHistoryDB::GetTestDataFromCardIDAddTestTime(const QString& strCardIDAndTime, QList<QList<double> > &dFLDataList)
{
    QString strCmd = QString("SELECT Raw FROM CTData WHERE FLID like '%1%'").arg(strCardIDAndTime);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    if(strList.isEmpty())
        return false;

    for(int i=0; i<strList.size(); i++)
    {
        QList<qreal> dFLData;
        QStringList oneStrList = strList.at(i);
        if(oneStrList.isEmpty())
        {
            continue;
        }
        dFLData = SplitDoubleFromQString(oneStrList.at(0), ",");
        dFLDataList.push_back(dFLData);
    }
    return true;
}

bool CHistoryDB::AddCTData(const QString &strFLID, int iCycle, const QString &strRaw, const QString &strFit,
                           const QString &strBaseline, const QString &strSmooth, const QString &strDelta)
{
    QString strCmd = QString("insert into CTData (FLID, Cycle, Raw, Fit, Baseline, Smooth, Delta) "
                             "values ('%1', %2, '%3', '%4', '%5', '%6', '%7')")
            .arg(strFLID).arg(iCycle).arg(strRaw).arg(strFit)
            .arg(strBaseline).arg(strSmooth).arg(strDelta);
    return _ExecuteDB(strCmd);
}

bool CHistoryDB::UpdateReviewCTData(const QString &strFLID, const QString &strFitReview, const QString &strBaselineReview, const QString &strSmoothReview, const QString &strDeltaReview)
{
    QString strCmd = QString("UPDATE CTData SET FitReview = '%1', BaselineReview = '%2', "
                             "SmoothReview = '%3', DeltaReview = '%4' where FLID = '%5'")
            .arg(strFitReview).arg(strBaselineReview)
            .arg(strSmoothReview).arg(strDeltaReview).arg(strFLID);
    return _ExecuteDB(strCmd);
}

bool CHistoryDB::GetCTDelta(const QString &strCardID, QString &strDelta,bool bReview)
{
    QString strFit;
    if(bReview)
    {
        strFit = "FitReview";
    }
    else
    {
        strFit = "Fit";
    }
    QString strCmd = QString("select %1 from CTData where FLID = '%2'").arg(strFit).arg(strCardID);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    strDelta = _GetFirstValue(strList);
    return true;
}

bool CHistoryDB::GetCTDeltaFormFLID(const QString &strFlid, QStringList &dFLFitList, QStringList &dFLFitReviewList)
{
    QString strCmd = QString("SELECT Fit, FitReview FROM CTData WHERE FLID  like '%1%'").arg(strFlid);
    QList<QStringList> strList;
    if (!_QueryDB(strCmd, strList))
        return false;

    for(int i=0; i<strList.size(); i++)
    {
        QString dFLData;
        QString dFLReviewData;
        QStringList oneStrList = strList.at(i);
        if(oneStrList.size() < 2)
        {
            continue;
        }
        dFLData = oneStrList.at(0);
        dFLReviewData = oneStrList.at(1);
        dFLFitList.push_back(dFLData);
        dFLFitReviewList.push_back(dFLReviewData);
    }
    return true;


}

bool CHistoryDB::GetCTDeltaDataCount(const QString &strCardID, int &iCycleCount)
{
    QString strCmd = QString("select Cycle from CTData where FLID like '%1%'  LIMIT 1").arg(strCardID);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    iCycleCount = _GetFirstValue(strList).toInt();
    return true;
}

bool CHistoryDB::GetCtDataRaw(const QString &strCardID, QString &strCtRaw)
{

    QString strCmd = QString("select %1 from CTData where FLID = '%2'").arg("Raw").arg(strCardID);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    strCtRaw = _GetFirstValue(strList);
    return true;
}

bool CHistoryDB::AddHrmData(const QString &strFLID, int iCycle,const QString &strTemp, const QString &strRaw, const QString &strSmooth, const QString &strDevFirst)
{
    QString strCmd = QString("insert into HrmData (FLID, Cycle,Temp, Raw, Smooth, DevFirst) "
                             "values ('%1', %2, '%3', '%4', '%5','%6')")
            .arg(strFLID).arg(iCycle).arg(strTemp).arg(strRaw).arg(strSmooth)
            .arg(strDevFirst);
    return _ExecuteDB(strCmd);
}

bool CHistoryDB::GetHrmDelta(const QString &strCardID, QStringList &strDevFirstList)
{
    QString strCmd = QString("select DevFirst from HrmData where FLID like '%1%'").arg(strCardID+"_");
    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    strDevFirstList = _GetColumnValueList(strList);
    return true;
}

CHistoryDB *CHistoryDB::GetInstance()
{
    if(nullptr == m_pInstance)
        m_pInstance = new CHistoryDB;
    return m_pInstance;
}

void CHistoryDB::_InitHistoryTable()
{
    QString strCreateTable  = "CREATE TABLE IF NOT EXISTS history ("
                              "id INTEGER PRIMARY KEY AUTOINCREMENT,"
                              "FLID VARCHAR,"
                              "SNCode VARCHAR,"
                              "TestBeginTime VARCHAR,"
                              "TestEndTime VARCHAR,"
                              "CycleCount INT,"
                              "FLRaw VARCHAR,"
                              "FLStandard VARCHAR,"
                              "FLCross VARCHAR,"
                              "FLMark1 VARCHAR,"
                              "FLMark2 VARCHAR,"
                              "Remarks VARCHAR)";
    _ExecuteDB(strCreateTable);

    // 20240927 chenhao 修改荧光数据字段

    QString strTemp = "select * from sqlite_master where type = 'table' and name = 'history' and  sql like '%FLCross%'";
    QList<QStringList> strList;
    if(_QueryDB(strTemp, strList))
    {
        if(!strList.isEmpty())
        {
            return;
        }

        _ExecuteDB("BEGIN TRANSACTION");
        QString strCmd = "create table if not exists historyTemp ("
                         "id INTEGER PRIMARY KEY AUTOINCREMENT,"
                         "FLID VARCHAR,"
                         "SNCode VARCHAR,"
                         "TestBeginTime VARCHAR,"
                         "TestEndTime VARCHAR,"
                         "CycleCount INT,"
                         "FLRaw VARCHAR,"
                         "FLStandard VARCHAR,"
                         "FLCross VARCHAR,"
                         "FLMark1 VARCHAR,"
                         "FLMark2 VARCHAR,"
                         "Remarks VARCHAR)";
        if(_ExecuteDB(strCmd))
        {
            strCmd = "INSERT INTO historyTemp SELECT T.id, T.FLID, T.SNCode, T.TestBeginTime,T.TestEndTime,T.CycleCount,T.FLRaw,'','','','',T.Remarks FROM history T";
            if(!_ExecuteDB(strCmd))
            {
                _ExecuteDB("DROP TABLE historyTemp");
                _ExecuteDB("ROLLBACK");
                return;
            }

            strCmd = "DROP TABLE history";
            if(!_ExecuteDB(strCmd))
            {
                _ExecuteDB("ROLLBACK");
                return;
            }

            strCmd = "ALTER TABLE historyTemp RENAME TO history";
            if(!_ExecuteDB(strCmd))
            {
                _ExecuteDB("ROLLBACK");
                return;
            }
            qDebug()<<"history update column FlRaw";
        }
        // 提交事务
        _ExecuteDB("COMMIT");
    }

}

void CHistoryDB::_InitMeltingTable()
{
    QString strCmd  = "CREATE TABLE IF NOT EXISTS melting ("
                      "id INTEGER PRIMARY KEY AUTOINCREMENT,"
                      "FLID VARCHAR,"
                      "CycleCount INT,"
                      "PeakValue VARCHAR,"
                      "Melting VARCHAR,"
                      "Remarks VARCHAR)";

    _ExecuteDB(strCmd);
}

void CHistoryDB::_InitCTDataTable()
{
    QString strCmd  = "CREATE TABLE IF NOT EXISTS CTData ("
                      "id INTEGER PRIMARY KEY AUTOINCREMENT,"
                      "FLID VARCHAR,"
                      "Cycle INT,"
                      "Raw VARCHAR,"
                      "Fit VARCHAR,"
                      "Baseline VARCHAR,"
                      "Smooth VARCHAR,"
                      "Delta VARCHAR)";

    _ExecuteDB(strCmd);


    // 新增人工审核数据列
    QString strTemp = "select * from sqlite_master where type = 'table' and name = 'CTData' and sql like '%FitReview%'";
    QList<QStringList> strList;

    if(_QueryDB(strTemp, strList))
    {
        if(!strList.isEmpty())
        {
            return;
        }
        _ExecuteDB("BEGIN TRANSACTION");
        QString strCmd  = "CREATE TABLE IF NOT EXISTS CTDataTemp ("
                          "id INTEGER PRIMARY KEY AUTOINCREMENT,"
                          "FLID VARCHAR,"
                          "Cycle INT,"
                          "Raw VARCHAR,"
                          "Fit VARCHAR,"
                          "Baseline VARCHAR,"
                          "Smooth VARCHAR,"
                          "Delta VARCHAR,"
                          "FitReview VARCHAR,"
                          "BaselineReview VARCHAR,"
                          "SmoothReview VARCHAR,"
                          "DeltaReview VARCHAR)";
        if(_ExecuteDB(strCmd))
        {
            strCmd = "INSERT INTO CTDataTemp SELECT T.id, T.FLID, T.Cycle, T.Raw,T.Fit,T.Baseline,T.Smooth,T.Delta,'' ,'' ,'' ,'' FROM CTData T";
            if(!_ExecuteDB(strCmd))
            {
                _ExecuteDB("DROP TABLE CTDataTemp");
                _ExecuteDB("ROLLBACK");
                return;
            }
            strCmd = "DROP TABLE CTData";
            if(!_ExecuteDB(strCmd))
            {
                _ExecuteDB("ROLLBACK");
                return;
            }
            strCmd = "ALTER TABLE CTDataTemp RENAME TO CTData";
            if(!_ExecuteDB(strCmd))
            {
                _ExecuteDB("ROLLBACK");
                return;
            }
            qDebug()<<"CTData Add new column success";
        }
        // 提交事务
        _ExecuteDB("COMMIT");

    }
}

void CHistoryDB::_InitHrmDataTable()
{
    QString strCmd  = "CREATE TABLE IF NOT EXISTS HrmData ("
                      "id INTEGER PRIMARY KEY AUTOINCREMENT,"
                      "FLID VARCHAR,"
                      "Cycle INT,"
                      "Temp VARCHAR,"
                      "Raw VARCHAR,"
                      "Smooth VARCHAR,"
                      "DevFirst VARCHAR)";

    _ExecuteDB(strCmd);
}

