#include "CMyLogThread.h"
#include <QTextCodec>
#include <QApplication>
#include "PublicFunction.h"
#include "PublicConfig.h"
#include "ZipManager/zip.h"

#define SIZE_1M 1024*1024

CMyLogThread* CMyLogThread::m_spInstance = NULL;

CMyLogThread::CMyLogThread()
{
    setbuf(stdout,NULL);

    connect(this,&CMyLogThread::SignalSaveAllLog,this,&CMyLogThread::_SlotSaveAllLog,Qt::BlockingQueuedConnection);
    connect(this,&CMyLogThread::SignalInitThread,this,&CMyLogThread::_SlotInitThread);
    connect(this,&CMyLogThread::SignalExitThread,this,&CMyLogThread::_SlotExitThread,Qt::BlockingQueuedConnection);

    m_pThread = new QThread;
    moveToThread(m_pThread);
    m_pThread->start();

    emit SignalInitThread();
}

CMyLogThread::~CMyLogThread()
{
    emit SignalExitThread();

    if(m_pThread->isRunning())
    {
        m_pThread->quit();
        m_pThread->wait();
    }

    m_pThread->deleteLater();

    printf("CMyLogThread::~CMyLogThread()\n");
}

void CMyLogThread::_SlotExitThread()
{
    for(int i=0;i<m_pQFileVector.size();i++)
    {
        QFile* pQFile = m_pQFileVector[i];
        if(pQFile->isOpen())
        {
            pQFile->flush();
            pQFile->close();
        }
        delete pQFile;
        pQFile = NULL;
    }

    if(m_pDeleteLogTimer)
    {
        m_pDeleteLogTimer->stop();
        delete m_pDeleteLogTimer;
        m_pDeleteLogTimer = NULL;
    }

    if(m_pWriteLogTimer)
    {
        m_pWriteLogTimer->stop();
        delete m_pWriteLogTimer;
        m_pWriteLogTimer = NULL;
    }

    if(m_pUpdateFileNameTimer)
    {
        m_pUpdateFileNameTimer->stop();
        delete m_pUpdateFileNameTimer;
        m_pUpdateFileNameTimer = NULL;
    }
}

CMyLogThread* CMyLogThread::GetInstance()
{
    if(NULL == m_spInstance)
        m_spInstance = new CMyLogThread;
    return m_spInstance;
}

void CMyLogThread::FreeInstance()
{
    if(NULL != m_spInstance)
    {
        delete m_spInstance;
        m_spInstance = NULL;
    }
}

void CMyLogThread::SaveAllLogBeforeExit()
{
    emit SignalSaveAllLog();
}

void CMyLogThread::_SlotSaveAllLog()
{
    m_pWriteLogTimer->stop();
    _SlotTime2WriteLog();

    for(int i=0; i<m_pQFileVector.size();i++)
    {
        QFile* pQFile = m_pQFileVector[i];
        pQFile->flush();
        pQFile->close();
    }

    System("sync");
}

void CMyLogThread::UpdateLogConfig()
{
    emit SignalUpdateLogConfig();
}

void CMyLogThread::SlotUpdateLogConfig()
{
    QVariantMap qVarMap;
    if(!ReadJsonFile(m_strLogJsonFilePath,qVarMap))
        return;

    m_bStandardOutput = qVarMap.value("StandardOutput").toBool();
    m_iKeepDays = qVarMap.value("KeepDays").toInt();
    m_iMaxSize = qVarMap.value("MaxSize").toInt();
    QString strLogSaveDir = qVarMap.value("Path").toString();
    if(m_strLogSaveDir == strLogSaveDir)
        return;

    m_strLogSaveDir = strLogSaveDir;

    QString strCurrentDay = QDate::currentDate().toString("yyyyMMdd");
    for(int i=0;i<LOG_TYPE_MAX;i++)
    {
        QString strFileDir = m_strLogSaveDir + m_strLogSubSaveDirList[i] + "/";
        CreateDir(strFileDir);
        QString strFileName = strFileDir + m_strLogSubSaveDirList[i] + "_" + strCurrentDay + ".log";

        QFile *pQFile = m_pQFileVector[i];
        if(pQFile->isOpen())
        {
            pQFile->flush();
            pQFile->close();
        }
        pQFile->setFileName(strFileName);
    }
}

void CMyLogThread::AddLog(const SLogSaveStruct &sLogSaveStruct)
{
    QMutexLocker locker(&m_qMutex);
    m_sSaveLogStructList.append(sLogSaveStruct);
}

void CMyLogThread::_SlotTime2WriteLog()
{
    if(m_sSaveLogStructList.isEmpty())
        return;

    m_qMutex.lock();
    QList<SLogSaveStruct> sLogList = m_sSaveLogStructList;
    m_sSaveLogStructList.clear();
    m_qMutex.unlock();

    for(int i=0;i<sLogList.size();i++)
    {
        SLogSaveStruct sLogSaveStruct = sLogList[i];

        if(sLogSaveStruct.logType >= LOG_TYPE_MAX)
            continue;

        QFile* pQFile = m_pQFileVector[sLogSaveStruct.logType];
        //fatal error这种,没有则不创建文件
        if(!pQFile->isOpen())
        {
            pQFile->open(QIODevice::WriteOnly | QIODevice::Append);
        }

        _CheckFileSize(pQFile);
        _PrintfLog(sLogSaveStruct.strLog);
        pQFile->write(sLogSaveStruct.strLog.toLocal8Bit() + "\n");
        pQFile->flush();
    }
}

void CMyLogThread::_CheckFileSize(QFile *pQFile)
{
    if(NULL == pQFile)
        return;

    if(pQFile->size() >= (m_iMaxSize * SIZE_1M))
    {
        QString strOldName = pQFile->fileName();
        int iIndex = strOldName.split('.').last().toInt();
        QString strNewName;
        if(0 == iIndex) // .log
        {
            strNewName = strOldName + ".1";
        }
        else // 其他 .1 .2 ...
        {
            int iLast = strOldName.lastIndexOf('.');
            strNewName = strOldName.mid(0,iLast) + QString(".%1").arg(iIndex+1);
        }
        printf("new log name:%s\n",strNewName.toLocal8Bit().data());
        pQFile->flush();
        pQFile->close();
        pQFile->setFileName(strNewName);
        pQFile->open(QIODevice::WriteOnly | QIODevice::Append);
    }
}

void CMyLogThread::_SlotInitThread()
{
    //qDebug()<<"日志线程ID:"<<QThread::currentThreadId();
    connect(this,&CMyLogThread::SignalUpdateLogConfig,this,&CMyLogThread::SlotUpdateLogConfig);

    m_strLogJsonFilePath = CPublicConfig::GetInstance()->GetLogJsonFilePath();
    if(!_ReadLogJson())
    {
        m_bStandardOutput = false;
        m_iKeepDays = 30;
        m_iMaxSize = 200;
        m_strLogSaveDir = CPublicConfig::GetInstance()->GetLogSaveDir();
        CreateDir(m_strLogSaveDir);
    }

    _WriteLogJson();

#ifdef __aarch64__
    if(m_bStandardOutput)
        freopen("/dev/ttymxc0", "w", stdout);
#endif

    m_strLogSubSaveDirList.reserve(LOG_TYPE_MAX);
    for(int i=0;i<LOG_TYPE_MAX;i++)
        m_strLogSubSaveDirList.push_back("");
    m_strLogSubSaveDirList[LOG_TYPE_DEBUG] = "global";
    m_strLogSubSaveDirList[LOG_TYPE_INFO] = "info";
    m_strLogSubSaveDirList[LOG_TYPE_WARNING] = "warning";
    m_strLogSubSaveDirList[LOG_TYPE_ERROR] = "error";
    m_strLogSubSaveDirList[LOG_TYPE_FATAL] = "fatal";
    m_strLogSubSaveDirList[LOG_TYPE_SLAVE] = "slave";

    QString strCurrentDay = QDate::currentDate().toString("yyyyMMdd");

    for(int i=0;i<LOG_TYPE_MAX;i++)
    {
        QString strFileDir = m_strLogSaveDir + m_strLogSubSaveDirList[i] + "/";
        CreateDir(strFileDir);
        QString strFileName = strFileDir + m_strLogSubSaveDirList[i] + "_" + strCurrentDay + ".log";

        QFile *pQFile = new QFile(strFileName);
        m_pQFileVector.push_back(pQFile);
    }

    m_pDeleteLogTimer = new QTimer(this);
    connect(m_pDeleteLogTimer,&QTimer::timeout,this,&CMyLogThread::_SlotTime2DeleteLogFiles);
    m_pDeleteLogTimer->start(3600*1000);
    _SlotTime2DeleteLogFiles();

    m_pWriteLogTimer = new QTimer(this);
    connect(m_pWriteLogTimer,&QTimer::timeout,this,&CMyLogThread::_SlotTime2WriteLog);
    m_pWriteLogTimer->start(5);

    m_qCurrentDate = QDate::currentDate();

    m_pUpdateFileNameTimer = new QTimer(this);
    connect(m_pUpdateFileNameTimer,&QTimer::timeout,this,&CMyLogThread::_SlotTime2UpdateFileName);
    m_pUpdateFileNameTimer->start(1000);

    pthread_t tid;
    pthread_create(&tid, NULL, _ZipLogFile, this);
    pthread_detach(tid);
}

void CMyLogThread::_SlotTime2UpdateFileName()
{
    QDate qNowDate = QDate::currentDate();
    if(qNowDate <= m_qCurrentDate)
        return;

    //新的一天更新文件名,检测并压缩以前的日志
    QString strNowDay = qNowDate.toString("yyyyMMdd");
    for(int i=0;i<LOG_TYPE_MAX;i++)
    {
        QString strFileDir = m_strLogSaveDir + m_strLogSubSaveDirList[i] + "/";
        QString strFileName = strFileDir + m_strLogSubSaveDirList[i] + "_" + strNowDay + ".log";
        printf("new date log:%s\n",strFileName.toLocal8Bit().data());

        QFile *pQFile = m_pQFileVector[i];
        if(pQFile->isOpen())
        {
            pQFile->flush();
            pQFile->close();
        }
        pQFile->setFileName(strFileName);
    }

    pthread_t tid;
    pthread_create(&tid, NULL, _ZipLogFile, this);
    pthread_detach(tid);

    m_qCurrentDate = qNowDate;
}

bool CMyLogThread::_ReadLogJson()
{
    QVariantMap qVarMap;
    if(!ReadJsonFile(m_strLogJsonFilePath,qVarMap))
        return false;

    m_bStandardOutput = qVarMap.value("StandardOutput",false).toBool();
    QString strLog = QString("Log standardOutput:%1").arg(m_bStandardOutput);
    //qDebug()<<strLog;

    m_iKeepDays = qVarMap.value("KeepDays").toInt();
    if(m_iKeepDays <= 7)
        m_iKeepDays = 7;
    if(m_iKeepDays >= 90)
        m_iKeepDays = 90;
    strLog = QString("Log keep days:%1").arg(m_iKeepDays);
    //qDebug()<<strLog;

    m_iMaxSize = qVarMap.value("MaxSize").toInt();
    if(m_iMaxSize <= 10)
        m_iMaxSize = 10;
    if(m_iMaxSize >= 500)
        m_iMaxSize = 500;
    strLog = QString("Log max size:%1M").arg(m_iMaxSize);
    //qDebug()<<strLog;

    m_strLogSaveDir = qVarMap.value("Path").toString();
    if(m_strLogSaveDir.isEmpty())
        m_strLogSaveDir = CPublicConfig::GetInstance()->GetLogSaveDir();

    QDir dir(m_strLogSaveDir);
    if(!dir.exists())
        return false;

    if(!m_strLogSaveDir.endsWith('/'))
    {
        m_strLogSaveDir.push_back('/');
    }
    CPublicConfig::GetInstance()->SetLogSaveDir(m_strLogSaveDir);

    strLog = QString("Log save dir:%1").arg(m_strLogSaveDir);
    //qDebug()<<strLog;

    return true;
}

void CMyLogThread::_WriteLogJson()
{
    QString strManual = "KeepDays:日志保留天数;"
                        "MaxSize:单个日志文件最大大小,单位M;"
                        "Path:日志文件夹保存路径;"
                        "StandardOutput:是否输出到调试串口";

    QVariantMap qVarMap;
    qVarMap.insert("StandardOutput",m_bStandardOutput);
    qVarMap.insert("KeepDays",m_iKeepDays);
    qVarMap.insert("MaxSize",m_iMaxSize);
    qVarMap.insert("Path",m_strLogSaveDir);

    WriteJsonFile(m_strLogJsonFilePath,qVarMap);
}

void CMyLogThread::_PrintfLog(const QString &strLog)
{
#ifndef __aarch64__
    m_bStandardOutput = true;
#endif

    if(m_bStandardOutput)
    {
#ifdef Q_OS_WIN
        QTextCodec* pTextCodec = QTextCodec::codecForName("GBK");
        QByteArray byte = pTextCodec->fromUnicode(strLog);
        printf("%s \n",byte.data());
#else
        printf("%s\n",strLog.toLocal8Bit().data());
#endif
    }
}

void CMyLogThread::_SlotTime2DeleteLogFiles()
{
    QString strLog;
    QString strCurrentTime = QDate::currentDate().toString("yyyyMMdd");
    int iCurrentYear = strCurrentTime.mid(0,4).toInt();
    int iCurrentMonth = strCurrentTime.mid(4,2).toInt();
    int iCurrentDay = strCurrentTime.mid(6,2).toInt();
    int iCurrentDays = iCurrentYear * 365 + iCurrentMonth * 30 + iCurrentDay;

    for(int i=0;i<LOG_TYPE_MAX;i++)
    {
        QString strLogDir = m_strLogSaveDir + m_strLogSubSaveDirList[i] + "/";
        QDir LogDir(strLogDir);
        QFileInfoList list = LogDir.entryInfoList(QDir::Files);
        for(int j=0;j<list.size();j++)
        {
            QString strFilePath = list.at(j).absoluteFilePath();
            QString strBaseName = list.at(j).baseName();
            QString strFileTime = strBaseName.split('_').last();

            int iFileYear = strFileTime.mid(0,4).toInt();
            int iFileMonth = strFileTime.mid(4,2).toInt();
            int iFileDay = strFileTime.mid(6,2).toInt();
            int iFileDays = iFileYear * 365 + iFileMonth * 30 + iFileDay;

            if(iCurrentDays - iFileDays >= m_iKeepDays)
            {
                QFile::remove(strFilePath);
                strLog = QString("delete log: %1").arg(strFilePath);
                //qDebug()<<strLog;
            }
        }
    }
    System("sync");
}

void* CMyLogThread::_ZipLogFile(void* arg)
{
    //qDebug()<<"压缩线程ID:"<<QThread::currentThreadId();

    static bool bIsZipping = false;
    if(bIsZipping)
        return NULL;

    bIsZipping = true;

    CMyLogThread* pClass = (CMyLogThread*)arg;
    for(int i=0;i<LOG_TYPE_MAX;i++)
    {
        QString strUpperLogDir = pClass->m_strLogSaveDir + pClass->m_strLogSubSaveDirList[i] + "/";
        pClass->_ZipDirEveryFile(strUpperLogDir);
    }

    bIsZipping = false;

    return NULL;
}

void CMyLogThread::_ZipDirEveryFile(QDir qDir)
{
    if(!qDir.exists())
        return;

    QDate qNowDate = QDate::currentDate();
    QString strNowDay = qNowDate.toString("yyyyMMdd");

    QDir dir(qDir);
    QFileInfoList infoList = dir.entryInfoList(QDir::Files);
    for(int j=0;j<infoList.size();j++)
    {
        QString strFileName = infoList.at(j).absoluteFilePath();
        QString strBaseName = infoList.at(j).baseName();
        QString strFileTime = strBaseName.split('_').last();
        strFileTime = strFileTime.mid(0,8);
        if(strNowDay == strFileTime) //当天日期忽略
            continue;
        if(strFileName.endsWith(".zip"))
            continue;

        QString strZipName = strFileName + ".zip";
#ifdef Q_OS_LINUX1
        //QTime t1 = QTime::currentTime();
        bool bZipResult = JlCompress::compressFile(strZipName,strFileName);
        //QTime t2 = QTime::currentTime();
        //qDebug()<<"压缩日志:"<<strFileName<<","<<strZipName<<"结果:"<<bZipResult<<",耗时:"<<t1.msecsTo(t2);
        if(bZipResult)
        {
            QFile::remove(strFileName);
            //qDebug()<<"delete:"<<strFileName;
        }
#endif
    }
}
