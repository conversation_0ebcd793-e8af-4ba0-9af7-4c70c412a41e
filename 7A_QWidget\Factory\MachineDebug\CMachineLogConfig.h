#ifndef CMACHINELOGCONFIG_H
#define CMACHINELOGCONFIG_H

#include <QTimer>
#include <QWidget>
#include <QGroupBox>

#include "CCmdBase.h"
#include "CTextBrowser.h"
#include "CLabelComboBox.h"
#include "CLabelLineEdit.h"

class CMachineLogConfig : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CMachineLogConfig(QWidget *parent = nullptr);
    ~CMachineLogConfig();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

public slots:
    void SlotGetLogData(int iMachineID, int iPackID, const QByteArray &byteLogData);

private slots:
    void _SlotMachineComboBoxChanged(int iMachineID);
    void _SlotSetDebugBtn();
    void _SlotReadDebugBtn();
    void _SlotSetLevelBtn();
    void _SlotGetComboBoxChanged(int index);
    void _SlotGetLogBtn();

    void _SlotGetLogTimeout();
    void _SlotDelayTimeout();

private:
     void _ResetParams();
     void _SaveLog(const QString &strLog);
     void _EnableWidget(bool bEnable);
     void _GetLogSizeReply(int iMachineID, const QVariant &qVarData);

private:
    void _InitWidget();
    void _InitLayout();

private:
    QGroupBox *m_pGroupBox;
    CLabelComboBox *m_pMachineComboBox;
    QComboBox *m_pDebugComboBox;
    QPushButton *m_pSetDebugBtn, *m_pReadDebugBtn;
    QComboBox *m_pFlagComboBox, *m_pLevelComboBox;
    QPushButton *m_pSetLevelBtn;
    QComboBox *m_pGetLogComboBox;
    CLabelLineEdit *m_pLastSizeLineEdit;
    QPushButton *m_pGetLogBtn;
    QLabel *m_pProgressLabel;
    CTextBrowser *m_pTextBrowser;

    int m_iUiMachineID;

private:
    enum Get_Slave_Log
    {
        Get_All_Slave_Log  = 0,  //提取全部日志
        Get_Last_Slave_log = 1,  //提取最新日志
    };

    enum Clear_OR_Save_Slave_Log
    {
        Clear_Slave_Log = 0, //清除中位机日志
        Save_Slave_Log  = 1, //不清除中位机日志
    };

    int m_iLogSize;
    int m_iPackID;
    int m_iGetLogType;
    bool m_bDelay;           //在提取中是否发生过延迟提取
    QTimer* m_pTimeoutTimer; //提取日志超时
    QTimer* m_pDelayTimer;   //延迟提取日志
    QString m_strLogSaveName;
};

#endif // CMACHINELOGCONFIG_H
