#include "CUserDB.h"
#include "PublicConfig.h"
#include "PublicParams.h"

CUserDB *CUserDB::m_spInstance = nullptr;

CUserDB *CUserDB::GetInstance()
{
    if(nullptr == m_spInstance)
        m_spInstance = new CUserDB;
    return m_spInstance;
}

CUserDB::CUserDB() : CSqliteDBBase(CPublicConfig::GetInstance()->GetUserDBPath(), gk_strUserDBConnect)
{
    QString strCmd = "create table if not exists userdata ("
                     "id integer not null primary key autoincrement,"
                     "user_name varchar,"
                     "user_password varchar,"
                     "create_time varchar,"
                     "user_notes varchar)";
    _ExecuteDB(strCmd);
}

CUserDB::~CUserDB()
{

}

bool CUserDB::AddUserData(const QStringList &strUserList)
{
    if(4 != strUserList.size())
    {
        m_strLastError = "添加用户数据数组内容长度不为4";
        return false;
    }

    QString strCmd = QString("insert into userdata (user_name, user_password, create_time, user_notes) "
                             "values ('%1', '%2', '%3', '%4')").
            arg(strUserList.at(0)).arg(strUserList.at(1)).arg(strUserList.at(2)).arg(strUserList.at(3));
    return _ExecuteDB(strCmd);
}

QStringList CUserDB::GetAllUserNameList()
{
    QString strCmd = QString("select user_name from userdata");
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return _GetColumnValueList(strList);
}

QStringList CUserDB::GetUserDataListByName(const QString &strUserName)
{
    QString strCmd = QString("select * from userdata where user_name = '%1'").arg(strUserName);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return _GetRowValueList(strList);
}

QList<QStringList> CUserDB::GetAllUserDataList()
{
    QString strCmd = QString("select * from userdata");
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return strList;
}

QString CUserDB::FindUserPasswordByName(const QString &strUserName)
{
    if(strUserName.isEmpty())
        return "";

    QString strCmd = QString("select user_password from userdata where user_name = '%1'").arg(strUserName);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return _GetFirstValue(strList);
}

bool CUserDB::UpdateUserPasswordNoteByName(const QString &strUserName, const QString &strPassword,
                                           const QString &strNote)
{
    if(strUserName.isEmpty())
        return false;

    QString strCmd = QString("update userdata set user_password = '%1', user_notes = '%2' "
                             "where user_name = '%3'")
            .arg(strPassword).arg(strNote).arg(strUserName);
    return _ExecuteDB(strCmd);
}

bool CUserDB::DeleteUserByName(const QString &strUserName)
{
    QString strCmd = QString("delete from userdata where user_name = '%1'").arg(strUserName);
    return _ExecuteDB(strCmd);
}

bool CUserDB::DeleteAllUser()
{
    return _DeleteTable("userdata");
}

bool CUserDB::IsUserExistInDB(const QString &strUserName)
{
    if(strUserName.isEmpty())
        return false;

    QString strCmd = QString("select * from userdata where user_name = '%1'").arg(strUserName);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    return strList.isEmpty() ? false : true;
}

bool CUserDB::IsUserEnabled(const QString &strUserName)
{
    if(strUserName.isEmpty())
        return false;

    QString strCmd = QString("select user_notes from userdata where user_name = '%1'").arg(strUserName);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);

    QString strStatus = _GetFirstValue(strList);
    return "-1" != strStatus;
}
