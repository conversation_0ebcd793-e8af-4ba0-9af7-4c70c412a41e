#include "CMotorMethod.h"
#include <QListView>
#include <QBoxLayout>
#include <QStyleFactory>
#include <QDebug>
#include "CMotorInfoDB.h"
#include "CMotorDB.h"

CMotorMethod::CMotorMethod(QWidget *parent) : QWidget(parent)
{
    _InitWidget();

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalReGetMotorTextIDData,
            this, &CMotorMethod::SlotReGetMotorTextIDData);
}

void CMotorMethod::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    Q_UNUSED(iMachineID);
    Q_UNUSED(iMethodID);
    Q_UNUSED(iResult);
    Q_UNUSED(qVarData);
}

void CMotorMethod::SlotReGetMotorTextIDData()
{
    QMap<QString, QString> map = CMotorDB::GetInstance().GetCmdIDNameMap();
    m_pIDComboBox->clear();
    m_pIDComboBox->addItems(map.values());
}

void CMotorMethod::_SlotSendBtn()
{
    QString strID = m_pIDComboBox->currentText();
    if(strID.contains("_"))
        strID = strID.split("_").last();

    QString strParam = m_pParamLineEdit->text();
    QString strCmd;
    if(strParam.isEmpty())
        strCmd = GetJsonCmdString(strID.toInt());
    else
        strCmd = GetJsonCmdString(strID.toInt(), strParam);
    SendJsonCmd(m_pMachineComboBox->GetCurrentIndex(), strID.toInt(), strCmd);
}

void CMotorMethod::_SlotCmdComboBoxChanged(const QString &strText)
{
    QString strID = strText;
    if(strID.contains("_"))
        strID = strID.split("_").last();
    QString strName = CMotorDB::GetInstance().GetCmdNameByCmdID(strID);
    m_pNameLabel->setText(strName);
}

void CMotorMethod::_InitWidget()
{
    m_pMachineComboBox = new CLabelComboBox(tr("机器:"));
    m_pMachineComboBox->SetComboBoxList(gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(80, 50);

    m_pNameLabel = new QLabel;
    m_pNameLabel->setObjectName("daylabel");
    m_pNameLabel->setFixedSize(120 ,50);
    m_pNameLabel->setAlignment(Qt::AlignCenter);

    m_pIDComboBox = new QComboBox;
    m_pIDComboBox->setView(new QListView);
    m_pIDComboBox->setFixedSize(450, 50);
    m_pIDComboBox->view()->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_pIDComboBox->setMaxVisibleItems(10);
    m_pIDComboBox->setStyle(QStyleFactory::create("Windows"));
    connect(m_pIDComboBox, SIGNAL(currentIndexChanged(const QString &)), this, SLOT(_SlotCmdComboBoxChanged(const QString &)));
    QMap<QString, QString> map = CMotorDB::GetInstance().GetCmdIDNameMap();
    m_pIDComboBox->clear();
    m_pIDComboBox->addItems(map.values());

    m_pParamLineEdit = new CLineEdit;
    m_pParamLineEdit->setFixedSize(120, 50);
    m_pParamLineEdit->setAlignment(Qt::AlignCenter);

    m_pSendBtn = new QPushButton(tr("发送"));
    m_pSendBtn->setFixedSize(80, 50);
    connect(m_pSendBtn, &QPushButton::clicked, this, &CMotorMethod::_SlotSendBtn);

    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(20);
    pTopLayout->addWidget(m_pMachineComboBox);
    pTopLayout->addWidget(m_pIDComboBox);
    pTopLayout->addWidget(m_pNameLabel);
    pTopLayout->addWidget(m_pParamLineEdit);
    pTopLayout->addWidget(m_pSendBtn);
    pTopLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setContentsMargins(20, 0, 0, 0);
    pLayout->addLayout(pTopLayout);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}
