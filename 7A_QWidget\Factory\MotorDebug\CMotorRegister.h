#ifndef CMOTORREGISTER_H
#define CMOTORREGISTER_H

#include <QWidget>
#include <QPushButton>
#include "CCmdBase.h"
#include "CLabelComboBox.h"
#include "CLabelLineEdit.h"

class CMotorRegister : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CMotorRegister(QWidget *parent = nullptr);
    ~CMotorRegister();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

public slots:
    void SlotSoftTypeChanged(int iSoftType);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;

private slots:
    void _SlotBtnClicked();

private:
    void _ReadParam();
    void _SetParam();
    void _ClearParam();

    QString getRegisterItemValue(QString strIhold, QString strIrun, QString strIholddelay);
    QStringList getRegisterItemValueFromData(QString strValue);

private:
    void _InitWidget();

private:
    QList<CLabelComboBox *> m_pComboBoxList;
    QList<CLabelLineEdit *> m_pLineEditList;
    CLabelComboBox * m_pMachineComboBox;
    QVariantList m_iNumList;
    QList<QWidget *> m_pWidgetList;
    QMap<int,QWidget*> m_pWidgetMap;
};

#endif // CMOTORREGISTER_H
