#ifndef CCANBUSTHREAD_H
#define CCANBUSTHREAD_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-08-26
  * Description:
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QMutex>
#include <QThread>
#include <QTimer>
#include <QCanBus>
#include <QCanBusFrame>
#include <QCanBusDevice>
#include "PublicParams.h"

class CCanBusThread : public QObject
{
    Q_OBJECT
public:
    static CCanBusThread *GetInstance();
    ~CCanBusThread();

    void WriteCan(const SCanBusDataStruct &sSCanBusDataStruct);
    QByteArray GetReciveCanMsg();
    QList<QByteArray> GetReciveCanMsgList();

    void ReOpenCan();

signals:
    void SignalInitThread();
    void SignalExitThread();
    void SignalReopenCan();
    void SignalWriteCan(SCanBusDataStruct sSCanBusDataStruct);
    void SignalACKOut(int iMachineID);
    void SignalRemoveSeq(int iSeq);
    void SignalWriteByte2Can(QByteArray qByteWrite);

private slots:
    void _SlotInitThread();
    void _SlotExitThread();
    void _SlotReopenCan();
    void _SlotCanStateChanged(QCanBusDevice::CanBusDeviceState state);
    void _SlotCanBusError(QCanBusDevice::CanBusError eError);
    void _SlotWriteCan(SCanBusDataStruct sSCanBusDataStruct);
    void _SlotReadCanBus();
    void _SlotWriteTimer();
    void _SlotReadTimer();
    void _SlotRemoveSeq(int iSeq);
    void _SlotWriteByte2Can(QByteArray qByteWrite);

private:
    CCanBusThread();
    QByteArray _GetSendData(const SCanBusDataStruct &sSCanBusDataStruct, quint16 quSeq);
    void _ThreadHandleReadBuff();
    void _ParseReadBuff(QByteArray& readBuff);

private:
    static CCanBusThread *m_spIntance;
    bool m_bOpenCan;
    bool m_bHandling;
    QThread *m_pThread;
    QCanBusDevice *m_pCanBusDevice;

    QTimer *m_pWriteTimer;
    QTimer *m_pReadTimer;

    QMap<int, STSendStruct> m_iSendStructMap;
    QList<QByteArray> m_qReceiveFrameList; //一条一条的指令list

    QList<QCanBusFrame> m_qBusFrameVector;
    QList<QByteArray> m_qBuffList; //每台机器的read buff

    quint16 m_iSendSeq;
    QMutex *m_pGetMutex;
    QMutex *m_pReadMutex;

    QList<QByteArray> m_qLastSeqList;
    QList<QByteArray> m_qLastCRCList;
    QList<int> m_bReceivedList;
    QList<int> m_iLoseTimesList; //连续收不满字节次数

    int m_iEmptySize;
};

#endif // CCANBUSTHREAD_H
