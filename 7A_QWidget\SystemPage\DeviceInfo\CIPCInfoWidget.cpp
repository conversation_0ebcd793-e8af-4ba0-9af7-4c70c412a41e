#include "CIPCInfoWidget.h"
#include <QDebug>
#include <QBoxLayout>
#include "CMessageBox.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "CScanCodeThread.h"
#include "DBControl/CDevInfoDB.h"
#include "CConfigJson.h"
#include "PublicConfig.h"

CIPCInfoWidget::CIPCInfoWidget(QWidget *parent)
    : QWidget(parent)
    , m_bEdit(false)
    , m_strTipsText(tr("提示"))
{   
    this->setFixedSize(1636, 800);

    _InitWidget();
    _InitLayout();

    m_pCDateTimeWidget = new CDateTimeWidget(this);
    m_pCDateTimeWidget->SetOnlyDateModel();
    connect(m_pCDateTimeWidget, &CDateTimeWidget::SignalDateTime, this, &CIPCInfoWidget::_SlotConfirmDateTime);
    m_pCDateTimeWidget->setVisible(false);

    QString strSN = CConfigJson::GetInstance()->GetSystemValue("SN").toString();
    m_pSNLineEdit->setText(strSN);
    CPublicConfig::GetInstance()->SetMachineSN(strSN);

    QString strMADE = CConfigJson::GetInstance()->GetSystemValue("MADE").toString();
    m_pDateValueLabel->setText(strMADE);
}

void CIPCInfoWidget::showEvent(QShowEvent *pEvent)
{
    bool bBtnShow = false;
    if(CPublicConfig::GetInstance()->GetLoginLevel() < eUser_Factory)
        bBtnShow = false;
    else
        bBtnShow = true;
    m_pEditBtn->setVisible(bBtnShow);
    m_pScanBtn->setVisible(bBtnShow);

    connect(CScanCodeThread::GetInstance(), &CScanCodeThread::SignalScanData, this, &CIPCInfoWidget::_SlotScanData);

    QString strSN = CConfigJson::GetInstance()->GetSystemValue("SN").toString();
    m_pSNLineEdit->setText(strSN);

    QString strMADE = CConfigJson::GetInstance()->GetSystemValue("MADE").toString();
    m_pDateValueLabel->setText(strMADE);

    QStringList strInfoList;
    if(CPublicConfig::GetInstance()->GetWiFiConnect())
        strInfoList = GetIPInfoList("wlan0");
    if(CPublicConfig::GetInstance()->GetEthConnect())
        strInfoList = GetIPInfoList("eth1");
    if(strInfoList.size() >= 4)
    {
        m_pIPLabel->setText(tr("IP地址：") + strInfoList.at(0));
        m_pMACLabel->setText(tr("MAC地址：") + strInfoList.at(3));
    }
    else
    {
        m_pIPLabel->setText(tr("IP地址："));
        m_pMACLabel->setText(tr("MAC地址："));
    }

#ifdef Q_OS_WIN

    m_pIPLabel->setText(tr("IP地址：") + "***********");
    m_pMACLabel->setText(tr("MAC地址：") + "C0:F5:35:EC:9E:11");

#endif

    QWidget::showEvent(pEvent);
}

void CIPCInfoWidget::hideEvent(QHideEvent *pEvent)
{
    m_bEdit = false;
    m_pEditBtn->setText(tr("编辑"));
    m_pSNLineEdit->setEnabled(false);
    m_pDateValueLabel->setEnabled(false);
    m_pDateValueLabel->setProperty("edit", false);
    m_pDateValueLabel->style()->polish(m_pDateValueLabel);

    disconnect(CScanCodeThread::GetInstance(), &CScanCodeThread::SignalScanData, this, &CIPCInfoWidget::_SlotScanData);

    CScanCodeThread::GetInstance()->StopScan();

    QWidget::hideEvent(pEvent);
}

void CIPCInfoWidget::_SlotEditBtn()
{
    if(false == m_bEdit)
    {
        m_bEdit = true;
        m_pEditBtn->setText(tr("保存"));
        m_pDateValueLabel->setEnabled(true);
        m_pDateValueLabel->setProperty("edit", true);
        m_pDateValueLabel->style()->polish(m_pDateValueLabel);
        m_pSNLineEdit->setEnabled(true);
        m_pSNLineEdit->setFocus();
    }
    else
    {
        QString strSN = m_pSNLineEdit->text();
        if(strSN.isEmpty())
        {
            ShowInformation(this, m_strTipsText, tr("SN不能为空"));
            return;
        }

        QString strMADE = m_pDateValueLabel->text();
        if(strMADE.isEmpty())
        {
            ShowInformation(this, m_strTipsText, tr("出厂日期不能为空"));
            return;
        }

        m_bEdit = false;
        m_pEditBtn->setText(tr("编辑"));
        m_pSNLineEdit->setEnabled(false);
        m_pDateValueLabel->setEnabled(false);
        m_pDateValueLabel->setProperty("edit", false);
        m_pDateValueLabel->style()->polish(m_pDateValueLabel);

        qDebug()<<"保存上位机SN和MADE:"<<strSN<<strMADE;
        CPublicConfig::GetInstance()->SetMachineSN(strSN);

        CConfigJson::GetInstance()->SetSystemValue("SN", strSN);
        CConfigJson::GetInstance()->SetSystemValue("MADE", strMADE);
        ShowSuccess(this, tr("提示"), tr("保存成功"));
    }
}

void CIPCInfoWidget::_SlotScanBtn()
{
    if(!m_bEdit)
    {
        ShowInformation(this, tr("提示"), tr("请先点击编辑按钮"));
        return;
    }

    CScanCodeThread::GetInstance()->StartScan();
}

void CIPCInfoWidget::_SlotShowDateTime()
{
    m_pCDateTimeWidget->SetDateTime(m_pDateValueLabel->text());
    m_pCDateTimeWidget->show();
}

void CIPCInfoWidget::_SlotConfirmDateTime(const QString &strDateTime)
{
    m_pDateValueLabel->setText(strDateTime);
}

void CIPCInfoWidget::_SlotScanData(QByteArray qScanData)
{
    qDebug()<<Q_FUNC_INFO<<qScanData;
    System("aplay fireware/audio/bi.wav");

    m_pSNLineEdit->setText(qScanData.data());
}

void CIPCInfoWidget::_InitWidget()
{
    m_pImageLabel = new QLabel;
    m_pImageLabel->setFixedSize(550, 360);
    m_pImageLabel->setObjectName("IPCLabel");
    m_pImageLabel->setAlignment(Qt::AlignCenter);

    m_pNameLabel = new QLabel(tr("名称：全自动核酸扩增分析仪控制软件"));

    m_pSNLabel = new QLabel(tr("序列号："));
    m_pSNLineEdit = new CLineEdit("WonDx101A000001");
    m_pSNLineEdit->setFixedWidth(400);
    m_pSNLineEdit->setEnabled(false);

    m_pFirmVerLabel = new QLabel(tr("固件版本：%1").arg(GetAppVersion()));
    m_pIPLabel = new QLabel(tr("IP地址："));
    m_pMACLabel = new QLabel(tr("MAC地址："));

    m_pDateNameLabel = new QLabel(tr("出厂日期："));
    m_pDateValueLabel = new CPressLabel("2024-07-30");
    m_pDateValueLabel->setFixedWidth(200);
    m_pDateValueLabel->setObjectName("DateValueLabel");
    m_pDateValueLabel->setProperty("edit", false);
    m_pDateValueLabel->setEnabled(false);
    connect(m_pDateValueLabel, &CPressLabel::SignalPressEvent, this, &CIPCInfoWidget::_SlotShowDateTime);

    m_pManufacturerLabel = new QLabel(tr("注册申请人：广州万孚生物技术股份有限公司"));

    m_pAddressNameLabel = new QLabel(tr("设计开发地址："));
    m_pAddressNameLabel->setAlignment(Qt::AlignTop);

    m_pAddressValueLabel = new QLabel(tr("广州市黄埔区科学城荔枝山路8号、\n\n广州市黄埔区神舟路268号"));
    m_pAddressValueLabel->setAlignment(Qt::AlignTop);

    m_pReturnBtn = new QPushButton(tr("返回"));
    m_pReturnBtn->setFixedSize(150, 56);
    m_pReturnBtn->setObjectName("CancelBtn");
    connect(m_pReturnBtn, &QPushButton::clicked, this, &CIPCInfoWidget::SignalReturn);

    m_pEditBtn = new QPushButton(tr("编辑"));
    m_pEditBtn->setFixedSize(150, 56);
    connect(m_pEditBtn, &QPushButton::clicked, this, &CIPCInfoWidget::_SlotEditBtn);

    m_pScanBtn = new QPushButton(tr("SN扫码"));
    m_pScanBtn->setFixedSize(150, 56);
    connect(m_pScanBtn, &QPushButton::clicked, this, &CIPCInfoWidget::_SlotScanBtn);

    if(eLanguage_Italian == gk_iLanguage)
        m_pScanBtn->setFixedSize(170, 56);
}

void CIPCInfoWidget::_InitLayout()
{
    QHBoxLayout *pSNLayout = new QHBoxLayout;
    pSNLayout->setMargin(0);
    pSNLayout->setSpacing(0);
    pSNLayout->addWidget(m_pSNLabel);
    pSNLayout->addWidget(m_pSNLineEdit);
    pSNLayout->addStretch(1);

    QHBoxLayout *pDateLayout = new QHBoxLayout;
    pDateLayout->setMargin(0);
    pDateLayout->setSpacing(0);
    pDateLayout->addWidget(m_pDateNameLabel);
    pDateLayout->addWidget(m_pDateValueLabel);
    pDateLayout->addStretch(1);

    QHBoxLayout *pAddressLayout = new QHBoxLayout;
    pAddressLayout->setMargin(0);
    pAddressLayout->setSpacing(0);
    pAddressLayout->addWidget(m_pAddressNameLabel);
    pAddressLayout->addWidget(m_pAddressValueLabel);
    pAddressLayout->addStretch(1);

    QVBoxLayout *pTextLayout = new QVBoxLayout;
    pTextLayout->setMargin(0);
    pTextLayout->setSpacing(40);
    //pTextLayout->addWidget(m_pNameLabel, 0, Qt::AlignLeft);
    pTextLayout->addLayout(pSNLayout);
    pTextLayout->addWidget(m_pFirmVerLabel, 0, Qt::AlignLeft);
    pTextLayout->addWidget(m_pIPLabel, 0, Qt::AlignLeft);
    pTextLayout->addWidget(m_pMACLabel, 0, Qt::AlignLeft);
    pTextLayout->addLayout(pDateLayout);
    pTextLayout->addWidget(m_pManufacturerLabel, 0, Qt::AlignLeft);
    //pTextLayout->addLayout(pAddressLayout);
    pTextLayout->addStretch(1);

    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(40);
    pTopLayout->addStretch(1);
    pTopLayout->addWidget(m_pImageLabel);
    pTopLayout->addLayout(pTextLayout);
    pTopLayout->addStretch(1);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(60);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pReturnBtn);
    pBtnLayout->addWidget(m_pEditBtn);
    pBtnLayout->addWidget(m_pScanBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addLayout(pTopLayout);
    pLayout->addStretch(1);
    pLayout->addLayout(pBtnLayout);
    this->setLayout(pLayout);
}
