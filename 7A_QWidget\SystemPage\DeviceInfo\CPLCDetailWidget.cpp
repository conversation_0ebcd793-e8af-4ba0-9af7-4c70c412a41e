#include "CPLCDetailWidget.h"
#include <QPainter>
#include <QBoxLayout>

#include "COperationUnit.h"
#include "CMessageBox.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "CScanCodeThread.h"

CPLCDetailWidget::CPLCDetailWidget(QWidget *parent)
    : QWidget(parent)
    , m_bShow(false)
    , m_bEdit(false)
    , m_iCurrentMachineID(-1)
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setAttribute(Qt::WA_TranslucentBackground);
    this->setFixedSize(parent ? parent->size() : G_QRootSize);
    this->move(0, 0);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroupBox(), 0, Qt::AlignHCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);

    m_pCDateTimeWidget = new CDateTimeWidget(this);
    m_pCDateTimeWidget->SetOnlyDateModel();
    connect(m_pCDateTimeWidget, &CDateTimeWidget::SignalDateTime, this, &CPLCDetailWidget::_SlotConfirmDateTime);
    m_pCDateTimeWidget->setVisible(false);

    LoadQSS(this, ":/qss/qss/system/deviceinfo.qss");

    Register2Map(Method_sys_info);

    for(int i=0; i<gk_iMachineCount; i++)
    {
        m_sDetailInfoList << SDetailInfoStruct();
    }
    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalSetDevStatus, this, &CPLCDetailWidget::SlotSetDevStatus);
}

CPLCDetailWidget::~CPLCDetailWidget()
{
    UnRegister2Map(Method_sys_info);
    //UnRegister2Map(Method_machine_reset);
}

void CPLCDetailWidget::PageShow(int iMachineID)
{
    qDebug()<<Q_FUNC_INFO<<QString("%1#").arg(iMachineID + 1);
    m_iCurrentMachineID = iMachineID;
    SDetailInfoStruct sInfo;
    if(iMachineID >= 0 && iMachineID < gk_iMachineCount)
    {
        sInfo = m_sDetailInfoList.at(iMachineID);
    }  
    m_pIDLabel->setText(tr("编号：") + QString("%1#").arg(iMachineID + 1));
    m_pSNLineEdit->setText(sInfo.strSN);
    m_pFirmVerLabel->setText(tr("固件版本：") + sInfo.strVersion);
    m_pDateValueLabel->setText(sInfo.strMADE);
    this->show();

#ifdef Q_OS_WIN
    if(sInfo.strVersion.isEmpty())
        m_pFirmVerLabel->setText(tr("固件版本：") + GetAppVersion());
#endif
}

void CPLCDetailWidget::SlotSetDevStatus(int iMachineID, DeviceStatus eStatus)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    m_sDetailInfoList[iMachineID].eStatus = eStatus;
}

void CPLCDetailWidget::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    Q_UNUSED(iMethodID);

    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if(Method_sys_info == iMethodID)
    {
        if(0 == iResult && qVarData.isNull() && m_bShow)
        {
            ShowInformation(this, m_strTipsText, tr("保存成功"));
            return;
        }
        if(qVarData.isValid())
        {
            QVariantMap qVarMap = qVarData.toMap();
            SDetailInfoStruct &sInfo = m_sDetailInfoList[iMachineID];
            sInfo.strSN = qVarMap.value("SN").toString();
            sInfo.strMADE = qVarMap.value("MADE").toString();
            sInfo.strVersion = qVarMap.value("firmware").toString();
            if(qVarMap.contains("APPVER"))
                sInfo.strVersion = qVarMap.value("APPVER").toString();

            if(m_iCurrentMachineID == iMachineID)
            {
                m_pSNLineEdit->setText(sInfo.strSN);
                m_pDateValueLabel->setText(sInfo.strMADE);
                m_pFirmVerLabel->setText(tr("固件版本：") + sInfo.strVersion);
            }
        }
    }

    if(Method_machine_reset == iMethodID)
    {
        if(0 == iResult)
            ShowSuccess(this, m_strTipsText, tr("%1#自检成功").arg(iMachineID + 1));
        else
            ShowWarning(this, m_strTipsText, tr("%1#自检失败").arg(iMachineID + 1));
    }
}

void CPLCDetailWidget::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;

    int iUserLevel = CPublicConfig::GetInstance()->GetLoginLevel();
    m_pEditBtn->setVisible(iUserLevel >= eUser_Factory);
    m_pScanBtn->setVisible(iUserLevel >= eUser_Factory);
    m_pResetBtn->setVisible(false);

    connect(CScanCodeThread::GetInstance(), &CScanCodeThread::SignalScanData, this, &CPLCDetailWidget::_SlotScanData);

    QWidget::showEvent(pEvent);
}

void CPLCDetailWidget::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;

    m_bEdit = false;
    m_pEditBtn->setText(tr("编辑"));
    m_pSNLineEdit->setEnabled(false);
    m_pDateValueLabel->setEnabled(false);
    m_pDateValueLabel->setProperty("edit", false);
    m_pDateValueLabel->style()->polish(m_pDateValueLabel);

    disconnect(CScanCodeThread::GetInstance(), &CScanCodeThread::SignalScanData, this, &CPLCDetailWidget::_SlotScanData);

    CScanCodeThread::GetInstance()->StopScan();

    QWidget::hideEvent(pEvent);
}

void CPLCDetailWidget::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, gk_iBackTransparency));
    QWidget::paintEvent(pEvent);
}

void CPLCDetailWidget::_SlotEditBtn()
{
    if(false == m_bEdit)
    {
        m_bEdit = true;
        m_pEditBtn->setText(tr("保存"));
        m_pDateValueLabel->setEnabled(true);
        m_pDateValueLabel->setProperty("edit", true);
        m_pDateValueLabel->style()->polish(m_pDateValueLabel);
        m_pSNLineEdit->setEnabled(true);
        m_pSNLineEdit->setFocus();
    }
    else
    {
        QString strSN = m_pSNLineEdit->text();
        if(strSN.isEmpty())
        {
            ShowInformation(this, m_strTipsText, tr("SN不能为空"));
            return;
        }

        QString strMADE = m_pDateValueLabel->text();
        if(strMADE.isEmpty())
        {
            ShowInformation(this, m_strTipsText, tr("出厂日期不能为空"));
            return;
        }

        if(m_iCurrentMachineID >= 0 && m_iCurrentMachineID < m_sDetailInfoList.size())
        {
            m_sDetailInfoList[m_iCurrentMachineID].strSN = strSN;
            m_sDetailInfoList[m_iCurrentMachineID].strMADE = strMADE;
        }

        m_bEdit = false;
        m_pEditBtn->setText(tr("编辑"));
        m_pSNLineEdit->setEnabled(false);
        m_pDateValueLabel->setEnabled(false);
        m_pDateValueLabel->setProperty("edit", false);
        m_pDateValueLabel->style()->polish(m_pDateValueLabel);

        QVariantMap qVarMap;
        qVarMap.insert("SN", strSN);
        qVarMap.insert("MADE", strMADE);
        QString strCmd = GetJsonCmdString(Method_sys_info, qVarMap);
        qDebug()<<QString("%1#设置SN和出厂日期:%2,%3").arg(m_iCurrentMachineID + 1).arg(strSN).arg(strMADE);
        SendJsonCmd(m_iCurrentMachineID, Method_sys_info, strCmd);
    }
}

void CPLCDetailWidget::_SlotScanBtn()
{
    if(!m_bEdit)
    {
        ShowInformation(this, tr("提示"), tr("请先点击编辑按钮"));
        return;
    }

    CScanCodeThread::GetInstance()->StartScan();
}

void CPLCDetailWidget::_SlotResetBtn()
{
    if(eDeviceIdle != m_sDetailInfoList.at(m_iCurrentMachineID).eStatus)
    {
        ShowInformation(this, tr("提示"), tr("检测模块不是空闲状态，无法自检"));
        return;
    }

    QString strCmd = GetJsonCmdString(Method_machine_reset);
    SendJsonCmd(m_iCurrentMachineID, Method_machine_reset, strCmd);
}

void CPLCDetailWidget::_SlotIdentifyBtn()
{
    if(eDeviceDisconnect == m_sDetailInfoList.at(m_iCurrentMachineID).eStatus)
    {
        ShowInformation(this, tr("提示"), tr("检测模块离线中，无法识别"));
        return;
    }

    QVariantList qVarList = {5000};
    QString strCmd = GetJsonCmdString(Method_start_identify, qVarList);
    SendJsonCmd(m_iCurrentMachineID, Method_start_identify, strCmd);
}

void CPLCDetailWidget::_SlotShowDateTime()
{
    m_pCDateTimeWidget->SetDateTime(m_pDateValueLabel->text());
    m_pCDateTimeWidget->show();
}

void CPLCDetailWidget::_SlotConfirmDateTime(const QString &strDateTime)
{
    m_pDateValueLabel->setText(strDateTime);
}

void CPLCDetailWidget::_SlotScanData(QByteArray qScanData)
{
    qDebug()<<Q_FUNC_INFO<<qScanData;
    System("aplay fireware/audio/bi.wav");

    m_pSNLineEdit->setText(qScanData.data());
}

QGroupBox *CPLCDetailWidget::_CreateGroupBox()
{
    m_pCHLabelTitleWidget = new CHLabelTitleWidget(tr("仪器详情"), this);

    m_pImageLabel = new QLabel(this);
    m_pImageLabel->setFixedSize(420, 380);
    m_pImageLabel->setObjectName("PLCLabel");
    m_pImageLabel->setAlignment(Qt::AlignCenter);

    m_pIDLabel = new QLabel(tr("编号："), this);

    m_pSNLabel = new QLabel(tr("序列号："), this);
    m_pSNLineEdit = new CLineEdit(this);
    m_pSNLineEdit->setFixedWidth(400);
    m_pSNLineEdit->setEnabled(false);

    m_pFirmVerLabel = new QLabel(tr("固件版本："), this);

    m_pDateNameLabel = new QLabel(tr("出厂日期："), this);
    m_pDateValueLabel = new CPressLabel(this);
    m_pDateValueLabel->setFixedWidth(200);
    m_pDateValueLabel->setObjectName("DateValueLabel");
    m_pDateValueLabel->setProperty("edit", false);
    m_pDateValueLabel->setEnabled(false);
    connect(m_pDateValueLabel, &CPressLabel::SignalPressEvent, this, &CPLCDetailWidget::_SlotShowDateTime);

    m_pManufacturerLabel = new QLabel(tr("注册申请人：广州万孚生物技术股份有限公司"), this);

    m_pAddressNameLabel = new QLabel(tr("设计开发地址："), this);
    m_pAddressNameLabel->setAlignment(Qt::AlignTop);
    m_pAddressNameLabel->setVisible(false);

    m_pAddressValueLabel = new QLabel(tr("广州市黄埔区科学城荔枝山路8号、\n\n广州市黄埔区神舟路268号"), this);
    m_pAddressValueLabel->setAlignment(Qt::AlignTop);
    m_pAddressValueLabel->setVisible(false);

    m_pEffectiveDateLabel = new QLabel(tr("维护到期日期：2029-07-30"), this);
    m_pEffectiveDateLabel->setVisible(false);

    int iBtnWidth = 150;
    if(eLanguage_Spanish == gk_iLanguage)
        iBtnWidth = 170;
    else if(eLanguage_German == gk_iLanguage)
        iBtnWidth = 180;
    else if(eLanguage_Italian == gk_iLanguage)
        iBtnWidth = 170;

    m_pCloseBtn = new QPushButton(tr("关闭"), this);
    m_pCloseBtn->setFixedSize(iBtnWidth, 56);
    m_pCloseBtn->setObjectName("CancelBtn");
    connect(m_pCloseBtn, &QPushButton::clicked, this, [this]{this->close();});

    m_pEditBtn = new QPushButton(tr("编辑"), this);
    m_pEditBtn->setFixedSize(iBtnWidth, 56);
    connect(m_pEditBtn, &QPushButton::clicked, this, &CPLCDetailWidget::_SlotEditBtn);

    m_pScanBtn = new QPushButton(tr("SN扫码"), this);
    m_pScanBtn->setFixedSize(iBtnWidth, 56);
    connect(m_pScanBtn, &QPushButton::clicked, this, &CPLCDetailWidget::_SlotScanBtn);

    m_pResetBtn = new QPushButton(tr("自检"), this);
    m_pResetBtn->setFixedSize(iBtnWidth, 56);
    connect(m_pResetBtn, &QPushButton::clicked, this, &CPLCDetailWidget::_SlotResetBtn);

    m_pIdentifyBtn = new QPushButton(tr("仪器识别"), this);
    m_pIdentifyBtn->setFixedSize(iBtnWidth, 56);
    connect(m_pIdentifyBtn, &QPushButton::clicked, this, &CPLCDetailWidget::_SlotIdentifyBtn);

    QHBoxLayout *pSNLayout = new QHBoxLayout;
    pSNLayout->setMargin(0);
    pSNLayout->setSpacing(0);
    pSNLayout->addWidget(m_pSNLabel);
    pSNLayout->addWidget(m_pSNLineEdit);
    pSNLayout->addStretch(1);

    QHBoxLayout *pDateLayout = new QHBoxLayout;
    pDateLayout->setMargin(0);
    pDateLayout->setSpacing(0);
    pDateLayout->addWidget(m_pDateNameLabel);
    pDateLayout->addWidget(m_pDateValueLabel);
    pDateLayout->addStretch(1);

    QHBoxLayout *pAddressLayout = new QHBoxLayout;
    pAddressLayout->setMargin(0);
    pAddressLayout->setSpacing(0);
    pAddressLayout->addWidget(m_pAddressNameLabel);
    pAddressLayout->addWidget(m_pAddressValueLabel);
    pAddressLayout->addStretch(1);

    QVBoxLayout *pTextLayout = new QVBoxLayout;
    pTextLayout->setMargin(0);
    pTextLayout->setSpacing(40);
    pTextLayout->addStretch(1);
    pTextLayout->addWidget(m_pIDLabel, 0, Qt::AlignLeft);
    pTextLayout->addLayout(pSNLayout);
    pTextLayout->addWidget(m_pFirmVerLabel, 0, Qt::AlignLeft);
    pTextLayout->addLayout(pDateLayout);
    pTextLayout->addWidget(m_pManufacturerLabel, 0, Qt::AlignLeft);
    pTextLayout->addStretch(1);

    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(40);
    pTopLayout->addStretch(1);
    pTopLayout->addWidget(m_pImageLabel);
    pTopLayout->addLayout(pTextLayout);
    pTopLayout->addStretch(1);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(40);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pCloseBtn);
    pBtnLayout->addWidget(m_pIdentifyBtn);
    pBtnLayout->addWidget(m_pResetBtn);
    pBtnLayout->addWidget(m_pEditBtn);
    pBtnLayout->addWidget(m_pScanBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setContentsMargins(24, 15, 24, 24);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pCHLabelTitleWidget, 0, Qt::AlignLeft);
    pLayout->addStretch(1);
    pLayout->addLayout(pTopLayout);
    pLayout->addStretch(1);
    pLayout->addLayout(pBtnLayout);

    int iWidth = 1350, iHeight = 760;
    if(eLanguage_Chinese == gk_iLanguage)
    {
        iWidth = 1100;
        iHeight = 700;
    }

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(iWidth, iHeight);
    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}
