#ifndef CMOTORCOMPOSE_H
#define CMOTORCOMPOSE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-11-21
  * Description:
  * -------------------------------------------------------------------------
  * History:
  * 2024-04-10 修改数据库调用
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QComboBox>
#include <QPushButton>
#include <QTableWidget>
#include <QStackedWidget>

#include "CCmdBase.h"
#include "CLabelComboBox.h"
#include "CLabelLineEdit.h"
#include "MotorCompose/CSixParams.h"
#include "MotorCompose/CThreeParams.h"
#include "MotorCompose/COptoCountTouchStop.h"
#include "MotorCompose/COptoCompose.h"

class CMotorCompose : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CMotorCompose(QWidget *parent = nullptr);
    ~CMotorCompose();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

public slots:
    void _SlotSoftTypeChanged(int iSoftType);
    void _SlotSixParmasConfirm(int iRow, const QString &strParmas);
    void _SlotThreeParmasConfirm(int iRow, const QString &strParmas);
    void _SlotOptoTouchStopConfirm(int iRow, const QString &strParmas);
    void _SlotOptoComposeConfirm(int iRow, const QString &strParmas);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual bool eventFilter(QObject *pObject, QEvent *pEvent) override;

private slots:
    void _SlotParseBtn();
    void _SlotFindBtn();
    void _SlotLoadBtn();
    void _SlotDelBtn();
    void _SlotMoveUpBtn();
    void _SlotMoveDownBtn();
    void _SlotCopyBtn();
    void _SlotListBtn();

private slots:
    void _SlotMachineComboBoxChanged(int);
    void _SlotMotorChangedBoxChanged(int);
    void _SlotCmdComboBoxChanged(int);
    void _SlotTComboBoxChanged(int);
    void _SlotSixParamsShow();
    void _SlotThreeParamsShow();
    void _SlotSetOptoComboBoxChanged(int); //设置光耦
    void _SlotOptoStopComboBoxChanged(int); //光耦触停
    void _SlotSpeedRunComboBoxChanged(int); //速度运行
    void _SlotGoStepsComboBoxChanged(int);  //走指定步
    void _SlotWithDirGoStepsComboBoxChanged(int);  //带方向走指定步
    void _SlotHomeEndMoveComboBoxChanged(int); //HOME END移动
    void _SlotOptoCountTouchStopShow(); //光耦计数触停
    void _SlotOptoComposeShow();   //组合光耦

private:
    void _AddOneRow();
    void _DelOneRow();
    void _SaveAndSendData();
    void _ClearTableWidget();
    void _ReadOne();
    void _ReadAll();
    void _ClearDB();
    void _ClearMachine();

private:
    void _InitTextList();
    void _LoadName2Table();
    void _InsertCmdIDName2Table(int iRow, QString strIDName);
    void _SetOneRowData(QTableWidget *pTableWidget, int iRow, const QStringList &oneList);
    QString _GetOneRowData(QTableWidget *pTableWidget, int iRow);

private:
    void _InitWidget();
    void _InitLayout();

private:
    int m_iUiMachineID;
    QStringList m_strIDNameList;
    QVariantList m_varReadAllIDList; //读取所有读到的ID 然后1个个去读取
    QStringList m_strMotorNameList;
    QStringList m_strCmdNameList;
    QStringList m_strMotorCompensateNameList; //电机补偿名称 从其他页面读取

private:
    CLabelComboBox *m_pMachineComboBox;
    CLabelComboBox *m_pMotorComboBox;
    CLabelLineEdit *m_pCmdIDLineEidt, *m_pCmdNameLineEdit, *m_pTimeoutLineEdit;
    CLineEdit *m_pCHTextLineEdit;
    CLineEdit *m_pFindLineEdit;
    QPushButton *m_pFindBtn;
    QTableWidget *m_pCmdTableWidget, *m_pFileTableWidget;
    QList<QPushButton *> m_pBtnList;

    QPushButton *m_pLoadBtn, *m_pDelBtn;
    QPushButton *m_pMoveUpBtn, *m_pMoveDownBtn;
    QPushButton *m_pCopyBtn;
    CLineEdit *m_pCopyLineEidt;

    CSixParams *m_pSixParams;
    CThreeParams *m_pThreeParams;
    COptoCountTouchStop *m_pOptoCountTouchStop;
    COptoCompose *m_pOptoCompose;
};

#endif // CMOTORCOMPOSE_H
