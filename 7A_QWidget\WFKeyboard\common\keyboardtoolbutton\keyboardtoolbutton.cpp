#include "keyboardtoolbutton.h"
#include <QEvent>
#include <QTouchEvent>
#include <QDebug>

class KeyBoardToolButton::PrivateData
{
public:
        PrivateData()
        {
            lowerCaseKeyChar = "";
            capitalKeyChar = "";
            lowerLongPressStr = "";
            capitalLongPressStr = "";

            isLongPress = false;
        }

public:
    bool isLongPress;
    QString lowerCaseKeyChar;
    QString capitalKeyChar;
    QString lowerLongPressStr;
    QString capitalLongPressStr;
    QString currentLongPressStr;
};

KeyBoardToolButton::KeyBoardToolButton(QWidget *parent)
    : QToolButton(parent)
    , md(new PrivateData())
{
    this->setObjectName("KeyBordButton");
    this->setMinimumHeight(35);

//    this->installEventFilter(this);
//    this->setAttribute(Qt::WA_AcceptTouchEvents);
}

KeyBoardToolButton::KeyBoardToolButton(QString lowerCase, QString capital,QString lowerLongPressStr,QString capitalLongPressStr, QWidget *parent)
    : QToolButton(parent)
    , md(new PrivateData())
{
    this->setObjectName("KeyBordButton");
    this->setMinimumHeight(35);

    md->lowerCaseKeyChar = lowerCase;
    md->capitalKeyChar = capital;
    md->lowerLongPressStr = lowerLongPressStr;
    md->capitalLongPressStr = capitalLongPressStr;

     if(!md->lowerLongPressStr.isEmpty() || !md->capitalLongPressStr.isEmpty())
     {
         md->isLongPress = true;
     }
     else
     {
         md->isLongPress = false;
     }
}

void KeyBoardToolButton::SetCharPropetry(QString lowerCase, QString capital,QString lowerLongPressStr,QString capitalLongPressStr)
{
    md->lowerCaseKeyChar = lowerCase;
    md->capitalKeyChar = capital;
    md->lowerLongPressStr = lowerLongPressStr;
    md->capitalLongPressStr = capitalLongPressStr;

    if(!md->lowerLongPressStr.isEmpty() || !md->capitalLongPressStr.isEmpty())
    {
        md->isLongPress = true;
    }
    else
    {
        md->isLongPress = false;
    }
    this->SetLowerCaseChar();
}

void KeyBoardToolButton::SetCapitalChar()
{
    this->setText(md->capitalKeyChar);
    md->currentLongPressStr = md->capitalLongPressStr;
}

void KeyBoardToolButton::SetLowerCaseChar()
{
    this->setText(md->lowerCaseKeyChar);

    md->currentLongPressStr = md->lowerLongPressStr;
}


bool KeyBoardToolButton::GetLongPressFlag()
{
    return md->isLongPress;
}

QString KeyBoardToolButton::GetCurrentLongPressStr()
{
    return md->currentLongPressStr;
}

void KeyBoardToolButton::mousePressEvent(QMouseEvent *e)
{
    emit pressEventSignal();
    return QToolButton::mousePressEvent(e);
}

void KeyBoardToolButton::mouseReleaseEvent(QMouseEvent *e)
{
    emit releaseEventSignal();
    return QToolButton::mouseReleaseEvent(e);
}
