﻿#ifndef _OBR_H_
#define _OBR_H_
#include "../interface/base.h"
#include "../interface/IOBR.h"
#include "../common/HL7Segment.h"

class OBR :
	public HL7Segment, public IOBR
{
public:
	OBR(OBRType obrType=SampleResult);

	void InitOBRSegment();

	virtual ~OBR();
	DECLARE_OBJECTBASE

	BEGIN_IMPL_QUERYIF(OBR)
		IMPL_QUERYIF_BYPATH(IF_OBJECTBASE, IHL7Segment, IObjectBase)
		IMPL_QUERYIF(IF_HL7SEGMENT, IHL7Segment)
		IMPL_QUERYIF(IF_OBR, IOBR)
		END_IMPL_QUERYIF()

	void SetOBRType(OBRType obrType);

	OBRType GetOBRType();
	/*
	 *	\brief OBR流水号，从1开始递增
	 */
	void SetOBRIndex(const char* obrIndexStr);

	void GetOBRIndex(char** obrIndexStr);
	//////////////////////适用于样本结果///////////////////////

	/*
	 *	\brief 设置样本条码,index 1
	 */
	void SetSampleBarcode(const char* sampleBarcode);

	void GetSampleBarcode(char** sampleBarcode);
	/*
	 *	\brief 设置样本编号,index 2
	 */
	void SetSampleID(const char* sampleID);

	void GetSampleID(char** sampleID);

    /*
    *	\brief 设置流水号,index 3
    */
    void SetSerialNumber(const char* serialNumber);

    void GetSerialNumber(char** serialNumber);

	/*
	 *	\brief 设置检验时间,index 6
	 */
	void SetTestTime(const char* testTime);

	void GetTestTime(char** testTime);

	/*
	 *	\brief 设置病人临床诊断信息, index 12
	 */

	void SetDiagnosticMessage(const char* diagnosticMessage);

	void GetDiagnosticMessage(char** diagnosticMessage);

	/*
	 *	\brief 设置样本送检时间, index 13
	 */
	void SetSubmitSampleTime(const char* submitSampleTime);

	void GetSubmitSampleTime(char** submitSampleTime);

	/*
	 *	\brief 设置样本类型， index 14
	 */
	void SetSampleType(const char* sampleType);

	void GetSampleType(char** sampleType);


	/*
	 *	\brief 设置送检医师，index 15
	 */
	void SetSubmittingPhysician(const char* submittingPhysician);

	void GetSubmittingPhysician(char** submittingPhysician);

	/*
	 *	\brief 设置送检科室，index 16
	 */
	void SetSubmittingDepartment(const char* submittingDepartment);

	void GetSubmittingDepartment(char** submittingDepartment);

	/*
	 *	\brief 设置主治医师，index 19
	 */
	void SetAttendingPhysician(const char* attendingPhysician);

	void GetAttendingPhysician(char** attendingPhysician);

	/*
	 *	\brief 设置治疗科室，index 20
	 */
	void SetTreatDepartment(const char* treatDepartment);

	void GetTreatDepartment(char** treatDepartment);
	////////////////////////适用于质控/////////////////////////////////

	/*
	 *	\brief 设置项目编号，index 1
	 */
	void SetProjectID(const char* projectID);

	void GetProjectID(char** projectID);

	/*
	 *	\brief 设置项目名称, index 2
	 */
	void SetProjectName(const char* projectName);

	void GetProjectName(char** projectName);

    /*
     *	\brief 设置流水号, index 3
     */
    void SetQcSerialNumber(const char* serialNumber);

    void GetQcSerialNumber(char** serialNumber);

	/*
	 *	\brief 设置质控日期, index 6
	 */
	void SetQCTime(const char* qcTime);

	void GetQCTime(char** qcTime);

	/*
	 *	\brief 设置质控液数量,index 10
	 */
	void SetQCLiquidQuantity(const char* qcLiquidQuantity);

	void GetQCLiquidQuantity(char** qcLiquidQuantity);

	/*
	 *	\brief 设置质控液编号,index 11
	 */
	void SetQCLiquidID(const char* qcLiquidID);

	void GetQCLiquidID(char** qcLiquidID);
	/*
	*	\brief 设置质控液名称,index 12
	*/
	void SetQCLiquidName(const char* qcLiquidName);

	void GetQCLiquidName(char** qcLiquidName);

	/*
	*	\brief 设置质控液有效期,index 13
	*/
	void SetQCLiquidValidDate(const char* qcLiquidValidDate);

	void GetQCLiquidValidDate(char** qcLiquidValidDate);

	/*
	*	\brief 设置质控液批号,index 14
	*/
	void SetQCLiquidBatchNo(const char* qcLiquidBatchNo);

	void GetQCLiquidBatchNo(char** qcLiquidBatchNo);
	/*
	*	\brief 设置质控范围,index 15
	*/
	void SetQCScope(const char* qcScope);

	void GetQCScope(char** qcScope);

	/*
	 *	\brief 设置质控液浓度水平，index 16
	 */
	void SetQCLiquidDensityLevel(const char* qcLiquidDensityLevel);

	void GetQCLiquidDensityLevel(char** qcLiquidDensityLevel);

	/*
	*	\brief 设置质控液浓度水平，index 17
	*/
	void SetQCLiquidDensityAV(const char* qcLiquidDensityAV);

	void GetQCLiquidDensityAV(char** qcLiquidDensityAV);

	/*
	*	\brief 设置质控液标准差，index 18
	*/
	void SetQCLiquidSD(const char* qcLiquidSD);

	void GetQCLiquidSD(char** qcLiquidSD);

	/*
	*	\brief 设置质控结果，index 19
	*/
	void SetQCTestResult(const char* qcTestResult);

	void GetQCTestResult(char** qcTestResult);


	/*
	*	\brief 设置质控试剂批号，index 20
	*/
	void SetQCReagentBatchNo(const char* qcReagentBatchNo);

	void GetQCReagentBatchNo(char** qcReagentBatchNo);

	virtual void Parse(const char* segmentStr, EncodingCharacters encodingCharacters);


	OBR& operator=(OBR& dsc);

private:
	HL7Field m_obrIndex;//1
	OBRType m_obrType; //默认值为SampleResult

	//适用于样本结果
	HL7Field m_sampleBarcode;//1样本条码号
	HL7Field m_sampleID;//2样本编号
    HL7Field m_serialNumber; //3流水号
	HL7Field m_testTime;//6检验日期
	HL7Field m_diagnosticMessage;//13病人临床诊断信息
	HL7Field m_sampleType;//14样本类型
	HL7Field m_submitSampleTime;//15送检时间
	HL7Field m_submittingPhysician;//16送检医生
	HL7Field m_submittingDepartment;//17送检科室
	HL7Field m_attendingPhysician;//19主治医生
	HL7Field m_treatDepartment;//20治疗科室

	//适用于质控结果
	HL7Field m_projectID;//1项目编号
	HL7Field m_projectName;//2项目名称
    HL7Field m_qcSerialNumber;//3流水号
	HL7Field m_qcTime;//6质控日期
	HL7Field m_qcLiquidQuantity;//10质控液数量
	HL7Field m_qcLiquidID;//11质控液编号
	HL7Field m_qcLiquidName;//12质控液名称
	HL7Field m_qcLiquidBatchNo;//13质控液批号
	HL7Field m_qcLiquidValidDate;//14质控液有效期
	HL7Field m_qcScope;//15质控范围
	HL7Field m_qcLiquidDensityLevel;//16质控液浓度水平
	HL7Field m_qcLiquidDensityAV;//17质控液均值
	HL7Field m_qcLiquidSD;//18质控液标准差
	HL7Field m_qcTestResult;//19质控测试结果
	HL7Field m_qcReagentBatchNo;//20质控试剂批号
};
REGISTER_CLASS(OBR);
#endif
