QLabel
{
    color: #6B788F;
    font-size: 24px;
    font-weight: 400;
    font-family: "Source Han Sans CN";
    border: 0px solid red;
}

QLabel#TitleIconLabel
{
   border-radius: 3px;
   background-color: #3D78E5;
}

QLabel#TitleTextLabel
{
   color: #353E4E;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
}

/*一级标题*/
QLabel#SysTitleLabel1
{
    color: #6B788F;
    font-size: 24px;
    font-weight: 400;
    font-family: "Source Han Sans CN";
}

/*一级标题*/
QLabel#SysTitleLabel2
{
    color: #353E4E;
    font-size: 24px;
    font-weight: 500;
    font-family: "Source Han Sans CN";
}

QLabel#GotoLabel
{
   color: #6B788F;
   font-size: 24px;
   font-family: "Source Han Sans CN";
}

QLabel#PageLabel
{
   color: #353E4E;
   font-size: 24px;
   font-weight: 400;
   font-family: "Source Han Sans CN";
}

QLineEdit
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    padding-left: 0px;
    border-radius: 20px;
    border: 0px solid #A1ABBB;
    background-color: #F3F8FF;
}
QLineEdit:disabled
{
    background-color: #A2A2A2;
}

QGroupBox
{
   border-radius: 32px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
       stop: 0 #DEE8FB, stop: 0.15 #FFF);
}

QPushButton#ExportBtn
{
   font-size: 22px;
   font-weight: 400;
   font-family: "Source Han Sans CN";
   border-radius: 22px;
}

QPushButton#GotoBtn
{
   font-size: 18px;
   font-weight: 400;
   font-family: "Source Han Sans CN";
   border-radius: 20px;
}

QPushButton#PrePageBtn
{
   background-color: transparent;
   image: url(:/image/ico/history/previous.png);
}
QPushButton#PrePageBtn:disabled
{
   background-color: transparent;
   image: url(:/image/ico/history/previous-disable.png);
}
QPushButton#NextPageBtn
{
   background-color: transparent;
   image: url(:/image/ico/history/next.png);
}
QPushButton#NextPageBtn:disabled
{
   background-color: transparent;
   image: url(:/image/ico/history/next-disable.png);
}

QTableWidget
{
    color: #353E4E;
    font-size: 20px;
    font-family: "Source Han Sans CN";
    selection-background-color: #60C8FF;
    alternate-background-color: #F2F2F2;
    border: 0px solid #D6DFE9;
    border-radius: 0px;
    background-color: #fff;
}

QHeaderView::section
{
    color: #353E4E;
    font-size: 24px;
    font-weight: 500;
    font-family:"Source Han Sans CN";
    border: 0px solid #D6DFE9;
    border-radius: 0px;
    background-color: #EAEDF6;
    height: 50px;
}
