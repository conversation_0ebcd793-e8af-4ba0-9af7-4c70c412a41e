#include "CPdfTcpServer.h"
#include <QProcess>
#include <QJsonObject>
#include <QJsonDocument>
#include <QApplication>
#include "PublicConfig.h"
#include "DBControl/CFtpDB.h"

CPdfTcpServer *CPdfTcpServer::m_spInstance = nullptr;

CPdfTcpServer *CPdfTcpServer::GetInstance()
{
    if(nullptr == m_spInstance)
        m_spInstance = new CPdfTcpServer;
    return m_spInstance;
}

CPdfTcpServer::CPdfTcpServer()
{
    connect(this, &CPdfTcpServer::SignalInitThread, this, &CPdfTcpServer::_SlotInitThread);
    connect(this, &CPdfTcpServer::SignalWriteData, this, &CPdfTcpServer::_SlotWriteData);

    m_pThread = new QThread;
    this->moveToThread(m_pThread);
    m_pThread->start();

    emit SignalInitThread();
}

CPdfTcpServer::~CPdfTcpServer()
{

}

void CPdfTcpServer::WriteData(QByteArray qByteData)
{
    emit SignalWriteData(qByteData);
}

void CPdfTcpServer::_SlotInitThread()
{
    qDebug()<<"pdf server线程:"<<QThread::currentThreadId();

    m_bConnected = false;
    m_iPort = 10000;
    m_strIP = "127.0.0.1";

    m_pTcpServer = new QTcpServer(this);
    m_pTcpSocket = new QTcpSocket(this);

    m_bServerInit = m_pTcpServer->listen(QHostAddress(m_strIP), m_iPort);
    if(!m_bServerInit)
    {
        RUN_LOG(QString("创建pdf tcp server失败,请检查网络设置.ip:127.0.0.1,port:10000,error:%1").arg(m_pTcpServer->errorString()));
        m_pTcpServer->close();
    }
    else
    {
        RUN_LOG(QString("创建pdf tcp server成功.ip:127.0.0.1,port:10000"));
        connect(m_pTcpServer, SIGNAL(newConnection()), this, SLOT(_SlotRecvConnection()));
        connect(m_pTcpSocket, SIGNAL(error(QAbstractSocket::SocketError)), this, SLOT(_SlotDisplayError(QAbstractSocket::SocketError)));
    }

    m_pRestartPDFTimer = new QTimer(this);
    connect(m_pRestartPDFTimer, SIGNAL(timeout()), this, SLOT(_SlotRestartPDFTimeout()));
    m_pRestartPDFTimer->start(10000);
}

void CPdfTcpServer::_SlotWriteData(QByteArray qByteData)
{
    qDebug()<<Q_FUNC_INFO<<qByteData.data();
    m_pTcpSocket->write(qByteData);
    m_pTcpSocket->flush();
}

void CPdfTcpServer::_SlotRecvConnection()
{
    m_bConnected = true;
    RUN_LOG("receive pdf tcp client connect");
    m_pTcpSocket = m_pTcpServer->nextPendingConnection();
    connect(m_pTcpSocket, SIGNAL(readyRead()), this, SLOT(_SlotReadMessage()));
    connect(m_pTcpSocket, &QTcpSocket::disconnected, this, &CPdfTcpServer::_SlotDisconnect);
}

void CPdfTcpServer::_SlotDisplayError(QAbstractSocket::SocketError err)
{
    qDebug()<<Q_FUNC_INFO<<"pdf server err:"<<err<<m_pTcpServer->errorString()<<m_pTcpSocket->errorString();
}

void CPdfTcpServer::_SlotReadMessage()
{
    QByteArray byteRead = m_pTcpSocket->readAll();
    qDebug()<<Q_FUNC_INFO<<byteRead.data();

    QJsonObject qRootObj;
    QJsonParseError qErr;
    QJsonDocument qJsonDoc = QJsonDocument::fromJson(byteRead, &qErr);
    if(QJsonParseError::NoError != qErr.error)
        return;

    qRootObj = qJsonDoc.object();
    if(qRootObj.contains("Log"))
        RUN_LOG(qRootObj.value("Log").toString());
    if(qRootObj.contains("PDFName"))
        CFtpDB::GetInstance()->AddFtpUploadFile(qRootObj.value("PDFName").toString());
}

void CPdfTcpServer::_SlotDisconnect()
{
    m_bConnected = false;
    qDebug()<<"pdf client disconnect";
}

void CPdfTcpServer::_SlotRestartPDFTimeout()
{
    if(!m_bServerInit)
        return;

    if(m_bConnected)
        return;

    qDebug()<<Q_FUNC_INFO<<"重启pdf client";
    QString strAppDirPath = QApplication::applicationDirPath() + "/";
#ifdef Q_OS_WIN
    QProcess::execute("taskkill /f /t /im 7CPDF.exe");
    QProcess::startDetached(strAppDirPath + "7CPDF.exe");
#else
    QProcess::execute("killall 7CPDF");
    QProcess::startDetached(strAppDirPath + "7CPDF");
#endif
}
