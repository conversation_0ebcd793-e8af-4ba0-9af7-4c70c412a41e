#ifndef PINGYINSELECTOR_H
#define PINGYINSELECTOR_H

#include <QWidget>
#include <QShowEvent>

class KeyBoard;
class PingYinSelector : public QWidget
{
    Q_OBJECT

public:
    explicit PingYinSelector(KeyBoard *keyboard);

    ~PingYinSelector();

    void SetActivate(bool bActive);

    void InPutChar(const QChar* );

    void DeleteInputChar();

    const QString &PinYin() const;

    QString GetCandidateWord(const int&);

    void SetKeyboardInputFlag(const bool&);

    void CandidateWordPageUp();

    void CandidateWordPageDown();

    void Reset();

    void SetPagingEnabled(const bool&);

    void SetPageSize(const int&);

protected:
    void showEvent(QShowEvent*);

private slots:
    void SlotCandidatesList(QList<QString>);

    void SlotScroolValueChange(int);

    void SlotSelectText(const QString&);

    void SlotPrevBtnClicked();

    void SlotNextBtnClicked();

private:
    class PrivateData;
    PrivateData *const md;
};

#endif // PINGYINSELECTOR_H
