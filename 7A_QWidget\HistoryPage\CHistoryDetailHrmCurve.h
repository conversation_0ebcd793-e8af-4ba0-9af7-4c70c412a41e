#ifndef CHISTORYDETAILHRMCURVE_H
#define CHISTORYDETAILHRMCURVE_H

#include <QWidget>
#include <QPushButton>
#include <QRadioButton>
#include <QTableWidget>

#include "CLineEdit.h"
#include "qcustomplot.h"
#include "CLabelLineEdit.h"
#include "CHLabelTitleWidget.h"
#include "HistoryPage/CManualHrmReviewWidget.h"
#include "PublicParams.h"

class CHistoryDetailHrmCurve : public QWidget
{
     Q_OBJECT
public:
    CHistoryDetailHrmCurve(bool bHistoryMode, QWidget *parent = nullptr);

    void ClearData();
    void ClearFL();
    void closeBtnHandle();

    void SetReviewMode(bool bReview); //审核模型生成的pdf隐藏原始曲线和审核项
    void ThresholdLlineShow(bool bShow);
    void ShowPCRNmzaData();
    void ShowHRMPeakData();
    void Reset2RawData();

    void ShowRawData();
    void SwitchTab(int iTab);
    void SwitchHole(int iHole);

    void SetHistoryIDHandle(const SCardInfoStruct&  sCardInfo, const SResultInfoStruct& sResultInfo,const SLotInfoStruct& sLotInfo);
    void UpdateTestReusltLabel(const SResultInfoStruct& sResultInfo,const SLotInfoStruct& sLotInfo);
    void UpdateTableWidget(const SResultInfoStruct& sResultInfo,const SLotInfoStruct& sLotInfo);
    void UpdateInfo(const SResultInfoStruct &sResult); //更新结果Label和CT表格
    void SetFLDataMap(const QList<QMap<double, double>> &dFLMap);
    void SetFLMeltingDataMap(const QList<double> &dTempList, const QList<QMap<double, double>> &dFLMap);


protected:
    void paintEvent(QPaintEvent* pEvent) override;
    void showEvent(QShowEvent *pEvent) override;
    void hideEvent(QHideEvent *pEvent) override;

private slots:
    void _SlotPcrRawRadioBtn();
    void _SlotPcrNmzaRadioBtn();
    void _SlotHrmPeakRadioBtn();
    void _SlotHrmRawRadioBtn();
    void _SlotChannelCheckBox();
    void _SlotTabBtn();
    void _SlotReviewBtn();
    void _SlotReviewConfirmBtn();
    void _SlotReviewConfirm(stHrmReviewParam stReviewParam);


private:
    struct sResultMeltingInfo
    {
        QString m_strCurveName;        
        QString m_strTempRangeValue;
        QString m_strTmValues;        
        QString m_strYmValues;
        QString m_strCtValues;
        QString m_strCtResult;
        QString m_strTestMode;

        bool m_bshowCt = false;

        QString m_strReview;
        bool m_bNull = false;
        bool m_bControl = false;        
        int m_index = -1; //位置索引
    };
    void UpdateTableWidgetHandle(QTableWidget* pTableWidget,QList<sResultMeltingInfo>& sInfolist);
    void _InitWidget();
    void _InitLayout();
    void _InitTableWidget();
    void _ChannelCheckBoxHandle(QCustomPlot* pCustomPlot);
    void _InitTabBtnWidget();
    void _InitPcrDataWidget();
    void _InitHrmDataWidget();
    void _SetThreshouldLineValue();
    QWidget* _InitChannelWidget();
    void _ChangeTableResult(QTableWidget* table,const stHrmReviewParam& sResultParam);

private:
    void _UpdatePcrRawPlot();
    void _UpdatePcrNmzaPlot();
    void _UpdateHrmRawPlot();
    void _UpdateHrmPeakPlot();
    void _UpdateTestResultLabel(const QString& strResult);

    void _AddGraph(QCustomPlot *pCustomPlot, const QColor &qColor, int iChart, const QString &strChartName);
    void _SetPlotData(const QList<double> &dTempDataList,const QList<QList<double>> &dFLDataList);
    void _SetPlotData(const QList<QList<double>> &dFLDataList);
    void _ResetYRang(const QCustomPlot* pCustomPlot,const QList<QList<double> > &dXList,const QList<QList<double> > &dYList);
    void _SetChannelCheckBox(const QString &qColor, int iChart, const QString &strChannelName);
    void _SetManualBtn(bool bReview,const SResultInfoStruct& sResultInfo);

private:
    bool m_bShow{false};

    QLabel *m_pTestResultLabel; //项目的总结果
    //QLabel *m_pTestResultLabel;
    QRadioButton *m_pHrmRawRadioBtn, *m_pHrmPeakRadioBtn;// 原始数据、峰值数据
    QCustomPlot *m_pHrmCustomPlot;
    QButtonGroup* m_HrmButton_group;
    QWidget* m_pHrmWidget;
    QRadioButton *m_pPcrRawRadioBtn, *m_pPcrNmzaRadioBtn;// 原始数据、归一化数据
    QCustomPlot *m_pPcrCustomPlot;
    QButtonGroup* m_PcrButton_group;
    QWidget* m_pPcrWidget;

    QStackedWidget* m_pPlotStackWidget;

    QList<QCPItemLine *> m_pDotLineFirstList;
    QList<QCPItemLine *> m_pDotLineSecondList;

    QList<QCheckBox *> m_pHoleACheckBoxList;
    QList<QCheckBox *> m_pHoleBCheckBoxList;
    QCheckBox* m_threshouldCheckBox;

    QTableWidget *m_pTableWidget;// 计算结果 表格
    QPushButton *m_pReviewCalcBtn;// 人工审核
    QPushButton *m_pReviewPassBtn; // 复审确认

    QList<QPushButton*> m_pTabBtnList;
    QLabel *m_pTopTabBackgroundLabel;


    QStringList m_strColorList;
    QStringList m_StrChannelNameList;

    SResultInfoStruct m_sResultInfo;
    SCardInfoStruct m_sCardInfo;
    SLotInfoStruct m_sLotInfo;

    const bool m_bHistoryMode;
    QList<QMap<double, double>> m_dFLMap;
    QList<QMap<double, double>> m_dMeltingFLMap;
    QList<double> m_dMeltingTempMap;
    QStringList m_strICNameList;
    QList<sResultMeltingInfo> m_sResultMeltingInfoList;
    const QString m_strPositive, m_strNegative, m_strError, m_strNull;
    CManualHrmReviewWidget *m_pManualReviewWidget;

};

#endif // CHISTORYDETAILHRMCURVE_H
