#include "languagebasekey.h"

#include <QSizePolicy>
#include <QButtonGroup>
#include <QLayout>

#include "common/keyboardtoolbutton/keyboardtoolbutton.h"

class LanguageBaseKey::PrivateData
{
public:
    PrivateData(LanguageBaseKey*p, QString n)
        : parent(p)
    {
        name                = n;
        initWgt             = false;
        capsLock            = false;
        wgt                 = NULL;
        leftCapsLockBtn     = new KeyBoardToolButton;
        rightCapsLockBtn    = new KeyBoardToolButton;

        leftCapsLockBtn->setObjectName("capsLockBtn");
        rightCapsLockBtn->setObjectName("capsLockBtn");

        leftCapsLockBtn->setCheckable(true);
        rightCapsLockBtn->setCheckable(true);
    }

    void InitConnect();

    void SetMapperProper();

    void ConnectMapper();

public:
    LanguageBaseKey* parent;

    bool initWgt;
    bool capsLock;
    QString name;
    QWidget* wgt;

    KeyBoardToolButton* leftCapsLockBtn;
    KeyBoardToolButton* rightCapsLockBtn;

    QSignalMapper *sm;
    QSignalMapper *pressedSm;
    QSignalMapper *releaseSm;

    QString chs;
    QString eng;
    QString math;
    QString space;

    QMap<int, QList<KeyBoardToolButton*> > buttonsMap;
};

void LanguageBaseKey::PrivateData::InitConnect()
{
    if(leftCapsLockBtn == NULL || rightCapsLockBtn == NULL)
        return;

    parent->connect(leftCapsLockBtn,SIGNAL(clicked()),parent,SLOT(SlotCapsLockBtnClicked()));
    parent->connect(rightCapsLockBtn,SIGNAL(clicked()),parent,SLOT(SlotCapsLockBtnClicked()));
}

void LanguageBaseKey::PrivateData::SetMapperProper()
{
    if(sm == NULL || pressedSm == NULL || releaseSm == NULL)
    {
        return;
    }

    for (QMap<int, QList<KeyBoardToolButton*> >::const_iterator iter = buttonsMap.constBegin(); iter != buttonsMap.constEnd(); ++iter)
    {
        QList<KeyBoardToolButton*> keyboardBtns = iter.value();
        for (int i = 0; i < keyboardBtns.count(); i++)
        {
            KeyBoardToolButton* button = keyboardBtns.at(i);
            sm->setMapping(button, button->text());
            pressedSm->setMapping(button, button);
            releaseSm->setMapping(button, button);
        }
    }
}

void LanguageBaseKey::PrivateData::ConnectMapper()
{
    if(sm == NULL || pressedSm == NULL || releaseSm == NULL)
    {
        return;
    }

    for (QMap<int, QList<KeyBoardToolButton*> >::const_iterator iter = buttonsMap.constBegin(); iter != buttonsMap.constEnd(); ++iter)
    {
        QList<KeyBoardToolButton*> keyboardBtns = iter.value();
        for (int i = 0; i < keyboardBtns.count(); i++)
        {
            KeyBoardToolButton* button = keyboardBtns.at(i);
            parent->connect(button, SIGNAL(clicked()), sm, SLOT(map()));
            parent->connect(button, SIGNAL(pressEventSignal()), pressedSm, SLOT(map()));
            parent->connect(button, SIGNAL(releaseEventSignal()), releaseSm, SLOT(map()));
        }
    }
}

LanguageBaseKey::LanguageBaseKey(const QString& name)
    :md(new PrivateData(this,name))
{
    md->InitConnect();
}

QString LanguageBaseKey::GetName()
{
    return md->name;
}

void LanguageBaseKey::SetLowerCase()
{
    md->capsLock = false;

    for (QMap<int, QList<KeyBoardToolButton*> >::const_iterator iter = md->buttonsMap.constBegin(); iter != md->buttonsMap.constEnd(); ++iter)
    {
        QList<KeyBoardToolButton*> keyboardBtns = iter.value();
        for (int i = 0; i < keyboardBtns.count(); i++)
        {
            keyboardBtns.at(i)->SetLowerCaseChar();
        }
    }
}

void LanguageBaseKey::SetCapital()
{
    md->capsLock = true;

    for (QMap<int, QList<KeyBoardToolButton*> >::const_iterator iter = md->buttonsMap.constBegin(); iter != md->buttonsMap.constEnd(); ++iter)
    {
        QList<KeyBoardToolButton*> keyboardBtns = iter.value();
        for (int i = 0; i < keyboardBtns.count(); i++)
        {
            keyboardBtns.at(i)->SetCapitalChar();
        }
    }
}

QWidget* LanguageBaseKey::GetWidget()
{
    if(md->wgt == NULL)
    {
        md->wgt = new QWidget;
    }

    if(!md->initWgt && !md->buttonsMap.isEmpty())
    {
        QVBoxLayout* vLyt = new QVBoxLayout;
        vLyt->setMargin(0);
        vLyt->setSpacing(10);

        for (QMap<int, QList<KeyBoardToolButton*> >::const_iterator iter = md->buttonsMap.constBegin(); iter != md->buttonsMap.constEnd(); ++iter)
        {
            QHBoxLayout* hLyt = new QHBoxLayout;
            hLyt->setMargin(0);
            hLyt->setSpacing(10);
            QList<KeyBoardToolButton*> keyboardBtns = iter.value();
            for (int i = 0; i < keyboardBtns.count(); i++)
            {
                KeyBoardToolButton* button = keyboardBtns.at(i);
                button->setSizePolicy(QSizePolicy::Preferred,QSizePolicy::Preferred);
                button->SetLowerCaseChar();
                hLyt->addWidget(button);
            }
            vLyt->addLayout(hLyt);
        }

        md->wgt->setLayout(vLyt);
        md->initWgt = true;
    }

    return md->wgt;
}

void LanguageBaseKey::SetSignalMapper(QSignalMapper *mapper, QSignalMapper *pressMapper, QSignalMapper *releaseMapper)
{
   md->sm        = mapper;
   md->pressedSm = pressMapper;
   md->releaseSm = releaseMapper;

   md->SetMapperProper();
   md->ConnectMapper();
}

void LanguageBaseKey::SlotCapsLockBtnClicked()
{
    if(md->leftCapsLockBtn == NULL || md->rightCapsLockBtn == NULL)
        return;

    md->capsLock = !md->capsLock;
    md->leftCapsLockBtn->setChecked(md->capsLock);
    md->rightCapsLockBtn->setChecked(md->capsLock);

    if(md->capsLock)
    {
        for (QMap<int, QList<KeyBoardToolButton*> >::const_iterator iter = md->buttonsMap.constBegin(); iter != md->buttonsMap.constEnd(); ++iter)
        {
            QList<KeyBoardToolButton*> keyboardBtns = iter.value();
            for (int i = 0; i < keyboardBtns.count(); i++)
            {
                keyboardBtns.at(i)->SetCapitalChar();
            }
        }
    }else
    {
        for (QMap<int, QList<KeyBoardToolButton*> >::const_iterator iter = md->buttonsMap.constBegin(); iter != md->buttonsMap.constEnd(); ++iter)
        {
            QList<KeyBoardToolButton*> keyboardBtns = iter.value();
            for (int i = 0; i < keyboardBtns.count(); i++)
            {
                keyboardBtns.at(i)->SetLowerCaseChar();
            }
        }
    }

    md->SetMapperProper();
}

void LanguageBaseKey::SetButtonsMap(const QMap<int, QList<KeyBoardToolButton*> >& map)
{
    md->buttonsMap = map;
}

void LanguageBaseKey::SetTranslate(const QString &chs, const QString &eng, const QString &math, const QString &space)
{
    md->chs     = chs;
    md->eng     = eng;
    md->math    = math;
    md->space   = space;
}

void LanguageBaseKey::GetTranslate(QString &chs, QString &eng, QString &math, QString &space)
{
    chs     = md->chs;
    eng     = md->eng;
    math    = md->math;
    space   = md->space;
}

KeyBoardToolButton* LanguageBaseKey::GetLeftCapsLockBtn()
{
    return md->leftCapsLockBtn;
}

KeyBoardToolButton* LanguageBaseKey::GetRightCapsLockBtn()
{
    return md->rightCapsLockBtn;
}
