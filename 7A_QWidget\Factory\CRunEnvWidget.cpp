#include "CRunEnvWidget.h"
#include <QLabel>
#include <QBoxLayout>
#include "PublicFunction.h"

CRunEnvWidget::CRunEnvWidget(QWidget *parent) : QWidget(parent)
{
    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(10);
    pLayout->setSpacing(0);
    pLayout->addWidget(_CreateGroupBox(), 0, Qt::AlignTop);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}

QGroupBox *CRunEnvWidget::_CreateGroupBox()
{
    QLabel *pHardLabel = new QLabel(tr("硬件配置："));
    pHardLabel->setFixedHeight(40);
    pHardLabel->setObjectName("RunEnvLabel");

    QLabel *pFreqLabel = new QLabel(tr("主频：2GHz"));
    pFreqLabel->setFixedHeight(40);

    QLabel *pDiskLabel = new QLabel(tr("电子硬盘：16G"));
    pDiskLabel->setFixedHeight(40);

    QLabel *pLCDLabel = new QLabel(tr("液晶屏：15.6 inch，彩色TFT"));
    pLCDLabel->setFixedHeight(40);

    QLabel *pResoLabel = new QLabel(tr("分辨率：1920*1080"));
    pResoLabel->setFixedHeight(40);

    QLabel *pScreenLabel = new QLabel(tr("触摸屏：电容式触控面板"));
    pScreenLabel->setFixedHeight(40);

    QLabel *pSoftLabel = new QLabel(tr("软件环境："));
    pSoftLabel->setFixedHeight(40);
    pSoftLabel->setObjectName("RunEnvLabel");

    QLabel *pSystemLabel = new QLabel(tr("操作系统："));
    pSystemLabel->setFixedHeight(40);

    QLabel *pLinuxLabel = new QLabel("Linux：V4.1");
    pLinuxLabel->setFixedHeight(40);

    QLabel *pRtosLabel = new QLabel("FreeRTos: V10.0");
    pRtosLabel->setFixedHeight(40);

    QLabel *pAppLabel = new QLabel(tr("应用软件："));
    pAppLabel->setFixedHeight(40);

    QLabel *pSqlLabel = new QLabel(tr("SQLite：V3.36"));
    pSqlLabel->setFixedHeight(40);

    QLabel *pMidLabel = new QLabel(tr("中间件："));
    pMidLabel->setFixedHeight(40);

    QLabel *pQtLabel = new QLabel(tr("Qt：V5.12"));
    pQtLabel->setFixedHeight(40);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(10);
    pLayout->setSpacing(10);
    pLayout->addWidget(pHardLabel);
    pLayout->addWidget(pFreqLabel);
    pLayout->addWidget(pDiskLabel);
    pLayout->addWidget(pLCDLabel);
    pLayout->addWidget(pResoLabel);
    pLayout->addWidget(pScreenLabel);
    pLayout->addSpacing(20);
    pLayout->addWidget(pSoftLabel);
    pLayout->addWidget(pSystemLabel);
    pLayout->addWidget(pLinuxLabel);
    pLayout->addWidget(pRtosLabel);
    pLayout->addWidget(pAppLabel);
    pLayout->addWidget(pSqlLabel);
    pLayout->addWidget(pMidLabel);
    pLayout->addWidget(pQtLabel);
    pLayout->addStretch(1);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(800, 630);
    pGroupBox->setObjectName("RunEnvGroup");
    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}
