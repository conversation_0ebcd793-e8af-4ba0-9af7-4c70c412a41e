#ifndef COPTICALCALIBRATION_H
#define COPTICALCALIBRATION_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: system
  * Date: 2024-12-19
  * Description: 光学校准
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QTextBrowser>
#include <QStackedWidget>
#include <QTableWidget>
#include "CCmdBase.h"
#include "CLabelComboBox.h"
#include "CLabelLineEdit.h"
#include "CBusyProgressBar.h"
#include "CLightOneTiming.h"

class COpticalCalibration : public QWidget, public CCmdBase
{
    Q_OBJECT
public:
    explicit COpticalCalibration(QWidget* parent = nullptr);
    ~COpticalCalibration();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant& qVarData) override;
    void ReceiveMDTData(const QVariant &qVarData);
    void startGainCalibration();
    void stopCalibration();          // 停止校准，执行后置时序
    void ClearAllData();

private slots:
    void _SlotFamCalibrateBtn();
    void _SlotHexCalibrateBtn();
    void _SlotRoxCalibrateBtn();
    void _SlotCY5CalibrateBtn();
    void _SlotMachineChanged(int iMachineID);
    void _SlotFamValueChange(const QString &strValue);
    void _SlotHexValueChange(const QString &strValue);
    void _SlotRoxValueChange(const QString &strValue);
    void _SlotCY5ValueChange(const QString &strValue);
    void _SlotTimingEnd(void);
    void _SlotPreTimingEnd(void);    // 前置时序完成槽函数
    void _SlotTimingStopped(void);   // 时序停止槽函数
    void _SlotStopCalibration(void); // 停止校准槽函数

    private:
    void _InitWidget();
    void _SetTableItem(int iRow, int iCol, QString strText);
    void _SetTableWidget(int iRow, int iCol, int iWidth, int iHeight, QWidget *pUIWidget);
    void _setGain(int index, int hole1Gain, int hole2Gain);
    void _startFineSearch();  // 开始精细搜索
private:

    typedef struct {
        int currentGain[4];
        int minGain[4];
        int maxGain[4];
    }sLightHoleGain_t;
    sLightHoleGain_t m_holeGain[2];

    struct {
        int value[4];
    }m_lightTestValue[2];

    // 新增二分查找相关成员变量
    int m_targetValue[4];
    bool m_isCalibrating;
    int m_currentColor;
    CLightOneTiming *m_pLightOneTiming;

    // 精细搜索相关成员变量
    bool m_isFineSearching;          // 是否正在进行精细搜索
    int m_fineSearchIndex;           // 当前精细搜索索引
    QList<int> m_fineSearchGains[2]; // 两个孔的精细搜索增益列表
    struct {
        int gain;
        int value;
        int diff;
    } m_fineSearchResults[2][5];     // 两个孔各5个增益的测试结果

private:
    CLabelComboBox *m_pMachineComboBox;
    CBusyProgressBar *m_pCBusyProgressBar;
    CLabelLineEdit *m_pFamValueLineEdit, *m_pHexValueLineEdit, *m_pRoxValueLineEdit, *m_pCY5ValueLineEdit;
    QPushButton *m_pFamCalibrateBtn, *m_pHexCalibrateBtn, *m_pRoxCalibrateBtn, *m_pCY5CalibrateBtn;
    QTableWidget *m_pTableWidget;
    int minGain, maxGain;

};

#endif // COPTICALCALIBRATION_H 