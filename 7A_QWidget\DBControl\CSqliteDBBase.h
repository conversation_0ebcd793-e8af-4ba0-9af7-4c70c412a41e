#ifndef CSQLITEDBBASE_H
#define CSQLITEDBBASE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-10-30
  * Description: 数据库基类
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QMap>
#include <QMutex>
#include <QObject>
#include <QSqlDatabase>

typedef struct _SQueryParamsStruct
{
    _SQueryParamsStruct()
    {
        strTypeList.clear();
        strTable.clear();
        strCondiMap.clear();
    }

    QStringList strTypeList;           //查询字段
    QString strTable;                  //表格名称
    QMap<QString,QString> strCondiMap; //查询条件  where
}SQueryParamsStruct;

class CSqliteDBBase
{
public:
    CSqliteDBBase(QString strDBPath, QString strConnectName);
    virtual ~CSqliteDBBase();

    QString GetLastErrorString() const;
    bool ReOpenDataBaseFromUDisk();// U盘替换后重新打开

protected:
    bool _ExecuteDB(const QString &strCmd);
    bool _ExecuteDB(const QStringList &strCmdList);
    bool _QueryDB(const QString &strCmd, QList<QStringList> &strDataList);
    bool _QueryDB(const SQueryParamsStruct &params, QList<QStringList> &strDataList);
    bool _DeleteTable(QString strTableName);

    QString _GetFirstValue(const QList<QStringList> &strList);
    QStringList _GetColumnValueList(const QList<QStringList> &strList);
    QStringList _GetRowValueList(const QList<QStringList> &strList);

private:
    bool _OpenDataBase();
    bool _CloseDataBase();

protected:
    QString m_strDBName;
    QString m_strDBPath;
    QString m_strConnectName;
    QString m_strDefaultErr;
    QString m_strLastError;

private:
    QMutex m_mutex;
    QSqlDatabase m_qSqldb;

    Q_DISABLE_COPY(CSqliteDBBase)
};

#endif // CSQLITEDBBASE_H
