#include "CSerialThread.h"
#include <QDebug>
#include "PublicFunction.h"
#include "PublicConfig.h"
#include "COperationUnit.h"
#include "CmdBus/CCmdBase.h"

#define MAX_PRINT_LEN 256

static int GetNumber(QByteArray byteData)
{
    if(2 != byteData.size())
        return 0;

    char ch = byteData[0];
    byteData[0] = byteData[1];
    byteData[1] = ch;

    int num = byteData.toHex().toInt(nullptr, 16);
    return num;
}

CSerialThread* CSerialThread::m_spInstance = nullptr;

CSerialThread::CSerialThread()
{
    qRegisterMetaType<SCanBusDataStruct>("SCanBusDataStruct");
    m_pSerialPort = nullptr;
    m_bOpenSerialPort = false;
    connect(this, &CSerialThread::SignalInitThread, this, &CSerialThread::_SlotInitThread);
    connect(this, &CSerialThread::SignalExitThread, this, &CSerialThread::_SlotExitThread, Qt::BlockingQueuedConnection);
    m_pThread = new QThread;
    this->moveToThread(m_pThread);
    m_pThread->start();

    emit SignalInitThread();
}

CSerialThread::~CSerialThread()
{
    emit SignalExitThread();

    if(m_pThread->isRunning())
    {
        m_pThread->quit();
        m_pThread->wait();
    }

    m_pThread->deleteLater();

    printf("CSerialThread::~CSerialThread()\n");
}

CSerialThread* CSerialThread::GetInstance()
{
    if(nullptr == m_spInstance)
        m_spInstance = new CSerialThread();
    return m_spInstance;
}

void CSerialThread::FreeInstance()
{
    if(nullptr != m_spInstance)
    {
        delete m_spInstance;
        m_spInstance = nullptr;
    }
}

void CSerialThread::ResetSerial(QString strNewSerialName, QString strBandRate)
{
    emit SignalResetSerial(strNewSerialName,strBandRate);
}

void CSerialThread::WriteSerial(const SCanBusDataStruct &sSCanBusDataStruct)
{
    emit SignalAddWriteMap(sSCanBusDataStruct);
}

QByteArray CSerialThread::GetReciveCanMsg()
{
    QByteArray frame;
    if(!m_pMutex->tryLock(1000))
        return frame;

    if(m_receiveFrameList.isEmpty())
    {
        m_pMutex->unlock();
        return frame;
    }

    qDebug()<<"***************** GetReciveCanMsg"<<m_receiveFrameList.size();
    frame = m_receiveFrameList.takeFirst();
    m_pMutex->unlock();

    return frame;
}

QList<QByteArray> CSerialThread::GetReciveCanMsgList()
{
    QMutexLocker locker(m_pMutex);

    QList<QByteArray> qFrameList;
    if(m_receiveFrameList.isEmpty())
        return qFrameList;

    int iLen = m_receiveFrameList.size();
    qDebug()<<Q_FUNC_INFO<<iLen;
    if(iLen <= 10)
    {
        qFrameList = m_receiveFrameList;
        m_receiveFrameList.clear();
    }
    else
    {
        qFrameList = m_receiveFrameList.mid(0, 10);
        m_receiveFrameList = m_receiveFrameList.mid(10, iLen - 10);
    }
    return qFrameList;
}

bool CSerialThread::IsOpenSuccess()
{
    return m_bOpenSerialPort;
}

void CSerialThread::_SlotInitThread()
{
    qDebug()<<"上位机通信串口线程ID:"<<QThread::currentThreadId();
    m_iSendSeq = 0;
    m_iLoseTimes = 0;
    m_pMutex = new QMutex;

    _InitSerial("COM19", "115200");
    connect(this, &CSerialThread::SignalResetSerial, this, &CSerialThread::_SlotResetSerial);
    connect(this, &CSerialThread::SignalAddWriteMap, this, &CSerialThread::_SlotAddWriteMap);

    m_pRunTimer = new QTimer(this);
    connect(m_pRunTimer, &QTimer::timeout, this, &CSerialThread::_SlotRunTimeout);
    m_pRunTimer->start(10);

    //7D00140F
    //{\"id\":254,\"method\":\"heartBeat\",\"result\":0,\"params\":[20000, 0, 1]}
    QByteArray heartbeat= "404D4478150003000000FA3E000042007B226964223A3235342C226D6574686F64223A22686561727442656174222C22726573756C74223A302C22706172616D73223A5B32303030302C20302C20315D";

    //{\"method\":\"version\",\"id\":775,\"result\":0,\"params\":{\"FW\":\"v1.0.42.702b\",\"HW\":\"10B\",\"BUILT\":\"Jan 13 2023\",\"BOOTVER\":\"v1.0.570D\"}}
    QByteArray strMapCmd = "404D4478150003030000010000007F007B226D6574686F64223A2276657273696F6E222C226964223A3737352C22726573756C74223A302C22706172616D73223A7B224657223A2276312E302E34322E37303262222C224857223A22313042222C224255494C54223A224A616E2031332032303233222C22424F4F54564552223A2276312E302E35373044227D7D00C966";

    //{\"id\":5,\"method\":\"status\",\"result\":0,\"params\":{\"state\":0}}
    QByteArray intMapCmd = "404D4478150003000000000000003B007B226964223A352C226D6574686F64223A22737461747573222C22726573756C74223A302C22706172616D73223A7B227374617465223A307D7D00D8B3";

    //{\"id\":366,\"method\":\"GAAM\",\"result\":0,\"params\":[0, 0, 0, 32, 96, 64, 0, 66, 6, 18, 18, 18, 0, 8, 107, 255]}
    QByteArray intListCmd = "404D4478150003010000020000006B007B226964223A3336362C226D6574686F64223A224741414D222C22726573756C74223A302C22706172616D73223A5B302C20302C20302C2033322C2039362C2036342C20302C2036362C20362C2031382C2031382C2031382C20302C20382C203130372C203235355D7D00C38F";

    QByteArray hex = heartbeat + strMapCmd + intMapCmd + intListCmd;
    QByteArray byte = QByteArray::fromHex(hex);
    //m_readBuff = byte; //for test
}

void CSerialThread::_SlotExitThread()
{
    if(m_pRunTimer)
    {
        m_pRunTimer->stop();
        delete m_pRunTimer;
        m_pRunTimer = nullptr;
    }

    if(m_pSerialPort)
    {
        m_pSerialPort->close();
        delete m_pSerialPort;
        m_pSerialPort = nullptr;
    }
}

void CSerialThread::_HandleSendBuff()
{
    if(m_sendFrameMap.isEmpty())
        return;

    int iSendSeq = m_sendFrameMap.firstKey();
    STSendStruct &st = m_sendFrameMap.first();

    if(0 != st.iSendTimes)
    {
        int iMsc = st.qSendTime.msecsTo(QDateTime::currentDateTime());
        if(iMsc < gk_iWaitMsecs) //没到重发的时候
            return;

        if(st.iSendTimes > gk_iReSendTimes)
        {
            emit SignalACKOut(0);
            qDebug()<<"***************** ACK timeout"<<",seq:"<<iSendSeq;
            m_sendFrameMap.remove(iSendSeq);
            return;
        }
        qDebug()<<"***************** NO ACK Resend"<<",times:"<<st.iSendTimes<<",seq:"<<iSendSeq;
    }

    // if(st.qSendByte.length() < MAX_PRINT_LEN)
    qDebug()<<"***************** send frame data"<<st.qSendByte<<st.qSendByte.toHex().toUpper()<<",seq:"<<iSendSeq;
    // else
    //     qDebug()<<"***************** send frame len"<<st.qSendByte.length()<<",seq:"<<iSendSeq;

    if(!m_bOpenSerialPort)
        qDebug()<<"***************** 通信串口未打开,数据无法发送"<<m_strSerialName;
    else
        m_pSerialPort->write(st.qSendByte);

    st.iSendTimes++;
    st.qSendTime = QDateTime::currentDateTime();
}

void CSerialThread::_InitSerial(QString strSerialName, QString strBandRate)
{
    m_strSerialName = strSerialName;

    if(!m_pSerialPort)
        m_pSerialPort = new QSerialPort(this);
    else
        m_pSerialPort->close();

    QString strLog;
    m_pSerialPort->setPortName(strSerialName);
    if(m_pSerialPort->open(QIODevice::ReadWrite))
    {
        m_pSerialPort->setBaudRate(strBandRate.toInt());
        m_pSerialPort->setDataBits(QSerialPort::Data8);
        m_pSerialPort->setParity(QSerialPort::NoParity);
        m_pSerialPort->setFlowControl(QSerialPort::NoFlowControl);
        m_pSerialPort->setStopBits(QSerialPort::OneStop);
        connect(m_pSerialPort, &QSerialPort::readyRead, this, &CSerialThread::_SlotReadSerial);
        m_bOpenSerialPort = true;
        strLog = "上位机串口打开成功:" + strSerialName + " " + strBandRate;
    }
    else
    {
        m_bOpenSerialPort = false;
        strLog = "上位机串口打开失败:" + strSerialName + m_pSerialPort->errorString();
    }

    qDebug()<<strLog;
    emit SignalLog(m_bOpenSerialPort,strLog);
}

void CSerialThread::_SlotReadSerial()
{
    QByteArray temp = m_pSerialPort->readAll();
    if(temp.isEmpty())
        return;

    m_readBuff += temp;
}

void CSerialThread::_SlotResetSerial(QString strNewName, QString strBandRate)
{
    if(strNewName.isEmpty())
    {
        if(m_pSerialPort)
        {
            m_bOpenSerialPort = false;
            m_pSerialPort->close();
            emit SignalLog(m_bOpenSerialPort, "上位机串口已关闭");
        }
        return;
    }

    if(m_pSerialPort)
    {
        disconnect(m_pSerialPort, &QSerialPort::readyRead, this, &CSerialThread::_SlotReadSerial);
    }
    _InitSerial(strNewName,strBandRate);
}

void CSerialThread::_SlotAddWriteMap(SCanBusDataStruct sSCanBusDataStruct)
{
    QByteArray frame = _GetSendData(sSCanBusDataStruct,m_iSendSeq);

    qDebug()<<"***************** AddSendData"<<frame.mid(10,2).toHex().toUpper();
    STSendStruct st;
    st.iSendTimes = 0;
    st.qSendByte = frame;
    m_sendFrameMap.insert(m_iSendSeq,st);

    if(m_iSendSeq >= 65535)
        m_iSendSeq = 0;
    m_iSendSeq++;
}

void CSerialThread::_SlotRunTimeout()
{
    _HandleSendBuff();
    _HandleReadBuff();
}

void CSerialThread::_HandleReadBuff()
{
    if(m_readBuff.size() < 22)
        return;

    _HandleReadBuff(m_readBuff);
}
/*
 * 1.找@MDx,找不到则为无用数据,清空buff
 * 2.假设1找到的@MDx为头,按照协议获取seq len payload和crc,
 * 计算crc是否匹配:若匹配则为一帧数据,若不匹配则1找到的@MDx不为头,从接收buff删除开头到@MDX的数据.结束.
 * 3.获取一帧完整数据.如果是ack,获取seq,移除发送map key.不是ack,应答ack.保存在接收队列
 * 4.从接收buff删除开头到取得的完整数据
*/

void CSerialThread::_HandleReadBuff(QByteArray &readBuff)
{
    //不能每4个字节遍历找头,当前面有很多无用数据会造成大量耗时
    int headIndex = readBuff.indexOf("@MDx");
    if(headIndex < 0)
    {
        readBuff.clear();
        return;
    }

    //if(readBuff.length() < MAX_PRINT_LEN)
    //    qDebug()<<"readbuff:"<<readBuff.toHex().toUpper()<<",head index:"<<headIndex;

    int iReadSeq = GetNumber(readBuff.mid(headIndex+10,2));
    //qDebug()<<"seq:"<<readBuff.mid(headIndex+10,2).toHex().toUpper()<<iReadSeq;

    int iPayloadLen = GetNumber(readBuff.mid(headIndex+18,2));
    //qDebug()<<"payload len:"<<readBuff.mid(headIndex+14,2).toHex().toUpper()<<iPayloadLen;

    //处理接收长度大于18但没收完的情况
    if(iPayloadLen > 0)
    {
        int len = readBuff.length();
        QByteArray payload1 = readBuff.mid(headIndex+20,len-headIndex-20-2);
        //qDebug()<<"payload1:"<<payload1.toHex().toUpper()<<payload1;
        if(payload1.length() < iPayloadLen)
        {
            //qDebug()<<"***************** 指令未接收完:"<<readBuff.toHex().toUpper()<<readBuff<<m_iLoseTimes;
            if(m_iLoseTimes > 14)
            {
                m_readBuff.clear();
                m_iLoseTimes = 0;
                return;
            }
            m_iLoseTimes++;
            return;
        }

        //        QByteArray bytePayload = readBuff.mid(headIndex+16,iPayloadLen-1);
        //        if(bytePayload.length() < MAX_PRINT_LEN)
        //            qDebug()<<"payload data:"<<bytePayload.toHex().toUpper()<<bytePayload;
    }

    QByteArray byteCRC = readBuff.mid(headIndex+20+iPayloadLen,2);
    bool ok;
    int iReadCRC = byteCRC.toHex().toInt(&ok,16);
    int iGetCRC = GetCRC16(readBuff.data()+headIndex,iPayloadLen+20,0);
    //qDebug()<<"CRC:"<<byteCRC.toHex().toUpper()<<iReadCRC<<", calc CRC:"<<iGetCRC;
    if(iReadCRC != iGetCRC)
    {
        qDebug()<<"***************** bad crc,get CRC:"<<byteCRC.toHex().toUpper()<<iReadCRC<<",calc CRC:"<<iGetCRC;
        RUN_LOG("bad crc");
        qDebug()<<"head:"<<headIndex<<"total frame:"<<readBuff.toHex().toUpper();

        int iLen = readBuff.size();
        readBuff = readBuff.right(iLen-4-headIndex);
        return;
    }

    m_iLoseTimes = 0;
    QByteArray frame = readBuff.mid(headIndex,iPayloadLen+22);
    if(frame.length() < MAX_PRINT_LEN)
        qDebug()<<"***************** read frame data:"<<frame<<",seq:"<<iReadSeq;
    else
        qDebug()<<"***************** read frame len:"<<frame.length()<<",seq:"<<iReadSeq;
    if(0x02 == frame[6])
    {
        qDebug()<<"get ack data"<<iReadSeq;
        if(m_sendFrameMap.contains(iReadSeq))
            m_sendFrameMap.remove(iReadSeq);
    }
    else
    {
        SCanBusDataStruct sSCanBusDataStruct;
        sSCanBusDataStruct.quMachineID = frame[5];
        sSCanBusDataStruct.quCmdID = 0x02;
        sSCanBusDataStruct.quObjectID = frame[7];
        sSCanBusDataStruct.quFrameSeq = iReadSeq;
        QByteArray ackArray = _GetSendData(sSCanBusDataStruct,quint16(iReadSeq));
        qDebug()<<"***************** send ack"<<ackArray;
        if(m_bOpenSerialPort)
            m_pSerialPort->write(ackArray);

        if(m_pMutex->tryLock(1000))
        {
            m_receiveFrameList.push_back(frame);
            m_pMutex->unlock();
        }
    }

    qDebug()<<"***************** remove "<<readBuff.length()<<headIndex+iPayloadLen+22;
    readBuff = readBuff.remove(0,headIndex+iPayloadLen+22);
}

QByteArray CSerialThread::_GetSendData(SCanBusDataStruct sSCanBusDataStruct, quint16 quSeq)
{
    qDebug()<<"send cmd,iMachineID:"<<sSCanBusDataStruct.quMachineID
           <<"iMethodID:"<<sSCanBusDataStruct.quMethonID;
    //<<"data:"<<sSCanBusDataStruct.qbPayload;

    QByteArray qBlockByteArray;
    QDataStream qOutDataStream(&qBlockByteArray,QIODevice::ReadWrite);
    qOutDataStream.setByteOrder(QDataStream::LittleEndian);  // 设置xiao端格式
    qOutDataStream << quint16(0x4D40)
                   << quint16(0x7844)
                   << quint8(0x10)
                   << quint8(sSCanBusDataStruct.quMachineID)
                   << sSCanBusDataStruct.quCmdID
                   << sSCanBusDataStruct.quObjectID
                   << sSCanBusDataStruct.quSourceID
                   << sSCanBusDataStruct.quFormat;
    if(0x00 == sSCanBusDataStruct.quFrameSeq)
        qOutDataStream << quSeq;
    else
        qOutDataStream << sSCanBusDataStruct.quFrameSeq;
    qOutDataStream << sSCanBusDataStruct.quReserve;
    qOutDataStream << quint32(sSCanBusDataStruct.quMethonID);
    qOutDataStream << quint16(sSCanBusDataStruct.qbPayload.count());
    qBlockByteArray += sSCanBusDataStruct.qbPayload;
    qOutDataStream.device()->seek(20+sSCanBusDataStruct.qbPayload.count());
    quint16 iCrc16 = GetCRC16(qBlockByteArray.data(), qBlockByteArray.count(), 0);

    //qOutDataStream << quint16(GetSmallByte(iCrc16));
    //quint16 st = GetSmallByte(iCrc16);

    QByteArray byteCRC;
    byteCRC[0] = iCrc16 % 256;
    byteCRC[1] = iCrc16 / 256;
    qOutDataStream << byteCRC.toHex().toUShort(nullptr, 16);

    return qBlockByteArray;
}
