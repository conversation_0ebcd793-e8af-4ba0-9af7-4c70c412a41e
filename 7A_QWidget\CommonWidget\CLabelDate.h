#ifndef CLABELDATE_H
#define CLABELDATE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-10-31
  * Description:
  * -------------------------------------------------------------------------
  * History: 日期选择 label-lable(点击弹出选择日期)
  * 2025-06-23 hxr 把日期时间控件分离出来:(1)由全屏弹窗改为局部小弹窗(2)节省内存
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include "CPressLabel.h"

class CLabelDate : public QWidget
{
    Q_OBJECT
public:
    explicit CLabelDate(const QString &strName, const QString &strDate, int iSpacing = 5, QWidget *parent = nullptr);
    ~CLabelDate();

    QString GetDateString() const;
    void SetDateString(const QString &strDate);

    void SetLabelFixedSize(int iWidth, int iHeight);
    void SetDateFixedSize(int iWidth, int iHeight);

signals:
    void SignalPressEvent();

private:
    QLabel *m_pNameLabel;
    CPressLabel *m_pDateLabel;

    int m_iSpacing;
    QString m_strName;
    QString m_strDate;
};

#endif // CLABELDATE_H
