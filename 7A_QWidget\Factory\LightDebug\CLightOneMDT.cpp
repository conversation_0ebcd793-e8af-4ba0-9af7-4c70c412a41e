#include "CLightOneMDT.h"
#include <QBoxLayout>
#include "qcustomplot.h"
#include "CMessageBox.h"
#include "PublicParams.h"
#include "PublicConfig.h"
#include "PublicFunction.h"

CLightOneMDT::CLightOneMDT(int iMachineID, QWidget *parent)
    : QWidget(parent)
    , m_bShow(false)
    , m_bReplot(false)
    , m_iMachineID(iMachineID)
    , m_dMaxX(1000)
    , m_dMinY(0)
    , m_dMaxY(1000)
{
    for(int i=0; i<(gk_iHoleCount * gk_iBGYRCount); i++)
    {
        QVector<double> vec;
        m_dDataList.push_back(vec);
    }

    _InitWidget();
}

CLightOneMDT::~CLightOneMDT()
{

}

void CLightOneMDT::ExportData()
{
    if(!UDiskExist(this))
        return;

    QString strDateTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    QString strName = QString("%1#_MDT_%2.txt").arg(m_iMachineID + 1).arg(strDateTime);
    QString strData = m_pTextBrowser->toPlainText();
    WriteFile(strName, strData);
    QString strExportDir = CPublicConfig::GetInstance()->GetUDiskExportDir();
    QDir qExportdir(strExportDir);
    CreateDir(strExportDir);
    qDebug()<<strName<<strData<<strExportDir;
    MoveQFile(strName, qExportdir);
    ExportEndUmountUSB();

    ShowSuccess(this, tr("提示"), tr("MDT导出完成"));
}

void CLightOneMDT::ClearData()
{
    m_pTextBrowser->clear();

    m_dXList.clear();
    for(int i=0; i<m_dDataList.size(); i++)
        m_dDataList[i].clear();

    QVector<double> x, y;
    for(int i=0; i<4; i++)
        m_pCustomPlot->graph(i)->setData(x, y);
    m_pCustomPlot->replot();
}

void CLightOneMDT::SwitchPage()
{
    int index = m_pStackedWidget->currentIndex();
    m_pStackedWidget->setCurrentIndex((++index) % 2);
}

void CLightOneMDT::ReceiveMDTData(const QVariant &qVarData)
{
    QVariantList qVarList = qVarData.toList();
    if(qVarList.size() < 5)
        return;

    int iHole = qVarList.at(0).toInt(); // 0; 1
    if(iHole < 0 || iHole >= gk_iHoleCount)
        return;

    QString strLog = QString("%1").arg(iHole);

    int size = m_dXList.size();
    m_dXList.push_back(size);

    for(int i=0; i<gk_iBGYRCount; i++)
    {
        int index = 4 * iHole + i;
        double data = qVarList.at(i + 1).toDouble();
        strLog += QString(",%1").arg(data);
        m_dMaxY = qMax(m_dMaxY, data);
        m_dMinY = qMin(m_dMinY, data);
        m_dDataList[index].push_back(data);
        m_pCustomPlot->graph(i)->setData(m_dXList, m_dDataList.at(index));
    }
    m_pTextBrowser->append(strLog);
    m_pTextBrowser->moveCursor(QTextCursor::End);

    _UpdatePlot();
}

void CLightOneMDT::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;
    if(m_bReplot)
    {
        m_bReplot = false;
        m_pCustomPlot->replot();
    }
    QWidget::showEvent(pEvent);
}

void CLightOneMDT::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;
    QWidget::hideEvent(pEvent);
}

void CLightOneMDT::_SlotSetXYRange(const QStringList &strList)
{
    if(4 != strList.size())
        return;

    m_pCustomPlot->xAxis->setRange(strList.at(0).toDouble(), strList.at(1).toDouble());
    m_pCustomPlot->yAxis->setRange(strList.at(2).toDouble(), strList.at(3).toDouble());
    m_pCustomPlot->replot();
}

void CLightOneMDT::_SlotHoleIndexChanged(int iHole)
{
    for(int i=0; i<gk_iBGYRCount; i++)
    {
        int index = 4 * iHole + i;
        m_pCustomPlot->graph(i)->setData(m_dXList, m_dDataList.at(index));
    }
    m_pCustomPlot->replot();
}

void CLightOneMDT::_InitWidget()
{
    m_pTextBrowser = new QTextBrowser;
    m_pTextBrowser->setMinimumSize(this->width() - 100, this->height());
    m_pTextBrowser->append("mdt textbrowser");

    _InitCustomPlot();

    QStringList strRangeList = {"0", "1000", "0", "1000"};
    m_pCSetChartXYRange = new CSetChartXYRange(strRangeList);
    m_pCSetChartXYRange->SetLineEditTextAlignment();
    connect(m_pCSetChartXYRange, &CSetChartXYRange::SignalSetRange, this, &CLightOneMDT::_SlotSetXYRange);

    m_pHoleComboBox = new QComboBox;
    m_pHoleComboBox->setFixedSize(80, 50);
    m_pHoleComboBox->setView(new QListView);
    m_pHoleComboBox->addItems(CPublicConfig::GetInstance()->GetHoleNameList());
    connect(m_pHoleComboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotHoleIndexChanged(int)));

    QHBoxLayout *pHLayout = new QHBoxLayout;
    pHLayout->setMargin(0);
    pHLayout->addWidget(m_pCSetChartXYRange);
    pHLayout->addStretch(1);
    pHLayout->addWidget(m_pHoleComboBox);

    QVBoxLayout *pVLayout = new QVBoxLayout;
    pVLayout->setMargin(0);
    pVLayout->addWidget(m_pCustomPlot);
    pVLayout->addSpacing(5);
    pVLayout->addLayout(pHLayout);

    QWidget *pWidget = new QWidget;
    pWidget->setLayout(pVLayout);

    m_pStackedWidget = new QStackedWidget;
    m_pStackedWidget->addWidget(m_pTextBrowser);
    m_pStackedWidget->addWidget(pWidget);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->addWidget(m_pStackedWidget);
    this->setLayout(pLayout);
}

void CLightOneMDT::_InitCustomPlot()
{
    m_pCustomPlot = new QCustomPlot;

    QFont font;
    font.setPointSize(10);
    m_pCustomPlot->legend->setFont(font);
    m_pCustomPlot->legend->setSelectedFont(font);
    m_pCustomPlot->legend->setVisible(true);
    m_pCustomPlot->legend->setSelectableParts(QCPLegend::spItems);
    m_pCustomPlot->legend->setBorderPen(Qt::NoPen);
    m_pCustomPlot->legend->setWrap(1);
    m_pCustomPlot->axisRect()->insetLayout()->setInsetAlignment(0,Qt::AlignTop);

    _AddGraph(m_pCustomPlot, Qt::blue,   Qt::blue,   0, "B");
    _AddGraph(m_pCustomPlot, Qt::green,  Qt::green,  1, "G");
    _AddGraph(m_pCustomPlot, Qt::yellow, Qt::yellow, 2, "Y");
    _AddGraph(m_pCustomPlot, Qt::red,    Qt::red,    3, "R");

    m_pCustomPlot->xAxis->setRange(0, 1000);
    m_pCustomPlot->yAxis->setRange(0, 1000);
}

void CLightOneMDT::_AddGraph(QCustomPlot *pCustomPlot, QColor penColor, QColor pointColor, int iChart, QString strChartName)
{
    QPen pen;
    pen.setWidth(2);
    pen.setColor(penColor);
    pCustomPlot->addGraph();
    pCustomPlot->graph(iChart)->setPen(pen);
    pCustomPlot->graph(iChart)->setName(strChartName);
    pCustomPlot->graph(iChart)->setAntialiasedFill(true);
    pCustomPlot->graph(iChart)->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssNone,
                                                                QPen(pointColor, 1),
                                                                QBrush(pointColor), 3));
}

void CLightOneMDT::_UpdatePlot(bool bResetRange)
{
    if(bResetRange)
    {
        if(!m_dXList.isEmpty())
            m_dMaxX = m_dXList.last() * 1.2;
        if(m_dMinY < 0)
            m_dMinY *= 1.2;
        QStringList strList = {QString::number(0), QString::number(m_dMaxX),
                               QString::number(m_dMinY), QString::number(m_dMaxY)};
        m_pCSetChartXYRange->SetRange(strList);
        m_pCustomPlot->yAxis->setRange(m_dMinY, m_dMaxY);
        m_pCustomPlot->xAxis->setRange(0, m_dMaxX);
    }

    m_bReplot = true;
    if(m_bShow)
    {
        m_bReplot = false;
        m_pCustomPlot->replot();
    }
}
