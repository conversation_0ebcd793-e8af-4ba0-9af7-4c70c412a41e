#include "CPasswordLineEdit.h"
#include <QIcon>
#include <QBoxLayout>

CPasswordLineEdit::CPasswordLineEdit(QWidget *parent) : QLineEdit(parent)
{
    m_bSee = false;

    _InitWidget();
    _InitLayout();
    this->setEchoMode(QLineEdit::Password);
}

QString CPasswordLineEdit::GetPassword() const
{
    return this->text();
}

void CPasswordLineEdit::_SlotSeeBtn()
{
    QString strImagePath = "";
    if(!m_bSee)
    {
        m_bSee = true;
        this->setEchoMode(QLineEdit::Normal);
        strImagePath = ":/image/ico/login/see.png";
    }
    else
    {
        m_bSee = false;
        this->setEchoMode(QLineEdit::Password);
        strImagePath = ":/image/ico/login/no_see.png";
    }
    m_pSeeBtn->setIcon(QIcon(strImagePath));
    m_pSeeBtn->setIconSize(QSize(32, 32));
}

void CPasswordLineEdit::_InitWidget()
{
    m_pIconLabel = new QLabel;
    m_pIconLabel->setFixedSize(32, 32);
    m_pIconLabel->setCursor(QCursor(Qt::ArrowCursor));
    m_pIconLabel->setPixmap(QPixmap(":/image/ico/login/password.png"));

    m_pSeeBtn = new QPushButton;
    m_pSeeBtn->setFixedSize(32, 32);
    m_pSeeBtn->setObjectName("SeeBtn");
    m_pSeeBtn->setCursor(QCursor(Qt::ArrowCursor));
    m_pSeeBtn->setIcon(QIcon(":/image/ico/login/no_see.png"));
    m_pSeeBtn->setIconSize(QSize(32, 32));
    connect(m_pSeeBtn, &QPushButton::clicked, this, &CPasswordLineEdit::_SlotSeeBtn);
}

void CPasswordLineEdit::_InitLayout()
{
    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addSpacing(24);
    pLayout->addWidget(m_pIconLabel);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pSeeBtn);
    pLayout->addSpacing(24);
    this->setLayout(pLayout);
}
