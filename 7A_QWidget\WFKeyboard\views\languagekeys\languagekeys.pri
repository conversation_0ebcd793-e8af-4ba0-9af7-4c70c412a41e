INCLUDEPATH += $$PWD

FORMS +=

HEADERS += \
    $$PWD/arabickey/arabickey.h \
    $$PWD/bulgariakey/bulgariakey.h \
    $$PWD/croatiakey/croatiakey.h \
    $$PWD/englishkey/englishkey.h \
    $$PWD/finnishkey/finnishkey.h \
    $$PWD/frenchkey/frenchkey.h \
    $$PWD/germankey/germankey.h \
    $$PWD/greekkey/greekkey.h \
    $$PWD/italiankey/italiankey.h \
    $$PWD/kazakhkey/kazakhkey.h \
    $$PWD/polskikey/polskikey.h \
    $$PWD/portugalkey/portugalkey.h \
    $$PWD/romaniakey/romaniakey.h \
    $$PWD/russiakey/russiakey.h \
    $$PWD/spanishkey/spanishkey.h \
    $$PWD/turkishkey/turkishkey.h \
    $$PWD/ukrainekey/ukrainekey.h \
    $$PWD/languagebasekey.h \


SOURCES += \
    $$PWD/arabickey/arabickey.cpp \
    $$PWD/bulgariakey/bulgariakey.cpp \
    $$PWD/croatiakey/croatiakey.cpp \
    $$PWD/englishkey/englishkey.cpp \
    $$PWD/finnishkey/finnishkey.cpp \
    $$PWD/frenchkey/frenchkey.cpp \
    $$PWD/germankey/germankey.cpp \
    $$PWD/greekkey/greekkey.cpp \
    $$PWD/italiankey/italiankey.cpp \
    $$PWD/kazakhkey/kazakhkey.cpp \
    $$PWD/polskikey/polskikey.cpp \
    $$PWD/portugalkey/portugalkey.cpp \
    $$PWD/romaniakey/romaniakey.cpp \
    $$PWD/russiakey/russiakey.cpp \
    $$PWD/spanishkey/spanishkey.cpp \
    $$PWD/turkishkey/turkishkey.cpp \
    $$PWD/ukrainekey/ukrainekey.cpp \
    $$PWD/languagebasekey.cpp \


RESOURCES +=


