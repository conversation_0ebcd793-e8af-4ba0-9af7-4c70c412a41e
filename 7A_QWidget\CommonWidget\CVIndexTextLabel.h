#ifndef CVINDEXTEXTLABEL_H
#define CVINDEXTEXTLABEL_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-24
  * Description: 数字序号-内容 垂直布局 主页输入信息界面使用
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>

class CVIndexTextLabel : public QWidget
{
    Q_OBJECT
public:
    CVIndexTextLabel(const QString &strIndex, const QString &strData, QWidget *parent = nullptr);

    void SetOjbName(const QString &strIndexObjName, const QString &strTextObjName);
    void SetPropertyText(const QString &strPropertyName, const QString &strPropertyData);

private:
    QLabel *m_pIndexLabel, *m_pTextLabel;
};

#endif // CVINDEXTEXTLABEL_H
