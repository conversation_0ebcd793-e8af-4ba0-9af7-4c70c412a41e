#include "CMessageBox.h"
#include <QDebug>
#include <QPainter>
#include <QBoxLayout>
#include <QFontMetrics>
#include "PublicParams.h"
#include "PublicFunction.h"

static int G_IWindowPopNums = 0;

static QString AddEndSymbol(const QString &strRawText, POPMSG_TYPE eMsgType)
{
    QString strNewText = strRawText;
    if(eLanguage_Chinese == gk_iLanguage)
    {
        if(ePopMsg_Question == eMsgType)
        {
            if(!strNewText.endsWith("？"))
                strNewText += "？";
        }
        else if(ePopMsg_Success == eMsgType)
        {
            if(!strNewText.endsWith("。"))
                strNewText += "。";
        }
        else
        {
            if(!strNewText.endsWith("！"))
                strNewText += "！";
        }
    }
    else
    {
        if(ePopMsg_Question == eMsgType)
        {
            if(!strNewText.endsWith("?"))
                strNewText += "?";
        }
        else if(ePopMsg_Success == eMsgType)
        {
            if(!strNewText.endsWith("."))
                strNewText += ".";
        }
        else
        {
            if(!strNewText.endsWith("!"))
                strNewText += "!";
        }
    }
    return strNewText;
}

CNewMessageBox::CNewMessageBox(const QString &strTitle, const QString &strText, POPMSG_TYPE eMsgType, QWidget *parent)
    : QDialog(parent)
    , m_strTitle(strTitle)
    , m_strText(strText)
    , m_eMsgType(eMsgType)
    , m_eStandardBtn(QMessageBox::No)
{    
    LoadQSS(this, ":/qss/qss/message.qss");

    G_IWindowPopNums++;
    m_strText = AddEndSymbol(strText, eMsgType);
    qDebug()<<__FUNCTION__<<strTitle<<m_strText<<G_IWindowPopNums;

    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setAttribute(Qt::WA_TranslucentBackground);
    this->setAttribute(Qt::WA_DeleteOnClose);
    this->setFixedSize(parent ? parent->size() : G_QRootSize);
    this->move(0, 0);

    _InitWidget();
    _InitLayout();
}

CNewMessageBox::~CNewMessageBox()
{
    G_IWindowPopNums--;
    qDebug()<<Q_FUNC_INFO<<G_IWindowPopNums;
}

QMessageBox::StandardButton CNewMessageBox::GetClickedBtn() const
{
    return m_eStandardBtn;
}

void CNewMessageBox::paintEvent(QPaintEvent *pEvent)
{
    int iTransparency = 20;
    if(G_IWindowPopNums >= 5)
        iTransparency = 1;

    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, iTransparency));
    QWidget::paintEvent(pEvent);
}

void CNewMessageBox::_SlotCancelBtn()
{
    m_eStandardBtn = QMessageBox::No;
    emit SignalCancel();
    this->close();
}

void CNewMessageBox::_SlotConfirmBtn()
{
    m_eStandardBtn = QMessageBox::Yes;
    emit SignalConfirm();
    this->close();
}

QString CNewMessageBox::_GetIconPath(POPMSG_TYPE eMsgType)
{
    QString strIconPath;
    switch (eMsgType)
    {
    case ePopMsg_Info:
        strIconPath = ":/image/ico/test/information.png";
        break;
    case ePopMsg_Error:
        strIconPath = ":/image/ico/test/error.png";
        break;
    case ePopMsg_Success:
        strIconPath = ":/image/ico/test/success.png";
        break;
    case ePopMsg_Question:
        strIconPath = ":/image/ico/test/question.png";
        break;
    case ePopMsg_Warning:
        strIconPath = ":/image/ico/test/warning.png";
        break;
    case ePopMsg_Crital:
        strIconPath = ":/image/ico/test/error.png";
        break;
    default:
        strIconPath = ":/image/ico/test/information.png";
        break;
    }
    return strIconPath;
}

void CNewMessageBox::_InitWidget()
{
    m_pCHLabelTitleWidget = new CHLabelTitleWidget(m_strTitle);

    m_pIconLabel = new QLabel;
    m_pIconLabel->setFixedSize(40, 40);
    m_pIconLabel->setPixmap(QPixmap(_GetIconPath(m_eMsgType)));

    int iMaxLen = 40;
    int iBtnWidth = 140;
    if(eLanguage_Chinese == gk_iLanguage)
        iMaxLen = 54;
    if(eLanguage_German == gk_iLanguage)
        iBtnWidth = 150;

    m_pTextLabel = new QLabel(m_strText);
    if(m_strText.toLocal8Bit().length() >= iMaxLen)
    {
        m_pTextLabel->setFixedWidth(450);
        m_pTextLabel->setWordWrap(true);
        m_pTextLabel->setText(m_strText);
    }

    if(ePopMsg_Question == m_eMsgType)
    {
        m_pCancalBtn = new QPushButton(tr("取消"));
        m_pCancalBtn->setFixedSize(iBtnWidth, 56);
        m_pCancalBtn->setObjectName("CancelBtn");
        connect(m_pCancalBtn, &QPushButton::clicked, this, &CNewMessageBox::_SlotCancelBtn);
    }

    m_pConfirmBtn = new QPushButton(tr("确认"));
    m_pConfirmBtn->setFixedSize(iBtnWidth, 56);
    m_pConfirmBtn->setObjectName("ConfirmBtn");
    connect(m_pConfirmBtn, &QPushButton::clicked, this, &CNewMessageBox::_SlotConfirmBtn);

    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(596, 374);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");
}

void CNewMessageBox::_InitLayout()
{
    QHBoxLayout *pLabelLayout = new QHBoxLayout;
    pLabelLayout->setMargin(0);
    pLabelLayout->setSpacing(0);
    pLabelLayout->addStretch(1);
    pLabelLayout->addWidget(m_pIconLabel);
    pLabelLayout->addSpacing(20);
    pLabelLayout->addWidget(m_pTextLabel);
    pLabelLayout->addStretch(1);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(0);
    pBtnLayout->addStretch(1);
    if(ePopMsg_Question == m_eMsgType)
    {
        pBtnLayout->addWidget(m_pCancalBtn);
        pBtnLayout->addSpacing(30);
    }
    pBtnLayout->addWidget(m_pConfirmBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pBackLayout = new QVBoxLayout;
    pBackLayout->setContentsMargins(24, 12, 24, 24);
    pBackLayout->setSpacing(0);
    pBackLayout->addWidget(m_pCHLabelTitleWidget, 0, Qt::AlignLeft);
    pBackLayout->addStretch(1);
    pBackLayout->addLayout(pLabelLayout);
    pBackLayout->addStretch(1);
    pBackLayout->addLayout(pBtnLayout);
    m_pBackgroundLabel->setLayout(pBackLayout);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pBackgroundLabel, 0, Qt::AlignCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}

QMessageBox::StandardButton ShowInformation(QWidget *parent, const QString &title, const QString &text)
{
    CNewMessageBox *pCNewMessageBox = new CNewMessageBox(title, text, ePopMsg_Info, parent);
    pCNewMessageBox->showNormal();
    return pCNewMessageBox->GetClickedBtn();
}

QMessageBox::StandardButton ShowError(QWidget *parent, const QString &title, const QString &text)
{
    CNewMessageBox *pCNewMessageBox = new CNewMessageBox(title, text, ePopMsg_Error, parent);
    pCNewMessageBox->showNormal();
    return pCNewMessageBox->GetClickedBtn();
}

QMessageBox::StandardButton ShowSuccess(QWidget *parent, const QString &title, const QString &text)
{
    CNewMessageBox *pCNewMessageBox = new CNewMessageBox(title, text, ePopMsg_Success, parent);
    pCNewMessageBox->showNormal();
    return pCNewMessageBox->GetClickedBtn();
}

//提问的一定要阻塞,否则无法确定返回值
QMessageBox::StandardButton ShowQuestion(QWidget *parent, const QString &title, const QString &text)
{
    CNewMessageBox *pCNewMessageBox = new CNewMessageBox(title, text, ePopMsg_Question, parent);
    if(-1 == pCNewMessageBox->exec())
        return QMessageBox::No;
    return pCNewMessageBox->GetClickedBtn();
}

QMessageBox::StandardButton ShowWarning(QWidget *parent, const QString &title, const QString &text)
{
    CNewMessageBox *pCNewMessageBox = new CNewMessageBox(title, text, ePopMsg_Warning, parent);
    pCNewMessageBox->showNormal();
    return pCNewMessageBox->GetClickedBtn();
}

QMessageBox::StandardButton ShowCritical(QWidget *parent, const QString &title, const QString &text)
{
    CNewMessageBox *pCNewMessageBox = new CNewMessageBox(title, text, ePopMsg_Crital, parent);
    pCNewMessageBox->showNormal();
    return pCNewMessageBox->GetClickedBtn();
}
