#ifndef CSHOWDETAIL_H
#define CSHOWDETAIL_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-01-22
  * Description: 详情 传入QStringList,窗口关闭自销毁
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QGroupBox>
#include <QPushButton>
#include <QTextBrowser>
#include "CHLabelTitleWidget.h"

class CShowLogDetail : public QWidget
{
    Q_OBJECT
public:
    explicit CShowLogDetail(QWidget *parent = nullptr);

    void ShowTextList(const QStringList &strList);

protected:
    void paintEvent(QPaintEvent* pEvent) override;

private slots:
    void _SlotCloseButton();

private:
    QGroupBox *_CreateGroup();

private:
    CHLabelTitleWidget *m_pCHLabelTitleWidget;
    QPushButton *m_pCloseButton;
    QTextBrowser *m_pTextBrower;
};

#endif // CSHOWDETAIL_H
