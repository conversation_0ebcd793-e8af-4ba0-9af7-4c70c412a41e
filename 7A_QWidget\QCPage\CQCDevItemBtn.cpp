#include "CQCDevItemBtn.h"
#include <QStyle>
#include <QDebug>
#include <QVariant>
#include <QBoxLayout>
#include "PublicConfig.h"

CQCDevItemBtn::CQCDevItemBtn(int iMachineID, QWidget *parent)
    : QPushButton(parent)
    , m_iMachineID(iMachineID)
{
    this->setFixedSize(140, 190);
    this->setObjectName("ItemBtn");

    m_pIndexLabel = new QLabel(QString("%1").arg(m_iMachineID + 1));
    m_pIndexLabel->setFixedSize(60, 50);
    m_pIndexLabel->setObjectName("IndexLabel");
    m_pIndexLabel->setProperty("status", "idle");
    m_pIndexLabel->setAlignment(Qt::AlignCenter);

    m_pTextLabel = new QLabel(tr("空闲中"));
    m_pTextLabel->setObjectName("TipsLabel");
    m_pTextLabel->setProperty("status", "idle");
    m_pTextLabel->setWordWrap(true);
    m_pTextLabel->setAlignment(Qt::AlignTop | Qt::AlignHCenter);
    m_pTextLabel->setIndent(5);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addSpacing(45);
    pLayout->addWidget(m_pIndexLabel, 0, Qt::AlignHCenter);
    pLayout->addSpacing(20);
    pLayout->addWidget(m_pTextLabel, 0, Qt::AlignHCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalSetDevStatus, this, &CQCDevItemBtn::SlotSetDevStatus);

    SlotSetDevStatus(iMachineID, eDeviceDisconnect);
}

int CQCDevItemBtn::GetMachineID() const
{
    return m_iMachineID;
}

DeviceStatus CQCDevItemBtn::GetStatus() const
{
    return m_eDeviceStatus;
}

void CQCDevItemBtn::SlotSetDevStatus(int iMachineID, DeviceStatus eStatus)
{
    if(m_iMachineID != iMachineID)
        return;

    m_eDeviceStatus = eStatus;

    QString strPropertyText, strTipsText;
    switch (eStatus)
    {
    case eDeviceDisconnect:
        strPropertyText = "disconnect";
        strTipsText = tr("离线中");
        break;
    case eDeviceSelfTest:
        strPropertyText = "self_test";
        strTipsText = tr("自检中");
        break;
    case eDeviceIdle:
        strPropertyText = "idle";
        strTipsText = tr("空闲中");
        break;
    case eDeviceTesting:
        strPropertyText = "testing";
        strTipsText = tr("测试中");
        break;
    case eDeviceTestDone:
        strPropertyText = "test_done";
        strTipsText = tr("测试完成");
        break;
    case eDeviceTestStopped:
        strPropertyText = "test_stopped";
        strTipsText = tr("测试停止");
        break;
    case eDeviceTestStopping:
        strPropertyText = "test_stopping";
        strTipsText = tr("停止中");
        break;
    case eDeviceTestFail:
        strPropertyText = "test_fail";
        strTipsText = tr("测试失败");
        break;
    case eDeviceFault:
        strPropertyText = "fault";
        strTipsText = tr("故障中");
        break;
    case eDeviceProcessing:
        strPropertyText = "processing";
        strTipsText = tr("处理中");
        break;
    case eDeviceReset:
        strPropertyText = "reset";
        strTipsText = tr("复位中");
        break;
    default:
        break;
    }

    m_pIndexLabel->setProperty("status", strPropertyText);
    m_pIndexLabel->style()->polish(m_pIndexLabel);

    m_pTextLabel->setProperty("status", strPropertyText);
    m_pTextLabel->style()->polish(m_pTextLabel);

    m_pTextLabel->setText(strTipsText);
}
