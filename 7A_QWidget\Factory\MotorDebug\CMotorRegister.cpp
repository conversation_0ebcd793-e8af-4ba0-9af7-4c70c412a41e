#include "CMotorRegister.h"
#include <QGridLayout>
#include <QBoxLayout>
#include <QtMath>
#include "CMessageBox.h"

CMotorRegister::CMotorRegister(QWidget *parent) : QWidget(parent)
{
    m_iNumList << 16 << 19 << 20 << 32<< 35 << 36 << 37 << 38
               << 39 << 40 << 42 << 43<< 44 << 45 << 108;

    Register2Map(Method_RREGA);
    Register2Map(Method_SREGA);
    Register2Map(Method_CREGA);

    _InitWidget();

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalSoftTypeChanged,
            this, &CMotorRegister::SlotSoftTypeChanged);
}

CMotorRegister::~CMotorRegister()
{
    UnRegister2Map(Method_RREGA);
    UnRegister2Map(Method_SREGA);
    UnRegister2Map(Method_CREGA);
}

void CMotorRegister::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    QString strLog = QString("%1#").arg(iMachineID + 1);
    if(Method_SREGA == iMethodID)
        qDebug()<<strLog<<"设置寄存器参数:"<<iResult;
    else if(Method_CREGA == iMethodID)
        qDebug()<<strLog<<"清空寄存器参数:"<<iResult;
    else if(Method_RREGA == iMethodID)
    {
        QStringList strList = qVarData.toString().split(SPLIT_BETWEEN_CMD);
        if(strList.isEmpty())
            return;
        m_pComboBoxList.at(0)->SetCurrentIndex(strList.at(0).toInt());

        for(int i=0; i<strList.size(); i++)
        {
            QStringList oneList = strList.at(i).split(SPLIT_IN_CMD);
            if(2 != oneList.size())
                continue;

            int iNum = oneList.first().toInt();
            QString strValue = oneList.last();
            if(108 == iNum)  //MERS特殊处理是text不是index
            {
                m_pComboBoxList.last()->SetCurrentText(strValue);
                continue;
            }

            int index = m_iNumList.indexOf(QVariant(iNum));
            if(index < 0)
                continue;

            if(0 == index)
            {
                QStringList valueList = getRegisterItemValueFromData(strValue);
                for(int i=0; i<valueList.size(); i++)
                    m_pLineEditList.at(i)->SetLineEditText(valueList.at(i));
            }
            else
            {
                if(index + 3 >= m_pWidgetList.size())
                    continue;

                QWidget *pWidget = m_pWidgetList.at(index + 3);
                CLabelComboBox *pComboBox = dynamic_cast<CLabelComboBox *>(pWidget);
                if(pComboBox)
                    pComboBox->SetCurrentIndex(strValue.toInt());
                CLabelLineEdit *pLineEdit = dynamic_cast<CLabelLineEdit *>(pWidget);
                if(pLineEdit)
                    pLineEdit->SetLineEditText(strValue);
            }
        }
    }
}

void CMotorRegister::SlotSoftTypeChanged(int iSoftType)
{
    Q_UNUSED(iSoftType);
    m_pComboBoxList.at(0)->SetComboBoxList(CPublicConfig::GetInstance()->GetMotorNameList());
}

void CMotorRegister::showEvent(QShowEvent *pEvent)
{ 
    QWidget::showEvent(pEvent);
}

void CMotorRegister::_SlotBtnClicked()
{
    QPushButton *pBtn = dynamic_cast<QPushButton *>(sender());
    if(nullptr == pBtn)
        return;

    int index = pBtn->property("index").toInt();
    switch (index)
    {
    case 0: _ReadParam(); break;
    case 1: _SetParam(); break;
    case 2:
    {
        //QVariant data = "3;16,197121;20,25;32,1;35,26;36,27;37,28;39,30;40,31;42,32;43,33;44,34;45,35;108,256;";
        //receiveMachineCmdReplay(0, Method_RREGA, true, data);
        break;
    }
    case 3: break;
    case 4: _ClearParam(); break;
    default: break;
    }
}

void CMotorRegister::_ReadParam()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    int iMotorIndex = m_pComboBoxList.at(0)->GetCurrentIndex();
    QVariantList qVarList = m_iNumList;
    qVarList.push_front(iMotorIndex);
    QString strCmd = GetJsonCmdString(Method_RREGA, qVarList);    
    qDebug()<<QString("%1#读取寄存器:%2").arg(iMachineID + 1).arg(strCmd);
    SendJsonCmd(iMachineID, Method_RREGA, strCmd);
}

void CMotorRegister::_SetParam()
{
    QString q32Value = getRegisterItemValue(m_pLineEditList.at(0)->GetLineEditText(),
                                            m_pLineEditList.at(1)->GetLineEditText(),
                                            m_pLineEditList.at(2)->GetLineEditText());
    QString strWriteData = QString("%1;").arg(m_pComboBoxList.at(0)->GetCurrentIndex());
    if(!q32Value.isEmpty())
        strWriteData += QString("%1,%2;").arg(m_iNumList.first().toInt()).arg(q32Value);

    for(int i=0; i<14; i++)
    {
        int iNum = m_iNumList.at(i + 1).toInt();
        CLabelComboBox *pComboBox = dynamic_cast<CLabelComboBox *>(m_pWidgetList.at(i + 4));
        if(pComboBox)
        {
            int index = pComboBox->GetCurrentIndex();
            if(index > 0)
            {
                if(pComboBox == m_pComboBoxList.last()) //MERS特殊处理是text不是index
                    strWriteData += QString("%1,%2;").arg(iNum).arg(pComboBox->GetCurrentText());
                else
                    strWriteData += QString("%1,%2;").arg(iNum).arg(index);
            }
        }
        CLabelLineEdit *pLineEdit = dynamic_cast<CLabelLineEdit *>(m_pWidgetList.at(i + 4));
        if(pLineEdit)
        {
            QString text = pLineEdit->GetLineEditText();
            if(!text.isEmpty())
                strWriteData += QString("%1,%2;").arg(iNum).arg(text);
        }
    }

    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    QString strCmd = GetJsonCmdString(Method_SREGA, QVariant(strWriteData));
    qDebug()<<QString("%1#设置寄存器参数:").arg(iMachineID + 1).arg(strCmd);
    SendJsonCmd(iMachineID, Method_SREGA, strCmd);
}

void CMotorRegister::_ClearParam()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定要清除寄存器参数吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    int iMotorIndex = m_pComboBoxList.at(0)->GetCurrentIndex();

    for(int i=0; i<m_pComboBoxList.size(); i++)
        m_pComboBoxList.at(i)->SetCurrentIndex(-1);
    for(int i=0; i<m_pLineEditList.size(); i++)
        m_pLineEditList.at(i)->SetLineEditText("");

    QVariantList qVarList = m_iNumList;
    qVarList.push_front(iMotorIndex);

    QString strCmd = GetJsonCmdString(Method_CREGA, qVarList);
    qDebug()<<QString("%1#清空寄存器参数:").arg(iMachineID + 1).arg(strCmd);
    SendJsonCmd(iMachineID, Method_CREGA, strCmd);
}

QString CMotorRegister::getRegisterItemValue(QString strIhold, QString strIrun, QString strIholddelay)
{
    quint8 q8IHOLD  = strIhold.toUInt();
    quint16 q16IRUN  = strIrun.toUInt();
    quint32 q32Left = strIholddelay.toUInt();
    qDebug() << "q8IHOLD " << q8IHOLD << q16IRUN << q32Left;
    //
    q8IHOLD = ((q8IHOLD << 4) >> 4);
    quint16 qRightValue = q8IHOLD;
    q16IRUN = (q16IRUN << 12)  >> 4;
    qRightValue = qRightValue | q16IRUN;
    q32Left = (q32Left << 28) >> 12;
    quint32 q32Value = qRightValue;
    q32Value = q32Value | q32Left;
    qDebug() << "q8IHOLD " << q8IHOLD << q16IRUN << q32Left << q32Value;
    QString strValue = "";
    if(q32Value > 0)
    {
        strValue = QString::number(q32Value);
    }
    return strValue;
}

QStringList CMotorRegister::getRegisterItemValueFromData(QString strValue)
{
    QStringList strReturn;
    quint32 q32Value = strValue.toUInt();
    quint16 q16RightValue = q32Value;
    quint8 q8IHOLD = q16RightValue;
    q8IHOLD = (q8IHOLD << 4) >> 4;
    strReturn.push_back(QString::number(q8IHOLD));
    q16RightValue = (q16RightValue << 4) >> 12;
    quint8 q8IRUN = q16RightValue;
    strReturn.push_back(QString::number(q8IRUN));
    q32Value = (q32Value << 12) >> 28;
    quint8 q8IHOLDDELAY = q32Value;
    strReturn.push_back(QString::number(q8IHOLDDELAY));
    return strReturn;
}

void CMotorRegister::_InitWidget()
{
    QGridLayout *pGridLayout = new QGridLayout;
    pGridLayout->setContentsMargins(20, 0, 20, 0);
    pGridLayout->setHorizontalSpacing(10);
    pGridLayout->setVerticalSpacing(20);

    QStringList strList = {tr("电机"), "IHOLD", "IRUN", "IHOLDDELAY", "TPWMTHRS", "TCOOLTHRS",
                           "RAMPMODE", "VSTART", "A1", "V1", "AMAX", "VMAX", "DMAX", "D1", "VSTOP",
                           "TZEROWAIT", "XTARGET", "MERS"};
    for(int i=0; i<strList.size(); i++)
    {
        if(0 == i || 6 == i || 17 == i)
        {
            CLabelComboBox *pComboBox = new CLabelComboBox(strList.at(i));
            pComboBox->SetLabelFixedSize(130, 50);
            pComboBox->SetComboBoxFixedSize(170, 50);
            m_pComboBoxList.push_back(pComboBox);

            m_pWidgetList.push_back(pComboBox);
            pGridLayout->addWidget(pComboBox, i / 3, i % 3);
        }
        else
        {
            CLabelLineEdit *pLineEdit = new CLabelLineEdit(strList.at(i));
            pLineEdit->SetLabelFixedSize(130, 50);
            pLineEdit->SetLineEditFixedSize(170, 50);
            m_pLineEditList.push_back(pLineEdit);

            m_pWidgetList.push_back(pLineEdit);
            pGridLayout->addWidget(pLineEdit, i / 3, i % 3);
        }
    }

    m_pComboBoxList.at(0)->SetComboBoxList(CPublicConfig::GetInstance()->GetMotorNameList());

    QStringList strModeList = {"", tr("位置模式"), tr("速度模式到正"), tr("速度模式到负"), tr("保持模式")};
    m_pComboBoxList.at(1)->SetComboBoxList(strModeList);

    QStringList strMERSList = {""};
    for(int i=0; i<10; i++)
        strMERSList.push_back(QString::number(qPow(2, i)));
    m_pComboBoxList.last()->SetComboBoxList(strMERSList);

    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxMinSize(110, 50);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(20);
    pBtnLayout->addSpacing(60);
    pBtnLayout->addWidget(m_pMachineComboBox);

    QStringList strBtnNameList = {tr("读取"), tr("设置"), tr("导入"), tr("导出"), tr("清除参数")};
    for(int i=0; i<strBtnNameList.size(); i++)
    {
        QPushButton *pBtn = new QPushButton(strBtnNameList.at(i));
        pBtn->setMinimumSize(110, 50);
        pBtn->setProperty("index", i);
        connect(pBtn, &QPushButton::clicked, this, &CMotorRegister::_SlotBtnClicked);

        pBtnLayout->addWidget(pBtn);
    }
    pBtnLayout->addSpacing(60);

    QVBoxLayout *pMainLayout = new QVBoxLayout;
    pMainLayout->setMargin(0);
    pMainLayout->addLayout(pGridLayout);
    pMainLayout->addSpacing(30);
    pMainLayout->addLayout(pBtnLayout);
    pMainLayout->addSpacing(30);
    pMainLayout->addStretch(1);
    this->setLayout(pMainLayout);
}
