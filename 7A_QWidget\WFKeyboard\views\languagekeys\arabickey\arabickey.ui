<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ArabicKey</class>
 <widget class="QWidget" name="Arabic<PERSON>ey">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>828</width>
    <height>160</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QStackedWidget" name="stackedWidget_Normal">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="page_Qwerty_Board">
      <layout class="QVBoxLayout" name="verticalLayout">
       <property name="spacing">
        <number>3</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <layout class="QHBoxLayout" name="layout_Normal_Row02">
         <property name="spacing">
          <number>2</number>
         </property>
         <item>
          <widget class="KeyBordToolButton" name="toolButton1_1">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Arial</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="autoRepeat">
            <bool>false</bool>
           </property>
           <property name="autoRepeatInterval">
            <number>100</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton1_2">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton1_3">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton1_4">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton1_5">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton1_6">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton1_7">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton1_8">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton1_9">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton1_10">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton1_11">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton1_12">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="layout_Normal_Row03">
         <property name="spacing">
          <number>2</number>
         </property>
         <item>
          <widget class="KeyBordToolButton" name="toolButton2_1">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton2_2">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton2_3">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton2_4">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton2_5">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton2_6">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton2_7">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton2_8">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton2_9">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton2_10">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton2_11">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="layout_Normal_Row04" stretch="0,10,10,10,10,10,10,10,10,10,10,10,10,0">
         <property name="spacing">
          <number>2</number>
         </property>
         <item>
          <spacer name="horizontalSpacer_7">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>6</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_capslock">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>100</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>20</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="icon">
            <iconset>
             <normaloff>:/newImg/newImg/KeyBorder/icon_small_normal.png</normaloff>
             <normalon>:/images/KeyBorder/icon_big.png</normalon>
             <activeoff>:/images/KeyBorder/icon_small_normal.png</activeoff>
             <activeon>:/newImg/newImg/KeyBorder/icon_big.png</activeon>:/newImg/newImg/KeyBorder/icon_small_normal.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>40</width>
             <height>40</height>
            </size>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton3_1">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton3_2">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton3_3">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton3_4">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton3_5">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton3_6">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton3_7">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton3_8">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton3_9">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton3_10">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_capslock_right">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>100</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>20</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="icon">
            <iconset>
             <normaloff>:/newImg/newImg/KeyBorder/icon_small_normal.png</normaloff>
             <normalon>:/images/KeyBorder/icon_big.png</normalon>
             <activeoff>:/images/KeyBorder/icon_small_normal.png</activeoff>
             <activeon>:/newImg/newImg/KeyBorder/icon_big.png</activeon>:/newImg/newImg/KeyBorder/icon_small_normal.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>40</width>
             <height>40</height>
            </size>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_8">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>6</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>KeyBordToolButton</class>
   <extends>QToolButton</extends>
   <header>./common/keybordtoolbutton/keybordtoolbutton.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
