﻿#ifndef CSCANCODETHREAD_H
#define CSCANCODETHREAD_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-09-19
  * Description:
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QTimer>
#include <QThread>
#include <QSerialPort>

class CScanCodeThread : public QObject
{
    Q_OBJECT
public:
    static CScanCodeThread *GetInstance();
    ~CScanCodeThread();

    void StartScan();
    void StopScan();

signals:
    void SignalInitThread();
    void SignalExitThread();
    void SignalStartScan();
    void SignalStopScan();
    void SignalScanData(QByteArray);

private slots:
    void _SlotInitThread();
    void _SlotExitThread();
    void _SlotStartScan();
    void _SlotStopScan();
    void _SlotScanTimeout();
    void _SlotDelayTimeout();
    void _SlotReadSerial();

private:
    CScanCodeThread();

private:
    static CScanCodeThread *m_spInstance;

    QThread *m_pThread;
    bool m_bOpenOK;
    QSerialPort *m_pSerialPort;
    int m_iScantimes;
    QTimer *m_pScanTimer;

    bool m_bScanning;
    QByteArray m_qScanData;
    QTimer *m_pDelayTimer;
};

#endif // CSCANCODETHREAD_H
