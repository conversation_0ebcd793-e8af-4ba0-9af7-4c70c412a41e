﻿#ifndef CLISWIDGET_H
#define CLISWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-01-16
  * Description: LIS UI
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QCheckBox>
#include <QPushButton>

#include "CLabelLineEdit.h"
#include "CIPLabelLineEdit.h"
#include "CHLabelTitleWidget.h"

class CLisWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CLisWidget(QWidget *parent = nullptr);

protected:
    virtual void showEvent(QShowEvent *pEvent);

private slots:
    void _SlotLisTwoWayCheck(bool bChecked);
    void _SlotSaveBtn();

private:
    void _ReadCfg();

private:
    void _InitWidget();
    void _InitLayout();

private:
    CHLabelTitleWidget *m_pTitleWidget;
    QCheckBox *m_pTwoWayCheckBox;
    CIPLabelLineEdit *m_pIPWidget;
    CHLabelLineEdit *m_pPortWidget;
    QPushButton *m_pSaveBtn;

    int m_iPort;
    QString m_strIP;
};

#endif // CLISWIDGET_H
