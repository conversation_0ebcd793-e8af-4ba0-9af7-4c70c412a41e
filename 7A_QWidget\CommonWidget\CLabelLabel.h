#ifndef CLABELLABEL_H
#define CLABELLABEL_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-10-25
  * Description: QLabel-QLabel组合控件
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>

class CLabelLabel : public QWidget
{
    Q_OBJECT
public:
    CLabelLabel(const QString &strNameText, const QString &strValueText = QString(""), int iSpacing = 0, QWidget* parent = nullptr);
    ~CLabelLabel();

public:
    void SetNameLabelAlignment(Qt::Alignment qAlig);
    void SetValueLabelAlignment(Qt::Alignment qAlig);
    void SetValueLabelText(const QString &strText);
    void SetNameLabelMinSize(int iWidth, int iHeight);
    void SetNameLabelFixedSize(int iWidth, int iHeight);
    void SetValueLabelMinSize(int iWidth, int iHeight);
    void SetValueLabelFixedSize(int iWidth, int iHeight);
    void SetNameLabelObjectName(const QString &strObjName);
    void SetValueLabelObjectName(const QString &strObjName);

public:
    QString GetValueText() const;

private:
    void _InitWidget();

private:
    QLabel* m_pNameLabel;
    QLabel* m_pValueLabel;

    int m_iSpacing;
    QString m_strNameText;
    QString m_strValueText;
};

#endif // CLABELLABEL_H
