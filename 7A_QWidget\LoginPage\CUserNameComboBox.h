#ifndef CUSERNAMECOMBOBOX_H
#define CUSERNAMECOMBOBOX_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-14
  * Description: 用户输入下拉编辑框
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QComboBox>
#include <QLineEdit>

class CUserNameComboBox : public QComboBox
{
    Q_OBJECT
public:
    explicit CUserNameComboBox(QWidget *parent = nullptr);

private:
    QLabel *m_pIconLabel;
};

class CUserNameLineEdit : public QLineEdit
{
    Q_OBJECT
public:
    explicit CUserNameLineEdit(QWidget *parent = nullptr);

private:
    QLabel *m_pIconLabel;
};

#endif // CUSERNAMECOMBOBOX_H
