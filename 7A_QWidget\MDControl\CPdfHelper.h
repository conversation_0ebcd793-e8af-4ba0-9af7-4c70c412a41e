#ifndef CPDFHELPER_H
#define CPDFHELPER_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-11-07
  * Description: 生成pdf
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QFont>
#include <QObject>

class QPixmap;
class QPainter;
class QPrinter;

class CPdfHelper : public QObject
{
    Q_OBJECT
public:
    CPdfHelper();
    ~CPdfHelper();

    void SetPdfName(const QString &strFileName);
    void WriteTextToPdf(const QString &strText);
    void InsertPictureToPdf(const QPixmap &pixmap);
    void InsertPictureToPdf(const QString &strPictruePath);
    void EndPainter();

private:
    void _WritePixmap(QPixmap pixmap);

private:
    QPrinter *m_printer;
    QPainter *m_painter;
    int m_x;
    int m_y;
    int m_iWidthPX, m_iHeightPX;
    QFont m_font;
};

#endif // CPDFHELPER_H
