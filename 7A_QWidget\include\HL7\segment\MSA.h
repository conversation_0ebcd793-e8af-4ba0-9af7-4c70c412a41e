﻿#ifndef _MSA_H_
#define _MSA_H_
#include "../common/HL7Segment.h"
#include "../interface/IMSA.h"

class MSA :
	public HL7Segment, public IMSA
{
public:
	MSA();
	virtual ~MSA();

	DECLARE_OBJECTBASE

	BEGIN_IMPL_QUERYIF(MSA)
		IMPL_QUERYIF_BYPATH(IF_OBJECTBASE, IHL7Segment, IObjectBase)
		IMPL_QUERYIF(IF_HL7SEGMENT, IHL7Segment)
		IMPL_QUERYIF(IF_MSA, IMSA)
		END_IMPL_QUERYIF()

	/*
	 *	\brief 确认代码,index 0
	 */
	void SetACKCode(const char* ackCodeStr);

	void GetACKCode(char** ackCodeStr);
	/*
	 *	\brief 信息控制ID,index 1
	 */
	void SetMsgCtrlID(const char* msgCtrlIDStr);
	
	void GetMsgCtrlID(char** msgCtrlIDStr);

	/*
	 *	\brief 文本信息, index 2
	 */
	void SetTextMessage(const char* textMsgCtrl);

	void GetTextMessage(char** textMsgCtrl);

	/*
	 *	\brief 期望系列号，index 3，目前保留置空
	 */
	void SetExpectSequenceNum(const char* expectSequenceNum);
	
	void GetExpectSequenceNum(char** expectSequenceNum);
	/*
	 *	\brief 延迟确认类型，index 4, 目前保留置空
	 */
	void SetDelayACKType(const char* delayAckTypeStr);

	void GetDelayACKType(char** delayAckTypeStr);

	/*
	 *	\brief 错误情况,index 5
	 */
	void SetErrorCondition(const char* errorCondition);
	
	void GetErrorCondition(char** errorCondition);

	virtual void Parse(const char* segmentStr, EncodingCharacters encodingCharacters);

	MSA& operator=(MSA& msa);

private:
	HL7Field m_ackCode;//Acknowledgement Code 确认代码
	HL7Field m_msgCtrlID;//Message control ID 信息控制ID
	HL7Field m_textMessage;//文本信息
	HL7Field m_expectSequenceNum;//Expected Sequence Number 期望系列号
	HL7Field m_delayACKType;//Delayed Acknowledgment Type 延迟确认类型
	HL7Field m_errorCondition;//错误情况,默认为0，即成功
};

REGISTER_CLASS(MSA);
#endif
