#ifndef CPROGRESSBAR_H
#define CPROGRESSBAR_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-10-26
  * Description: 进度条
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QGroupBox>
#include <QPushButton>
#include <QProgressBar>
#include "CHLabelTitleWidget.h"

class CProgressBar : public QWidget
{
    Q_OBJECT
public:
    CProgressBar(const QString &strTitleText, const QString &strInfoText, QWidget *parent = nullptr);
    ~CProgressBar();

    void SetTitle(const QString &strTitleText);
    void SetInfo(const QString &strInfoText);
    void SetRange(int iMinValue, int iMaxValue);
    void SetValue(int iValue);
    void AddValue();
    void SetMaxValue();
    void SetLabelVisible(bool bVisable);
    void SetBarObjectName(const QString &strObjName);

protected:
    void paintEvent(QPaintEvent* pEvent) override;

private:
    void _ReSetData();

private:
    QGroupBox *_CreateGroup();

private:
    int m_iValue;
    int m_iMinValue;
    int m_iMaxValue;
    int m_iHasRunPercent;

    QString m_strTitleText;
    QString m_strInfoText;

    CHLabelTitleWidget *m_pTitleWidget;
    QLabel* m_pInfoLabel;
    QLabel* m_pPercentLabel;
    QProgressBar* m_pProgressBar;
};

#endif // CPROGRESSBAR_H
