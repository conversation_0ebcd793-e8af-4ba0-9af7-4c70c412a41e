#ifndef CLABELLINEEDIT_H
#define CLABELLINEEDIT_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-10-25
  * Description: QLabel-QLineEdit组合控件
  * -------------------------------------------------------------------------
  * History: 2024-06-28 hxr 添加垂直布局和水平布局
  * 由于早期工厂模式已使用此类，部分接口保留
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QBoxLayout>

#include "CLineEdit.h"

class CLabelLineEdit : public QWidget
{
    Q_OBJECT
public:
    CLabelLineEdit(const QString &strLabelText, const QString &strEditText = "", int iSpacing = 5,
                   QBoxLayout::Direction eDirection = QBoxLayout::LeftToRight, QWidget *parent = nullptr);
    virtual ~CLabelLineEdit();

public:
    QString GetLineEditText() const;

    void ResetLabelSize(int iWidth, int iHeight);
    void ResetLineEditSize(int iWidth, int iHeight);

    void SetLineEidtAlignment(Qt::Alignment qAlig);
    void SetLineEditText(const QString &strEditText);
    void SetLabelFixedSize(int iWidth, int iHeight);
    void SetLineEditFixedSize(int iWidth, int iHeight);
    void SetLineEditInputMethod(Qt::InputMethodHint eInputMethod);

    void SetLineEditFocus();

signals:
    void SignalTextChanged(const QString &strText);
    void SignalTextEdited(const QString &strText);
    void SignalEditingFinished();

private:
    QLabel* m_pLabel;
    CLineEdit* m_pLineEdit;
};

class CVLabelLineEdit : public CLabelLineEdit
{
    Q_OBJECT
public:
    CVLabelLineEdit(const QString &strLabelText, const QString &strEditText = "",
                    int iSpacing = 5, QWidget *parent = nullptr);
    virtual ~CVLabelLineEdit();
};

class CHLabelLineEdit : public CLabelLineEdit
{
    Q_OBJECT
public:
    CHLabelLineEdit(const QString &strLabelText, const QString &strEditText = "",
                    int iSpacing = 16, QWidget *parent = nullptr);
    virtual ~CHLabelLineEdit();
};

#endif // CLABELLINEEDIT_H
