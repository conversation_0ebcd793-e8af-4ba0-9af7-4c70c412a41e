#ifndef CLISTCPCLIENT_H
#define CLISTCPCLIENT_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-02-16
  * Description: LIS UI
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QTimer>
#include <QThread>
#include <QTcpSocket>

class CLisTcpClient : public QObject
{
    Q_OBJECT
public:
    static CLisTcpClient &GetInstace();
    ~CLisTcpClient();

    bool GetConnect();
    void SetConnect(bool bConnect);
    void SetIPAndPort(QString strIP, int iPort);
    void ConnectServer();
    void WriteData(QByteArray qByteData);

signals:
    void SignalInitThread();
    void SignalLisState(bool bConnect);
    void SignalConnectServer();
    void SignalWriteData(QByteArray qByteData);
    void SignalReadData(QByteArray qByteData);

private slots:
    void _SlotInitThread();
    void _SlotExitThread();
    void _SlotWriteData(QByteArray qByteData);
    void _SlotReadMesg();
    void _SlotConnected();
    void _SlotDisconnect();
    void _SlotError(QAbstractSocket::SocketError);
    void _SlotCheckTimer();
    void _SlotConnectServer();

private:
    CLisTcpClient();

private:
    bool m_bConnectOK;
    QThread m_qThread;
    QTimer *m_pCheckTimer;
    QTcpSocket *m_pSocket;

    int m_iPort;
    QString m_strIP;
};

#endif // CLISTCPCLIENT_H
