#include "CRunTest.h"
#include <QApplication>
#include "CMessageBox.h"
#include "PublicConfig.h"
#include "CTimingTecDB.h"
#include "CProjectDB.h"
#include "CConfigJson.h"
#include "CPrintThread.h"
#include "CCalculateThread.h"
#include "CReadWriteXlsxThread.h"
#include "MDControl/CPdfHelper.h"
#include "MDControl/CPdfTcpServer.h"
#include "CFtpDB.h"
#include "CLotInfoDB.h"
#include "CCreateImage.h"

CRunTest *CRunTest::m_spInstance = nullptr;

CRunTest *CRunTest::GetInstance()
{
    if(nullptr == m_spInstance)
        m_spInstance = new CRunTest;
    return m_spInstance;
}

CRunTest::CRunTest()
{
    for(int i=0; i<gk_iMachineCount; i++)
    {
        m_sRunInfoList.push_back(SRunningInfoStruct());
        m_qRootJsonObjList.push_back(QJsonObject());
        m_strPngPathList.push_back(QStringList());

        QTimer *pTimer = new QTimer;
        pTimer->setProperty("iMachineID", i);
        connect(pTimer, &QTimer::timeout, this, &CRunTest::_SlotDelayAgingTimerout);
        m_pDelayAgingTimerList.push_back(pTimer);
    }

    connect(CCalculateThread::GetInstance(),&CCalculateThread::SingalCalcResultReady,this,&CRunTest::_SlotCalculateUpdateResult);
    Register2Map(Method_pcr_tec_table_end);
    Register2Map(Method_timing_file);
    Register2Map(Method_start);
    Register2Map(Method_stop);
}

CRunTest::~CRunTest()
{
    UnRegister2Map(Method_pcr_tec_table_end);
    UnRegister2Map(Method_timing_file);
    UnRegister2Map(Method_start);
    UnRegister2Map(Method_stop);
}

SRunningInfoStruct &CRunTest::GetRunInfoStruct(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return m_sRunInfoList[0];

    return m_sRunInfoList[iMachineID];
}

void CRunTest::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    switch (iMethodID)
    {
    case Method_pcr_tec_table_end:
        _ParseSendTecEndCmd(iMachineID, iResult);
        break;
    case Method_timing_file:
        _ParseTimingFileCmd(iMachineID, iResult, qVarData);
        break;
    case Method_start:
        _ParseStartCmd(iMachineID, iResult);
        break;
    case Method_stop:
        _ParseStopCmd(iMachineID, iResult);
        break;
    default:
        break;
    }
}

void CRunTest::_ParseSendTecEndCmd(int iMachineID, int iResult)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if(0 == iResult && !m_sRunInfoList.at(iMachineID).strTimingData.isEmpty())
    {
        RUN_LOG(QString("%1#TEC发送完成,开始发送时序").arg(iMachineID + 1));
        _SendTimingData(iMachineID);
    }
}

/*
 * 时序文件下发失败不记录,否则浪费卡盒
*/
void CRunTest::_ParseTimingFileCmd(int iMachineID, int iResult, const QVariant &qVarData)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    SRunningInfoStruct &sRunInfo = m_sRunInfoList[iMachineID];
    if(0 != iResult)
    {
        //sRunInfo.sResultInfo.iStatus = eTestFail;
        //CProjectDB::GetInstance()->UpdateHistroyTestStatus(sRunInfo.sResultInfo.iHistoryID, sRunInfo.sResultInfo.iStatus);
        sRunInfo.strTimingData.clear();
        emit SignalUpdateItemStatus(iMachineID, eDeviceIdle); //更新设备状态
        RUN_LOG(QString("%1#时序下发失败").arg(iMachineID + 1));
        ShowError((QWidget*)gk_pMainWindow, m_strTipsText, tr("%1#时序下发失败").arg(iMachineID + 1));
        return;
    }

    QMap<int, QString> &map = sRunInfo.strTimingMap;
    if(map.isEmpty())
    {
#if 0
        sRunInfo.sResultInfo.strSampleID = sRunInfo.sSampleInfo.strSampleID;
        sRunInfo.sResultInfo.strCardID = sRunInfo.sCardInfo.strCardID;
        sRunInfo.sResultInfo.strProjectName = sRunInfo.sCardInfo.strProject;
        sRunInfo.sResultInfo.strTestTime = sRunInfo.sCardInfo.strTestTime;
        sRunInfo.sResultInfo.strOperator = sRunInfo.sSampleInfo.strOperator;
        sRunInfo.sResultInfo.strMode = sRunInfo.sSampleInfo.bQCTest ? "Q" : "T";
        sRunInfo.sResultInfo.iStatus = eTestRunning;
        sRunInfo.sResultInfo.iMachineID = iMachineID;
        sRunInfo.sResultInfo.iTestProject = sRunInfo.iTecIndex;
        int iHistoryID = CProjectDB::GetInstance()->AddHistoryData(sRunInfo.sResultInfo);
        sRunInfo.sResultInfo.iHistoryID = iHistoryID;

        CProjectDB::GetInstance()->AddSampleData(sRunInfo.sSampleInfo);
        CProjectDB::GetInstance()->AddCardData(sRunInfo.sCardInfo);

        emit SignalUpdateItemStatus(iMachineID, eDeviceTesting); //更新设备状态
#endif

        sRunInfo.bRunning = true;
        QString strCmd = GetJsonCmdString(Method_start);
        SendJsonCmd(iMachineID, Method_start, strCmd);
        //RUN_LOG(QString("%1#发送运行时序完成,开始测试,iHistoryID:%2").arg(iMachineID + 1).arg(iHistoryID));
        RUN_LOG(QString("%1#发送运行时序完成,开始测试").arg(iMachineID + 1));
        return;
    }
    QVariantList qVarList = qVarData.toList();
    if(qVarList.isEmpty())
        return;

    int index = qVarList.at(0).toInt() - 1;
    QString strLast = map.value(index);
    map.remove(index);

    QVariant qVarString = QString("%1:%2").arg(index).arg(strLast);
    QString strCmd = GetJsonCmdString(Method_timing_file, qVarString);
    SendJsonCmd(iMachineID, Method_timing_file, strCmd);
}

void CRunTest::_ParseStartCmd(int iMachineID, int iResult)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    SRunningInfoStruct &sRunInfo = m_sRunInfoList[iMachineID];
    if(0 != iResult)
    {
        sRunInfo.strTimingData.clear();
        sRunInfo.sResultInfo.iStatus = eTestFail;
        sRunInfo.bRunning = false;
        CProjectDB::GetInstance()->UpdateHistroyTestStatus(sRunInfo.sResultInfo.iHistoryID, sRunInfo.sResultInfo.iStatus);
        emit SignalUpdateItemStatus(iMachineID, eDeviceTestFail); //更新设备状态
        RUN_LOG(QString("%1#测试失败").arg(iMachineID + 1));
        ShowError((QWidget*)gk_pMainWindow, m_strTipsText, tr("%1#测试失败").arg(iMachineID + 1));
        return;
    }

    // 需要等CT线程计算完
}

void CRunTest::_CalcEnd(int iMachineID,int iStatus)
{
    SRunningInfoStruct &sRunInfo = m_sRunInfoList[iMachineID];

    sRunInfo.bRunning = false;
    sRunInfo.sResultInfo.strTestEndTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    sRunInfo.sResultInfo.iStatus = iStatus;
    CProjectDB::GetInstance()->UpdateHistroyTestStatus(sRunInfo.sResultInfo.iHistoryID, sRunInfo.sResultInfo.iStatus);
    if(eTestDone == iStatus) //工厂荧光那边传给CT计算再传到这里
        emit SignalUpdateItemStatus(iMachineID, eDeviceTestDone); //更新设备状态
    else
        emit SignalUpdateItemStatus(iMachineID, eDeviceTestFail); //更新设备状态
    RUN_LOG(QString("%1#测试完成").arg(iMachineID + 1));

    bool bReset = CPublicConfig::GetInstance()->GetTestDoneAutoReset();
    qDebug()<<"测试结束是否自动复位:"<<bReset;
    if(bReset)
    {
        QString strResetCmd = GetJsonCmdString(Method_MCHK);
        SendJsonCmd(iMachineID, Method_MCHK, strResetCmd);
    }

    _WriteCalcInfo2Xlsx(iMachineID);
    _WriteOther2Xlsx(iMachineID);
    CCreateImage::GetInstance()->SaveHistoryDetailImage(sRunInfo,false);
    WritePdf(iMachineID,CPublicConfig::GetInstance()->GetTestPdfName(iMachineID),m_sRunInfoList.at(iMachineID),m_strPngPathList.at(iMachineID));

    bool bAutoPrint = CConfigJson::GetInstance()->GetConfigValue("Print", "bAutoPrint").toBool();
    qDebug()<<"是否自动打印:"<<bAutoPrint<<sRunInfo.sResultInfo.iHistoryID<<"语言:"<<gk_iLanguage;
    if(bAutoPrint)
    {
        if(eLanguage_Chinese != gk_iLanguage)
            CPrintThread::GetInstance()->AddPrintHistoryID(sRunInfo.sResultInfo.iHistoryID);
    }

    if(!sRunInfo.bFactroyTest) //不是工厂测试,是主界面创建的测试
    {
        sRunInfo.strTimingData.clear();
        return;
    }

    //工厂模式创建的测试
    int &iLeftTimes = sRunInfo.iRunTimes;
    iLeftTimes--;
    if(iLeftTimes <= 0)
        iLeftTimes = 0;
    emit SignalAgingLeftTimes(iMachineID, iLeftTimes);
    if(iLeftTimes <= 0)
    {
        sRunInfo.strTimingData.clear();
        ShowSuccess((QWidget*)gk_pMainWindow, m_strTipsText, tr("%1#时序测试完成").arg(iMachineID + 1));
        return;
    }

    if(eTestDone != iStatus)
    {
        qDebug()<<QString("%1#测试失败，不再老化").arg(iMachineID + 1);
        return;
    }

    m_pDelayAgingTimerList.at(iMachineID)->start(40000); //老化间隔40秒
}

void CRunTest::_SlotDelayAgingTimerout()
{
    QTimer *pTimer = dynamic_cast<QTimer*>(sender());
    if(nullptr == pTimer)
        return;

    pTimer->stop();
    int iMachineID = pTimer->property("iMachineID").toInt();
    _ContinueAgingTest(iMachineID);
}

void CRunTest::_ContinueAgingTest(int iMachineID)
{
    emit SignalUpdateItemStatus(iMachineID, eDeviceIdle); //先空闲

    SRunningInfoStruct &sRunInfo = m_sRunInfoList[iMachineID];
    sRunInfo.sResultInfo.Clear();

    QDateTime dateTime = QDateTime::currentDateTime();
    QString strCurrentTime = dateTime.toString("yyyyMMddhhmmsszzz");
    QString strCardID = "C" + strCurrentTime;
    QString strSampleID = "S" + strCurrentTime;

    sRunInfo.sCardInfo.strCardID = strCardID;
    sRunInfo.sSampleInfo.strSampleID = strSampleID;

    qDebug()<<QString("%1#继续老化").arg(iMachineID + 1)<<sRunInfo.iRunTimes;
    StartTest(iMachineID);
}

void CRunTest::_ParseStopCmd(int iMachineID, int iResult)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    SRunningInfoStruct &sRunInfo = m_sRunInfoList[iMachineID];
    sRunInfo.strTimingData.clear();
    sRunInfo.bRunning = false;

    if(0 == iResult)
    {
        emit SignalUpdateItemStatus(iMachineID, eDeviceTestStopped); //更新设备状态
        qDebug()<<QString("%1#停止成功").arg(iMachineID + 1);
        ShowSuccess((QWidget*)gk_pMainWindow, m_strTipsText, tr("%1#停止成功").arg(iMachineID + 1));
    }
    else
    {
        emit SignalUpdateItemStatus(iMachineID, eDeviceFault); //更新设备状态
        qDebug()<<QString("%1#停止失败").arg(iMachineID + 1);
        ShowError((QWidget*)gk_pMainWindow, m_strTipsText, tr("%1#停止失败").arg(iMachineID + 1));
    }

    sRunInfo.sResultInfo.iStatus = eTestStop;
    CProjectDB::GetInstance()->UpdateHistroyTestStatus(sRunInfo.sResultInfo.iHistoryID, sRunInfo.sResultInfo.iStatus);
}

void CRunTest::StartTest(int iMachineID)
{
    //clear
    m_qRootJsonObjList[iMachineID] = QJsonObject();

    QString strTestTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");

    SRunningInfoStruct &sRunInfo = m_sRunInfoList[iMachineID];
    sRunInfo.sResultInfo.Clear();

    sRunInfo.bRunning = true;
    sRunInfo.sCardInfo.strTestTime = strTestTime;
    sRunInfo.sSampleInfo.strTestTime = strTestTime;

    // 20241021 开始测试就写入数据库
#if 1
    sRunInfo.sResultInfo.strSampleID = sRunInfo.sSampleInfo.strSampleID;
    sRunInfo.sResultInfo.strCardID = sRunInfo.sCardInfo.strCardID;
    sRunInfo.sResultInfo.strProjectName = sRunInfo.sCardInfo.strProject;
    sRunInfo.sResultInfo.strTestTime = sRunInfo.sCardInfo.strTestTime;
    sRunInfo.sResultInfo.strOperator = sRunInfo.sSampleInfo.strOperator;
    sRunInfo.sResultInfo.strMode = sRunInfo.sSampleInfo.strQCTestModel;
    sRunInfo.sResultInfo.iStatus = eTestRunning;
    sRunInfo.sResultInfo.iMachineID = iMachineID;
    sRunInfo.sResultInfo.iTestProject = sRunInfo.iTecIndex;
    // 读取SN号和下位机SN号


    QString strFullVersion = CPublicConfig::GetInstance()->GetFullVersion();
    QString strMachineSN = CPublicConfig::GetInstance()->GetMachineSN();

    SPLCVerStruct sPLCVerStruct = CPublicConfig::GetInstance()->GetPLCVersionStruct(iMachineID);
    sRunInfo.sResultInfo.strTmValue = strMachineSN + "&" + sPLCVerStruct.strSN + "&" + strFullVersion;

    int iHistoryID = CProjectDB::GetInstance()->AddHistoryData(sRunInfo.sResultInfo);
    sRunInfo.sResultInfo.iHistoryID = iHistoryID;

    CProjectDB::GetInstance()->AddSampleData(sRunInfo.sSampleInfo);
    CProjectDB::GetInstance()->AddCardData(sRunInfo.sCardInfo);

#endif

    sRunInfo.strTecData = CTimingTecDB::GetInstance().GetTecContent(sRunInfo.strTecName);
    sRunInfo.strTimingData = CTimingTecDB::GetInstance().GetTimingContent(sRunInfo.strTimingName);
    QString strTimingData = sRunInfo.strTimingData;
    sRunInfo.strTimingData = _GetRealSendTimingData(strTimingData);

    QString strProject = sRunInfo.sResultInfo.strProjectName;
    strProject.replace("/", "+");

    QString strBaseName = QString("%1_%2#_%3_%4_%5")
            .arg(strTestTime.remove(" ").remove("-").remove(":"))
            .arg(iMachineID + 1)
            .arg(DeleteSpecialCharacters(sRunInfo.sSampleInfo.strSampleID))
            .arg(DeleteSpecialCharacters(sRunInfo.sCardInfo.strCardID))
            .arg(DeleteSpecialCharacters(strProject));
    strBaseName.remove("\\").remove("/").remove(":").remove("*").remove("?");
    strBaseName.remove("\"").remove("<").remove(">").remove("|").remove(" ");

    QString strXlsxName = strBaseName + ".xlsx";
    QString strPdfName = strBaseName + ".pdf";
    CPublicConfig::GetInstance()->SetTestXlsxName(iMachineID, strXlsxName);
    CPublicConfig::GetInstance()->SetTestPdfName(iMachineID, strPdfName);

    qDebug() << QString("%1#测试信息:").arg(iMachineID + 1)
             << "HistoryID:" << iHistoryID
             << "操作者:" << sRunInfo.sSampleInfo.strOperator
             << "样本编号:" << sRunInfo.sSampleInfo.strSampleID
             << "卡盒编号:" << sRunInfo.sCardInfo.strCardID
             << "项目:" << sRunInfo.sCardInfo.strProject
             << "测试时间:" << sRunInfo.sSampleInfo.strTestTime
             << "TEC:" << sRunInfo.strTecName << sRunInfo.strTecData
             << "时序:" << sRunInfo.strTimingName << sRunInfo.strTimingData
             << "测试类型:" << sRunInfo.sSampleInfo.strQCTestModel
             << "xlsx:" << strXlsxName
             << "pdf:" << strPdfName;

    emit CPublicConfig::GetInstance()->SignalTimingTestStart(iMachineID);
    emit SignalUpdateItemStatus(iMachineID, eDeviceTesting); //更新设备状态

    if(!m_sRunInfoList.at(iMachineID).strTecName.isEmpty())
    {
        emit CPublicConfig::GetInstance()->SignalTecTestStart(iMachineID, sRunInfo.strTecName);
        RUN_LOG(QString("%1#开始发送TEC时序").arg(iMachineID + 1));
        return;
    }

    RUN_LOG(QString("%1#开始发送运行时序,无TEC时序发送").arg(iMachineID + 1));
    _SendTimingData(iMachineID);
}

void CRunTest::_SendTimingData(int iMachineID)
{
    const int iOnePieceLen = 1000;
    QString strTimingData = m_sRunInfoList.at(iMachineID).strTimingData;
    int iPackets = strTimingData.size() / iOnePieceLen;
    int iLeft = strTimingData.size() % iOnePieceLen;

    QStringList strList;
    for(int i=0; i<iPackets; i++)
        strList.push_back(strTimingData.mid(i * iOnePieceLen, iOnePieceLen));
    if(0 != iLeft)
        strList.push_back(strTimingData.right(iLeft));

    QMap<int, QString> map;
    for(int i=0; i<strList.size(); i++)
        map.insert(i, strList.at(i));

    if(!map.isEmpty())
    {
        QString strLast = map.last();
        int index = map.lastKey();
        map.remove(index);
        m_sRunInfoList[iMachineID].strTimingMap = map;

        QVariant qVarData = QString("%1:%2").arg(index).arg(strLast);
        QString strCmd = GetJsonCmdString(Method_timing_file, qVarData);
        SendJsonCmd(iMachineID, Method_timing_file, strCmd);
    }
}

void CRunTest::_SlotCalculateUpdateResult(int iMachineID,int intiHistoryID,int iStatus)
{
    if(iMachineID >= gk_iMachineCount || iMachineID < 0)
    {
        return;
    }
    SRunningInfoStruct &RunInfo = m_sRunInfoList[iMachineID];
    if(!CProjectDB::GetInstance()->GetHistoryData(intiHistoryID,RunInfo.sResultInfo))
        return;

    emit SignalUpdateItemCalcResult(iMachineID);

    _CalcEnd(iMachineID,iStatus);
}

QString CRunTest::_GetRealSendTimingData(const QString &strTimingData)
{
    QString strNewTiming = "";
    QStringList strTimingList = strTimingData.split(SPLIT_BETWEEN_CMD);
    QStringList strNewList;
    int iMethodID = 0;
    for(int i=0; i<strTimingList.count(); ++i)
    {
        QStringList oneCmdList = strTimingList[i].split(SPLIT_IN_CMD);
        iMethodID = oneCmdList.at(0).toInt();
        if(Method_HTST == iMethodID)
        {
            if(oneCmdList.size() >= 3) // value * 100
                oneCmdList[2] = QString::number((int)(oneCmdList[2].toFloat() * 100));
        }
        else if(Method_Motor_CMD == iMethodID)
        {
            oneCmdList.pop_front();
        }

        strNewList<<oneCmdList.join(SPLIT_IN_CMD);
    }
    strNewTiming = strNewList.join(SPLIT_BETWEEN_CMD);
    return strNewTiming;
}

void CRunTest::_WriteCalcInfo2Xlsx(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = CPublicConfig::GetInstance()->GetTestXlsxName(iMachineID);
    pXlsxStruct->strTableName = "ct info";
    pXlsxStruct->strTitleList << "" << "FAM_1" << "JOE_1" << "ROX_1" << "CY5_1"
                              << "FAM_2" << "JOE_2" << "ROX_2" << "CY5_2";

    const SRunningInfoStruct &sRunInfo = m_sRunInfoList[iMachineID];

    QStringList strInfoList = sRunInfo.sResultInfo.strCTInfo.split(";");
    int iMin = qMin(8, strInfoList.size());
    QVariantList qUpliftVarList = {"抬升值"};
    QVariantList qThresholdVarList = {"阈值"};
    QVariantList qCTVarList = {"CT"};
    for(int i=0; i<iMin; i++)
    {
        QStringList strHoleList = strInfoList.at(i).split(",");
        if(strHoleList.size() < 3)
        {
            qUpliftVarList << "";
            qThresholdVarList << "";
            qCTVarList << "";
        }
        else
        {
            qThresholdVarList << strHoleList.at(0);
            qUpliftVarList << strHoleList.at(1);
            float fCtValue = strHoleList.at(2).toFloat();
            QString strCtValue;
            if(strHoleList.at(2).toFloat() <= 1 )
            {
                strCtValue = "0";
            }
            else
            {
                if(bHrmTDTecType(sRunInfo.sResultInfo.iTestProject))
                {
                    strCtValue = QString("%1").arg(fCtValue+10);
                }
                else
                {
                    strCtValue = strHoleList.at(2);
                }
            }
            qCTVarList << strCtValue;
        }
    }

    QVariantList qResultVarList = {"结果"};
    QStringList strResultList = sRunInfo.sResultInfo.strResult.split(";");
    iMin = qMin(8, strResultList.size());
    for(int i=0; i<iMin; i++)
    {
        qResultVarList << GetResultFormFlag(strResultList.at(i));
    }
    pXlsxStruct->varWriteDataList << qUpliftVarList << qThresholdVarList << qCTVarList;

    if(bHrmTecType(sRunInfo.iTecIndex))
    {
        QStringList strMeltInfoList = sRunInfo.sResultInfo.strMeltingInfo.split(";");
        int iMin = qMin(8, strMeltInfoList.size());
        QVariantList qTM1List = {"TM1"};
        QVariantList qRM1List = {"RM1"};
        QVariantList qYM1List = {"YM1"};

        QVariantList qTM2List = {"TM2"};
        QVariantList qRM2List = {"RM2"};
        QVariantList qYM2List = {"YM2"};


        QVariantList qTM3List = {"TM3"};
        QVariantList qRM3List = {"RM3"};
        QVariantList qYM3List = {"YM3"};

        QVariantList qTM4List = {"TM4"};
        QVariantList qRM4List = {"RM4"};
        QVariantList qYM4List = {"YM4"};


        for(int i=0; i<iMin; i++)
        {
            QStringList strHoleList = strMeltInfoList.at(i).split(",");
            QString strPeak;
            QString strAmp;
            QStringList strM1List;
            QStringList strM2List;
            QStringList strM3List;
            QStringList strM4List;
            if(strHoleList.size() >= 4)
            {
                strPeak = strHoleList.at(2);
                strAmp = strHoleList.at(3);
                strM1List = strHoleList.at(0).split("&");
                strM2List = strHoleList.at(1).split("&");
            }
            if(strHoleList.size() >= 6)
            {
                strM3List = strHoleList.at(4).split("&");
                strM4List = strHoleList.at(5).split("&");
            }
            QString strTm1,strRm1,strYm1;
            QString strTm2,strRm2,strYm2;
            QString strTm3,strRm3,strYm3;
            QString strTm4,strRm4,strYm4;
            if(strM1List.size() >= 3)
            {
                float fTm1 = strM1List.at(0).toFloat();
                strTm1 = QString::number(fTm1, 'f', 2);

                float fRm1 = strM1List.at(1).toFloat();
                strRm1 = QString::number(fRm1, 'f', 2);

                float fYm1 = strM1List.at(2).toFloat();
                strYm1 = QString::number(fYm1, 'f', 2);
            }
            if(strM2List.size() >= 3)
            {
                float fTm2 = strM2List.at(0).toFloat();
                strTm2 = QString::number(fTm2, 'f', 2);

                float fRm2 = strM2List.at(1).toFloat();
                strRm2 = QString::number(fRm2, 'f', 2);

                float fYm2 = strM2List.at(2).toFloat();
                strYm2 = QString::number(fYm2, 'f', 2);
            }
            if(strM3List.size() >= 3)
            {
                float fTm3 = strM3List.at(0).toFloat();
                strTm3 = QString::number(fTm3,'f',2);

                float fRm3 = strM3List.at(1).toFloat();
                strRm3 = QString::number(fRm3,'f',2);

                float fYm3 = strM3List.at(2).toFloat();
                strYm3 = QString::number(fYm3,'f',2);
            }
            if(strM4List.size() >= 3)
            {
                float fTm4 = strM4List.at(0).toFloat();
                strTm4 = QString::number(fTm4,'f',2);

                float fRm4 = strM4List.at(1).toFloat();
                strRm4 = QString::number(fRm4,'f',2);

                float fYm4 = strM4List.at(2).toFloat();
                strYm4 = QString::number(fYm4,'f',2);
            }
            qTM1List << strTm1;
            qRM1List << strRm1;
            qYM1List << strYm1;

            qTM2List << strTm2;
            qRM2List << strRm2;
            qYM2List << strYm2;

            qTM3List << strTm3;
            qRM3List << strRm3;
            qYM3List << strYm3;

            qTM4List << strTm4;
            qRM4List << strRm4;
            qYM4List << strYm4;

        }

        qResultVarList.clear();
        qResultVarList << "结果";
        qResultVarList << sRunInfo.sResultInfo.strHrmResult;
        /*
        QStringList strMeltResultList = sRunInfo.sResultInfo.strHrmResult.split(";");
        iMin = qMin(8, strMeltResultList.size());
        for(int i=0; i<iMin; i++)
        {
            if("P" == strMeltResultList.at(i))
                qResultVarList << "耐药型";
            else if("N" == strMeltResultList.at(i))
                qResultVarList << "敏感型";
            else if("E" == strMeltResultList.at(i))
                qResultVarList << "无效";
            else
                qResultVarList << "";
        }
        */
        pXlsxStruct->varWriteDataList << qTM1List << qRM1List << qYM1List
                                      << qTM2List << qRM2List << qYM2List
                                      << qTM3List << qRM3List << qYM3List
                                      << qTM4List << qRM4List << qYM4List;
    }
    pXlsxStruct->varWriteDataList << qResultVarList;

    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
    qDebug()<<QString("%1# ct info write to xlsx end").arg(iMachineID + 1);
}

void CRunTest::_WriteOther2Xlsx(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    const SRunningInfoStruct &sRunInfo = m_sRunInfoList.at(iMachineID);

    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = CPublicConfig::GetInstance()->GetTestXlsxName(iMachineID);
    pXlsxStruct->strTableName = "other";
    pXlsxStruct->strTitleList << "-" << "-";

    MdyWidthStruct col0;
    col0.dWidth = 30;
    col0.iColumn = 1;

    MdyWidthStruct col1;
    col1.dWidth = 50;
    col1.iColumn = 2;

    pXlsxStruct->mdyWidthList << col0 << col1;

    QList<QVariantList> &varOtherDataList = pXlsxStruct->varWriteDataList;

    QVariantList qUserList = {"操作者:", sRunInfo.sResultInfo.strOperator};
    varOtherDataList << qUserList;

    QVariantList qSampleIDList = {"样本编号:", sRunInfo.sSampleInfo.strSampleID};
    varOtherDataList << qSampleIDList;

    QVariantList qCardIDList = {"试剂卡编号:", sRunInfo.sCardInfo.strCardID};
    varOtherDataList << qCardIDList;

    QVariantList qStartTimeList = {"开始时间:", sRunInfo.sResultInfo.strTestTime};
    varOtherDataList << qStartTimeList;

    QVariantList qEndTimeList = {"结束时间:", sRunInfo.sResultInfo.strTestEndTime};
    varOtherDataList << qEndTimeList;

    QDateTime qBeginTime = QDateTime::fromString(sRunInfo.sResultInfo.strTestTime, "yyyy-MM-dd hh:mm:ss");
    QDateTime qEndTime = QDateTime::fromString(sRunInfo.sResultInfo.strTestEndTime, "yyyy-MM-dd hh:mm:ss");
    int iSecond = qBeginTime.secsTo(qEndTime);
    int iMin = iSecond / 60;
    if(0 != iSecond % 60)
        iMin++;
    QVariantList qUseTimeList = {"测试用时:", QString("%1分钟").arg(iMin)};
    varOtherDataList << qUseTimeList;

    QVariantList qIPCSNList = {"上位机SN:", CPublicConfig::GetInstance()->GetMachineSN()};
    varOtherDataList << qIPCSNList;

    SPLCVerStruct sPLCVerStruct = CPublicConfig::GetInstance()->GetPLCVersionStruct(iMachineID);
    QVariantList qPLCSNList = {"下位机SN:", sPLCVerStruct.strSN};
    varOtherDataList << qPLCSNList;

    QVariantList qProjetcList = {"测试项目:", sRunInfo.sCardInfo.strProject};
    varOtherDataList << qProjetcList;

    QVariantList qTimingNameList = {"时序名称:", sRunInfo.strTimingName};
    varOtherDataList << qTimingNameList;

    QVariantList qTimingDataList = {"时序内容:", sRunInfo.strTimingData};
    varOtherDataList << qTimingDataList;

    QVariantList qPCRNameList = {"PCR名称:", sRunInfo.strTecName};
    varOtherDataList << qPCRNameList;

    QVariantList qPCRDataList = {"PCR内容", sRunInfo.strTecData};
    varOtherDataList << qPCRDataList;

    QString strAllVer;
    ReadFile(QApplication::applicationDirPath() + "/Resources/all.txt", strAllVer);
    QVariantList qAllVerList = {"总版本", strAllVer.remove("\r").remove("\n").remove("\t")};
    varOtherDataList << qAllVerList;

    QVariantList qIPCVerList = {"上位机版本:", GetAppVersion()};
    varOtherDataList << qIPCVerList;

    CCalCTLib calcLib;
    QVariantList qCalcVerList = {"算法版本:", calcLib.getVersion()};
    varOtherDataList << qCalcVerList;

    QString strAutoVersion;
    QString strPath = QApplication::applicationDirPath() +  "/Resources/version";
    ReadFile(strPath, strAutoVersion);
    strAutoVersion.remove("\r").remove("\n");
    QVariantList qAutoVerList = {"配置版本:", strAutoVersion};
    varOtherDataList << qAutoVerList;

    QVariantList qMedianVerList = {"中位机版本:", sPLCVerStruct.strMedianShowVersion};
    varOtherDataList << qMedianVerList;

    QVariantList qPCRVerList = {"PCR版本:", sPLCVerStruct.strPCRShowVersion};
    varOtherDataList << qPCRVerList;

    QVariantList qFLVerList = {"FL版本:", sPLCVerStruct.strFLShowVersion};
    varOtherDataList << qFLVerList;

    FunWriteXlsxEndCallBack lambdaFunction = [](QString strXlsxName, QString strTableName)
    {
        qDebug()<<strXlsxName<<strTableName<<"write end";
        CFtpDB::GetInstance()->AddFtpUploadFile(strXlsxName);
    };

    pXlsxStruct->WriteEndCallBack = lambdaFunction;

    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
    qDebug()<<QString("%1# other data write to result xlsx end").arg(iMachineID + 1);
}

void CRunTest::AddPngPath(int iMachineID, QStringList strPathList)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    m_strPngPathList[iMachineID] = strPathList;
}

void CRunTest::AddJsonObj(int iMachineID, QString strName, QJsonObject qJsonObj)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    QJsonObject &qRootObj = m_qRootJsonObjList[iMachineID];
    qRootObj.insert(strName, qJsonObj);
}

void CRunTest::UpdatePrintInfo(SPrintInfoStruct sSPrintInfoStruct)
{
    m_sSPrintInfoStruct = sSPrintInfoStruct;
}

void CRunTest::WritePdf(int iMachineID, const QString& strPDFName,  const SRunningInfoStruct &sRunInfo,const QStringList& strImagePathList)
{

    //QString strPDFName = CPublicConfig::GetInstance()->GetTestPdfName(iMachineID);
    qDebug()<<"测试结果保存pdf:"<<strPDFName;
    if(strPDFName.isEmpty())
        return;

    //const SRunningInfoStruct &sRunInfo = m_sRunInfoList.at(iMachineID);
    CCalCTLib calcLib;
    QString strAutoVersion;
    QString strPath = QApplication::applicationDirPath() +  "/Resources/version";
    ReadFile(strPath, strAutoVersion);
    strAutoVersion.remove("\r").remove("\n");

    SPLCVerStruct sPLCVerStruct = CPublicConfig::GetInstance()->GetPLCVersionStruct(iMachineID);

    //QJsonObject &qRootObj = m_qRootJsonObjList[iMachineID];
    QJsonObject qRootObj;
    qRootObj.insert("PDFName", strPDFName);
    qRootObj.insert("iMachineID", iMachineID);
    qRootObj.insert("Operator", sRunInfo.sResultInfo.strOperator);
    qRootObj.insert("SampleID", sRunInfo.sSampleInfo.strSampleID);
    qRootObj.insert("CardID", sRunInfo.sCardInfo.strCardID);
    qRootObj.insert("StartTime", sRunInfo.sResultInfo.strTestTime);
    qRootObj.insert("EndTime", sRunInfo.sResultInfo.strTestEndTime);
    qRootObj.insert("SN1", CPublicConfig::GetInstance()->GetMachineSN());
    qRootObj.insert("SN2", sPLCVerStruct.strSN);
    qRootObj.insert("Project", sRunInfo.sCardInfo.strProject);
    qRootObj.insert("TimingName", sRunInfo.strTimingName);
    qRootObj.insert("TimingData", sRunInfo.strTimingData);
    qRootObj.insert("PCRName", sRunInfo.strTecName);
    qRootObj.insert("PCRData", sRunInfo.strTecData);
    qRootObj.insert("SoftVer", GetAppVersion());
    qRootObj.insert("CalcVer", calcLib.getVersion());
    qRootObj.insert("AutoVer", strAutoVersion);
    qRootObj.insert("SlaveVer", sPLCVerStruct.strMedianShowVersion);
    qRootObj.insert("PCRVer", sPLCVerStruct.strPCRShowVersion);
    qRootObj.insert("FLVer", sPLCVerStruct.strFLShowVersion);
    qRootObj.insert("TestType", bHrmTecType(sRunInfo.iTecIndex) ? 1 : 0);
    qRootObj.insert("PngPath", strImagePathList.join(" "));

    QString strAllVer;
    ReadFile(QApplication::applicationDirPath() + "/Resources/all.txt", strAllVer);
    qRootObj.insert("AllVer", strAllVer);

    // add print info
    {
        QJsonObject qPrintObj;
        qPrintObj.insert("SampleID", sRunInfo.sSampleInfo.strSampleID);
        if(false == sRunInfo.sSampleInfo.strSampleType.isEmpty())
        {
            qPrintObj.insert("SampleType", sRunInfo.sSampleInfo.strSampleType);
        }
        if(false == sRunInfo.sSampleInfo.strSamplingDate.isEmpty())
        {
            qPrintObj.insert("SamplingDate", sRunInfo.sSampleInfo.strSamplingDate);
        }
        qPrintObj.insert("TestTime", sRunInfo.sResultInfo.strTestTime);
        qPrintObj.insert("CardID", sRunInfo.sCardInfo.strCardID);
        if(false == sRunInfo.sCardInfo.strCardLot.isEmpty())
        {
            qPrintObj.insert("CardLot", sRunInfo.sCardInfo.strCardLot);
        }
        if(false == sRunInfo.sCardInfo.strCardMFG.isEmpty())
        {
            qPrintObj.insert("CardMFG", sRunInfo.sCardInfo.strCardMFG);
        }
        if(false == sRunInfo.sCardInfo.strCardEXP.isEmpty())
        {
            qPrintObj.insert("CardEXP", sRunInfo.sCardInfo.strCardEXP);
        }
        if(false == sRunInfo.sSampleInfo.strName.isEmpty())
        {
            qPrintObj.insert("Name", sRunInfo.sSampleInfo.strName);
        }
        if(false == sRunInfo.sSampleInfo.strGender.isEmpty())
        {
            qPrintObj.insert("Gender", sRunInfo.sSampleInfo.strGender);
        }
        if(false == sRunInfo.sSampleInfo.strAge.isEmpty())
        {
            qPrintObj.insert("Age", sRunInfo.sSampleInfo.strAge);
        }
        if(false == sRunInfo.sSampleInfo.strBirthday.isEmpty())
        {
            qPrintObj.insert("Birthday", sRunInfo.sSampleInfo.strBirthday);
        }
        if(false == sRunInfo.sSampleInfo.strTelephone.isEmpty())
        {
            qPrintObj.insert("Telephone", sRunInfo.sSampleInfo.strTelephone);
        }
        //        if(m_sSPrintInfoStruct.bOperator)
        //        {
        //            qPrintObj.insert("Operator", sRunInfo.sResultInfo.strOperator);
        //        }

        SLotInfoStruct sLotInfo;
        CLotInfoDB::GetInstance()->GetLotInfoByShowName(sRunInfo.sResultInfo.strProjectName, sLotInfo);
        QStringList strTarPrintList = GetTargetOutputInfoList(sRunInfo.sResultInfo, sLotInfo);
        qPrintObj.insert("Target", strTarPrintList.join("@#"));
        qRootObj.insert("Print", qPrintObj);
    }

    QJsonDocument qJsonDoc(qRootObj);
    QByteArray qByteJson = qJsonDoc.toJson(QJsonDocument::Compact);
    qDebug()<<Q_FUNC_INFO<<qByteJson.data();

    QString strTestTime = sRunInfo.sResultInfo.strTestTime;
    QString strProject = sRunInfo.sResultInfo.strProjectName;
    strProject.replace("/", "+");
    QString strFileName = QString("%1_%2#_%3_%4_%5")
            .arg(strTestTime.remove(" ").remove("-").remove(":"))
            .arg(iMachineID + 1)
            .arg(DeleteSpecialCharacters(sRunInfo.sSampleInfo.strSampleID))
            .arg(DeleteSpecialCharacters(sRunInfo.sCardInfo.strCardID))
            .arg(DeleteSpecialCharacters(strProject));

    QString strMsgFilePath = QApplication::applicationDirPath() + "/" + strFileName;
    WriteFile(strMsgFilePath, qByteJson);

    CPdfTcpServer::GetInstance()->WriteData(strMsgFilePath.toLocal8Bit());
    return;
}

void CRunTest::SaveSimutePdf()
{
    QList<int> iHistoryIDList;
    CProjectDB::GetInstance()->GetAllHistoryID(iHistoryIDList);

    for(int i=0; i<iHistoryIDList.size(); i++)
    {
        qDebug()<<"生成######################################:"<<i;

        SResultInfoStruct sResultInfo;
        CProjectDB::GetInstance()->GetHistoryData(iHistoryIDList.at(i), sResultInfo);

        QString strSampleID = sResultInfo.strSampleID;
        QString strCardID = sResultInfo.strCardID;
        QString strTestTime = sResultInfo.strTestTime;
        QString strProject = sResultInfo.strProjectName;

        SSampleInfoStruct sSampleInfo;
        CProjectDB::GetInstance()->GetSampleData(strProject, strSampleID, strTestTime, sSampleInfo);

        SCardInfoStruct sCardInfo;
        CProjectDB::GetInstance()->GetCardData(strCardID, strTestTime, sCardInfo);

        int iMachineID = sResultInfo.iMachineID;
        QString strPdfName = QString("01/%1_%2#_%3_%4_%5.pdf")
                .arg(strTestTime.remove("-").remove(":").remove(" "))
                .arg(iMachineID+1)
                .arg(DeleteSpecialCharacters(strSampleID))
                .arg(DeleteSpecialCharacters(strCardID))
                .arg(DeleteSpecialCharacters(strProject));

        SRunningInfoStruct sRunInfo;
        sRunInfo.sResultInfo = sResultInfo;
        sRunInfo.sSampleInfo = sSampleInfo;
        sRunInfo.sCardInfo = sCardInfo;
        sRunInfo.iTecIndex = sResultInfo.iTestProject;

        CCreateImage::GetInstance()->SaveHistoryDetailImage(sRunInfo,false);
        WritePdf(iMachineID, strPdfName, sRunInfo,m_strPngPathList.at(iMachineID));

        Delay_MSec(1000);
    }
}
