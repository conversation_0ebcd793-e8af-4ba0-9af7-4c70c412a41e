QLabel
{
   color: #353E4E;
   font-size: 20px;
   font-family: "Source Han Sans CN";
   border: 0px solid red;
}

QLabel#TopLabel
{
   color: #6B788F;
   font-size: 24px;
   font-family: "Source Han Sans CN";
}

QLabel#BackgroundLabel
{
   border-radius: 32px;
   background-color: #FFF;
}

QLabel#TitleIconLabel
{
   border-radius: 3px;
   background-color: #3D78E5;
}
QLabel#TitleTextLabel
{
   color: #353E4E;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
}

QLabel#PageLabel
{
   color: #353E4E;
   font-size: 24px;
   font-weight: 400;
   font-family: "Source Han Sans CN";
}

QLineEdit
{
   color: #353E4E;
   font-size: 24px;
   font-family: "Source Han Sans CN";
   padding-left: 20px;
   border-radius: 28px;
   background-color: #F4F7FE;
}
QLineEdit:focus{ padding-left: 20px; }

QLineEdit#GotoLineEdit
{
   padding-left: 0px;
   border-radius: 20px;
}

QPushButton
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #8490FF, stop: 1 #3D78E5);
}
QPushButton:pressed
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #6670c7, stop: 1 #3361b8);
}

QPushButton:focus{outline: none;}

QPushButton#GotoBtn
{
   font-size: 18px;
   font-weight: 400;
   font-family: "Source Han Sans CN";
   border-radius: 20px;
}

QPushButton#CancelBtn
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 2px solid #3D78E5;
   background-color: #FFF;
}

QPushButton#CancelBtn:hover
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 3px solid #3D78E5;
   background-color: #FFF;
}

QPushButton#CancelBtn:pressed
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 4px solid #3D78E5;
   background-color: #FFF;
}

QPushButton#PrePageBtn
{
   background-color: transparent;
   image: url(:/image/ico/history/previous.png);
}
QPushButton#PrePageBtn:disabled
{
   background-color: transparent;
   image: url(:/image/ico/history/previous-disable.png);
}
QPushButton#NextPageBtn
{
   background-color: transparent;
   image: url(:/image/ico/history/next.png);
}
QPushButton#NextPageBtn:disabled
{
   background-color: transparent;
   image: url(:/image/ico/history/next-disable.png);
}

QCheckBox
{
   color: #6B788F;
   font-size: 24px;
   font-family: "Source Han Sans CN";
   border: 0px solid red;
}
QCheckBox:focus{outline: none;}
QCheckBox::indicator
{
   width: 44px;
   height: 44px;
   /* subcontrol-position:right  right;*/
}
QCheckBox::indicator::unchecked
{
   image: url(:/image/ico/history/uncheck.png);
}
QCheckBox::indicator::checked
{
   image: url(:/image/ico/history/check.png);
}

QListWidget#HistoryListWidget
{
   border: 0px solid red;
   border-radius: 20px;
   background-color: #FFF;
   color: #353E4E;
   font-family: "Source Han Sans CN";
   font-size: 20px;
   outline: none;
}
QListWidget#HistoryListWidget::item
{
   margin: 0px 0px 10px 0px;
   border: 1px solid #D6DFE9;
   border-radius: 10px;
   background-color: #FFF;
   color: #333; /* text color */
   font-family: "Source Han Sans CN";
   font-size: 20px;
   outline: none;
}
QListWidget#HistoryListWidget::Item:hover,
QListWidget#HistoryListWidget::Item:selected
{
   border: 4px solid #498CE5;
   border-radius: 10px;
   background-color: #FFF;
}
QListWidget#HistoryListWidget::Item:selected:!active {
   border: 1px solid #D6DFE9;
   border-radius: 10px;
   background-color: #FFF;
}

QComboBox
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    padding-left: 20px;
    border-radius: 28px;
    background-color: #F3F8FF;
}

QComboBox::drop-down
{
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 60px;
    font-weight: 500;
    border-left: 0px solid red;
}
QComboBox::down-arrow
{
    width: 32px;
    height: 32px;
    image: url(:/image/ico/login/commod.png);
    padding: 0px 20px 0px 0px;
}
QComboBox QAbstractItemView
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    selection-background-color: #248CEB;
}
QComboBox QAbstractItemView::item
{
    min-height: 56px;/*下拉列表的行高，也可以看做行距*/
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
}

QComboBox:focus{outline: none;}
