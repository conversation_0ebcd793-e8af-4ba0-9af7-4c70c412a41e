#include "CLightCalibrate.h"
#include <QBoxLayout>
#include "CMessageBox.h"

CLightCalibrate::CLightCalibrate(QWidget *parent) : QWidget(parent)
{
    Register2Map(Method_FLGAINSET);
    Register2Map(Method_FLGAINGET);    
    Register2Map(Method_fl_ledi_get);
    Register2Map(Method_fl_ledi_set);
    Register2Map(Method_fl_mppcv_set);
    Register2Map(Method_fl_mppcv_get);

    // 初始化操作标志
    m_bWaitingForGainResult = false;
    m_bWaitingForLedResult = false;
    m_bWaitingForMppcResult = false;

    for(int i=0; i<gk_iMachineCount; i++)
    {
        QStringList strList;
        for(int j=0; j<8; j++)
            strList.push_back("");
        m_strValueList.push_back(strList);
    }

    _InitWidget();
}

CLightCalibrate::~CLightCalibrate()
{
    UnRegister2Map(Method_FLGAINSET);
    UnRegister2Map(Method_FLGAINGET);
    UnRegister2Map(Method_fl_ledi_get);
    UnRegister2Map(Method_fl_ledi_set);
    UnRegister2Map(Method_fl_mppcv_set);
    UnRegister2Map(Method_fl_mppcv_get);
}

void CLightCalibrate::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if(Method_FLGAINSET == iMethodID)
    {
        if(m_bWaitingForGainResult)
        {
            m_bWaitingForGainResult = false;
            if(0 == iResult)
                ShowInformation(this, m_strTipsText, tr("%1#机器设置光学校准参数成功").arg(iMachineID + 1));
            else
                ShowError(this, m_strTipsText, tr("%1#机器设置光学校准参数失败").arg(iMachineID + 1));
        }
    }
    else if(Method_FLGAINGET == iMethodID)
    {
        QVariantList qVarList = qVarData.toList();
        if(0 != iResult || qVarList.size() < 8)
        {
            if(m_bWaitingForGainResult)
            {
                m_bWaitingForGainResult = false;
                ShowError(this, m_strTipsText, tr("%1#机器读取光学校准参数失败").arg(iMachineID + 1));
            }
            return;
        }

        QStringList strGetList;
        for(int i=0; i<8; i++)
           strGetList.push_back(qVarList.at(i).toString());
        m_strValueList[iMachineID] = strGetList;

        if(m_pMachineComboBox->GetCurrentIndex() == iMachineID)
        {
            m_pLineEditList1->SetValueList(strGetList.mid(0, 4));
            m_pLineEditList2->SetValueList(strGetList.mid(4, 8));
        }
        
        if(m_bWaitingForGainResult)
        {
            m_bWaitingForGainResult = false;
            ShowInformation(this, m_strTipsText, tr("%1#机器读取光学校准参数成功").arg(iMachineID + 1));
        }
    }
    else if (Method_fl_ledi_get == iMethodID)
    {
        if(0 != iResult)
        {
            if(m_bWaitingForLedResult)
            {
                m_bWaitingForLedResult = false;
                ShowError(this, m_strTipsText, tr("%1#机器读取LED参数失败").arg(iMachineID + 1));
            }
        }
        else
        {
            QVariantList qVarList = qVarData.toList();
            m_pLedIComboBox->SetCurrentIndex(qVarList.at(0).toInt());
            
            if(m_bWaitingForLedResult)
            {
                m_bWaitingForLedResult = false;
                ShowInformation(this, m_strTipsText, tr("%1#机器读取LED参数成功").arg(iMachineID + 1));
            }
        }
    }
    else if (Method_fl_ledi_set == iMethodID)
    {
        if(m_bWaitingForLedResult)
        {
            m_bWaitingForLedResult = false;
            if(0!= iResult)
            {
                ShowError(this, m_strTipsText, tr("%1#机器设置LED参数失败").arg(iMachineID + 1));
            }
            else
            {
                ShowInformation(this, m_strTipsText, tr("%1#机器设置LED参数成功").arg(iMachineID + 1));
            }
        }
    }
    else if (Method_fl_mppcv_set == iMethodID)
    {
        if(m_bWaitingForMppcResult)
        {
            m_bWaitingForMppcResult = false;
            if(0!= iResult)
            {
                ShowError(this, m_strTipsText, tr("%1#机器设置MPPCV参数失败").arg(iMachineID + 1));
            }
            else
            {
                ShowInformation(this, m_strTipsText, tr("%1#机器设置MPPCV参数成功").arg(iMachineID + 1));
            }
        }
    }
    else if (Method_fl_mppcv_get == iMethodID)
    {
        if(0!= iResult)
        {
            if(m_bWaitingForMppcResult)
            {
                m_bWaitingForMppcResult = false;
                ShowError(this, m_strTipsText, tr("%1#机器读取MPPCV参数失败").arg(iMachineID + 1));
            }
        } 
        else
        {
            QVariantList qVarList = qVarData.toList();
            m_pMppcVLineEdit->SetLineEditText(qVarList.at(0).toString());
            
            if(m_bWaitingForMppcResult)
            {
                m_bWaitingForMppcResult = false;
                ShowInformation(this, m_strTipsText, tr("%1#机器读取MPPCV参数成功").arg(iMachineID + 1));
            }
        }
    }
}

void CLightCalibrate::_SlotReadBtn()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    QString strCmd = GetJsonCmdString(Method_FLGAINGET);
    m_bWaitingForGainResult = true;
    SendJsonCmd(iMachineID, Method_FLGAINGET, strCmd);
}

void CLightCalibrate::_SlotSetBtn()
{
    QVariantList qVarList;
    QStringList strList1 = m_pLineEditList1->GetValueList();
    foreach (QString str, strList1)
    {
        if(str.isEmpty())
        {
            ShowInformation(this, m_strTipsText, tr("孔1有参数为空"));
            return;
        }        
        qVarList.push_back(str.toDouble());
    }

    QStringList strList2 = m_pLineEditList2->GetValueList();
    foreach (QString str, strList2)
    {
        if(str.isEmpty())
        {
            ShowInformation(this, m_strTipsText, tr("孔2有参数为空"));
            return;
        }
        qVarList.push_back(str.toDouble());
    }

    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_strValueList[iMachineID] = strList1 + strList2;
    QString strCmd = GetJsonCmdString(Method_FLGAINSET, qVarList);
    m_bWaitingForGainResult = true;
    SendJsonCmd(iMachineID, Method_FLGAINSET, strCmd);
}

void CLightCalibrate::_SlotReadLedBtn()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    QString strCmd = GetJsonCmdString(Method_fl_ledi_get);
    m_bWaitingForLedResult = true;
    SendJsonCmd(iMachineID, Method_fl_ledi_get, strCmd);
}

void CLightCalibrate::_SlotSetLedBtn()
{
    QVariantList qVarList;
    qVarList.push_back(m_pLedIComboBox->GetCurrentIndex());

    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    QString strCmd = GetJsonCmdString(Method_fl_ledi_set, qVarList);
    m_bWaitingForLedResult = true;
    SendJsonCmd(iMachineID, Method_fl_ledi_set, strCmd);
}

void CLightCalibrate::_SlotSetMppcVBtn()
{
    QVariantList qVarList;
    qVarList.push_back(m_pMppcVLineEdit->GetLineEditText().toInt());

    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    QString strCmd = GetJsonCmdString(Method_fl_mppcv_set, qVarList);
    m_bWaitingForMppcResult = true;
    SendJsonCmd(iMachineID, Method_fl_mppcv_set, strCmd);
}

void CLightCalibrate::_SlotGetMppcVBtn()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    QString strCmd = GetJsonCmdString(Method_fl_mppcv_get);
    m_bWaitingForMppcResult = true;
    SendJsonCmd(iMachineID, Method_fl_mppcv_get, strCmd);
}

void CLightCalibrate::_SlotMachineChanged(int iMachineID)
{
    QStringList strList = m_strValueList.at(iMachineID);
    m_pLineEditList1->SetValueList(strList.mid(0, 4));
    m_pLineEditList2->SetValueList(strList.mid(4, 8));
}

void CLightCalibrate::_InitWidget()
{
    QStringList strNameList = CPublicConfig::GetInstance()->GetHoleNameList();
    QStringList strValueList = {"", "", "", ""};
    m_pLineEditList1 = new CIPLabelLineEdit(strNameList.first(), strValueList);
    m_pLineEditList1->SetPlaceholderText(gk_strColorNameList);
    m_pLineEditList1->SetPointLabelVisable(false);

    m_pLineEditList2 = new CIPLabelLineEdit(strNameList.last(), strValueList);
    m_pLineEditList2->SetPlaceholderText(gk_strColorNameList);
    m_pLineEditList2->SetPointLabelVisable(false);

    m_pReadBtn = new QPushButton(tr("读取"));
    m_pReadBtn->setFixedSize(120, 50);
    connect(m_pReadBtn, &QPushButton::clicked, this, &CLightCalibrate::_SlotReadBtn);

    m_pSetBtn = new QPushButton(tr("设置"));
    m_pSetBtn->setFixedSize(120, 50);
    connect(m_pSetBtn, &QPushButton::clicked, this, &CLightCalibrate::_SlotSetBtn);

    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(120, 50);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMachineChanged(int)));

    const QStringList gk_strLedIList = {"250mA", "500mA"};
    m_pLedIComboBox = new CLabelComboBox(tr("LED电流:"), gk_strLedIList);
    m_pLedIComboBox->SetComboBoxFixedSize(120, 50);
    m_pLedIComboBox->SetCurrentIndex(0);

    m_pReadLedBtn = new QPushButton(tr("读取"));
    m_pReadLedBtn->setFixedSize(120, 50);
    connect(m_pReadLedBtn, &QPushButton::clicked, this, &CLightCalibrate::_SlotReadLedBtn);

    m_pSetLedBtn = new QPushButton(tr("设置"));
    m_pSetLedBtn->setFixedSize(120, 50);
    connect(m_pSetLedBtn, &QPushButton::clicked, this, &CLightCalibrate::_SlotSetLedBtn);

    m_pMppcVLineEdit = new CLabelLineEdit(tr("MPPC偏压:"));
    m_pMppcVLineEdit->SetLineEditInputMethod(Qt::ImhDigitsOnly);
    m_pMppcVLineEdit->SetLineEditFixedSize(80, 50);

    m_pReadMppcVBtn = new QPushButton(tr("读取"));
    m_pReadMppcVBtn->setFixedSize(120, 50);
    connect(m_pReadMppcVBtn, &QPushButton::clicked, this, &CLightCalibrate::_SlotGetMppcVBtn);

    m_pSetMppcVBtn = new QPushButton(tr("设置"));
    m_pSetMppcVBtn->setFixedSize(120, 50);
    connect(m_pSetMppcVBtn, &QPushButton::clicked, this, &CLightCalibrate::_SlotSetMppcVBtn);

    QHBoxLayout *pHLayout = new QHBoxLayout;
    pHLayout->setMargin(0);
    pHLayout->addStretch(1);
    pHLayout->addWidget(m_pReadBtn);
    pHLayout->addSpacing(20);
    pHLayout->addWidget(m_pSetBtn);
    pHLayout->addStretch(1);

    QHBoxLayout *pHLedButtonLayout = new QHBoxLayout;
    pHLedButtonLayout->setMargin(0);
    pHLedButtonLayout->addStretch(1);
    pHLedButtonLayout->addWidget(m_pReadLedBtn);
    pHLedButtonLayout->addSpacing(20);
    pHLedButtonLayout->addWidget(m_pSetLedBtn);
    pHLedButtonLayout->addStretch(1);

    QHBoxLayout *pHMppcVButtonLayout = new QHBoxLayout;
    pHMppcVButtonLayout->setMargin(0);
    pHMppcVButtonLayout->addStretch(1);
    pHMppcVButtonLayout->addWidget(m_pReadMppcVBtn);
    pHMppcVButtonLayout->addSpacing(20);
    pHMppcVButtonLayout->addWidget(m_pSetMppcVBtn);
    pHMppcVButtonLayout->addStretch(1);

    QVBoxLayout *pVLayout = new QVBoxLayout;
    pVLayout->setMargin(0);
    pVLayout->setSpacing(20);
    pVLayout->addStretch(1);
    pVLayout->addWidget(m_pMachineComboBox, 0, Qt::AlignCenter);
    pVLayout->addWidget(m_pLineEditList1, 0, Qt::AlignCenter);
    pVLayout->addWidget(m_pLineEditList2, 0, Qt::AlignCenter);
    pVLayout->addLayout(pHLayout);
    pVLayout->addWidget(m_pLedIComboBox, 0, Qt::AlignCenter);
    pVLayout->addLayout(pHLedButtonLayout);
    pVLayout->addWidget(m_pMppcVLineEdit, 0, Qt::AlignCenter);
    pVLayout->addLayout(pHMppcVButtonLayout);
    pVLayout->addStretch(1);

    this->setLayout(pVLayout);
}
