#include "CSelfTestWidget.h"
#include <QBoxLayout>
#include "PublicFunction.h"

CSelfTestWidget::CSelfTestWidget(QWidget *parent) : QWidget(parent)
{
    _InitWidget();
    _InitLayout();

    LoadQSS(this, ":/qss/qss/system/calibration.qss");
}

void CSelfTestWidget::_SlotTitleChanged(int iTitle)
{
    m_pStackedWidget->setCurrentIndex(iTitle);
}

void CSelfTestWidget::_InitWidget()
{
    m_pCSysTtileLabelWidget = new CSysFirstTitleWidget(tr("系统设置"), tr("仪器自检"));
    connect(m_pCSysTtileLabelWidget, &CSysFirstTitleWidget::SignalTitlePress, this, &CSelfTestWidget::SignalReturn);

    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(1684, 904);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");

    m_pCSysSecondTitleWidget = new CSysSecondTitleWidget({tr("设备自检"), tr("自检历史")});
    connect(m_pCSysSecondTitleWidget, &CSysSecondTitleWidget::SignalSecondTitle, this, &CSelfTestWidget::_SlotTitleChanged);

    m_pCSelfTestDeviceWidget = new CSelfTestDeviceWidget;
    connect(m_pCSelfTestDeviceWidget, &CSelfTestDeviceWidget::SignalReturn, this, &CSelfTestWidget::SignalReturn);

    m_pCSelfTestHistoryWidget = new CSelfTestHistoryWidget;
    connect(m_pCSelfTestHistoryWidget, &CSelfTestHistoryWidget::SignalReturn, this, &CSelfTestWidget::SignalReturn);

    m_pStackedWidget = new QStackedWidget;
    m_pStackedWidget->setFixedSize(1636, 800);
    m_pStackedWidget->addWidget(m_pCSelfTestDeviceWidget);
    m_pStackedWidget->addWidget(m_pCSelfTestHistoryWidget);
}

void CSelfTestWidget::_InitLayout()
{
    QVBoxLayout *pBackLayout = new QVBoxLayout;
    pBackLayout->setContentsMargins(24, 15, 24, 24);
    pBackLayout->addWidget(m_pCSysSecondTitleWidget, 0, Qt::AlignLeft);
    pBackLayout->addStretch(1);
    pBackLayout->addWidget(m_pStackedWidget, 0, Qt::AlignHCenter);
    m_pBackgroundLabel->setLayout(pBackLayout);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pCSysTtileLabelWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pBackgroundLabel);
    this->setLayout(pLayout);
}
