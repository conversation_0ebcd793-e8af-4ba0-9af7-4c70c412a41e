#ifndef CHRMONECALIBRATE_H
#define CHRMONECALIBRATE_H

#include <QLabel>
#include <QPushButton>
#include "CLineEdit.h"

class CLabel2LineEdit : public QWidget
{
    Q_OBJECT
public:
    CLabel2LineEdit(const QString &strTitle, QWidget *parent = nullptr);
    ~CLabel2LineEdit();

    QString GetValue1() const;
    QString GetValue2() const;

private:
    void _InitWidget();

private:
    QLabel *m_pLabel;
    CLineEdit *m_pLineEdit1, *m_pLineEdit2;
    QString m_strTitle;
};

class CLysisOneCalibrate : public QWidget
{
    Q_OBJECT

public:
    explicit CLysisOneCalibrate(const QString &strGroup, QWidget *parent = nullptr);
    ~CLysisOneCalibrate();

    QStringList GetSrcDstList() const;

private:
    void _InitWidget();

private:
    QString m_strGroup;
    CLabel2LineEdit *m_pFirst, *m_pSecond, *m_pThird, *m_pForth;

};

#endif // CHRMONECALIBRATE_H
