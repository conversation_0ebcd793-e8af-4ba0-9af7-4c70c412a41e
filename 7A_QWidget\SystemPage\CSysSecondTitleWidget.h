#ifndef CSYSSECONDTITLEWIDGET_H
#define CSYSSECONDTITLEWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-02
  * Description: 系统二级标题 xxx xxx xxx
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QPushButton>

class CSysSecondTitleWidget : public QWidget
{
    Q_OBJECT
public:
    CSysSecondTitleWidget(const QStringList &strTextList, int iLabelWidth = 1636, QWidget *parent = nullptr);

signals:
    void SignalSecondTitle(int iTitle);

private slots:
    void _SlotBtnClicked();

private:
    QList<QPushButton *> m_pBtnList;
    QLabel *m_pLabel;
};

#endif // CSYSSECONDTITLEWIDGET_H
