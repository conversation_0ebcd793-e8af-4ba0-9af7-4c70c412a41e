#include "CSystemLogWidget.h"
#include <QDir>
#include <QThread>
#include <QProcess>
#include <QDirIterator>
#include <QBoxLayout>
#include <QDateTime>
#include <QApplication>
#include "CMessageBox.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "ZipManager/zip.h"
#include "DBControl/CLogDB.h"

CSystemLogWidget::CSystemLogWidget(QWidget *parent)
    : QWidget(parent)
    , m_bExporting(false)
    , m_strErrString(tr("系统日志导出失败，请检查U盘"))
    , m_iDateType(eBeginDate)
{
    this->setFixedSize(1636, 800);

    _InitWidget();
    _InitLayout();
    connect(this, &CSystemLogWidget::SignalUpdateInfo, this, &CSystemLogWidget::_SlotUpdateInfo, Qt::BlockingQueuedConnection);
    connect(this, &CSystemLogWidget::SignalExportError, this, &CSystemLogWidget::_SlotExportError);
    connect(this, &CSystemLogWidget::SignalExportEnd, this, &CSystemLogWidget::_SlotExportEnd);

    m_pCDateTimeWidget = new CDateTimeWidget(this);
    m_pCDateTimeWidget->SetOnlyDateModel();
    connect(m_pCDateTimeWidget, &CDateTimeWidget::SignalDateTime, this, &CSystemLogWidget::_SlotConfirmDate);
    m_pCDateTimeWidget->setVisible(false);

    System("rm -rf 7C_SystemLog* &");
}

void CSystemLogWidget::_SlotShowDateWidget()
{
    CHNewLabelDate *pLabelDate = (CHNewLabelDate*)sender();
    if(nullptr == pLabelDate)
        return;

    m_iDateType = pLabelDate->property("DateType").toInt();
    m_pCDateTimeWidget->SetDateTime(pLabelDate->GetDateString());
    m_pCDateTimeWidget->setVisible(true);
}

void CSystemLogWidget::_SlotConfirmDate(const QString &strDate)
{
    if(eBeginDate == m_iDateType)
        m_pBeginDate->SetDateString(strDate);
    else
        m_pEndDate->SetDateString(strDate);
}

void CSystemLogWidget::_SlotUpdateInfo(QString strInfo)
{
    if(m_bExporting)
    {
        qDebug()<<"系统日志:"<<strInfo;
        m_pInfoLabel->setText(strInfo);
    }
}

void CSystemLogWidget::_SlotExportError(QString strError)
{    
    m_pInfoLabel->clear();
    m_pExportBtn->setText(tr("导出"));
    if(m_bExporting)
        ShowWarning(this, tr("提示"), strError);
    m_bExporting = false;
}

void CSystemLogWidget::_SlotExportEnd()
{ 
    m_pInfoLabel->clear();
    m_pExportBtn->setText(tr("导出"));
    if(m_bExporting)
        ShowSuccess(this, tr("提示"), tr("系统日志导出结束"));
    m_bExporting = false;
}

void CSystemLogWidget::_SlotExportBtn()
{
    if(m_bExporting)
    {
        int iBtnType = ShowQuestion(this, tr("提示"), tr("确定取消导出吗"));
        if(QMessageBox::Yes != iBtnType)
            return;

        QString strUser = CPublicConfig::GetInstance()->GetLoginUser();
        QString strLog = tr("取消导出系统日志");
        CLogDB::instance().AddOperationLog(strUser, strLog, CLogDB::eExportLog);

        m_bExporting = false;
        m_pInfoLabel->clear();
        m_pExportBtn->setText(tr("导出"));
        return;
    }

    QString strBeginDate = m_pBeginDate->GetDateString();
    QString strEndDate = m_pEndDate->GetDateString();
    if(strEndDate < strBeginDate)
    {
        strBeginDate = m_pEndDate->GetDateString();
        strEndDate = m_pBeginDate->GetDateString();
    }
    qDebug()<<"导出系统日志:"<<strBeginDate<<strEndDate;
    QDate qBeginDate = QDate::fromString(strBeginDate, "yyyy-MM-dd");
    QDate qEndDate = QDate::fromString(strEndDate, "yyyy-MM-dd");
    if(qBeginDate.daysTo(qEndDate) > 30)
    {
        ShowInformation(this, tr("提示"), tr("日期相差不能大于30天，请重新选择"));
        return;
    }

    QDir dir(GetUDiskDir());
    if(!dir.exists())
    {
        ShowInformation(this, tr("提示"), tr("请先插入U盘"));
        return;
    }

    m_bExporting = true;
    m_pExportBtn->setText(tr("取消导出"));

    QString strUser = CPublicConfig::GetInstance()->GetLoginUser();
    QString strLog = tr("导出系统日志，开始日期：%1，结束日期：%2").arg(m_pBeginDate->GetDateString()).arg(m_pEndDate->GetDateString());
    CLogDB::instance().AddOperationLog(strUser, strLog, CLogDB::eExportLog);

    std::thread exportThread(&CSystemLogWidget::_Thread2Export, this, strBeginDate, strEndDate);
    exportThread.detach();;
}

void CSystemLogWidget::_Thread2Export(QString strBeginDate, QString strEndDate)
{
    QString strCurrentDir = QApplication::applicationDirPath() + "/";
    QString strFolderName = QString("7C_SystemLog_%1_%2").arg(QString(strBeginDate).remove("-")).arg(QString(strEndDate).remove("-"));
    m_strFolderName = strFolderName;
    QString strZipDir = strCurrentDir + strFolderName + "/";
    qDebug()<<"导出系统日志:"<<strZipDir;
    CreateDir(strZipDir);

   // _ExportLoginLog(strBeginDate, strEndDate);

    if(!_ExportDB(strZipDir))
    {
        _DelZipDirAndFile();
        emit SignalExportError(m_strErrString);
        return;
    }

    if(!_ExportResources(strZipDir))
    {
        _DelZipDirAndFile();
        emit SignalExportError(m_strErrString);
        return;
    }

    if(!_ExportLog(strZipDir, strBeginDate, strEndDate))
    {
        _DelZipDirAndFile();
        emit SignalExportError(m_strErrString);
        return;
    }

#if 0
    if(!_ExportPdf(strZipDir, strBeginDate, strEndDate))
    {
        _DelZipDirAndFile();
        emit SignalExportError(m_strErrString);
        return;
    }
#endif

    if(!_ExportXlsx(strZipDir, strBeginDate, strEndDate))
    {
        _DelZipDirAndFile();
        emit SignalExportError(m_strErrString);
        return;
    }

#ifdef __aarch64__
    CopyQFileDir(strCurrentDir + gk_strAppName, QDir(strZipDir));
    CopyQFileDir(strCurrentDir + "7CPDF", QDir(strZipDir));
    CopyQFileDir(strCurrentDir + "7CStart", QDir(strZipDir));
    CopyQFileDir("/usr/lib/" + gk_strAlgorithmName, QDir(strZipDir));
    CopyQFileDir("/etc/init.d/S99_7C", QDir(strZipDir));
#endif

    if(!_ZipFolder(strFolderName))
    {
        _DelZipDirAndFile();
        emit SignalExportError(m_strErrString);
        return;
    }

    QString strZipName = strFolderName + ".zip";
    QString strExportDir = CPublicConfig::GetInstance()->GetUDiskExportDir();
    CreateDir(strExportDir);
    QFile::remove(strExportDir + strZipName); //先删除U盘可能存在的同名zip
    MoveQFile(m_strLoginLogPath, QDir(strExportDir));
    bool bMove = MoveQFile(strZipName, QDir(strExportDir));    
    _DelZipDirAndFile();
    if(bMove)
        emit SignalExportEnd();
    else
        emit SignalExportError(m_strErrString);
}

bool CSystemLogWidget::_ExportLoginLog(QString strBeginDate, QString strEndDate)
{
    emit SignalUpdateInfo(tr("正在导出登录日志"));

    QList<QStringList> strReadList;
    CLogDB::instance().ReadLoginLog(strBeginDate, strEndDate, strReadList);

    m_strLoginLogPath = QApplication::applicationDirPath() + "/";
    m_strLoginLogPath += QString("login_%1_%2.log").arg(QString(strBeginDate).remove("-")).arg(QString(strEndDate).remove("-"));
    QFile file(m_strLoginLogPath);
    if(!file.open(QIODevice::WriteOnly))
    {
        qDebug()<<__FUNCTION__<<file.errorString();
        return false;
    }
    for(int i=0; i<strReadList.size(); i++)
    {
        QStringList strOne = strReadList.at(i);
        if(strOne.size() < 5)
             continue;
        ELOGINTYPE eLogType = ELOGINTYPE(strOne.at(2).toInt());
        QString strUsrName = strOne.at(3);
        QString strDateTime = strOne.at(4);
        QString strMachineCode = strOne.at(5);
        QString strLogText = strDateTime + " " + GetLoginLogString(eLogType, strUsrName, strMachineCode) + "\n";
        file.write(strLogText.toUtf8());
    }
    file.close();
    return true;
}

bool CSystemLogWidget::_ExportDB(QString strExportDir)
{
    if(!_ContinueExport())
        return false;

    emit SignalUpdateInfo(tr("正在导出数据库"));
    QString strCurrentDir = QApplication::applicationDirPath() + "/";
    QString strSrcPath = strCurrentDir + "db/";
    QString strDstPath = strExportDir + "db/";
    CreateDir(strDstPath);
#ifdef Q_OS_WIN
    QProcess process;
    process.start("robocopy", QStringList() << strSrcPath << strDstPath);
    process.waitForFinished();
    process.close();
#else
    QString strCmd = QString("cp -r %1* %2").arg(strSrcPath).arg(strDstPath);
    System(strCmd);
#endif
    return true;
}

bool CSystemLogWidget::_ExportResources(QString strExportDir)
{
    if(!_ContinueExport())
        return false;

    emit SignalUpdateInfo(tr("正在导出配置"));
    QString strCurrentDir = QApplication::applicationDirPath() + "/";
    QString strSrcPath = strCurrentDir + "Resources/";
    QString strDstPath = strExportDir + "Resources/";
    CreateDir(strDstPath);
#ifdef Q_OS_WIN
    QProcess process;
    process.start("robocopy", QStringList() << strSrcPath << strDstPath);
    process.waitForFinished();
    process.close();
#else
    QString strCmd = QString("cp -r %1* %2").arg(strSrcPath).arg(strDstPath);
    System(strCmd);
#endif
    return true;
}

bool CSystemLogWidget::_ExportLog(QString strExportDir, QString strBeginDate, QString strEndDate)
{
    if(!_ContinueExport())
        return false;

    emit SignalUpdateInfo(tr("正在导出日志"));
    QStringList strFileList;
    QStringList strDirList = CPublicConfig::GetInstance()->GetLogDirList();
    for(int i=0; i<strDirList.size(); i++)
        strFileList.append(_GetMacthDateFilesFromDir(eLog, strDirList.at(i), strBeginDate, strEndDate));
    int iNums = strFileList.size();
    if(0 == iNums)
        return true;

    QDir qExportDir(strExportDir + "log/");
    CreateDir(qExportDir.path());
    for(int i=0; i<iNums; i++)
    {
        if(!_ContinueExport())
            return false;

        bool bCopy = CopyQFileDir(strFileList.at(i), qExportDir);
        emit SignalUpdateInfo(tr("正在导出日志:%1/%2").arg(i+1).arg(iNums));
        if(false == bCopy)
            return false;
    }

    return true;
}

bool CSystemLogWidget::_ExportPdf(QString strExportDir, QString strBeginDate, QString strEndDate)
{
    if(!_ContinueExport())
        return false;

    emit SignalUpdateInfo(tr("正在导出pdf"));
    QString strPdfDir = CPublicConfig::GetInstance()->GetPdfDir();
    QStringList strFileList = _GetMacthDateFilesFromDir(ePdf, strPdfDir, strBeginDate, strEndDate);
    int iNums = strFileList.size();
    if(0 == iNums)
        return true;

    QDir qExportDir(strExportDir + "pdf/");
    CreateDir(qExportDir.path());
    for(int i=0; i<iNums; i++)
    {
        if(!_ContinueExport())
            return false;

        bool bCopy = CopyQFileDir(strFileList.at(i), qExportDir);
        emit SignalUpdateInfo(tr("正在导出pdf:%1/%2").arg(i+1).arg(iNums));
        if(false == bCopy)
            return false;
    }

    return true;
}

bool CSystemLogWidget::_ExportXlsx(QString strExportDir, QString strBeginDate, QString strEndDate)
{
    if(!_ContinueExport())
        return false;

    emit SignalUpdateInfo(tr("正在导出xlsx"));
    QString strXlsxDir = CPublicConfig::GetInstance()->GetXlsxDir();
    QStringList strFileList = _GetMacthDateFilesFromDir(ePdf, strXlsxDir, strBeginDate, strEndDate);
    int iNums = strFileList.size();
    if(0 == iNums)
        return true;

    QDir qExportDir(strExportDir + "xlsx/");
    CreateDir(qExportDir.path());
    for(int i=0; i<iNums; i++)
    {
        if(!_ContinueExport())
            return false;

        bool bCopy = CopyQFileDir(strFileList.at(i), qExportDir);
        emit SignalUpdateInfo(tr("正在导出xlsx:%1/%2").arg(i+1).arg(iNums));
        if(false == bCopy)
            return false;
    }

    return true;
}

bool CSystemLogWidget::_ZipFolder(QString strFolderName)
{
    if(!_ContinueExport())
        return false;

    emit SignalUpdateInfo(tr("正在压缩"));
    QStringList strFileList;
    QDirIterator it(strFolderName, QDir::Files | QDir::NoDotAndDotDot, QDirIterator::Subdirectories);
    while(it.hasNext())
    {
        if(!_ContinueExport())
            return false;

        it.next();
        strFileList << it.filePath();
    }
    QString strZipName = strFolderName + ".zip";
    HZIP hz = CreateZip(strZipName.toStdString().c_str(), "wondfo");
    for(int i=0; i<strFileList.size(); i++)
    {
        if(!_ContinueExport())
        {
            CloseZip(hz);
            return false;
        }

        QString strOneFile = strFileList.at(i);
        qDebug()<<"系统日志 zip:"<<strOneFile;
        ZipAdd(hz, strOneFile.toStdString().c_str(), strOneFile.toStdString().c_str());
    }
    CloseZip(hz);

    return true;
}

void CSystemLogWidget::_DelZipDirAndFile()
{
    //导出结束或者异常时删除文件夹和压缩包
    QString strZipDir = QApplication::applicationDirPath() + "/" + m_strFolderName;
    QDir qDelDir(strZipDir);
    if(qDelDir.exists())
        qDelDir.removeRecursively();

    QFile::remove(strZipDir + ".zip");
    qDebug()<<"CSystemLogWidget del:"<<strZipDir<<strZipDir + ".zip";
}

bool CSystemLogWidget::_ContinueExport()
{
    QDir qDir(GetUDiskDir());
    if(!qDir.exists())
        return false;

    return m_bExporting;
}

QStringList CSystemLogWidget::_GetMacthDateFilesFromDir(int iFileType, QDir qDir, QString strBeginDate, QString strEndDate)
{
    QStringList strFileList;
    QDate qBeginDate = QDate::fromString(strBeginDate, "yyyy-MM-dd");
    QDate qEndDate = QDate::fromString(strEndDate, "yyyy-MM-dd");
    if(qBeginDate > qEndDate)
    {
        qBeginDate = QDate::fromString(strEndDate, "yyyy-MM-dd");
        qEndDate = QDate::fromString(strBeginDate, "yyyy-MM-dd");
    }

    QFileInfoList fileList = qDir.entryInfoList(QDir::Files);
    for(int i=0; i<fileList.size(); i++)
    {
        QStringList strNameList = fileList.at(i).baseName().split("_");
        if(strNameList.size() < 2)
            continue;
        QDate qFileDate;
        if(eLog == iFileType)
            qFileDate = QDate::fromString(strNameList.last(), "yyyyMMdd");
        else if(ePdf == iFileType || eXlsx == iFileType)
            qFileDate = QDate::fromString(strNameList.first().mid(0, 8), "yyyyMMdd");
        else
            qFileDate = fileList.at(i).metadataChangeTime().date();

        if(qFileDate >= qBeginDate && qFileDate <= qEndDate)
            strFileList.push_back(fileList.at(i).absoluteFilePath());
    }

    return strFileList;
}

void CSystemLogWidget::_InitWidget()
{
    QString strDay = QDate::currentDate().toString("yyyy-MM-dd");

    m_pBeginDate = new CHNewLabelDate(tr("开始日期："), strDay, 10);
    m_pBeginDate->ResetDateLabelSize(280, 56);
    m_pBeginDate->setProperty("DateType", eBeginDate);
    connect(m_pBeginDate, &CHNewLabelDate::SignalPressEvent, this, &CSystemLogWidget::_SlotShowDateWidget);

    m_pGrayLabel = new QLabel;
    m_pGrayLabel->setFixedSize(48, 2);
    m_pGrayLabel->setObjectName("GrayLabel");

    m_pInfoLabel = new QLabel;
    m_pInfoLabel->setFixedSize(500, 50);
    m_pInfoLabel->setAlignment(Qt::AlignCenter);

    m_pEndDate = new CHNewLabelDate(tr("结束日期："), strDay, 10);
    m_pEndDate->ResetDateLabelSize(280, 56);
    m_pEndDate->setProperty("DateType", eEndDate);
    connect(m_pEndDate, &CHNewLabelDate::SignalPressEvent, this, &CSystemLogWidget::_SlotShowDateWidget);

    int iExportWidth = 120;
    if(eLanguage_Spanish == gk_iLanguage)
        iExportWidth = 140;
    else if(eLanguage_German == gk_iLanguage)
        iExportWidth = 160;

    m_pExportBtn = new QPushButton(tr("导出"));
    m_pExportBtn->setFixedSize(iExportWidth, 56);
    connect(m_pExportBtn, &QPushButton::clicked, this, &CSystemLogWidget::_SlotExportBtn);

    m_pReturnBtn = new QPushButton(tr("返回"));
    m_pReturnBtn->setFixedSize(150, 56);
    m_pReturnBtn->setObjectName("CancelBtn");
    connect(m_pReturnBtn, &QPushButton::clicked, this, &CSystemLogWidget::SignalReturn);
}

void CSystemLogWidget::_InitLayout()
{
    QHBoxLayout *pDateLayout = new QHBoxLayout;
    pDateLayout->setMargin(0);
    pDateLayout->setSpacing(0);
    pDateLayout->addStretch(1);
    pDateLayout->addWidget(m_pBeginDate);
    pDateLayout->addSpacing(24);
    pDateLayout->addWidget(m_pGrayLabel);
    pDateLayout->addSpacing(24);
    pDateLayout->addWidget(m_pEndDate);
    pDateLayout->addSpacing(48);
    pDateLayout->addWidget(m_pExportBtn);
    pDateLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addLayout(pDateLayout);
    pLayout->addSpacing(50);
    pLayout->addWidget(m_pInfoLabel, 0, Qt::AlignHCenter);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pReturnBtn, 0, Qt::AlignHCenter);
    this->setLayout(pLayout);
}
