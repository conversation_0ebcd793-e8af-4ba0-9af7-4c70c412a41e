#ifndef CCHECKUSERWIDGET_H
#define CCHECKUSERWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-11-13
  * Description: 用户身份确认
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QDialog>
#include <QGroupBox>
#include <QPushButton>

#include "CLineEdit.h"
#include "CHLabelTitleWidget.h"
#include "LoginPage/algorithmdescrypt.h"

class CCheckUserWidget : public QDialog
{
    Q_OBJECT
public:
    explicit CCheckUserWidget(const QString &strTitle = QString(), QWidget *parent = nullptr);
    ~CCheckUserWidget();

    bool GetCheckResult() const;

protected:
    void showEvent(QShowEvent *pEvent) override;
    void paintEvent(QPaintEvent *pEvent) override;

private slots:
    void _SlotUserChanged(const QString &strUser);
    void _SlotSeeBtn();
    void _SlotCancleBtn();
    void _SlotConfirmBtn();

private:
    void _CheckOK();
    void _CodeFactoryPassword();
    void _CodeAdminPassword();

private:
    QGroupBox *_CreateGroupBox();
    CLineEdit *_CreateLineEdit(int iEditWidth);

private:
    bool m_bSee;
    bool m_bDynamicPassword;
    bool m_bCheckOK;
    AlgorithmDesCrypt *m_pAlgorithmDesCrypt;
    QString m_strTitle;
    QString m_strCodeAdmin, m_strCodeFactory; //Admin和factroy的动态密码

private:
    CHLabelTitleWidget *m_pCHLabelTitleWidget;
    QLabel *m_pUserLabel;
    CLineEdit *m_pUserLineEdit;
    QLabel *m_pPasswordLabel;
    CLineEdit *m_pPasswordLineEdit;
    QPushButton *m_pSeeBtn;
    QLabel *m_pCodeLabel;
    QLabel *m_pInfoLabel;
    QPushButton *m_pCancleBtn, *m_pConfirmBtn;
};

bool ShowCheckUser(const QString &strTitle = QString(), QWidget *parent = nullptr);

#endif // CCHECKUSERWIDGET_H
