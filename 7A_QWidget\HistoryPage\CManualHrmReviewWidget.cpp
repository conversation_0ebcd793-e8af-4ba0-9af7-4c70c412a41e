#include "CManualHrmReviewWidget.h"
#include <QPainter>
#include <QBoxLayout>
#include "CProjectDB.h"
#include "CHistoryDB.h"
#include "PublicFunction.h"
#include <QStringList>

CManualHrmReviewWidget::CManualHrmReviewWidget(QWidget *parent)
    : QWidget(parent)
    , m_strPositive(tr("耐药型"))
    , m_strNegative(tr("敏感型"))
    , m_strError(tr("无效"))
    , m_strNull(tr("/"))
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setFixedSize(G_QRootSize);
    this->move(G_QRootPoint);
    this->setAttribute(Qt::WA_TranslucentBackground);
    //this->setWindowModality(Qt::WindowModal);
    qRegisterMetaType<stHrmReviewParam>("stHrmReviewParam");
    _InitWidget();
    _InitLayout();
    LoadQSS(this, ":/qss/qss/history/review.qss");

}

void CManualHrmReviewWidget::SetManualReviewParam(const SResultInfoStruct &sResultInfo, const SLotInfoStruct &sLotInfo,const SCardInfoStruct &sCardInfo,const QString& strTextName)
{
    m_sResultInfo = sResultInfo;
    m_sCardInfo = sCardInfo;
    m_sLotInfo = sLotInfo;
    _InitParamValue(sResultInfo,sLotInfo);
    _SetComboBoxValue();
    m_index = -1;
    int nIndex = 0;
    for(int i = 0 ; i < m_ReviewParamList.size(); i++)
    {
        if(m_ReviewParamList.at(i).m_strName == strTextName)
        {
            nIndex = i;
            break;
        }
    }

    if(m_ReviewParamList.at(nIndex).m_index == -1)
    {
        m_pHoleNameCombo->setCurrentIndex(0);
        m_index = 0;
    }
    else
    {
        m_pHoleNameCombo->setCurrentIndex(m_ReviewParamList.at(nIndex).m_index);
        m_index = m_ReviewParamList.at(nIndex).m_index;
    }
    qDebug()<<Q_FUNC_INFO<<"init cardId: "<<sResultInfo.strCardID<<"index: "<< nIndex; //阈值配置
    if(!m_qEventLoop.isRunning())
        m_qEventLoop.exec();
}
void CManualHrmReviewWidget::_InitParamValue(const SResultInfoStruct& sRestltInfo,const SLotInfoStruct& sLotInfo)
{
    m_ReviewParamList.clear();
    QStringList strNameList = sLotInfo.strCurveName.split(";");
    QStringList strMeltingInfoList = sRestltInfo.strMeltingInfo.split(";");
    QStringList strMeltingReviewInfoList = sRestltInfo.strMeltingInfo_Review.split(";");
    QStringList strResultList =sRestltInfo.strHrmResult_Review.split(";");
    QStringList strThresholdList = sLotInfo.strWildTypeTmValue.split(";");
    QStringList strTempRangeValue =  sLotInfo.strTempRangeValue.split(";");


    bool bReview = sRestltInfo.strReview == "m";
    int nSize = qMin(strNameList.size(),strMeltingInfoList.size());
    for(int i = 0 ; i < nSize; i++)
    {
        stHrmReviewParam stHrmReviewParam;
        stHrmReviewParam.m_bNull = ("0" == strNameList.at(i));
        stHrmReviewParam.m_strName = strNameList.at(i);
        if(stHrmReviewParam.m_bNull)
        {
            m_ReviewParamList.push_back(stHrmReviewParam);
            continue;
        }
        if(IsCtrlTarget(strNameList.at(i)))
        {
            stHrmReviewParam.m_bControl = true;
        }
        stHrmReviewParam.m_strMeltingInfo = strMeltingInfoList.at(i);

        if(i < strThresholdList.size() && i < strTempRangeValue.size())
        {
            stHrmReviewParam.m_fWildTmValue = strThresholdList.at(i).toFloat();
            stHrmReviewParam.m_fRange = strTempRangeValue.at(i).toFloat();
        }
        if(bReview && i < strResultList.size())
        {
            if(strResultList.at(i) != "/" && !strResultList.at(i).isEmpty())
            {
                stHrmReviewParam.m_bReview = true;
            }
            else
            {
                stHrmReviewParam.m_bReview = false;
            }
        }

        if(bReview && i < strMeltingReviewInfoList.size())
        {
            if(strMeltingReviewInfoList.at(i).split(",").size() >= 4)
            {
                stHrmReviewParam.m_strMeltingInfo_Review = strMeltingReviewInfoList.at(i);
            }
            if(i < strResultList.size())
            {
                stHrmReviewParam.m_result =strResultList.at(i);
            }
        }
        m_ReviewParamList.push_back(stHrmReviewParam);
    }
}

void CManualHrmReviewWidget::_SetParamResultValue()
{
    QStringList strResulReviewtList,strMeltingInfo_ReviewList;
    bool bReview = false;
    for(const auto& item : m_ReviewParamList)
    {
        if(item.m_bReview)
        {
           strMeltingInfo_ReviewList.push_back(item.m_strMeltingInfo_Review);
           strResulReviewtList.push_back(item.m_result);
           bReview = true;
        }
        else
        {
            strMeltingInfo_ReviewList.push_back("/");
            strResulReviewtList.push_back("/");
        }
    }
    if(bReview)
    {
        m_sResultInfo.strReview = bReview?"m":"";
        m_sResultInfo.strMeltingInfo_Review = strMeltingInfo_ReviewList.join(";");
        m_sResultInfo.strHrmResult_Review = strResulReviewtList.join(";");
    }
    qDebug()<<Q_FUNC_INFO<<"MeltingInfo_Review: "<<m_sResultInfo.strMeltingInfo_Review << "MeltingResult_Review: " << m_sResultInfo.strHrmResult_Review;

}
void CManualHrmReviewWidget::GetManualReviewParam(SResultInfoStruct &sResultInfo, SLotInfoStruct &sLotInfo)
{
    sResultInfo = m_sResultInfo;
    sLotInfo = m_sLotInfo;
}

void CManualHrmReviewWidget::_SlotOnHoleNameChanged(int index)
{
    //"-1;-1;,,,;"
    // 这里可以读取CtInfoReview ,如果不为空说明这个是读取过得
    // 就可以解析值，填写值；
    _ClearData();
    m_index = index;
    _SetParamValue(m_index);
}

void CManualHrmReviewWidget::_ClearData()
{
    m_pThreshouldEdit->clearData();
    m_pTmFirstValueEdit->clear();
    m_pRmFirstValueEdit->clear();
    m_pTmSecondValueEdit->clear();
    m_pRmSecondValueEdit->clear();
    m_pRangeEdit->clear();
    m_pResultEdit->clear();
}



void CManualHrmReviewWidget::_SetParamValue(int index)
{
    stHrmReviewParam stParam;
    for(const auto& item : m_ReviewParamList)
    {
        if(item.m_index == index)
        {
            stParam = item;
        }
    }
    QString strInfo;
    bool bShow = false;
    if(stParam.m_bReview)
    {
        strInfo = stParam.m_strMeltingInfo_Review;
        m_pResultEdit->setText(_GetShowResult(stParam.m_result));
        bShow = true;
    }
    else
    {
        strInfo = stParam.m_strMeltingInfo;
        bShow = false;
    }
    m_pConfirmBtn->setVisible(bShow);
    qDebug()<<Q_FUNC_INFO<<"changed combobox index:  "<<index << "strInfo: " << strInfo; //阈值配置
    QStringList strTmList,strRmList,strYmList;
    QString strTm1Text,strTm2Text,strRm1Text,strRm2Text,strThreashould;
    _GetTmFormMeltInfo(strInfo,strTmList,strRmList,strYmList,strThreashould);
    for(int i = 0 ; i < strTmList.size();i++)
    {
        switch (i) {
        case 0:
        {
            strTm1Text = strTmList.at(i);
            break;
        }
        case 1:
        {
            strTm2Text = strTmList.at(i);
            break;
        }
        default:
            break;
        }
    }
    for(int i = 0 ; i < strRmList.size();i++)
    {
        switch (i) {
        case 0:
        {
            strRm1Text = strRmList.at(i);
            break;
        }
        case 1:
        {
            strRm2Text = strRmList.at(i);
            break;
        }
        default:
            break;
        }
    }
    static auto formatFloatString = [](const QString& strFloat) {
        double number = strFloat.toDouble();
        return QString::number(number, 'f', 2);
    };
    m_pThreshouldEdit->setText(strThreashould);
    m_pRangeEdit->setText(QString("%1±%2").arg(stParam.m_fWildTmValue).arg(stParam.m_fRange));
    m_pTmFirstValueEdit->setText(formatFloatString(strTm1Text));
    m_pRmFirstValueEdit->setText(formatFloatString(strRm1Text));
    m_pTmSecondValueEdit->setText(formatFloatString(strTm2Text));
    m_pRmSecondValueEdit->setText(formatFloatString(strRm2Text));
}

void CManualHrmReviewWidget::_SetComboBoxValue()
{
    m_pHoleNameCombo->clear();
    int index = 0;
    for (auto& item : m_ReviewParamList)
    {
        if(!item.m_bNull&&!item.m_bControl)
        {
            item.m_index = index;
            m_pHoleNameCombo->addItem(item.m_strName);
            index++;
        }
        else
        {
         item.m_index = -1;
        }
    }
    // 插入内控靶标
    for (auto& item : m_ReviewParamList)
    {
        if(!item.m_bNull&&item.m_bControl)
        {
            item.m_index = index;
            m_pHoleNameCombo->addItem(item.m_strName);
            index++;
        }
    }
}

void CManualHrmReviewWidget::_SlotAutoThreshouldBtn()
{
#if 1
    stHrmReviewParam sReviewParam;
    for(const auto& item : m_ReviewParamList)
    {
        if(item.m_index == m_index)
        {
            sReviewParam  = item;
            break;
        }

    }
    QString strInfo = sReviewParam.m_strMeltingInfo;
    QStringList strInfoList = strInfo.split(",");
    qDebug()<<Q_FUNC_INFO<<"auto Threshould strInfo: " <<strInfo;
    if(strInfoList.size() >= 4)
    {
        m_pThreshouldEdit->setText(strInfoList.at(2));
    }
#endif
}


void CManualHrmReviewWidget::_SlotManualCalcBtn()
{
    m_strResult.clear();
    m_strMeltingInfo.clear();
    stHrmReviewParam sReviewParam;    
    int index = 0;
    for(int i = 0 ; i < m_ReviewParamList.size(); i++)
    {
        if(m_ReviewParamList.at(i).m_index == m_index)
        {
            sReviewParam  = m_ReviewParamList.at(i);
            index = i;
            break;
        }
    }

    QString strTestTime = m_sCardInfo.strTestTime;
    strTestTime.remove(":").remove("-").remove(" ");

    QString strHole = QString::number(index/4);
    QStringList strColorList = gk_strColorNameList;
    QString strFLID = "";
    QList<qreal> dAllYDataList; //所有Y的值,用以求最大最小值确定坐标范围
    strFLID = m_sResultInfo.strCardID + "+" + strTestTime;

    // 历史温度数据
    QList<double> dTempList;
    CHistoryDB::GetInstance()->GetMeltingDoubleData(strFLID, dTempList);
    // 溶解数据    
    strFLID.append("_" + strHole + "-" + strColorList.at(index%4));
    QList<double> dFLList;
    CHistoryDB::GetInstance()->GetMeltingDoubleData(strFLID, dFLList);
    m_MeltingCalc.meltingCalc(dTempList,dFLList,QString("%1;%2").arg(m_pThreshouldEdit->getText()).arg(2));//这个配置参数得从数据库中获

    QString strMeltingInfo = m_MeltingCalc.getTmRmStrResult();
    QStringList strTmList,strRmList,strYmList;
    QString strTm1Text,strTm2Text,strRm1Text,strRm2Text,strThreashould;
    _GetTmFormMeltInfo(strMeltingInfo,strTmList,strRmList,strYmList,strThreashould);
    for(int i = 0 ; i < strTmList.size();i++)
    {
        switch (i) {
        case 0:
        {
            strTm1Text = strTmList.at(i);
            break;
        }
        case 1:
        {
            strTm2Text = strTmList.at(i);
            break;
        }
        default:
            break;
        }
    }
    for(int i = 0 ; i < strRmList.size();i++)
    {
        switch (i) {
        case 0:
        {
            strRm1Text = strRmList.at(i);
            break;
        }
        case 1:
        {
            strRm2Text = strRmList.at(i);
            break;
        }
        default:
            break;
        }
    }
    static auto formatFloatString = [](const QString& strFloat) {
        double number = strFloat.toDouble();
        return QString::number(number, 'f', 2);
    };
    m_pThreshouldEdit->setText(strThreashould);
    m_pTmFirstValueEdit->setText(formatFloatString(strTm1Text));
    m_pRmFirstValueEdit->setText(formatFloatString(strRm1Text));
    m_pTmSecondValueEdit->setText(formatFloatString(strTm2Text));
    m_pRmSecondValueEdit->setText(formatFloatString(strRm2Text));

    m_strMeltingInfo = strMeltingInfo;


    // 只用更新结果，曲线更新不了；
    // 因为没有基线 什么的调整；
    // 这里用到CtInfo，MeltingInfo； 主要取MeltingInfo数据；
    // CtInfo不改变直接用result中的；

    QStringList strMeltingInfoList;
    // 少靶标的特殊情况 逻辑无问题
    for(int i = 0 ; i < m_ReviewParamList.size(); i++)
    {
        QString strMeltingInfo;
        if(m_ReviewParamList.at(i).m_index == m_index)
        {
            strMeltingInfo = m_strMeltingInfo;
        }
        else
        {
            if(m_ReviewParamList.at(i).m_bReview)
            {
                strMeltingInfo = m_ReviewParamList.at(i).m_strMeltingInfo_Review;
            }
            else
            {
                strMeltingInfo = m_ReviewParamList.at(i).m_strMeltingInfo;
            }
        }
        strMeltingInfoList.push_back(strMeltingInfo);
    }
    QStringList strHoleNameList = m_sLotInfo.strCurveName.split(";");
    QStringList strTmVildList = m_sLotInfo.strWildTypeTmValue.split(";");
    QStringList strTmRangeList = m_sLotInfo.strTempRangeValue.split(";");
    QStringList strCtInfoList = m_sResultInfo.strCTInfo.split(";");
    QStringList strResultList = m_sResultInfo.strResult.split(";");

    m_strResult = GetMeltingResult(m_sLotInfo.strProjectShowName,strHoleNameList,strCtInfoList
                                   ,strResultList,strMeltingInfoList,strTmVildList,strTmRangeList);

    m_pResultEdit->setText(_GetShowResult(m_strResult));
    m_pConfirmBtn->setVisible(true);
    m_pRangeEdit->setText(QString("%1±%2").arg(sReviewParam.m_fWildTmValue).arg(sReviewParam.m_fRange));
    qDebug()<<Q_FUNC_INFO<<"calc strConfig: " <<QString("%1;%2").arg(m_pThreshouldEdit->getText()).arg(2) << " resultCtInfo: "<< m_strMeltingInfo;
}


QString CManualHrmReviewWidget::_GetShowResult(const QString &strResult)
{

    if("P" == strResult)
    {
        return m_strPositive;
    }
    else if("N" == strResult)
    {
        return m_strNegative;
    }
    else if("E" == strResult)
    {
        return m_strError;
    }
    else
    {
        return  m_strNull;
    }
}

void CManualHrmReviewWidget::_SlotCancelBtn()
{
    m_eStandardBtn = QMessageBox::No;
    if(m_qEventLoop.isRunning())
        m_qEventLoop.quit();
    _SetParamResultValue();
    this->close();
}

void CManualHrmReviewWidget::_SlotConfirmBtn()
{
    stHrmReviewParam stHrmReviewParamTemp;
    for(int i = 0 ; i < m_ReviewParamList.size(); i++)
    {
        if(m_ReviewParamList.at(i).m_index == m_index)
        {
            stHrmReviewParam& sReviewParam  = m_ReviewParamList[i];
            sReviewParam.m_strMeltingInfo_Review = m_strMeltingInfo;
            sReviewParam.m_result = m_strResult;
            sReviewParam.m_bReview = true;
            stHrmReviewParamTemp = sReviewParam;
            break;
        }
    }
    emit SignalReviewConfirm(stHrmReviewParamTemp);
    qDebug()<<Q_FUNC_INFO<<"save resultCtInfo: "<< stHrmReviewParamTemp.m_strMeltingInfo_Review << " result: " << stHrmReviewParamTemp.m_result;
}

void CManualHrmReviewWidget::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, 30));
    QWidget::paintEvent(pEvent);
}

void CManualHrmReviewWidget::showEvent(QShowEvent *pEvent)
{
    QWidget::showEvent(pEvent);
}

void CManualHrmReviewWidget::_InitWidget()
{
    int iBackWidth = 580;
    if(eLanguage_Italian == gk_iLanguage || eLanguage_Spanish == gk_iLanguage)
        iBackWidth = 625;
    if(eLanguage_German == gk_iLanguage)
        iBackWidth = 600;

    //标题
    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(iBackWidth, 480);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");
    m_pCHLabelTitleWidget = new CHLabelTitleWidget(tr("人工审核"));
    m_pHoleNameLabel = new QLabel(tr("靶标:"));
    //m_pHoleNameLabel->setFixedSize(80, 56);
    m_pHoleNameLabel->setFixedHeight(56);

    QStringList strList =  {};
    m_pHoleNameCombo = new QComboBox;
    m_pHoleNameCombo->setFixedSize(250, 56);
    m_pHoleNameCombo->setView(new QListView);
    m_pHoleNameCombo->addItems(strList);
    connect(m_pHoleNameCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &CManualHrmReviewWidget::_SlotOnHoleNameChanged);

    // 阈值
    m_pThreshouldLabel = new QLabel(tr("峰高阈值:"));
    //m_pThreshouldLabel->setFixedSize(110, 56);
    m_pThreshouldLabel->setFixedHeight(56);

    m_pThreshouldEdit = new CLineEditSpinBox;
    m_pThreshouldEdit->setFixedSize(250, 56);

    m_pAutoThreshouldBtn = new QPushButton(tr("默认阈值"));
    m_pAutoThreshouldBtn->setFixedSize(130, 56);
    connect(m_pAutoThreshouldBtn,&QPushButton::clicked,this,&CManualHrmReviewWidget::_SlotAutoThreshouldBtn);

    // Tm  Rm 值
    m_pTmRmFirstLabel = new QLabel(tr("Tm1/Rm1:"));
    //m_pTmRmFirstLabel->setFixedSize(110, 56);
    m_pTmRmFirstLabel->setFixedHeight(56);

    m_pTmRmSecondLabel = new QLabel(tr("Tm2/Rm2:"));
    //m_pTmRmSecondLabel->setFixedSize(110, 56);
    m_pTmRmSecondLabel->setFixedHeight(56);

    m_pTmFirstValueEdit = new CLineEdit;
    m_pTmFirstValueEdit->setFixedSize(120, 56);
    m_pTmFirstValueEdit->setReadOnly(true);

    m_pRmFirstValueEdit = new CLineEdit;
    m_pRmFirstValueEdit->setFixedSize(120, 56);
    m_pRmFirstValueEdit->setReadOnly(true);

    m_pTmSecondValueEdit = new CLineEdit;
    m_pTmSecondValueEdit->setFixedSize(120, 56);
    m_pTmSecondValueEdit->setReadOnly(true);

    m_pRmSecondValueEdit = new CLineEdit;
    m_pRmSecondValueEdit->setFixedSize(120, 56);
    m_pRmSecondValueEdit->setReadOnly(true);
    // Tm  Rm end

    m_pManualCalcBtn = new QPushButton(tr("开始分析"));
    m_pManualCalcBtn->setFixedSize(130, 56);
    connect(m_pManualCalcBtn,&QPushButton::clicked,this,&CManualHrmReviewWidget::_SlotManualCalcBtn);

    m_pRangeLabel = new QLabel(tr("阈值区间:"));
    //m_pRangeLabel->setFixedSize(110, 56);
    m_pRangeLabel->setFixedHeight(56);

    m_pRangeEdit =  new CLineEdit;
    m_pRangeEdit->setFixedSize(130, 56);
    m_pRangeEdit->setReadOnly(true);

    m_pResultLabel = new QLabel(tr("结果:"));
    //m_pResultLabel->setFixedSize(80, 56);
    m_pResultLabel->setFixedHeight(56);

    m_pResultEdit =  new CLineEdit;
    m_pResultEdit->setFixedSize(130, 56);
    m_pResultEdit->setReadOnly(true);

    m_pCancelBtn = new QPushButton(tr("退出"));
    m_pCancelBtn->setFixedSize(150, 56);
    m_pCancelBtn->setObjectName("CancelBtn");
    connect(m_pCancelBtn, &QPushButton::clicked, this, &CManualHrmReviewWidget::_SlotCancelBtn);

    m_pConfirmBtn = new QPushButton(tr("应用"));
    m_pConfirmBtn->setFixedSize(150, 56);
    connect(m_pConfirmBtn, &QPushButton::clicked, this, &CManualHrmReviewWidget::_SlotConfirmBtn);

}

void CManualHrmReviewWidget::_InitLayout()
{
    QGridLayout *pGridLayout = new QGridLayout;
    pGridLayout->setMargin(0);
    pGridLayout->setSpacing(20);
    pGridLayout->addWidget(m_pHoleNameLabel, 0, 0);
    pGridLayout->addWidget(m_pHoleNameCombo, 0, 1, 1, 2);

    pGridLayout->addWidget(m_pThreshouldLabel, 1, 0);
    pGridLayout->addWidget(m_pThreshouldEdit, 1, 1,1,2);
    pGridLayout->addWidget(m_pAutoThreshouldBtn, 1, 3);


    pGridLayout->addWidget(m_pTmRmFirstLabel, 2, 0);
    pGridLayout->addWidget(m_pTmFirstValueEdit, 2, 1);
    pGridLayout->addWidget(m_pRmFirstValueEdit, 2, 2);

    pGridLayout->addWidget(m_pTmRmSecondLabel, 3, 0);
    pGridLayout->addWidget(m_pTmSecondValueEdit, 3, 1);
    pGridLayout->addWidget(m_pRmSecondValueEdit, 3, 2);
    pGridLayout->addWidget(m_pManualCalcBtn, 2, 3);

    pGridLayout->addWidget(m_pRangeLabel, 4, 0);
    pGridLayout->addWidget(m_pRangeEdit, 4, 1);
    pGridLayout->addWidget(m_pResultLabel, 4, 2);
    pGridLayout->addWidget(m_pResultEdit, 4, 3);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(60);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pCancelBtn);
    pBtnLayout->addWidget(m_pConfirmBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pBackLayout = new QVBoxLayout;
    pBackLayout->setContentsMargins(24, 12, 24, 24);
    pBackLayout->setSpacing(40);
    pBackLayout->addWidget(m_pCHLabelTitleWidget, 0, Qt::AlignLeft);
    pBackLayout->addLayout(pGridLayout);
    pBackLayout->addStretch(1);
    pBackLayout->addLayout(pBtnLayout);
    m_pBackgroundLabel->setLayout(pBackLayout);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setContentsMargins(24, 24, 24, 24);
    pLayout->addItem(new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum));
    pLayout->addWidget(m_pBackgroundLabel, 0, Qt::AlignRight|Qt::AlignBottom);
    this->setLayout(pLayout);
}

