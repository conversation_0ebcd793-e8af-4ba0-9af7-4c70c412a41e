#include "CMotorConfig.h"
#include <QBoxLayout>
#include <QGridLayout>
#include "CMessageBox.h"
#include "DBControl/CMotorInfoDB.h"

CMotorConfig::CMotorConfig(QWidget *parent) : QWidget(parent)
{
    Register2Map(Method_GAP);
    m_strConfigIDList <<  "1" << "3" << "23" << "24" <<
                          "27" << "28" << "29" << "34" <<
                          "162" << "163" << "164" << "165"<<
                          "166" << "167" << "184" << "186"<<
                          "187" << "188" << "191" << "192"<<
                          "204";
    m_strConsumList = CMotorInfoDB::GetInstance()->getAllCompensateType();

    _InitWidget();

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalSoftTypeChanged, this, &CMotorConfig::SlotSoftTypeChanged);
}

CMotorConfig::~CMotorConfig()
{
    UnRegister2Map(Method_GAP);
}

void CMotorConfig::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    qDebug()<<"电机配置文本:"<<iMachineID + 1<<iMethodID<<iResult;
    if(Method_GAP == iMethodID)
    {
        QVariantList qVarList = qVarData.toList();
        if(qVarList.size() >= 2)
        {
            m_pMotorComboBox->SetCurrentIndex(qVarList.at(0).toInt());
            m_pTextLineEdit->setText(qVarList.at(1).toString());
        }
    }
}

void CMotorConfig::SlotSoftTypeChanged(int iSoftType)
{
    Q_UNUSED(iSoftType);
    m_pMotorComboBox->SetComboBoxList(CPublicConfig::GetInstance()->GetMotorNameList());
}

void CMotorConfig::showEvent(QShowEvent *pEvent)
{
    QWidget::showEvent(pEvent);
}

void CMotorConfig::_SlotReadText()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    QVariantList qVarList = {m_pMotorComboBox->GetCurrentIndex(),
                             m_strConfigIDList[m_pTextComboBox->GetCurrentIndex()].toInt()};
    QString strCmd = GetJsonCmdString(Method_GAP, qVarList);
    qDebug()<<"读取电机配置文件:"<<strCmd;
    SendJsonCmd(iMachineID, Method_GAP, strCmd);
}

void CMotorConfig::_SlotSetText()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    QVariantList qVarList = {m_pMotorComboBox->GetCurrentIndex(),
                             m_strConfigIDList[m_pTextComboBox->GetCurrentIndex()].toInt(),
                            m_pTextLineEdit->text().toInt()};
    QString strCmd = GetJsonCmdString(Method_SAP, qVarList);
    qDebug()<<"设置电机配置文件:"<<strCmd;
    SendJsonCmd(iMachineID, Method_SAP, strCmd);
}

void CMotorConfig::_SlotAddConsum()
{
    QString strData = m_pConsumComboBox->GetCurrentText();
    if(strData.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("内容为空,添加失败"));
        return;
    }

    if(m_strConsumList.contains(strData))
    {
        ShowInformation(this, m_strTipsText, tr("重复的耗材名称,添加失败"));
        return;
    }

    m_strConsumList.push_back(strData);
    m_pConsumComboBox->SetComboBoxList(m_strConsumList);
}

void CMotorConfig::_SlotDelConsum()
{
    QString strData = m_pConsumComboBox->GetCurrentText();
    if(strData.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("耗材信息为空"));
        return;
    }

    int iBtnType =ShowQuestion(this, m_strTipsText, tr("确认删除吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    CMotorInfoDB::GetInstance()->deleteFromType(strData);
    m_strConsumList.removeOne(strData);
    m_pConsumComboBox->SetComboBoxList(m_strConsumList);
    m_pConsumComboBox->SetCurrentIndex(0);
    ShowSuccess(this, m_strTipsText, tr("删除成功"));
}

void CMotorConfig::_SlotMotor2DB()
{
    QString strData = m_pConsumComboBox->GetCurrentText();
    if(strData.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("耗材信息为空"));
        return;
    }

    int iBtnType =ShowQuestion(this, m_strTipsText, tr("确定保存当前电机参数至%1吗").arg(strData));
    if(QMessageBox::Yes != iBtnType)
        return;
}

void CMotorConfig::_SlotDB2Motor()
{

}

void CMotorConfig::_InitWidget()
{
    m_pMotorComboBox = new CLabelComboBox(tr("电机名称:"), CPublicConfig::GetInstance()->GetMotorNameList());
    m_pMotorComboBox->SetComboBoxFixedSize(200, 50);

    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(200, 50);

    QStringList strTextList;
    strTextList << "Actual Pos" << "Actual Speed" << "THIGH" << "VDCMIN"
                << "VHIGCHM" << "VHIGHFS" << "Measure SPD" << "REFR"
                << "TBL" << "CHM" << "DISFDCC" << "HEND"
                << "HSTRT" << "TOFF" << "Random TOFF" << "TPWMTHRS"
                << "PWM GRAD" << "PWM OFS" << "PWM FREQ" << "PWM AUTOSCALE"
                << "FREE WHEEL";
    m_pTextComboBox = new CLabelComboBox(tr("配置文本:"), strTextList);
    m_pTextComboBox->SetComboBoxFixedSize(200, 50);

    m_pTextLineEdit = new CLineEdit;
    m_pTextLineEdit->setFixedSize(100, 50);

    m_pReadTextBtn = new QPushButton(tr("读取"));
    m_pReadTextBtn->setFixedSize(100, 50);
    connect(m_pReadTextBtn, &QPushButton::clicked, this, &CMotorConfig::_SlotReadText);

    m_pSetTextBtn = new QPushButton(tr("设置"));
    m_pSetTextBtn->setFixedSize(100, 50);
    connect(m_pSetTextBtn, &QPushButton::clicked, this, &CMotorConfig::_SlotSetText);

    m_pConsumComboBox = new CLabelComboBox(tr("配置耗材:"));
    m_pConsumComboBox->SetComboBoxFixedSize(200, 50);

    m_pAddConsumBtn = new QPushButton(tr("添加"));
    m_pAddConsumBtn->setFixedSize(100, 50);
    connect(m_pAddConsumBtn, &QPushButton::clicked, this, &CMotorConfig::_SlotAddConsum);

    m_pDelConsumBtn = new QPushButton(tr("删除"));
    m_pDelConsumBtn->setFixedSize(100, 50);
    connect(m_pDelConsumBtn, &QPushButton::clicked, this, &CMotorConfig::_SlotDelConsum);

    m_pMotor2DBBtn = new QPushButton(tr("电机存储至DB"));
    m_pMotor2DBBtn->setFixedSize(150, 50);
    connect(m_pMotor2DBBtn, &QPushButton::clicked, this, &CMotorConfig::_SlotMotor2DB);

    m_pDB2MotorBtn = new QPushButton(tr("DB应用至电机"));
    m_pDB2MotorBtn->setFixedSize(150, 50);
    connect(m_pDB2MotorBtn, &QPushButton::clicked, this, &CMotorConfig::_SlotDB2Motor);

    QHBoxLayout *pLayout1 = new QHBoxLayout;
    pLayout1->setMargin(0);
    pLayout1->setSpacing(20);
    pLayout1->addWidget(m_pMotorComboBox);
    pLayout1->addWidget(m_pMachineComboBox);
    pLayout1->addStretch(1);

    QHBoxLayout *pLayout2 = new QHBoxLayout;
    pLayout2->setMargin(0);
    pLayout2->setSpacing(20);
    pLayout2->addWidget(m_pTextComboBox);
    pLayout2->addWidget(m_pTextLineEdit);
    pLayout2->addWidget(m_pReadTextBtn);
    pLayout2->addWidget(m_pSetTextBtn);
    pLayout2->addStretch(1);

    QHBoxLayout *pLayout3 = new QHBoxLayout;
    pLayout3->setMargin(0);
    pLayout3->setSpacing(20);
    pLayout3->addWidget(m_pConsumComboBox);
    pLayout3->addWidget(m_pAddConsumBtn);
    pLayout3->addWidget(m_pDelConsumBtn);
    pLayout3->addWidget(m_pMotor2DBBtn);
    pLayout3->addWidget(m_pDB2MotorBtn);
    pLayout3->addStretch(1);

    QVBoxLayout *pMainLayout = new QVBoxLayout;
    pMainLayout->setContentsMargins(20, 0, 20, 0);
    pMainLayout->setSpacing(20);
    pMainLayout->addLayout(pLayout1);
    pMainLayout->addLayout(pLayout2);
    pMainLayout->addLayout(pLayout3);
    pMainLayout->addStretch(1);
    this->setLayout(pMainLayout);
}
