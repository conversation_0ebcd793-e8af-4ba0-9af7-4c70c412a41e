#ifndef CLINEEDITSPINBOX_H
#define CLINEEDITSPINBOX_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: chenhao
  * Date: 2024-9-25
  * Description: QLineEdit-QSPin组合控件
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <CLineEdit.h>
#include <QPushButton>

//setLayoutDirection(Qt::RightToLeft);

class CLineEditSpinBox : public QWidget
{
    Q_OBJECT
public:
    CLineEditSpinBox(QWidget *parent = nullptr);
    ~CLineEditSpinBox();
    QString getText();
    void setText(const QString& strText);
    void clearData();

private slots:
    void _SlotSpinUpBtn();
    void _SlotSpinDownBtn();

private:
    void _InitWidget();

private:
    // spin 按钮
    float m_fStepValue{0.1};
    CLineEdit* m_pEdit;
    QPushButton *m_pSpinUp, *m_pSpinDown;
};

#endif // CLABELCHECKBOX_H
