#ifndef CLOCALNETWORKHANDLE_H
#define CLOCALNETWORKHANDLE_H

#include <QWidget>
#include <QGroupBox>
#include <QRadioButton>
#include <QButtonGroup>
#include <QPushButton>
#include <QProcess>
#include <QMap>

struct IPInfoStruct
{

    IPInfoStruct()
    {
        Clear();
    }
    void Clear()
    {
        bAuto = false;
        strIP.clear();
        strMask.clear();
        strGeteway.clear();
    }
    bool bAuto;
    QString strIP;
    QString strMask;
    QString strGeteway;
};

class CLocalNetworkHandle : public QObject
{
    Q_OBJECT
public:
    explicit CLocalNetworkHandle(QObject *parent = nullptr);
    void _ReadConfigFile(bool bAutoIp,int LocalOrwifi,const QString& strIp,const QString& strNetMask,const QString& strGetWay);
    IPInfoStruct GetIPInfoStruct();
    void SetIPInfoStruct(IPInfoStruct& sIpInfo);
    void SlotSetWifiConnectState(bool bWifiConnect);
    void SlotWiFiOpenState(bool bOpenState);
    void ManualConnect();
    void AutoConnect();
    void SaveInfo(const IPInfoStruct& sIpInfo);

signals:
    void SignalChangeIpInfo(const IPInfoStruct& ipInfo);

private:    
    void _CheckEth0NetworkState();
    void _SlotReGetIP();
    void _ReGetNetworkInfoAndUpdateUi();
    void _GetNetworkInfo();
    void _GetGateway();
    bool GetEth0NetworkState();

private:

    typedef struct _SNetworkInfoStruct
    {
        QString strInterfaceName;
        QString strIP;
        QString strNetmask;
        QString strGateway;
        QString strHardwareAddress;
        QString strBroadcast;
    }SNetworkInfoStruct;



    bool m_bAutoConnect;
    bool m_bWifiOpenState;
    QString m_strIPV4Name;

    QTimer* m_pReGetIPTimer;

    IPInfoStruct m_sIPInfoStruct;
    QMap<QString,SNetworkInfoStruct> m_networkInfoStructMap;

    bool m_bConnect = false; // 有线是否链接
};

#endif // CLOCALNETWORKHANDLE_H
