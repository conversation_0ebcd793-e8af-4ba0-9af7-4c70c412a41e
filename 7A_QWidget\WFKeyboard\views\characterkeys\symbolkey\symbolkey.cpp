#include "symbolkey.h"
#include "ui_symbolkey.h"

#include <cassert>
#include <algorithm>

class Symbolkey::PrivateData
{
public:
    PrivateData(Symbolkey *p)
        : parent(p)
    {
        chsSymbol = QString("·~！@#￥%…&*（）-=—+｛｝【】、|：；“‘，。、《》？℃");
        engSymbol = QString("`~!@#$%^&*()_+-={}[]|\\:;\"',./<>?αаβμºδεζηθξσλφψ∞∑Ω");
        mathSymbol = QString("+-×÷=∈∏∑∕√∝∞∟∠∣∥∧∨∩∪∫∮∴∵∶∷∽≈≌≒≠≡≤≥≦≧≮≯⊕⊙⊥⊿⌒①②③④⑤⑥⑦⑧⑨⑩");
    }

    void Init();

    void UpdateUi();

    void ActiveSymbol(const QString *kpSymbol);

public:
    Ui::Symbolkey *ui;

    Symbolkey       *parent;
    const QString   *curSymbol;

    QString engSymbol;
    QString chsSymbol;
    QString mathSymbol;

    int pageNum;
    std::vector<QToolButton *> pageVector;

};

void Symbolkey::PrivateData::Init()
{
    pageVector.push_back(ui->toolButton_00);
    pageVector.push_back(ui->toolButton_01);
    pageVector.push_back(ui->toolButton_02);
    pageVector.push_back(ui->toolButton_03);
    pageVector.push_back(ui->toolButton_04);
    pageVector.push_back(ui->toolButton_05);
    pageVector.push_back(ui->toolButton_06);
    pageVector.push_back(ui->toolButton_07);
    pageVector.push_back(ui->toolButton_08);
    pageVector.push_back(ui->toolButton_09);
    pageVector.push_back(ui->toolButton_10);
    pageVector.push_back(ui->toolButton_11);
    pageVector.push_back(ui->toolButton_12);
    pageVector.push_back(ui->toolButton_13);
    pageVector.push_back(ui->toolButton_14);
    pageVector.push_back(ui->toolButton_15);
    pageVector.push_back(ui->toolButton_16);
    pageVector.push_back(ui->toolButton_17);

    for (std::vector<QToolButton *>::iterator it = pageVector.begin(); it != pageVector.end(); it++)
    {
        QToolButton *btn = *it;
        parent->ConnectMapper(btn, true);
    }
    parent->ConnectMapper(ui->toolButton_Space, false, " ");

    parent->connect(ui->toolButton_Return, SIGNAL(clicked()), parent, SLOT(SlotReturnBtnClicked()));
    parent->connect(ui->toolButton_Hide, SIGNAL(clicked()), (QObject*)parent->GetKeyBoard(),SLOT(SlotEscBtnClicked()));
    parent->connect(ui->toolButton_BackSpace, SIGNAL(clicked()), parent, SLOT(SlotBackSpaceBtnClicked()));
    parent->connect(ui->toolButton_PageUp, SIGNAL(clicked()), parent, SLOT(SlotPrevPageBtnClicked()));
    parent->connect(ui->toolButton_PageDown, SIGNAL(clicked()), parent, SLOT(SlotNextPageBtnClicked()));
    parent->connect(ui->toolButton_CHS, SIGNAL(clicked()), parent, SLOT(SlotCHSBtnClicked()));
    parent->connect(ui->toolButton_ENG, SIGNAL(clicked()), parent, SLOT(SlotENGBtnClicked()));
    parent->connect(ui->toolButton_Math, SIGNAL(clicked()), parent, SLOT(SLotMathBtnClicked()));
}

void Symbolkey::PrivateData::UpdateUi()
{
    int offset = pageNum * pageVector.size();
    QString::const_iterator it = curSymbol->begin() + offset;

    size_t i = 0;

    for (; i < pageVector.size() && it != curSymbol->end(); it++, i++)
    {
        pageVector[i]->setEnabled(true);
        if (*it == '&')
            pageVector[i]->setText(QString("&") + *it);
        else
            pageVector[i]->setText(*it);

        parent->SetMapping(pageVector[i], *it);
    }

    for (; i < pageVector.size(); i++)
    {
        pageVector[i]->setEnabled(false);
        pageVector[i]->setText("");
        parent->SetMapping(pageVector[i]);
    }

    int pageCount = (curSymbol->size() + pageVector.size() - 1) / pageVector.size();
    ui->label_CurPage->setText(QString().sprintf("%d/%d", pageNum + 1, pageCount));

    ui->toolButton_PageDown->setEnabled(it != curSymbol->end());
    ui->toolButton_PageUp->setEnabled(pageNum > 0);

    ui->toolButton_CHS->setChecked(curSymbol == &chsSymbol);
    ui->toolButton_ENG->setChecked(curSymbol == &engSymbol);
    ui->toolButton_Math->setChecked(curSymbol == &mathSymbol);
}

void Symbolkey::PrivateData::ActiveSymbol(const QString *kpSymbol)
{
    if (curSymbol == kpSymbol)
        return ;

    curSymbol = kpSymbol;
    pageNum = 0;

    UpdateUi();
}

Symbolkey::Symbolkey(KeyBoard *parent, QStackedWidget *stackedWidget)
    : CharacterBaseKey(parent, stackedWidget)
    , md(new PrivateData(this))
{
    md->ui = new Ui::Symbolkey;
    md->ui->setupUi(this);
    md->Init();
}

Symbolkey::~Symbolkey()
{
    delete md;
}

void Symbolkey::Active(SymbolTypeEnum eSymbolTypeEnum)
{
    switch(eSymbolTypeEnum)
    {
    case CHS:
        SlotCHSBtnClicked();
        break;
    case ENG:
        SlotENGBtnClicked();
        break;
    case MATH:
        SLotMathBtnClicked();
        break;
    default:
        assert(0);
    };
}

void Symbolkey::changeEvent(QEvent *event)
{
    if (event->type() == QEvent::LanguageChange) {
        md->ui->retranslateUi(this);
    }

    QWidget::changeEvent(event);
}

void Symbolkey::SlotPrevPageBtnClicked()
{
    --md->pageNum;
    md->UpdateUi();
}

void Symbolkey::SlotNextPageBtnClicked()
{
    ++md->pageNum;
    md->UpdateUi();
}

void Symbolkey::SlotCHSBtnClicked()
{
    md->ActiveSymbol(&md->chsSymbol);
}

void Symbolkey::SlotENGBtnClicked()
{
    md->ActiveSymbol(&md->engSymbol);
}

void Symbolkey::SLotMathBtnClicked()
{
    md->ActiveSymbol(&md->mathSymbol);
}

void Symbolkey::Translate(const QString &chs, const QString &eng, const QString &math, const QString &space)
{
    md->ui->toolButton_CHS->setText(chs);
    md->ui->toolButton_ENG->setText(eng);
    md->ui->toolButton_Math->setText(math);
    md->ui->toolButton_Space->setText(space);
}
