QLabel
{
   color: #6B788F;
   font-size: 20px;
   border: 0px solid red;
   font-family: "Source Han Sans CN";
}
QLabel#TitleTextLabel
{
   color: #353E4E;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
}
QLabel#TitleLabel
{
   font-weight: bold;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 rgba(89,147,255,0), stop: 1 rgba(89,147,255,100));
   border-top-left-radius: 24px;
   border-top-right-radius: 24px;
   border-bottom-left-radius: 0px;
   border-bottom-right-radius: 0px;
}
QLabel[status="fault"]#TitleLabel
{
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 rgba(232,108,121,0), stop: 1 rgba(232,108,121,100));
   border-top-left-radius: 24px;
   border-top-right-radius: 24px;
   border-bottom-left-radius: 0px;
   border-bottom-right-radius: 0px;
}
QLabel[status="test_fail"]#TitleLabel
{
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 rgba(232,108,121,0), stop: 1 rgba(232,108,121,100));
   border-top-left-radius: 24px;
   border-top-right-radius: 24px;
   border-bottom-left-radius: 0px;
   border-bottom-right-radius: 0px;
}
QLabel[status="disconnect"]#TitleLabel
{
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 rgba(153,153,153,0), stop: 1 rgba(153,153,153,100));
   border-top-left-radius: 24px;
   border-top-right-radius: 24px;
   border-bottom-left-radius: 0px;
   border-bottom-right-radius: 0px;
}

QLabel#IndexLabel
{
   color: #FFF;
   font-size: 24px;
   font-weight: bold;
   font-family: "Source Han Sans CN";
   background-color: #3D78E5;
   border-top-left-radius: 24px;
   border-top-right-radius: 0px;
   border-bottom-left-radius: 0px;
   border-bottom-right-radius: 24px;
}
QLabel[status="fault"]#IndexLabel
{ 
   color: #FFF;
   font-size: 24px;
   font-weight: bold;
   font-family: "Source Han Sans CN";
   background-color: #E86C79;
   border-top-left-radius: 24px;
   border-top-right-radius: 0px;
   border-bottom-left-radius: 0px;
   border-bottom-right-radius: 24px;
}
QLabel[status="test_fail"]#IndexLabel
{
   color: #FFF;
   font-size: 24px;
   font-weight: bold;
   font-family: "Source Han Sans CN";
   background-color: #E86C79;
   border-top-left-radius: 24px;
   border-top-right-radius: 0px;
   border-bottom-left-radius: 0px;
   border-bottom-right-radius: 24px;
}
QLabel[status="disconnect"]#IndexLabel
{
   color: #FFF;
   font-size: 24px;
   font-weight: bold;
   font-family: "Source Han Sans CN";
   background-color: #C2C2C2;
   border-top-left-radius: 24px;
   border-top-right-radius: 0px;
   border-bottom-left-radius: 0px;
   border-bottom-right-radius: 24px;
}

QLabel#StatusLabel
{   
   color: #3D78E5;
   font-size: 22px;
   font-family: "Source Han Sans CN";
}
QLabel[status="fault"]#StatusLabel
{
   color: #E86C79;
   font-size: 22px;
   font-family: "Source Han Sans CN";
}
QLabel[status="test_fail"]#StatusLabel
{
   color: #E86C79;
   font-size: 22px;
   font-family: "Source Han Sans CN";
}
QLabel[status="disconnect"]#StatusLabel
{
   color: #C2C2C2;
   font-size: 22px;
   font-family: "Source Han Sans CN";
}

QLabel#TimeLabel
{  
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
}
QLabel[status="fault"]#TimeLabel
{
   color: #E86C79;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
}
QLabel[status="test_fail"]#TimeLabel
{
   color: #E86C79;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
}
QLabel[status="disconnect"]#TimeLabel
{
   color: #C2C2C2;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
}

QLabel[status="idle"]#ActIconLabel
{
   image: url(:/image/ico/home/<USER>/create_test.png);
}
QLabel[status="testing"]#ActIconLabel
{
   image: url(:/image/ico/home/<USER>/stop_test.png);
}
QLabel[status="test_stopped"]#ActIconLabel
{
   image: url(:/image/ico/home/<USER>/take_cardbox.png);
}
QLabel[status="test_stopping"]#ActIconLabel
{
   image: url(:/image/ico/home/<USER>/stop_test.png);
}
QLabel[status="test_done"]#ActIconLabel
{
   image: url(:/image/ico/home/<USER>/take_cardbox.png);
}
QLabel[status="test_fail"]#ActIconLabel
{
   image: url(:/image/ico/home/<USER>/take_cardbox.png);
}
QLabel[status="disconnect"]#ActIconLabel
{
   image: url(:/image/ico/home/<USER>/dev_fault.png);
}
QLabel[status="fault"]#ActIconLabel
{
   image: url(:/image/ico/home/<USER>/dev_fault.png);
}
QLabel[status="self_test"]#ActIconLabel
{
   image: url(:/image/ico/home/<USER>/dev_fault.png);
}
QLabel[status="processing"]#ActIconLabel
{
   image: url(:/image/ico/home/<USER>/stop_test.png);
}
QLabel[status="reset"]#ActIconLabel
{
   image: url(:/image/ico/home/<USER>/dev_fault.png);
}

QLabel#ActTextLabel
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family:"Source Han Sans CN";
}

QLabel#DevImageLabel
{
   border-radius: 24px;
   background-color: #EAEFFD;
}

QLabel#CalibrationLabel
{
   font-size: 26px;
}

QPushButton#ActBtn
{
   color: #FFF;
   font-size: 24px;
   font-family:"Source Han Sans CN";
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #8490FF, stop: 1 #3D78E5);
   border: 0px solid #0068b7;
   border-top-left-radius: 0px;
   border-top-right-radius: 0px;
   border-bottom-left-radius: 24px;
   border-bottom-right-radius: 24px;
}
QPushButton#ActBtn:pressed
{
   color: #FFF;
   font-size: 24px;
   font-family:"Source Han Sans CN";
   background-color: #3D78E5;
   border: 0px solid #0068b7;
   border-top-left-radius: 0px;
   border-top-right-radius: 0px;
   border-bottom-left-radius: 24px;
   border-bottom-right-radius: 24px;
}

QPushButton[status="fault"]#ActBtn
{
   color: #FFF;
   font-size: 24px;
   font-family:"Source Han Sans CN";
   background-color: #E86C79;
   border: 0px solid #0068b7;
   border-top-left-radius: 0px;
   border-top-right-radius: 0px;
   border-bottom-left-radius: 24px;
   border-bottom-right-radius: 24px;
}
QPushButton[status="test_fail"]#ActBtn
{
   color: #FFF;
   font-size: 24px;
   font-family:"Source Han Sans CN";
   background-color: #E86C79;
   border: 0px solid #0068b7;
   border-top-left-radius: 0px;
   border-top-right-radius: 0px;
   border-bottom-left-radius: 24px;
   border-bottom-right-radius: 24px;
}
QPushButton[status="disconnect"]#ActBtn
{
   color: #FFF;
   font-size: 24px;
   font-family:"Source Han Sans CN";
   background-color: #C2C2C2;
   border: 0px solid #0068b7;
   border-top-left-radius: 0px;
   border-top-right-radius: 0px;
   border-bottom-left-radius: 24px;
   border-bottom-right-radius: 24px;
}

QGroupBox#BackgroundGroup
{
   border-radius: 32px;
   border: 0px solid red;
   background-color: #FFF;
}

QGroupBox#ItemGroupBox
{
   border-radius: 24px;
   border: 0px solid red;
   background-color: #F4F7FE;
}

QGroupBox#PLCItemGroupBox
{
   border-radius: 24px;
   border: 0px solid #EAEFFD;
   background-color: #F4F7FE;
}

QGroupBox#DevGroupBox
{
   border-radius: 32px;
   border: 0px solid red;
   background-color: #FFF;
}

QGroupBox#PLCDevGroupBox
{
   border-radius: 32px;
   border: 0px solid red;
   background-color: #EAEFFD;
}
