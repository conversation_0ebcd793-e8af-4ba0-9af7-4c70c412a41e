#include "CQCPage.h"
#include <QPixmap>
#include <QBoxLayout>

#include "CRunTest.h"
#include "CHeartBeat.h"
#include "CMessageBox.h"
#include "CConfigJson.h"
#include "PublicConfig.h"
#include "PublicFunction.h"

CQCPage::CQCPage(QWidget *parent) : QWidget(parent)
{
    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(_CreateUpGroupBox());
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateDownGroupBox());
    this->setLayout(pLayout);

    LoadQSS(this, ":/qss/qss/QC.qss");
}

void CQCPage::hideEvent(QHideEvent *pEvent)
{
    for(int i=0; i<m_pDevGroupWidgetList.size(); i++)
        m_pDevGroupWidgetList.at(i)->SetGroupUnSelect();

    for(int i=0; i<m_pDevItemBtnList.size(); i++)
        m_pDevItemBtnList.at(i)->setEnabled(true);

    QWidget::hideEvent(pEvent);
}

void CQCPage::_SlotItemBtnClicked()
{
    CQCDevItemBtn *pItemBtn = dynamic_cast<CQCDevItemBtn *>(sender());
    if(nullptr == pItemBtn)
        return;

    int iMachineID = pItemBtn->GetMachineID();
    if(iMachineID < 0 || iMachineID >= m_pDevItemBtnList.size())
        return;

    for(int i=0; i<m_pDevItemBtnList.size(); i++)
    {
        if(iMachineID == m_pDevItemBtnList.at(i)->GetMachineID())
            m_pDevItemBtnList.at(i)->setEnabled(false);
        else
            m_pDevItemBtnList.at(i)->setEnabled(true);
    }
}

void CQCPage::_SlotGroupSelected()
{
    CQCDevGroupWidget *pDevGroupWidget = dynamic_cast<CQCDevGroupWidget *>(sender());
    if(nullptr == pDevGroupWidget)
        return;

    int iDev = pDevGroupWidget->property("dev").toInt();
    for(int i=0; i<m_pDevGroupWidgetList.size(); i++)
    {
        if(iDev != m_pDevGroupWidgetList.at(i)->property("dev").toInt())
        {
            m_pDevGroupWidgetList.at(i)->SetGroupUnSelect();
        }
    }
}

void CQCPage::_SlotPostiveBtn()
{
    int iMachineID = -1;
    if(!GetSelectedMachineID(iMachineID))
        return;
    qDebug()<<Q_FUNC_INFO<<QString("选择%1#阳性质控").arg(iMachineID + 1);
    emit SignalCreateQCTest(iMachineID,"PQC");
}

void CQCPage::_SlotNegativeBtn()
{
    int iMachineID = -1;
    if(!GetSelectedMachineID(iMachineID))
        return;
    qDebug()<<Q_FUNC_INFO<<QString("选择%1#阴性质控").arg(iMachineID + 1);
    emit SignalCreateQCTest(iMachineID,"NQC");
}

bool CQCPage::GetSelectedMachineID(int &iMachineID)
{
    int iSelectedID = -1;
    for(int i=0; i<m_pDevItemBtnList.size(); i++)
    {
        if(!m_pDevItemBtnList.at(i)->isEnabled())
        {
            iSelectedID = m_pDevItemBtnList.at(i)->GetMachineID();
            break;
        }
    }

    if(-1 == iSelectedID)
    {
        ShowInformation(this, tr("提示"), tr("请先选择设备"));
        return false;
    }
    if(eDeviceIdle != m_pDevItemBtnList.at(iSelectedID)->GetStatus())
    {
        ShowInformation(this, tr("提示"), tr("当前设备不是空闲状态，请选择其他设备"));
        return false;
    }
    if(CHeartBeat::GetInstance()->GetDeviceHearbeatStruct(iSelectedID)->bCardboxExist)
    {
        ShowInformation(this, tr("提示"), tr("请先取出当前设备中的试剂卡"));
        return false;
    }

    iMachineID = iSelectedID;

    return true;
}

QPushButton *CQCPage::_CreateActBtn(const QString &strIconPath, const QString &strBtnText)
{
    QLabel *pIconLabel = new QLabel;
    pIconLabel->setFixedSize(56, 56);
    pIconLabel->setPixmap(QPixmap(strIconPath));

    QLabel *pTextLabel = new QLabel(strBtnText);
    pTextLabel->setAlignment(Qt::AlignCenter);
    pTextLabel->setObjectName("BtnLabel");

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(pIconLabel, 0, Qt::AlignHCenter);
    pLayout->addSpacing(30);
    pLayout->addWidget(pTextLabel, 0, Qt::AlignHCenter);
    pLayout->addStretch(1);

    QPushButton *pBtn = new QPushButton;
    pBtn->setFixedSize(210, 230);
    pBtn->setLayout(pLayout);
    return pBtn;
}

QGroupBox *CQCPage::_CreateUpGroupBox()
{
    m_pIcon1Label = new QLabel("1");
    m_pIcon1Label->setFixedSize(50, 50);
    m_pIcon1Label->setObjectName("GroupIconLabel");
    m_pIcon1Label->setAlignment(Qt::AlignCenter);

    m_pTitle1Label = new QLabel(tr("请选择设备"));
    m_pTitle1Label->setFixedHeight(50);
    m_pTitle1Label->setObjectName("GroupTitleLabel");

    QHBoxLayout *pTitleLayout = new QHBoxLayout;
    pTitleLayout->setMargin(0);
    pTitleLayout->setSpacing(0);
    pTitleLayout->addSpacing(15);
    pTitleLayout->addWidget(m_pIcon1Label);
    pTitleLayout->addSpacing(10);
    pTitleLayout->addWidget(m_pTitle1Label);
    pTitleLayout->addStretch(1);

    m_iDevNum = 1;
    m_iItemNum = 8;
    CPublicConfig::GetInstance()->GetDevItemNum(m_iDevNum, m_iItemNum);

    QHBoxLayout *pItemBtnLayout = new QHBoxLayout;
    pItemBtnLayout->setMargin(0);
    pItemBtnLayout->addStretch(1);

    for(int i=0; i<m_iItemNum; i++)
    {
        CQCDevItemBtn *pBtn = new CQCDevItemBtn(i);
        connect(pBtn, &CQCDevItemBtn::clicked, this, &CQCPage::_SlotItemBtnClicked);
        m_pDevItemBtnList.push_back(pBtn);

        pItemBtnLayout->addWidget(pBtn);
        pItemBtnLayout->addSpacing(10);
    }
    pItemBtnLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addSpacing(10);
    pLayout->addLayout(pTitleLayout);
    pLayout->addStretch(1);
    pLayout->addLayout(pItemBtnLayout);
    pLayout->addStretch(1);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(1680, 350);
    pGroupBox->setObjectName("QCGroupBox");
    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}

QGroupBox *CQCPage::_CreateDownGroupBox()
{
    m_pIcon2Label = new QLabel("2");
    m_pIcon2Label->setFixedSize(50, 50);
    m_pIcon2Label->setObjectName("GroupIconLabel");
    m_pIcon2Label->setAlignment(Qt::AlignCenter);

    m_pTitle2Label = new QLabel(tr("请选择质控方式"));
    m_pTitle2Label->setFixedHeight(50);
    m_pTitle2Label->setObjectName("GroupTitleLabel");

    QHBoxLayout *pTitleLayout = new QHBoxLayout;
    pTitleLayout->setMargin(0);
    pTitleLayout->setSpacing(0);
    pTitleLayout->addSpacing(15);
    pTitleLayout->addWidget(m_pIcon2Label);
    pTitleLayout->addSpacing(10);
    pTitleLayout->addWidget(m_pTitle2Label);
    pTitleLayout->addStretch(1);

    m_pPositiveBtn = _CreateActBtn(":/image/ico/QC/positive.png", tr("阳性质控"));
    connect(m_pPositiveBtn, &QPushButton::clicked, this, &CQCPage::_SlotPostiveBtn);

    m_pNegativeBtn = _CreateActBtn(":/image/ico/QC/negative.png", tr("阴性质控"));
    connect(m_pNegativeBtn, &QPushButton::clicked, this, &CQCPage::_SlotNegativeBtn);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(0);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pPositiveBtn);
    pBtnLayout->addSpacing(216);
    pBtnLayout->addWidget(m_pNegativeBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addSpacing(10);
    pLayout->addLayout(pTitleLayout);
    pLayout->addStretch(1);
    pLayout->addLayout(pBtnLayout);
    pLayout->addStretch(1);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(1680, 550);
    pGroupBox->setObjectName("QCGroupBox");
    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}
