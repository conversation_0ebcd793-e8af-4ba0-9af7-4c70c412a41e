﻿#ifndef _ORU_R01_PATIENT_RESULT_H
#define _ORU_R01_PATIENT_RESULT_H

#include "ORU_R01_ORDER_OBSERVATION.h"
#include "../segment/PID.h"
#include <vector>
#include "../macros.h"
#include "../interface/IORU_R01_PATITENT_RESULT.h"

class ORU_R01_PATIENT_RESULT : public IORU_R01_PATIENT_RESULT
{
public:
	ORU_R01_PATIENT_RESULT();
	~ORU_R01_PATIENT_RESULT();

	DECLARE_OBJECTBASE

	BEGIN_IMPL_QUERYIF(ORU_R01_PATIENT_RESULT)
		IMPL_QUERYIF_BYPATH(IF_OBJECTBASE, IORU_R01_PATIENT_RESULT, IObjectBase)
		IMPL_QUERYIF(IF_ORU_R01_PATIENT_RESULT, IORU_R01_PATIENT_RESULT)
	END_IMPL_QUERYIF()

	bool GetPatient(IPID* pid);

	void SetPatient(const IPID* pid);

	void Add_ORU_R01_ORDER_OBSERVATION(IORU_R01_ORDER_OBSERVATION* orderObservation);

	void Delete_ORU_R01_ORDER_OBSERVATION(std::size_t index);

	void Delete_ALL_ORU_R01_ORDER_OBSERVATIONS();

	NRET Get_ORU_R01_ORDER_OBSERVATION_COLLECTION(IObjectList* objectList);

	void GetString(char** str);

	virtual int GetOrderObservationSize();

	virtual int GetOBXSize(std::size_t orderObservationIndex);

	virtual bool GetOBR(IOBR* obr, std::size_t orderOBRIndex);

	virtual bool GetOBX(IOBX* obx, std::size_t orderOBRIndex, 
		std::size_t obxIndex);
private:
	//观察结果观察报告组集合{OBR{[OBX]}}
	std::vector<IORU_R01_ORDER_OBSERVATION*> m_orderObservationVect;
	//病人信息[PID]
	PID* m_pid;
};

#endif
