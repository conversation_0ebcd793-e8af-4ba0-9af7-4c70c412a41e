#ifndef CCMDMANAGER_H
#define CCMDMANAGER_H

#include <QTimer>
#include <QObject>
#include <QMultiMap>

// 首先在头文件中定义与C语言对应的数据结构
#include <QtGlobal>
#include <QByteArray>
#include <QDataStream>

enum AppFlag {
    eAppFlagVM        = 0x01,
    eAppFlagPcr       = 0x02,
    eAppFlagHT        = 0x04,
    eAppFlagPressure  = 0x08
};

#pragma pack(push)
#pragma pack(1)

struct sPcrInfo {
    quint16 runStep;
    quint16 runCir;
    int targetTemp;
    int module1Temp;
    int module2Temp;
};

struct sHTInfo {
    quint16 moduleTemp[4];
};

struct ParsedStatus {
    quint8 dataFlag;
    quint16 timingStep;
    sPcrInfo pcrInfo;
    sHTInfo htInfo;
    int pressure;
    
    bool hasVM() const { return dataFlag & eAppFlagVM; }
    bool hasPcr() const { return dataFlag & eAppFlagPcr; }
    bool hasHT() const { return dataFlag & eAppFlagHT; }
    bool hasPressure() const { return dataFlag & eAppFlagPressure; }
};
#pragma pack(pop)

class ProtocolParser {
public:
    static bool parseStatusInfo(const QByteArray& data, ParsedStatus& result) {
        if(data.isEmpty()) return false;

        QDataStream stream(data);

        // 解析DataFlag
        if(stream.readRawData(reinterpret_cast<char*>(&result.dataFlag), 1) != 1)
            return false;

        // 解析VM数据
        if(result.hasVM()) {
            if(!parseVM(stream, result)) return false;
        }

        // 解析PCR数据
        if(result.hasPcr()) {
            if(!parsePcr(stream, result)) return false;
        }

        // 解析HT数据
        if(result.hasHT()) {
            if(!parseHT(stream, result)) return false;
        }

        // 解析Pressure数据
        if(result.hasPressure()) {
            if(!parsePressure(stream, result)) return false;
        }

        return stream.status() == QDataStream::Ok;
    }

private:
    static bool parseVM(QDataStream& stream, ParsedStatus& result) {
        return stream.readRawData(reinterpret_cast<char*>(&result.timingStep), 
                                sizeof(result.timingStep)) == sizeof(result.timingStep);
    }

    static bool parsePcr(QDataStream& stream, ParsedStatus& result) {
        return stream.readRawData(reinterpret_cast<char*>(&result.pcrInfo),
                                sizeof(sPcrInfo)) == sizeof(sPcrInfo);
    }

    static bool parseHT(QDataStream& stream, ParsedStatus& result) {
        return stream.readRawData(reinterpret_cast<char*>(&result.htInfo),
                                sizeof(sHTInfo)) == sizeof(sHTInfo);
    }

    static bool parsePressure(QDataStream& stream, ParsedStatus& result) {
        return stream.readRawData(reinterpret_cast<char*>(&result.pressure),
                                sizeof(int)) == sizeof(int);
    }
};

class CCmdBase;

class CCmdManager : public QObject
{
    Q_OBJECT
public:
    static CCmdManager *GetInstance();
    static void FreeInstance();

    void Register2Map(int iMethodID, CCmdBase* pClass);
    void UnRegister2Map(int iMethodID, CCmdBase* pClass);

    void ForTest(int iMachineID, QList<bool> bPnList);

private slots:
    void _SlotTimeout();

private:
    explicit CCmdManager(QObject *parent = nullptr);
    ~CCmdManager();

private:
    QVariantList _Makedata(QList<bool> bPnList);
    QVariantList _MakeMeltingdata(bool bNegtive);
	bool _RegisterFLMode(int iMachineID, QVariant &qVarRawData);

private:
    static CCmdManager *m_spInstance;
    QMultiMap<int, CCmdBase*> m_cmdObjMap;
    QTimer *m_pTimer;

    Q_DISABLE_COPY(CCmdManager)
};

#endif // CCMDMANAGER_H
