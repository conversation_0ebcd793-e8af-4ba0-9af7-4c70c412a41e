#ifndef CLOGDB_H
#define CLOGDB_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-01-23
  * Description: 记录故障日志
  * -------------------------------------------------------------------------
  * History: 添加操作日志
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QObject>
#include "CSqliteDBBase.h"

class CLogDB : public QObject , public CSqliteDBBase
{
    Q_OBJECT
public:
    static CLogDB &instance();
    virtual ~CLogDB();

    enum EOperationLogType{eLoginLog, eExportLog, eReviewLog};

     /*****************故障日志*****************/
    bool RealAllFaultLogData(QList<QStringList> &strList);
    int GetFaultLogAllRecordCount();
    int GetFaultLogQueryRecordCount(const QString &strType, const QString &strParam, bool bLike);
    bool AddFaultLog(const QStringList &strDataList);
    bool GetOnePageFaultLog(int iPage, int iOffset, QList<QStringList> &strReadList);
    bool GetQueryOnePageFaultLog(const QString &strType, const QString &strParam,
                                 int iPage, int iOffset,
                                 bool bLike, QList<QStringList> &strReadList);
    bool DeleteAllFaultLogAndResetID();

    bool ReadFaultLogLevel4(QList<QStringList> &strList);

    /*****************登录日志*****************/
    bool AddLoginLog(int iLogType, QString strUser, QString strLogText, QString strMachineCode = "");
    bool ReadAllLoginLog(QList<QStringList> &strReadList);
    bool ReadLoginLog(QString strBeginTime, QString strEndTime, QList<QStringList> &strReadList);

    /*****************操作日志 登录、审核、导出*****************/
    bool AddOperationLog(QString strUser, QString strLog, EOperationLogType eLogType);
    bool ReadAllOperationLog(QString strUser, bool bReview, QList<QStringList> &strList); //区分用户,是否显示审核

    bool ReadAllOperationLog(QList<QStringList> &strList);
    int GetOperationLogAllCount();
    int GetOperationLogQueryCount(QString strType, QString strParam, bool bLike);
    bool GetOnePageOperationLog(int iPage, int iOffset, QList<QStringList> &strReadList);
    bool GetQueryOnePageOperationLog(QString strType, QString strParam, int iPage, int iOffset,
                                     bool bLike, QList<QStringList> &strReadList);

signals:
    void SignalRefreshOperationLog();

private:
    CLogDB();

    void _InitFaultLogTable();
    void _InitLoginLogTable();
    void _InitOperationLogTable();
};

#endif // CLOGDB_H
