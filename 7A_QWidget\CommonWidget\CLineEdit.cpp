#include "CLineEdit.h"
#include "PublicConfig.h"
#include "PublicParams.h"
#include "CQmlKeyboard.h"

CLineEdit::CLineEdit(QWidget *parent)
    : QLineEdit(parent)
{
    //setInputMethodHints(Qt::ImhNone);
}

CLineEdit::CLineEdit(const QString &strText, QWidget *parent)
    : QLineEdit(strText, parent)
{
    //setInputMethodHints(Qt::ImhNone);
}

CLineEdit::~CLineEdit()
{

}

void CLineEdit::mousePressEvent(QMouseEvent *pEvent)
{
    if(CPublicConfig::GetInstance()->GetQmlKeyboard())
    {
        QWidget *pThis = this;
        while (pThis->parentWidget()) {
            pThis = pThis->parentWidget();
        }
        QWidget *pRootWidget = pThis;
        QPoint point = this->mapTo(pRootWidget, QPoint(0, 0));
        CQmlKeybaord::GetInstance()->SetKeyboardPosition(point.x(), point.y(), height(), this);
    }
    emit SignalPressEvent();
    QLineEdit::mousePressEvent(pEvent);
}
