#include "CMotorPosition.h"
#include <QBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QScrollArea>
#include <QDateTime>
#include <QTextCursor>
#include <QButtonGroup>
#include <QFile>
#include "CMessageBox.h"
#include "PublicParams.h"
#include <QTextCodec>
#include <algorithm>

CMotorPosition::CMotorPosition(QWidget *parent) : QWidget(parent)
{
    // 注册基础指令到映射表
    Register2Map(Method_GCMP);
    Register2Map(Method_SCMP);
    Register2Map(Method_MCHK);

    m_strConfigPath = QApplication::applicationDirPath() + "/motor_position_config.ini";
    m_pSettings = nullptr;
    m_iCurrentModuleIndex = 0; // 从第一个模块开始
    m_iCurrentSequenceIndex = 0;
    
    // 初始化跨模块操作状态
    m_bWaitingForReset = false;
    m_bCrossModuleAction = false;
    m_iPendingModuleIndex = -1;
    m_iPendingPositionIndex = -1;
    m_bModuleFinishedReset = false;
    m_iOriginalModuleIndex = -1;
    
    // 初始化补偿值获取状态
    m_bWaitingForCompensate = false;
    m_iCompensateRequestModuleIndex = -1;
    m_iCompensateRequestPositionIndex = -1;
    
    // 初始化补偿数据管理状态
    m_iCurrentCompensateMotorID = -1;
    m_bWaitingForSetCompensate = false;

    m_pResponseTimer = new QTimer(this);
    m_pResponseTimer->setSingleShot(true);
    connect(m_pResponseTimer, &QTimer::timeout, [this]() {
        _AddDebugRecord(tr("电机运动超时，请检查连接状态"));
    });

    m_pSequenceTimer = new QTimer(this);
    m_pSequenceTimer->setSingleShot(true);
    connect(m_pSequenceTimer, &QTimer::timeout, [this]() {
        // 继续执行序列中的下一个ID
        if(m_iCurrentSequenceIndex < m_currentSequence.size())
        {
            int iCmdID = m_currentSequence.at(m_iCurrentSequenceIndex);
            QString strCmd = GetJsonCmdString(iCmdID);
            SendJsonCmd(m_pMachineComboBox->GetCurrentIndex(), iCmdID, strCmd);
            m_pResponseTimer->start(5000); // 5秒超时
            _AddDebugRecord(tr("执行序列命令ID: %1").arg(iCmdID));
        }
    });

    _LoadConfigFromFile();  // 先加载配置文件
    _RegisterCommandsFromConfig();  // 根据配置文件注册指令
    _InitMotorModules();    // 再初始化数据
    _InitWidget();          // 再初始化界面
    _UpdateButtonStates();  // 更新按钮状态
    
    // 界面初始化完成后再添加调试记录
    _AddDebugRecord(tr("电机位置调试流程开始"));
    _AddDebugRecord(tr("所有模块都可以自由调试，当前模块为黄色，已完成模块为绿色"));
}

CMotorPosition::~CMotorPosition()
{
    // 反注册基础指令
    UnRegister2Map(Method_GCMP);
    UnRegister2Map(Method_SCMP);
    UnRegister2Map(Method_MCHK);
    
    // 反注册所有从配置文件注册的指令
    _UnregisterCommandsFromConfig();
    
    // 不需要特殊清理
}

void CMotorPosition::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    Q_UNUSED(qVarData);
    
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if(m_pMachineComboBox->GetCurrentIndex() != iMachineID)
        return;
     _AddDebugRecord(tr("ID %1 执行完成").arg(iMethodID));
     qDebug() << "ID " << iMethodID << " 执行完成" << m_iCurrentSequenceIndex << m_currentSequence.size();
    // 检查是否是当前执行序列中的指令回复
    if(m_iCurrentSequenceIndex < m_currentSequence.size() && 
       iMethodID == m_currentSequence.at(m_iCurrentSequenceIndex))
    {
        m_pResponseTimer->stop();
        
        if(0 == iResult)
        {
            _AddDebugRecord(tr("命令ID %1 执行完成").arg(iMethodID));
            
            // 执行下一个指令
            m_iCurrentSequenceIndex++;
            if(m_iCurrentSequenceIndex < m_currentSequence.size())
            {
                m_pSequenceTimer->start(100); // 100ms后执行下一个指令
            }
            else
            {
                // 序列执行完成
                _AddDebugRecord(tr("序列执行完成"));
                m_currentSequence.clear();
                m_iCurrentSequenceIndex = 0;
            }
        }
        else
        {
            _AddDebugRecord(tr("命令ID %1 执行失败，错误码: %2").arg(iMethodID).arg(iResult));
            m_currentSequence.clear();
            m_iCurrentSequenceIndex = 0;
        }
    }
    
    // 检查是否是复位指令回复
    if(iMethodID == Method_MCHK) // Method_MCHK
    {
        m_pResponseTimer->stop();
        if(0 == iResult)
        {
            // 区分整机复位和模块复位
            if(m_bCrossModuleAction && m_iPendingModuleIndex != -1 && m_iPendingPositionIndex != -1)
            {
                // 跨模块操作的模块复位
                _AddDebugRecord(tr("模块复位完成"));
                _AddDebugRecord(tr("复位完成，执行跨模块操作"));
                _ExecutePositionAction(m_iPendingModuleIndex, m_iPendingPositionIndex);
                
                // 清除等待状态
                m_bCrossModuleAction = false;
                m_iPendingModuleIndex = -1;
                m_iPendingPositionIndex = -1;
            }
            else if(m_bModuleFinishedReset)
            {
                // 模块完成后的复位（保留此逻辑以防其他地方调用）
                _AddDebugRecord(tr("***模块完成后的整机复位完成***"));
                
                // 清除标志位
                m_bModuleFinishedReset = false;
                
                // 更新按钮状态
                _UpdateButtonStates();
            }
            else
            {
                // 整机复位
                _AddDebugRecord(tr("***整机复位完成***"));
                _AddDebugRecord(tr("系统已恢复到初始状态"));
            }
        }
        else
        {
            // 区分整机复位和模块复位的失败处理
            if(m_bCrossModuleAction)
            {
                _AddDebugRecord(tr("模块复位失败，错误码: %1").arg(iResult));
                _AddDebugRecord(tr("复位失败，取消跨模块操作"));
                m_bCrossModuleAction = false;
                m_iPendingModuleIndex = -1;
                m_iPendingPositionIndex = -1;
            }
            else if(m_bModuleFinishedReset)
            {
                _AddDebugRecord(tr("***模块完成后的整机复位失败，错误码: %1***").arg(iResult));
                _AddDebugRecord(tr("复位失败，但模块已标记为完成"));
                
                // 清除标志位
                m_bModuleFinishedReset = false;
                
                // 更新按钮状态
                _UpdateButtonStates();
            }
            else
            {
                _AddDebugRecord(tr("***整机复位失败，错误码: %1***").arg(iResult));
            }
        }
    }
    
    // 检查是否是获取补偿值指令回复
    if(iMethodID == Method_GCMP)
    {
        m_pResponseTimer->stop();
        if(0 == iResult)
        {
            _AddDebugRecord(tr("获取补偿值成功"));
            
            // 解析补偿值数据
            if(m_bWaitingForCompensate && 
               m_iCompensateRequestModuleIndex != -1 && 
               m_iCompensateRequestPositionIndex != -1)
            {
                // 解析qVarData中的补偿值数组
                QStringList strList = qVarData.toString().split(SPLIT_BETWEEN_CMD);
                if(strList.size() >= 2)
                {
                    // 第一个元素是电机ID
                    int iMotorID = strList.at(0).toInt();
                    
                    // 保存完整的补偿数据数组和名称数组
                    m_listCurrentCompensateData.clear();
                    m_listCurrentCompensateNames.clear();
                    m_iCurrentCompensateMotorID = iMotorID;
                    
                    // 从第二个元素开始是补偿值数组
                    for(int i = 1; i < strList.size(); i++)
                    {
                        QStringList compensateItem = strList.at(i).split(SPLIT_IN_CMD);
                        if(compensateItem.size() >= 3)
                        {
                            // 格式：索引,名称,补偿值
                            int iIndex = compensateItem.at(0).toInt();
                            QString strName = compensateItem.at(1);
                            int iCompensate = compensateItem.at(2).toInt();
                            
                            // 确保数组足够大
                            while(m_listCurrentCompensateData.size() <= iIndex)
                            {
                                m_listCurrentCompensateData.append(0);
                                m_listCurrentCompensateNames.append("");
                            }
                            
                            // 同时保存补偿值和名称
                            m_listCurrentCompensateData[iIndex] = iCompensate;
                            m_listCurrentCompensateNames[iIndex] = strName;
                        }
                    }
                    
                    // 更新当前位置的补偿值显示
                    SMotorModule &module = m_MotorModules[m_iCompensateRequestModuleIndex];
                    if(m_iCompensateRequestPositionIndex < module.listPositions.size())
                    {
                        SMotorPosition &position = module.listPositions[m_iCompensateRequestPositionIndex];
                        
                        // 根据补偿索引获取对应的补偿值
                        if(position.iCompensateIndex < m_listCurrentCompensateData.size())
                        {
                            position.iCurrentCompensate = m_listCurrentCompensateData.at(position.iCompensateIndex);
                            _UpdateCompensateDisplay(m_iCompensateRequestModuleIndex, m_iCompensateRequestPositionIndex);
                            _AddDebugRecord(tr("更新 %1 补偿值: %2 (索引: %3)")
                                           .arg(position.strActionName)
                                           .arg(position.iCurrentCompensate)
                                           .arg(position.iCompensateIndex));
                        }
                        else
                        {
                            _AddDebugRecord(tr("补偿索引超出范围: %1").arg(position.iCompensateIndex));
                        }
                    }
                }
                else
                {
                    _AddDebugRecord(tr("补偿值数据格式错误"));
                }
                
                // 清除等待状态
                m_bWaitingForCompensate = false;
                m_iCompensateRequestModuleIndex = -1;
                m_iCompensateRequestPositionIndex = -1;
            }
        }
        else
        {
            _AddDebugRecord(tr("获取补偿值失败，错误码: %1").arg(iResult));
            
            // 获取失败，清除等待状态
            if(m_bWaitingForCompensate)
            {
                m_bWaitingForCompensate = false;
                m_iCompensateRequestModuleIndex = -1;
                m_iCompensateRequestPositionIndex = -1;
            }
        }
    }
    
    // 检查是否是设置补偿值指令回复
    if(iMethodID == Method_SCMP)
    {
        m_pResponseTimer->stop();
        if(0 == iResult)
        {
            _AddDebugRecord(tr("设置补偿值成功"));
        }
        else
        {
            _AddDebugRecord(tr("设置补偿值失败，错误码: %1").arg(iResult));
        }
        
        // 清除等待状态
        m_bWaitingForSetCompensate = false;
    }
}

void CMotorPosition::_SlotPositionMoveClicked()
{
    QPushButton *pBtn = qobject_cast<QPushButton*>(sender());
    if(!pBtn)
        return;

    // 查找是哪个位置的按钮
    int iModuleIndex = -1;
    int iPositionIndex = -1;
    
    for(int i = 0; i < m_MotorModules.size(); i++)
    {
        for(int j = 0; j < m_MotorModules[i].listPositions.size(); j++)
        {
            if(m_MotorModules[i].listPositions[j].pMoveBtn == pBtn)
            {
                iModuleIndex = i;
                iPositionIndex = j;
                break;
            }
        }
        if(iModuleIndex != -1)
            break;
    }

    if(iModuleIndex == -1 || iPositionIndex == -1)
        return;

    // 检查是否跨模块操作
    if(iModuleIndex != m_iCurrentModuleIndex)
    {
        _ExecuteCrossModuleAction(iModuleIndex, iPositionIndex);
        return;
    }

    // 执行当前模块的位置操作
    _ExecutePositionAction(iModuleIndex, iPositionIndex);
}

void CMotorPosition::_SlotCompensateAdd()
{
    if(m_iCurrentModuleIndex >= m_MotorModules.size())
        return;
        
    SMotorModule &module = m_MotorModules[m_iCurrentModuleIndex];
    if(module.iCurrentPositionIndex < 0 || module.iCurrentPositionIndex >= module.listPositions.size())
        return;
        
    SMotorPosition &position = module.listPositions[module.iCurrentPositionIndex];
    
    // 获取下拉框中的补偿步长
    QString strStepText = module.pCompensateStepComboBox->currentText().trimmed();
    if(strStepText.isEmpty())
    {
        strStepText = "1000";
        module.pCompensateStepComboBox->setCurrentText("1000");
        _AddDebugRecord(tr("补偿步长为空，已重置为默认值1000"));
    }
    
    bool ok;
    int iStepValue = strStepText.toInt(&ok);
    if(!ok || iStepValue <= 0)
    {
        iStepValue = 1000; // 如果转换失败或值无效，使用默认值
        module.pCompensateStepComboBox->setCurrentText("1000");
        _AddDebugRecord(tr("补偿步长输入无效，已重置为默认值1000"));
    }
    else if(iStepValue > 100000)
    {
        iStepValue = 100000;
        module.pCompensateStepComboBox->setCurrentText("100000");
        _AddDebugRecord(tr("补偿步长过大，已限制为100000"));
    }
    
    position.iCurrentCompensate += iStepValue;
    if(position.iCurrentCompensate > 100000)
        position.iCurrentCompensate = 100000; // 最大限制

    // 更新内存中的补偿数据数组
    if(position.iMotorID == m_iCurrentCompensateMotorID && 
       position.iCompensateIndex < m_listCurrentCompensateData.size())
    {
        m_listCurrentCompensateData[position.iCompensateIndex] = position.iCurrentCompensate;
        
        // 发送SCMP指令设置补偿值
        _SendSetCompensateCommand(position.iMotorID);
    }
    else
    {
        _AddDebugRecord(tr("补偿数据不匹配，请重新获取补偿值"));
    }

    _UpdateCompensateDisplay(m_iCurrentModuleIndex, module.iCurrentPositionIndex);
    _AddDebugRecord(tr("%1补偿值增加%2，当前值: %3")
                   .arg(position.strActionName)
                   .arg(iStepValue)
                   .arg(position.iCurrentCompensate));
}

void CMotorPosition::_SlotCompensateReduce()
{
    if(m_iCurrentModuleIndex >= m_MotorModules.size())
        return;
        
    SMotorModule &module = m_MotorModules[m_iCurrentModuleIndex];
    if(module.iCurrentPositionIndex < 0 || module.iCurrentPositionIndex >= module.listPositions.size())
        return;
        
    SMotorPosition &position = module.listPositions[module.iCurrentPositionIndex];
    
    // 获取下拉框中的补偿步长
    QString strStepText = module.pCompensateStepComboBox->currentText().trimmed();
    if(strStepText.isEmpty())
    {
        strStepText = "1000";
        module.pCompensateStepComboBox->setCurrentText("1000");
        _AddDebugRecord(tr("补偿步长为空，已重置为默认值1000"));
    }
    
    bool ok;
    int iStepValue = strStepText.toInt(&ok);
    if(!ok || iStepValue <= 0)
    {
        iStepValue = 1000; // 如果转换失败或值无效，使用默认值
        module.pCompensateStepComboBox->setCurrentText("1000");
        _AddDebugRecord(tr("补偿步长输入无效，已重置为默认值1000"));
    }
    else if(iStepValue > 100000)
    {
        iStepValue = 100000;
        module.pCompensateStepComboBox->setCurrentText("100000");
        _AddDebugRecord(tr("补偿步长过大，已限制为100000"));
    }
    
    position.iCurrentCompensate -= iStepValue;
    if(position.iCurrentCompensate < 0)
        position.iCurrentCompensate = 0; // 最小限制

    // 更新内存中的补偿数据数组
    if(position.iMotorID == m_iCurrentCompensateMotorID && 
       position.iCompensateIndex < m_listCurrentCompensateData.size())
    {
        m_listCurrentCompensateData[position.iCompensateIndex] = position.iCurrentCompensate;
        
        // 发送SCMP指令设置补偿值
        _SendSetCompensateCommand(position.iMotorID);
    }
    else
    {
        _AddDebugRecord(tr("补偿数据不匹配，请重新获取补偿值"));
    }

    _UpdateCompensateDisplay(m_iCurrentModuleIndex, module.iCurrentPositionIndex);
    _AddDebugRecord(tr("%1补偿值减少%2，当前值: %3")
                   .arg(position.strActionName)
                   .arg(iStepValue)
                   .arg(position.iCurrentCompensate));
}

void CMotorPosition::_SlotResetCompensate()
{
    if(m_iCurrentModuleIndex >= m_MotorModules.size())
        return;
        
    SMotorModule &module = m_MotorModules[m_iCurrentModuleIndex];
    if(module.iCurrentPositionIndex < 0 || module.iCurrentPositionIndex >= module.listPositions.size())
        return;
        
    SMotorPosition &position = module.listPositions[module.iCurrentPositionIndex];
    
    position.iCurrentCompensate = position.iDefaultCompensate; // 恢复默认值
    
    // 更新内存中的补偿数据数组
    if(position.iMotorID == m_iCurrentCompensateMotorID && 
       position.iCompensateIndex < m_listCurrentCompensateData.size())
    {
        m_listCurrentCompensateData[position.iCompensateIndex] = position.iCurrentCompensate;
        
        // 发送SCMP指令设置补偿值
        _SendSetCompensateCommand(position.iMotorID);
    }
    else
    {
        _AddDebugRecord(tr("补偿数据不匹配，请重新获取补偿值"));
    }
    
    _UpdateCompensateDisplay(m_iCurrentModuleIndex, module.iCurrentPositionIndex);
    _AddDebugRecord(tr("%1补偿值重置为默认值: %2")
                   .arg(position.strActionName)
                   .arg(position.iDefaultCompensate));
}

void CMotorPosition::_SlotModuleFinished()
{
    if(m_iCurrentModuleIndex >= m_MotorModules.size())
        return;

    // 标记当前模块调试完成
    m_MotorModules[m_iCurrentModuleIndex].bFinished = true;
    
    _AddDebugRecord(tr("***%1调试完成***")
                   .arg(m_strModuleNames.at(m_iCurrentModuleIndex)));

    // 检查是否所有模块都已完成
    bool bAllFinished = true;
    for(int i = 0; i < m_MotorModules.size(); i++)
    {
        if(!m_MotorModules[i].bFinished)
        {
            bAllFinished = false;
            break;
        }
    }
    
    if(bAllFinished)
    {
        _AddDebugRecord(tr("***所有模块都已完成！***"));
        _AddDebugRecord(tr("调试流程结束"));
    }
    else
    {
        _AddDebugRecord(tr("模块调试完成，可继续调试其他模块"));
    }
    
    // 更新按钮状态
    _UpdateButtonStates();
}

void CMotorPosition::_SlotClearRecord()
{
    m_pRecordTextEdit->clear();
    _AddDebugRecord(tr("调试记录已清空"));
}

void CMotorPosition::_SlotMachineReset()
{
    // 清空调试记录
    m_pRecordTextEdit->clear();
    
    // 只清除当前位置和补偿值，不改变模块调试状态
    for(int i = 0; i < m_MotorModules.size(); i++)
    {
        m_MotorModules[i].iCurrentPositionIndex = -1;  // 重置为未选择状态
        _UpdateCompensateDisplay(i, -1);  // 清空位置和补偿值显示
        _UpdatePositionButtonStates(i);   // 清除按钮高亮
        // 保持 bFinished 状态不变
    }
    
    // 清空当前的ID序列执行状态
    m_currentSequence.clear();
    m_iCurrentSequenceIndex = 0;
    
    // 重置跨模块操作状态
    m_bWaitingForReset = false;
    m_iPendingModuleIndex = -1;
    m_iPendingPositionIndex = -1;
    m_bModuleFinishedReset = false;
    m_iOriginalModuleIndex = -1;
    
    // 重置补偿值获取状态
    m_bWaitingForCompensate = false;
    m_iCompensateRequestModuleIndex = -1;
    m_iCompensateRequestPositionIndex = -1;
    
    // 清空补偿数据
    m_listCurrentCompensateData.clear();
    m_listCurrentCompensateNames.clear();
    m_iCurrentCompensateMotorID = -1;
    m_bWaitingForSetCompensate = false;
    
    // 停止所有定时器
    m_pResponseTimer->stop();
    m_pSequenceTimer->stop();
    
    // 更新界面状态
    _UpdateButtonStates();
    
    // 发送整机复位指令(Method_MCHK)
    QString strCmd = GetJsonCmdString(Method_MCHK); // Method_MCHK
    SendJsonCmd(m_pMachineComboBox->GetCurrentIndex(), Method_MCHK, strCmd);
    m_pResponseTimer->start(15000); // 15秒超时，整机复位可能需要更长时间
    
    // 添加记录
    _AddDebugRecord(tr("***整机复位***"));
    _AddDebugRecord(tr("发送整机复位指令(ID: 257)"));
    _AddDebugRecord(tr("已清除当前位置和补偿值"));
}

void CMotorPosition::_InitWidget()
{
    // 机器选择框和清空记录按钮
    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(80, 50);

    m_pMachineResetBtn = new QPushButton(tr("整机复位"));
    m_pMachineResetBtn->setFixedSize(100, 50);
    connect(m_pMachineResetBtn, &QPushButton::clicked, this, &CMotorPosition::_SlotMachineReset);

    // 右侧控制区域布局
    QHBoxLayout *pRightControlLayout = new QHBoxLayout;
    pRightControlLayout->setContentsMargins(10, 10, 10, 10);
    pRightControlLayout->addWidget(m_pMachineComboBox);
    pRightControlLayout->addStretch(1);
    pRightControlLayout->addWidget(m_pMachineResetBtn);

    // 调试记录区域
    QGroupBox *pRecordGroupBox = new QGroupBox(tr("调试记录"));

    m_pRecordTextEdit = new QTextEdit;
    m_pRecordTextEdit->setReadOnly(true);
    m_pRecordTextEdit->setMinimumHeight(300);  // 设置最小高度

    QVBoxLayout *pRecordLayout = new QVBoxLayout(pRecordGroupBox);
    pRecordLayout->setContentsMargins(10, 20, 10, 10);  // 增加顶部边距给标题留空间
    pRecordLayout->setSpacing(5);
    pRecordLayout->addWidget(m_pRecordTextEdit);

    // 右侧整体布局
    QVBoxLayout *pRightLayout = new QVBoxLayout;
    pRightLayout->setContentsMargins(0, 0, 0, 0);
    pRightLayout->setSpacing(10);
    pRightLayout->addLayout(pRightControlLayout);
    pRightLayout->addWidget(pRecordGroupBox, 1);  // 让日志记录区域可拉伸

    QWidget *pRightWidget = new QWidget;
    pRightWidget->setLayout(pRightLayout);
    pRightWidget->setFixedWidth(400);  // 固定右侧宽度

    // 左侧模块调试区域
    QWidget *pModuleWidget = new QWidget;
    QVBoxLayout *pModuleLayout = new QVBoxLayout(pModuleWidget);
    pModuleLayout->setContentsMargins(20, 20, 20, 20);
    pModuleLayout->setSpacing(20);  // 模块间距
    
    // 添加顶部弹簧实现垂直居中
    pModuleLayout->addStretch(1);

    // 创建模块调试组，垂直排列并居中
    for(int i = 0; i < m_MotorModules.size(); i++)
    {
        _InitModuleGroup(i);
        
        // 为每个模块创建水平居中布局
        QHBoxLayout *pModuleCenterLayout = new QHBoxLayout;
        pModuleCenterLayout->addStretch(1);  // 左侧弹簧
        pModuleCenterLayout->addWidget(m_MotorModules[i].pGroupBox);
        pModuleCenterLayout->addStretch(1);  // 右侧弹簧
        
        pModuleLayout->addLayout(pModuleCenterLayout);
    }
    
    // 添加底部弹簧实现垂直居中
    pModuleLayout->addStretch(1);

    // 左侧滚动区域
    QScrollArea *pScrollArea = new QScrollArea;
    pScrollArea->setWidget(pModuleWidget);
    pScrollArea->setWidgetResizable(true);
    pScrollArea->setMinimumHeight(400);

    // 主布局 - 左右分布
    QHBoxLayout *pMainLayout = new QHBoxLayout;
    pMainLayout->setContentsMargins(10, 10, 10, 10);
    pMainLayout->setSpacing(15);  // 左右间距
    pMainLayout->addWidget(pScrollArea, 1);  // 左侧模块区域可拉伸
    pMainLayout->addWidget(pRightWidget, 0);  // 右侧固定宽度
    this->setLayout(pMainLayout);

    // 初始记录已在构造函数中添加
}

void CMotorPosition::_InitMotorModules()
{
    m_MotorModules.clear();
    for(int i = 0; i < m_strModuleNames.size(); i++)
    {
        SMotorModule module;
        module.iCurrentPositionIndex = -1;
        module.bFinished = false;
        module.strModuleName = m_strModuleNames.at(i);
        module.pGroupBox = nullptr;
        module.pCompensateStepComboBox = nullptr;
        module.pAddBtn = nullptr;
        module.pReduceBtn = nullptr;
        module.pResetBtn = nullptr;
        module.pFinishBtn = nullptr;
        module.pCurrentPosLabel = nullptr;
        module.pCompensateLabel = nullptr;
        m_MotorModules.append(module);
    }
    if(!m_pSettings)
        return;
    for(int i = 0; i < m_strModuleNames.size(); i++)
    {
        QString section = m_strModuleKeys[i];
        _LoadModulePositions(section, i);
    }
}

void CMotorPosition::_InitModuleGroup(int iModuleIndex)
{
    if(iModuleIndex < 0 || iModuleIndex >= m_MotorModules.size())
        return;

    SMotorModule &module = m_MotorModules[iModuleIndex];

    // 创建组框
    module.pGroupBox = new QGroupBox(module.strModuleName);
    module.pGroupBox->setFixedSize(900, 400);  // 增加宽度到900px
    // 初始样式，会在_UpdateButtonStates中更新
    module.pGroupBox->setStyleSheet("QGroupBox { font-weight: bold; padding-top: 20px; }"
                                   "QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 5px 0 5px; }");

    // 位置按钮区域
    QWidget *pPositionWidget = new QWidget;
    pPositionWidget->setMinimumHeight(200);  // 设置最小高度给按钮区域
    QGridLayout *pPositionLayout = new QGridLayout(pPositionWidget);
    pPositionLayout->setContentsMargins(15, 5, 15, 15);  // 减少顶部边距
    pPositionLayout->setSpacing(10);  // 增加按钮间距

    // 创建位置按钮
    int iCols = 3; // 每行3个按钮（增加列数）
    for(int i = 0; i < module.listPositions.size(); i++)
    {
        SMotorPosition &position = module.listPositions[i];
        position.pMoveBtn = new QPushButton(position.strActionName);
        position.pMoveBtn->setFixedSize(280, 35);  // 增加按钮宽度到280px
        connect(position.pMoveBtn, &QPushButton::clicked, this, &CMotorPosition::_SlotPositionMoveClicked);
        
        int iRow = i / iCols;
        int iCol = i % iCols;
        pPositionLayout->addWidget(position.pMoveBtn, iRow, iCol);
    }

    // 补偿调整区域
    // 补偿步长选择框
    module.pCompensateStepComboBox = new QComboBox();
    module.pCompensateStepComboBox->setEditable(true);  // 设置为可编辑
    
    // 添加常用预设值
    QStringList stepValues;
    stepValues << "100" << "500" << "1000" << "2000" << "5000" << "10000";
    module.pCompensateStepComboBox->addItems(stepValues);
    module.pCompensateStepComboBox->setCurrentText("1000");  // 默认值1000
    
    // 设置验证器，只允许输入正整数
    QIntValidator *validator = new QIntValidator(1, 100000, this);
    module.pCompensateStepComboBox->setValidator(validator);
    
    // 设置样式
    module.pCompensateStepComboBox->setStyleSheet(
        "QComboBox {"
        "    border: 2px solid #CCCCCC;"
        "    border-radius: 5px;"
        "    padding: 2px 5px;"
        "    background-color: white;"
        "    font-size: 12px;"
        "    color: #333333;"
        "    selection-background-color: #2196F3;"
        "    selection-color: white;"
        "}"
        "QComboBox QLineEdit {"
        "    border: none;"
        "    background: transparent;"
        "    selection-background-color: #2196F3;"
        "    selection-color: white;"
        "}"
        "QComboBox:hover {"
        "    border: 2px solid #2196F3;"
        "    background-color: #F5F5F5;"
        "}"
        "QComboBox:focus {"
        "    border: 2px solid #2196F3;"
        "    background-color: #F5F5F5;"
        "}"
        "QComboBox::drop-down {"
        "    subcontrol-origin: padding;"
        "    subcontrol-position: top right;"
        "    width: 25px;"
        "    border: none;"
        "    border-top-right-radius: 5px;"
        "    border-bottom-right-radius: 5px;"
        "}"
        "QComboBox:hover::drop-down {"
        "    background-color: #F5F5F5;"
        "}"
        "QComboBox:focus::drop-down {"
        "    background-color: #F5F5F5;"
        "}"
        "QComboBox::down-arrow {"
        "    width: 16px;"
        "    height: 16px;"
        "    image: url(:/image/ico/login/commod1.png);"
        "    padding: 0px 4px 0px 0px;"
        "}"
        "QComboBox QAbstractItemView {"
        "    border: 1px solid #CCCCCC;"
        "    background-color: white;"
        "    selection-background-color: #2196F3;"
        "    selection-color: white;"
        "    outline: none;"
        "}"
        "QComboBox QAbstractItemView::item {"
        "    min-height: 25px;"
        "    padding: 3px 8px;"
        "    border: none;"
        "}"
        "QComboBox QAbstractItemView::item:hover {"
        "    background-color: #E3F2FD;"
        "    color: #2196F3;"
        "}"
        "QComboBox QAbstractItemView::item:selected {"
        "    background-color: #2196F3;"
        "    color: white;"
        "}"
    );
    
    module.pCompensateStepComboBox->setFixedSize(120, 30);
    
    // 连接信号以验证输入
    connect(module.pCompensateStepComboBox, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            [this, iModuleIndex](const QString &text) {
                _ValidateCompensateStep(iModuleIndex, text);
            });
    
    module.pAddBtn = new QPushButton(tr("增加补偿"));
    module.pAddBtn->setFixedSize(100, 30);
    connect(module.pAddBtn, &QPushButton::clicked, this, &CMotorPosition::_SlotCompensateAdd);

    module.pReduceBtn = new QPushButton(tr("减少补偿"));
    module.pReduceBtn->setFixedSize(100, 30);
    connect(module.pReduceBtn, &QPushButton::clicked, this, &CMotorPosition::_SlotCompensateReduce);

    module.pResetBtn = new QPushButton(tr("恢复默认"));
    module.pResetBtn->setFixedSize(100, 30);
    connect(module.pResetBtn, &QPushButton::clicked, this, &CMotorPosition::_SlotResetCompensate);

    // 当前选中位置和补偿值显示
    module.pCurrentPosLabel = new CLabelLabel(tr("当前位置:"), tr("未选择"));
    module.pCompensateLabel = new CLabelLabel(tr("补偿值:"), "0");

    // 模块完成按钮
    module.pFinishBtn = new QPushButton(tr("模块调试完成"));
    module.pFinishBtn->setFixedSize(150, 40);
    connect(module.pFinishBtn, &QPushButton::clicked, this, &CMotorPosition::_SlotModuleFinished);

    // 控制区域布局
    QHBoxLayout *pControlLayout = new QHBoxLayout;
    pControlLayout->addWidget(module.pCompensateStepComboBox);
    pControlLayout->addWidget(module.pAddBtn);
    pControlLayout->addWidget(module.pReduceBtn);
    pControlLayout->addWidget(module.pResetBtn);
    pControlLayout->addStretch(1);

    // 主布局
    QVBoxLayout *pMainLayout = new QVBoxLayout(module.pGroupBox);
    pMainLayout->setContentsMargins(15, 10, 15, 15);  // 减少顶部边距
    pMainLayout->setSpacing(12);  // 增加组件间距
    
    pMainLayout->addWidget(pPositionWidget, 1);  // 给按钮区域更多空间
    pMainLayout->addWidget(module.pCurrentPosLabel);
    pMainLayout->addWidget(module.pCompensateLabel);
    pMainLayout->addLayout(pControlLayout);  // 补偿控制按钮移到补偿值后面
    pMainLayout->addWidget(module.pFinishBtn);
}

void CMotorPosition::_UpdateCompensateDisplay(int iModuleIndex, int iPositionIndex)
{
    if(iModuleIndex < 0 || iModuleIndex >= m_MotorModules.size())
        return;
        
    SMotorModule &module = m_MotorModules[iModuleIndex];
    
    // 如果没有选中位置，显示空值
    if(iPositionIndex < 0 || iPositionIndex >= module.listPositions.size())
    {
        module.pCurrentPosLabel->SetValueLabelText(tr("未选择"));
        module.pCompensateLabel->SetValueLabelText("0");
        return;
    }

    // 更新当前位置显示
    module.pCurrentPosLabel->SetValueLabelText(module.listPositions[iPositionIndex].strActionName);
    
    // 更新补偿值显示
    module.pCompensateLabel->SetValueLabelText(
        QString::number(module.listPositions[iPositionIndex].iCurrentCompensate));
}

void CMotorPosition::_UpdateButtonStates()
{
    for(int i = 0; i < m_MotorModules.size(); i++)
    {
        bool bIsCurrent = (i == m_iCurrentModuleIndex);
        bool bIsFinished = m_MotorModules[i].bFinished;
        
        // 所有模块都可以操作，不再按顺序限制
        bool bEnabled = true;
        
        // 启用所有模块组
        _EnableModuleGroup(i, bEnabled);
        
        // 更新位置按钮状态 - 对当前模块设置选中样式
        if(bIsCurrent)
        {
            _UpdatePositionButtonStates(i);
            // 更新补偿值显示
            _UpdateCompensateDisplay(i, m_MotorModules[i].iCurrentPositionIndex);
        }
        else
        {
            // 非当前模块清除选中样式，但保持可用
            for(int j = 0; j < m_MotorModules[i].listPositions.size(); j++)
            {
                m_MotorModules[i].listPositions[j].pMoveBtn->setStyleSheet("");
            }
            // 非当前模块显示未选择状态
            _UpdateCompensateDisplay(i, -1);
        }
        
        // 模块组框显示不同颜色
        QString baseStyle = "QGroupBox { font-weight: bold; padding-top: 20px; }"
                           "QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 5px 0 5px; }";
        
        if(bIsFinished)
        {
            // 已完成的模块 - 绿色
            m_MotorModules[i].pGroupBox->setStyleSheet(baseStyle + 
                "QGroupBox { background-color: #E8F5E8; border: 2px solid #4CAF50; }");
        }
        else if(bIsCurrent)
        {
            // 当前模块 - 黄色
            m_MotorModules[i].pGroupBox->setStyleSheet(baseStyle + 
                "QGroupBox { background-color: #FFF3E0; border: 2px solid #FF9800; }");
        }
        else
        {
            // 未调试的模块 - 无特殊颜色
            m_MotorModules[i].pGroupBox->setStyleSheet(baseStyle + 
                "QGroupBox { background-color: #F5F5F5; border: 1px solid #CCCCCC; }");
        }
    }
}

void CMotorPosition::_UpdatePositionButtonStates(int iModuleIndex)
{
    if(iModuleIndex < 0 || iModuleIndex >= m_MotorModules.size())
        return;
        
    SMotorModule &module = m_MotorModules[iModuleIndex];
    
    // 更新所有位置按钮的样式
    for(int i = 0; i < module.listPositions.size(); i++)
    {
        QPushButton *pBtn = module.listPositions[i].pMoveBtn;
        if(i == module.iCurrentPositionIndex && module.iCurrentPositionIndex != -1)
        {
            // 选中的按钮高亮显示
            pBtn->setStyleSheet("QPushButton{background-color: #2196F3; color: white; font-weight: bold;}");
        }
        else
        {
            // 未选中的按钮恢复默认样式
            pBtn->setStyleSheet("");
        }
    }
}

void CMotorPosition::_AddDebugRecord(const QString &strRecord)
{
    // 安全检查，确保界面已初始化
    if(!m_pRecordTextEdit)
        return;
        
    QString strTime = QDateTime::currentDateTime().toString("hh:mm:ss");
    QString strFullRecord = QString("[%1] %2").arg(strTime).arg(strRecord);
    
    m_pRecordTextEdit->append(strFullRecord);
    m_pRecordTextEdit->moveCursor(QTextCursor::End);
}

void CMotorPosition::_EnableModuleGroup(int iModuleIndex, bool bEnabled)
{
    if(iModuleIndex < 0 || iModuleIndex >= m_MotorModules.size())
        return;

    SMotorModule &module = m_MotorModules[iModuleIndex];
    
    // 位置按钮 - 所有模块都可以点击
    for(int i = 0; i < module.listPositions.size(); i++)
    {
        QPushButton *pBtn = module.listPositions[i].pMoveBtn;
        pBtn->setEnabled(true);  // 所有按钮都启用
        
        // 清除非当前模块的选中样式
        if(iModuleIndex != m_iCurrentModuleIndex)
        {
            pBtn->setStyleSheet("");
        }
    }
    
    // 更新当前位置和补偿值显示
    if(iModuleIndex == m_iCurrentModuleIndex)
    {
        _UpdateCompensateDisplay(iModuleIndex, module.iCurrentPositionIndex);
    }
    else
    {
        // 非当前模块显示未选择状态
        _UpdateCompensateDisplay(iModuleIndex, -1);
    }
    
    // 控制按钮 - 只有当前模块且选中了位置才能调整补偿
    bool bIsCurrentModule = (iModuleIndex == m_iCurrentModuleIndex);
    bool bHasSelectedPosition = (module.iCurrentPositionIndex >= 0);
    bool bCanAdjustCompensate = bIsCurrentModule && bHasSelectedPosition;
    
    // 补偿步长选择框 - 只要是当前模块就能使用
    module.pCompensateStepComboBox->setEnabled(bIsCurrentModule);
    module.pAddBtn->setEnabled(bCanAdjustCompensate);
    module.pReduceBtn->setEnabled(bCanAdjustCompensate);
    module.pResetBtn->setEnabled(bCanAdjustCompensate);
    
    // 完成按钮只有当前模块时才能点击，无论是否已完成
    module.pFinishBtn->setEnabled(bIsCurrentModule);
    
    // 根据模块状态设置完成按钮颜色
    if(bIsCurrentModule)
    {
        if(module.bFinished)
        {
            // 已完成的当前模块 - 绿色
            module.pFinishBtn->setStyleSheet("QPushButton{background-color: #4CAF50; color: white; font-weight: bold;}");
            module.pFinishBtn->setText(tr("模块调试完成"));
        }
        else
        {
            // 未完成的当前模块 - 橙色
            module.pFinishBtn->setStyleSheet("QPushButton{background-color: #FF9800; color: white; font-weight: bold;}");
            module.pFinishBtn->setText(tr("模块调试完成"));
        }
    }
    else
    {
        // 非当前模块 - 灰色
        module.pFinishBtn->setStyleSheet("QPushButton{background-color: #CCCCCC; color: #666666; font-weight: bold;}");
        module.pFinishBtn->setText(tr("模块调试完成"));
    }
}

void CMotorPosition::_SendIDSequence(const QList<int> &listIDSequence, int iMotorID, int iCompensate)
{
    if(listIDSequence.isEmpty())
        return;

    m_currentSequence = listIDSequence;
    m_iCurrentSequenceIndex = 0;

    // 发送第一个指令
    int iCmdID = m_currentSequence.at(0);
    QString strCmd;
    
    // 如果是第一个指令，可能需要带补偿参数
    if(iCompensate != 0 && m_iCurrentSequenceIndex == 0)
    {
        strCmd = GetJsonCmdString(iCmdID, QString::number(iCompensate));
    }
    else
    {
        strCmd = GetJsonCmdString(iCmdID);
    }
    
    SendJsonCmd(m_pMachineComboBox->GetCurrentIndex(), iCmdID, strCmd);
    m_pResponseTimer->start(5000); // 5秒超时
    
    _AddDebugRecord(tr("开始执行序列，第一个命令ID: %1，电机ID: %2，补偿: %3")
                   .arg(iCmdID).arg(iMotorID).arg(iCompensate));
}

void CMotorPosition::_SendResetCommand()
{
    QString strCmd = GetJsonCmdString(Method_MCHK); // Method_SRST
    SendJsonCmd(m_pMachineComboBox->GetCurrentIndex(), Method_MCHK, strCmd);
    m_pResponseTimer->start(10000); // 10秒超时
    _AddDebugRecord(tr("发送模块复位指令(ID: 257)"));
}

void CMotorPosition::_SendGetCompensateCommand(int iMotorID)
{
    QVariant qVarData = QString::number(iMotorID);
    QString strCmd = GetJsonCmdString(Method_GCMP, qVarData);
    SendJsonCmd(m_pMachineComboBox->GetCurrentIndex(), Method_GCMP, strCmd);
    m_pResponseTimer->start(10000); // 10秒超时
    _AddDebugRecord(tr("发送获取补偿值指令(ID: %1), 电机ID: %2").arg(Method_GCMP).arg(iMotorID));
}

void CMotorPosition::_SendSetCompensateCommand(int iMotorID)
{
    if(m_listCurrentCompensateData.isEmpty())
    {
        _AddDebugRecord(tr("补偿数据为空，无法设置"));
        return;
    }
    
    // 检查名称数组和补偿值数组大小是否一致
    if(m_listCurrentCompensateNames.size() != m_listCurrentCompensateData.size())
    {
        _AddDebugRecord(tr("补偿数据不完整，名称和值的数量不匹配"));
        return;
    }
    
    // 构建补偿数据字符串，格式：电机ID;索引,名称,补偿值;索引,名称,补偿值...
    QStringList strDataList;
    for(int i = 0; i < m_listCurrentCompensateData.size(); i++)
    {
        // 使用真实的补偿名称，如果名称为空则使用默认格式
        QString strName = m_listCurrentCompensateNames.at(i);
        if(strName.isEmpty())
        {
            strName = QString("pos%1").arg(i);
        }
        
        QString strItem = QString("%1,%2,%3").arg(i).arg(strName).arg(m_listCurrentCompensateData.at(i));
        strDataList.append(strItem);
    }
    
    QString strCompensateData = strDataList.join(SPLIT_BETWEEN_CMD);
    QVariant qVarData = QString("%1;%2").arg(iMotorID).arg(strCompensateData);
    QString strCmd = GetJsonCmdString(Method_SCMP, qVarData);
    
    SendJsonCmd(m_pMachineComboBox->GetCurrentIndex(), Method_SCMP, strCmd);
    m_pResponseTimer->start(10000); // 10秒超时
    m_bWaitingForSetCompensate = true;
    
    _AddDebugRecord(tr("发送设置补偿值指令(ID: %1), 电机ID: %2, 包含真实补偿名称").arg(Method_SCMP).arg(iMotorID));
}

void CMotorPosition::_ExecutePositionAction(int iModuleIndex, int iPositionIndex)
{
    if(iModuleIndex < 0 || iModuleIndex >= m_MotorModules.size())
        return;
        
    SMotorModule &module = m_MotorModules[iModuleIndex];
    if(iPositionIndex < 0 || iPositionIndex >= module.listPositions.size())
        return;

    // 切换当前模块（如果需要）
    if(iModuleIndex != m_iCurrentModuleIndex)
    {
        // 清空之前模块的选中位置和补偿值显示
        if(m_iCurrentModuleIndex >= 0 && m_iCurrentModuleIndex < m_MotorModules.size())
        {
            SMotorModule &prevModule = m_MotorModules[m_iCurrentModuleIndex];
            prevModule.iCurrentPositionIndex = -1;  // 清空选中位置
            _UpdateCompensateDisplay(m_iCurrentModuleIndex, -1);  // 清空显示
            _UpdatePositionButtonStates(m_iCurrentModuleIndex);   // 清除按钮高亮
        }
        
        m_iCurrentModuleIndex = iModuleIndex;
        _UpdateButtonStates(); // 更新所有模块的状态显示
        _AddDebugRecord(tr("切换到模块：%1").arg(m_strModuleNames.at(iModuleIndex)));
    }

    // 更新当前选中的位置
    module.iCurrentPositionIndex = iPositionIndex;
    
    // 更新按钮状态（高亮选中的按钮）
    _UpdatePositionButtonStates(iModuleIndex);
    
    SMotorPosition &position = module.listPositions[iPositionIndex];
    
    // 先更新位置显示，补偿值将在GCMP回复后更新
    module.pCurrentPosLabel->SetValueLabelText(position.strActionName);
    
    // 立即启用调整按钮，因为已经选中了位置
    _EnableModuleGroup(iModuleIndex, true);
    
    // 发送GCMP指令获取当前补偿值
    m_bWaitingForCompensate = true;
    m_iCompensateRequestModuleIndex = iModuleIndex;
    m_iCompensateRequestPositionIndex = iPositionIndex;
    _SendGetCompensateCommand(position.iMotorID);
    
    // 发送ID序列
    _SendIDSequence(position.listIDSequence, position.iMotorID, position.iCurrentCompensate);
    
    _AddDebugRecord(tr("开始执行 %1 - %2")
                   .arg(m_strModuleNames.at(iModuleIndex))
                   .arg(position.strActionName));
}

void CMotorPosition::_ExecuteCrossModuleAction(int iModuleIndex, int iPositionIndex)
{
    if(iModuleIndex < 0 || iModuleIndex >= m_MotorModules.size())
        return;
        
    SMotorModule &module = m_MotorModules[iModuleIndex];
    if(iPositionIndex < 0 || iPositionIndex >= module.listPositions.size())
        return;

    SMotorPosition &position = module.listPositions[iPositionIndex];
    
    // 无论目标模块是否完成，都先执行复位操作
    _AddDebugRecord(tr("跨模块操作：%1 - %2，先执行复位")
                   .arg(m_strModuleNames.at(iModuleIndex))
                   .arg(position.strActionName));
    
    // 发送复位指令
    _SendResetCommand();
    
    // 保存要执行的动作
    m_iPendingModuleIndex = iModuleIndex;
    m_iPendingPositionIndex = iPositionIndex;
    m_bCrossModuleAction = true;
}

void CMotorPosition::_LoadConfigFromFile()
{
    m_strModuleNames.clear();
    m_strModuleKeys.clear();
    if(!QFile::exists(m_strConfigPath))
        return;

    if(m_pSettings)
        delete m_pSettings;
    m_pSettings = new QSettings(m_strConfigPath, QSettings::IniFormat);
    m_pSettings->setIniCodec("UTF-8");

    // 从Module_Order节读取模块顺序
    QStringList orderedKeys;
    m_pSettings->beginGroup("Module_Order");
    QStringList orderNumbers = m_pSettings->childKeys();
    
    // 按数字顺序排序
    QList<int> numbers;
    for(const QString &orderStr : orderNumbers)
    {
        bool ok;
        int orderNum = orderStr.toInt(&ok);
        if(ok)
            numbers.append(orderNum);
    }
    
    std::sort(numbers.begin(), numbers.end());
    
    // 按顺序读取模块key
    for(int orderNum : numbers)
    {
        QString key = m_pSettings->value(QString::number(orderNum)).toString().trimmed();
        if(!key.isEmpty())
        {
            orderedKeys.append(key);
        }
    }
    m_pSettings->endGroup();

    // 从Module_Names节读取模块名称
    m_pSettings->beginGroup("Module_Names");
    
    // 按配置的顺序读取模块
    for(const QString &key : orderedKeys)
    {
        if(m_pSettings->contains(key))
        {
            QString name = m_pSettings->value(key).toString().trimmed();
            qDebug() << "key:" << key << "name:" << name;
            if(!name.isEmpty()) {
                m_strModuleKeys << key;
                m_strModuleNames << name;
            }
        }
    }
    
    // 检查是否有Module_Names中存在但Module_Order中未定义的key
    QStringList allKeys = m_pSettings->childKeys();
    for(const QString &key : allKeys)
    {
        if(!orderedKeys.contains(key))
        {
            QString name = m_pSettings->value(key).toString().trimmed();
            qDebug() << "未在顺序中定义的key:" << key << "name:" << name;
            if(!name.isEmpty()) {
                m_strModuleKeys << key;
                m_strModuleNames << name;
            }
        }
    }
    
    m_pSettings->endGroup();
}

void CMotorPosition::_RegisterCommandsFromConfig()
{
    // 检查配置文件是否存在
    if(!QFile::exists(m_strConfigPath))
    {
        return;
    }

    // 用于收集所有指令ID的集合，避免重复注册
    QSet<int> registeredCommands;

    // 遍历所有模块配置节
    QStringList strGroups = m_pSettings->childGroups();
    for(const QString &strGroup : strGroups)
    {
        // 跳过空组名
        if(strGroup.trimmed().isEmpty())
            continue;
            
        // 读取该模块的所有位置配置
        for(int i = 1; i <= 20; i++)
        {
            QString strPrefix = QString("Position%1_").arg(i);
            QString strName = m_pSettings->value(strGroup + "/" + strPrefix + "Name", "").toString();
            
            // 如果名称为空，说明没有更多位置了
            if(strName.isEmpty())
                break;
                
            QString strSequence = m_pSettings->value(strGroup + "/" + strPrefix + "Sequence", "").toStringList().join(",");
            
            // 解析序列字符串，获取所有指令ID
            QList<int> listSequence = _ParseSequenceString(strSequence);
            for(int iCmdID : listSequence)
            {
                if(iCmdID > 0 && !registeredCommands.contains(iCmdID))
                {
                    Register2Map(iCmdID);
                    registeredCommands.insert(iCmdID);
                }
            }
        }
    }
}

void CMotorPosition::_UnregisterCommandsFromConfig()
{
    // 检查配置文件是否存在
    if(!QFile::exists(m_strConfigPath))
    {
        return;
    }
    
    // 用于收集所有指令ID的集合，避免重复反注册
    QSet<int> unregisteredCommands;
    
    // 遍历所有模块配置节
    QStringList strGroups = m_pSettings->childGroups();
    for(const QString &strGroup : strGroups)
    {
        // 跳过空组名
        if(strGroup.trimmed().isEmpty())
            continue;
            
        // 读取该模块的所有位置配置
        for(int i = 1; i <= 20; i++)
        {
            QString strPrefix = QString("Position%1_").arg(i);
            QString strName = m_pSettings->value(strGroup + "/" + strPrefix + "Name", "").toString();
            
            // 如果名称为空，说明没有更多位置了
            if(strName.isEmpty())
                break;
                
            QString strSequence = m_pSettings->value(strGroup + "/" + strPrefix + "Sequence", "").toStringList().join(",");
            
            // 解析序列字符串，获取所有指令ID
            QList<int> listSequence = _ParseSequenceString(strSequence);
            for(int iCmdID : listSequence)
            {
                if(iCmdID > 0 && !unregisteredCommands.contains(iCmdID))
                {
                    UnRegister2Map(iCmdID);
                    unregisteredCommands.insert(iCmdID);
                }
            }
        }
    }
}

QList<int> CMotorPosition::_ParseSequenceString(const QString &strSequence)
{
    QList<int> listSequence;
    QStringList strList = strSequence.split(",", QString::SkipEmptyParts);
    
    for(const QString &str : strList)
    {
        bool bOk;
        int iValue = str.trimmed().toInt(&bOk);
        if(bOk)
        {
            listSequence.append(iValue);
        }
    }
    
    return listSequence;
}

void CMotorPosition::_LoadModulePositions(const QString &strSection, int iModuleIndex)
{
    if(iModuleIndex < 0 || iModuleIndex >= m_MotorModules.size())
        return;

    SMotorModule &module = m_MotorModules[iModuleIndex];
    module.listPositions.clear();

    qDebug() << "开始加载模块位置，节名:" << strSection;

    // 检查配置文件中是否存在该节
    QStringList allGroups = m_pSettings->childGroups();
    qDebug() << "配置文件中所有节名:" << allGroups;

    if(!allGroups.contains(strSection))
    {
        qDebug() << "配置文件中未找到节:" << strSection;
        return;
    }

    // 读取位置参数，最多支持20个位置
    for(int i = 1; i <= 20; i++)
    {
        QString strPrefix = QString("Position%1_").arg(i);
        QString strKey = strSection + "/" + strPrefix + "Name";
        QString strName = m_pSettings->value(strKey, "").toString();

        // 如果名称为空，说明没有更多位置了
        if(strName.isEmpty())
            break;

        QString strSequence = m_pSettings->value(strSection + "/" + strPrefix + "Sequence", "").toStringList().join(",");
        int iMotorID = m_pSettings->value(strSection + "/" + strPrefix + "MotorID", 0).toInt();
        int iCompensateIndex = m_pSettings->value(strSection + "/" + strPrefix + "CompensateIndex", 0).toInt();
        int iDefaultCompensate = m_pSettings->value(strSection + "/" + strPrefix + "DefaultCompensate", 0).toInt();

        // 解析序列字符串
        QList<int> listSequence = _ParseSequenceString(strSequence);

        // 创建位置对象
        SMotorPosition position;
        position.strActionName = strName;
        position.listIDSequence = listSequence;
        position.iMotorID = iMotorID;
        position.iCompensateIndex = iCompensateIndex;
        position.iDefaultCompensate = iDefaultCompensate;
        position.iCurrentCompensate = iDefaultCompensate;
        position.pMoveBtn = nullptr;

        module.listPositions.append(position);
    }

    qDebug() << "模块" << strSection << "最终加载了" << module.listPositions.size() << "个位置";
}

void CMotorPosition::_ValidateCompensateStep(int iModuleIndex, const QString &strValue)
{
    if(iModuleIndex < 0 || iModuleIndex >= m_MotorModules.size())
        return;
        
    SMotorModule &module = m_MotorModules[iModuleIndex];
    
    // 如果输入为空，不做处理
    if(strValue.isEmpty())
        return;
        
    // 验证输入是否为有效数字
    bool bOk = false;
    int iValue = strValue.toInt(&bOk);
    
    if(!bOk)
    {
        // 输入无效，恢复到默认值
        module.pCompensateStepComboBox->setCurrentText("1000");
        _AddDebugRecord(tr("补偿步长输入无效，已恢复默认值1000"));
        return;
    }
    
    // 检查数值范围
    if(iValue < 1)
    {
        // 值过小，设置为最小值
        module.pCompensateStepComboBox->setCurrentText("1");
        _AddDebugRecord(tr("补偿步长不能小于1，已设置为1"));
        return;
    }
    
    if(iValue > 100000)
    {
        // 值过大，设置为最大值
        module.pCompensateStepComboBox->setCurrentText("100000");
        _AddDebugRecord(tr("补偿步长不能大于100000，已设置为100000"));
        return;
    }
    
    // 输入有效，更新边框颜色显示正常状态
    module.pCompensateStepComboBox->setStyleSheet(
        "QComboBox {"
        "    border: 2px solid #4CAF50;"  // 绿色边框表示有效
        "    border-radius: 5px;"
        "    padding: 2px 5px;"
        "    background-color: white;"
        "    font-size: 12px;"
        "    color: #333333;"
        "    selection-background-color: #2196F3;"
        "    selection-color: white;"
        "}"
        "QComboBox QLineEdit {"
        "    border: none;"
        "    background: transparent;"
        "    selection-background-color: #2196F3;"
        "    selection-color: white;"
        "}"
        "QComboBox:hover {"
        "    border: 2px solid #4CAF50;"
        "    background-color: #F5F5F5;"
        "}"
        "QComboBox:focus {"
        "    border: 2px solid #4CAF50;"
        "    background-color: #F5F5F5;"
        "}"
        "QComboBox::drop-down {"
        "    subcontrol-origin: padding;"
        "    subcontrol-position: top right;"
        "    width: 25px;"
        "    border: none;"
        "    border-top-right-radius: 5px;"
        "    border-bottom-right-radius: 5px;"
        "}"
        "QComboBox:hover::drop-down {"
        "    background-color: #F5F5F5;"
        "}"
        "QComboBox:focus::drop-down {"
        "    background-color: #F5F5F5;"
        "}"
        "QComboBox::down-arrow {"
        "    width: 16px;"
        "    height: 16px;"
        "    image: url(:/image/ico/login/commod1.png);"
        "    padding: 0px 4px 0px 0px;"
        "}"
        "QComboBox QAbstractItemView {"
        "    border: 1px solid #CCCCCC;"
        "    background-color: white;"
        "    selection-background-color: #2196F3;"
        "    selection-color: white;"
        "    outline: none;"
        "}"
        "QComboBox QAbstractItemView::item {"
        "    min-height: 25px;"
        "    padding: 3px 8px;"
        "    border: none;"
        "}"
        "QComboBox QAbstractItemView::item:hover {"
        "    background-color: #E3F2FD;"
        "    color: #2196F3;"
        "}"
        "QComboBox QAbstractItemView::item:selected {"
        "    background-color: #2196F3;"
        "    color: white;"
        "}"
    );
    
    // 500毫秒后恢复正常颜色
    QTimer::singleShot(500, [this, iModuleIndex]() {
        if(iModuleIndex >= 0 && iModuleIndex < m_MotorModules.size())
        {
            SMotorModule &mod = m_MotorModules[iModuleIndex];
            mod.pCompensateStepComboBox->setStyleSheet(
                "QComboBox {"
                "    border: 2px solid #CCCCCC;"
                "    border-radius: 5px;"
                "    padding: 2px 5px;"
                "    background-color: white;"
                "    font-size: 12px;"
                "    color: #333333;"
                "    selection-background-color: #2196F3;"
                "    selection-color: white;"
                "}"
                "QComboBox QLineEdit {"
                "    border: none;"
                "    background: transparent;"
                "    selection-background-color: #2196F3;"
                "    selection-color: white;"
                "}"
                "QComboBox:hover {"
                "    border: 2px solid #2196F3;"
                "    background-color: #F5F5F5;"
                "}"
                "QComboBox:focus {"
                "    border: 2px solid #2196F3;"
                "    background-color: #F5F5F5;"
                "}"
                "QComboBox::drop-down {"
                "    subcontrol-origin: padding;"
                "    subcontrol-position: top right;"
                "    width: 25px;"
                "    border: none;"
                "    border-top-right-radius: 5px;"
                "    border-bottom-right-radius: 5px;"
                "}"
                "QComboBox:hover::drop-down {"
                "    background-color: #F5F5F5;"
                "}"
                "QComboBox:focus::drop-down {"
                "    background-color: #F5F5F5;"
                "}"
                "QComboBox::down-arrow {"
                "    width: 16px;"
                "    height: 16px;"
                "    image: url(:/image/ico/login/commod1.png);"
                "    padding: 0px 4px 0px 0px;"
                "}"
                "QComboBox QAbstractItemView {"
                "    border: 1px solid #CCCCCC;"
                "    background-color: white;"
                "    selection-background-color: #2196F3;"
                "    selection-color: white;"
                "    outline: none;"
                "}"
                "QComboBox QAbstractItemView::item {"
                "    min-height: 25px;"
                "    padding: 3px 8px;"
                "    border: none;"
                "}"
                "QComboBox QAbstractItemView::item:hover {"
                "    background-color: #E3F2FD;"
                "    color: #2196F3;"
                "}"
                "QComboBox QAbstractItemView::item:selected {"
                "    background-color: #2196F3;"
                "    color: white;"
                "}"
            );
        }
    });
} 