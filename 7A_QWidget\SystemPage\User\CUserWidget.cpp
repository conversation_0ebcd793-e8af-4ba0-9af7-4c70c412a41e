#include "CUserWidget.h"
#include <QDateTime>
#include <QBoxLayout>
#include <QHeaderView>

#include "CUserDB.h"
#include "CMessageBox.h"
#include "PublicConfig.h"
#include "PublicFunction.h"

CUserWidget::CUserWidget(QWidget *parent) : QWidget(parent), m_strTipsText(tr("提示"))
{
    _InitWidget();
    _InitLayout();

    m_pCAddEditUserWidget = new CAddEditUserWidget(this);
    connect(m_pCAddEditUserWidget, &CAddEditUserWidget::SignalConfirm, this, &CUserWidget::_SlotConfirm);
    m_pCAddEditUserWidget->setVisible(false);

    LoadQSS(this, ":/qss/qss/system/user.qss");

    _LoadAllUser2Table();
}

void CUserWidget::showEvent(QShowEvent *pEvent)
{
    if(CPublicConfig::GetInstance()->GetLoginLevel() < eUser_Admin)
    {
        m_pAddBtn->setVisible(false);
        m_pDelBtn->setVisible(false);
    }
    else
    {
        m_pAddBtn->setVisible(true);
        m_pDelBtn->setVisible(true);
    }

    _RefrashTable();

    QWidget::showEvent(pEvent);
}

void CUserWidget::_RefrashTable()
{
    QString strUser = CPublicConfig::GetInstance()->GetLoginUser();
    if(strUser == m_strLastLoginUser)
        return;
    m_strLastLoginUser = strUser;

    if(CPublicConfig::GetInstance()->GetLoginLevel() < eUser_Admin)
        _LoadOneUser2Table(strUser);
    else
        _LoadAllUser2Table();
}

void CUserWidget::_SlotConfirm(bool bAddUser, const QStringList &strList)
{
    qDebug()<<Q_FUNC_INFO<<bAddUser<<strList;
    QString strName = strList.at(0);
    QString strPwd = strList.at(1);
    QString strStatus = strList.at(2);

    if(bAddUser)
    {
        if(gk_strFactoryUserName == strName || gk_strFlyUserName == strName || gk_strMaintianUserName == strName)
        {
            ShowInformation(this, m_strTipsText, tr("禁止添加此账号") + " " + strName);
            return;
        }

        QString strCurrentTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
        QStringList strAddList = {strName, strPwd, strCurrentTime, strStatus};
        CUserDB::GetInstance()->AddUserData(strAddList);

        int iRow = m_pTableWidget->rowCount();
        m_pTableWidget->setRowCount(iRow + 1);
        strAddList.clear();
        strAddList << QString::number(iRow + 1) << strName << strCurrentTime << strStatus;
        _AddList2Table(iRow, strAddList);
        m_pTableWidget->selectRow(iRow);
        return;
    }

    CUserDB::GetInstance()->UpdateUserPasswordNoteByName(strName, strPwd, strStatus);

    int iRow = m_pTableWidget->currentRow();

    if(CAddEditUserWidget::m_strDisableText == strStatus)
        m_pTableWidget->item(iRow, 3)->setText(tr("已禁用"));
    else
        m_pTableWidget->item(iRow, 3)->setText(tr("已激活"));
}

void CUserWidget::_SlotAddBtn()
{
    m_pCAddEditUserWidget->SetData(true, QStringList());
    m_pCAddEditUserWidget->show();
}

void CUserWidget::_SlotEditBtn()
{
    int iRow = m_pTableWidget->currentRow();
    if(iRow < 0)
    {
        ShowInformation(this, m_strTipsText, tr("请先选择"));
        return;
    }

    QString strUser = m_pTableWidget->item(iRow, 1)->text();
    if(CPublicConfig::GetInstance()->GetLoginLevel() < eUser_Admin)
    {
        if(CPublicConfig::GetInstance()->GetLoginUser() != strUser)
        {
            ShowInformation(this, m_strTipsText, tr("普通用户只能修改自身账号的信息"));
            return;
        }
    }

    QStringList strList = CUserDB::GetInstance()->GetUserDataListByName(strUser);
    if(strList.size() >= 4)
    {
        strList.pop_front();
        strList.removeAt(2);
    }
    m_pCAddEditUserWidget->SetData(false, strList);
    m_pCAddEditUserWidget->show();
}

void CUserWidget::_SlotDelBtn()
{
    int iRow = m_pTableWidget->currentRow();
    if(iRow < 0)
    {
        ShowInformation(this, m_strTipsText, tr("请先选择"));
        return;
    }

    QString strUserName = m_pTableWidget->item(iRow, 1)->text();
    if(gk_strAdminUserName == strUserName)
    {
        if(CPublicConfig::GetInstance()->GetLoginLevel() < eUser_Maintain)
        {
            ShowInformation(this, m_strTipsText, tr("Admin账号不允许删除"));
            return;
        }
    }
    if(gk_strMaintianUserName == strUserName)
    {
        if(CPublicConfig::GetInstance()->GetLoginLevel() < eUser_Factory)
        {
            ShowInformation(this, m_strTipsText, tr("maintain账号不允许删除"));
            return;
        }
    }

    int iBtnType = ShowQuestion(this, tr("提示"), tr("确定删除此用户吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    CUserDB::GetInstance()->DeleteUserByName(strUserName);
    m_pTableWidget->removeRow(iRow);
    ResortTableWidget(m_pTableWidget);
}

void CUserWidget::_LoadOneUser2Table(QString strUser)
{
    QStringList strList = CUserDB::GetInstance()->GetUserDataListByName(strUser);
    m_pTableWidget->clearContents();
    if(strList.size() < 5)
        return;
    strList[0] = "1";
    strList.removeAt(2);
    m_pTableWidget->setRowCount(1);
    _AddList2Table(0, strList);
    m_pTableWidget->selectRow(0);
}

void CUserWidget::_LoadAllUser2Table()
{
    QList<QStringList> strAllList = CUserDB::GetInstance()->GetAllUserDataList();
    m_pTableWidget->clearContents();

    int iUserLevel = CPublicConfig::GetInstance()->GetLoginLevel();
    if(iUserLevel < eUser_Maintain)
        m_pTableWidget->setRowCount(strAllList.size() - 1); //不显示维护账号
    else
        m_pTableWidget->setRowCount(strAllList.size());

    int index = 0;
    for(int iRow=0; iRow<strAllList.size(); iRow++)
    {
        QStringList oneList = strAllList.at(iRow);
        if(oneList.size() < 5)
            continue;

        if(gk_strMaintianUserName == oneList.at(1))
        {
            if(iUserLevel < eUser_Maintain)
            {
                continue;
            }
        }
        oneList[0] = QString::number(index + 1);
        oneList.removeAt(2);
        _AddList2Table(index, oneList);

        index++;
    }
}

void CUserWidget::_AddList2Table(int iRow, QStringList strList)
{
    int iMin = qMin(strList.size(), m_pTableWidget->columnCount());
    for(int i=0; i<iMin; i++)
    {
        QTableWidgetItem *pItem = new QTableWidgetItem;
        QString strText = strList.at(i);
        if(3 == i)
        {
            if(CAddEditUserWidget::m_strDisableText == strText)
                strText = tr("已禁用");
            else
                strText = tr("已激活");
        }
        pItem->setText(strText);
        pItem->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(iRow, i, pItem);
    }
}

void CUserWidget::_InitWidget()
{
    m_pCSysTtileLabelWidget = new CSysFirstTitleWidget(tr("系统设置"), tr("用户管理"));
    connect(m_pCSysTtileLabelWidget, &CSysFirstTitleWidget::SignalTitlePress, this, &CUserWidget::SignalReturn);

    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(1684, 904);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");

    m_pTableWidget = new QTableWidget;
    m_pTableWidget->setFixedSize(1636, 600);
    m_pTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pTableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_pTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pTableWidget->setWordWrap(true);
    m_pTableWidget->setFocusPolicy(Qt::NoFocus);
    //m_pTableWidget->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTableWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTableWidget->setAlternatingRowColors(true);
    m_pTableWidget->setShowGrid(false);

    QStringList strTitleList = {tr("序号"), tr("用户名"), tr("创建时间"), tr("状态")};
    m_pTableWidget->setColumnCount(strTitleList.size());
    m_pTableWidget->setHorizontalHeaderLabels(strTitleList);
    m_pTableWidget->setRowCount(8);

    QHeaderView *pVerticalHeader = m_pTableWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(50);

    QHeaderView *pHorizontalHeader = m_pTableWidget->horizontalHeader();
    pHorizontalHeader->resizeSection(0, 100);
    pHorizontalHeader->setSectionResizeMode(1, QHeaderView::Stretch);
    pHorizontalHeader->setSectionResizeMode(2, QHeaderView::Stretch);
    pHorizontalHeader->setSectionResizeMode(3, QHeaderView::Stretch);
    pHorizontalHeader->setDisabled(true);

    int iBtnWidth = 150;
    if(eLanguage_German == gk_iLanguage)
        iBtnWidth = 165;

    m_pReturnBtn = new QPushButton(tr("返回"));
    m_pReturnBtn->setFixedSize(iBtnWidth, 56);
    m_pReturnBtn->setObjectName("CancelBtn");
    connect(m_pReturnBtn, &QPushButton::clicked, this, &CUserWidget::SignalReturn);

    m_pAddBtn = new QPushButton(tr("新增"));
    m_pAddBtn->setFixedSize(iBtnWidth, 56);
    connect(m_pAddBtn, &QPushButton::clicked, this, &CUserWidget::_SlotAddBtn);

    m_pEditBtn = new QPushButton(tr("编辑"));
    m_pEditBtn->setFixedSize(iBtnWidth, 56);
    connect(m_pEditBtn, &QPushButton::clicked, this, &CUserWidget::_SlotEditBtn);

    m_pDelBtn = new QPushButton(tr("删除"));
    m_pDelBtn->setFixedSize(iBtnWidth, 56);
    connect(m_pDelBtn, &QPushButton::clicked, this, &CUserWidget::_SlotDelBtn);
}

void CUserWidget::_InitLayout()
{
    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(50);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pReturnBtn);
    pBtnLayout->addWidget(m_pAddBtn);
    pBtnLayout->addWidget(m_pEditBtn);
    pBtnLayout->addWidget(m_pDelBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pBackLayout = new QVBoxLayout;
    pBackLayout->setMargin(24);
    pBackLayout->setSpacing(0);
    pBackLayout->addWidget(m_pTableWidget, 0, Qt::AlignHCenter);
    pBackLayout->addStretch(1);
    pBackLayout->addLayout(pBtnLayout);
    m_pBackgroundLabel->setLayout(pBackLayout);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pCSysTtileLabelWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pBackgroundLabel);
    this->setLayout(pLayout);
}
