#ifndef ROLLINGBOX_H
#define ROLLINGBOX_H
#include <QListView>
#include <QMouseEvent>
#include <QItemDelegate>
#include <QPainter>

class ItemDelegate : public QItemDelegate
{
    Q_OBJECT

public:
   explicit ItemDelegate(QObject * parent = 0):
        QItemDelegate(parent)
    {
    }
    void paint(QPainter *painter, const QStyleOptionViewItem &option, const QModelIndex &index) const
    {
        QStyleOptionViewItem  viewOption(option);
        //高亮显示与普通显示时的前景色一致（即选中行和为选中时候的文字颜色一样）
        viewOption.palette.setColor(QPalette::HighlightedText, index.data(Qt::ForegroundRole).value<QColor>().red());
//        painter->drawText(viewOption.rect.width()/2,,QString::number(index.row()));
        QItemDelegate::paint(painter, viewOption, index);
    }
};
class RollingBox : public QListView
{

    Q_OBJECT

public:
   explicit RollingBox(QWidget*parent = NULL);

protected:
    virtual void mousePressEvent(QMouseEvent*);

    virtual void mouseMoveEvent(QMouseEvent*);

    virtual void mouseReleaseEvent(QMouseEvent*);

private:
    void _InitUi();

private:
    int m_mousePressPosX;

    bool m_moveFlag;

signals:

    void SelectText(const QString&);

};

#endif // ROLLINGBOX_H
