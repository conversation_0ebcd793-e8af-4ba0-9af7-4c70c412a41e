#include "CHeartBeat.h"
#include "PublicParams.h"

CHeartBeat *CHeartBeat::m_spInstance = nullptr;

CHeartBeat *CHeartBeat::GetInstance()
{
    if(nullptr == m_spInstance)
        m_spInstance = new CHeartBeat;
    return m_spInstance;
}

CHeartBeat::CHeartBeat()
{
    Register2Map(Method_heart_beat);

    for(int i=0; i<gk_iMachineCount; i++)
    {
        QTimer *pTimer = new QTimer(this);
        pTimer->setProperty("iMachineID", i);
        connect(pTimer, &QTimer::timeout, this, &CHeartBeat::_SlotCheckConnectTimeout);

        SDeviceHeartbeatStruct *pStruct = new SDeviceHeartbeatStruct;
        pStruct->pCheckTimer = pTimer;
        m_sDeviceHeartbeatList.push_back(pStruct);
    }
}

CHeartBeat::~CHeartBeat()
{
    UnRegister2Map(Method_heart_beat);
}

void CHeartBeat::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    Q_UNUSED(iResult);

    if(Method_heart_beat == iMethodID)
        _ParseHeartBeat(iMachineID, qVarData);
}

void CHeartBeat::ResetStatus(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= m_sDeviceHeartbeatList.size())
        return;

    SDeviceHeartbeatStruct *pStruct = m_sDeviceHeartbeatList.at(iMachineID);
    pStruct->bDeviceConnect = true;
    pStruct->iDisconnectTimes = 0;
    pStruct->pCheckTimer->stop();
    pStruct->pCheckTimer->start(pStruct->iHeartBeatSpan + 3000);

    emit SignalResetItemStatus(iMachineID);
}

SDeviceHeartbeatStruct *CHeartBeat::GetDeviceHearbeatStruct(int iMachineID) const
{
    if(iMachineID < 0 || iMachineID >= m_sDeviceHeartbeatList.size())
        return nullptr;

    return m_sDeviceHeartbeatList.at(iMachineID);
}

void CHeartBeat::_SlotCheckConnectTimeout()
{
    int iMachineID = sender()->property("iMachineID").toInt();
    if(iMachineID < 0 || iMachineID >= m_sDeviceHeartbeatList.size())
        return;

    SDeviceHeartbeatStruct *pStruct = m_sDeviceHeartbeatList.at(iMachineID);
    pStruct->iDisconnectTimes++;
    qDebug()<<QString("%1#心跳失联次数:%2").arg(iMachineID + 1).arg(pStruct->iDisconnectTimes);
    if(pStruct->iDisconnectTimes >= 3)
    {
        qDebug()<<QString("%1#连续3次没有心跳,通信故障").arg(iMachineID + 1);
        pStruct->bDeviceConnect = false;
        pStruct->iDeviceStatus = eDeviceDisconnect;
        emit SignalUpdateItemStatus(iMachineID, eDeviceDisconnect, false);
    }
}

void CHeartBeat::_ParseHeartBeat(int iMachineID, const QVariant &qVarData)
{
    if(iMachineID < 0 || iMachineID >= m_sDeviceHeartbeatList.size())
        return;

    QVariantList qVarList = qVarData.toList();
    if(qVarList.size() < 3)
        return;

    SDeviceHeartbeatStruct *pStruct = m_sDeviceHeartbeatList.at(iMachineID);
    pStruct->bDeviceConnect = true;
    pStruct->iHeartBeatSpan = qVarList.at(0).toInt();
    pStruct->iDeviceStatus = qVarList.at(1).toInt();
    pStruct->bCardboxExist = qVarList.at(2).toInt(); // 1:有卡盒; 0:无卡盒
    pStruct->iDisconnectTimes = 0;

    pStruct->pCheckTimer->stop();
    pStruct->pCheckTimer->start(pStruct->iHeartBeatSpan + 3000);

    emit SignalUpdateItemStatus(iMachineID, DeviceStatus(pStruct->iDeviceStatus), pStruct->bCardboxExist);

    if(!pStruct->bSetRTCTime)
    {
        pStruct->bSetRTCTime = true;
        QString strSinceTime = "1970-01-01 00:00:00";
        QDateTime dateSinceTime = QDateTime::fromString(strSinceTime,"yyyy-MM-dd hh:mm:ss");
        qint64 qSince = dateSinceTime.secsTo(QDateTime::currentDateTime());
        QVariantList qVarList = {qSince};
        QString strCmd = CCmdBase::GetJsonCmdString(Method_rtc, qVarList);
        SendJsonCmd(iMachineID, Method_rtc, strCmd);
    }
}
