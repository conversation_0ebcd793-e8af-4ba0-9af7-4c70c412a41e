#include <QTimer>
#include <QDebug>
#include "CLightOneTiming.h"
#include "CMessageBox.h"

const QList<int> cLightTiming = {Method_OFSTRST, Method_FLMST, Method_OFCOMP, Method_FLMSP};
const QList<int> cLightTimingType2 = {Method_PCRPR, Method_PM3PR, Method_OFSTRST, Method_FLMST, Method_OFCOMP, Method_FLMSP, Method_PCRRST, Method_PM2PR};

CLightOneTiming::CLightOneTiming(QWidget *parent)
    : QWidget(parent)
{
    Register2Map(Method_OFSTRST);
    Register2Map(Method_OFCOMP);
    Register2Map(Method_FLMST);
    Register2Map(Method_FLMSP);

    m_lightTimingInfo.timingState = eLightTimingState_Idle;
}

CLightOneTiming::~CLightOneTiming()
{
    UnRegister2Map(Method_OFSTRST);
    UnRegister2Map(Method_OFCOMP);
    UnRegister2Map(Method_FLMST);
    UnRegister2Map(Method_FLMSP);
}

void CLightOneTiming::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if (m_lightTimingInfo.timingState == eLightTimingState_WaitRet) {
        if(iMethodID == Method_OFSTRST) {
            if(iResult == 0) {
                timingSendNext(iMachineID);
            }
        }
        else if(iMethodID == Method_OFCOMP) {
            if(iResult == 0) {
                timingSendNext(iMachineID);
            } 
        }
        else if(iMethodID == Method_FLMST) {
            if(iResult == 0) {
                timingSendNext(iMachineID);
            } 
        }
        else if(iMethodID == Method_FLMSP) {
            if(iResult == 0) {
                timingSendNext(iMachineID);
            } 
        }
    }
}

void CLightOneTiming::startTiming(int iMachineID, int cycle)
{
    m_lightTimingInfo.machineID = iMachineID;
    m_lightTimingInfo.index = 0;
    m_iCurrentTiming = cLightTiming;
    int iMethodID = m_iCurrentTiming[m_lightTimingInfo.index];
    QString strCmd = GetJsonCmdString(iMethodID);
    SendJsonCmd(m_lightTimingInfo.machineID, iMethodID, strCmd);
    m_lightTimingInfo.methodID = iMethodID;
    m_lightTimingInfo.bMethodReply = false;
    m_lightTimingInfo.timingState = eLightTimingState_WaitRet;
    m_lightTimingInfo.timingLength = m_iCurrentTiming.size();
    m_lightTimingInfo.cycle = cycle;
    m_lightTimingInfo.cycleCount = 1;
}

void CLightOneTiming::startTimingType2(int iMachineID, int cycle)
{
    m_lightTimingInfo.machineID = iMachineID;
    m_lightTimingInfo.index = 0;
    m_iCurrentTiming = cLightTimingType2;
    int iMethodID = m_iCurrentTiming[m_lightTimingInfo.index];
    QString strCmd = GetJsonCmdString(iMethodID);
    SendJsonCmd(m_lightTimingInfo.machineID, iMethodID, strCmd);
    m_lightTimingInfo.methodID = iMethodID;
    m_lightTimingInfo.bMethodReply = false;
    m_lightTimingInfo.timingState = eLightTimingState_WaitRet;
    m_lightTimingInfo.timingLength = m_iCurrentTiming.size();
    m_lightTimingInfo.cycle = cycle;
    m_lightTimingInfo.cycleCount = 1;
}


void CLightOneTiming::timingSendNext(int iMachineID)
{
    m_lightTimingInfo.machineID = iMachineID;
    m_lightTimingInfo.index++;
    if (m_lightTimingInfo.index >= m_lightTimingInfo.timingLength) {
        
        if (m_lightTimingInfo.cycleCount < m_lightTimingInfo.cycle) {
            m_lightTimingInfo.cycleCount++;
            m_lightTimingInfo.index = 0;
        }
        else {
            m_lightTimingInfo.timingState = eLightTimingState_End;
            emit this->SignalTimingEnd();
            return;
        }
    }
    
    int iMethodID = m_iCurrentTiming[m_lightTimingInfo.index];
    QString strCmd;
    
    if (iMethodID == Method_FLMST) {
        QVariantList qVarList;
        qVarList.push_back(2);
        strCmd = GetJsonCmdString(iMethodID, qVarList);
    }
    else
    {
        strCmd = GetJsonCmdString(iMethodID); 
    }
    
    SendJsonCmd(m_lightTimingInfo.machineID, iMethodID, strCmd);
    m_lightTimingInfo.methodID = iMethodID;
    m_lightTimingInfo.bMethodReply = false;
    m_lightTimingInfo.timingState = eLightTimingState_WaitRet;

}