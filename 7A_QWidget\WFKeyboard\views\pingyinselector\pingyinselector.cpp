#include "pingyinselector.h"
#include "ui_pingyinselector.h"

#include <QScrollBar>
#include <QChar>
#include <QRegExp>
#include <QDebug>
#include <QStringListModel>

#include "../keyboard.h"
#include "3rd/plugins/pinyin/pinyininputmethod_p.h"


#define KEYBOARD_INPUT_COUNT 9

class PingYinSelector::PrivateData
{
public:
    PrivateData(PingYinSelector *p, KeyBoard *k)
        : parent(p)
        , keyBoard(k)
        , isKeyBoardInput(false)
        , page(0)
        , isPaging(false)
        , pageSize(KEYBOARD_INPUT_COUNT)
    {
        keyBoard->stackedWidget_Normal_Header->addWidget(parent);
    }

    void Init();

    QString SplitAndProcessStr(const QString&);

    void UpdatePageBtnEnable();

public:
    Ui::PingYinSelector *ui;

    PingYinSelector *parent;

    KeyBoard* keyBoard;

    PinyinInputMethod* im;

    QStringListModel* listModel;

    QString preText;

    bool isKeyBoardInput;

    int page;

    bool isPaging;

    int pageSize;
};

void PingYinSelector::PrivateData::Init()
{
    ui->label_pinyin->clear();

    im = new PinyinInputMethod(NULL);

    parent->connect(im,SIGNAL(SignalCandidatesList(QList<QString>)),parent,SLOT(SlotCandidatesList(QList<QString>)));

    listModel = new QStringListModel(parent);
    ui->listView->setModel(listModel);

    parent->connect(ui->listView,SIGNAL(SelectText(QString)),parent,SLOT(SlotSelectText(QString)));
    parent->connect(ui->listView->horizontalScrollBar(),SIGNAL(valueChanged(int)),parent,SLOT(SlotScroolValueChange(int)));
    parent->connect(ui->toolButton_prevPage,SIGNAL(clicked()),parent,SLOT(SlotPrevBtnClicked()));
    parent->connect(ui->toolButton_nextPage,SIGNAL(clicked()),parent,SLOT(SlotNextBtnClicked()));

    ui->toolButton_prevPage->setVisible(isPaging);
    ui->toolButton_nextPage->setVisible(isPaging);
}

QString PingYinSelector::PrivateData::SplitAndProcessStr(const QString &tmpStr)
{
   QString str = tmpStr;
   QRegExp reg("^[0-9]\.");
   str = str.remove(reg);
   return str;
}

void PingYinSelector::PrivateData::UpdatePageBtnEnable()
{
    if(page <= 0)
    {
        ui->toolButton_prevPage->setEnabled(false);
    }
    else
    {
        ui->toolButton_prevPage->setEnabled(true);
    }

    int totalNum = im->GetTotalChoiseNum();

    if(totalNum <= (page+1) * pageSize)
    {
        ui->toolButton_nextPage->setEnabled(false);
    }
    else
    {
        ui->toolButton_nextPage->setEnabled(true);
    }
}

void PingYinSelector::SetActivate(bool bActive)
{
    if (bActive)
    {
        md->keyBoard->stackedWidget_Normal_Header->setCurrentWidget(this);
    }
    else
    {
        md->keyBoard->stackedWidget_Normal_Header->setCurrentWidget(md->keyBoard->pageNormalHeader_Num);
    }
}

PingYinSelector::PingYinSelector(KeyBoard *keyboard)
    : QWidget(NULL)
    , md(new PrivateData(this, keyboard))
{
    md->ui = new Ui::PingYinSelector;
    md->ui->setupUi(this);
    md->Init();
}

PingYinSelector::~PingYinSelector()
{
    delete md;
}

void PingYinSelector::InPutChar(const QChar* tmpChar)
{
    QString str = QString(tmpChar);
    if (str == " " || str=="." || str=="-")
    {
        md->keyBoard->SetHanziText(str);
        return;
    }

    md->preText += str;
    md->ui->label_pinyin->setText(md->preText);
    md->ui->listView->horizontalScrollBar()->setValue(0);
    md->im->SetInputState();

//    im->KeyEventFun((Qt::Key)tmpChar->toUpper().toAscii(),str,Qt::NoModifier);
    if(md->isKeyBoardInput || md->isPaging)
        md->page = 0;

    md->im->KeyEventFun((Qt::Key)tmpChar->toUpper().unicode(),str,Qt::NoModifier);
}

void PingYinSelector::DeleteInputChar()
{
    if(md->preText.isEmpty())
        return;

    md->preText.resize(md->preText.size() - 1);
    md->ui->label_pinyin->setText(md->preText);
    md->ui->listView->horizontalScrollBar()->setValue(0);

    if(md->isKeyBoardInput || md->isPaging)
        md->page = 0;

    md->im->KeyEventFun(Qt::Key_Backspace,"",Qt::NoModifier);
}

void PingYinSelector::SlotCandidatesList(QList<QString> tmpStrList)
{
    if(md->isKeyBoardInput || md->isPaging)
    {
        QStringList keyBoardStrList;
        for(int i = 0; i < (tmpStrList.count() >= md->pageSize?md->pageSize:tmpStrList.count());i++)
        {
            keyBoardStrList.append(QString::number(i + 1) + "." + tmpStrList[i]);
        }

        md->listModel->setStringList(keyBoardStrList);

        md->UpdatePageBtnEnable();
        return;
    }

    md->listModel->setStringList(QStringList(tmpStrList));
}

void PingYinSelector::SlotScroolValueChange(int value)
{
    if(value >= md->ui->listView->horizontalScrollBar()->maximum())
    {
       int row = md->listModel->rowCount();
       md->im->SelectListData(row);
    }
}

void PingYinSelector::CandidateWordPageUp()
{
    if(md->im->GetTotalChoiseNum() <= 0)
        return;

    md->page--;

    if(md->page < 0)
        md->page = 0;

    md->im->TurnPage(md->page,md->pageSize);
}

void PingYinSelector::CandidateWordPageDown()
{
    if(md->im->GetTotalChoiseNum() <= 0)
        return;

    md->page++;

    int count = md->pageSize;
    int totalNum = md->im->GetTotalChoiseNum();
    if(totalNum < md->page * count)
    {
        md->page--;
        return;
    }
    md->im->TurnPage(md->page,count);
}

void PingYinSelector::SlotSelectText(const QString &text)
{
    QString str = text;
    if(md->isKeyBoardInput || md->isPaging)
    {
        str = md->SplitAndProcessStr(str);
    }

    md->keyBoard->SetHanziText(str);

    Reset();
}

void PingYinSelector::SlotPrevBtnClicked()
{
    CandidateWordPageUp();
}

void PingYinSelector::SlotNextBtnClicked()
{
    CandidateWordPageDown();
}

QString PingYinSelector::GetCandidateWord(const int &position)
{
    QString tmpStr = md->listModel->stringList().value(position);
    if(md->isKeyBoardInput || md->isPaging)
    {
        tmpStr = md->SplitAndProcessStr(tmpStr);
    }

    Reset();
    return tmpStr;
}

void PingYinSelector::SetKeyboardInputFlag(const bool &flag)
{
    md->isKeyBoardInput = flag;

    if(md->isKeyBoardInput || md->isPaging)
    {
        md->im->SetFirstCandiateCount(md->pageSize,true);
    }
    else
    {
        int textCount = md->ui->listView->width()/40;
        md->im->SetFirstCandiateCount(textCount);
    }
}

void PingYinSelector::showEvent(QShowEvent */*e*/)
{
    int textCount = md->ui->listView->width()/40;
    md->im->SetFirstCandiateCount(textCount);
}

const QString &PingYinSelector::PinYin() const
{
    return md->preText;
}

void PingYinSelector::Reset()
{
    md->preText.clear();
    md->ui->label_pinyin->clear();

    if(md->isKeyBoardInput || md->isPaging)
    {
        md->page = 0;
    }

    md->im->KeyEventFun(Qt::Key_Return,"",Qt::NoModifier);
}

void PingYinSelector::SetPagingEnabled(const bool& enable)
{
    md->isPaging = enable;
    md->ui->toolButton_prevPage->setVisible(md->isPaging);
    md->ui->toolButton_nextPage->setVisible(md->isPaging);

    if(md->isPaging)
    {
        disconnect(md->ui->listView->horizontalScrollBar(),SIGNAL(valueChanged(int)),this,SLOT(SlotScroolValueChange(int)));
        md->ui->listView->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
        md->ui->listView->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    }
    else
    {
        connect(md->ui->listView->horizontalScrollBar(),SIGNAL(valueChanged(int)),this,SLOT(SlotScroolValueChange(int)));
    }
}

void PingYinSelector::SetPageSize(const int& size)
{
    if(size <= 0)
        return;

    md->pageSize = size;
}
