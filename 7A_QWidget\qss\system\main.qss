QLabel
{
    color: #353E4E;
    font-size: 32px;
    font-weight: bold;
    font-family: "Source Han Sans CN";
}

QLabel#TitleIconLabel
{
   border-radius: 3px;
   background-color: #3D78E5;
}

QLabel#TitleTextLabel
{
   color: #353E4E;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
}

QLabel#BackgroundLabel
{
   border-radius: 32px;
   background-color: #FFF;
}

QPushButton#GeneralBtn
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/general.png);
}

QPushButton#GeneralBtn:pressed
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/general-pressed.png);
}

QPushButton#PrinterBtn
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/printer.png);
}
QPushButton#PrinterBtn:pressed
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/printer-pressed.png);
}

QPushButton#NetworkBtn
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/network.png);
}
QPushButton#NetworkBtn:pressed
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/network-pressed.png);
}

QPushButton#UserBtn
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/user.png);
}
QPushButton#UserBtn:pressed
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/user-pressed.png);
}

QPushButton#LogBtn
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/log.png);
}
QPushButton#LogBtn:pressed
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/log-pressed.png);
}

QPushButton#UpdateBtn
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/update.png);
}
QPushButton#UpdateBtn:pressed
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/update-pressed.png);
}

QPushButton#DevInfoBtn
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/devinfo.png);
}
QPushButton#DevInfoBtn:pressed
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/devinfo-pressed.png);
}

QPushButton#SelfTestBtn
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/selftest.png);
}
QPushButton#SelfTestBtn:pressed
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/selftest-pressed.png);
}

QPushButton#CalibrateBtn
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/calibrate.png);
}
QPushButton#CalibrateBtn:pressed
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/calibrate-pressed.png);
}

QPushButton#FactoryBtn
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/factory.png);
}
QPushButton#FactoryBtn:pressed
{
   border-radius: 24px;
   background-color: transparent;
   image: url(:/image/ico/system/main/factory-pressed.png);
}
