#
# Senarios
# 1. <PERSON><PERSON><PERSON>'s DEVELOP env
#    app-0.0.0.0000
# 2. Automation build for TEST
#    app-x.y.z_RCn.hhhh
# 3. Automation build for RELEAE
#    app-x.y.z.hhhh
#

#
# "VER_MAJ=1" "VER_MIN=2" "VER_PAT=3" "VER_SHA=abcd" "VER_RC=6"
isEmpty(VER_MAJ) {
    message(VER_MAJ is not set)
    VER_MAJ = 0
    VER_MIN = 0
    VER_PAT = 0
    VER_SHA = 0000
}

VERSION_BASE = $$sprintf("%1.%2.%3", $$VER_MAJ, $$VER_MIN, $$VER_PAT)
SHA = $$format_number($$VER_SHA, ibase=16) # Convert hex to dec
VERSION = $$sprintf("%1.%2", $$VERSION_BASE, $$SHA)

isEmpty(VER_RC) { # For both RELEASE and DEVELOP
    VERSION_FULL = $$sprintf("%1.%2", $$VERSION_BASE, $$VER_SHA)
} else {
    VERSION_FULL = $$sprintf("%1_RC%2.%3", $$VERSION_BASE, $$VER_RC, $$VER_SHA)
}

isEmpty(PROJECT_VERSION) {
    PROJECT_VERSION = 0.0.0.0
}

# TARGET = WonDx-$$VERSION_BASE
TARGET_x = $$VER_MAJ
TARGET_x.y.z = $$VERSION_BASE
# TARGET_EXT = -$$QT_ARCH
# CONFIG += skip_target_version_ext

DEFINES += VER_MAJ=$$VER_MAJ
DEFINES += VER_MIN=$$VER_MIN
DEFINES += VER_PAT=$$VER_PAT
DEFINES += VER_RC=$$VER_RC
DEFINES += VER_SHA=\\\"$$VER_SHA\\\"
DEFINES += VERSION_BASE=\\\"$$VERSION_BASE\\\"
DEFINES += VERSION_FULL=\\\"$$VERSION_FULL\\\"
DEFINES += PROJECT_VERSION=\\\"$$PROJECT_VERSION\\\"

message("VERSION_FULL: $${VERSION_FULL}")
message("PROJECT_VERSION: $${PROJECT_VERSION}")
