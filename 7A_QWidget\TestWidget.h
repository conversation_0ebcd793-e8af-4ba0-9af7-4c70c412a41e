#ifndef WIDGET_H
#define WIDGET_H

#include <QWidget>
#include <QPushButton>
#include "CLabelLabel.h"
#include "CLabelLineEdit.h"
#include "CLabelComboBox.h"
#include "CHBtnTitleWidget.h"
#include "CVBtnTitleWidget.h"
#include "CDateTime.h"
#include "CLabelDate.h"
#include "CIPLabelLineEdit.h"
#include <QMap>

class CProgressBar;

namespace Ui {
class Widget;
}

class TestWidget : public QWidget
{
    Q_OBJECT

public:
    explicit TestWidget(QWidget *parent = nullptr);
    ~TestWidget();

protected:
    virtual void resizeEvent(QResizeEvent *pEvent) override;
    virtual void moveEvent(QMoveEvent *pEvent) override;

public slots:
    void SlotInfoBtn();
    void SlotUpdateBtn();
    void SlotExportBtn();
    void SlotTestBtn();

private slots:
    void _SlotVTitleBtn(int index);
    void _SlotInputMethodChanged(int);

private:
    void _InitCommonWidget();
    void _InitProgressBar();
    void _InitLayout();

private:
    Ui::Widget *ui;

    CLabelLabel* m_pCLabelLabel;
    CLabelComboBox* m_pCLabelComboBox;
    CLabelLineEdit* m_pCLabelLineEdit;

    QMap<int,int> m_inputMap;
    CLabelComboBox *m_pInputLabelComboBox;
    CLabelLineEdit *m_pInputLabelLineEidt;

    QPushButton *m_pInfoBtn, *m_pTestBtn;
    QPushButton *m_pUpdateBtn, *m_pExportBtn;
    CProgressBar *m_pUpdateProgressBar, *m_pExportProgressBar;

    CHBtnTitleWidget *m_pHBtnTitleWidget;
    CVBtnTitleWidget *m_pVBtnTitleWidget;
    CDateTime* m_pCDateTime;
    CLabelDate *m_pCLabelDate;
    CIPLabelLineEdit *m_pCIPLabelLineEdit;
};

#endif // WIDGET_H
