#include "CSixParams.h"
#include <QPainter>
#include <QBoxLayout>
#include <QGridLayout>

#include "PublicParams.h"
#include "PublicFunction.h"
#include "CMessageBox.h"

CSixParams::CSixParams(QWidget *parent) : QWidget(parent), m_iRow(-1)
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setFixedSize(1494, 884);
    this->setAttribute(Qt::WA_TranslucentBackground);
    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroup(), 0, Qt::AlignCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
    LoadQSS(this,":/qss/qss/default.qss");
}

CSixParams::~CSixParams()
{

}

void CSixParams::Show(int iRow, const QString &strRawParams)
{
    m_iRow = iRow;
    QStringList strList = strRawParams.split(",");
    if(8 == strList.size())
    {
        for(int i=0; i<8; i++)
            m_pLineEditList.at(i)->SetLineEditText(strList.at(i));
    }
    else
    {
        for(int i=0; i<8; i++)
            m_pLineEditList.at(i)->SetLineEditText("");
    }

    //this->show();
}

void CSixParams::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, gk_iBackTransparency));
    QWidget::paintEvent(pEvent);
}

void CSixParams::_SlotConfirmBtn()
{
    QStringList strList;
    for(int i=0; i<m_pLineEditList.size(); i++)
        strList.push_back(m_pLineEditList.at(i)->GetLineEditText());

    emit SignalParamsConfirm(m_iRow, strList.join(","));
    this->close();
}

void CSixParams::_SlotResetBtn()
{
    for(int i=0; i<m_pLineEditList.size(); i++)
        m_pLineEditList.at(i)->SetLineEditText("0");
}

void CSixParams::_SlotCancelBtn()
{
    this->close();
}

QGroupBox *CSixParams::_CreateGroup()
{
    QGroupBox *pGroupBox = new QGroupBox(this);
    pGroupBox->setWindowOpacity(1);
    pGroupBox->setFixedSize(700, 400);

    QStringList strList = {"VSTART", "A1", "V1", "AMAX", "VMAX", "DMAX", "D1", "VSTOP"};
    for(int i=0; i<strList.size(); i++)
    {
        CLabelLineEdit *pLineEdit = new CLabelLineEdit(strList.at(i));
        pLineEdit->SetLabelFixedSize(90, 50);
        pLineEdit->SetLineEditFixedSize(100, 50);
        pLineEdit->SetLineEditText("0");
        pLineEdit->SetLineEidtAlignment(Qt::AlignCenter);
        m_pLineEditList.push_back(pLineEdit);
    }

    m_pConfirmBtn = new QPushButton(tr("确认"));
    m_pConfirmBtn->setFixedSize(120, 50);
    connect(m_pConfirmBtn, &QPushButton::clicked, this, &CSixParams::_SlotConfirmBtn);

    m_pResetBtn = new QPushButton(tr("重置"));
    m_pResetBtn->setFixedSize(120, 50);
    connect(m_pResetBtn, &QPushButton::clicked, this, &CSixParams::_SlotResetBtn);

    m_pCancelBtn = new QPushButton(tr("取消"));
    m_pCancelBtn->setFixedSize(120, 50);
    connect(m_pCancelBtn, &QPushButton::clicked, this, &CSixParams::_SlotCancelBtn);

    QGridLayout *pGridLayout = new QGridLayout;
    //pGridLayout->setMargin(0);
    pGridLayout->setVerticalSpacing(20);
    pGridLayout->setHorizontalSpacing(0);

    int row = 0, col =0;
    for(int i=0; i<m_pLineEditList.size(); i++)
    {
        row = i / 3;
        col = i % 3;
        pGridLayout->addWidget(m_pLineEditList.at(i), row, col);
    }

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->addStretch(1);
    pBtnLayout->setSpacing(20);
    pBtnLayout->addWidget(m_pConfirmBtn);
    pBtnLayout->addWidget(m_pResetBtn);
    pBtnLayout->addWidget(m_pCancelBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addStretch(1);
    pLayout->addLayout(pGridLayout);
    pLayout->addStretch(1);
    pLayout->addLayout(pBtnLayout);
    pLayout->addSpacing(40);
    pGroupBox->setLayout(pLayout);

    return pGroupBox;
}
