﻿#ifndef ALGORITHMDESCRYPT_H
#define ALGORITHMDESCRYPT_H

#include <QDebug>

class AlgorithmDesCrypt
{
public:
    AlgorithmDesCrypt();

    void SetKey(const QString &strKey);
    void SetSrc(const QString &src); // 明文

    QString encrypt(bool *ok = nullptr); // 取得密文

private:
    void _CreateSubkey(); // 创建子密钥
    QList<int> _Encrypt(); // 加密明文

private:
    QString m_strKey; // 密钥
    QString m_strSrc; // 明文

    int m_iSubkey[16][48]; // 子密钥
    QString m_strVec; // CBC模式加密向量值

    // 换位表
private:
    static int m_siTableIP[64]; // 初始换位表
    static int m_siTablePC1[56]; // PC1换位表（64—>56）
    static int m_siTablePC2[48]; // PC2换位表（56—>48）
    static int m_siTableExtension[48]; // 扩展换位表
    static int m_siTableMove[16]; // 循环移位表
    static int m_siTableSBox[8][4][16]; // S盒换位表
    static int m_siTablePBox[32]; // P盒换位表
};

#endif // ALGORITHMDESCRYPT_H
