#include "CMachineLogConfig.h"
#include "CMessageBox.h"
#include <QDateTime>
#include <QApplication>

#define GET_LOG_TIMEOUT     45*1000
#define DELAY_GET_LOG_TIME  60*1000
#define MAX_PIECE_SIZE 1024

CMachineLogConfig::CMachineLogConfig(QWidget *parent) : QWidget(parent)
{
    QTime t1 = QTime::currentTime();

    m_iUiMachineID = 0;

    Register2Map(Method_as_debug);
    Register2Map(Method_mlog_info);
    Register2Map(Method_mlog_req);
    Register2Map(Method_mlog);

    m_pTimeoutTimer = new QTimer(this);
    connect(m_pTimeoutTimer, &QTimer::timeout, this, &CMachineLogConfig::_SlotGetLogTimeout);

    m_pDelayTimer = new QTimer(this);
    connect(m_pDelayTimer, &QTimer::timeout, this, &CMachineLogConfig::_SlotDelayTimeout);

    _ResetParams();

    _InitWidget();
    _InitLayout();

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalGetLogData,
            this, &CMachineLogConfig::SlotGetLogData);

    qDebug()<<"提取中位机页面构造时间:"<<t1.msecsTo(QTime::currentTime());
}

CMachineLogConfig::~CMachineLogConfig()
{
    UnRegister2Map(Method_as_debug);
    UnRegister2Map(Method_mlog_info);
    UnRegister2Map(Method_mlog_req);
    UnRegister2Map(Method_mlog);
}

void CMachineLogConfig::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    qDebug()<<Q_FUNC_INFO<<iMachineID<<iMethodID<<iResult;

    QString strResult = "成功";
    if(0 != iResult)
        strResult = "失败";

    if(Method_as_debug == iMethodID)
    {
        QVariantList qVarList = qVarData.toList();
        if(qVarList.isEmpty())
        {
            _SaveLog(QString("%1#配置调试信息%2").arg(iMachineID + 1).arg(strResult));
        }
        else
        {
            int iDebug = qVarList.at(0).toInt();
            m_pDebugComboBox->setCurrentIndex(iDebug);
            _SaveLog(QString("%1#读取调试信息%2:%3").arg(iMachineID + 1).arg(strResult).arg(iDebug));
        }
    }
    else if(Method_mlog_info == iMethodID)
    {
        _SaveLog(QString("%1#配置日志信息%2").arg(iMachineID + 1).arg(strResult));
    }
    else if(Method_mlog_req == iMethodID)
    {
        _GetLogSizeReply(iMachineID, qVarData);
    }
    else if(Method_mlog == iMethodID)
    {
        _SaveLog(QString("%1#中位机主动上报提取日志").arg(iMachineID + 1));
        _ResetParams();
        m_iGetLogType = Get_All_Slave_Log;
        _SaveLog(QString("开始提取%1#中位机所有日志").arg(iMachineID + 1));

        m_pProgressLabel->setText("0/0");
        QVariantList qVarList = {iMachineID, m_iGetLogType};
        QString strCmd = GetJsonCmdString(Method_mlog_req, qVarList);
        SendJsonCmd(iMachineID, Method_mlog_req, strCmd);
        m_pTimeoutTimer->start(GET_LOG_TIMEOUT);

        _EnableWidget(false);
    }
}

void CMachineLogConfig::SlotGetLogData(int iMachineID, int iPackID, const QByteArray &byteLogData)
{
    if(iPackID != m_iPackID + 1)
    {
        QString strLog = QString("提取%1#中位机日志接收packID错误,应收:%2,实收:%3")
                .arg(iMachineID + 1).arg(m_iPackID+1).arg(iPackID);
        _SaveLog(strLog);
        return;
    }

    qDebug()<<QString("接收%1#中位机日志,packID:").arg(iMachineID + 1)<<iPackID;

    m_iPackID = iPackID;
    m_pTimeoutTimer->stop();
    WriteFile(m_strLogSaveName, byteLogData, QIODevice::Append);

    int iHasReceiveLen = (iPackID + 1) * MAX_PIECE_SIZE;
    if(iHasReceiveLen >= m_iLogSize)
    {
        _SaveLog(QString("%1#中位机日志提取完成").arg(iMachineID + 1));
        m_pProgressLabel->setText(QString("%1/%2").arg(m_iLogSize).arg(m_iLogSize));

        QVariantList qVarList;
        if(Get_All_Slave_Log == m_iGetLogType)
        {
            if(!m_bDelay)
            {
                //qDebug()<<"提取全部日志，提取中没有发生延迟提取，让中位机清空所有日志";
                qVarList << Clear_Slave_Log;
            }
            else
            {
                //qDebug()<<"提取全部日志，提取中发生延迟提取，让中位机保存所有日志";
                qVarList << Save_Slave_Log;
            }
        }
        else
        {
            //qDebug()<<"提取最新日志，让中位机保存所有日志";
            qVarList << Save_Slave_Log;
        }

        QString strCmd = GetJsonCmdString(Method_mlog_end, qVarList);
        SendJsonCmd(iMachineID, Method_mlog_end, strCmd);
        _EnableWidget(true);
        return;
    }

    m_pProgressLabel->setText(QString("%1/%2").arg(iHasReceiveLen).arg(m_iLogSize));


    //    if(CGlobalConfig::GetInstance().getRunning())
    //    {
    //        QString strLog = QString("正在测试,延迟提取中位机日志");
    //        qDebug()<<strLog;
    //        CGlobalConfig::GetInstance().recordRunLog(strLog);

    //        m_bDelay = true;
    //        QStringList strList = {QString::number(Save_Slave_Log)};
    //        COperationUnit::getInstance().sendDataList(iMachineID, Method_mlog_end,strList, Port_Log);
    //        m_pDelayTimer->start(DELAY_GET_LOG_TIME);
    //    }
    //    else
    {
        int iReadLen = m_iLogSize - iHasReceiveLen;
        if(iReadLen > MAX_PIECE_SIZE)
            iReadLen = MAX_PIECE_SIZE;
        else
            qDebug()<<QString("%1#中位机日志最后一包大小:").arg(iMachineID + 1)<<iReadLen;

        qDebug()<<QString("提取%1#中位机日志packID:").arg(iMachineID + 1)<<m_iPackID+1;
        QVariantMap packMap;
        packMap.insert("packID", m_iPackID+1);
        packMap.insert("packLen", iReadLen);
        QString strCmd = GetJsonCmdString(Method_mlog_data, packMap);
        SendJsonCmd(iMachineID, Method_mlog_data, strCmd);

        m_pTimeoutTimer->start(GET_LOG_TIMEOUT);
    }
}

void CMachineLogConfig::_SlotMachineComboBoxChanged(int iMachineID)
{
    m_iUiMachineID = iMachineID;
}

void CMachineLogConfig::_SlotSetDebugBtn()
{
    int iDebug = m_pDebugComboBox->currentIndex();
    qDebug()<<QString("%1#配置调试信息:").arg(m_iUiMachineID + 1)<<iDebug;
    QVariantList qVarList = {iDebug};
    SendJsonCmd(m_iUiMachineID, Method_as_debug, GetJsonCmdString(Method_as_debug, qVarList));
}

void CMachineLogConfig::_SlotReadDebugBtn()
{
    qDebug()<<QString("%1#读取调试信息:").arg(m_iUiMachineID + 1);
    SendJsonCmd(m_iUiMachineID, Method_as_debug, GetJsonCmdString(Method_as_debug));
}

void CMachineLogConfig::_SlotSetLevelBtn()
{
    int iFlag = m_pFlagComboBox->currentIndex();
    int iLevel = m_pLevelComboBox->currentIndex();
    QVariantMap qVarMap;
    qVarMap.insert("flag", iFlag);
    qVarMap.insert("level", iLevel);
    qDebug()<<QString("%1#配置记录信息:").arg(m_iUiMachineID + 1)<<iFlag<<iLevel;
    SendJsonCmd(m_iUiMachineID, Method_mlog_info, GetJsonCmdString(Method_mlog_info, qVarMap));
}

void CMachineLogConfig::_SlotGetComboBoxChanged(int index)
{
    if(0 == index)
        m_pLastSizeLineEdit->setVisible(false);
    else
        m_pLastSizeLineEdit->setVisible(true);
}

void CMachineLogConfig::_SlotGetLogBtn()
{
    int iGetType = m_pGetLogComboBox->currentIndex();
    QVariantList qVarList;
    if(0 == iGetType)
    {
        _SaveLog(QString("提取%1#中位机所有日志").arg(m_iUiMachineID + 1));
        m_iGetLogType = Get_All_Slave_Log;
        qVarList << m_iUiMachineID << m_iGetLogType;
    }
    else
    {
        int size = m_pLastSizeLineEdit->GetLineEditText().toInt();
        _SaveLog(QString("提取%1#中位机最新日志: %2kb").arg(m_iUiMachineID + 1).arg(size));
        if(size <= 0)
        {
            ShowInformation(this, m_strTipsText, tr("提取最新日志大小不能小于等于0"));
            return;
        }
        m_iGetLogType = Get_Last_Slave_log;
        qVarList << m_iUiMachineID << m_iGetLogType << size * 1024;
    }
    _ResetParams();
    m_pProgressLabel->setText("0/0");
    QString strCmd = GetJsonCmdString(Method_mlog_req, qVarList);
    SendJsonCmd(m_iUiMachineID, Method_mlog_req, strCmd);
    m_pTimeoutTimer->start(GET_LOG_TIMEOUT);

    _EnableWidget(false);
}

void CMachineLogConfig::_SlotGetLogTimeout()
{
    _EnableWidget(true);

    _SaveLog(QString("提取%1#中位机日志超时").arg(m_pMachineComboBox->GetCurrentIndex() + 1));
    m_pTimeoutTimer->stop();
    QString strText = m_pProgressLabel->text();
    m_pProgressLabel->setText(strText + "  " + tr("超时"));
}

void CMachineLogConfig::_SlotDelayTimeout()
{

}

void CMachineLogConfig::_ResetParams()
{
    m_iLogSize = 0;
    m_iPackID = -1;
    m_iGetLogType = 0;
    m_bDelay = false;
    m_pTimeoutTimer->stop();
    m_pDelayTimer->stop();
}

void CMachineLogConfig::_SaveLog(const QString &strLog)
{
    qDebug()<<strLog;
    m_pTextBrowser->AppendLog(strLog);
}

void CMachineLogConfig::_EnableWidget(bool bEnable)
{
    m_pGetLogComboBox->setEnabled(bEnable);
    m_pLastSizeLineEdit->setEnabled(bEnable);
    m_pGetLogBtn->setEnabled(bEnable);
}

void CMachineLogConfig::_GetLogSizeReply(int iMachineID,const QVariant &qVarData)
{
    QVariantList qVarList = qVarData.toList();
    if(qVarList.isEmpty())
        return;

    int iLogSize = qVarList.at(0).toInt();
    if(iLogSize <= 0)
    {
        _SaveLog(QString("%1#中位机日志大小为0,结束").arg(iMachineID + 1));
        _EnableWidget(true);
        _ResetParams();
        return;
    }

    m_iLogSize = iLogSize;
    m_pTimeoutTimer->stop();
    m_pTimeoutTimer->start(GET_LOG_TIMEOUT);
    _SaveLog(QString("%1#中位机日志大小:%2").arg(iMachineID + 1).arg(iLogSize));

    int iReadLen = MAX_PIECE_SIZE;
    if(iLogSize < MAX_PIECE_SIZE)
        iReadLen = iLogSize;

    qDebug()<<QString("%1#提取中位机日志,packID:").arg(iMachineID + 1)<<m_iPackID + 1;

    QVariantMap packMap;
    packMap.insert("packID", m_iPackID+1);
    packMap.insert("packLen", iReadLen);
    QString strCmd = GetJsonCmdString(Method_mlog_data, packMap);
    SendJsonCmd(iMachineID, Method_mlog_data, strCmd);

    m_pProgressLabel->setText(QString("0/%1").arg(iLogSize));

    //slave_1#_20200811.log
    //QString strSlaveLogDir = CGlobalConfig::GetInstance().GetLogDirList().last();
    QString strDate = QDateTime::currentDateTime().toString("yyyyMMdd");
    QString strSlaveLogName = "slave_" + QString("%1#_").arg(iMachineID + 1) + strDate + ".log";
    m_strLogSaveName = QApplication::applicationDirPath() + "/data/slave/" + strSlaveLogName;
    qDebug()<<"中位机日志保存路径:"<<m_strLogSaveName;
}

void CMachineLogConfig::_InitWidget()
{
    m_pGroupBox = new QGroupBox;

    int iWidth = 160;

    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(100, 50);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMachineComboBoxChanged(int)));

    m_pDebugComboBox = new QComboBox;
    m_pDebugComboBox->setView(new QListView);
    m_pDebugComboBox->setFixedSize(iWidth, 50);
    QStringList strDebugList = {tr("不输出到串口"), tr("输出到串口")};
    m_pDebugComboBox->addItems(strDebugList);

    m_pSetDebugBtn = new QPushButton(tr("配置调试信息"));
    m_pSetDebugBtn->setFixedSize(iWidth, 50);
    connect(m_pSetDebugBtn, &QPushButton::clicked, this, &CMachineLogConfig::_SlotSetDebugBtn);

    m_pReadDebugBtn = new QPushButton(tr("读取调试信息"));
    m_pReadDebugBtn->setFixedSize(iWidth, 50);
    connect(m_pReadDebugBtn, &QPushButton::clicked, this, &CMachineLogConfig::_SlotReadDebugBtn);

    m_pFlagComboBox = new QComboBox;
    m_pFlagComboBox->setView(new QListView);
    m_pFlagComboBox->setFixedSize(iWidth, 50);
    QStringList strSetList = {tr("开启"), tr("关闭")};
    m_pFlagComboBox->addItems(strSetList);

    m_pLevelComboBox = new QComboBox;
    m_pLevelComboBox->setView(new QListView);
    m_pLevelComboBox->setFixedSize(iWidth, 50);
    QStringList strLevelList = {"DEBUG", "ACE", "WARNING", "ERROR", "CRIT", "ALERT", "EMERG", "OFF"};
    m_pLevelComboBox->addItems(strLevelList);

    m_pSetLevelBtn = new QPushButton(tr("配置记录信息"));
    m_pSetLevelBtn->setFixedSize(iWidth, 50);
    connect(m_pSetLevelBtn, &QPushButton::clicked, this, &CMachineLogConfig::_SlotSetLevelBtn);

    m_pGetLogComboBox = new QComboBox;
    m_pGetLogComboBox->setView(new QListView);
    m_pGetLogComboBox->setFixedSize(iWidth, 50);
    QStringList strGetList = {tr("提取所有"), tr("提取最新")};
    m_pGetLogComboBox->addItems(strGetList);
    connect(m_pGetLogComboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotGetComboBoxChanged(int)));

    m_pLastSizeLineEdit = new CLabelLineEdit(tr("最新大小(kb):"));
    m_pLastSizeLineEdit->SetLabelFixedSize(iWidth, 50);
    m_pLastSizeLineEdit->SetLineEditFixedSize(iWidth, 50);
    m_pLastSizeLineEdit->setVisible(false);

    m_pGetLogBtn = new QPushButton(tr("提取日志"));
    m_pGetLogBtn->setFixedSize(iWidth, 50);
    connect(m_pGetLogBtn, &QPushButton::clicked, this, &CMachineLogConfig::_SlotGetLogBtn);

    m_pProgressLabel = new QLabel("0/0");
    m_pProgressLabel->setFixedSize(200, 50);

    m_pTextBrowser = new CTextBrowser;
}

void CMachineLogConfig::_InitLayout()
{
    QHBoxLayout *pDebugLayout = new QHBoxLayout;
    pDebugLayout->setMargin(0);
    pDebugLayout->setSpacing(10);
    pDebugLayout->addWidget(m_pDebugComboBox);
    pDebugLayout->addWidget(m_pSetDebugBtn);
    pDebugLayout->addWidget(m_pReadDebugBtn);
    pDebugLayout->addStretch(1);

    QHBoxLayout *pLevelLayout = new QHBoxLayout;
    pLevelLayout->setMargin(0);
    pLevelLayout->setSpacing(10);
    pLevelLayout->addWidget(m_pFlagComboBox);
    pLevelLayout->addWidget(m_pLevelComboBox);
    pLevelLayout->addWidget(m_pSetLevelBtn);
    pLevelLayout->addStretch(1);

    QHBoxLayout *pGetLayout = new QHBoxLayout;
    pGetLayout->setMargin(0);
    pGetLayout->setSpacing(10);
    pGetLayout->addWidget(m_pGetLogComboBox);
    pGetLayout->addWidget(m_pLastSizeLineEdit);
    pGetLayout->addWidget(m_pGetLogBtn);
    pGetLayout->addWidget(m_pProgressLabel);
    pGetLayout->addStretch(1);

    QVBoxLayout *pGroupLayout = new QVBoxLayout;
    pGroupLayout->setMargin(10);
    pGroupLayout->setSpacing(15);
    pGroupLayout->addWidget(m_pMachineComboBox, 0, Qt::AlignLeft);
    pGroupLayout->addLayout(pDebugLayout);
    pGroupLayout->addLayout(pLevelLayout);
    pGroupLayout->addLayout(pGetLayout);
    m_pGroupBox->setLayout(pGroupLayout);

    QVBoxLayout *pMainLayout = new QVBoxLayout;
    pMainLayout->setMargin(0);
    pMainLayout->addWidget(m_pGroupBox);
    pMainLayout->addSpacing(20);
    pMainLayout->addWidget(m_pTextBrowser);
    this->setLayout(pMainLayout);
}
