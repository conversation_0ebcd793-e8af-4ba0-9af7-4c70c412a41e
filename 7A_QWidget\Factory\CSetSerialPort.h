#ifndef CSETSERIAL_H
#define CSETSERIAL_H

#include <QWidget>
#include <QCheckBox>
#include <QPushButton>

#include "CLineEdit.h"
#include "CTextBrowser.h"
#include "CLabelComboBox.h"

class CSetSerialPort : public QWidget
{
    Q_OBJECT
public:
    explicit CSetSerialPort(QWidget *parent = nullptr);
    ~CSetSerialPort();

private slots:
    void _SlotOpenUpBtn();
    void _SlotOpenLowBtn();
    void _SlotReflashBtn();
    void _SlotSerialLog(bool bOpen,const QString &strLog);
    void _SlotFirmSerialLog(bool bOpen,const QString &strLog);
    void _SlotAutoCheckBox(bool bCheck);
    void _SlotExtractCheckBox(bool bCheck);

private:
    void _InitWidget();
    QStringList _GetComNameList();

private:
    CLabelComboBox *m_pUpSerialComboBox;
    QComboBox *m_pRateComboBox;
    QPushButton *m_pOpenUpBtn, *m_pOpenLowBtn;
    CLabelComboBox *m_pLowSerialComboBox;
    QPushButton *m_pReflashBtn;
    CTextBrowser *m_pTextBrowser;
    QCheckBox *m_pAutoCheckBox, *m_pExtractCheckBox;
};

#endif // CSETSERIAL_H
