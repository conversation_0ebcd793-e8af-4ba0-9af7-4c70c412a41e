#ifndef CMOTORMETHOD_H
#define CMOTORMETHOD_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-02-28
  * Description: 电机指令
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QComboBox>
#include <QPushButton>

#include "CCmdBase.h"
#include "CLineEdit.h"
#include "CLabelComboBox.h"

class CMotorMethod : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CMotorMethod(QWidget *parent = nullptr);

    void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

public slots:
    void SlotReGetMotorTextIDData();

private slots:
    void _SlotSendBtn();
    void _SlotCmdComboBoxChanged(const QString &strText);

private:
    void _InitWidget();

private:
    CLabelComboBox *m_pMachineComboBox;
    QComboBox *m_pIDComboBox;
    QLabel *m_pNameLabel;
    CLineEdit *m_pParamLineEdit;
    QPushButton *m_pSendBtn;
};

#endif // CMOTORMETHOD_H
