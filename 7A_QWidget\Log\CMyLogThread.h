#ifndef CMYLOGTHREAD_H
#define CMYLOGTHREAD_H

#include <QDir>
#include <QDate>
#include <QFile>
#include <QTimer>
#include <QMutex>
#include <QObject>
#include <QVector>
#include <QThread>

#include "PublicParams.h"

class CMyLogThread : public QObject
{
    Q_OBJECT
public:
    static CMyLogThread* GetInstance();
    static void FreeInstance();

    void AddLog(const SLogSaveStruct& sLogSaveStruct);
    void UpdateLogConfig();

    void SaveAllLogBeforeExit();

signals:
    void SignalInitThread();
    void SignalExitThread();
    void SignalUpdateLogConfig();
    void SignalSaveAllLog();

public slots:
    void SlotUpdateLogConfig();

private slots:
    void _SlotInitThread();
    void _SlotExitThread();
    void _SlotTime2DeleteLogFiles();
    void _SlotTime2WriteLog();
    void _SlotTime2UpdateFileName();
    void _SlotSaveAllLog();

private:
    CMyLogThread();
    ~CMyLogThread();

private:
    bool _ReadLogJson();
    void _WriteLogJson();
    void _PrintfLog(const QString& strLog);
    void _CheckFileSize(QFile* pQFIle);
    static void* _ZipLogFile(void* arg);
    void _ZipDirEveryFile(QDir qDir);

private:
    static CMyLogThread* m_spInstance;

    QThread* m_pThread;
    QString m_strLogJsonFilePath;
    QStringList m_strLogSubSaveDirList;

    bool m_bStandardOutput;
    int m_iKeepDays;
    int m_iMaxSize;
    QString m_strLogSaveDir;
    QVector<QFile*> m_pQFileVector;

    QTimer* m_pDeleteLogTimer;
    QTimer* m_pWriteLogTimer;
    QTimer* m_pUpdateFileNameTimer;

    QDate m_qCurrentDate;

    QMutex m_qMutex;
    QList<SLogSaveStruct> m_sSaveLogStructList;
};

#endif // CMYLOGTHREAD_H
