#ifndef CLOCKSCREENPAGE_H
#define CLOCKSCREENPAGE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-26
  * Description: 锁屏 解锁: 当前账号密码; Admin; factory; FLY
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QTimer>
#include <QLabel>
#include <QGroupBox>
#include <QPushButton>

#include "CLineEdit.h"
#include "CHLabelTitleWidget.h"
#include "LoginPage/algorithmdescrypt.h"

class CLockScreenPage : public QWidget
{
    Q_OBJECT
public:
    explicit CLockScreenPage(QWidget *parent = nullptr);

public slots:
    void SlotLockScreenParams(int iLockTime, bool bNeverLock);

protected:
    void showEvent(QShowEvent *pEvent) override;
    void paintEvent(QPaintEvent *pEvent) override;
    bool eventFilter(QObject *pWatched, QEvent *pEvent) override;

private slots:
    void _SlotUserChanged(const QString &strUser);
    void _SlotSeeBtn();
    void _SlotConfirmBtn();
    void _SlotLockTimeout();

private:
    void _UnlockOK();
    void _CodeFactoryPassword();
    void _CodeAdminPassword();

private:
    QGroupBox *_CreateGroupBox();
    CLineEdit *_CreateLineEdit(int iEditWidth);

private:
    bool m_bSee;
    bool m_bDynamicPassword;
    bool m_bNeverLock;
    int m_iLockTime;
    QTimer *m_pLockTimer;
    AlgorithmDesCrypt *m_pAlgorithmDesCrypt;
    QString m_strCodeAdmin, m_strCodeFactory; //Admin和factroy的动态密码

private:
    CHLabelTitleWidget *m_pCHLabelTitleWidget;
    QLabel *m_pUserLabel;
    CLineEdit *m_pUserLineEdit;
    QLabel *m_pPasswordLabel;
    CLineEdit *m_pPasswordLineEdit;
    QPushButton *m_pSeeBtn;
    QLabel *m_pCodeLabel;
    QLabel *m_pInfoLabel;
    QPushButton *m_pConfirmBtn;
};

#endif // CLOCKSCREENPAGE_H
