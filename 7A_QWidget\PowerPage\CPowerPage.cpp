﻿#include "CPowerPage.h"
#include <QThread>
#include <QPainter>
#include <QBoxLayout>
#include <QApplication>
#include <QElapsedTimer>

#include "CRunTest.h"
#include "CMessageBox.h"
#include "PublicParams.h"
#include "PublicFunction.h"

#include <stdio.h>
#include <stdlib.h>
#include <fcntl.h>
#include <errno.h>
#include <fcntl.h>
#include <unistd.h>

#ifdef Q_OS_LINUX
#include <sys/types.h>
#include <sys/ioctl.h>
#include <linux/types.h>
#include <linux/i2c.h>
#include <linux/i2c-dev.h>
#endif

#define I2C_CMD_REQ      0x00
#define I2C_CMD_SHTUDOWN 0x01
#define I2C_CMD_REBOOT   0x02
#define I2C_CMD_CANCEL   0x03
#define I2C_CMD_POWER    0x04

CPowerPage::CPowerPage(QWidget *parent) : QWidget(parent)
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setFixedSize(G_QRootSize);
    this->move(G_QRootPoint);
    this->setAttribute(Qt::WA_TranslucentBackground);
    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroupBox(), 0, Qt::AlignHCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);

    m_pCLockScreenPage = new CLockScreenPage;

    LoadQSS(this,":/qss/qss/power.qss");

    m_fd = -1;
    _SetI2C("/dev/i2c-3");

#ifdef __aarch64__
    m_bThreadRunning = true;
#else
    m_bThreadRunning = false;
#endif
    connect(this, &CPowerPage::Signal2Show, this, &CPowerPage::_Slot2Show);
    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalReadPowerVersion, this, &CPowerPage::SlotStartReadVersion);

    qDebug()<<"开始读取I2C";
    std::thread mythread(&CPowerPage::_ThreadReadI2C, this);
    mythread.detach();
}

CPowerPage::~CPowerPage()
{
    m_bThreadRunning = false;
}

void CPowerPage::SlotStartReadVersion()
{
   qDebug()<<"i2c读电源版本";
   m_bReadVersion = true;
   _WriteI2C(I2C_CMD_POWER);
}

void CPowerPage::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, 30));
    QWidget::paintEvent(pEvent);
}

void CPowerPage::_SlotShutdownBtn()
{
    for(int iMachineID=0; iMachineID<gk_iMachineCount; iMachineID++)
    {
        if(CRunTest::GetInstance()->GetRunInfoStruct(iMachineID).bRunning)
        {
            _WriteI2C(I2C_CMD_CANCEL);
            ShowInformation(this, tr("提示"), tr("正在进行测试，不允许关机，请稍后重试"));
            return;
        }
    }

    int iBtnType = ShowQuestion(this, tr("提示"), tr("确定关机吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    _WriteI2C(I2C_CMD_SHTUDOWN);

#ifndef __aarch64__
    System("shutdown -h now");
    qApp->quit();
#endif
}

void CPowerPage::_SlotRebootBtn()
{
    for(int iMachineID=0; iMachineID<gk_iMachineCount; iMachineID++)
    {
        if(CRunTest::GetInstance()->GetRunInfoStruct(iMachineID).bRunning)
        {
            _WriteI2C(I2C_CMD_CANCEL);
            ShowInformation(this, tr("提示"), tr("正在进行测试，不允许重启，请稍后重试"));
            return;
        }
    }

    int iBtnType = ShowQuestion(this, tr("提示"), tr("确定重启吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    _WriteI2C(I2C_CMD_REBOOT);

#ifndef __aarch64__
    System("reboot");
#endif
}

void CPowerPage::_SlotLogoutBtn()
{
    emit SignalLogout();
    this->close();
}

void CPowerPage::_SlotLockScreenBtn()
{
    //防止连点
    static QElapsedTimer timer;
    if(timer.isValid() && timer.elapsed() < 500)
        return;
    timer.start();

    m_pCLockScreenPage->show();
    this->close();
}

void CPowerPage::_SlotReturnBtn()
{
    _WriteI2C(I2C_CMD_CANCEL);

    this->close();
}

void CPowerPage::_Slot2Show()
{
    if(!gk_pMainWindow || this->isVisible())
    {
        _WriteI2C(I2C_CMD_CANCEL);
        return;
    }

    if(!this->isVisible())
        this->show();
}

void CPowerPage::_ThreadReadI2C()
{
    while (m_bThreadRunning)
    {
        char buff[] = {0x0F};
        read(m_fd, buff, 1);

        if(m_bReadVersion)
        {
            m_bReadVersion = false;
            m_strVersion = QString::number((int)buff[0]);
            qDebug()<<"电源固件版本:"<<buff[0]<<m_strVersion;
            CPublicConfig::GetInstance()->SetPowerVersion(m_strVersion);
            emit CPublicConfig::GetInstance()->SignalSetPowerVersion(m_strVersion);
        }

        if(I2C_CMD_REQ == buff[0])
        {
            qDebug()<<"电源按钮消息:0x00";
            emit Signal2Show();
        }

        QThread::msleep(50);
    }
}

int CPowerPage::_SetI2C(QString strI2CName)
{
    m_fd = open(strI2CName.toLocal8Bit().data(), O_RDWR);
    if(m_fd < 0)
    {
        qDebug()<<"打开I2C失败"<<strI2CName;
        return -1;
    }
    qDebug()<<"打开I2C成功"<<strI2CName;

#ifdef Q_OS_LINUX
    int iRet = ioctl(m_fd, I2C_SLAVE_FORCE, 0x50);
    qDebug()<<"设置I2C地址0x50:"<<iRet;
#endif
    return 0;
}

void CPowerPage::_WriteI2C(char chCmd)
{
    if(I2C_CMD_REBOOT == chCmd || I2C_CMD_SHTUDOWN == chCmd)
        System("sync");

    char buff[] = {chCmd};
    int iRet = write(m_fd, buff, 1);
    qDebug()<<Q_FUNC_INFO<<QString("write i2c data:%1,return:%2").arg(chCmd).arg(iRet);
}

QGroupBox *CPowerPage::_CreateGroupBox()
{
    m_pShutdownBtn = new QPushButton;
    m_pShutdownBtn->setFixedSize(72, 72);
    m_pShutdownBtn->setObjectName("ShutdownBtn");
    connect(m_pShutdownBtn, &QPushButton::clicked, this, &CPowerPage::_SlotShutdownBtn);

    m_pShutdownLabel = new QLabel(tr("关机"));
    m_pShutdownLabel->setAlignment(Qt::AlignHCenter);

    m_pRebootBtn = new QPushButton;
    m_pRebootBtn->setFixedSize(72, 72);
    m_pRebootBtn->setObjectName("RebootBtn");
    connect(m_pRebootBtn, &QPushButton::clicked, this, &CPowerPage::_SlotRebootBtn);

    m_pRebootLabel = new QLabel(tr("重启"));
    m_pRebootLabel->setAlignment(Qt::AlignHCenter);

    m_pLogoutBtn = new QPushButton;
    m_pLogoutBtn->setFixedSize(72, 72);
    m_pLogoutBtn->setObjectName("LogoutBtn");
    connect(m_pLogoutBtn, &QPushButton::clicked, this, &CPowerPage::_SlotLogoutBtn);

    m_pLogoutLabel = new QLabel(tr("注销"));
    m_pLogoutLabel->setAlignment(Qt::AlignHCenter);

    m_pLockScreenBtn = new QPushButton;
    m_pLockScreenBtn->setFixedSize(72, 72);
    m_pLockScreenBtn->setObjectName("LockScreenBtn");
    connect(m_pLockScreenBtn, &QPushButton::clicked, this, &CPowerPage::_SlotLockScreenBtn);

    m_pLockScreenLabel = new QLabel(tr("锁屏"));
    m_pLockScreenLabel->setAlignment(Qt::AlignHCenter);

    m_pReturnBtn = new QPushButton;
    m_pReturnBtn->setFixedSize(72, 72);
    m_pReturnBtn->setObjectName("ReturnBtn");
    connect(m_pReturnBtn, &QPushButton::clicked, this, &CPowerPage::_SlotReturnBtn);

    m_pReturnLabel = new QLabel(tr("返回"));
    m_pReturnLabel->setAlignment(Qt::AlignHCenter);

    QVBoxLayout *pShutdownLayout = new QVBoxLayout;
    pShutdownLayout->setMargin(0);
    pShutdownLayout->setSpacing(8);
    pShutdownLayout->addStretch(1);
    pShutdownLayout->addWidget(m_pShutdownBtn, 0, Qt::AlignHCenter);
    pShutdownLayout->addWidget(m_pShutdownLabel);
    pShutdownLayout->addStretch(1);

    QVBoxLayout *pRebootLayout = new QVBoxLayout;
    pRebootLayout->setMargin(0);
    pRebootLayout->setSpacing(8);
    pRebootLayout->addStretch(1);
    pRebootLayout->addWidget(m_pRebootBtn, 0, Qt::AlignHCenter);
    pRebootLayout->addWidget(m_pRebootLabel);
    pRebootLayout->addStretch(1);

    QVBoxLayout *pLogoutLayout = new QVBoxLayout;
    pLogoutLayout->setMargin(0);
    pLogoutLayout->setSpacing(8);
    pLogoutLayout->addStretch(1);
    pLogoutLayout->addWidget(m_pLogoutBtn, 0, Qt::AlignHCenter);
    pLogoutLayout->addWidget(m_pLogoutLabel);
    pLogoutLayout->addStretch(1);

    QVBoxLayout *pLockLayout = new QVBoxLayout;
    pLockLayout->setMargin(0);
    pLockLayout->setSpacing(8);
    pLockLayout->addStretch(1);
    pLockLayout->addWidget(m_pLockScreenBtn, 0, Qt::AlignHCenter);
    pLockLayout->addWidget(m_pLockScreenLabel);
    pLockLayout->addStretch(1);

    QVBoxLayout *pReturnLayout = new QVBoxLayout;
    pReturnLayout->setMargin(0);
    pReturnLayout->setSpacing(8);
    pReturnLayout->addStretch(1);
    pReturnLayout->addWidget(m_pReturnBtn, 0, Qt::AlignHCenter);
    pReturnLayout->addWidget(m_pReturnLabel);
    pReturnLayout->addStretch(1);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(80);
    pLayout->addStretch(1);
    pLayout->addLayout(pShutdownLayout);
    pLayout->addLayout(pRebootLayout);
    pLayout->addLayout(pLogoutLayout);
    pLayout->addLayout(pLockLayout);
    pLayout->addLayout(pReturnLayout);
    pLayout->addStretch(1);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(920, 276);
    if(eLanguage_Italian == gk_iLanguage)
        pGroupBox->setFixedSize(960, 276);
    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}
