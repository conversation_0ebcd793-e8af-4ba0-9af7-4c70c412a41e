#pragma once

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: chenhao
  * Date: 2024-7-8
  * Description: Pyrolysis-曲线
  * -------------------------------------------------------------------------
  * History: 20241205 hxr 横坐标改为数据个数
  *20250526 hxr 不再new多个表格，改为多台机器共用1个表格
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QDateTime>
#include <QCheckBox>
#include <QPushButton>
#include <QStackedWidget>

#include "CCmdBase.h"
#include "CLabelComboBox.h"
#include "CSetChartXYRange.h"


class QCustomPlot;
class QCPItemText;

class CPyrolysisCurve : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CPyrolysisCurve(QWidget *parent = nullptr);
    ~CPyrolysisCurve();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

public slots:
    void SlotClearData(int iMachineID);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual void hideEvent(QHideEvent *pEvent) override;

private slots:
    void _SlotExportBtn();
    void _SlotClearBtn();
    void _SlotSetXYRange(const QStringList &strRangeList);
    void _SlotMachineChange(int iMachineID);

private:
    void _HandleHTInfo(int iMachineID, const QVariant &qVarData);
    void _HandelHTSTResult(int iMachineID);
    void _TestEnd(int iMachineID);
    void _UpdateMachinePlot(int iMachineID);

private:
    void _InitWidget();
    QCustomPlot *_InitCustomPlot();
    QCPItemText *_InitCPItemText(QCustomPlot *pCustomPlot);
    void _AddGraph(QCustomPlot* pCustomPlot, QColor penColor, QColor pointColor, int iChart, QString strChartName);

private:
    typedef struct _SHrmCurveStruct
    {
        _SHrmCurveStruct()
        {
            bReplot = false;
            strCPItemText = "Module1Temp:\nModule2Temp:\nModule3Temp:\nModule4Temp:";
            strRangeList<<"0"<<"150"<<"0"<<"110";
            TempVecMap =
            {
                {0,QVector<double>()},
                {1,QVector<double>()},
                {2,QVector<double>()},
                {3,QVector<double>()}
            };
        }
        void Clear()
        {
            bReplot = false;
            strCPItemText = "Module1Temp:\nModule2Temp:\nModule3Temp:\nModule4Temp:";
            dTimeVec.clear();
            for(auto& item : TempVecMap)
            {
                item.clear();
            }
        }

        bool bReplot;
        QString strCPItemText;
        QStringList strRangeList;
        QVector<double> dTimeVec;
        QMap<int,QVector<double>> TempVecMap;
        QDateTime qBeginTime;
    }SHrmCurveStruct;

private:
    bool m_bShow;
    QPushButton *m_pExportBtn{nullptr}, *m_pClearBtn{nullptr};
    CSetChartXYRange *m_pCSetChartXYRange{nullptr};
    CLabelComboBox *m_pMachineComboBox{nullptr};
    QList<SHrmCurveStruct *> m_sCurveUiList;
    QCustomPlot *m_pCustomPlot{nullptr};
    QCPItemText *m_pCPItemText{nullptr};
};
