#include "CUpdateResultPage.h"
#include <QPainter>
#include <QBoxLayout>
#include <QHeaderView>

#include "PublicParams.h"
#include "PublicFunction.h"

CUpdateResultPage::CUpdateResultPage(QWidget *parent) : QWidget(parent)
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setAttribute(Qt::WA_TranslucentBackground);
    this->setAttribute(Qt::WA_DeleteOnClose);
    this->setFixedSize(parent ? parent->size() : G_QRootSize);
    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateBackgroundLabel(), 0, Qt::AlignCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
    this->move(0, 0);
    LoadQSS(this, ":/qss/qss/message.qss");
}

void CUpdateResultPage::SetResultMap(const QMap<int, QStringList> &iResultMap)
{
    int index = 0;
    int size = iResultMap.size();
    int iTableHeight = 50 * (size + 1);
    m_pTableWidget->setRowCount(size);
    m_pTableWidget->setFixedHeight(iTableHeight);
    m_pBackgroundLabel->setFixedHeight(iTableHeight + 250);

    for(auto it=iResultMap.constBegin(); it!=iResultMap.constEnd(); it++)
    {
        int iMachineID = it.key();
        QStringList strList = it.value();
        strList.push_front(QString("%1#").arg(iMachineID + 1));
        if(strList.size() >= 4)
        {
            // 1# fl pcr median -> 1# median pcr fl
            QString strFL = strList[1];
            strList[1] = strList.at(3);
            strList[3] = strFL;
        }

        for(int i=0; i<strList.size(); i++)
        {
            QString str = strList.at(i);
            QTableWidgetItem *pItem = new QTableWidgetItem;
            pItem->setText(str);
            pItem->setTextAlignment(Qt::AlignCenter);
            if(tr("成功") == str)
                pItem->setTextColor(Qt::green);
            else if(tr("失败") == str)
                pItem->setTextColor(Qt::red);
            else if(tr("未升级") == str)
                pItem->setTextColor(Qt::gray);
            else
                pItem->setTextColor(Qt::black);

            m_pTableWidget->setItem(index, i, pItem);
        }

        index++;
    }
}

void CUpdateResultPage::showEvent(QShowEvent *pEvent)
{
    this->activateWindow();
    QWidget::showEvent(pEvent);
}

void CUpdateResultPage::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, gk_iBackTransparency));
    QWidget::paintEvent(pEvent);
}

void CUpdateResultPage::_SlotOKButton()
{
    this->close();
}

QLabel *CUpdateResultPage::_CreateBackgroundLabel()
{
    int iBackWidth = 780;
    int iTableWidth = 680;
    if(eLanguage_Spanish == gk_iLanguage)
    {
        iBackWidth = 840;
        iTableWidth = 740;
    }
    else if(eLanguage_German == gk_iLanguage)
    {
        iBackWidth = 830;
        iTableWidth = 730;
    }
    else if(eLanguage_Italian == gk_iLanguage)
    {
        iBackWidth = 850;
        iTableWidth = 750;
    }

    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(iBackWidth, 700);
    m_pBackgroundLabel->setObjectName("BackgroundLabel24");

    m_pTitleWidget = new CHLabelTitleWidget(tr("升级结果"));

    m_pTableWidget = new QTableWidget;
    m_pTableWidget->setFixedSize(iTableWidth, 450);
    m_pTableWidget->setRowCount(8);
    m_pTableWidget->setColumnCount(4);

    QStringList strTitleList = {tr("检测模块"), tr("中位机"), tr("PCR"), tr("荧光板")};
    m_pTableWidget->setHorizontalHeaderLabels(strTitleList);

    QHeaderView *pVerticalHeader = m_pTableWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(50);

    QHeaderView *pHorizontalHeader = m_pTableWidget->horizontalHeader();
    pHorizontalHeader->setSectionResizeMode(0, QHeaderView::Stretch);
    pHorizontalHeader->resizeSection(1, 170);
    pHorizontalHeader->resizeSection(2, 170);
    pHorizontalHeader->resizeSection(3, 170);

    m_pTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pTableWidget->setSelectionMode(QAbstractItemView::NoSelection);
    m_pTableWidget->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTableWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTableWidget->setShowGrid(true);
    m_pTableWidget->setFocusPolicy(Qt::NoFocus);

    m_pInfoLabel = new QLabel(this);
    m_pInfoLabel->setAlignment(Qt::AlignCenter);
    m_pInfoLabel->setObjectName("ReadLabel");
    m_pInfoLabel->setText(tr("升级失败请联系客服"));

    m_pOKButton = new QPushButton(tr("确认"),this);
    m_pOKButton->setFixedSize(144, 56);
    connect(m_pOKButton, &QPushButton::clicked, this, &CUpdateResultPage::_SlotOKButton);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setContentsMargins(24, 12, 24, 12);
    pLayout->addWidget(m_pTitleWidget, 0, Qt::AlignLeft);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pTableWidget, 0, Qt::AlignHCenter);
    pLayout->addSpacing(15);
    pLayout->addWidget(m_pInfoLabel, 0, Qt::AlignHCenter);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pOKButton, 0, Qt::AlignHCenter);
    pLayout->addSpacing(12);
    m_pBackgroundLabel->setLayout(pLayout);

    return m_pBackgroundLabel;
}
