#ifndef CHISTORYDETAILWIDGET_H
#define CHISTORYDETAILWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-01
  * Description: 历史详情
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QPushButton>


#include "CHLabelTitleWidget.h"
#include "CHistoryDetailAmpCurve.h"
#include "CHistoryDetailHrmCurve.h"

#include "PublicParams.h"

class CHistoryDetailWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CHistoryDetailWidget(const QString &strTitle, bool bHistoryMode, int iMachineID = -1, QWidget *parent = nullptr);

    void ClearData();
    void ClearFL();
    void SetHistoryID(int iHistoryID);
    void SetHistoryIdHandle(const SResultInfoStruct& sResultInfo,const SSampleInfoStruct& sSampleInfo,const SCardInfoStruct& sCardInfo,const SLotInfoStruct& sLotInfo,bool bPdfImage);
    void SetFLDataMap(int iMachineID, const QList<QMap<double, double>> &dFLMap);
    void SetMeltingFLDataMap(int iMachineID, const QList<double> &dTempList, const QList<QMap<double, double>> &dFLMap);
    void UpdateInfo(int iHistoryID); //更新结果Label和CT表格
    void SavePdfImage(const SRunningInfoStruct &sRunInfo, bool bReview);

protected:
    void paintEvent(QPaintEvent* pEvent) override;

private slots:
    void _SlotCloseBtn();
    void _SlotUpdateCalcResult(int iMachineID);
    void _SlotReviewConfirm(SResultInfoStruct sResultInfo);

private:
    void _InitLeftLabel();
    void _InitWidget();
    void _InitLayout();
    void SaveImageHandle(const QTime& t1,const SRunningInfoStruct& sRunInfo,QStringList& strPngPathList,bool bReviewConfirm);
    QString _GetResultText(const SResultInfoStruct& sResultInfo,const QString& strHoleName);
private:
    QLabel *m_pBackgroundLabel;
    CHLabelTitleWidget *m_pDetailTitleWidget;
    QString m_strTitle;

    QLabel *m_pLeftBackgroundLabel;
    QLabel *m_pDevTitleLabel;
    QLabel *m_pDevItemLabel;
    QLabel *m_pDevSNLabel;
    QLabel *m_pDevItemStateLabel;
    QLabel *m_pOperatorLabel;
    QLabel *m_pVersionLabel;
    //QLabel *m_pResultLabel;

    QLabel *m_pCardBoxTitleLabel;
    QLabel *m_pProjectLabel;
    QLabel *m_pCardIDLabel;
    QLabel *m_pCardSNLabel;
    QLabel *m_pCardMFGLabel;
    QLabel *m_pCardEXPLabel;

    QLabel *m_pSampleTitleLabel;
    QLabel *m_pSampleIDLabel;
    QLabel *m_pSampleTypeLabel;
    QLabel *m_pSamplingDateLabel;
    QLabel *m_pNameLabel;
    QLabel *m_pGenderLabel;
    QLabel *m_pAgeLabel;
    QLabel *m_pBirthdayLabel;
    QLabel *m_pTelLabel;
    QLabel *m_pTestTypeLabel;
    QPushButton *m_pCloseBtn;

    CHistoryDetailAmpCurve* m_AmpCurve{nullptr};
    CHistoryDetailHrmCurve* m_HrmCurve{nullptr};

    QStackedWidget* m_pStackWidget;

    SResultInfoStruct m_sResultInfo;
    SCardInfoStruct m_sCardInfo;
    SLotInfoStruct m_sLotInfo;
    SSampleInfoStruct m_sSampleInfo;
    QStringList m_PngPathStrList;
    const bool m_bHistoryMode;
    const int m_iMachineID;
};

#endif // CHISTORYDETAILWIDGET_H
