#include "CCmdBase.h"
#include <QJsonArray>
#include <QJsonObject>
#include <QJsonDocument>
#include "COperationUnit.h"
#include "CCmdManager.h"

CCmdBase::CCmdBase()
{
    m_strTipsText = QObject::tr("提示");
}

CCmdBase::~CCmdBase()
{

}

QString CCmdBase::GetClassName()
{
    return m_strClassName;
}

void CCmdBase::Register2Map(int iMethodID)
{
    CCmdManager::GetInstance()->Register2Map(iMethodID, this);
}

void CCmdBase::UnRegister2Map(int iMethodID)
{
    CCmdManager::GetInstance()->UnRegister2Map(iMethodID, this);
}

QString CCmdBase::GetJsonCmdString(int iMethodID)
{
    QString strMethodName = CPublicConfig::GetInstance()->GetMethodNameByID(iMethodID);

    QJsonObject obj;
    obj.insert("method", strMethodName);
    obj.insert("id", iMethodID);

    QJsonDocument doc(obj);
    QByteArray byteJson = doc.toJson(QJsonDocument::Compact);
    return QString::fromLocal8Bit(byteJson);
}

QString CCmdBase::GetJsonCmdString(int iMethodID, const QVariant &qVarData)
{
    QString strMethodName = CPublicConfig::GetInstance()->GetMethodNameByID(iMethodID);

    QJsonObject obj;
    obj.insert("method", strMethodName);
    obj.insert("id", iMethodID);
    obj.insert("params", qVarData.toJsonValue());

    QJsonDocument doc(obj);
    QByteArray byteJson = doc.toJson(QJsonDocument::Compact);
    return QString::fromLocal8Bit(byteJson);
}

QString CCmdBase::GetJsonCmdString(int iMethodID, const QVariantList &qVarList)
{
    QString strMethodName = CPublicConfig::GetInstance()->GetMethodNameByID(iMethodID);

    QJsonObject obj;
    obj.insert("method", strMethodName);
    obj.insert("id", iMethodID);

    if(!qVarList.isEmpty())
    {
        QJsonArray params;
        for(auto it=qVarList.constBegin(); it!=qVarList.constEnd(); it++)
            params.append((*it).toJsonValue());

        obj.insert("params",params);
    }

    QJsonDocument doc(obj);
    QByteArray byteJson = doc.toJson(QJsonDocument::Compact);
    return QString::fromLocal8Bit(byteJson);
}

QString CCmdBase::GetJsonCmdString(int iMethodID, const QVariantMap &qVarMap)
{
    QString strMethodName = CPublicConfig::GetInstance()->GetMethodNameByID(iMethodID);

    QJsonObject obj;
    obj.insert("method", strMethodName);
    obj.insert("id", iMethodID);

    if(!qVarMap.isEmpty())
    {
        QJsonObject params;
        for(auto it=qVarMap.constBegin(); it!=qVarMap.constEnd(); it++)
            params.insert(it.key(), it.value().toJsonValue());

        obj.insert("params",params);
    }

    QJsonDocument doc(obj);
    QByteArray byteJson = doc.toJson(QJsonDocument::Compact);
    return QString::fromLocal8Bit(byteJson);
}

void CCmdBase::SendJsonCmd(int iMachineID, int iMethodID, const QString &strCmd)
{
    COperationUnit::GetInstance()->SendJsonText(iMachineID, iMethodID, strCmd);
}

void CCmdBase::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    Q_UNUSED(iMachineID);
    Q_UNUSED(iMethodID);
    Q_UNUSED(iResult);
    Q_UNUSED(qVarData);
}

void CCmdBase::_InitMethodMap()
{
    //中位机
    m_iMethodIDNameTextMap.insert(Method_start, {"start", "开始测试"});
    m_iMethodIDNameTextMap.insert(Method_stop, {"stop", "停止测试"});
    m_iMethodIDNameTextMap.insert(Method_pause, {"pause", "暂停时序"});
    m_iMethodIDNameTextMap.insert(Method_resume, {"resume", "恢复时序"});
    m_iMethodIDNameTextMap.insert(Method_status, {"status", "仪器状态"});
    m_iMethodIDNameTextMap.insert(Method_valve_fan_on, {"valve_fan_on", "打开蜡阀风扇"});
    m_iMethodIDNameTextMap.insert(Method_valve_fan_off, {"valve_fan_off", "关闭蜡阀风扇"});
    m_iMethodIDNameTextMap.insert(Method_sys_info, {"sys_info", "仪器信息"});
    m_iMethodIDNameTextMap.insert(Method_env_temp, {"env_temp", "系统环境温度"});
    m_iMethodIDNameTextMap.insert(Method_ht_info, {"ht_info", "加热环境温度"});
    m_iMethodIDNameTextMap.insert(Method_read_motor_cmd, {"read_motor_cmd", "读取特定电机指令"});
    m_iMethodIDNameTextMap.insert(Method_set_motor_cmd, {"set_motor_cmd", "设置特定电机指令"});
    m_iMethodIDNameTextMap.insert(Method_read_all_cmds, {"read_all_cmds", "读取全部电机指令"});
    m_iMethodIDNameTextMap.insert(Method_reset_all_cmds, {"reset_all_cmds", "清空全部电机指令到出厂状态"});
    m_iMethodIDNameTextMap.insert(Method_delete_motor_cmd, {"delete_motor_cmd", "删除特定电机指令"});
    m_iMethodIDNameTextMap.insert(Method_as_debug, {"as_debug", "配置读取调试信息是否输出到串口"});
    m_iMethodIDNameTextMap.insert(Method_mlog, {"mlog", "中位机日志文件传输预申请"});
    m_iMethodIDNameTextMap.insert(Method_mlog_req, {"mlog_req", "日志开始指令"});
    m_iMethodIDNameTextMap.insert(Method_mlog_data, {"mlog_data", "日志数据请求指令"});
    m_iMethodIDNameTextMap.insert(Method_mlog_end, {"mlog_end", "日志结束指令"});
    m_iMethodIDNameTextMap.insert(Method_mlog_info, {"mlog_info", "日志信息"});
    m_iMethodIDNameTextMap.insert(Method_opt_byte, {"opt_byte", "设置/读取OptionByte信息"});
    m_iMethodIDNameTextMap.insert(Method_timing_file, {"timing_file", "设置时序文件"});
    m_iMethodIDNameTextMap.insert(Method_timing_step, {"timing_step", "当前时序运行步数"});
    m_iMethodIDNameTextMap.insert(Method_notify_flag, {"notify_flag", "设置主动上报数据包标记"});
    m_iMethodIDNameTextMap.insert(Method_rtc, {"rtc", "设置系统时间"});
    m_iMethodIDNameTextMap.insert(Method_upgrade_req, {"upgrade_req", "升级开始指令"});
    m_iMethodIDNameTextMap.insert(Method_upgrade_data, {"upgrade_data", "升级数据请求指令"});
    m_iMethodIDNameTextMap.insert(Method_upgrade_end, {"upgrade_end", "升级结束指令"});
    m_iMethodIDNameTextMap.insert(Method_machine_reset, {"machine_reset", "自检"});
    m_iMethodIDNameTextMap.insert(Method_no_reset, {"no_reset", "设置时序失败不做电机板整机复位"});
    m_iMethodIDNameTextMap.insert(Method_reboot, {"reboot", "重启中位机应用"});
    m_iMethodIDNameTextMap.insert(Method_notify, {"notify", "报告状态"});
    m_iMethodIDNameTextMap.insert(Method_power_off, {"power_off", "关机"});
    m_iMethodIDNameTextMap.insert(Method_heart_beat, {"heart_beat", "心跳包"});
    m_iMethodIDNameTextMap.insert(Method_beep_flag, {"beep_flag", "设置蜂鸣器开启状态"});
    m_iMethodIDNameTextMap.insert(Method_beep_cfg, {"beep_cfg", "配置蜂鸣器信息"});
    m_iMethodIDNameTextMap.insert(Method_erase_flash, {"erase_flash", "擦除外部Flash"});
    m_iMethodIDNameTextMap.insert(Method_dev_id, {"dev_id", "读取/设置设备ID"});
    m_iMethodIDNameTextMap.insert(Method_fl_data, {"fl_data", "荧光数据"});
    m_iMethodIDNameTextMap.insert(Method_valve_on, {"valve_on", "泵接大气"});
    m_iMethodIDNameTextMap.insert(Method_valve_off, {"valve_off", "泵接卡盒"});
    m_iMethodIDNameTextMap.insert(Method_pressure_on, {"pressure_on", "开启压力检测"});
    m_iMethodIDNameTextMap.insert(Method_pressure_stop, {"pressure_stop", "停止压力检测"});
    m_iMethodIDNameTextMap.insert(Method_pressure_info, {"pressure_info", "压力数据"});
    m_iMethodIDNameTextMap.insert(Method_valve2_on, {"valve2_on", "卡盒接大气"});
    m_iMethodIDNameTextMap.insert(Method_valve2_off, {"valve2_off", "卡盒接泵"});
    m_iMethodIDNameTextMap.insert(Method_N1CP_N2CA, {"N1CP_N2CA", "气嘴1接泵"});
    m_iMethodIDNameTextMap.insert(Method_N2CP_N1CA, {"N2CP_N1CA", "气嘴2接泵"});
    m_iMethodIDNameTextMap.insert(Method_PumpConAir, {"PumpConAir", "泵接大气"});
    m_iMethodIDNameTextMap.insert(Method_timing_async, {"timing_async", "时序并行组合"});
    m_iMethodIDNameTextMap.insert(Method_parallel_start, {"parallel_start", "时序并行开始"});
    m_iMethodIDNameTextMap.insert(Method_parallel_end, {"parallel_end", "时序并行结束"});
    m_iMethodIDNameTextMap.insert(Method_serial_start, {"serial_start", "时序串行开始"});
    m_iMethodIDNameTextMap.insert(Method_serial_end, {"serial_end", "时序串行结束"});

    //电机
    m_iMethodIDNameTextMap.insert(Method_MCHK, {"MCHK", "整机自检"});
    m_iMethodIDNameTextMap.insert(Method_SRST, {"SRST", "电机复位"});
    m_iMethodIDNameTextMap.insert(Method_OFRST, {"OFRST", "光学滤片电机复位"});
    m_iMethodIDNameTextMap.insert(Method_PCRRST, {"PCRRST", "PCR夹紧电机复位"});

    //PCR
    m_iMethodIDNameTextMap.insert(Method_pcr_start, {"pcr_start", "启动PCR"});
    m_iMethodIDNameTextMap.insert(Method_pcr_stop, {"pcr_stop", "停止PCR"});
    m_iMethodIDNameTextMap.insert(Method_pcr_tec_table_req, {"tec_table_req", "请求传输TEC时序表"});
    m_iMethodIDNameTextMap.insert(Method_pcr_tec_table_data, {"tec_table_data", "传输TEC时序数据"});
    m_iMethodIDNameTextMap.insert(Method_pcr_tec_table_end, {"tec_table_end", "传输TEC时序结束"});
    m_iMethodIDNameTextMap.insert(Method_pcr_info, {"pcr_info", "PCR运行信息"});
    m_iMethodIDNameTextMap.insert(Method_pcr_set_info_interval, {"set_info_interval", "设置PCR状态上传间隔时间"});
    m_iMethodIDNameTextMap.insert(Method_pcr_signal, {"pcr_signal", "PCR信号上报"});
    m_iMethodIDNameTextMap.insert(Method_pcr_version, {"pcr_version", "PCR版本信息"});
    m_iMethodIDNameTextMap.insert(Method_pcr_reboot, {"pcr_reboot", "PCR重启"});
    m_iMethodIDNameTextMap.insert(Method_pcr_upgrade_req, {"pcr_upgrade_req", "PCR升级开始指令"});
    m_iMethodIDNameTextMap.insert(Method_pcr_upgrade_data, {"pcr_upgrade_data", "PCR升级数据请求指令"});
    m_iMethodIDNameTextMap.insert(Method_pcr_upgrade_end, {"pcr_upgrade_end", "PCR升级结束指令"});
    m_iMethodIDNameTextMap.insert(Method_pcr_save_env, {"save_env", "PCR保存配置参数"});
    m_iMethodIDNameTextMap.insert(Method_pcr_set_tpid, {"set_tpid", "PCR设置温度环PID参数"});
    m_iMethodIDNameTextMap.insert(Method_pcr_get_tpid, {"get_tpid", "PCR获取温度环PID参数"});
    m_iMethodIDNameTextMap.insert(Method_pcr_set_ipid, {"set_ipid", "PCR设置电流环PID参数"});
    m_iMethodIDNameTextMap.insert(Method_pcr_get_ipid, {"get_ipid", "PCR获取电流环PID参数"});
    m_iMethodIDNameTextMap.insert(Method_pcr_set_vpid, {"set_vpid", "PCR设置电压环PID参数"});
    m_iMethodIDNameTextMap.insert(Method_pcr_get_vpid, {"get_vpid", "PCR获取电压环PID参数"});
    m_iMethodIDNameTextMap.insert(Method_pcr_set_treach, {"set_treach", "PCR设置温度到达判断阈值"});
    m_iMethodIDNameTextMap.insert(Method_pcr_cali, {"set_tcalib", "PCR设置传感器温度校准"});
    m_iMethodIDNameTextMap.insert(Method_wait_signal, {"wait_signal", "等待PCR信号"});

    //FL
    m_iMethodIDNameTextMap.insert(Method_FLLED, {"FLLED", "开启LED灯"});
    m_iMethodIDNameTextMap.insert(Method_FLADC, {"FLADC", "获取荧光数据"});
    m_iMethodIDNameTextMap.insert(Method_FLCST, {"FLCST", "开启连续采光"});
    m_iMethodIDNameTextMap.insert(Method_FLCDT, {"FLCDT", "连续采样数据"});
    m_iMethodIDNameTextMap.insert(Method_FLCSP, {"FLCSP", "结束连续采光"});
    m_iMethodIDNameTextMap.insert(Method_FLFREQ, {"FLFREQ", "设置采集频率"});
    m_iMethodIDNameTextMap.insert(Method_FLMST, {"FLMST", "启动运动采光"});
    m_iMethodIDNameTextMap.insert(Method_FLMSP, {"FLMSP", "结束运动采光"});
    m_iMethodIDNameTextMap.insert(Method_FLMDT, {"FLMDT", "运动采光数据"});
    m_iMethodIDNameTextMap.insert(Method_FLGAINSET, {"FLGAINSET", "荧光校准参数设置"});
    m_iMethodIDNameTextMap.insert(Method_FLGAINGET, {"FLGAINGET", "荧光校准参数获取"});
    m_iMethodIDNameTextMap.insert(Method_fl_upgrade_req, {"fl_upgrade_req", "荧光升级开始请求"});
    m_iMethodIDNameTextMap.insert(Method_fl_upgrade_data, {"fl_upgrade_data", "荧光升级数据请求"});
    m_iMethodIDNameTextMap.insert(Method_fl_upgrade_end, {"fl_upgrade_end", "荧光升级结果"});
    m_iMethodIDNameTextMap.insert(Method_fl_version, {"fl_version", "荧光版本"});
    m_iMethodIDNameTextMap.insert(Method_fl_reboot, {"fl_reboot", "重启荧光"});
    m_iMethodIDNameTextMap.insert(Method_fl_ledi_get, {"fl_ledi_get", "获取荧光LED电流"});
    m_iMethodIDNameTextMap.insert(Method_fl_ledi_set, {"fl_ledi_set", "设置荧光LED电流"});
    m_iMethodIDNameTextMap.insert(Method_fl_mppcv_set, {"fl_mppcv_set", "设置荧光MPPC偏压"});
    m_iMethodIDNameTextMap.insert(Method_fl_mppcv_get, {"fl_mppcv_get", "获取荧光MPPC偏压"});

    //HT
    m_iMethodIDNameTextMap.insert(Method_HTST, {"HTST", "启动加热"});
    m_iMethodIDNameTextMap.insert(Method_HTSP, {"HTSP", "停止加热"});
    m_iMethodIDNameTextMap.insert(Method_HTSET, {"HTSET", "加热设置PID参数"});
    m_iMethodIDNameTextMap.insert(Method_HTGET, {"HTGET", "加热获取PID参数"});
    m_iMethodIDNameTextMap.insert(Method_HTCALC, {"HTCALC", "温度校准"});
    m_iMethodIDNameTextMap.insert(Method_HTPARAMS, {"ht_param", "设置/读取调整PID和上报加热信息频率"});

    //US
    m_iMethodIDNameTextMap.insert(Method_US_USST, {"USST", "启动超声"});
    m_iMethodIDNameTextMap.insert(Method_US_USSP, {"USSP", "停止超声"});
    m_iMethodIDNameTextMap.insert(Method_US_USPSET, {"USPSET", "设置超声功率PID参数"});
    m_iMethodIDNameTextMap.insert(Method_US_USMSET, {"USMSET", "设置超声电机PID参数"});
    m_iMethodIDNameTextMap.insert(Method_US_AMP, {"AMP", "设置超声振幅"});
    m_iMethodIDNameTextMap.insert(Method_US_GPWR, {"GPWR", "获取超声运行状态"});
    m_iMethodIDNameTextMap.insert(Method_US_USREBOOT, {"USREBOOT", "重启超声模块"});
    m_iMethodIDNameTextMap.insert(Method_US_USPARAM, {"USPARAM", "读取超声模组配置参数"});
    m_iMethodIDNameTextMap.insert(Method_US_USFTY, {"USFTY", "设置/读取超声模组厂家"});
    m_iMethodIDNameTextMap.insert(Method_US_USVERSION, {"USVERSION", "超声版本"});
    m_iMethodIDNameTextMap.insert(Method_US_USINFO, {"USINFO", "超声信息上传"});
    m_iMethodIDNameTextMap.insert(Method_US_USAMPOPT, {"USAMPOPT", "超声振幅选择"});
    m_iMethodIDNameTextMap.insert(Method_US_USAMPLIST, {"USAMPLIST", "超声振幅列表"});

    //VT
    m_iMethodIDNameTextMap.insert(Method_DELAY, {"DEALY", "延时"});
    m_iMethodIDNameTextMap.insert(Method_loop_st, {"loop_st", "循环开始"});
    m_iMethodIDNameTextMap.insert(Method_loop, {"loop", "循环"});
    m_iMethodIDNameTextMap.insert(Method_jump, {"jump", "跳转"});
    m_iMethodIDNameTextMap.insert(Method_communication, {"communication", "通信检测"});
}
