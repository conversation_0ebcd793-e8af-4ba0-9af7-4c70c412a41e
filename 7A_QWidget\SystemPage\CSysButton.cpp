#include "CSysButton.h"
#include <QLabel>
#include <QBoxLayout>

CSysButton::CSysButton(const QString &strBtnText, QWidget *parent) : QPushButton(parent)
{
    this->setFixedSize(409, 269);

    m_pTextLabel = new QLabel(strBtnText);
    m_pTextLabel->setFixedHeight(48);

    QHBoxLayout *pTextLayout = new QHBoxLayout;
    pTextLayout->setMargin(0);
    pTextLayout->setSpacing(0);
    pTextLayout->addSpacing(62);
    pTextLayout->addWidget(m_pTextLabel);
    pTextLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addLayout(pTextLayout);
    pLayout->addSpacing(53);
    this->setLayout(pLayout);
}
