#ifndef CQCDEVGROUPWIDGET_H
#define CQCDEVGROUPWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-21
  * Description: 质控设备组
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QGroupBox>
#include "CQCDevItemBtn.h"
#include "CHLabelTitleWidget.h"

class CQCDevGroupWidget : public QWidget
{
    Q_OBJECT
public:
    CQCDevGroupWidget(int iWidth, const QString &strDevName,
                      const QList<int> &iMachineIDList, QWidget *parent = nullptr);

    void SetGroupUnSelect();
    int GetSelectedMachineID();

signals:
    void SignalGroupSelected();

private slots:
    void _SlotDevItemSelected();

private:
    QGroupBox *_CreateGroupBox();

private:
    CHLabelTitleWidget *m_pTitleWidget;
    QList<CQCDevItemBtn *> m_pDevItemBtnList;

    int m_iWidth;
    QString m_strDevName;
    QList<int> m_iMachineIDList;
};

#endif // CQCDEVGROUPWIDGET_H
