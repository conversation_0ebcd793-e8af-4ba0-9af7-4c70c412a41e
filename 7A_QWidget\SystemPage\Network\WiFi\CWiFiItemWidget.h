﻿#ifndef CWIFIITEMWIDGET_H
#define CWIFIITEMWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-01-16
  * Description: WiFi item
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QLabel>
#include <QPushButton>

enum WiFiStatus
{
    eWiFi_Unconnect  = 0, //未连接
    eWiFi_Connecting = 1, //连接中
    eWiFi_Connected  = 2, //已连接
};

class CWiFiItemWidget : public QWidget
{
    Q_OBJECT
public:
    CWiFiItemWidget(const QStringList &strInfoList, QWidget *parent = nullptr);

signals:
    void SignalConnect(QString strWiFiName);
    void SignalDisconnect(QString strWiFiName);

public:
    void SetInfoList(const QStringList &strInfoList);
    void SetBtnVisable(bool bVisable);
    void SetStatus(WiFiStatus eStatus);
    QString GetName();

private slots:
    void _SlotConnectBtn();

private:
    void _InitWidget();
    void _InitLayout();

private:
    QLabel *m_pIndexLabel;
    QLabel *m_pNameLabel;
    QLabel *m_pStatusLabel;
    QPushButton *m_pConnectBtn;
    QLabel *m_pStrengthLabel;

    WiFiStatus m_eStatus;

    int m_index;
    QString m_strName;
    int m_iStrength;
};

#endif // CWIFIITEMWIDGET_H
