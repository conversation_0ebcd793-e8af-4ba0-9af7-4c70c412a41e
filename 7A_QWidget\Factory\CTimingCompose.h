#ifndef CTIMINGCOMPOSE_H
#define CTIMINGCOMPOSE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-11-21
  * Description:
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QComboBox>
#include <QCheckBox>
#include <QPushButton>
#include <QTableWidget>
#include <QStackedWidget>

#include "CCmdBase.h"
#include "CLabelComboBox.h"
#include "CLabelLineEdit.h"
#include "TimingCompose/CMotorSerialCompose.h"

class CTimingCompose : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CTimingCompose(QWidget *parent = nullptr);
    ~CTimingCompose();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

public slots:
    void SlotUpdateItemStatus(int iMachineID, DeviceStatus eStatus, bool bCardExist);
    void SlotSoftTypeChanged(int iSoftType);
    void SlotMotorSerialComposeData(const QString &strData);
    void SlotReGetMotorTextIDData();
    void SlotUpdatePCRNameInfo(const QStringList &strPCRList);
    void SlotUpdateAgingLeftTime(int iMachineID, int iLeftTimes);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual void hideEvent(QHideEvent *pEvent) override;
    virtual bool eventFilter(QObject *pObject, QEvent *pEvent) override;

private slots:
    void _SlotMachineComboBoxChanged(int iMachineID);
    void _SlotTimingNameTextChanged(const QString &strName);
    void _SlotCardIDTextChanged(const QString &strCardID);
    void _SlotPCRIndexChanged(int index);
    void _SlotProjectIndexChanged(int index);
    void _SlotLoadBtn();
    void _SlotDelBtn();
    void _SlotReleaseBtn();
    void _SlotPressBtn();
    void _SlotCopyBtn();    
    void _SlotRunTimesLineEditTextChanged(const QString &strTimes);
    void _SlotResetCheckBox(bool bChecked);
    void _SlotCmdComboBoxChanged(int index);
    void _SlotListBtn();
    void _SlotMotorSerialComposeShow();

private:
    void _parsePasuseCmd(int iMachineID, int iResult);
    void _parseResumeCmd(int iMachineID, int iResult);
    void _parseTimingStepCmd(int iMachineID, int iResult, const QVariant &qVarData);

private:
    void _LoadName2Table();   
    void _SetOneRowData(QTableWidget *pTableWidget, int iRow, const QStringList &oneList);
    QString _GetOneRowData(QTableWidget *pTableWidget, int iRow);

    void _AddOneRow(QTableWidget *pTableWidget);
    void _DelOneRow(QTableWidget *pTableWidget);
    void _MoveUpRow(QTableWidget *pTableWidget);
    void _MoveDownRow(QTableWidget *pTableWidget);
    void _SaveTabelData(QTableWidget *pTableWidget);
    void _ClearTableWidget(QTableWidget *pWidget);
    void _StartTest(int iMachineID);
    void _PauseTest();
    void _StopTest();
    void _ResumeTest();
    void _ImportTiming();
    void _ExportTiming();

private:
    void _InitTextList();
    void _InitMap();
    int _GetCmdIDByIndex(int index);
    int _GetCmdIndexByID(int iID);

    void _InitWidget();
    void _InitLayout();
    QTableWidget *_CreateCmdTableWidget();

private:
    int m_iSoftType;
    QStringList m_strTimingNameList;
    QMap<int, int> m_strIndexIDMap;        //时序index + methodid 全自动
    QMap<int, int> m_strExtractIndexIDMap; //时序index + methodid 提取工装

private:
    typedef struct _STimingStruct
    {
        _STimingStruct()
        {
            eStatus = eDeviceDisconnect;
            pTableWidget = nullptr;
            iPCRIndex = -1;
            iProjectIndex = 0;
            strRunTimes = "0";
        }
        ~_STimingStruct()
        {
            if(pTableWidget)
            {
                delete pTableWidget;
                pTableWidget = nullptr;
            }
        }

        DeviceStatus eStatus;
        int iPCRIndex;
        int iProjectIndex;
        QTableWidget *pTableWidget;       
        QString strTimingName;
        QString strCardID;
        QString strRunTimes;
        QMap<int, QString> strTimingMap;
    }STimingStruct;

    CLabelComboBox *m_pMachineComboBox;
    CLabelLineEdit *m_pTimingNameLineEdit;
    CLabelLineEdit *m_pCardIDLineEdit;
    CLabelComboBox *m_pPCRComboBox;
    CLabelComboBox *m_pProjectComboBox;
    QStackedWidget *m_pStackedWidget;
    QList<STimingStruct *> m_stTimingStructList;
    QList<QPushButton *> m_pBtnList;

    QTableWidget *m_pFileTableWidget;
    QPushButton *m_pLoadBtn, *m_pDelBtn;
    QPushButton *m_pReleaseBtn, *m_pPressedBtn;
    QPushButton *m_pCopyBtn;
    CLineEdit *m_pCopyLineEidt;
    QLabel *m_pAgingLabel;
    CLineEdit *m_pAgingLineEdit;
    QCheckBox *m_pResetCheckBox;
    CMotorSerialCompose *m_pMotorSerialCompose;

    bool m_bShow;
    int m_iUiMachineID;
    QStringList m_strAutoCmdTextList;    //全自动命令
    QStringList m_strExtractCmdTextList; //提取工装命令

    QStringList m_strParams2List;
    QStringList m_strParams3List;
    QStringList m_strParams4List;
    QStringList m_strParams6List;
    QStringList m_strLightCoverList;
    QStringList m_strHomeEndList;
    QStringList m_strNumber10List;
    QStringList m_strNumber4List;
    QStringList m_strLightList;
    QStringList m_strPDList;
    QStringList m_strHTOpenList;
    QStringList m_strHTCloseList;
    QStringList m_strUSAmpList;
    QStringList m_strRelList;
    QStringList m_strStopList;
    QStringList m_strMotorNameList;
    QMap<QString,QString> m_strMotorTextIDMap;
};

#endif // CTIMINGCOMPOSE_H
