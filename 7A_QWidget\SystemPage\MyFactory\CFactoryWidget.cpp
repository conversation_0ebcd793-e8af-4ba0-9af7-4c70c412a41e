#include "CFactoryWidget.h"
#include <QVBoxLayout>

#include "PublicFunction.h"

#include "Factory/CSetSerialPort.h"
#include "Factory/CMachineDebug.h"
#include "Factory/CMotorDebug.h"
#include "Factory/CMotorCompose.h"
#include "Factory/CTimingCompose.h"
#include "Factory/CRealFL.h"
#include "Factory/CMeltingCurve.h"
#include "Factory/HRM/CHrmWidget.h"
#include "Factory/LightDebug/CLightDebug.h"
#include "Factory/DataManager/CDataManager.h"
#include "Factory/Settings/CSettingsWidget.h"
#include "Factory/Pyrolysis/CPyrolysis.h"
#include "Factory/CRunEnvWidget.h"

CFactoryWidget::CFactoryWidget(QWidget *parent) : QWidget(parent)
{
    _InitWidget();
    _InitLayout();

    LoadQSS(this, ":/qss/qss/system/factory.qss");
}

void CFactoryWidget::GotoRunLog()
{
    m_pCVBtnTitleWidget->SetTitleIndex(9);
    m_pCDataManager->GotoRunLog();
}

void CFactoryWidget::_SlotVTitleChanged(int index)
{
    m_pStackedWidget->setCurrentIndex(index);
}

QGroupBox *CFactoryWidget::_CreateLeftGroup()
{
    QStringList strList = {tr("串口设置"), tr("通信调试"), tr("电机调试"), tr("电机组合"),
                           tr("时序组合"), tr("实时荧光"), tr("熔解曲线"), tr("温控调试"), tr("光学调试"),
                           tr("数据管理"), tr("热裂解调试"), tr("运行环境"), tr("系统调试")};
    m_pCVBtnTitleWidget = new CVBtnTitleWidget(strList);
    m_pCVBtnTitleWidget->SetBtnFixedSize(140, 50);
    connect(m_pCVBtnTitleWidget, &CVBtnTitleWidget::SignalTitleChanged, this, &CFactoryWidget::_SlotVTitleChanged);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(160, 884);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pCVBtnTitleWidget, 0, Qt::AlignHCenter);
    pLayout->addStretch(1);
    pGroupBox->setLayout(pLayout);

    return pGroupBox;
}

void CFactoryWidget::_InitWidget()
{
    m_pCSysTtileLabelWidget = new CSysFirstTitleWidget(tr("系统设置"), tr("工厂模式"));
    connect(m_pCSysTtileLabelWidget, &CSysFirstTitleWidget::SignalTitlePress, this, &CFactoryWidget::SignalReturn);

    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(1684, 904);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");

    m_pCSetSerial = new CSetSerialPort;
    m_pCMachineDebug = new CMachineDebug;
    m_pCMotorDebug = new CMotorDebug;
    m_pCMotorCompose = new CMotorCompose;
    m_pCTimingCompose = new CTimingCompose;
    // 先注册熔解，后注册荧光算法   Register2Map(Method_start);
    // 这样在计算线程中，先跑Ct计算，后跑熔解计算；
    m_pCMeltingCurve = new CMeltingCurve;
    m_pCRealFL = new CRealFL;
    m_pHrmWidget = new CHrmWidget;
    m_pCLightDebug = new CLightDebug;
    m_pCDataManager = new CDataManager;
    m_pCPyrolysis = new CPyrolysis;
    m_pCSettingsWidget = new CSettingsWidget;
    m_pCRunEnvWidget = new CRunEnvWidget;

    m_pStackedWidget = new QStackedWidget;
    m_pStackedWidget->setFixedSize(1494, 884);
    m_pStackedWidget->addWidget(m_pCSetSerial);
    m_pStackedWidget->addWidget(m_pCMachineDebug);
    m_pStackedWidget->addWidget(m_pCMotorDebug);
    m_pStackedWidget->addWidget(m_pCMotorCompose);
    m_pStackedWidget->addWidget(m_pCTimingCompose);
    m_pStackedWidget->addWidget(m_pCRealFL);
    m_pStackedWidget->addWidget(m_pCMeltingCurve);
    m_pStackedWidget->addWidget(m_pHrmWidget);
    m_pStackedWidget->addWidget(m_pCLightDebug);
    m_pStackedWidget->addWidget(m_pCDataManager);
    m_pStackedWidget->addWidget(m_pCPyrolysis);
    m_pStackedWidget->addWidget(m_pCRunEnvWidget);
    m_pStackedWidget->addWidget(m_pCSettingsWidget);    
}

void CFactoryWidget::_InitLayout()
{
    QHBoxLayout *pBackLayout = new QHBoxLayout;
    pBackLayout->setMargin(10);
    pBackLayout->addWidget(_CreateLeftGroup());
    pBackLayout->addStretch(1);
    pBackLayout->addWidget(m_pStackedWidget);    
    m_pBackgroundLabel->setLayout(pBackLayout);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pCSysTtileLabelWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pBackgroundLabel);
    this->setLayout(pLayout);
}
