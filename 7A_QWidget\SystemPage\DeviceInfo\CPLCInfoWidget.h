#ifndef CPLCINFOWIDGET_H
#define CPLCINFOWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-30
  * Description: 下位机
  * -------------------------------------------------------------------------
  * History: 2024-08-13 去除设备组的概念, 由一拖一到一拖八
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QPushButton>
#include "CPLCDevItemWidget.h"
#include "CPLCDetailWidget.h"

class CPLCInfoWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CPLCInfoWidget(QWidget *parent = nullptr);

signals:
    void SignalReturn();

private slots:
    void _SlotDetailWidgetShow(int iMachineID);

private:
    void _Init_1x8();

private:
    int m_iDevNum;
    int m_iItemNum;
    QPushButton *m_pReturnBtn;
    QList<CPLCDevItemWidget *> m_pDevItemList;
    CPLCDetailWidget *m_pCPLCDetailWidget;
};

#endif // CPLCINFOWIDGET_H
