#ifndef CCALIDEVGROUPWIDGET_H
#define CCALIDEVGROUPWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-08-05
  * Description: 校准-设备组
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include "CCaliDevItemWidget.h"
#include "CHLabelTitleWidget.h"

class CCaliDevGroupWidget : public QWidget
{
    Q_OBJECT
public:
    CCaliDevGroupWidget(const QString &strDevName, const SDevParamsStruct &sDevParams,
                        const QList<int> &iMachineIDList, QWidget *parent = nullptr);

private:
    QGroupBox *_CreateGroupBox();

private:
    CHLabelTitleWidget *m_pTitleWidget;
    QList<CCaliDevItemWidget *> m_pDevItemWidgetList;

    QString m_strDevName;
    SDevParamsStruct m_sDevParams;
    QList<int> m_iMachineIDList;

};

#endif // CCALIDEVGROUPWIDGET_H
