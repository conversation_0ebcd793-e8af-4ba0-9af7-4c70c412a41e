#ifndef CFAULTLOG_H
#define CFAULTLOG_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-01-22
  * Description: 故障日志
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QComboBox>
#include <QGroupBox>
#include <QPushButton>
#include <QTableWidget>

#include "CLineEdit.h"
#include "CBusyProgressBar.h"
#include "CmdBus/CCmdBase.h"

class CFaultLog : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CFaultLog(QWidget *parent = nullptr);
    ~CFaultLog();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

public slots:
    void SlotTimingTestStart(int iMachineID);
    void SlotUpdateCodeInfoMap(const QMap<int, QStringList> &map);
    void SlotSaveFaultCode(int iCode, int iMachineID, const QString &strTime);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual void hideEvent(QHideEvent *pEvent) override;
    virtual void resizeEvent(QResizeEvent *pEvent) override;

private slots:
    void _SlotListBtn();
    void _SlotPrePageBtn();
    void _SlotNextPageBtn();
    void _SlotGotoPageBtn();

private:
    void _ShowCurrentPageAllData();   //显示当前页所有数据
    void _ShowCurrentPageQueryData(); //显示当前页查询数据
    void _UpdateTableWidget(const QList<QStringList> &strList);
    void _UpdateGroupBoxInfo();

    void _QueryLog();
    void _ShowAll();
    void _ShowDetail();
    void _ExportLog();
    void _Thread2Export();
    void _ClearAll();

    void _TestEnd(int iMachineID);

private:
    void _InitWidget();    
    void _InitLayout();
    QGroupBox *_CreatePageGroupBox();

private:
    bool m_bRefresh;
    bool m_bShow;
    bool m_bHasQuery;
    int m_iTotalLines;
    int m_iTotalPages;
    int m_iCurrentPage;
    int m_iLeftLines;
    int m_iOnePageLines;
    bool m_bQueryLike; //是否模糊查询
    QString m_strQueryType, m_strQueryParam;
    QMap<int, QStringList> m_iCodeInfoMap;

    QStringList m_strTitleList;
    QTableWidget *m_pTableWidget;
    QPushButton *m_pPrePageBtn, *m_pNextPageBtn, *m_pGotoPageBtn;
    CLineEdit *m_pGotoLineEdit;
    QLabel *m_pPageLabel, *m_pLinesLabel;
    QGroupBox *m_pGroupBox;
    QComboBox *m_pQueryComboBox;
    CLineEdit *m_pQueryLineEdit;
    QList<QPushButton *> m_pBtnList;
    CBusyProgressBar *m_pExportBar;

    QList<QList<QVariantList>> m_qCurrentTestLogList;
};

#endif // CFAULTLOG_H
