#ifndef CHOMEENTERINFOWIDGET_H
#define CHOMEENTERINFOWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-24
  * Description: 主页录入样本卡盒信息
  * -------------------------------------------------------------------------
  * History: 20250320 样本类型移到试剂卡栏，去除试剂卡生产日期
  * 20250701 主页弹窗都全屏
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QTimer>
#include <QMovie>
#include <QWidget>
#include <QGroupBox>
#include <QPushButton>

#include "CAgeWidget.h"
#include "CNewLabelDate.h"
#include "CLabelLineEdit.h"
#include "CLabelComboBox.h"
#include "CVIndexTextLabel.h"
#include "CHLabelTitleWidget.h"
#include "CHomeSelectProjectWidget.h"
#include "CDateTimeWidget.h"

#include "PublicParams.h"
#include "CMessageBox.h"

class CHomeEnterInfoWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CHomeEnterInfoWidget(QWidget *parent = nullptr);

public:
    void CreateTest(int iMachineID,QString strQCModel);
    int GetCurrentMachineID();

    SCardInfoStruct GetCardInfoStruct() const;
    SSampleInfoStruct GetSampleInfoStruct() const;
    void GetRunTimingTecName(QString &strTimingName, QString &strTecName) const;

public slots:
    void SlotAppStartEnd();
    void SlotLisSampleInfoMap(QMap<int, QString> strDSPMap);

protected:
    void showEvent(QShowEvent *pEvent) override;
    void hideEvent(QHideEvent *pEvent) override;

signals:
    void SignalCancelTest();
    void SignalStartTest(int iMachineID);

private slots:
    void _SlotSelectBtn();
    void _SlotPreStepBtn();
    void _SlotNextStepBtn();
    void _SlotCancelTestBtn();
    void _SlotStartTestBtn();    
    void _SlotSelectProject(const QString &strProjectName, const QString &strTimingName, const QString &strTecName);
    void _SlotScanData(QByteArray qScanData);

    void _SlotShowDateWidget();
    void _SlotConfirmDate(const QString &strDate);

    void _SlotSampleIDEditFinished();
    void _SlotSampleIDChanged(const QString &strSampleID);
    void _SlotNameChanged(const QString &strName);
    void _SlotTelephoneChanged(const QString &strTelephone);
    void _SlotCardIDChanged(const QString &strCardID);
    void _SlotCardLotChanged(const QString &strCardLot);

private:
    void _SetProjectWidgetText(QString strProjectName);
    void _UpdateSampleTypeWidget(QString strProjectName);

    bool _IsSampleIDLegal(QString strSampleID);
    bool _IsNameLegal(QString strName);
    bool _IsTelephoneLegal(QString strTelephone);
    bool _IsCardIDLegal(QString strCardID);
    bool _IsCardLotLegal(QString strCardLot);

private:
    void _SetStep(int iStep);
    void _SetCardWidgetEnable(bool bEnable);
    void _SetSampleWidgetEnable(bool bEnable);
    void _ClearData();

private:
    QGroupBox *_CreateGroupBox();
    void _InitWidget();
    void _InitLayout();

private:
    CVIndexTextLabel *m_pScanSampleIDLabel, *m_pScanCardIDLabel, *m_pAddSampleLabel, *m_pLoadCardBoxLabel;
    QLabel *m_pSpcingLabel1, *m_pSpcingLabel2, *m_pSpcingLabel3;

    QLabel *m_pInfoLabel;

    QLabel *m_pMovieLabel;

    QLabel *m_pCardBackLabel;
    CHLabelTitleWidget *m_pCardTitleWidget;
    CVLabelLineEdit *m_pProjectWidget;
    QPushButton *m_pSelectBtn;
    CVLabelLineEdit *m_pCardBoxIDWidget;
    CVLabelLineEdit *m_pCardBoxLotWidget;
    CVNewLabelDate *m_pCardBoxMFGWidget;
    CVNewLabelDate *m_pCardBoxEXPWidget;

    QLabel *m_pSampleBackLabel;
    CHLabelTitleWidget *m_pSampleTitleWidget;
    CVLabelLineEdit *m_pSampleIDWidget;
    CVNewLabelDate *m_pBirthdayWidget;
    CVLabelComboBox *m_pSampleTypeWidget;
    CVNewLabelDate *m_pSamplingDateWidget;
    CVLabelLineEdit *m_pNameWidget;
    CVLabelLineEdit *m_pTelephoneWidget;
    CVLabelComboBox *m_pGenderWidget;
    CVAgeWidget *m_pAgeWidget;

    QPushButton *m_pCancelTestBtn, *m_pPreStepBtn, *m_pNextStepBtn, *m_pStartTestBtn;    
    QString m_strQCTestModel;
    int m_iMachineID;

    QString m_strProjectName;
    QString m_strTimingName, m_strTecName;
    CHomeSelectProjectWidget *m_pCHomeSelectProjectWidget;
    CDateTimeWidget *m_pCDateTimeWidget;

    enum {eSamplingDate, eBirthDate, eCardMFG, eCardEXP};
    int m_iDateType;

    enum {eScanSample = 0, eScanCardbox = 1, eAddSample = 2, eLoadCardbox = 3};
    int m_iCurrentStep;
    int m_iUserLevel;

    QString m_strTipsText;
    QStringList m_strGifPathList;
    QMovie *m_pGifMovie;

    bool m_bScanCardID;
};

#endif // CHOMEENTERINFOWIDGET_H
