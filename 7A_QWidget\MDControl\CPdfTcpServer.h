#ifndef CPDFTCPSERVER_H
#define CPDFTCPSERVER_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-12-04
  * Description:
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QTimer>
#include <QThread>
#include <QTcpServer>
#include <QTcpSocket>

class CPdfTcpServer : public QObject
{
    Q_OBJECT
public:
    static CPdfTcpServer *GetInstance();
    ~CPdfTcpServer();

    void WriteData(QByteArray qByteData);

signals:
    void SignalInitThread();
    void SignalWriteData(QByteArray);

private slots:
    void _SlotInitThread();
    void _SlotWriteData(QByteArray qByteData);

    void _SlotRecvConnection();
    void _SlotDisplayError(QAbstractSocket::SocketError);
    void _SlotReadMessage();
    void _SlotDisconnect();
    void _SlotRestartPDFTimeout();

private:
    CPdfTcpServer();

private:
    static CPdfTcpServer *m_spInstance;

    QThread *m_pThread;

    int m_iPort;
    QString m_strIP;
    QTcpServer *m_pTcpServer;
    QTcpSocket *m_pTcpSocket;

    bool m_bServerInit;
    bool m_bConnected;
    QTimer *m_pRestartPDFTimer;

    Q_DISABLE_COPY(CPdfTcpServer)
};

#endif // CPDFTCPSERVER_H
