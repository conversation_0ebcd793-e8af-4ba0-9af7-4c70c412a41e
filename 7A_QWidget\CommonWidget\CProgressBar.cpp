#include "CProgressBar.h"
#include <QPainter>
#include <QBoxLayout>
#include "PublicParams.h"
#include "PublicFunction.h"

CProgressBar::CProgressBar(const QString &strTitleText, const QString &strInfoText, QWidget *parent)
    : QWidget(parent)
    , m_strTitleText(strTitleText)
    , m_strInfoText(strInfoText)
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setAttribute(Qt::WA_TranslucentBackground);
    this->setFixedSize(parent ? parent->size() : G_QRootSize);
    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroup(), 0, Qt::AlignCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
    LoadQSS(this,":/qss/qss/message.qss");

    _ReSetData();
}

CProgressBar::~CProgressBar()
{

}

void CProgressBar::SetTitle(const QString &strTitleText)
{
    m_pTitleWidget->ResetTitle(strTitleText);
}

void CProgressBar::SetInfo(const QString &strInfoText)
{
    m_pInfoLabel->setText(strInfoText);
}

void CProgressBar::SetRange(int iMinValue, int iMaxValue)
{
    m_iMinValue = iMinValue;
    m_iMaxValue = iMaxValue;
    m_pProgressBar->setRange(iMinValue, iMaxValue);
}

void CProgressBar::SetValue(int iValue)
{
    if(iValue < m_iMinValue)
        iValue = m_iMinValue;
    else if(iValue > m_iMaxValue)
        iValue = m_iMaxValue;

    m_iValue = iValue;
    m_pProgressBar->setValue(iValue);

    int iPercent = 0;
    if(0 != m_iMaxValue) // setRange可把m_iMaxValue set 0
      iPercent = iValue * 100 / m_iMaxValue;
    QString strPercent = QString("%1%").arg(iPercent);
    m_pPercentLabel->setText(strPercent);
}

void CProgressBar::AddValue()
{
    int iValue = m_iValue + 1;
    SetValue(iValue);
}

void CProgressBar::SetMaxValue()
{
    m_pProgressBar->setValue(m_iMaxValue);
    m_pPercentLabel->setText("100%");
}

void CProgressBar::SetLabelVisible(bool bVisible)
{
    m_pInfoLabel->setVisible(bVisible);
    m_pPercentLabel->setVisible(bVisible);
}

void CProgressBar::SetBarObjectName(const QString &strObjName)
{
    m_pProgressBar->setObjectName(strObjName);
}

void CProgressBar::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, gk_iBackTransparency));
    QWidget::paintEvent(pEvent);
}

void CProgressBar::_ReSetData()
{
    m_iValue = 0;
    m_iMinValue = 0;
    m_iMaxValue = 100;
    m_iHasRunPercent = 0;
    m_pProgressBar->setRange(m_iMinValue,m_iMaxValue);
    m_pProgressBar->setValue(0);
    m_pPercentLabel->setText("0%");
}

QGroupBox *CProgressBar::_CreateGroup()
{
    QGroupBox *pGroupBox = new QGroupBox(this);
    pGroupBox->setWindowOpacity(1);
    pGroupBox->setFixedSize(560, 360);
    pGroupBox->setObjectName("CMessageGroupBox");

    m_pTitleWidget = new CHLabelTitleWidget(m_strTitleText, this);

    QString strQSS = "border: 1px solid red;";
    m_pInfoLabel = new QLabel(m_strInfoText, this);
    m_pInfoLabel->setFixedHeight(30);
    m_pInfoLabel->setObjectName("InfoLabel");
    //m_pInfoLabel->setStyleSheet(strQSS);

    m_pPercentLabel = new QLabel(tr("0%"), this);
    m_pPercentLabel->setFixedHeight(30);
    m_pPercentLabel->setObjectName("InfoLabel");
    //m_pPercentLabel->setStyleSheet(strQSS);

    m_pProgressBar = new QProgressBar(this);
    m_pProgressBar->setFixedSize(pGroupBox->width() - 100, 10);
    m_pProgressBar->setTextVisible(false);
    m_pProgressBar->setObjectName("UpdateProgressBar");

    QHBoxLayout* pLabelLayout = new QHBoxLayout;
    pLabelLayout->setMargin(0);
    pLabelLayout->addSpacing(26);
    pLabelLayout->addWidget(m_pInfoLabel);
    pLabelLayout->addStretch(1);
    pLabelLayout->addWidget(m_pPercentLabel);
    pLabelLayout->addSpacing(26);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setContentsMargins(24, 10, 24, 10);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pTitleWidget, 0, Qt::AlignLeft);
    pLayout->addStretch(1);
    pLayout->addLayout(pLabelLayout);
    pLayout->addSpacing(5);
    pLayout->addWidget(m_pProgressBar, 0, Qt::AlignCenter);
    pLayout->addStretch(1);

    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}
