#include "CLockScreenPage.h"
#include <QPainter>
#include <QBoxLayout>
#include <QApplication>
#include <QRandomGenerator>

#include "CConfigJson.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "DBControl/CUserDB.h"

CLockScreenPage::CLockScreenPage(QWidget *parent)
    : QWidget(parent)
    , m_bSee(false)
    , m_bDynamicPassword(false)
    , m_iLockTime(1)
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setFixedSize(G_QRootSize);
    this->move(G_QRootPoint);
    this->setAttribute(Qt::WA_TranslucentBackground);
    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroupBox(), 0, Qt::AlignHCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);

    LoadQSS(this,":/qss/qss/power.qss");

    m_pLockTimer = new QTimer(this);
    connect(m_pLockTimer, &QTimer::timeout, this, &CLockScreenPage::_SlotLockTimeout);

    qApp->installEventFilter(this);

    m_pAlgorithmDesCrypt = new AlgorithmDesCrypt;

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalLockScreenParams,
            this, &CLockScreenPage::SlotLockScreenParams);
}

void CLockScreenPage::SlotLockScreenParams(int iLockTime, bool bNeverLock)
{
    qDebug()<<Q_FUNC_INFO<<"锁屏参数:"<<iLockTime<<bNeverLock;
    m_iLockTime = iLockTime;
    m_bNeverLock = bNeverLock;

    m_pLockTimer->stop();
    m_pLockTimer->start(m_iLockTime * 60 * 1000);
}

void CLockScreenPage::showEvent(QShowEvent *pEvent)
{
    m_bDynamicPassword = CPublicConfig::GetInstance()->GetDynamicPassword();

    QString strUser = CPublicConfig::GetInstance()->GetLoginUser();
    m_pUserLineEdit->setText(strUser);

    QWidget::showEvent(pEvent);
}

void CLockScreenPage::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, 50));
    QWidget::paintEvent(pEvent);
}

bool CLockScreenPage::eventFilter(QObject *pWatched, QEvent *pEvent)
{
    switch (pEvent->type())
    {
    case QEvent::MouseButtonPress:
    case QEvent::MouseButtonRelease:
    case QEvent::MouseButtonDblClick:
    case QEvent::MouseMove:
    case QEvent::KeyPress:
    case QEvent::KeyRelease:
    case QEvent::FocusIn:
    case QEvent::FocusOut:
        m_pLockTimer->stop();
        m_pLockTimer->start(m_iLockTime * 60 * 1000);
        break;
    default:
        break;
    }

    return QWidget::eventFilter(pWatched, pEvent);
}

void CLockScreenPage::_SlotUserChanged(const QString &strUser)
{
    m_pCodeLabel->clear();
    m_pCodeLabel->setVisible(false);
    m_pInfoLabel->clear();

    if(!m_bDynamicPassword)
        return;

    if(gk_strAdminUserName == strUser)
        _CodeAdminPassword();
    else if(gk_strFactoryUserName == strUser)
        _CodeFactoryPassword();
}

void CLockScreenPage::_SlotSeeBtn()
{
    QString strImagePath = "";
    if(!m_bSee)
    {
        m_bSee = true;
        m_pPasswordLineEdit->setEchoMode(QLineEdit::Normal);
        strImagePath = ":/image/ico/login/see.png";
    }
    else
    {
        m_bSee = false;
        m_pPasswordLineEdit->setEchoMode(QLineEdit::Password);
        strImagePath = ":/image/ico/login/no_see.png";
    }
    m_pSeeBtn->setIcon(QIcon(strImagePath));
    m_pSeeBtn->setIconSize(QSize(32, 32));
}

void CLockScreenPage::_SlotConfirmBtn()
{
    QString strUser = m_pUserLineEdit->text();
    QString strPassword = m_pPasswordLineEdit->text();
    if(strUser.isEmpty() || strPassword.isEmpty())
    {
        m_pInfoLabel->setText(tr("请输入用户名和密码"));
        return;
    }

    QString strErrorInfo = tr("密码错误");
    if(gk_strFlyUserName == strUser)
    {
        if(gk_strFlyPassword == strPassword)
            _UnlockOK();
        else
            m_pInfoLabel->setText(strErrorInfo);
        return;
    }

    if(gk_strMaintianUserName == strUser)
    {
        QString strDB = CUserDB::GetInstance()->FindUserPasswordByName(strUser);
        if(strDB == strPassword)
            _UnlockOK();
        else
            m_pInfoLabel->setText(strErrorInfo);
        return;
    }

    if(gk_strAdminUserName == strUser)
    {
        QString strDB = CUserDB::GetInstance()->FindUserPasswordByName(strUser);
        if(strDB == strPassword)
        {
            _UnlockOK();
            return;
        }

        if(m_bDynamicPassword)
        {
            if(m_strCodeAdmin == strPassword)
            {
                _UnlockOK();
                return;
            }
        }

        m_pInfoLabel->setText(strErrorInfo);
        return;
    }

    if(gk_strFactoryUserName == strUser)
    {
        if(m_bDynamicPassword)
        {
            if(m_strCodeFactory == strPassword)
                _UnlockOK();
            else
                m_pInfoLabel->setText(strErrorInfo);
        }
        else
        {
            if(gk_strFactoryPassWord == strPassword)
                _UnlockOK();
            else
                m_pInfoLabel->setText(strErrorInfo);
        }
        return;
    }

    QString strLoginUser = CPublicConfig::GetInstance()->GetLoginUser();
    QString strLoginPassword = CPublicConfig::GetInstance()->GetLoginPassword();
    if(strLoginUser != strUser)
    {
        m_pInfoLabel->setText(tr("请输入当前登录账号或管理员账号来解锁"));
        return;
    }

    if(strLoginPassword == strPassword)
        _UnlockOK();
    else
        m_pInfoLabel->setText(strErrorInfo);
}

void CLockScreenPage::_SlotLockTimeout()
{
    //1已经锁屏; 2从不锁屏; 3处在登录界面
    if(this->isVisible() || m_bNeverLock || CPublicConfig::GetInstance()->GetLoginLevel() < 0)
        return;

    qDebug()<<Q_FUNC_INFO<<"锁屏时间到";
    this->showNormal();
}

void CLockScreenPage::_UnlockOK()
{
    qDebug()<<Q_FUNC_INFO<<"锁屏解除";

    m_pLockTimer->stop();
    m_pLockTimer->start(m_iLockTime * 60 * 1000);

    m_pUserLineEdit->clear();
    m_pPasswordLineEdit->clear();
    m_pCodeLabel->clear();
    m_pInfoLabel->clear();
    m_strCodeAdmin.clear();
    m_strCodeFactory.clear();

    m_bSee = false;
    m_pPasswordLineEdit->setEchoMode(QLineEdit::Password);
    m_pSeeBtn->setIcon(QIcon(":/image/ico/login/no_see.png"));
    m_pSeeBtn->setIconSize(QSize(32, 32));

    this->close();
}

void CLockScreenPage::_CodeFactoryPassword()
{
    int iRand= QRandomGenerator::global()->bounded(1000, 9999);
    QString strCode = "F" + QTime::currentTime().toString("ssmm") + QString::number(iRand);
    m_pCodeLabel->setVisible(true);
    m_pCodeLabel->setText(tr("机器代码：") + strCode);
    m_pAlgorithmDesCrypt->SetKey("Wondfofc");
    m_pAlgorithmDesCrypt->SetSrc(strCode);
    m_strCodeFactory = m_pAlgorithmDesCrypt->encrypt();
    qDebug()<<Q_FUNC_INFO<<"生成factory动态密码:"<<m_strCodeFactory;
}

void CLockScreenPage::_CodeAdminPassword()
{
    int iRand= QRandomGenerator::global()->bounded(1000, 9999);
    QString strCode = "A" + QTime::currentTime().toString("ssmm") + QString::number(iRand);
    m_pCodeLabel->setVisible(true);
    m_pCodeLabel->setText(tr("机器代码：") + strCode);
    m_pAlgorithmDesCrypt->SetKey("Wondfoad");
    m_pAlgorithmDesCrypt->SetSrc(strCode);
    m_strCodeAdmin = m_pAlgorithmDesCrypt->encrypt();
    qDebug()<<Q_FUNC_INFO<<"生成Admin动态密码:"<<m_strCodeAdmin;
}

QGroupBox *CLockScreenPage::_CreateGroupBox()
{
    m_pCHLabelTitleWidget = new CHLabelTitleWidget(tr("解除锁屏"));

    m_pUserLabel = new QLabel(tr("用户名"));

    int iEditWidth = 460;
    if(eLanguage_English == gk_iLanguage)
        iEditWidth = 420;
    else if(eLanguage_Spanish == gk_iLanguage)
        iEditWidth = 400;
    else if(eLanguage_German == gk_iLanguage)
        iEditWidth = 390;
    else if(eLanguage_Italian == gk_iLanguage)
        iEditWidth = 385;

    m_pUserLineEdit = new CLineEdit;
    m_pUserLineEdit->setFixedSize(iEditWidth, 56);
    connect(m_pUserLineEdit, &QLineEdit::textChanged, this, &CLockScreenPage::_SlotUserChanged);

    m_pPasswordLabel = new QLabel(tr("密码"));

    m_pPasswordLineEdit = _CreateLineEdit(iEditWidth);

    m_pCodeLabel = new QLabel;
    m_pCodeLabel->setFixedWidth(596);
    m_pCodeLabel->setObjectName("InfoLabel");
    m_pCodeLabel->setAlignment(Qt::AlignCenter);
    m_pCodeLabel->setVisible(false);

    m_pInfoLabel = new QLabel;
    m_pInfoLabel->setFixedWidth(596);
    m_pInfoLabel->setObjectName("InfoLabel");
    m_pInfoLabel->setAlignment(Qt::AlignCenter);

    m_pConfirmBtn = new QPushButton(tr("确认"));
    m_pConfirmBtn->setFixedSize(400, 56);
    m_pConfirmBtn->setObjectName("ConfirmBtn");
    connect(m_pConfirmBtn, &QPushButton::clicked, this, &CLockScreenPage::_SlotConfirmBtn);

    QHBoxLayout *pNameLayout = new QHBoxLayout;
    pNameLayout->setMargin(0);
    pNameLayout->setSpacing(0);
    pNameLayout->addWidget(m_pUserLabel);
    pNameLayout->addStretch(1);
    pNameLayout->addWidget(m_pUserLineEdit);

    QHBoxLayout *pPassswordLayout = new QHBoxLayout;
    pPassswordLayout->setMargin(0);
    pPassswordLayout->setSpacing(0);
    pPassswordLayout->addWidget(m_pPasswordLabel);
    pPassswordLayout->addStretch(1);
    pPassswordLayout->addWidget(m_pPasswordLineEdit);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setContentsMargins(24, 15, 24, 24);
    pLayout->addWidget(m_pCHLabelTitleWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(55);
    pLayout->addLayout(pNameLayout);
    pLayout->addSpacing(40);
    pLayout->addLayout(pPassswordLayout);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pCodeLabel, 0, Qt::AlignHCenter);
    pLayout->addSpacing(5);
    pLayout->addWidget(m_pInfoLabel, 0, Qt::AlignHCenter);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pConfirmBtn, 0, Qt::AlignHCenter);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(596, 460);
    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}

CLineEdit *CLockScreenPage::_CreateLineEdit(int iEditWidth)
{
    CLineEdit *pLineEdit = new CLineEdit;
    pLineEdit->setFixedSize(iEditWidth, 56);
    pLineEdit->setEchoMode(QLineEdit::Password);

    m_pSeeBtn = new QPushButton;
    m_pSeeBtn->setFixedSize(32, 32);
    m_pSeeBtn->setObjectName("SeeBtn");
    m_pSeeBtn->setCursor(QCursor(Qt::ArrowCursor));
    m_pSeeBtn->setIcon(QIcon(":/image/ico/login/no_see.png"));
    m_pSeeBtn->setIconSize(QSize(32, 32));
    connect(m_pSeeBtn, &QPushButton::clicked, this, &CLockScreenPage::_SlotSeeBtn);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pSeeBtn);
    pLayout->addSpacing(24);
    pLineEdit->setLayout(pLayout);

    return pLineEdit;
}
