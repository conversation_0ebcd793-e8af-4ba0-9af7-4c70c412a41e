#include "MainWindow.h"
#include "ui_MainWindow.h"
#include <QDebug>
#include <QDateTime>
#include <QStackedWidget>
#include <QQuickWidget>
#include <QBoxLayout>
#include <QQmlApplicationEngine>

#include "PublicFunction.h"
#include "CQmlKeyboard.h"

#include "MDControl/CPdfTcpServer.h"
#include "MDControl/CScanCodeThread.h"
#include "MDControl/CPrintThread.h"
#include "MDControl/CCanBusThread.h"
#include "MDControl/CSerialThread.h"
#include "MDControl/CFirmLogSerialThread.h"
#include "MDControl/CReadWriteXlsxThread.h"
#include "PublicConfig.h"

#include "CFtpDB.h"
#include "CDevInfoDB.h"
#include "CUserDB.h"
#include "CTecDB.h"
#include "CSystemDB.h"
#include "CHistoryDB.h"
#include "CMotorInfoDB.h"
#include "CLogDB.h"
#include "CProjectDB.h"
#include "CMessageBox.h"
#include "CCmdManager.h"

#include "CMotorDB.h"
#include "CTimingTecDB.h"
#include "CLotInfoDB.h"
#include "CHeartBeat.h"
#include "CRunTest.h"
#include "include/ccalctlib.h"

#ifdef Q_OS_WIN
  QSize G_QRootSize(1915, 1075);
#else
  QSize G_QRootSize(1920, 1080);
#endif
QPoint G_QRootPoint(0, 0);

//#define VERSION "V0.1.12-20240619"

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
{
    ui->setupUi(this);

#ifdef __aarch64__
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
#else
    this->setWindowFlags(Qt::CustomizeWindowHint);
#endif
    this->setFixedSize(G_QRootSize);
    this->move(0, 0);

    QTime qStart = QTime::currentTime();

    // 需要在使用CCalCTLib 三方库之前初始化日志库
    CCalCTLib::initCalc();

    CCalCTLib ctLib;
    QString strCalcVer = ctLib.getVersion();
    qDebug()<<"算法库版本:"<<strCalcVer;

    _InitControl();
    _InitWidget();
    CPdfTcpServer::GetInstance();

    QTime qEnd = QTime::currentTime();
    qDebug()<<"软件构造UI时间:"<<qStart.msecsTo(qEnd);

    m_pStackedWidget->setCurrentIndex(0);
}

MainWindow::~MainWindow()
{
    delete ui;
    printf("%s\n",Q_FUNC_INFO);
}

//void MainWindow::resizeEvent(QResizeEvent *pEvent)
//{
//    //qDebug()<<"m_pStackedWidget size:"<<m_pStackedWidget->size()<<",MainWindow size:"<<size();
//    G_QRootSize.setWidth(this->width());
//    G_QRootSize.setHeight(this->height() + 30);
//    //m_qmlInputPanel->hide(); //窗口拉伸先隐藏键盘
//    //m_qmlInputPanel->move((this->width() - 1024) / 2, this->height()-370);

//    /*软件启动后,虚拟操作键盘.如果在Qt Creator强制关闭软件非窗口右上角的X关闭,会提示
//     * QQuickWidget::invalidateRenderControl could not make context current
//     屏蔽基类实现QMainWindow::resizeEvent(pEvent)则不会有此提示.
//     如果点击软件右上角的X关闭,不管有没有基类实现都不会有此提示
//    */
//    QWidget::resizeEvent(pEvent);
//}

//void MainWindow::moveEvent(QMoveEvent *pEvent)
//{
//    G_QRootPoint = pos();
//    QWidget::moveEvent(pEvent);
//}

void MainWindow::closeEvent(QCloseEvent *pEvent)
{
    int iBtnType = ShowQuestion(nullptr, tr("提示"), tr("确认要退出软件吗"));
    if(QMessageBox::Yes == iBtnType)
        pEvent->accept();
    else
        pEvent->ignore();
}

void MainWindow::showEvent(QShowEvent *pEvent)
{
    QWidget::showEvent(pEvent);
}

void MainWindow::_SlotLoginOK()
{
    m_pCMdxMainWidget->Set2MainPage();
    m_pStackedWidget->setCurrentIndex(1);
}

void MainWindow::_SlotLogout()
{
    QString strUser = CPublicConfig::GetInstance()->GetLoginUser();
    QString strLoginLog = GetLoginLogString(eLogout, strUser);
    qDebug()<<strLoginLog;
    //CLogDB::instance().AddLoginLog(eLogout, strUser, strLoginLog);

    CPublicConfig::GetInstance()->SetLoginLevel(-1);
    CPublicConfig::GetInstance()->SetLoginUser("");

    m_pStackedWidget->setCurrentIndex(0);

    CLogDB::instance().AddOperationLog(strUser, tr("退出登录"), CLogDB::eLoginLog);
}

void MainWindow::_InitControl()
{
#ifdef __aarch64__
    CCanBusThread::GetInstance();
#endif

    CScanCodeThread::GetInstance();
    CPrintThread::GetInstance();
    CSerialThread::GetInstance();
    CFirmLogSerialThread::GetInstance();
    CReadWriteXlsxThread::GetInstance();

    CUserDB::GetInstance();
    CTecDB::instance();
    CSystemDB::GetInstance();
    CHistoryDB::GetInstance();
    CMotorInfoDB::GetInstance();
    CLogDB::instance();
    CProjectDB::GetInstance();
    CLotInfoDB::GetInstance();
    CDevInfoDB::GetInstance();
    CFtpDB::GetInstance();
    CPublicConfig::GetInstance()->InitSampleTypeMap();

    //从旧表读出数据存入新表
    if(!QFile::exists(CPublicConfig::GetInstance()->GetTimingDBPath()))
    {
        QList<QStringList> strTecList = CTecDB::instance().RealAllTecList();
        CTimingTecDB::GetInstance().SaveTecList(strTecList);

        QList<QStringList> strTimingList = CMotorInfoDB::GetInstance()->readAllTimingList();
        CTimingTecDB::GetInstance().SaveTimingList(strTimingList);
    }
    else
    {
        CTimingTecDB::GetInstance();
    }

    if(!QFile::exists(CPublicConfig::GetInstance()->GetMotorDBPath()))
    {
        QMap<QString, QString> map1 = CMotorInfoDB::GetInstance()->readAllCmdNameContent();
        QMap<QString, QStringList> map2 = CMotorInfoDB::GetInstance()->readAllMotorComposeInfo();

        for(auto it=map2.begin(); it!=map2.end(); it++)
        {
            QString cmdID = it.key();
            QStringList list = it.value();
            QString cmdName = list.at(0);
            QString cmdParam = list.at(1);
            QString cmdText = list.at(2);
            QString cmdContent = map1.value(cmdName);

            CMotorDB::GetInstance().AddOneCmd(cmdID, cmdName, cmdText, cmdParam, cmdContent);
        }

        QList<QStringList> strCompensateList = CMotorInfoDB::GetInstance()->readAllMotorCompensate();
        for(int i=0; i<strCompensateList.size(); i++)
        {
            QStringList one = strCompensateList.at(i);
            if(one.size() < 5)
                continue;

            CMotorDB::GetInstance().AddOneCompensate(one.at(1).toInt(), one.at(2).toInt(), one.at(3), one.at(4));
        }

        QList<QStringList> strNormalDataList = CMotorInfoDB::GetInstance()->readAllNormalData();
        for(int i=0; i<strNormalDataList.size(); i++)
        {
            QStringList one = strNormalDataList.at(i);
            if(one.size() < 5)
                continue;

            one.pop_back();
            one.pop_front();
            CMotorDB::GetInstance().AddOneParam(one);
        }
    }
    else
    {
        CMotorDB::GetInstance();
    }

    CCmdManager::GetInstance();
    CHeartBeat::GetInstance();
    CRunTest::GetInstance();

    //如果不存在运维账号,添加默认运维账号
    if(!CUserDB::GetInstance()->IsUserExistInDB(gk_strMaintianUserName))
    {
        QString strDateTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
        QStringList strAddList = {gk_strMaintianUserName, gk_strMaintianPassword, strDateTime, "0"};
        CUserDB::GetInstance()->AddUserData(strAddList);
    }
}

void MainWindow::_InitWidget()
{
    QTime start = QTime::currentTime();

    m_pCLoginWidget = new CLoginWidget;
    connect(m_pCLoginWidget, &CLoginWidget::SignalLoginOK, this, &MainWindow::_SlotLoginOK);

    m_pCMdxMainWidget = new CMdxMainWidget;
    connect(m_pCMdxMainWidget, &CMdxMainWidget::SignalLogout, this, &MainWindow::_SlotLogout);

    m_pStackedWidget = new QStackedWidget;
    m_pStackedWidget->setFixedSize(G_QRootSize);
    m_pStackedWidget->addWidget(m_pCLoginWidget);
    m_pStackedWidget->addWidget(m_pCMdxMainWidget);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pStackedWidget);

    QWidget *pWidget = new QWidget;
    pWidget->setFixedSize(G_QRootSize);
    pWidget->setLayout(pLayout);

    this->setCentralWidget(pWidget);

    qDebug()<<"构造所有界面耗时:"<<start.msecsTo(QTime::currentTime());
}

void MainWindow::_InitKeyboard()
{
    connect(CQmlKeybaord::GetInstance(), &CQmlKeybaord::SignalHideKeyboard,
            this, &MainWindow::_SlotHideKeyboard);
    connect(CQmlKeybaord::GetInstance(), &CQmlKeybaord::SignalShowQucikWidget,
            this, &MainWindow::_SlotShowQuickWidget);
    connect(CQmlKeybaord::GetInstance(), &CQmlKeybaord::SignalSetKeyboardPosition,
            this, &MainWindow::_SlotSetKeyboardPosition);
    connect(CQmlKeybaord::GetInstance(), &CQmlKeybaord::SignalSetKeyboardParentWidget,
            this, &MainWindow::_SlotSetKeyboardParentWidget);

    m_qmlInputPanel = new QQuickWidget(this);
    m_qmlInputPanel->setResizeMode(QQuickWidget::SizeViewToRootObject);
    m_qmlInputPanel->setAttribute(Qt::WA_AlwaysStackOnTop);
    // m_qmlInputPanel->setSource(QStringLiteral("qrc:/qml/qmlkeyboard/mkeyboard.qml"));
    m_qmlInputPanel->move((this->width() - 1024) / 2, this->height());
    m_qmlInputPanel->setFocusProxy(this);
    m_qmlInputPanel->hide();
}

void MainWindow::_SlotHideKeyboard()
{
    if(m_qmlInputPanel)
        m_qmlInputPanel->hide();
}

void MainWindow::_SlotShowQuickWidget()
{
    if(m_qmlInputPanel)
        m_qmlInputPanel->show();
}

void MainWindow::_SlotSetKeyboardParentWidget(QWidget* pWidget)
{
    if(pWidget)
        m_qmlInputPanel->setParent(pWidget);
}

void MainWindow::_SlotSetKeyboardPosition(int x, int y, int height, QLineEdit* pLineEdit)
{
    if(!m_qmlInputPanel || !pLineEdit)
        return;

    qDebug()<<"当前编辑QLineEdit信息,x:"<<x<<",y:"<<y<<",height:"<<height;

    int iKeyboardWidth = 1024;
    int iKeyboardHeight = 370;

    int y1 = y - iKeyboardHeight -5;

    if(this->height() - y - height >= iKeyboardHeight)
    {
        y1 = this->height() - iKeyboardHeight; //落在最下面
    }
    else
    {
        y1 = y - iKeyboardHeight - 5; //落在最上面

        if(y1 >= 0)
            y1 = 0;
        if(y1 <= -20)
            y1 = -20;
    }

    m_qmlInputPanel->move((this->width() - iKeyboardWidth) / 2, y1);
    m_qmlInputPanel->setFocusProxy(pLineEdit);
    //QGuiApplication::inputMethod()->show();
    m_qmlInputPanel->show();
}
