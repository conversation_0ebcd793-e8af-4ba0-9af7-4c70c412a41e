#include "COptoCompose.h"
#include <QPainter>
#include <QBoxLayout>
#include <QGridLayout>

#include "PublicParams.h"
#include "PublicFunction.h"
#include "CMessageBox.h"

COptoCompose::COptoCompose(QWidget *parent) : QWidget(parent), m_iRow(-1), m_iType(0)
{
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setFixedSize(1494, 884);
    this->setAttribute(Qt::WA_TranslucentBackground);
    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroup(), 0, Qt::AlignCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);
    LoadQSS(this,":/qss/qss/default.qss");
}

void COptoCompose::Show(int iRow, int iType, const QString &strRawParams)
{
    m_iRow = iRow;
    m_iType = iType;

    QStringList strList = strRawParams.split(SPLIT_IN_CMD);
    if(8 == strList.size())
    {
        for(int i=0; i<2; i++)
            m_pLineEditList.at(i)->SetLineEditText(strList.at(i));

        for(int i=0; i<2; i++)
        {
            if(0 == iType)
            {
                m_pComboBoxList.at(i)->setVisible(true);
                m_pIndexLineEditList.at(i)->setVisible(false);
                m_pComboBoxList.at(i)->SetCurrentIndex(strList.at(i+2).toInt());
            }
            else
            {
                m_pComboBoxList.at(i)->setVisible(false);
                m_pIndexLineEditList.at(i)->setVisible(true);
                m_pIndexLineEditList.at(i)->SetLineEditText(strList.at(i+2));
            }
        }
    }

    //this->show();
}

void COptoCompose::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, gk_iBackTransparency));
    QWidget::paintEvent(pEvent);
}

void COptoCompose::_SlotConfirmBtn()
{
    QStringList strList;
    for(int i=0; i<m_pLineEditList.size(); i++)
        strList.push_back(m_pLineEditList.at(i)->GetLineEditText());

    for(int i=0; i<2; i++)
    {
        if(0 == m_iType)
            strList.push_back(QString::number(m_pComboBoxList.at(i)->GetCurrentIndex()));
        else
            strList.push_back(m_pIndexLineEditList.at(i)->GetLineEditText());
    }

    emit SignalParamsConfirm(m_iRow, strList.join(SPLIT_IN_CMD));
    this->close();
}

void COptoCompose::_SlotResetBtn()
{
    for(int i=0; i<2; i++)
    {
        m_pLineEditList.at(i)->SetLineEditText("0");
        m_pComboBoxList.at(i)->SetCurrentIndex(0);
        m_pIndexLineEditList.at(i)->SetLineEditText("0");
    }
}

void COptoCompose::_SlotCancelBtn()
{
    this->close();
}

QGroupBox *COptoCompose::_CreateGroup()
{
    QStringList strList = {tr("覆盖"), tr("非覆盖")};
    for(int i=0; i<2; i++)
    {
        CLabelLineEdit *pLineEdit = new CLabelLineEdit(tr("光耦ID:"), "0");
        pLineEdit->SetLineEditFixedSize(100, 50);
        pLineEdit->SetLabelFixedSize(80, 50);
        m_pLineEditList.push_back(pLineEdit);

        CLabelComboBox *pComboBox = new CLabelComboBox(tr("触发类型:"), strList);
        pComboBox->SetComboBoxFixedSize(100, 50);
        pComboBox->SetLabelFixedSize(100, 50);
        m_pComboBoxList.push_back(pComboBox);

        CLabelLineEdit *pIndexLineEdit = new CLabelLineEdit(tr("索引:"), "0");
        pIndexLineEdit->SetLineEditFixedSize(100, 50);
        pIndexLineEdit->SetLabelFixedSize(80, 50);
        m_pIndexLineEditList.push_back(pIndexLineEdit);
    }

    QGridLayout *pGridLayout = new QGridLayout;
    pGridLayout->setContentsMargins(130, 30, 130, 30);
    pGridLayout->setHorizontalSpacing(30);
    pGridLayout->setVerticalSpacing(15);
    for(int i=0; i<2; i++)
    {
        pGridLayout->addWidget(m_pLineEditList.at(i), i, 0);
        pGridLayout->addWidget(m_pComboBoxList.at(i), i, 1);
        pGridLayout->addWidget(m_pIndexLineEditList.at(i), i, 2);
        m_pIndexLineEditList.at(i)->setVisible(false);
    }

    m_pConfirmBtn = new QPushButton(tr("确认"));
    m_pConfirmBtn->setFixedSize(120, 50);
    connect(m_pConfirmBtn, &QPushButton::clicked, this, &COptoCompose::_SlotConfirmBtn);

    m_pResetBtn = new QPushButton(tr("重置"));
    m_pResetBtn->setFixedSize(120, 50);
    connect(m_pResetBtn, &QPushButton::clicked, this, &COptoCompose::_SlotResetBtn);

    m_pCancelBtn = new QPushButton(tr("取消"));
    m_pCancelBtn->setFixedSize(120, 50);
    connect(m_pCancelBtn, &QPushButton::clicked, this, &COptoCompose::_SlotCancelBtn);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->addStretch(1);
    pBtnLayout->setSpacing(20);
    pBtnLayout->addWidget(m_pConfirmBtn);
    pBtnLayout->addWidget(m_pResetBtn);
    pBtnLayout->addWidget(m_pCancelBtn);
    pBtnLayout->addStretch(1);

    QGroupBox *pGroupBox = new QGroupBox(this);
    pGroupBox->setWindowOpacity(1);
    pGroupBox->setFixedSize(700, 400);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addStretch(1);
    pLayout->addLayout(pGridLayout);
    pLayout->addStretch(1);
    pLayout->addLayout(pBtnLayout);
    pLayout->addSpacing(20);
    pGroupBox->setLayout(pLayout);

    return pGroupBox;
}
