#ifndef WFKEYBOARD_H
#define WFKEYBOARD_H

#include <QApplication>

class WfKeyBoard
{
public:
    WfKeyBoard();

    // 设置键盘位置大小
    void SetGeometry(int x, int y, int w, int h);

    // 设置背景和按钮颜色
    void SetColor(const QString& backgroundColor = QString("#E6E6E6"), const QString& buttonColor = QString("#FFFFFF"),
                  const QString& textColor = QString("#000"), const QString& pressedColor = QString("#868482"));

    // 设置分页，默认不分页
    void SetPagingEnabled(const bool&);

    // 设置每页数量
    void SetPageSize(const int& size = 9);

    // 设置底部填充
    void SetBottomPadding(int padding = 5);

    // 设置字体
    void SetFontFamily(const QString& fontFamily);

    // 设置字体大小
    void SetFontPixelSize(const int& size = 30);

    // 获取当前支持语言
    QStringList GetSupportLanguages();

    // 安装键盘
    void InstallKeyBoard(QApplication* app, QString language = "chinese");

    // 获取当前版本
    QString Version()const;

private:
    class PrivateData;
    PrivateData *const md;
};

#endif
