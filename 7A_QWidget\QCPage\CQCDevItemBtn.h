#ifndef CQCDEVITEMBTN_H
#define CQCDEVITEMBTN_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-21
  * Description: 质控设备Item
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QPushButton>

#include "PublicParams.h"

class CQCDevItemBtn : public QPushButton
{
    Q_OBJECT
public:
    CQCDevItemBtn(int iMachineID, QWidget *parent = nullptr);

    int GetMachineID() const;
    DeviceStatus GetStatus() const;

public slots:
    void SlotSetDevStatus(int iMachineID, DeviceStatus eStatus);

private:
    QLabel *m_pIndexLabel;
    QLabel *m_pTextLabel;

    int m_iMachineID;
    DeviceStatus m_eDeviceStatus;
};

#endif // CQCDEVITEMBTN_H
