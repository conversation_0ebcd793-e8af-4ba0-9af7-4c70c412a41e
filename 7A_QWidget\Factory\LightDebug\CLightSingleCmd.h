#ifndef CLIGHTSINGALCMD_H
#define CLIGHTSINGALCMD_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-11-14
  * Description: 光学调试-单指令
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QComboBox>
#include <QCheckBox>
#include <QPushButton>
#include <QTextBrowser>
#include <QStackedWidget>

#include "CCmdBase.h"
#include "CLabelComboBox.h"
#include "CLabelLineEdit.h"

class CLightSingleCmd : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CLightSingleCmd(QWidget *parent = nullptr);
    ~CLightSingleCmd();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

private slots:
    void _SlotLedBtn();
    void _SlotLightBtn();
    void _SlotIntervalChanged(const QString &strInterval);
    void _SlotTimesChanged(const QString &strTimes);
    void _SlotLed1Checked(bool bChecked);
    void _SlotLed2Checked(bool bChecked);
    void _SlotPDChanged(int index);
    void _SlotMachineChanged(int iMachineID);
    void _SlotLedTimeout();
    void _SlotPDTimeout();

private:
    void _InitWidget();
    QString _GetLedCmd(int iMachineID);

private:
    typedef struct _SLightUiStuct
    {
        _SLightUiStuct()
        {
            bLight1Checked = false;
            bLight2Checked = false;
            iPDIndex = 0;
            pTextBrowser = nullptr;
            pLedTimer = nullptr;
            pPDTimer = nullptr;
        }

        bool bLight1Checked;
        bool bLight2Checked;
        int iPDIndex;
        QTimer *pLedTimer;
        QTimer *pPDTimer;
        QTextBrowser *pTextBrowser;
        QString strInterval;
        QString strTimes;
    }SLightUiStuct;

private:
    CLabelComboBox *m_pMachineComboBox;
    CLabelLineEdit *m_pIntevalLineEdit, *m_pTimesLineEdit;
    QCheckBox *m_pLed1CheckBox, *m_pLed2CheckBox;
    QComboBox *m_pPDComboBox;
    QPushButton *m_pLedBtn, *m_pPDBtn;
    QStackedWidget *m_pStackedWidget;
    QList<SLightUiStuct *> m_sLightUiList;
};

#endif // CLIGHTSINGALCMD_H
