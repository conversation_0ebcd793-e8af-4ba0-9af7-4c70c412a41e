#include "CHomeEnterInfoWidget.h"
#include <QBoxLayout>
#include <QGridLayout>
#include <QDateTime>
#include <QElapsedTimer>

#include "CMessageBox.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "CProjectDB.h"
#include "CHeartBeat.h"
#include "CLotInfoDB.h"
#include "CTimingTecDB.h"
#include "COperationUnit.h"
#include "CScanCodeThread.h"
#include "MDControl/CHL7MsgThread.h"

CHomeEnterInfoWidget::CHomeEnterInfoWidget(QWidget *parent)
    : QWidget(parent)
    , m_iMachineID(0)
    , m_iCurrentStep(eScanSample)
    , m_iUserLevel(eUser_Normal)
    , m_bScanCardID(false)
{
    m_strGifPathList << ":/image/ico/home/<USER>"
                     << ":/image/ico/home/<USER>"
                     << ":/image/ico/home/<USER>"
                     << ":/image/ico/home/<USER>";

    _InitWidget();
    _InitLayout();

    m_strTipsText = tr("提示");

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalAppStartEnd, this, &CHomeEnterInfoWidget::SlotAppStartEnd);
}

void CHomeEnterInfoWidget::SlotAppStartEnd()
{
    m_pCHomeSelectProjectWidget->setParent((QWidget*)gk_pMainWindow);
    m_pCHomeSelectProjectWidget->setVisible(false);

    m_pCDateTimeWidget->setParent((QWidget*)gk_pMainWindow);
    m_pCDateTimeWidget->setVisible(false);
}

void CHomeEnterInfoWidget::CreateTest(int iMachineID, QString strQCModel)
{
    QVariantList qVarList = {0};
    QString strCmd = CCmdBase::GetJsonCmdString(Method_start_identify, qVarList);
    COperationUnit::GetInstance()->SendJsonText(iMachineID, Method_start_identify, strCmd);

    m_bScanCardID = false;
    m_iMachineID = iMachineID;

    m_strQCTestModel = strQCModel;
    QString strMachine = QString("<font color=red>%1#</font>").arg(iMachineID + 1);
    m_pInfoLabel->setText(tr("%1正在创建测试，请注意检测模块指示灯").arg(strMachine));
    m_pSampleTypeWidget->SetComboBoxList(QStringList());

    _ClearData();

    if(IsQCTest(strQCModel))
    {
        m_iCurrentStep = eScanCardbox;
        QStringList strSampleList = {tr("质控样品")};
        m_pSampleTypeWidget->SetComboBoxList(strSampleList);
        m_pSampleTypeWidget->setEnabled(false);
        QString strDateTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
        m_pSampleIDWidget->SetLineEditText("QCS" + strDateTime);
        _SetStep(m_iCurrentStep);
    }
    else
    {
        m_iCurrentStep = eScanSample;
        _SetStep(m_iCurrentStep);
    }
}

int CHomeEnterInfoWidget::GetCurrentMachineID()
{
    return m_iMachineID;
}

SCardInfoStruct CHomeEnterInfoWidget::GetCardInfoStruct() const
{
    SCardInfoStruct sCardInfo;
    sCardInfo.strProject = m_strProjectName;
    sCardInfo.strCardID = m_pCardBoxIDWidget->GetLineEditText();
    sCardInfo.strCardLot = m_pCardBoxLotWidget->GetLineEditText();
    sCardInfo.strCardMFG = m_pCardBoxMFGWidget->GetDateString();
    sCardInfo.strCardEXP = m_pCardBoxEXPWidget->GetDateString();

    return sCardInfo;
}

SSampleInfoStruct CHomeEnterInfoWidget::GetSampleInfoStruct() const
{
    SSampleInfoStruct sSampleInfo;
    sSampleInfo.strQCTestModel = m_strQCTestModel;
    sSampleInfo.strSampleID = m_pSampleIDWidget->GetLineEditText();
    sSampleInfo.iSampleType = CPublicConfig::GetInstance()->GetSampleTypeKey(m_pSampleTypeWidget->GetCurrentText());
    sSampleInfo.strSampleType = m_pSampleTypeWidget->GetCurrentText();
    sSampleInfo.strSamplingDate = m_pSamplingDateWidget->GetDateString();
    sSampleInfo.strName = m_pNameWidget->GetLineEditText();
    sSampleInfo.strGender = m_pGenderWidget->GetCurrentText();
    sSampleInfo.strBirthday = m_pBirthdayWidget->GetDateString();
    sSampleInfo.strAge = m_pAgeWidget->GetAge();
    sSampleInfo.strTelephone = m_pTelephoneWidget->GetLineEditText();
    sSampleInfo.strOperator = CPublicConfig::GetInstance()->GetLoginUser();

    return sSampleInfo;
}

void CHomeEnterInfoWidget::GetRunTimingTecName(QString &strTimingName, QString &strTecName) const
{
    strTimingName = m_strTimingName;
    strTecName = m_strTecName;
}

void CHomeEnterInfoWidget::SlotLisSampleInfoMap(QMap<int, QString> strDSPMap)
{
    QString strName = strDSPMap.value(3);
    QString strBirthday = strDSPMap.value(4);
    QString strGender = strDSPMap.value(5);
    QString strPhoneNum = strDSPMap.value(11);
    QString strCardInfo = strDSPMap.value(21);
    QString strSampleID = strDSPMap.value(22);
    QString strSampleType = strDSPMap.value(26);
    QString strProjectName = strDSPMap.value(30);

    QString strUISampleID = m_pSampleIDWidget->GetLineEditText();
    qDebug()<<Q_FUNC_INFO<<strUISampleID<<strSampleID;
    if(strUISampleID != strSampleID)
    {
        qDebug()<<Q_FUNC_INFO<<"样本编号不一致:"<<strUISampleID<<strSampleID;
        return;
    }

    if(strBirthday.size() > 8)
        strBirthday = strBirthday.mid(0, 8);
    QDate qBirthDate = QDate::fromString(strBirthday, "yyyyMMdd");
    int iDays = qBirthDate.daysTo(QDate::currentDate());
    if(0 == iDays)
        iDays = 1;
    int iYears = iDays / 365;
    QString strAge;
    if(iYears > 0)
    {
        strAge = QString::number(iYears) + " " + tr("岁");
    }
    else
    {
        int iMonths = iDays / 30;
        if(iMonths > 0)
        {
            strAge = QString::number(iMonths) + " " + tr("月");
        }
        else
        {
            strAge = QString::number(iDays) + " " + tr("天");
        }
    }
    qDebug()<<Q_FUNC_INFO<<"生日:"<<strBirthday<<",年龄:"<<strAge<<",days:"<<iDays;
    if(iDays > 0) //防止生日大于实际当前时间
        m_pAgeWidget->SetAge(strAge);
    else
        m_pAgeWidget->SetAge("");

    int iGender = 0;
    if("M" == strGender)
        iGender = 1;
    else if("F" == strGender)
        iGender = 2;
    else if("O" == strGender)
        iGender = 3;
    else
        iGender = 0;
    m_pGenderWidget->SetCurrentIndex(iGender);

    m_pNameWidget->SetLineEditText(strName);
    m_pBirthdayWidget->SetDateString(qBirthDate.toString("yyyy-MM-dd"));    
    m_pTelephoneWidget->SetLineEditText(strPhoneNum);
    m_pSampleTypeWidget->SetCurrentText(strSampleType);

    m_pProjectWidget->SetLineEditText("");
    m_pCardBoxIDWidget->SetLineEditText("");
    m_pCardBoxLotWidget->SetLineEditText("");
    m_pCardBoxMFGWidget->SetDateString("");
    m_pCardBoxEXPWidget->SetDateString("");
    m_pSampleTypeWidget->SetComboBoxList(QStringList());

    //项目
    if(!strProjectName.isEmpty())
    {
        _SetProjectWidgetText(strProjectName);
        if(!CLotInfoDB::GetInstance()->IsProjectNameExist(strProjectName))
        {
            ShowInformation((QWidget*)gk_pMainWindow, m_strTipsText, tr("从LIS获取的项目名称不存在，请检查"));
            return;
        }

        m_strProjectName = strProjectName;
        QStringList strTimingTecList = CTimingTecDB::GetInstance().GetProjectRunTimingTec(strProjectName);
        if(strTimingTecList.size() >= 2)
        {
            m_strTimingName = strTimingTecList.at(0);
            m_strTecName = strTimingTecList.at(1);
        }

        _UpdateSampleTypeWidget(strProjectName);
        m_pSampleTypeWidget->SetCurrentText(strSampleType);
    }

    //试剂卡二维码解析
    if(!strCardInfo.isEmpty())
    {
        QStringList strCodeList = strCardInfo.split(";");
        if(strCodeList.size() < 5)
        {
            ShowInformation((QWidget*)gk_pMainWindow, m_strTipsText, tr("从LIS获取的试剂卡信息格式不对，请检查"));
            return;
        }

        QString strCardMFG = strCodeList.at(2);
        strCardMFG = strCardMFG.replace("A", "10");
        strCardMFG = strCardMFG.replace("B", "11");
        strCardMFG = strCardMFG.replace("C", "12");
        QDateTime qMFGDateTime;
        if(5 == strCardMFG.length())
        {
            qMFGDateTime = QDateTime::fromString("20" + strCardMFG, "yyyyMdd");
            strCardMFG = qMFGDateTime.toString("yyyy-MM-dd");
        }
        else if(6 == strCardMFG.length())
        {
            qMFGDateTime = QDateTime::fromString("20" + strCardMFG, "yyyyMMdd");
            strCardMFG = qMFGDateTime.toString("yyyy-MM-dd");
        }

        QString strCardLot = strCodeList.at(0);
        QString strCardID = strCodeList.at(1);
        QString strCardEXP = strCodeList.at(3);
        if(strCardEXP.size() >= 8)
        {
            strCardEXP = strCardEXP.mid(0, 4) + "-" + strCardEXP.mid(4, 2) + "-" + strCardEXP.mid(6, 2);
        }

        QString strProjectCode = strCodeList.at(4);
        QString strProjectName = CLotInfoDB::GetInstance()->GetProjectNameByCode(strProjectCode);
        QStringList strTimingTecList = CTimingTecDB::GetInstance().GetProjectRunTimingTec(strProjectName);
        if(strTimingTecList.size() >= 2)
        {
            m_strTimingName = strTimingTecList.at(0);
            m_strTecName = strTimingTecList.at(1);
        }
        qDebug()<<"LIS获取试剂卡信息,ID:"<<strCardID<<"Lot:"<<strCardLot<<"MFG:"<<strCardMFG
               <<"EXP:"<<strCardEXP<<"Project:"<<strProjectCode<<strProjectName
              <<"timing:"<<m_strTimingName<<"tec:"<<m_strTecName;

        //1.项目是否存在
        if(strProjectName.isEmpty())
        {
            ShowInformation((QWidget*)gk_pMainWindow, m_strTipsText, tr("从LIS获取的试剂卡信息有误，无法找到测试项目"));
            return;
        }

        //2.是否已过期
        QString strEXP = strCodeList.at(3);
        strEXP.remove("-").remove(" ");
        QString strCurrentDate = QDateTime::currentDateTime().toString("yyyyMMdd");
        if(strEXP < strCurrentDate)
        {
            ShowInformation((QWidget*)gk_pMainWindow, m_strTipsText, tr("从LIS获取的试剂卡已过期，请更换试剂卡"));
            return;
        }

        //3.是否已使用
        if(CProjectDB::GetInstance()->IsCardIDUsed(strCardID))
        {
            if(m_iUserLevel < eUser_Factory)
            {
                ShowInformation((QWidget*)gk_pMainWindow, m_strTipsText, tr("从LIS获取的试剂卡已使用，请更换试剂卡"));
                return;
            }
            else
            {
                int iBtnType = ShowQuestion((QWidget*)gk_pMainWindow, m_strTipsText, tr("从LIS获取的试剂卡已使用，是否继续"));
                if(QMessageBox::Yes != iBtnType)
                    return;
            }
        }

        m_strProjectName = strProjectName;
        _SetProjectWidgetText(strProjectName);

        m_pCardBoxIDWidget->SetLineEditText(strCardID);
        m_pCardBoxLotWidget->SetLineEditText(strCardLot);
        m_pCardBoxMFGWidget->SetDateString(strCardMFG);
        m_pCardBoxEXPWidget->SetDateString(strCardEXP);

        _UpdateSampleTypeWidget(strProjectName);
        m_pSampleTypeWidget->SetCurrentText(strSampleType);
    }
}

void CHomeEnterInfoWidget::showEvent(QShowEvent *pEvent)
{
    m_pStartTestBtn->setEnabled(true);
    m_iUserLevel = CPublicConfig::GetInstance()->GetLoginLevel();
    connect(CScanCodeThread::GetInstance(), &CScanCodeThread::SignalScanData, this, &CHomeEnterInfoWidget::_SlotScanData);
    connect(&CHL7MsgThread::GetInstace(), &CHL7MsgThread::SignalSampleInfoMap, this, &CHomeEnterInfoWidget::SlotLisSampleInfoMap);

    QWidget::showEvent(pEvent);
}

void CHomeEnterInfoWidget::hideEvent(QHideEvent *pEvent)
{
    m_bScanCardID = false;
    m_pGifMovie->stop();
    m_pStartTestBtn->setEnabled(true);
    disconnect(CScanCodeThread::GetInstance(), &CScanCodeThread::SignalScanData, this, &CHomeEnterInfoWidget::_SlotScanData);
    disconnect(&CHL7MsgThread::GetInstace(), &CHL7MsgThread::SignalSampleInfoMap, this, &CHomeEnterInfoWidget::SlotLisSampleInfoMap);

    QWidget::hideEvent(pEvent);
}

void CHomeEnterInfoWidget::_SlotSelectBtn()
{
    //防止连点出现多次弹窗
    static QElapsedTimer timer;
    if(timer.isValid() && timer.elapsed() < 500)
        return;
    timer.start();

    m_pCHomeSelectProjectWidget->raise();
    m_pCHomeSelectProjectWidget->show();
    m_pCHomeSelectProjectWidget->activateWindow();
}

void CHomeEnterInfoWidget::_SlotPreStepBtn()
{
    _SetStep(--m_iCurrentStep);
}

void CHomeEnterInfoWidget::_SlotNextStepBtn()
{
    if(eScanSample == m_iCurrentStep)
    {
        if(m_pSampleIDWidget->GetLineEditText().isEmpty())
        {
            if(m_iUserLevel < eUser_Factory)
            {
                ShowInformation((QWidget*)gk_pMainWindow, m_strTipsText, tr("样本编号不能为空"));
                return;
            }
            else
            {
                QString strDateTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
                m_pSampleIDWidget->SetLineEditText("S" + strDateTime);
            }
        }

        if(!_IsSampleIDLegal(m_pSampleIDWidget->GetLineEditText()))
            return;

        if(!_IsNameLegal(m_pNameWidget->GetLineEditText()))
            return;

        if(!_IsTelephoneLegal(m_pTelephoneWidget->GetLineEditText()))
            return;
    }

    if(eScanCardbox == m_iCurrentStep)
    {
        if(m_pProjectWidget->GetLineEditText().isEmpty())
        {
            ShowInformation((QWidget*)gk_pMainWindow, m_strTipsText, tr("测试项目不能为空"));
            return;
        }

        if(!CLotInfoDB::GetInstance()->IsProjectNameExist(m_strProjectName))
        {
            ShowInformation((QWidget*)gk_pMainWindow, m_strTipsText, tr("测试项目不存在，请检查"));
            return;
        }

        QString strCardID = m_pCardBoxIDWidget->GetLineEditText();
        if(strCardID.isEmpty())
        {
            if(m_iUserLevel < eUser_Factory)
            {
                ShowInformation((QWidget*)gk_pMainWindow, m_strTipsText, tr("试剂卡编号不能为空"));
                return;
            }
            else
            {
                QString strDateTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
                m_pCardBoxIDWidget->SetLineEditText("C" + strDateTime);
            }
        }

        if(!_IsCardIDLegal(strCardID))
            return;

        if(!_IsCardLotLegal(m_pCardBoxLotWidget->GetLineEditText()))
            return;

        if(CProjectDB::GetInstance()->IsCardIDUsed(strCardID))
        {
            if(m_iUserLevel < eUser_Factory)
            {
                ShowInformation((QWidget*)gk_pMainWindow, m_strTipsText, tr("此试剂卡已使用，请更换试剂卡"));
                return;
            }

            //扫描的CardID在扫描时已判断，这里只判断手动输入的CardID
            if(!m_bScanCardID)
            {
                int iBtnType = ShowQuestion((QWidget*)gk_pMainWindow, m_strTipsText, tr("试剂卡编号已使用，是否继续"));
                if(QMessageBox::Yes != iBtnType)
                    return;
            }
        }

        if(m_pSampleTypeWidget->GetCurrentText().isEmpty())
        {
            ShowInformation((QWidget*)gk_pMainWindow, m_strTipsText, tr("样本类型不能为空"));
            return;
        }
    }

    _SetStep(++m_iCurrentStep);
}

void CHomeEnterInfoWidget::_SlotCancelTestBtn()
{
    //防止连点出现多次弹窗
    static QElapsedTimer timer;
    if(timer.isValid() && timer.elapsed() < 500)
        return;
    timer.start();

    int iBtnType = ShowQuestion((QWidget*)gk_pMainWindow, m_strTipsText, tr("确定要取消此次测试吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    QString strCmd = CCmdBase::GetJsonCmdString(Method_stop_identify);
    COperationUnit::GetInstance()->SendJsonText(m_iMachineID, Method_stop_identify, strCmd);

    CScanCodeThread::GetInstance()->StopScan();
    emit SignalCancelTest();
}

void CHomeEnterInfoWidget::_SlotStartTestBtn()
{
    //防止连点出现多次弹窗
    static QElapsedTimer timer;
    if(timer.isValid() && timer.elapsed() < 500)
        return;
    timer.start();

    SDeviceHeartbeatStruct *pStruct = CHeartBeat::GetInstance()->GetDeviceHearbeatStruct(m_iMachineID);
    if(nullptr == pStruct)
    {
        qDebug()<<Q_FUNC_INFO<<"机器ID异常:"<<m_iMachineID;
        return;
    }
    if(CPublicConfig::GetInstance()->GetCheckCardExistBeforeStartTest())
    {
        if(false == pStruct->bCardboxExist)
        {
            ShowInformation((QWidget*)gk_pMainWindow, m_strTipsText, tr("请先将试剂卡放入检测模块"));
            return;
        }
    }

    int iBtnType = ShowQuestion((QWidget*)gk_pMainWindow, m_strTipsText, tr("请确认是否已关闭仓门"));
    if(QMessageBox::Yes != iBtnType)
    {
        return;
    }

    emit SignalStartTest(m_iMachineID);
}

void CHomeEnterInfoWidget::_SetProjectWidgetText(QString strProjectName)
{
    QFont font;
    font.setFamily("Source Han Sans CN");
    font.setPixelSize(24);

    QFontMetrics met(font);
    QString strElideText = met.elidedText(strProjectName, Qt::ElideRight, 212);
    m_pProjectWidget->SetLineEditText(strElideText);
}

void CHomeEnterInfoWidget::_UpdateSampleTypeWidget(QString strProjectName)
{
    QStringList strSampleTypeList = CPublicConfig::GetInstance()->GetSampleTypeList(strProjectName);
    if(false == strSampleTypeList.isEmpty())
    {
        if(false == IsQCTest(m_strQCTestModel))
        {
            strSampleTypeList.pop_back(); //最后1个样本是质控样本,非质控测试不应该显示
        }
    }
    m_pSampleTypeWidget->SetComboBoxList(strSampleTypeList);
    if(true == IsQCTest(m_strQCTestModel))
    {
        m_pSampleTypeWidget->SetCurrentIndex(strSampleTypeList.size() - 1);
    }
}

void CHomeEnterInfoWidget::_SlotSelectProject(const QString &strProjectName,
                                              const QString &strTimingName, const QString &strTecName)
{
    qDebug()<<"CHomeEnterInfoWidget::_SlotSelectProject"<<strProjectName<<strTimingName<<strTecName;

    m_strProjectName = strProjectName;
    m_strTimingName = strTimingName;
    m_strTecName = strTecName;

    _SetProjectWidgetText(strProjectName);
    _UpdateSampleTypeWidget(strProjectName);
}

void CHomeEnterInfoWidget::_SlotScanData(QByteArray qScanData)
{
    qDebug()<<Q_FUNC_INFO<<qScanData<<m_iCurrentStep;

    if(eScanSample == m_iCurrentStep)
    {
        System("aplay fireware/audio/bi.wav");
        m_pSampleIDWidget->SetLineEditText(qScanData.data());
        return;
    }

    if(eScanCardbox == m_iCurrentStep)
    {
        System("aplay fireware/audio/bi.wav");
        QStringList strCodeList = QString::fromLocal8Bit(qScanData).split(";");
        if(strCodeList.size() < 5)
        {
            int iBtnType = ShowQuestion((QWidget*)gk_pMainWindow, m_strTipsText, tr("试剂卡扫描失败，二维码格式不对，是否重新打开扫码头"));
            if(QMessageBox::Yes == iBtnType)
                CScanCodeThread::GetInstance()->StartScan();
            return;
        }

        QString strCardMFG = strCodeList.at(2);
        strCardMFG = strCardMFG.replace("A", "10");
        strCardMFG = strCardMFG.replace("B", "11");
        strCardMFG = strCardMFG.replace("C", "12");
        QDateTime qMFGDateTime;
        if(5 == strCardMFG.length())
        {
            qMFGDateTime = QDateTime::fromString("20" + strCardMFG, "yyyyMdd");
            strCardMFG = qMFGDateTime.toString("yyyy-MM-dd");
        }
        else if(6 == strCardMFG.length())
        {
            qMFGDateTime = QDateTime::fromString("20" + strCardMFG, "yyyyMMdd");
            strCardMFG = qMFGDateTime.toString("yyyy-MM-dd");
        }

        QString strCardLot = strCodeList.at(0);
        QString strCardID = strCodeList.at(1);
        QString strCardEXP = strCodeList.at(3);
        if(strCardEXP.size() >= 8)
        {
            strCardEXP = strCardEXP.mid(0, 4) + "-" + strCardEXP.mid(4, 2) + "-" + strCardEXP.mid(6, 2);
        }

        QString strProjectCode = strCodeList.at(4);
        QString strProjectName = CLotInfoDB::GetInstance()->GetProjectNameByCode(strProjectCode);
        QStringList strTimingTecList = CTimingTecDB::GetInstance().GetProjectRunTimingTec(strProjectName);
        if(strTimingTecList.size() >= 2)
        {
            m_strTimingName = strTimingTecList.at(0);
            m_strTecName = strTimingTecList.at(1);
        }
        qDebug()<<"试剂卡扫描信息,ID:"<<strCardID<<"Lot:"<<strCardLot<<"MFG:"<<strCardMFG
               <<"EXP:"<<strCardEXP<<"Project:"<<strProjectCode<<strProjectName
              <<"timing:"<<m_strTimingName<<"tec:"<<m_strTecName;

        //1.项目是否存在
        if(strProjectName.isEmpty())
        {
            int iBtnType = ShowQuestion((QWidget*)gk_pMainWindow, m_strTipsText, tr("试剂卡扫描失败，无法找到测试项目，是否重新打开扫码头"));
            if(QMessageBox::Yes == iBtnType)
                CScanCodeThread::GetInstance()->StartScan();
            return;
        }

        //2.是否已过期
        QString strEXP = strCodeList.at(3);
        strEXP.remove("-").remove(" ");
        QString strCurrentDate = QDateTime::currentDateTime().toString("yyyyMMdd");
        if(strEXP < strCurrentDate)
        {
            int iBtnType = ShowQuestion((QWidget*)gk_pMainWindow, m_strTipsText, tr("试剂卡已过期，是否重新打开扫码头"));
            if(QMessageBox::Yes == iBtnType)
                CScanCodeThread::GetInstance()->StartScan();
            return;
        }

        //3.是否已使用
        if(CProjectDB::GetInstance()->IsCardIDUsed(strCardID))
        {
            if(m_iUserLevel < eUser_Factory)
            {
                ShowInformation((QWidget*)gk_pMainWindow, m_strTipsText, tr("此试剂卡已使用，请更换试剂卡"));
                Delay_MSec(1500);
                CScanCodeThread::GetInstance()->StartScan();
                return;
            }

            int iBtnType = ShowQuestion((QWidget*)gk_pMainWindow, m_strTipsText, tr("试剂卡已使用，是否重新打开扫码头"));
            if(QMessageBox::Yes == iBtnType)
            {
                CScanCodeThread::GetInstance()->StartScan();
                return;
            }
            m_bScanCardID = true;
        }

        m_strProjectName = strProjectName;
        _SetProjectWidgetText(strProjectName);

        m_pCardBoxIDWidget->SetLineEditText(strCardID);
        m_pCardBoxLotWidget->SetLineEditText(strCardLot);
        m_pCardBoxMFGWidget->SetDateString(strCardMFG);
        m_pCardBoxEXPWidget->SetDateString(strCardEXP);

        _UpdateSampleTypeWidget(strProjectName);

        m_pSelectBtn->setEnabled(false);
    }
}

void CHomeEnterInfoWidget::_SlotShowDateWidget()
{
    CVNewLabelDate *pLabelDate = (CVNewLabelDate*)sender();
    if(nullptr == pLabelDate)
        return;

    m_iDateType = pLabelDate->property("DateType").toInt();
    m_pCDateTimeWidget->SetDateTime(pLabelDate->GetDateString());

    m_pCDateTimeWidget->raise();
    m_pCDateTimeWidget->show();
    m_pCDateTimeWidget->activateWindow();
}

void CHomeEnterInfoWidget::_SlotConfirmDate(const QString &strDate)
{
    if(eSamplingDate == m_iDateType)
        m_pSamplingDateWidget->SetDateString(strDate);
    else if(eBirthDate == m_iDateType)
        m_pBirthdayWidget->SetDateString(strDate);
    else if(eCardEXP == m_iDateType)
        m_pCardBoxEXPWidget->SetDateString(strDate);
    else if(eCardMFG == m_iDateType)
        m_pCardBoxMFGWidget->SetDateString(strDate);
    else
        return;
}

void CHomeEnterInfoWidget::_SlotSampleIDEditFinished()
{
    //edit finished信号输入法会触发两遍
}

void CHomeEnterInfoWidget::_SlotSampleIDChanged(const QString &strSampleID)
{
    _IsSampleIDLegal(strSampleID);

    bool bLisTwoWay = CPublicConfig::GetInstance()->GetLisTwoWay();
    qDebug()<<Q_FUNC_INFO<<strSampleID<<bLisTwoWay;

    if(bLisTwoWay)
        CHL7MsgThread::GetInstace().AddSampleID(strSampleID);
}

void CHomeEnterInfoWidget::_SlotNameChanged(const QString &strName)
{
    _IsNameLegal(strName);
}

void CHomeEnterInfoWidget::_SlotTelephoneChanged(const QString &strTelephone)
{
    _IsTelephoneLegal(strTelephone);
}

void CHomeEnterInfoWidget::_SlotCardIDChanged(const QString &strCardID)
{
    _IsCardIDLegal(strCardID);
}

void CHomeEnterInfoWidget::_SlotCardLotChanged(const QString &strCardLot)
{
    _IsCardLotLegal(strCardLot);
}

bool CHomeEnterInfoWidget::_IsSampleIDLegal(QString strSampleID)
{
    if(strSampleID.toLocal8Bit().length() > 50)
    {
        ShowInformation((QWidget*)gk_pMainWindow, m_strTipsText, tr("样本编号长度不能大于50"));
        return false;
    }
    return true;
}

bool CHomeEnterInfoWidget::_IsNameLegal(QString strName)
{
    if(strName.toLocal8Bit().length() > 50)
    {
        ShowInformation((QWidget*)gk_pMainWindow, m_strTipsText, tr("姓名长度不能大于50"));
        return false;
    }
    return true;
}

bool CHomeEnterInfoWidget::_IsTelephoneLegal(QString strTelephone)
{
    if(strTelephone.toLocal8Bit().length() > 50)
    {
        ShowInformation((QWidget*)gk_pMainWindow, m_strTipsText, tr("电话长度不能大于50"));
        return false;
    }
    return true;
}

bool CHomeEnterInfoWidget::_IsCardIDLegal(QString strCardID)
{
    if(strCardID.toLocal8Bit().length() > 50)
    {
        ShowInformation((QWidget*)gk_pMainWindow, m_strTipsText, tr("试剂卡编号长度不能大于50"));
        return false;
    }
    return true;
}

bool CHomeEnterInfoWidget::_IsCardLotLegal(QString strCardLot)
{
    if(strCardLot.toLocal8Bit().length() > 50)
    {
        ShowInformation((QWidget*)gk_pMainWindow, m_strTipsText, tr("试剂卡批次长度不能大于50"));
        return false;
    }
    return true;
}

void CHomeEnterInfoWidget::_SetStep(int iStep)
{
    qDebug()<<Q_FUNC_INFO<<iStep;
    switch (iStep)
    {
    case eScanSample:
        m_pScanSampleIDLabel->SetPropertyText("step", "yes");
        m_pScanCardIDLabel->SetPropertyText("step", "no");
        m_pAddSampleLabel->SetPropertyText("step", "no");
        m_pLoadCardBoxLabel->SetPropertyText("step", "no");

        m_pSpcingLabel1->setProperty("step", "yes");
        m_pSpcingLabel2->setProperty("step", "no");
        m_pSpcingLabel3->setProperty("step", "no");

        _SetSampleWidgetEnable(true);
        _SetCardWidgetEnable(false);
        m_pSampleBackLabel->setProperty("step", "yes");
        m_pCardBackLabel->setProperty("step", "no");

        m_pCancelTestBtn->setVisible(true);
        m_pPreStepBtn->setVisible(false);
        m_pNextStepBtn->setVisible(true);
        m_pStartTestBtn->setVisible(false);

        CScanCodeThread::GetInstance()->StartScan();
        m_pSampleIDWidget->SetLineEditFocus();

        break;
    case eScanCardbox:
        m_pScanSampleIDLabel->SetPropertyText("step", "yes");
        m_pScanCardIDLabel->SetPropertyText("step", "yes");
        m_pAddSampleLabel->SetPropertyText("step", "no");
        m_pLoadCardBoxLabel->SetPropertyText("step", "no");

        m_pSpcingLabel1->setProperty("step", "yes");
        m_pSpcingLabel2->setProperty("step", "yes");
        m_pSpcingLabel3->setProperty("step", "no");

        _SetSampleWidgetEnable(false);
        _SetCardWidgetEnable(true);
        m_pSampleBackLabel->setProperty("step", "no");
        m_pCardBackLabel->setProperty("step", "yes");

        m_pCancelTestBtn->setVisible(true);
        m_pPreStepBtn->setVisible(true);
        m_pNextStepBtn->setVisible(true);
        m_pStartTestBtn->setVisible(false);

        CScanCodeThread::GetInstance()->StartScan();
        m_pCardBoxIDWidget->SetLineEditFocus();

        break;
    case eAddSample:
        CScanCodeThread::GetInstance()->StopScan();
        m_pScanSampleIDLabel->SetPropertyText("step", "yes");
        m_pScanCardIDLabel->SetPropertyText("step", "yes");
        m_pAddSampleLabel->SetPropertyText("step", "yes");
        m_pLoadCardBoxLabel->SetPropertyText("step", "no");

        m_pSpcingLabel1->setProperty("step", "yes");
        m_pSpcingLabel2->setProperty("step", "yes");
        m_pSpcingLabel3->setProperty("step", "yes");

        _SetSampleWidgetEnable(false);
        _SetCardWidgetEnable(false);
        m_pSampleBackLabel->setProperty("step", "no");
        m_pCardBackLabel->setProperty("step", "no");

        m_pCancelTestBtn->setVisible(true);
        m_pPreStepBtn->setVisible(true);
        m_pNextStepBtn->setVisible(true);
        m_pStartTestBtn->setVisible(false);
        break;
    case eLoadCardbox:
        m_pScanSampleIDLabel->SetPropertyText("step", "yes");
        m_pScanCardIDLabel->SetPropertyText("step", "yes");
        m_pAddSampleLabel->SetPropertyText("step", "yes");
        m_pLoadCardBoxLabel->SetPropertyText("step", "yes");

        m_pSpcingLabel1->setProperty("step", "yes");
        m_pSpcingLabel2->setProperty("step", "yes");
        m_pSpcingLabel3->setProperty("step", "yes");

        _SetSampleWidgetEnable(false);
        _SetCardWidgetEnable(false);
        m_pSampleBackLabel->setProperty("step", "no");
        m_pCardBackLabel->setProperty("step", "no");

        m_pCancelTestBtn->setVisible(true);
        m_pPreStepBtn->setVisible(true);
        m_pNextStepBtn->setVisible(false);
        m_pStartTestBtn->setVisible(true);
        break;
    default:
        break;
    }

    m_pGifMovie->stop();
    m_pGifMovie->setFileName(m_strGifPathList.at(iStep));
    m_pGifMovie->start();
    m_pMovieLabel->setMovie(m_pGifMovie);

    m_pSpcingLabel1->style()->polish(m_pSpcingLabel1);
    m_pSpcingLabel2->style()->polish(m_pSpcingLabel2);
    m_pSpcingLabel3->style()->polish(m_pSpcingLabel3);

    m_pSampleBackLabel->style()->polish(m_pSampleBackLabel);
    m_pCardBackLabel->style()->polish(m_pCardBackLabel);
}

void CHomeEnterInfoWidget::_SetCardWidgetEnable(bool bEnable)
{
    m_pSelectBtn->setEnabled(bEnable);
    m_pCardBoxIDWidget->setEnabled(bEnable);
    m_pCardBoxLotWidget->setEnabled(bEnable);
    m_pCardBoxMFGWidget->setEnabled(bEnable);
    m_pCardBoxEXPWidget->setEnabled(bEnable);
    m_pSampleTypeWidget->setEnabled(bEnable);

    if(IsQCTest(m_strQCTestModel)) //质控测试样本类型不可选取,只能是质控样本
        m_pSampleTypeWidget->setEnabled(false);
}

void CHomeEnterInfoWidget::_SetSampleWidgetEnable(bool bEnable)
{
    m_pSampleIDWidget->setEnabled(bEnable);
    m_pBirthdayWidget->setEnabled(bEnable);
    //m_pSampleTypeWidget->setEnabled(bEnable);
    m_pSamplingDateWidget->setEnabled(bEnable);
    m_pAgeWidget->setEnabled(bEnable);
    m_pNameWidget->setEnabled(bEnable);
    m_pGenderWidget->setEnabled(bEnable);
    m_pTelephoneWidget->setEnabled(bEnable);
}

void CHomeEnterInfoWidget::_ClearData()
{
    m_strProjectName.clear();
    m_strTimingName.clear();
    m_strTecName.clear();

    m_pProjectWidget->SetLineEditText("");
    m_pCardBoxIDWidget->SetLineEditText("");
    m_pCardBoxLotWidget->SetLineEditText("");
    m_pCardBoxMFGWidget->SetDateString("");
    m_pCardBoxEXPWidget->SetDateString("");

    m_pSampleIDWidget->SetLineEditText("");
    m_pSampleTypeWidget->SetCurrentIndex(-1);
    m_pSamplingDateWidget->SetDateString("");
    m_pNameWidget->SetLineEditText("");
    m_pGenderWidget->SetCurrentIndex(0);
    m_pBirthdayWidget->SetDateString("");
    m_pAgeWidget->SetAge("");
    m_pTelephoneWidget->SetLineEditText("");
}

QGroupBox *CHomeEnterInfoWidget::_CreateGroupBox()
{
    m_pInfoLabel = new QLabel(tr("正在向1#创建测试，请注意设备指示灯"));
    m_pInfoLabel->setFixedSize(1670, 76);
    m_pInfoLabel->setObjectName("InfoLabel");
    m_pInfoLabel->setAlignment(Qt::AlignCenter);

    m_pGifMovie = new QMovie(m_strGifPathList.at(eAddSample), "gif", this);

    m_pMovieLabel = new QLabel;
    m_pMovieLabel->setFixedSize(508, 615);
    m_pMovieLabel->setObjectName("MovieLabel");
    m_pMovieLabel->setMovie(m_pGifMovie);

    m_pCardBackLabel = new QLabel;
    m_pCardBackLabel->setFixedSize(430, 615);
    m_pCardBackLabel->setObjectName("RectBackLabel");
    m_pCardBackLabel->setProperty("step", "no");

    m_pCardTitleWidget = new CHLabelTitleWidget(tr("试剂卡信息"));

    m_pProjectWidget = new CVLabelLineEdit(tr("测试项目") + "<font color=red> *</font>");
    m_pProjectWidget->ResetLineEditSize(252, 56);
    m_pProjectWidget->setEnabled(false);

    m_pSelectBtn = new QPushButton(tr("选择"));
    m_pSelectBtn->setFixedSize(107, 56);
    m_pSelectBtn->setObjectName("SelectBtn");
    if(eLanguage_German == gk_iLanguage || eLanguage_Italian == gk_iLanguage)
        m_pSelectBtn->setObjectName("SelectBtn-German");
    if(eLanguage_Spanish == gk_iLanguage)
        m_pSelectBtn->setObjectName("SelectBtn-Spanlish");
    connect(m_pSelectBtn, &QPushButton::clicked, this, &CHomeEnterInfoWidget::_SlotSelectBtn);

    m_pCardBoxIDWidget = new CVLabelLineEdit(tr("试剂卡编号") + "<font color=red> *</font>");
    m_pCardBoxIDWidget->ResetLineEditSize(371, 56);
    connect(m_pCardBoxIDWidget, &CVLabelLineEdit::SignalTextChanged, this, &CHomeEnterInfoWidget::_SlotCardIDChanged);

    m_pCardBoxLotWidget = new CVLabelLineEdit(tr("试剂卡批次"));
    m_pCardBoxLotWidget->ResetLineEditSize(371, 56);
    connect(m_pCardBoxLotWidget, &CVLabelLineEdit::SignalTextChanged, this, &CHomeEnterInfoWidget::_SlotCardLotChanged);

    m_pCardBoxMFGWidget = new CVNewLabelDate(tr("试剂卡生产日期"), "", 5 , this);
    m_pCardBoxMFGWidget->ResetDateLabelSize(371, 56);
    m_pCardBoxMFGWidget->setVisible(false);
    m_pCardBoxMFGWidget->setProperty("DateType", eCardMFG);
    connect(m_pCardBoxMFGWidget, &CVNewLabelDate::SignalPressEvent, this, &CHomeEnterInfoWidget::_SlotShowDateWidget);

    m_pCardBoxEXPWidget = new CVNewLabelDate(tr("试剂卡有效日期"));
    m_pCardBoxEXPWidget->ResetDateLabelSize(371, 56);
    m_pCardBoxEXPWidget->setProperty("DateType", eCardEXP);
    connect(m_pCardBoxEXPWidget, &CVNewLabelDate::SignalPressEvent, this, &CHomeEnterInfoWidget::_SlotShowDateWidget);

    m_pSampleBackLabel = new QLabel;
    m_pSampleBackLabel->setFixedSize(678, 615);
    m_pSampleBackLabel->setObjectName("RectBackLabel");
    m_pSampleBackLabel->setProperty("step", "no");

    m_pSampleTitleWidget = new CHLabelTitleWidget(tr("样本信息"));

    m_pSampleIDWidget = new CVLabelLineEdit(tr("样本编号") + "<font color=red> *</font>");
    m_pSampleIDWidget->ResetLineEditSize(303, 56);
    connect(m_pSampleIDWidget, &CVLabelLineEdit::SignalTextChanged, this, &CHomeEnterInfoWidget::_SlotSampleIDChanged);
    //connect(m_pSampleIDWidget, &CVLabelLineEdit::SignalEditingFinished, this, &CHomeEnterInfoWidget::_SlotSampleIDEditFinished);

    m_pBirthdayWidget = new CVNewLabelDate(tr("生日"));
    m_pBirthdayWidget->ResetDateLabelSize(303, 56);
    m_pBirthdayWidget->setProperty("DateType", eBirthDate);
    connect(m_pBirthdayWidget, &CVNewLabelDate::SignalPressEvent, this, &CHomeEnterInfoWidget::_SlotShowDateWidget);

    m_pSampleTypeWidget = new CVLabelComboBox(tr("样本类型") + "<font color=red> *</font>");
    m_pSampleTypeWidget->SetMaxVisibleItems(8);
    m_pSampleTypeWidget->ResetComboBoxSize(371, 56);

    m_pSamplingDateWidget = new CVNewLabelDate(tr("采样日期"));
    m_pSamplingDateWidget->ResetDateLabelSize(303, 56);
    m_pSamplingDateWidget->setProperty("DateType", eSamplingDate);
    connect(m_pSamplingDateWidget, &CVNewLabelDate::SignalPressEvent, this, &CHomeEnterInfoWidget::_SlotShowDateWidget);

    m_pAgeWidget = new CVAgeWidget;
    if(eLanguage_English == gk_iLanguage || eLanguage_German == gk_iLanguage)
    {
        m_pAgeWidget->ResetLineEditSize(141, 56);
        m_pAgeWidget->ResetComboBoxSize(150, 56);
    }
    else if(eLanguage_Spanish == gk_iLanguage)
    {
        m_pAgeWidget->ResetLineEditSize(161, 56);
        m_pAgeWidget->ResetComboBoxSize(130, 56);
    }
    else if(eLanguage_Italian == gk_iLanguage)
    {
        m_pAgeWidget->ResetLineEditSize(136, 56);
        m_pAgeWidget->ResetComboBoxSize(155, 56);
    }

    m_pNameWidget = new CVLabelLineEdit(tr("姓名"));
    m_pNameWidget->ResetLineEditSize(303, 56);
    connect(m_pNameWidget, &CVLabelLineEdit::SignalTextChanged, this, &CHomeEnterInfoWidget::_SlotNameChanged);

    m_pTelephoneWidget = new CVLabelLineEdit(tr("电话"));
    m_pTelephoneWidget->ResetLineEditSize(303, 56);
    connect(m_pTelephoneWidget, &CVLabelLineEdit::SignalTextChanged, this, &CHomeEnterInfoWidget::_SlotTelephoneChanged);

    m_pGenderWidget = new CVLabelComboBox(tr("性别"), {tr(""), tr("男"), tr("女"), tr("其他")});
    m_pGenderWidget->ResetComboBoxSize(303, 56);

    int iBtnWidth = 168;
    if(eLanguage_Spanish == gk_iLanguage)
        iBtnWidth = 220;
    else if(eLanguage_German == gk_iLanguage)
        iBtnWidth = 215;
    else if(eLanguage_Italian == gk_iLanguage)
        iBtnWidth = 195;

    m_pCancelTestBtn = new QPushButton(tr("取消测试"));
    m_pCancelTestBtn->setFixedSize(iBtnWidth, 56);
    m_pCancelTestBtn->setObjectName("CancelBtn");
    connect(m_pCancelTestBtn, &QPushButton::clicked, this, &CHomeEnterInfoWidget::_SlotCancelTestBtn);

    m_pPreStepBtn = new QPushButton(tr("上一步"));
    m_pPreStepBtn->setFixedSize(iBtnWidth, 56);
    connect(m_pPreStepBtn, &QPushButton::clicked, this, &CHomeEnterInfoWidget::_SlotPreStepBtn);

    m_pNextStepBtn = new QPushButton(tr("下一步"));
    m_pNextStepBtn->setFixedSize(iBtnWidth, 56);
    connect(m_pNextStepBtn, &QPushButton::clicked, this, &CHomeEnterInfoWidget::_SlotNextStepBtn);

    m_pStartTestBtn = new QPushButton(tr("开始测试"));
    m_pStartTestBtn->setFixedSize(iBtnWidth, 56);
    m_pStartTestBtn->setObjectName("StartTest");
    m_pStartTestBtn->setAutoRepeat(false);
    connect(m_pStartTestBtn, &QPushButton::clicked, this, &CHomeEnterInfoWidget::_SlotStartTestBtn);

    QLabel *pSepLabel1 = new QLabel;
    pSepLabel1->setFixedSize(1, 609);
    pSepLabel1->setObjectName("SepLabel");

    QLabel *pSepLabel2 = new QLabel;
    pSepLabel2->setFixedSize(1, 609);
    pSepLabel2->setObjectName("SepLabel");

    QHBoxLayout *pProjectLayout = new QHBoxLayout;
    pProjectLayout->setMargin(0);
    pProjectLayout->setSpacing(0);
    pProjectLayout->addStretch(1);
    pProjectLayout->addWidget(m_pProjectWidget);
    pProjectLayout->addSpacing(12);
    pProjectLayout->addWidget(m_pSelectBtn, 0, Qt::AlignBottom);
    pProjectLayout->addStretch(1);

    QVBoxLayout *pBoxLayout = new QVBoxLayout;
    pBoxLayout->setContentsMargins(18, 0, 0, 0);
    pBoxLayout->setSpacing(18);
    pBoxLayout->addLayout(pProjectLayout);
    pBoxLayout->addWidget(m_pCardBoxIDWidget, 0, Qt::AlignHCenter);
    pBoxLayout->addWidget(m_pCardBoxLotWidget, 0, Qt::AlignHCenter);
    //pBoxLayout->addWidget(m_pCardBoxMFGWidget, 0, Qt::AlignHCenter);
    pBoxLayout->addWidget(m_pCardBoxEXPWidget, 0, Qt::AlignHCenter);
    pBoxLayout->addWidget(m_pSampleTypeWidget, 0, Qt::AlignHCenter);

    QVBoxLayout *pCardLayout = new QVBoxLayout;
    pCardLayout->setContentsMargins(10, 0, 10, 0);
    pCardLayout->setSpacing(0);
    pCardLayout->addWidget(m_pCardTitleWidget, 0, Qt::AlignLeft);
    pCardLayout->addSpacing(24);
    pCardLayout->addLayout(pBoxLayout);
    pCardLayout->addStretch(1);
    m_pCardBackLabel->setLayout(pCardLayout);

    QGridLayout *pGridLayout = new QGridLayout;
    pGridLayout->setContentsMargins(18, 0, 0, 0);
    pGridLayout->setVerticalSpacing(18);
    pGridLayout->setHorizontalSpacing(18);
    pGridLayout->addWidget(m_pSampleIDWidget, 0, 0);
    pGridLayout->addWidget(m_pSamplingDateWidget, 1, 0);
    pGridLayout->addWidget(m_pNameWidget, 2, 0);

    pGridLayout->addWidget(m_pGenderWidget, 0, 1);
    pGridLayout->addWidget(m_pBirthdayWidget, 1, 1);
    pGridLayout->addWidget(m_pAgeWidget, 2, 1);
    pGridLayout->addWidget(m_pTelephoneWidget, 3, 0);

    QVBoxLayout *pSampleLayout = new QVBoxLayout;
    pSampleLayout->setContentsMargins(10, 0, 10, 0);
    pSampleLayout->setSpacing(0);
    pSampleLayout->addWidget(m_pSampleTitleWidget, 0, Qt::AlignLeft);
    pSampleLayout->addSpacing(24);
    pSampleLayout->addLayout(pGridLayout);
    pSampleLayout->addStretch(1);
    m_pSampleBackLabel->setLayout(pSampleLayout);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(30);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pCancelTestBtn);
    pBtnLayout->addWidget(m_pPreStepBtn);
    pBtnLayout->addWidget(m_pNextStepBtn);
    pBtnLayout->addWidget(m_pStartTestBtn);
    pBtnLayout->addStretch(1);

    QHBoxLayout *pMidLayout = new QHBoxLayout;
    pMidLayout->setMargin(0);
    pMidLayout->setSpacing(12);
    pMidLayout->addStretch(1);
    pMidLayout->addWidget(m_pMovieLabel);
    pMidLayout->addWidget(pSepLabel1);
    pMidLayout->addWidget(m_pSampleBackLabel);
    pMidLayout->addWidget(pSepLabel2);
    pMidLayout->addWidget(m_pCardBackLabel);
    pMidLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addSpacing(16);
    pLayout->addWidget(m_pInfoLabel, 0, Qt::AlignHCenter);
    pLayout->addSpacing(24);
    pLayout->addLayout(pMidLayout);
    pLayout->addStretch(1);
    pLayout->addLayout(pBtnLayout);
    pLayout->addSpacing(24);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(1684, 854);
    if(eLanguage_English == gk_iLanguage || eLanguage_German == gk_iLanguage || eLanguage_Spanish == gk_iLanguage)
        pGroupBox->setFixedSize(1684, 830);
    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}

void CHomeEnterInfoWidget::_InitWidget()
{
    m_pScanSampleIDLabel = new CVIndexTextLabel("1", tr("扫描或输入样本编号"));
    m_pScanSampleIDLabel->SetPropertyText("step", "yes");

    m_pScanCardIDLabel = new CVIndexTextLabel("2", tr("扫描或输入试剂卡编号"));
    m_pScanCardIDLabel->SetPropertyText("step", "no");

    m_pAddSampleLabel = new CVIndexTextLabel("3", tr("加样操作"));
    m_pAddSampleLabel->SetPropertyText("step", "no");

    m_pLoadCardBoxLabel = new CVIndexTextLabel("4", tr("将试剂卡放入检测模块"));
    m_pLoadCardBoxLabel->SetPropertyText("step", "no");

    QString strText = "------------->";
    if(eLanguage_German == gk_iLanguage || eLanguage_Italian == gk_iLanguage)
        strText = "------->";

    m_pSpcingLabel1 = new QLabel(strText);
    m_pSpcingLabel1->setFixedHeight(25);
    m_pSpcingLabel1->setProperty("step", "no");
    m_pSpcingLabel1->setObjectName("SpacingLabel");

    m_pSpcingLabel2 = new QLabel(strText);
    m_pSpcingLabel2->setFixedHeight(25);
    m_pSpcingLabel2->setProperty("step", "no");
    m_pSpcingLabel2->setObjectName("SpacingLabel");

    m_pSpcingLabel3 = new QLabel(strText);
    m_pSpcingLabel3->setFixedHeight(25);
    m_pSpcingLabel3->setProperty("step", "no");
    m_pSpcingLabel3->setObjectName("SpacingLabel");

    m_pCHomeSelectProjectWidget = new CHomeSelectProjectWidget;
    connect(m_pCHomeSelectProjectWidget, &CHomeSelectProjectWidget::SignalSelectProject,
            this, &CHomeEnterInfoWidget::_SlotSelectProject);

    m_pCDateTimeWidget = new CDateTimeWidget;
    m_pCDateTimeWidget->SetOnlyDateModel();
    connect(m_pCDateTimeWidget, &CDateTimeWidget::SignalDateTime, this, &CHomeEnterInfoWidget::_SlotConfirmDate);
    m_pCDateTimeWidget->setVisible(false);
}

void CHomeEnterInfoWidget::_InitLayout()
{
    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(5);
    pTopLayout->addStretch(1);
    pTopLayout->addWidget(m_pScanSampleIDLabel);
    pTopLayout->addWidget(m_pSpcingLabel1);
    pTopLayout->addWidget(m_pScanCardIDLabel);
    pTopLayout->addWidget(m_pSpcingLabel2);
    pTopLayout->addWidget(m_pAddSampleLabel);
    pTopLayout->addWidget(m_pSpcingLabel3);
    pTopLayout->addWidget(m_pLoadCardBoxLabel);
    pTopLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addLayout(pTopLayout);
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroupBox());
    this->setLayout(pLayout);
}
