#ifndef CUPDATERESULTPAGE_H
#define CUPDATERESULTPAGE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-12-26
  * Description: 升级结果
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QPushButton>
#include <QTableWidget>
#include "CHLabelTitleWidget.h"

class CUpdateResultPage : public QWidget
{
    Q_OBJECT
public:
    explicit CUpdateResultPage(QWidget *parent = nullptr);

    void SetResultMap(const QMap<int, QStringList> &iResultMap);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual void paintEvent(QPaintEvent *pEvent) override;

private slots:
    void _SlotOKButton();

private:
    QLabel *_CreateBackgroundLabel();

private:
    QLabel *m_pBackgroundLabel;
    CHLabelTitleWidget *m_pTitleWidget;
    QTableWidget *m_pTableWidget;
    QLabel *m_pInfoLabel;
    QPushButton *m_pOKButton;
};

#endif // CUPDATERESULTPAGE_H
