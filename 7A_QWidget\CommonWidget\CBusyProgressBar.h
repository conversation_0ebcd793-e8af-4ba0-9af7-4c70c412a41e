#ifndef CBUSYPROGRESSBAR_H
#define CBUSYPROGRESSBAR_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-01-24
  * Description: 繁忙进度条
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QGroupBox>
#include <QPushButton>
#include <QProgressBar>

class CBusyProgressBar : public QWidget
{
    Q_OBJECT
public:
    CBusyProgressBar(const QString &strTitle, const QString &strLabelText, QWidget *parent = nullptr);
    ~CBusyProgressBar();

    void SetCancelButtonVisible(bool visible);

    void ResetInfoText(const QString &strText);

protected:
    void paintEvent(QPaintEvent* pEvent) override;

private:
    QGroupBox *_CreateGroup();

signals:
    void SignalCancel();

private slots:
    void _SlotCancelBtn();

private:
    QString m_strTitleText;
    QString m_strInfoText;

    QLabel* m_pTitleLabel;
    QLabel* m_pInfoLabel;
    QProgressBar* m_pProgressBar;
    QPushButton * m_pCancelBtn;
};

#endif // CBUSYPROGRESSBAR_H
