﻿#ifndef _HL7_FIELD_COLLECTION_H_
#define _HL7_FIELD_COLLECTION_H_

#include "HL7Field.h"
#include <vector>
class HL7FieldCollection
{
public:
	HL7FieldCollection();
	~HL7FieldCollection();
	/*
	*@brief 在末尾添加字段元素
	*/
	void AddField(HL7Field field);

	/*
	*@brief 在index位置插入字段元素，合法的index从0开始
	*/
	void AddField(HL7Field field, std::size_t index);

	/*
	*@brief 删除指定位置的字段元素,合法的index从0开始,并返回是否删除成功
	*/
	bool DeleteField(HL7Field field, std::size_t index);

	/*
	*@brief 返回字段集合
	*/
	std::vector<HL7Field> GetFieldVector();

	/*
	*@brief 设置index位置的字段元素，如果index则返回错误
	*/
	bool SetField(HL7Field field, std::size_t index);

	/*
	*@brief 清空所有字段
	*/
	void ClearField();

private:
	std::vector<HL7Field> m_fieldVect;
};

#endif
