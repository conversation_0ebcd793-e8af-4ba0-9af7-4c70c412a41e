#include "CCalculateThread.h"
#include <QDebug>
#include <QTimer>
#include <QEventLoop>
#include "CLotInfoDB.h"
#include "CHistoryDB.h"
#include "CProjectDB.h"
#include "CRegisterDB.h"
#include "PublicFunction.h"
#include "PublicConfig.h"


extern bool G_RegisterMode;

CCalculateThread *CCalculateThread::GetInstance()
{
    static CCalculateThread calclateThread;
    return  &calclateThread;
}

CCalculateThread::CCalculateThread()
{
    qRegisterMetaType<SRunningInfoStruct>("SRunningInfoStruct");
    m_pThread = new QThread;
    this->moveToThread(m_pThread);
    connect(this, &CCalculateThread::SingalCalculate, this, &CCalculateThread::doCalclateWork);
    connect(this, &CCalculateThread::SingalMeltingCalculate, this, &CCalculateThread::doMeltingCalclateWork);
    m_pThread->start();
}

CCalculateThread::~CCalculateThread()
{
    if(m_pThread->isRunning())
    {
        m_pThread->quit();
        m_pThread->wait();
    }
    m_pThread->deleteLater();
    printf("CCalculateThread::~CCalculateThread()\n");
}

void CCalculateThread::calculateHandle(const SRunningInfoStruct& sRunInfo,const QList<QMap<double, double>>& dFLDataMapList)
{
    emit SingalCalculate(sRunInfo,dFLDataMapList);
}

void CCalculateThread::calculateMeltingHandle(const SRunningInfoStruct &sConstRunInfo, const QList<double> &dTempVec, const QList<QMap<double, double> > &dFLDataMapList)
{
    emit SingalMeltingCalculate(sConstRunInfo,dTempVec,dFLDataMapList);
}

void CCalculateThread::doCalclateWork(SRunningInfoStruct sRunInfo, QList<QMap<double, double>> dFLDataMapList)
{
#if 1
    QString result;
    qDebug()<<"任务开始工作 in Thread "<<thread()->currentThreadId()<<result;
    SRunningInfoStruct sRunInfoTmp = sRunInfo;
    SLotInfoStruct sLotInfo;
    CLotInfoDB::GetInstance()->GetLotInfoByShowName(sRunInfoTmp.sResultInfo.strProjectName, sLotInfo);
    QStringList strUpliftValueList = sLotInfo.strUpliftThresholdValue.split(";");
    QStringList strFlThreshouldList = sLotInfo.strFlThreshouldValue.split(";");
    QStringList strFirstDevValueList = sLotInfo.strFirstDevThreshouldValue.split(";");
    QStringList strFlInterfereValueList = sLotInfo.FlInterfereKPCR.split(";");
    QList<float>& fAmplifyList = sRunInfoTmp.fAmplifyList;

    qDebug()<<Q_FUNC_INFO<<"抬升值:"<<strUpliftValueList<<"荧光阈值:"<<strFlThreshouldList<<"一阶导数阈值:"<<strFirstDevValueList<<"荧光串扰系数:"<<strFlInterfereValueList;
    QStringList strCTList,  strCTinfoList;
    for(int i=0; i<dFLDataMapList.size(); i++)
    {
        QList<double> dRawFLList = dFLDataMapList.at(i).values();
        qDebug()<<Q_FUNC_INFO<<dRawFLList.size()<<dRawFLList;
        //if(dRawFLList.size() < sLotInfo.iMinCycleCount)
        //    continue;

        QString strHole = QString::number(i / 4);
        QString strColor = gk_strColorNameList.at(i % 4);

        if(strColor == "G" && i < strFlInterfereValueList.size())
        {
            float interfereValue = strFlInterfereValueList.at(i).toFloat();
            if(interfereValue > 0 && (i-1) >= 0)
            {
                QList<double> dBFLList = dFLDataMapList.at(i-1).values();
                int iMinCount = qMin(dBFLList.size(),dRawFLList.size());
                for(int j = 0; j < iMinCount; j++)
                {
                    dRawFLList[j] = dRawFLList[j] - dBFLList.at(j)*interfereValue;
                }
            }
        }
        else if(strColor == "Y" && i < strFlInterfereValueList.size())
        {
            float interfereValue = strFlInterfereValueList.at(i).toFloat();
            if(interfereValue > 0 && (i-1) >= 0)
            {
                QList<double> dGFLList = dFLDataMapList.at(i-1).values();
                int iMinCount = qMin(dGFLList.size(),dRawFLList.size());
                for(int j = 0; j < iMinCount; j++)
                {
                    dRawFLList[j] = dRawFLList[j] - dGFLList.at(j)*interfereValue;
                }
            }
        }
        QStringList strConfigList = {"","","","","",""};
        if( i < strUpliftValueList.size())
        {
            strConfigList[0] = strUpliftValueList.at(i);
        }
        if( i < strFlThreshouldList.size())
        {
            strConfigList[1] = strFlThreshouldList.at(i);
        }
        if( i < strFirstDevValueList.size())
        {
            strConfigList[2] = strFirstDevValueList.at(i);
        }
        if( i < fAmplifyList.size())
        {
            strConfigList[3] = "";

            if(CPublicConfig::GetInstance()->GetDynamicUpValue())
            {
                strConfigList[4] = "true";
            }
            else
            {
                strConfigList[4] = "false";
            }
            strConfigList[5] = QString("%1").arg(fAmplifyList.at(i));
        }

        QString strConfig = strConfigList.join(";");

        m_calcLib.CalcCtValue(dRawFLList,strConfig);

        QString strCTInfo;
        QStringList strReslutList = m_calcLib.getResultInfoStringList();
        if(G_RegisterMode && dRawFLList.size() >= 20)
        {
            QString strCardID = sRunInfo.sResultInfo.strCardID;
            QStringList strInfoList;
            qDebug()<<"注册模式,预置CT:"<<strCardID<<strInfoList;
            CRegisterDB::GetInstance()->GetCTInfo(strCardID, strInfoList);
            if(!strInfoList.isEmpty())
            {
                QStringList strDBCTList = strInfoList.at(0).split(";");
                QStringList strDBUpliftList = strInfoList.at(1).split(";");
                QStringList strDBThresList = strInfoList.at(2).split(";");
                if(strReslutList.size() >= 3)
                {
                    if(i < strDBThresList.size())
                        strReslutList[0] = strDBThresList.at(i);

                    if(i < strDBUpliftList.size())
                        strReslutList[1] = strDBUpliftList.at(i);

                    if(i < strDBCTList.size())
                        strReslutList[2] = strDBCTList.at(i);
                }
            }
        }

        float fCtValue = -1;
        if(4 == strReslutList.size())
        {
            strCTInfo = strReslutList.join(",");
            fCtValue = strReslutList.at(2).toFloat();
        }
        else
        {
            strCTInfo = "-1,-1,-1,0-0";
        }
        qDebug()<<Q_FUNC_INFO<<",threshould,BenchValue,CtValue,baseLine:"<<strCTInfo<<",strconfig:"<<strConfig;

        strCTinfoList.append(strCTInfo);
        strCTList << strReslutList.at(2);
        //strResultList << _GetAmpResult(fCtValue);// 当前靶标结果 这个计算方法应该算完所有Ct后计算；

        QString strTestTime = sRunInfoTmp.sCardInfo.strTestTime;
        strTestTime.remove(":").remove("-").remove(" ");

        QString strFLID = sRunInfoTmp.sCardInfo.strCardID + "+" + strTestTime + "_" + strHole + "-" + strColor;

        QString strRaw = DoubleList2StringList(dRawFLList).join(",");
        QList<qreal> dFitList = getYListFromQPontF( m_calcLib.getDeltaRnCorrectPointFList());
        QString strFit = DoubleList2StringList(dFitList).join(",");
        //QList<qreal> dBaseLineList = getYListFromQPontF(m_calcLib.getBaseLinePointFList());
        //QString strBaseline = DoubleList2StringList(dBaseLineList).join(",");
        //QList<qreal> dSmoothList = getYListFromQPontF(m_calcLib.getSmoothPointFList());
        //QString strSmooth = DoubleList2StringList(dSmoothList).join(",");
        //QList<qreal> dDeltaList = getYListFromQPontF( m_calcLib.getSigmoidRawDataFList(CCalCTLib::fitModelEm::kDelatRnSigmoid));
        //QString strDelta = DoubleList2StringList(dDeltaList).join(",");
        CHistoryDB::GetInstance()->AddCTData(strFLID, dRawFLList.size(), strRaw, strFit,
                                             "", "", "");
    }

    // add by chenhao 验证靶标，验证全血
    bool b3PNMethod = true;
    if("Vector-borne Pathogen Panel" == sRunInfoTmp.sResultInfo.strProjectName
            &&(sRunInfoTmp.sSampleInfo.iSampleType == 13 || sRunInfoTmp.sSampleInfo.iSampleType == 14))
    {
        b3PNMethod = false;
    }
    QString strResult = _GetAmpResult(strCTList.join(";"),sLotInfo.strThresholdValue,sRunInfoTmp.sResultInfo.strProjectName,sLotInfo.strCurveBRGYLine,sRunInfo.sResultInfo.strMode,sLotInfo.strPQCCutoffValue,b3PNMethod);
    sRunInfoTmp.sResultInfo.strCTValue = strCTList.join(";");
    sRunInfoTmp.sResultInfo.strResult = strResult;
    sRunInfoTmp.sResultInfo.strCTInfo = strCTinfoList.join(";");
    sRunInfoTmp.sResultInfo.iStatus = sRunInfo.sResultInfo.iStatus;
    qDebug()<<"更新历史结果:"<<sRunInfoTmp.sResultInfo.iHistoryID<<sRunInfoTmp.sResultInfo.strCTValue<<sRunInfoTmp.sResultInfo.strResult<<",CTInfo:"<<sRunInfoTmp.sResultInfo.strCTInfo;
    CProjectDB::GetInstance()->UpdateHistroyTestData(sRunInfoTmp.sResultInfo);

    if(bPCRTecType(sRunInfoTmp.sResultInfo.iTestProject))
    {
        emit SingalCalcResultReady(sRunInfoTmp.sResultInfo.iMachineID,sRunInfoTmp.sResultInfo.iHistoryID,sRunInfoTmp.sResultInfo.iStatus);
    }

#endif
}

void CCalculateThread::doMeltingCalclateWork(SRunningInfoStruct sConstRunInfo,QList<double> dTempVec, QList<QMap<double, double> > dFLDataMapList)
{
    SRunningInfoStruct sRunInfo = sConstRunInfo;

    QString strTestTime = sRunInfo.sCardInfo.strTestTime;
    strTestTime.remove(":").remove("-").remove(" ");

    SLotInfoStruct sLotInfo;
    CLotInfoDB::GetInstance()->GetLotInfoByShowName(sRunInfo.sResultInfo.strProjectName, sLotInfo);

    QStringList strRmThresholdList = sLotInfo.strRmThreshold.split(";");
    QStringList strTmThresholdList = sLotInfo.strTmRange.split(";");
    QStringList strAmpThreshouldList = sLotInfo.strAmplitudeThresholdValue.split(";");

    qDebug()<<Q_FUNC_INFO<<",TmThreshold： "<<strTmThresholdList <<", RmThreshold: " << strRmThresholdList <<"   ,AmpThreshold: "<< strAmpThreshouldList; //阈值配置

    QStringList StrTmResultList,StrRmResultList,strMeltingInfoList;
    if(dTempVec.isEmpty())
    {
        return;
    }
    QList<double> realTempVec;
    for (int i = 0;i < dTempVec.size(); i++)
    {
        realTempVec.push_back(dTempVec.at(i) /100.0);
    }

    for(int i = 0 ; i < dFLDataMapList.size(); i++)
    {
        QList<double> dRawFLList = dFLDataMapList.at(i).values();
        qDebug()<<Q_FUNC_INFO<<dRawFLList.size()<<dRawFLList;

        if(dRawFLList.size() < sLotInfo.iMinCycleCount)
            continue;

        QStringList strConfigList = {"","",""};
        if( i < strRmThresholdList.size())
        {
            strConfigList[0] = strRmThresholdList.at(i);
        }
        if( i < strAmpThreshouldList.size())
        {
            strConfigList[1] = strAmpThreshouldList.at(i);
        }
        if( i < strTmThresholdList.size())
        {
            strConfigList[2] = strTmThresholdList.at(i);//Tm范围  45.1&90.1;
        }


        m_MeltingCalc.meltingCalc(realTempVec,dRawFLList,strConfigList.join(";"));//这个配置参数得从数据库中获取
        QList<QPointF> pointDevList = m_MeltingCalc.getDerivatePointList();
        QList<double> devList;
        for (auto& item : pointDevList) {
            devList.push_back(item.y());
        }

        /* 不需要用到的平滑数据 不写入数据库
         * QList<QPointF> pointSmoothList = m_MeltingCalc.getSmoothPointList();

        QList<double> smoothList;
        for (auto& item : pointSmoothList) {
            smoothList.push_back(item.y());
        }
        */
        QString strMeltingInfo = m_MeltingCalc.getTmRmStrResult();
        strMeltingInfoList.push_back(strMeltingInfo);

        QStringList strTmList;//("26.8,26.8;");
        QStringList strRmList;//("144,144;");
        QStringList strYmList;
        QString strThreashould;
        _GetTmFormMeltInfo(strMeltingInfo,strTmList,strRmList,strYmList,strThreashould);

        StrTmResultList.append(strTmList.join(","));
        StrRmResultList.append(strRmList.join(","));
        qDebug()<<Q_FUNC_INFO<<",Tm： "<<strTmList<<"   ,Rm: "<< strRmList;


        QString strCurrentTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
        if(sRunInfo.sCardInfo.strCardID.isEmpty())
            sRunInfo.sCardInfo.strCardID = strCurrentTime;

        QString strHole = QString::number(i / 4);
        QString strColor = gk_strColorNameList.at(i % 4);
        QString strFLID = sRunInfo.sCardInfo.strCardID + "+" + strTestTime + "_" + strHole + "-" + strColor;

        QString strTempData = DoubleList2StringList(realTempVec).join(",");
        QString strFLData = DoubleList2StringList(dRawFLList).join(",");
        //QString strSmoothData = DoubleList2StringList(smoothList).join(",");
        QString strDevFirstData = DoubleList2StringList(devList).join(",");

        // 插入温度与熔解FL数据
        //CHistoryDB::GetInstance()->AddMeltingData(strFLID, dRawFLList.size(), "", strFLData);
        CHistoryDB::GetInstance()->AddMeltingTestData(strFLID, "",dRawFLList.size(),strFLData,"");
        CHistoryDB::GetInstance()->AddHrmData(strFLID,dRawFLList.size(),strTempData,strFLData,"",strDevFirstData);


    }


    QStringList strWildTmThresholdList = sLotInfo.strWildTypeTmValue.split(";");
    QStringList strTempRangeValueList =  sLotInfo.strTempRangeValue.split(";");


    //sRunInfo.sResultInfo.strTmValue = StrTmResultList.join(";");
    //sRunInfo.sResultInfo.strRmValue = StrRmResultList.join(";");
    sRunInfo.sResultInfo.strMeltingInfo = strMeltingInfoList.join(";");


    // 用到Ct值，Tm值，Ct结果，Tm参考区间    
    QString strProjectName = sRunInfo.sResultInfo.strProjectName;

    SResultInfoStruct sCtResultInfo;

    CProjectDB::GetInstance()->GetHistoryData( sRunInfo.sResultInfo.iHistoryID,sCtResultInfo);
    // Result需要从数据库取 覆盖一下；
    QStringList strCtInfoList = sCtResultInfo.strCTInfo.split(";");
    QStringList strCtResult = sCtResultInfo.strResult.split(";");

    QStringList strHoleNameList =  sLotInfo.strCurveName.split(";");
    sRunInfo.sResultInfo.strHrmResult = GetMeltingResult(strProjectName,strHoleNameList,strCtInfoList,strCtResult,strMeltingInfoList,strWildTmThresholdList,strTempRangeValueList);
    sRunInfo.sResultInfo.iStatus  = sConstRunInfo.sResultInfo.iStatus;

    qDebug()<<"熔解曲线，更新历史结果HistoryId: "<<sRunInfo.sResultInfo.iHistoryID<<", TmValue: " <<sRunInfo.sResultInfo.strTmValue<<", RmValue: " <<sRunInfo.sResultInfo.strRmValue<<", result: "<<sRunInfo.sResultInfo.strResult;

    QString strFLID = sRunInfo.sCardInfo.strCardID + "+" + strTestTime;
    QString strTempData = DoubleList2StringList(realTempVec).join(",");
    CHistoryDB::GetInstance()->AddMeltingData(strFLID, dTempVec.size(), "", strTempData);
    CProjectDB::GetInstance()->UpdateHistroyMeltingTestData(sRunInfo.sResultInfo);

    if(bHrmTecType(sRunInfo.sResultInfo.iTestProject))
    {
        emit SingalCalcResultReady(sRunInfo.sResultInfo.iMachineID,sRunInfo.sResultInfo.iHistoryID,sRunInfo.sResultInfo.iStatus);
    }
}

QString CCalculateThread::_GetAmpResult(const QString &strCt, const QString& strCtCutoffValue, const QString& strProjectName, const QString& strHoleName,const QString& strQCTestModel,const QString& strQCCtCutoffValue,bool b3PNMethod)
{
    SPNIParamStruct sPNIParam;
    sPNIParam.init();
    sPNIParam.m_strCt = strCt;
    sPNIParam.m_strCtCutoffValue = strCtCutoffValue;
    sPNIParam.m_strQCCtCutoff = strQCCtCutoffValue;
    sPNIParam.m_b3PNMethod = b3PNMethod;
    sPNIParam.m_strProjectName = strProjectName;
    sPNIParam.m_strHoleName = strHoleName;
    sPNIParam.m_strQCTestModel = strQCTestModel;
    return m_CPNICodeResult.GetResultFromCTValue(sPNIParam);
}

QStringList CCalculateThread::DoubleList2StringList(const QList<double> &dDataList)
{
    QStringList strList;
    for(int i=0; i<dDataList.size(); i++)
        strList.push_back(QString::number(dDataList.at(i)));
    return strList;
}

QList<qreal> CCalculateThread::getYListFromQPontF(const QList<QPointF> &qSrc)
{
    QList<qreal> dSrcDataVector;
    for(int i = 0; i < qSrc.length(); ++i)
    {
        dSrcDataVector.push_back(qSrc.at(i).y());
    }
    return dSrcDataVector;
}


