#include "CSoftUpdate.h"
#include <QHeaderView>
#include <QFileDialog>
#include <QApplication>
#include "CMessageBox.h"
#include "COperationUnit.h"
#include "include/ccalctlib.h"

#define UPDATE_TIME_OUT 45*1000

CSoftUpdate::CSoftUpdate(QWidget *parent) : QWidget(parent)
{
    m_bShow = false;
    m_iUiMachineID = 0;
    m_iUpdateType = -1;
    m_iOnePieceLen = 1024;
    m_strCurrentDir = QApplication::applicationDirPath() + "/";

    for(int i=0; i<gk_iMachineCount; i++)
        m_sPLCVerList << SPLCVerStruct();

    m_pUpdateTimer = new QTimer(this);
    connect(m_pUpdateTimer, &QTimer::timeout, this, &CSoftUpdate::_SlotUpdateTimeout);

    Register2Map(Method_sys_info);
    Register2Map(Method_reboot);
    Register2Map(Method_upgrade_data);
    Register2Map(Method_upgrade_end);

    Register2Map(Method_pcr_version);
    Register2Map(Method_pcr_reboot);
    Register2Map(Method_pcr_upgrade_data);
    Register2Map(Method_pcr_upgrade_end);

    Register2Map(Method_fl_version);
    Register2Map(Method_fl_reboot);
    Register2Map(Method_fl_upgrade_data);
    Register2Map(Method_fl_upgrade_end);

    Register2Map(Method_heart_beat);

    _InitWidget();
    _InitLayout();

    m_pProgressBar = new CProgressBar(m_strTipsText, "", this);
    m_pProgressBar->setVisible(false);

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalSetPowerVersion, this, &CSoftUpdate::SlotPowerVersion);
}

CSoftUpdate::~CSoftUpdate()
{
    UnRegister2Map(Method_sys_info);
    UnRegister2Map(Method_reboot);
    UnRegister2Map(Method_upgrade_data);
    UnRegister2Map(Method_upgrade_end);

    UnRegister2Map(Method_pcr_version);
    UnRegister2Map(Method_pcr_reboot);
    UnRegister2Map(Method_pcr_upgrade_data);
    UnRegister2Map(Method_pcr_upgrade_end);

    UnRegister2Map(Method_fl_version);
    UnRegister2Map(Method_fl_reboot);
    UnRegister2Map(Method_fl_upgrade_data);
    UnRegister2Map(Method_fl_upgrade_end);

    UnRegister2Map(Method_heart_beat);
}

void CSoftUpdate::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    switch (iMethodID)
    {
    case Method_sys_info:
    case Method_reboot:
    case Method_upgrade_data:
    case Method_upgrade_end:
        _MedainCmdReply(iMachineID, iMethodID, iResult, qVarData);
        break;
    case Method_pcr_version:
    case Method_pcr_reboot:
    case Method_pcr_upgrade_data:
    case Method_pcr_upgrade_end:
        _PCRCmdReply(iMachineID, iMethodID, iResult, qVarData);
        break;
    case Method_fl_version:
    case Method_fl_reboot:
    case Method_fl_upgrade_data:
    case Method_fl_upgrade_end:
        _FLCmdReply(iMachineID, iMethodID, iResult, qVarData);
        break;
    case Method_heart_beat:
        _CheckPLCVersion(iMachineID);
        break;
    default:
        break;
    }
}

void CSoftUpdate::showEvent(QShowEvent *pEvent)
{
    m_bShow = true;
    m_pPowerVersionLabel->setText(CPublicConfig::GetInstance()->GetPowerVersion());
    QWidget::showEvent(pEvent);
}

void CSoftUpdate::hideEvent(QHideEvent *pEvent)
{
    m_bShow = false;
    QWidget::hideEvent(pEvent);
}

void CSoftUpdate::_MedainCmdReply(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    switch (iMethodID)
    {
    case Method_sys_info:
    {
        if(qVarData.isNull())
            return;
        QVariantMap qVarMap = qVarData.toMap();
        QString strAppVer = qVarMap.value("APPVER").toString();
        if(qVarMap.contains("firmware"))
            strAppVer = qVarMap.value("firmware").toString();
        QString strHardVer = qVarMap.value("HARDVER").toString();
        QString strBootVer = qVarMap.value("BOOTVER").toString();
        QString strVersion = strAppVer + " " + strHardVer + " " + strBootVer;
        qDebug()<<QString("%1#中位机版本:%2").arg(iMachineID + 1).arg(strVersion);
        m_sPLCVerList[iMachineID].strMedianShowVersion = strVersion;
        m_sPLCVerList[iMachineID].strMedianAppVersion = strAppVer;
        m_sPLCVerList[iMachineID].strMedianHardVersion = strHardVer;
        m_sPLCVerList[iMachineID].strMedianBootVersion = strBootVer;
        m_sPLCVerList[iMachineID].strSN = qVarMap.value("SN").toString();
        CPublicConfig::GetInstance()->SetPLCVersionStruct(iMachineID, m_sPLCVerList.at(iMachineID));

        if(iMachineID == m_pMachineComboBox->currentIndex())
        {
            _SetTableItem(0, 2, strAppVer);
            _SetTableItem(0, 3, strHardVer);
            _SetTableItem(0, 4, strBootVer);
        }
        break;
    }
    case Method_reboot:
        qDebug()<<QString("%1#中位机重启:%2").arg(iMachineID + 1).arg(iResult);
        break;
    case Method_upgrade_data:
        if(!m_bShow)
            return;
        _UpdateFirmReply(iMachineID, iMethodID, iResult, qVarData);
        break;
    case Method_upgrade_end:
        if(!m_bShow)
            return;
        m_byteUpdateData.clear();
        m_pUpdateTimer->stop();
        m_pProgressBar->close();
        qDebug()<<QString("%1#中位机升级:%2").arg(iMachineID + 1).arg(iResult);
        if(0 == iResult)
            ShowSuccess(this, m_strTipsText, tr("中位机升级成功"));
        else
            ShowError(this, m_strTipsText, tr("中位机升级失败"));
        break;
    default:
        break;
    }
}

void CSoftUpdate::_PCRCmdReply(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    switch (iMethodID)
    {
    case Method_pcr_version:
    {
        QVariantMap qVarMap = qVarData.toMap();
        QString strAppVer = qVarMap.value("APPVER").toString();
        QString strHardVer = qVarMap.value("HARDVER").toString();
        QString strBootVer = qVarMap.value("BOOTVER").toString();
        QString strVersion = strAppVer + " " + strHardVer + " " + strBootVer;
        qDebug()<<QString("%1#PCR版本:%2").arg(iMachineID + 1).arg(strVersion);
        m_sPLCVerList[iMachineID].strPCRShowVersion = strVersion;
        m_sPLCVerList[iMachineID].strPCRAppVersion = strAppVer;
        m_sPLCVerList[iMachineID].strPCRHardVersion = strHardVer;
        m_sPLCVerList[iMachineID].strPCRBootVersion = strBootVer;
        CPublicConfig::GetInstance()->SetPLCVersionStruct(iMachineID, m_sPLCVerList.at(iMachineID));

        if(iMachineID == m_pMachineComboBox->currentIndex())
        {
            _SetTableItem(1, 2, strAppVer);
            _SetTableItem(1, 3, strHardVer);
            _SetTableItem(1, 4, strBootVer);
        }
        break;
    }
    case Method_pcr_reboot:
        qDebug()<<QString("%1#PCR重启:%2").arg(iMachineID + 1).arg(iResult);
        break;
    case Method_pcr_upgrade_data:
        if(!m_bShow)
            return;
        _UpdateFirmReply(iMachineID, iMethodID, iResult, qVarData);
        break;
    case Method_pcr_upgrade_end:
        if(!m_bShow)
            return;
        m_byteUpdateData.clear();
        m_pUpdateTimer->stop();
        m_pProgressBar->close();
        qDebug()<<QString("%1#PCR升级:%2").arg(iMachineID + 1).arg(iResult);
        if(0 == iResult)
            ShowSuccess(this, m_strTipsText, tr("PCR升级成功"));
        else
            ShowError(this, m_strTipsText, tr("PCR升级失败"));
        break;
    default:
        break;
    }
}

void CSoftUpdate::_FLCmdReply(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    switch (iMethodID)
    {
    case Method_fl_version:
    {
        QVariantMap qVarMap = qVarData.toMap();
        QString strAppVer = qVarMap.value("APPVER").toString();
        QString strHardVer = qVarMap.value("HARDVER").toString();
        QString strBootVer = qVarMap.value("BOOTVER").toString();
        QString strVersion = strAppVer + " " + strHardVer + " " + strBootVer;
        qDebug()<<QString("%1#FL版本:%2").arg(iMachineID + 1).arg(strVersion);
        m_sPLCVerList[iMachineID].strFLShowVersion = strVersion;
        m_sPLCVerList[iMachineID].strFLAppVersion = strAppVer;
        m_sPLCVerList[iMachineID].strFLHardVersion = strHardVer;
        m_sPLCVerList[iMachineID].strFLBootVersion = strBootVer;
        CPublicConfig::GetInstance()->SetPLCVersionStruct(iMachineID, m_sPLCVerList.at(iMachineID));

        if(iMachineID == m_pMachineComboBox->currentIndex())
        {
            _SetTableItem(2, 2, strAppVer);
            _SetTableItem(2, 3, strHardVer);
            _SetTableItem(2, 4, strBootVer);
        }
        break;
    }
    case Method_fl_reboot:
        qDebug()<<QString("%1#FL重启:%2").arg(iMachineID + 1).arg(iResult);
        break;
    case Method_fl_upgrade_data:
        if(!m_bShow)
            return;
        _UpdateFirmReply(iMachineID, iMethodID, iResult, qVarData);
        break;
    case Method_fl_upgrade_end:
        if(!m_bShow)
            return;
        m_byteUpdateData.clear();
        m_pUpdateTimer->stop();
        m_pProgressBar->close();
        qDebug()<<QString("%1#荧光升级:%2").arg(iMachineID + 1).arg(iResult);
        if(0 == iResult)
            ShowSuccess(this, m_strTipsText, tr("荧光升级成功"));
        else
            ShowError(this, m_strTipsText, tr("荧光升级失败"));
        break;
    default:
        break;
    }
}

void CSoftUpdate::_SlotMachineChanged(int iMachineID)
{
    m_iUiMachineID = iMachineID;
    SPLCVerStruct sVerStruct = m_sPLCVerList.at(iMachineID);

    _SetTableItem(0, 2, sVerStruct.strMedianAppVersion);
    _SetTableItem(0, 3, sVerStruct.strMedianHardVersion);
    _SetTableItem(0, 4, sVerStruct.strMedianBootVersion);

    _SetTableItem(1, 2, sVerStruct.strPCRAppVersion);
    _SetTableItem(1, 3, sVerStruct.strPCRHardVersion);
    _SetTableItem(1, 4, sVerStruct.strPCRBootVersion);

    _SetTableItem(2, 2, sVerStruct.strFLAppVersion);
    _SetTableItem(2, 3, sVerStruct.strFLHardVersion);
    _SetTableItem(2, 4, sVerStruct.strFLBootVersion);
}

void CSoftUpdate::_SlotMedianBtnList()
{
    QPushButton *pBtn = dynamic_cast<QPushButton *>(sender());
    if(nullptr == pBtn)
        return;

    int index = pBtn->property("index").toInt();
    switch (index)
    {
    case 0: _QueryMedian(m_iUiMachineID);  break;
    case 1: _UpdateMedian(); break;
    case 2: _RebootMedian(); break;
    default: break;
    }
}

void CSoftUpdate::_SlotPCRBtnList()
{
    QPushButton *pBtn = dynamic_cast<QPushButton *>(sender());
    if(nullptr == pBtn)
        return;

    int index = pBtn->property("index").toInt();
    switch (index)
    {
    case 0: _QueryPCR(m_iUiMachineID);  break;
    case 1: _UpdatePCR(); break;
    case 2: _RebootPCR(); break;
    default: break;
    }
}

void CSoftUpdate::_SlotFLBtnList()
{
    QPushButton *pBtn = dynamic_cast<QPushButton *>(sender());
    if(nullptr == pBtn)
        return;

    int index = pBtn->property("index").toInt();
    switch (index)
    {
    case 0: _QueryFL(m_iUiMachineID);  break;
    case 1: _UpdateFL(); break;
    case 2: _RebootFL(); break;
    default: break;
    }
}

void CSoftUpdate::_SlotUpdateTimeout()
{
    m_byteUpdateData.clear();
    m_pUpdateTimer->stop();
    m_pProgressBar->close();
    qDebug()<<"升级失败,超时";
    ShowError(this, m_strTipsText, tr("升级失败，超时"));
}

void CSoftUpdate::_SlotUpdateApp()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定升级上位机软件吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    QDir dir(GetUDiskDir());
    if(!dir.exists())
    {
        ShowInformation(this, m_strTipsText, tr("请先插入U盘"));
        return;
    }

    QString strAppPath = GetUDiskUpdateDir() + gk_strAppName;
    if(!QFile::exists(strAppPath))
    {
        ShowInformation(this, m_strTipsText, tr("U盘中文件不存在"));
        return;
    }

    QString strPdfPath = GetUDiskUpdateDir() + "7CPDF";
    if(QFile::exists(strPdfPath))
    {
        System(QString("cp %1 /root/app/").arg(strPdfPath));
    }

    QString strStartPath = GetUDiskUpdateDir() + "7CStart";
    if(QFile::exists(strStartPath))
    {
        System(QString("cp %1 /root/app/").arg(strStartPath));
    }

    System("rm /root/app/7CAPP");
    System(QString("cp %1 /root/app/").arg(strAppPath));
    System("sync");

    if(QFile::exists("/root/app/7CAPP"))
    {
        ShowSuccess(this, tr("提示"), tr("上位机软件升级成功，正在重启"));
        Delay_MSec(500);
        System("reboot");
    }
    else
    {
        ShowError(this, m_strTipsText, tr("上位机软件升级失败"));
    }
}

void CSoftUpdate::_SlotUpdateCalcLib()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定升级算法库吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    QString strFilePath = GetUDiskUpdateDir() + gk_strAlgorithmName;
    QDir dir(GetUDiskDir());
    if(!dir.exists())
    {
        ShowInformation(this, m_strTipsText, tr("请先插入U盘"));
        return;
    }

    if(!QFile::exists(strFilePath))
    {
        ShowInformation(this, m_strTipsText, tr("U盘中文件不存在"));
        return;
    }

    QString strLibPath = QApplication::applicationDirPath()+"/lib/" + gk_strAlgorithmName;
    QString strCopyCmd = QString("cp %1 %2").arg(strFilePath).arg(strLibPath);
    System(strCopyCmd);
    System("sync");

    if(QFile::exists(strLibPath))
    {
        ShowSuccess(this, tr("提示"), tr("算法库升级成功，正在重启"));
        Delay_MSec(500);
        System("reboot");
    }
    else
    {
        ShowError(this, m_strTipsText, tr("算法库升级失败"));
    }
}

void CSoftUpdate::_SlotUpdateAuto()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定升级配置吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    QDir dir(GetUDiskDir());
    if(!dir.exists())
    {
        ShowInformation(this, m_strTipsText, tr("请先插入U盘"));
        return;
    }

    QString strGzPath = GetUDiskUpdateDir() + gk_strAutoName;
    QString strShPath = GetUDiskUpdateDir() + gk_strMyShName;

    if(!QFile::exists(strGzPath) || !QFile::exists(strShPath))
    {
        ShowInformation(this, m_strTipsText, tr("U盘中文件不存在"));
        return;
    }

    QString strKillPdf = "killall 7CPDF";
    System(strKillPdf.toStdString().c_str());

    QString strCmd = QString("cp %1 ./").arg(strGzPath);
    System(strCmd.toStdString().c_str());
    System("sync");

    strCmd = QString("cp %1 ./").arg(strShPath);
    System(strCmd.toStdString().c_str());
    System("sync");

    QString strAppPath = GetUDiskUpdateDir() + gk_strAppName;
    if(QFile::exists(strAppPath))
    {
        QString strRemoveCmd = "rm " + gk_strAppName;
        System(strRemoveCmd);
        System("sync");

        QString strCopyCmd = QString("cp %1 ./").arg(strAppPath);
        System(strCopyCmd);
        System("sync");
    }

    ShowSuccess(this, tr("提示"), tr("配置升级成功，正在重启"));
    Delay_MSec(500);
    System("./mylocalconfig.sh");
}

void CSoftUpdate::_SlotPowerQuery()
{
    emit CPublicConfig::GetInstance()->SignalReadPowerVersion();
}

void CSoftUpdate::SlotPowerVersion(QString strVersion)
{
    if(m_pPowerVersionLabel)
        m_pPowerVersionLabel->setText(strVersion);
}

void CSoftUpdate::_SlotSystemQuery()
{
    QString strSystemVer;
    ReadFile("/etc/.deploy_info", strSystemVer);
    m_pSystemVerLabel->setText(strSystemVer);
}

void CSoftUpdate::_CheckPLCVersion(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if(m_sPLCVerList.at(iMachineID).strMedianShowVersion.isEmpty())
        QTimer::singleShot(100, this, [=](){_QueryMedian(iMachineID);});

    if(m_sPLCVerList.at(iMachineID).strFLShowVersion.isEmpty())
        QTimer::singleShot(100, this, [=](){_QueryFL(iMachineID);});

    if(m_sPLCVerList.at(iMachineID).strPCRShowVersion.isEmpty())
        QTimer::singleShot(100, this, [=](){_QueryPCR(iMachineID);});
}

void CSoftUpdate::_QueryMedian(int iMachineID)
{
    QString strCmd = GetJsonCmdString(Method_sys_info);
    qDebug()<<QString("%1#查询中位机版本:%2").arg(iMachineID + 1).arg(strCmd);
    SendJsonCmd(iMachineID, Method_sys_info, strCmd);
}

void CSoftUpdate::_UpdateMedian()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定升级中位机吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    QString strFilePath;
#ifdef __aarch64__
    strFilePath = GetUDiskUpdateDir() + gk_strSlaveName;
    if(!IsUDiskAndUpdateFileExist(strFilePath, this))
        return;
#else
    strFilePath = QFileDialog::getOpenFileName(this, tr("升级中位机"), m_strCurrentDir, "*.xbin");
    if(strFilePath.isEmpty() || !QFile::exists(strFilePath))
        return;
#endif
    qDebug()<<QString("%1#选择中位机升级文件:%2").arg(m_iUiMachineID + 1).arg(strFilePath);
    m_iUpdateType = Update_Median;
    _StartUpdateFirm(m_iUiMachineID, strFilePath);
}

void CSoftUpdate::_RebootMedian()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定重启中位机吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    QString strCmd = GetJsonCmdString(Method_reboot);
    qDebug()<<QString("%1#重启中位机:%2").arg(m_iUiMachineID + 1).arg(strCmd);
    SendJsonCmd(m_iUiMachineID, Method_reboot, strCmd);
}

void CSoftUpdate::_QueryPCR(int iMachineID)
{
    QString strCmd = GetJsonCmdString(Method_pcr_version);
    qDebug()<<QString("%1#查询PCR版本:%2").arg(iMachineID + 1).arg(strCmd);
    SendJsonCmd(iMachineID, Method_pcr_version, strCmd);
}

void CSoftUpdate::_UpdatePCR()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定升级PCR吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    QString strFilePath;
#ifdef __aarch64__
    strFilePath = GetUDiskUpdateDir() + gk_strPCRName;
    if(!IsUDiskAndUpdateFileExist(strFilePath, this))
        return;
#else
    strFilePath = QFileDialog::getOpenFileName(this, tr("升级PCR"), m_strCurrentDir, "*.xbin");
    if(strFilePath.isEmpty() || !QFile::exists(strFilePath))
        return;
#endif
    qDebug()<<QString("%1#选择PCR升级文件:%2").arg(m_iUiMachineID + 1).arg(strFilePath);
    m_iUpdateType = Update_PCR;
    _StartUpdateFirm(m_iUiMachineID, strFilePath);
}

void CSoftUpdate::_RebootPCR()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定重启PCR吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    QString strCmd = GetJsonCmdString(Method_pcr_reboot);
    qDebug()<<QString("%1#重启PCR:%2").arg(m_iUiMachineID + 1).arg(strCmd);
    SendJsonCmd(m_iUiMachineID, Method_pcr_reboot, strCmd);
}

void CSoftUpdate::_QueryFL(int iMachineID)
{
    QString strCmd = GetJsonCmdString(Method_fl_version);
    qDebug()<<QString("%1#查询荧光版本:%2").arg(iMachineID + 1).arg(strCmd);
    SendJsonCmd(iMachineID, Method_fl_version, strCmd);
}

void CSoftUpdate::_UpdateFL()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定升级荧光吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    QString strFilePath;
#ifdef __aarch64__
    strFilePath = GetUDiskUpdateDir() + gk_strFLName;
    if(!IsUDiskAndUpdateFileExist(strFilePath, this))
        return;
#else
    strFilePath = QFileDialog::getOpenFileName(this, tr("升级荧光"), m_strCurrentDir, "*.xbin");
    if(strFilePath.isEmpty() || !QFile::exists(strFilePath))
        return;
#endif
    qDebug()<<QString("%1#选择荧光升级文件:%2").arg(m_iUiMachineID + 1).arg(strFilePath);
    m_iUpdateType = Update_FL;
    _StartUpdateFirm(m_iUiMachineID, strFilePath);
}

void CSoftUpdate::_RebootFL()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定重启荧光吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    QString strCmd = GetJsonCmdString(Method_fl_reboot);
    qDebug()<<QString("%1#重启荧光:%2").arg(m_iUiMachineID + 1).arg(strCmd);
    SendJsonCmd(m_iUiMachineID, Method_fl_reboot, strCmd);
}

void CSoftUpdate::_StartUpdateFirm(int iMachineID, const QString &strUpdateFileName)
{
    QFile file(strUpdateFileName);
    if(!file.open(QIODevice::ReadOnly))
    {
        qDebug()<<"升级失败,文件无法打开:"<<file.errorString();
        ShowInformation(this, m_strTipsText, tr("升级失败,文件无法打开"));
        return;
    }
    m_byteUpdateData = file.readAll();
    file.close();

    m_iOnePieceLen = 1024;

    QString strSync = m_byteUpdateData.mid(0, 4).toHex();
    QString strCrc = m_byteUpdateData.mid(4, 4).toHex();
    int iLen = m_byteUpdateData.mid(8, 4).toHex().toInt(nullptr, 16);
    QString strLoadAddr = m_byteUpdateData.mid(12, 4).toHex();
    QString strEpAddr = m_byteUpdateData.mid(16, 4).toHex();

    int iDataLen = m_byteUpdateData.length();
    int iPacks = iDataLen / m_iOnePieceLen;
    int iLeft = iDataLen % m_iOnePieceLen;
    int iTotalPacks = iPacks;
    if(0 != iLeft)
        iTotalPacks = iPacks + 1;
    qDebug()<<"升级文件大小:"<<iDataLen<<",包数:"<<iPacks<<",剩余:"<<iLeft;

    QString strInfo;
    int iMethodID = -1;
    if(Update_Median == m_iUpdateType)
    {
        strInfo = tr("升级中位机");
        iMethodID = Method_upgrade_req;
    }
    else if(Update_PCR == m_iUpdateType)
    {
        strInfo = tr("升级PCR");
        iMethodID = Method_pcr_upgrade_req;
    }
    else if(Update_FL == m_iUpdateType)
    {
        strInfo = tr("升级荧光");
        iMethodID = Method_fl_upgrade_req;
    }

    QVariantList qVarList = {iLen};
    SendJsonCmd(iMachineID, iMethodID, GetJsonCmdString(iMethodID, qVarList));

    m_pUpdateTimer->start(UPDATE_TIME_OUT);
    m_pProgressBar->SetInfo(strInfo);
    m_pProgressBar->SetRange(0, iTotalPacks);
    m_pProgressBar->show();
}

void CSoftUpdate::_UpdateFirmReply(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    QVariantMap qVarMap = qVarData.toMap();
    int iPackID = qVarMap.value("packID").toInt();
    qDebug()<<QString("%1# %2 升级PackID:%3 %4").arg(iMachineID + 1).arg(iMethodID).arg(iPackID).arg(iResult);
    if(iPackID < 0)
        return;

    int iTotalSize = m_byteUpdateData.size();
    int iOnePieceLen = m_iOnePieceLen;
    int iOffset = iPackID * iOnePieceLen;
    int iLeftSize = iTotalSize - iOffset;
    if(iLeftSize < 0)
    {
        qDebug()<<"packID有误,超过文件大小:"<<iLeftSize;
        return;
    }

    if(iLeftSize < iOnePieceLen)
    {
        qDebug()<<"升级最后一包大小:"<<iTotalSize<<iOffset<<iLeftSize;
        iOnePieceLen = iLeftSize;
    }

    QByteArray byteID;
    byteID[0] = uchar(iPackID / 256);
    byteID[1] = uchar(iPackID % 256);

    QByteArray byteData = m_byteUpdateData.mid(iOffset, iOnePieceLen);

    QByteArray payload;
    payload.append(byteID);
    payload.append(byteData);

    if(Update_Median == m_iUpdateType)
        COperationUnit::GetInstance()->MedianUpdate(iMachineID, payload);
    else if(Update_PCR == m_iUpdateType)
        COperationUnit::GetInstance()->PCRUpdate(iMachineID, payload);
    else if(Update_FL == m_iUpdateType)
        COperationUnit::GetInstance()->FLUpdate(iMachineID, payload);

    m_pUpdateTimer->start(UPDATE_TIME_OUT);
    m_pProgressBar->SetValue(iPackID);
}

void CSoftUpdate::_SetTableItem(int iRow, int iCol, QString strText)
{
    QTableWidgetItem *pItem = m_pTableWidget->item(iRow, iCol);
    if(nullptr != pItem)
    {
        pItem->setText(strText);
        return;
    }
    pItem = new QTableWidgetItem;
    pItem->setText(strText);
    pItem->setTextAlignment(Qt::AlignCenter);
    m_pTableWidget->setItem(iRow, iCol, pItem);
}

void CSoftUpdate::_SetTableWidget(int iRow, int iCol, int iWidth, int iHeight, QWidget *pUIWidget)
{
    QWidget *pCellWidget = new QWidget;
    pCellWidget->setFixedSize(iWidth, iHeight);
    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(pUIWidget, 0, Qt::AlignVCenter);
    pLayout->addStretch(1);
    pCellWidget->setLayout(pLayout);
    m_pTableWidget->setCellWidget(iRow, iCol, pCellWidget);
}

void CSoftUpdate::_InitWidget()
{
    this->setFixedSize(1494, 798);

    m_pMachineComboBox = new QComboBox(this);
    m_pMachineComboBox->setFixedSize(100, 50);
    m_pMachineComboBox->setView(new QListView);
    m_pMachineComboBox->addItems(gk_strMachineNameList);
    connect(m_pMachineComboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotMachineChanged(int)));

    int iBtnWidth = 100;
    //if(eLanguage_Spanish == gk_iLanguage)
    //    iBtnWidth = 130;
    //else if(eLanguage_Italian == gk_iLanguage)
    //    iBtnWidth = 180;

    QStringList strList = {tr("查询"), tr("升级"), tr("重启")};
    for(int i=0; i<strList.size(); i++)
    {
        QPushButton *pMedianBtn = new QPushButton(strList.at(i));
        pMedianBtn->setFixedSize(iBtnWidth, 50);
        pMedianBtn->setProperty("index", i);
        m_pMedianBtnList.push_back(pMedianBtn);
        connect(pMedianBtn, &QPushButton::clicked, this, &CSoftUpdate::_SlotMedianBtnList);

        QPushButton *pPCRBtn = new QPushButton(strList.at(i));
        pPCRBtn->setFixedSize(iBtnWidth, 50);
        pPCRBtn->setProperty("index", i);
        m_pPCRBtnList.push_back(pPCRBtn);
        connect(pPCRBtn, &QPushButton::clicked, this, &CSoftUpdate::_SlotPCRBtnList);

        QPushButton *pFLBtn = new QPushButton(strList.at(i));
        pFLBtn->setFixedSize(iBtnWidth, 50);
        pFLBtn->setProperty("index", i);
        m_pFLBtnList.push_back(pFLBtn);
        connect(pFLBtn, &QPushButton::clicked, this, &CSoftUpdate::_SlotFLBtnList);
    }

    QStringList strTitleList;
    strTitleList << tr("机器") << tr("板卡") << tr("软件版本") << tr("硬件版本") << tr("BOOT版本") << "" << "" << "";

    m_pTableWidget = new QTableWidget(this);
    m_pTableWidget->setColumnCount(strTitleList.size());
    m_pTableWidget->setHorizontalHeaderLabels(strTitleList);
    m_pTableWidget->setRowCount(3);
    m_pTableWidget->setFixedSize(1410, 230);

    QHeaderView* pVerticalHeader = m_pTableWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(60);
    QHeaderView* pHorizontalHeader = m_pTableWidget->horizontalHeader();
    pHorizontalHeader->resizeSection(0, 150);
    pHorizontalHeader->resizeSection(1, 150);
    pHorizontalHeader->resizeSection(2, 250);
    pHorizontalHeader->resizeSection(3, 250);
    pHorizontalHeader->resizeSection(4, 250);
    pHorizontalHeader->resizeSection(5, 120);
    pHorizontalHeader->resizeSection(6, 120);
    pHorizontalHeader->resizeSection(7, 120);

    m_pTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pTableWidget->setSelectionMode(QAbstractItemView::NoSelection);
    m_pTableWidget->setShowGrid(true);
    m_pTableWidget->setFocusPolicy(Qt::NoFocus);
    m_pTableWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTableWidget->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);

    _SetTableItem(0, 1, tr("中位机"));
    _SetTableItem(1, 1, tr("PCR"));
    _SetTableItem(2, 1, tr("荧光"));

    for(int iCol=0; iCol<m_pMedianBtnList.size(); iCol++)
    {
        _SetTableWidget(0, 5 + iCol, 120, 60, m_pMedianBtnList.at(iCol));
        _SetTableWidget(1, 5 + iCol, 120, 60, m_pPCRBtnList.at(iCol));
        _SetTableWidget(2, 5 + iCol, 120, 60, m_pFLBtnList.at(iCol));
    }

    m_pTableWidget->setSpan(0, 0, 3, 1);
    _SetTableWidget(0, 0, 150, 60*3, m_pMachineComboBox);

    /***********************上位机**************************/
    int iNameWidth = 150, iVerWidth = 320;

    m_pAppNameLabel = new QLabel(tr("软件："));
    m_pAppNameLabel->setFixedSize(iNameWidth, 50);

    QString strAppVersion = GetAppVersion();
    m_pAppVersionLabel = new QLabel(strAppVersion);
    m_pAppVersionLabel->setFixedSize(iVerWidth, 50);
    qDebug()<<"软件版本:"<<strAppVersion;

    m_pAppUpdateBtn = new QPushButton(tr("升级"));
    m_pAppUpdateBtn->setFixedSize(iBtnWidth, 50);
    connect(m_pAppUpdateBtn, &QPushButton::clicked, this, &CSoftUpdate::_SlotUpdateApp);

    m_pCalcNameLabel = new QLabel(tr("算法："));
    m_pCalcNameLabel->setFixedSize(iNameWidth, 50);

    CCalCTLib ctLib;
    QString strCalcVersion = ctLib.getVersion();
    m_pCalcVersionLabel = new QLabel(strCalcVersion);
    m_pCalcVersionLabel->setFixedSize(iVerWidth, 50);
    qDebug()<<"算法版本:"<<strCalcVersion;

    m_pCalcUpdateBtn = new QPushButton(tr("升级"));
    m_pCalcUpdateBtn->setFixedSize(iBtnWidth, 50);
    connect(m_pCalcUpdateBtn, &QPushButton::clicked, this, &CSoftUpdate::_SlotUpdateCalcLib);

    m_pAutoNameLabel = new QLabel(tr("配置："));
    m_pAutoNameLabel->setFixedSize(iNameWidth, 50);

    QString strAutoVersion;
    QString strPath = QApplication::applicationDirPath() +  "/Resources/version";
    ReadFile(strPath, strAutoVersion);
    strAutoVersion.remove("\r").remove("\n");
    qDebug()<<"配置："<<strPath<<strAutoVersion;

    m_pAutoVersionLabel = new QLabel(strAutoVersion);
    m_pAutoVersionLabel->setFixedSize(iVerWidth, 50);

    m_pAutoUpdateBtn = new QPushButton(tr("升级"));
    m_pAutoUpdateBtn->setFixedSize(iBtnWidth, 50);
    connect(m_pAutoUpdateBtn, &QPushButton::clicked, this, &CSoftUpdate::_SlotUpdateAuto);

    m_pPowerNameLabel = new QLabel(tr("电源："));
    m_pPowerNameLabel->setFixedSize(iNameWidth, 50);

    m_pPowerVersionLabel = new QLabel;
    m_pPowerVersionLabel->setFixedSize(iVerWidth, 50);

    m_pPowerQueryBtn = new QPushButton(tr("查询"));
    m_pPowerQueryBtn->setFixedSize(iBtnWidth, 50);
    connect(m_pPowerQueryBtn, &QPushButton::clicked, this, &CSoftUpdate::_SlotPowerQuery);

    m_pSystemNameLabel = new QLabel(tr("系统："));
    m_pSystemNameLabel->setFixedSize(iNameWidth, 50);

    QString strSystemVer;
    ReadFile("/etc/.deploy_info", strSystemVer);
    m_pSystemVerLabel = new QLabel(strSystemVer, this);
    m_pSystemVerLabel->setFixedSize(iVerWidth, 130);
    m_pSystemVerLabel->setAlignment(Qt::AlignLeft | Qt::AlignTop);
    qDebug()<<"系统版本:"<<strSystemVer;

    m_pSystemQueryBtn = new QPushButton(tr("查询"));
    m_pSystemQueryBtn->setFixedSize(iBtnWidth, 50);
    connect(m_pSystemQueryBtn, &QPushButton::clicked, this, &CSoftUpdate::_SlotSystemQuery);

    m_pBackgroundLabel = new QLabel(this);
    m_pBackgroundLabel->setFixedSize(640, 440);
    m_pBackgroundLabel->setObjectName("VersionLabel");
}

void CSoftUpdate::_InitLayout()
{
    QHBoxLayout *pAppLayout = new QHBoxLayout;
    pAppLayout->setMargin(0);
    pAppLayout->setSpacing(20);
    pAppLayout->addWidget(m_pAppNameLabel);
    pAppLayout->addWidget(m_pAppVersionLabel);
    pAppLayout->addWidget(m_pAppUpdateBtn);
    pAppLayout->addStretch(1);

    QHBoxLayout *pCalcLayout = new QHBoxLayout;
    pCalcLayout->setMargin(0);
    pCalcLayout->setSpacing(20);
    pCalcLayout->addWidget(m_pCalcNameLabel);
    pCalcLayout->addWidget(m_pCalcVersionLabel);
    pCalcLayout->addWidget(m_pCalcUpdateBtn);
    pCalcLayout->addStretch(1);

    QHBoxLayout *pAutoLayout = new QHBoxLayout;
    pAutoLayout->setMargin(0);
    pAutoLayout->setSpacing(20);
    pAutoLayout->addWidget(m_pAutoNameLabel);
    pAutoLayout->addWidget(m_pAutoVersionLabel);
    pAutoLayout->addWidget(m_pAutoUpdateBtn);
    pAutoLayout->addStretch(1);

    QHBoxLayout *pPowerLayout = new QHBoxLayout;
    pPowerLayout->setMargin(0);
    pPowerLayout->setSpacing(20);
    pPowerLayout->addWidget(m_pPowerNameLabel);
    pPowerLayout->addWidget(m_pPowerVersionLabel);
    pPowerLayout->addWidget(m_pPowerQueryBtn);
    pPowerLayout->addStretch(1);

    QHBoxLayout *pSystemLayout = new QHBoxLayout;
    pSystemLayout->setMargin(0);
    pSystemLayout->setSpacing(20);
    pSystemLayout->addWidget(m_pSystemNameLabel, 0, Qt::AlignTop);
    pSystemLayout->addWidget(m_pSystemVerLabel, 0, Qt::AlignTop);
    pSystemLayout->addWidget(m_pSystemQueryBtn, 0, Qt::AlignTop);
    pSystemLayout->addStretch(1);

    QVBoxLayout *pUpperLayout = new QVBoxLayout;
    pUpperLayout->setContentsMargins(10, 10, 0, 0);
    pUpperLayout->setSpacing(20);
    pUpperLayout->addLayout(pAppLayout);
    pUpperLayout->addLayout(pCalcLayout);
    pUpperLayout->addLayout(pAutoLayout);
    pUpperLayout->addLayout(pPowerLayout);
    pUpperLayout->addLayout(pSystemLayout);
    pUpperLayout->addStretch(1);
    m_pBackgroundLabel->setLayout(pUpperLayout);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setContentsMargins(10, 0, 10, 0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pTableWidget, 0, Qt::AlignLeft);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pBackgroundLabel, 0, Qt::AlignLeft);
    pLayout->addSpacing(20);
    this->setLayout(pLayout);
}
