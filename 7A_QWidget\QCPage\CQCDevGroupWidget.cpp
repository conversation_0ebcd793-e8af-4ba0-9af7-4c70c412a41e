#include "CQCDevGroupWidget.h"
#include <QDebug>
#include <QBoxLayout>

CQCDevGroupWidget::CQCDevGroupWidget(int iWidth, const QString &strDevName, const QList<int> &iMachineIDList, QWidget *parent)
    : QWidget(parent)
    , m_iWidth(iWidth)
    , m_strDevName(strDevName)
    , m_iMachineIDList(iMachineIDList)
{
    this->setFixedSize(iWidth, 250);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroupBox());
    pLayout->addStretch(1);
    this->setLayout(pLayout);
}

void CQCDevGroupWidget::SetGroupUnSelect()
{
    for(int i=0; i<m_pDevItemBtnList.size(); i++)
        m_pDevItemBtnList.at(i)->setEnabled(true);
}

int CQCDevGroupWidget::GetSelectedMachineID()
{
    for(int i=0; i<m_pDevItemBtnList.size(); i++)
    {
        if(!m_pDevItemBtnList.at(i)->isEnabled())
            return m_pDevItemBtnList.at(i)->GetMachineID();
    }
    return -1;
}

void CQCDevGroupWidget::_SlotDevItemSelected()
{
    CQCDevItemBtn *pDevItemWidget = dynamic_cast<CQCDevItemBtn *>(sender());
    if(nullptr == pDevItemWidget)
        return;

    for(int i=0; i<m_pDevItemBtnList.size(); i++)
    {
        if(pDevItemWidget->GetMachineID() == m_pDevItemBtnList.at(i)->GetMachineID())
            m_pDevItemBtnList.at(i)->setEnabled(false);
        else
            m_pDevItemBtnList.at(i)->setEnabled(true);
    }

    emit SignalGroupSelected();
}

QGroupBox *CQCDevGroupWidget::_CreateGroupBox()
{
    m_pTitleWidget = new CHLabelTitleWidget(m_strDevName);

    QHBoxLayout *pTitleLayout = new QHBoxLayout;
    pTitleLayout->setMargin(0);
    pTitleLayout->setSpacing(0);
    pTitleLayout->addSpacing(10);
    pTitleLayout->addWidget(m_pTitleWidget);
    pTitleLayout->addStretch(1);

    QHBoxLayout *pItemLayout = new QHBoxLayout;
    pItemLayout->setMargin(0);
    pItemLayout->setSpacing(10);
    pItemLayout->addStretch(1);
    for(int i=0; i<m_iMachineIDList.size(); i++)
    {
        CQCDevItemBtn *pDevItemWidget = new CQCDevItemBtn(m_iMachineIDList.at(i));
        connect(pDevItemWidget, &CQCDevItemBtn::clicked, this, &CQCDevGroupWidget::_SlotDevItemSelected);
        m_pDevItemBtnList.push_back(pDevItemWidget);

        pItemLayout->addWidget(pDevItemWidget);
    }
    pItemLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addSpacing(16);
    pLayout->addLayout(pTitleLayout);
    pLayout->addStretch(1);
    pLayout->addLayout(pItemLayout);
    pLayout->addStretch(1);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(m_iWidth, 250);
    pGroupBox->setObjectName("QCDevGroupBox");
    pGroupBox->setLayout(pLayout);

    return pGroupBox;
}
