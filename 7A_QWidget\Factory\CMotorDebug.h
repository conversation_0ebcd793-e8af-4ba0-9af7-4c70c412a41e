#ifndef CMOTORDEBUG_H
#define CMOTORDEBUG_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-11-30
  * Description: 电机调试
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QStackedWidget>
#include "CHBtnTitleWidget.h"

class CNormalMotor;
class CMotorRegister;
class CMotorChopper;
class CMotorGXIO;
class CMotorConfig;
class CMotorCopmensate;
class CMotorMethod;
class CMotorCalibrate;
class CMotorPosition;

class CMotorDebug : public QWidget
{
    Q_OBJECT
public:
    explicit CMotorDebug(QWidget *parent = nullptr);
    ~CMotorDebug();

private slots:
    void _SlotTitleChanged(int index);

private:
    void _InitWidget();

private:
    CHBtnTitleWidget *m_pCHBtnTitle;
    QStackedWidget *m_pStackedWidget;
    CNormalMotor *m_pCNormalMotor;
    CMotorMethod *m_pCMotorMethod;
    CMotorRegister *m_pCMotorRegister;
    CMotorChopper *m_pCChopper;
    CMotorGXIO *m_pCMotorGXIO;
    CMotorConfig *m_pCMotorConfig;
    CMotorCopmensate *m_pCMotorCopmensate;
    CMotorCalibrate *m_pCMotorCalibrate;
    CMotorPosition *m_pCMotorPosition;
};

#endif // CMOTORDEBUG_H
