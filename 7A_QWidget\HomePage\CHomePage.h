#ifndef CHOMEPAGE_H
#define CHOMEPAGE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-17
  * Description: 主页
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QStackedWidget>

#include "CHomeDeviceWidget.h"
#include "CHomeEnterInfoWidget.h"

class CHomePage : public QWidget
{
    Q_OBJECT
public:
    explicit CHomePage(QWidget *parent = nullptr);

    int GetCurrentPage();
    int GetCurrentMachineID();
    void Set2DevicePage();
    void CreateQCTest(int iMachineID,QString strQCModel);

private slots:
    void _SlotCancelTest();
    void _SlotCreateNormalTest(int iMachineID);
    void _SlotStartTest(int iMachineID);

private:
    CHomeDeviceWidget *m_pCHomeDeviceWidget;
    CHomeEnterInfoWidget *m_pCHomeEnterInfoWidget;
    QStackedWidget *m_pStackWidget;
};

#endif // CHOMEPAGE_H
