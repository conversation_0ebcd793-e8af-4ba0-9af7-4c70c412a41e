#ifndef CSYSTEMPAGE_H
#define CSYSTEMPAGE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-25
  * Description: 系统页
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QStackedWidget>

#include "CSystemMainWidget.h"
#include "General/CGeneralWidget.h"
#include "Network/CNetworkWidget.h"
#include "User/CUserWidget.h"
#include "Log/CLogWidget.h"
#include "Update/CUpdateWidget.h"
#include "DeviceInfo/CDeviceInfoWidget.h"
#include "SelfTest/CSelfTestWidget.h"
#include "Calibration/CCalibrationWidget.h"
#include "Printer/CPrinterWidget.h"
#include "Maintian/CMaintainWidget.h"
#include "MyFactory/CFactoryWidget.h"

enum EnumSystem_Page
{
    eSystemPage_Main        = 0, //标题主页
    eSystemPage_General     = 1, //常规设置
    eSystemPage_Network     = 2, //网络设置
    eSystemPage_User        = 3, //用户管理
    eSystemPage_Log         = 4, //日志管理
    eSystemPage_Update      = 5, //软件升级
    eSystemPage_DeviceInfo  = 6, //仪器信息
    eSystemPage_SelfTest    = 7, //仪器自检
    eSystemPage_Calibration = 8, //仪器校准
    eSystemPage_Printer     = 9, //打印设置
    eSystemPage_Maintain    = 10,//运维模式
    eSystemPage_Factory     = 11,//工厂模式
};

class CSystemPage : public QWidget
{
    Q_OBJECT
public:
    explicit CSystemPage(QWidget *parent = nullptr);

    void GotoRunLogPage();
    void GotoMainPage();

private slots:
    void _SlotGotoPage(int iPage);
    void _SlotReturn2Main();

private:
    CSystemMainWidget *m_pCSystemTitleWidget;
    CGeneralWidget *m_pCGeneralWidget;
    CNetworkWidget *m_pCNetworkWidget;
    CUserWidget *m_pCUserWidget;
    CLogWidget *m_pCLogWidget;
    CUpdateWidget *m_pCUpdateWidget;
    CDeviceInfoWidget *m_pCDeviceInfoWidget;
    CSelfTestWidget *m_pCSelfTestWidget;
    CCalibrationWidget *m_pCCalibrationWidget;
    CPrinterWidget *m_CPrinterWidget;
    CMaintainWidget *m_pCMaintainWidget;
    CFactoryWidget *m_pCFactoryWidget;

    QStackedWidget *m_pStackWidget;
};

#endif // CSYSTEMPAGE_H
