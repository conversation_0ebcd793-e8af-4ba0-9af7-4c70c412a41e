#ifndef CHOMEDEVGROUPWIDGET_H
#define CHOMEDEVGROUPWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-17
  * Description: 设备组 (分子检测设备)
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QGroupBox>
#include "CHLabelTitleWidget.h"
#include "CHomeDevItemWidget.h"

class CHomeDevGroupWidget : public QWidget
{
    Q_OBJECT
public:
    CHomeDevGroupWidget(const QString &strDevName, const SDevParamsStruct &sDevParams,
                        const QList<int> &iMachineIDList, QWidget *parent = nullptr);

    void SetCardSampleInfo(int iMachineID, const SCardInfoStruct &sCardInfo, const SSampleInfoStruct &sSampleInfo);
    void SetFLData(int iMachineID, const QList<QMap<double, double>> &dFLMap);

signals:
    void SignalCreateTest(int iMachineID);

private:
    QGroupBox *_CreateGroupBox();

private:
    CHLabelTitleWidget *m_pTitleWidget;
    QList<CHomeDevItemWidget *> m_pDevItemWidgetList;

    QString m_strDevName;
    SDevParamsStruct m_sDevParams;
    QList<int> m_iMachineIDList;
};

#endif // CHOMEDEVGROUPWIDGET_H
