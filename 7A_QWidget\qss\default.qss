QTextBrowser
{
    color: #000;
    font-family: "Source Han Sans CN";
    font-size: 20px;
    border: 0px solid #CAD2DC;
    border-radius: 10px;
}

QStackedWidget
{
    border: 0px solid #CAD2DC;
    border-radius: 10px;
    font-size: 18px;
    font-family:"Source Han Sans CN";
    background-color: #CCDEEE;
}

QLabel
{
    border-radius: 10px;
    color: #000;
    font-family: "Source Han Sans CN";
    font-size: 20px;
    /*background-color: red;*/
}
QLabel#label_mark
{
    image: url(:/image/ico/main/label_mark.png);
}
/*一级标题*/
QLabel#SysTitleLabel1
{
    color: #6B788F;
    font-size: 24px;
    font-weight: 400;
    font-family: "Source Han Sans CN";
}

/*一级标题*/
QLabel#SysTitleLabel2
{
    color: #353E4E;
    font-size: 24px;
    font-weight: 500;
    font-family: "Source Han Sans CN";
}
QLabel#m_pDlgTitleLabel
{
    font-family:"Source Han Sans CN";
    font-size: 24px;
    color:#fff;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
    background-color: #248ceb;
    padding-left: 10px;
}
QLabel#daylabel
{
    color: #000; /* text color */
    font-family: "Source Han Sans CN";
    font-size: 20px;
    border: 1px solid #cad2dc;
    border-radius:10px;
    background-color: #FFFFFF;
}

QPushButton
{
    color: #fff;
    background-color: rgb(30, 141, 255);
    font-size: 22px;
    font-family:"Source Han Sans CN";
    border: 0px solid #0068b7;
    border-radius: 10px;
}
QPushButton:disabled
{
    background-color: #888888;
}
QPushButton:pressed
{
    background: rgb(4, 115, 239);
}
QPushButton:focus{outline: none;}
QPushButton#Button5Radius
{
   border-radius: 5px;
}

QPushButton#HBtnTitleLeft
{
    background-color: #F5FAFF;
    border-right: 1px solid #BBCBE2;
    border-bottom-left-radius: 10px;
    border-top-left-radius: 10px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
    font-size:20px;
    font-family:"Source Han Sans CN";
    color:#505050;
}
QPushButton:disabled#HBtnTitleLeft
{
    background-color: #0C72CD;
    border-bottom-left-radius: 10px;
    border-top-left-radius: 10px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
    border: 0px solid #529BFF;
    font-size:22px;
    font-family:"Source Han Sans CN";
    color:#FFF;
}
QPushButton:hover#HBtnTitleLeft
{
    font-size:22px;
}

QPushButton#VBtnTitle
{
    background-color: #D9E5F2;
    border: 1px solid #C7D7E6;
    border-radius: 10px;
    font-size: 20px;
    font-family:"Source Han Sans CN";
    color:#505050;
}

QPushButton:disabled#VBtnTitle
{
    background-color: #0C72CD;;
    border-radius: 10px;
    border: 0px solid #529BFF;
    font-size:22px;
    font-family:"Source Han Sans CN";
    color:#FFF;
    border-image: url(:/image/ico/test/menubtn-pressed.png);
}

QPushButton#HBtnTitleMiddle
{
    background-color: #F5FAFF;
    border-right: 1px solid #BBCBE2;
    border-bottom-left-radius: 0px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
    font-size:20px;
    font-family:"Source Han Sans CN";
    color:#505050;
}
QPushButton:disabled#HBtnTitleMiddle
{
    background-color: #0C72CD;;
    border-bottom-left-radius: 0px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
    border: 0px solid #529BFF;
    font-size:22px;
    font-family:"Source Han Sans CN";
    color:#FFF;
}
QPushButton:hover#HBtnTitleMiddle
{
    font-size:22px;
}

QPushButton#HBtnTitleRight
{
    background-color: #F5FAFF;
    border: 0px solid #529BFF;
    border-bottom-left-radius: 0px;
    border-top-left-radius: 0px;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    font-size:20px;
    font-family:"Source Han Sans CN";
    color:#505050;
}
QPushButton:disabled#HBtnTitleRight
{
    background-color: #0C72CD;;
    border-bottom-left-radius: 0px;
    border-top-left-radius: 0px;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    border: 0px solid #529BFF;
    font-size:22px;
    font-family:"Source Han Sans CN";
    color:#FFF;
}
QPushButton:hover#HBtnTitleRight
{
    font-size:22px;
}

QPushButton#AddBtn
{
    color : rgb(0, 0, 0);
    background-color: #ffffff;
    border: 0px solid #c9c9c9;
    border-radius: 10px;
    border-radius: 3px;
    outline:0px;
    image: url(:/image/ico/setting/normal-setting/time-up-normal.png);
}

QPushButton:pressed#AddBtn
{
    background-color: #C7D7E6;
    border: 1px solid #c9c9c9;
    border-radius: 10px;
    border-radius: 3px;
    outline:0px;
    image: url(:/image/ico/setting/normal-setting/time-up-pressed.png);
}

QPushButton#SubBtn
{
    background-color: #ffffff;
    border: 0px solid #c9c9c9;
    border-radius: 3px;
    outline:0px;
    image: url(:/image/ico/setting/normal-setting/time-down-normal.png);
}

QPushButton:pressed#SubBtn
{
    background-color: #C7D7E6;
    border: 0px solid #c9c9c9;
    border-radius: 3px;
    outline:0px;
    image: url(:/image/ico/setting/normal-setting/time-down-pressed.png);
}

QPushButton#PrePageBtn
{
    background-color: transparent;
    image: url(:/image/ico/main/up-able.png);
}
QPushButton:disabled#PrePageBtn
{
    background-color: transparent;
    image: url(:/image/ico/main/up-disable.png);
}
QPushButton#NextPageBtn
{
    background-color: transparent;
    image: url(:/image/ico/main/down-able.png);
}
QPushButton:disabled#NextPageBtn
{
    background-color: transparent;
    image: url(:/image/ico/main/down-disable.png);
}

QLineEdit
{
    color:#232323;
    font-size: 20px;    
    font-family:"Source Han Sans CN";
    padding-left: 0px;
    border: 1px solid #CAD2DC;
    border-radius: 10px;
}

QLineEdit#LineEdit5Radius
{
    border-radius: 5px;
}

QCheckBox
{
    color: #000;
    font-size: 20px;
    font-family: "Source Han Sans CN";

   /* font-weight: bold */
}
QCheckBox::indicator
{
    width: 40px;
    height: 40px;
   /* subcontrol-position:right  right;*/
}
QCheckBox::indicator::unchecked
{
    image: url(:/image/ico/setting/normal-setting/uncheck.png);
}

QCheckBox::indicator::checked
{
    image: url(:/image/ico/setting/normal-setting/check.png);
}

QCheckBox:focus{outline: none;}


QComboBox
{    
    color: #000;
    font-family: "Source Han Sans CN";
    font-size: 20px;
    padding: 0 0px;
    border: 1px solid #CAD2DC;
    border-radius:10px;
    background-color: #FFF;
}

QComboBox#ComboBox5Radius
{
    border-radius: 5px;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 1px;

    border-left-width: 0px;
    border-left-color: darkgray;
    border-left-style: solid; /* just a single line */
    border-top-right-radius: 3px; /* same radius as the QComboBox */
    border-bottom-right-radius: 3px;
}

QComboBox::down-arrow {
    width: 40px;
    height: 40px;
    image: url(:/image/ico/login/commod1.png);
    padding: 0px 30px 0px 0px;
}
QComboBox QAbstractItemView
{
    border: 0px solid darkgray;/*下拉列表的边线的粗细、实虚*/
    border-radius:0px;
    selection-background-color: #248CEB;/*下拉列表选中的行的背景色*/
    color: #000; /* text color */
    font-family: "Source Han Sans CN";
    font-size: 20px;
    min-width: 380px;
}
QComboBox QAbstractItemView::item
{
    height: 50px;/*下拉列表的行高，也可以看做行距*/
    color: #000; /* text color */
    font-family: "Source Han Sans CN";
    font-size: 20px;
}

QRadioButton
{
    color: #232323; /* text color:/image/ico/setting/set_language_checked.png */
    font-family: "Source Han Sans CN";
    font-size: 24px;
}
QRadioButton:focus{outline: none;}
QRadioButton::indicator {
    width: 40px;
    height: 40px;
}
/*未选中*/
QRadioButton::indicator::unchecked {
    image: url(:/image/ico/system/radio-off.png);
}
/*选中*/
QRadioButton::indicator::checked {
    image: url(:/image/ico/system/radio-on.png);
}


QGroupBox
{
    border: 0px solid #0068b7;
    border-radius: 10px;
    font-size: 18px;
    font-family:"Source Han Sans CN";
    background-color: #E5EDF8;
}
QGroupBox#CMessageGroupBox
{
    border: 0px solid #0068b7;
    border-radius: 10px;
    font-size: 18px;
    font-family:"Source Han Sans CN";
    background-color: #fff;
}

QGroupBox#LogGroupBox
{
    border: 0px solid #0068b7;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    font-size: 18px;
    font-family:"Source Han Sans CN";
    background-color: #E5EDF8;
}

QProgressBar
{
   border:1px solid grey;
   border-radius:3px;
   font-size: 18px;
   text-align:center;
}
QProgressBar::chunk
{
   background-color:#21DE44;
}

/*一键升级进度条样式*/
QProgressBar#UpdateProgressBar
{
   border:0px solid grey;
   border-radius:5px;
   background-color: #e6e6e6;
}
QProgressBar::chunk#UpdateProgressBar
{
   border-radius:5px;
   background-color:#FF9900;
}

/*远程下载进度条样式*/
QProgressBar#DownloadProgressBar
{
   border:0px solid grey;
   border-radius:5px;
   background-color: #e6e6e6;
}
QProgressBar::chunk#DownloadProgressBar
{
   border-radius:5px;
   background-color:#00ff00;
}

/*繁忙进度条样式*/
QProgressBar#BusyProgressBar
{
   border: 0px solid grey;
   border-radius: 5px;
   background-color: #e6e6e6;
}
QProgressBar::chunk#BusyProgressBar
{
   border-radius: 5px;
   background-color: #05B8CC;
   width: 10px;
   margin: 0.5px;
}


QTableWidget
{
    border: 0px solid #D6DFE9;
    border-radius: 0px;
    selection-background-color: #60c8ff;
    color: #333; /* text color */
    font-family: "Source Han Sans CN";
    font-size: 20px;
}
QTableView
{
border: 0px solid #D6DFE9;
border-radius: 0px;
selection-background-color: #60c8ff;
color: #333; /* text color */
font-family: "Source Han Sans CN";
font-size: 20px;
}
QHeaderView::section
{
    border: 1px solid #D6DFE9;
    border-radius: 0px;
    background-color: #455E7C;
    height: 50px;
    color: white;
    font-size: 21px;
    font-family:"Source Han Sans CN";
 /*   font-weight:bold;*/
}
/*垂直滚动条整体*/
QScrollBar:vertical
{
    width:30px;
    background:rgb(240, 240, 240);
    margin:0px,0px,0px,0px;
    padding-top:30px;   /*上预留位置*/
    padding-bottom:30px;    /*下预留位置*/
}

/*滚动条中滑块的样式*/
QScrollBar::handle:vertical
{
    width:30px;
    background:rgb(166, 166, 166);
    border-radius:0px;
    min-height:30px;
}

/*鼠标触及滑块样式*/
QScrollBar::handle:vertical:hover
{
    width:30px;
    background:rgb(86, 86, 86);
    border-radius:0px;
}
/*水平滚动条整体*/
QScrollBar:horizontal
{
    height:30px;
    background:rgb(240, 240, 240);
    margin:0px,0px,0px,0px;
    padding-left:0px;   /*上预留位置*/
    padding-right:0px;    /*下预留位置*/
}

/*滚动条中滑块的样式*/
QScrollBar::handle:horizontal
{
    height:30px;
    background:rgb(166, 166, 166);
    border-radius:0px;
    width:30px;
}

/*鼠标触及滑块样式*/
QScrollBar::handle:horizontal:hover
{
    height:30px;
    background:rgba(166, 166, 166);
    border-radius:0px;
}
