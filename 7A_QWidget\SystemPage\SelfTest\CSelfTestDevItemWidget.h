#ifndef CSELFTESTDEVITEMWIDGET_H
#define CSELFTESTDEVITEMWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-05-23
  * Description: 自检单个设备
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QTimer>
#include <QLabel>
#include <QGroupBox>
#include <QPushButton>

#include "PublicParams.h"

class CSelfTestDevItemWidget : public QWidget
{
    Q_OBJECT
public:
    CSelfTestDevItemWidget(int iMachineID, const SDevParamsStruct &sDevParams, QWidget *parent = nullptr);

    int GetMachineID() const;
    void ReceiveCaliResult(int iResult);
    void UpdateDateLabel(QString strLastDate, QString strNextDate);

public slots:
    void SlotSetDevStatus(int iMachineID, DeviceStatus eStatus);

private slots:
    void _SlotSelftestBtn();
    void _SlotRunTimer();
    void _SlotTestReceiveSelftest();

private:
    void _SelftestResult(int iResult);

private:
    QGroupBox *_CreateGroupBox();

private:
    QLabel *m_pTitleLabel;
    QLabel *m_pIndexLabel, *m_pTextLabel;
    QLabel *m_pLastNameLabel, *m_pLastDateLabel;
    QLabel *m_pNextNameLabel, *m_pNextDateLabel;
    QPushButton *m_pCaliBtn;

private:
    int m_iMachineID;
    SDevParamsStruct m_sDevParams;
    DeviceStatus m_eStatus;
    const QString m_strTipsText;
    QTimer *m_pRunTimer;
    QString m_strXlsxPath;

};

#endif // CSELFTESTDEVITEMWIDGET_H
