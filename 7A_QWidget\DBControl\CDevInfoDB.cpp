#include "CDevInfoDB.h"
#include <QApplication>

CDevInfoDB &CDevInfoDB::GetInstance()
{
    static CDevInfoDB db;
    return db;
}

CDevInfoDB::~CDevInfoDB()
{

}

QMap<int, QStringList> CDevInfoDB::GetAllCalibrateDate()
{
    QString strCmd = "select MachineID,LastDate,NextDate from calibrate_date";
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);

    QMap<int, QStringList> iDateMap;
    for(int i=0; i<strList.size(); i++)
    {
        QStringList strOne = strList.at(i);
        if(strOne.size() >= 3)
        {
            int iID = strOne.at(0).toInt();
            QStringList strDateList = {strOne.at(1), strOne.at(2)};
            iDateMap.insert(iID, strDateList);
        }
    }
    return iDateMap;
}

bool CDevInfoDB::GetCalibrateDate(int iMachineID, QString &strLastDate, QString &strNextDate)
{
    QString strCmd = QString("select LastDate,NextDate from calibrate_date where MachineID='%1'").arg(iMachineID);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    QStringList strDateList = _GetRowValueList(strList);
    if(strDateList.size() >= 2)
    {
        strLastDate = strDateList.at(0);
        strNextDate = strDateList.at(1);
    }
    return true;
}

bool CDevInfoDB::UpdateCalibrateDate(int iMachineID, QString strLastDate, QString strNextDate)
{    
    QString strCmd = QString("select * from calibrate_date where MachineID = '%1'").arg(iMachineID);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    if(strList.isEmpty())
    {
        strCmd = QString("insert into calibrate_date(MachineID, LastDate, NextDate) values('%1', '%2', '%3')")
                .arg(iMachineID).arg(strLastDate).arg(strNextDate);
    }
    else
    {
        strCmd = QString("update calibrate_date set LastDate='%1',NextDate='%2' where MachineID='%3'")
                .arg(strLastDate).arg(strNextDate).arg(iMachineID);
    }

    return _ExecuteDB(strCmd);
}

bool CDevInfoDB::AddCalibrateResult(int iMachineID, QString strResult, QString strDetails, QString strDate, QString strReport)
{
    QString strCmd = QString("insert into calibrate_result(MachineID,Result,Details,Date,Report,Remarks) "
                             "values('%1','%2','%3','%4','%5','')")
            .arg(iMachineID).arg(strResult).arg(strDetails).arg(strDate).arg(strReport);
    return _ExecuteDB(strCmd);
}

int CDevInfoDB::GetCalibrateResultCounts()
{
    QString strCmd = "select count(*) from calibrate_result";
    QList<QStringList> strList;
    if(_QueryDB(strCmd, strList))
        return _GetFirstValue(strList).toInt();

    return 0;
}

bool CDevInfoDB::GetCalibrateOnePageData(int iPage, int iOffset, QList<QStringList> &strReadList)
{
    QString strCmd = QString("select * from calibrate_result order by id desc limit %1,%2").arg(iPage * iOffset).arg(iOffset);
    return _QueryDB(strCmd, strReadList);
}

QMap<int, QStringList> CDevInfoDB::GetAllSelftestDate()
{
    QString strCmd = "select MachineID,LastDate,NextDate from selftest_date";
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);

    QMap<int, QStringList> iDateMap;
    for(int i=0; i<strList.size(); i++)
    {
        QStringList strOne = strList.at(i);
        if(strOne.size() >= 3)
        {
            int iID = strOne.at(0).toInt();
            QStringList strDateList = {strOne.at(1), strOne.at(2)};
            iDateMap.insert(iID, strDateList);
        }
    }
    return iDateMap;
}

bool CDevInfoDB::GetSelftestDate(int iMachineID, QString &strLastDate, QString &strNextDate)
{
    QString strCmd = QString("select LastDate,NextDate from selftest_date where MachineID='%1'").arg(iMachineID);
    QList<QStringList> strList;
    if(!_QueryDB(strCmd, strList))
        return false;

    QStringList strDateList = _GetRowValueList(strList);
    if(strDateList.size() >= 2)
    {
        strLastDate = strDateList.at(0);
        strNextDate = strDateList.at(1);
    }
    return true;
}

bool CDevInfoDB::UpdateSelftestDate(int iMachineID, QString strLastDate, QString strNextDate)
{
    QString strCmd = QString("select * from selftest_date where MachineID = '%1'").arg(iMachineID);
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    if(strList.isEmpty())
    {
        strCmd = QString("insert into selftest_date(MachineID, LastDate, NextDate) values('%1', '%2', '%3')")
                .arg(iMachineID).arg(strLastDate).arg(strNextDate);
    }
    else
    {
        strCmd = QString("update selftest_date set LastDate='%1',NextDate='%2' where MachineID='%3'")
                .arg(strLastDate).arg(strNextDate).arg(iMachineID);
    }

    return _ExecuteDB(strCmd);
}

bool CDevInfoDB::AddSelftestResult(int iMachineID, QString strResult, QString strDetails, QString strDate, QString strReport)
{
    QString strCmd = QString("insert into selftest_result(MachineID,Result,Details,Date,Report,Remarks) "
                             "values('%1','%2','%3','%4','%5','')")
            .arg(iMachineID).arg(strResult).arg(strDetails).arg(strDate).arg(strReport);
    return _ExecuteDB(strCmd);
}

int CDevInfoDB::GetSelftestResultCounts()
{
    QString strCmd = "select count(*) from selftest_result";
    QList<QStringList> strList;
    if(_QueryDB(strCmd, strList))
        return _GetFirstValue(strList).toInt();

    return 0;
}

bool CDevInfoDB::GetSelftestOnePageData(int iPage, int iOffset, QList<QStringList> &strReadList)
{
    QString strCmd = QString("select * from selftest_result order by id desc limit %1,%2").arg(iPage * iOffset).arg(iOffset);
    return _QueryDB(strCmd, strReadList);
}

CDevInfoDB::CDevInfoDB() : CSqliteDBBase(QApplication::applicationDirPath() + "/db/devInfo.db", "devInfo")
{
    _ExecuteDB("drop table dev_info");

    QString strCmd = "SELECT COUNT(*) FROM pragma_table_info('calibrate_result') WHERE name='Details'";
    QList<QStringList> strList;
    _QueryDB(strCmd, strList);
    int iCount = _GetFirstValue(strList).toInt();
    if(iCount <= 0)
        _ExecuteDB("drop table calibrate_result");

    _InitCalibrateDateTable();
    _InitCalibrateResultTable();

    _InitSelftestDateTable();
    _InitSelftestResultTable();
}

void CDevInfoDB::_InitCalibrateDateTable()
{
    QString strCmd = "create table if not exists calibrate_date ("
                     "id integer not null primary key autoincrement,"
                     "MachineID varchar,"
                     "LastDate varchar,"
                     "NextDate varchar)";
    _ExecuteDB(strCmd);
}

void CDevInfoDB::_InitCalibrateResultTable()
{
    QString strCmd = "create table if not exists calibrate_result ("
                     "id integer not null primary key autoincrement,"
                     "MachineID varchar,"
                     "Result varchar,"
                     "Details varchar,"
                     "Date varchar,"
                     "Report varchar,"
                     "Remarks varchar)";
    _ExecuteDB(strCmd);
}

void CDevInfoDB::_InitSelftestDateTable()
{
    QString strCmd = "create table if not exists selftest_date ("
                     "id integer not null primary key autoincrement,"
                     "MachineID varchar,"
                     "LastDate varchar,"
                     "NextDate varchar)";
    _ExecuteDB(strCmd);
}

void CDevInfoDB::_InitSelftestResultTable()
{
    QString strCmd = "create table if not exists selftest_result ("
                     "id integer not null primary key autoincrement,"
                     "MachineID varchar,"
                     "Result varchar,"
                     "Details varchar,"
                     "Date varchar,"
                     "Report varchar,"
                     "Remarks varchar)";
    _ExecuteDB(strCmd);
}
