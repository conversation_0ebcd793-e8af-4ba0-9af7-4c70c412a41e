#include "CSetChartXYRange.h"
#include <QHBoxLayout>

CSetChartXYRange::CSetChartXYRange(QWidget *parent) : QWidget(parent)
{
    _InitWidget();
}

CSetChartXYRange::CSetChartXYRange(const QStringList &strRangeList, QWidget *parent)
    : QWidget(parent)
    , m_strRangeList(strRangeList)
{
    _InitWidget();
}

CSetChartXYRange::~CSetChartXYRange()
{

}

void CSetChartXYRange::SetRange(const QStringList &strRangeList)
{
    if(strRangeList.size() < 4)
        return;

    m_strRangeList = strRangeList;
    m_pX0->SetLineEditText(m_strRangeList.at(0));
    m_pX1->SetLineEditText(m_strRangeList.at(1));
    m_pY0->SetLineEditText(m_strRangeList.at(2));
    m_pY1->SetLineEditText(m_strRangeList.at(3));
}

void CSetChartXYRange::SetLineEditTextAlignment(Qt::Alignment qAli)
{
    m_pX0->SetLineEidtAlignment(qAli);
    m_pX1->SetLineEidtAlignment(qAli);
    m_pY0->SetLineEidtAlignment(qAli);
    m_pY1->SetLineEidtAlignment(qAli);
}

void CSetChartXYRange::ChangedObjName(const QString &strLineEditName, const QString &strBtnName)
{
    QList<CLineEdit*> list = findChildren<CLineEdit*>();
    foreach(CLineEdit *pObj, list)
        pObj->setObjectName(strLineEditName);

    m_pSetButton->setObjectName(strBtnName);
}

void CSetChartXYRange::SetObjFixedHeight(int iHeight)
{
    QList<CLineEdit*> list = findChildren<CLineEdit*>();
    foreach(CLineEdit *pObj, list)
        pObj->setFixedHeight(iHeight);

    QList<QLabel*> labelList = findChildren<QLabel*>();
    foreach(QLabel *pObj, labelList)
        pObj->setFixedHeight(iHeight);

    m_pSetButton->setFixedHeight(iHeight);

    this->setFixedHeight(iHeight);
}

void CSetChartXYRange::_SlotBtnClicked()
{
    m_strRangeList.clear();
    m_strRangeList<<m_pX0->GetLineEditText()<<m_pX1->GetLineEditText()
                 <<m_pY0->GetLineEditText()<<m_pY1->GetLineEditText();
    emit SignalSetRange(m_strRangeList);
}

void CSetChartXYRange::_InitWidget()
{
    if(m_strRangeList.size() < 4)
    {
        m_strRangeList.clear();
        m_strRangeList<<"0"<<"100"<<"0"<<"100";
    }

    int iHeight = 50;
    int iLabelWidth = 30;
    int iLineEditWidth = 80;
    this->setFixedHeight(iHeight);
    m_pX0 = new CLabelLineEdit("x0:", m_strRangeList.at(0));
    m_pX0->SetLineEditInputMethod(Qt::ImhDigitsOnly);
    m_pX0->SetLabelFixedSize(iLabelWidth, iHeight);
    m_pX0->SetLineEditFixedSize(iLineEditWidth, iHeight);

    m_pX1 = new CLabelLineEdit("x1:", m_strRangeList.at(1));
    m_pX1->SetLineEditInputMethod(Qt::ImhDigitsOnly);
    m_pX1->SetLabelFixedSize(iLabelWidth, iHeight);
    m_pX1->SetLineEditFixedSize(iLineEditWidth, iHeight);

    m_pY0 = new CLabelLineEdit("y0:", m_strRangeList.at(2));
    m_pY0->SetLineEditInputMethod(Qt::ImhDigitsOnly);
    m_pY0->SetLabelFixedSize(iLabelWidth, iHeight);
    m_pY0->SetLineEditFixedSize(iLineEditWidth, iHeight);

    m_pY1 = new CLabelLineEdit("y1:", m_strRangeList.at(3));
    m_pY1->SetLineEditInputMethod(Qt::ImhDigitsOnly);
    m_pY1->SetLabelFixedSize(iLabelWidth, iHeight);
    m_pY1->SetLineEditFixedSize(iLineEditWidth, iHeight);

    m_pSetButton = new QPushButton(tr("设置坐标"));
    m_pSetButton->setFixedSize(120, iHeight);
    connect(m_pSetButton, &QPushButton::clicked, this, &CSetChartXYRange::_SlotBtnClicked);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(10);
    pLayout->addWidget(m_pX0);
    pLayout->addWidget(m_pX1);
    pLayout->addWidget(m_pY0);
    pLayout->addWidget(m_pY1);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pSetButton);
    this->setLayout(pLayout);
}

