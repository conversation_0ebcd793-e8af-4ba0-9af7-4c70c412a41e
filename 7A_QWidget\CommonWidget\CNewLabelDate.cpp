#include "CNewLabelDate.h"
#include <QBoxLayout>

CNewLabelDate::CNewLabelDate(const QString &strNameText, const QString &strDateText, int iSpacing,
                             QBoxLayout::Direction eDirection, QWidget *parent) : QWidget(parent)
{
    m_pNameLabel = new QLabel(strNameText);

    m_pDateLabel = new CPressLabel(strDateText);
    m_pDateLabel->setFixedSize(303, 56);
    m_pDateLabel->setObjectName("DateLabel");
    connect(m_pDateLabel, &CPressLabel::SignalPressEvent, this, &CNewLabelDate::SignalPressEvent);

    QBoxLayout *pLayout = nullptr;
    if(QBoxLayout::Direction::LeftToRight == eDirection)
        pLayout = new QHBoxLayout;
    else
        pLayout = new QVBoxLayout;

    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    //pLayout->addStretch(1);
    pLayout->addWidget(m_pNameLabel);
    pLayout->addSpacing(iSpacing);
    pLayout->addWidget(m_pDateLabel);
    //pLayout->addStretch(1);
    this->setLayout(pLayout);
}

CNewLabelDate::~CNewLabelDate()
{

}

QString CNewLabelDate::GetDateString() const
{
    return m_pDateLabel->text();
}

void CNewLabelDate::SetDateString(const QString &strDateText)
{
    m_pDateLabel->setText(strDateText);
}

void CNewLabelDate::ResetNameLabelSize(int iWidth, int iHeight)
{
    m_pNameLabel->setFixedSize(iWidth, iHeight);
}

void CNewLabelDate::ResetDateLabelSize(int iWidth, int iHeight)
{
    m_pDateLabel->setFixedSize(iWidth, iHeight);
}

CVNewLabelDate::CVNewLabelDate(const QString &strNameText, const QString &strDateText, int iSpacing, QWidget *parent)
    : CNewLabelDate(strNameText, strDateText, iSpacing, QBoxLayout::Direction::TopToBottom, parent)
{

}

CVNewLabelDate::~CVNewLabelDate()
{

}

CHNewLabelDate::CHNewLabelDate(const QString &strNameText, const QString &strDateText, int iSpacing, QWidget *parent)
    : CNewLabelDate(strNameText, strDateText, iSpacing, QBoxLayout::Direction::LeftToRight, parent)
{

}

CHNewLabelDate::~CHNewLabelDate()
{

}
