#ifndef CTECDB_H
#define CTECDB_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-11-15
  * Description: tec数据库
  * -------------------------------------------------------------------------
  * History: 添加时序表格
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QObject>
#include "CSqliteDBBase.h"

class CTecDB : public QObject , public CSqliteDBBase
{
    Q_OBJECT
public:
    static CTecDB &instance();

    QStringList ReadAllTecName();
    QString ReadTecDataByName(const QString &tecName);
    bool InsertOneTec(const QString &tecName, const QString &tecData);
    bool DeleteOneTecByName(const QString &tecName);
    QList<QStringList> RealAllTecList();

private:
    CTecDB();
    virtual ~CTecDB();

private:
    void _InitTecTable();

    Q_DISABLE_COPY(CTecDB)
};

#endif // CTECDB_H
