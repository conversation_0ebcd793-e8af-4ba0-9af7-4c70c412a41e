#ifndef CMOTORCOPMENSATE_H
#define CMOTORCOPMENSATE_H

#include <QWidget>
#include <QPushButton>
#include <QTableWidget>

#include "CCmdBase.h"
#include "CLineEdit.h"
#include "CLabelComboBox.h"

class CMotorCopmensate : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CMotorCopmensate(QWidget *parent = nullptr);
    ~CMotorCopmensate();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

public slots:
    void SlotSoftTypeChanged(int iSoftType);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual void hideEvent(QHideEvent *pEvent) override;

private slots:
    void _SlotMachineComboBoxChanged(int iMachineID);
    void _SlotMotorComboBoxChanged(int index);
    void _SlotClearBtn();
    void _SlotReadBtn();
    void _SlotSetBtn();
    void _SlotExeBtn();
    void _SlotMoveBtnList();
    void _SlotMoveDirBtn();
    void _SlotBottomBtnList();

private:
    QString _GetTableData();
    void _LoadData2TableWidget(const QList<QStringList> &strList);
    void _SaveTableData2DB();
    void _ResortCompensateIndex();

private:
    void _InitWidget();
    void _InitLayout();

private:
    CLabelComboBox *m_pMachineComboBox;
    CLabelComboBox *m_pMotorComboBox;
    QPushButton *m_pClearBtn;
    CLineEdit *m_pValueLineEdit;
    QPushButton *m_pReadBtn;
    QPushButton *m_pSetBtn;

    QTableWidget *m_pTableWidget;
    QLabel *m_pLabel1, *m_pLabel2;
    QComboBox *m_pMethodComboBox;
    QPushButton *m_pExeBtn;

    QList<QPushButton *> m_pMoveBtnList;

    CLineEdit *m_pStepLineEdit;
    QComboBox *m_pDirComboBox;
    QPushButton *m_pMoveDirBtn;

    QList<QPushButton *> m_pBottomBtnList;

    bool m_bShow;
    int m_iUiMachineID;
    QString m_strMachineLog;
    int m_iMotorIndex;
    QString m_strMotorName;
    QList<QStringList> m_strComboTextList;
    QStringList m_strLabelTextList1, m_strLabelTextList2;
};

#endif // CMOTORCOPMENSATE_H
