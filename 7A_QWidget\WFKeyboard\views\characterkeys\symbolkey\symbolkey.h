#ifndef SYMBOLKEY_H
#define SYMBOLKEY_H

#include <vector>
#include "../characterbasekey.h"

class QToolButton;

namespace Ui {
class Symbolkey;
}

class Symbolkey : public CharacterBaseKey
{
    Q_OBJECT
    
public:
    enum SymbolTypeEnum
    {
        CHS, ENG, MATH
    };

    explicit Symbolkey(KeyBoard *parent, QStackedWidget *stackedWidget);

    ~Symbolkey();

    void Active(SymbolTypeEnum eSymbolTypeEnum);

    void Translate(const QString& chs,const QString& eng,const QString& math,const QString& space);

private slots:
    void SlotPrevPageBtnClicked();

    void SlotNextPageBtnClicked();

    void SlotCHSBtnClicked();

    void SlotENGBtnClicked();

    void SLotMathBtnClicked();

protected:
    void changeEvent(QEvent *event);

private:
    class PrivateData;
    PrivateData *const md;
};

#endif // SYMBOLKEY_H
