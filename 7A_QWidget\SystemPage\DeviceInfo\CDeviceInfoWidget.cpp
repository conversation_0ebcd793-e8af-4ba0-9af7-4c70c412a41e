#include "CDeviceInfoWidget.h"
#include <QBoxLayout>
#include "PublicFunction.h"

CDeviceInfoWidget::CDeviceInfoWidget(QWidget *parent) : QWidget(parent)
{
    _InitWidget();
    _InitLayout();

    LoadQSS(this, ":/qss/qss/system/deviceinfo.qss");
}

void CDeviceInfoWidget::_SlotTitleChanged(int iTitle)
{
    m_pStackedWidget->setCurrentIndex(iTitle);
}

void CDeviceInfoWidget::_InitWidget()
{
    m_pCSysFirstTtileWidget = new CSysFirstTitleWidget(tr("系统设置"), tr("仪器信息"));
    connect(m_pCSysFirstTtileWidget, &CSysFirstTitleWidget::SignalTitlePress, this, &CDeviceInfoWidget::SignalReturn);

    m_pBackgroundLabel = new QLabel;
    m_pBackgroundLabel->setFixedSize(1684, 904);
    m_pBackgroundLabel->setObjectName("BackgroundLabel");

    m_pCSysSecondTitleWidget = new CSysSecondTitleWidget({tr("控制台"), tr("检测模块")});
    connect(m_pCSysSecondTitleWidget, &CSysSecondTitleWidget::SignalSecondTitle, this, &CDeviceInfoWidget::_SlotTitleChanged);

    m_pCIPCInfoWidget = new CIPCInfoWidget;
    connect(m_pCIPCInfoWidget, &CIPCInfoWidget::SignalReturn, this, &CDeviceInfoWidget::SignalReturn);

    m_pCPLCInfoWidget = new CPLCInfoWidget;
    connect(m_pCPLCInfoWidget, &CPLCInfoWidget::SignalReturn, this, &CDeviceInfoWidget::SignalReturn);

    m_pStackedWidget = new QStackedWidget;
    m_pStackedWidget->setFixedSize(1636, 800);
    m_pStackedWidget->addWidget(m_pCIPCInfoWidget);
    m_pStackedWidget->addWidget(m_pCPLCInfoWidget);
}

void CDeviceInfoWidget::_InitLayout()
{
    QVBoxLayout *pBackLayout = new QVBoxLayout;
    pBackLayout->setContentsMargins(24, 15, 24, 24);
    pBackLayout->addWidget(m_pCSysSecondTitleWidget, 0, Qt::AlignLeft);
    pBackLayout->addStretch(1);
    pBackLayout->addWidget(m_pStackedWidget, 0, Qt::AlignHCenter);
    m_pBackgroundLabel->setLayout(pBackLayout);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pCSysFirstTtileWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pBackgroundLabel);
    this->setLayout(pLayout);
}
