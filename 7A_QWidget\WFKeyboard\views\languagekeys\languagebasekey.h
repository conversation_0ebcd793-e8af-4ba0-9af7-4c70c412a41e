#ifndef BASEKEY_H
#define BASEKEY_H

#include <QObject>
#include <QMap>
#include <QWidget>
#include <QSignalMapper>

class KeyBoardToolButton;

class LanguageBaseKey : public QObject
{
    Q_OBJECT

public:
    explicit LanguageBaseKey(const QString&);

    void SetLowerCase();

    void SetCapital();

    QString GetName();

    QWidget* GetWidget();

    void SetSignalMapper(QSignalMapper*,QSignalMapper*,QSignalMapper*);

    void GetTranslate(QString &chs, QString &eng, QString &math, QString &space);

    KeyBoardToolButton* GetLeftCapsLockBtn();

    KeyBoardToolButton* GetRightCapsLockBtn();

private:
    virtual void InitButtons() = 0;

protected:
    void SetTranslate(const QString& chs,const QString& eng,const QString& math,const QString& space);

    void SetButtonsMap(const QMap<int, QList<KeyBoardToolButton*> > &);

private slots:
    void SlotCapsLockBtnClicked();

private:
    class PrivateData;
    PrivateData *const md;
};

#endif // BASEKEY_H
