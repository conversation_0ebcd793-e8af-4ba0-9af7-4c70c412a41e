﻿#pragma once
#include "interface/IObjectList.h"
#include <vector>
#include "macros.h"
class HL7ObjectList :
	public IObjectList
{
public:
	typedef std::vector<IObjectBase*> VecObjectList;
	HL7ObjectList();
	virtual ~HL7ObjectList();

	DECLARE_OBJECTBASE

	BEGIN_IMPL_QUERYIF(HL7ObjectList)
		IMPL_QUERYIF(IF_OBJECTBASE, IObjectBase)
		IMPL_QUERYIF(IF_OBJECTLIST, IObjectList)
		END_IMPL_QUERYIF()

	virtual UINT GetCount();

	virtual IObjectBase* Get(UINT index);

	virtual NRET Add(IObjectBase* obj);

	virtual NRET Delete(UINT index);

	virtual void Clear();

private:
	VecObjectList	m_ObjectList;
};

