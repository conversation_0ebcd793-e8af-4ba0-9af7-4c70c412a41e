#ifndef CDATAEXPORT_H
#define CDATAEXPORT_H

#include <QDir>
#include <QLabel>
#include <QPushButton>
#include "CLabelDate.h"
#include "CDateTime.h"

class CDataExport : public QWidget
{
    Q_OBJECT
public:
    explicit CDataExport(QWidget *parent = nullptr);
    ~CDataExport();

private:
    enum EnumExportType {eLog = 0, ePdf, eXlsx};
    enum EnumDateType {eStartDateLog = 0, eEndDateLog, eStartDatePdf, eEndDatePdf, eStartDateXlsx, eEndDateXlsx};

signals:
    void SignalNoFiles(int iFileType);
    void SignalExportProgress(int iFileType, QString strProgress);
    void SignalExportError(int iFileType);
    void SignalExportEnd(int iFileType);

private slots:
    void _SlotShowDateWidget();
    void _SlotConfirmDate(const QString &strDate);

    void _SlotExportLogBtn();
    void _SlotExportPdfBtn();
    void _SlotExportXlsxBtn();

    void _SlotNoFiles(int iFileType);
    void _SlotExportProgress(int iFileType, QString strProgress);
    void _SlotExportError(int iFileType);
    void _SlotExportEnd(int iFileType);

private:
    void _Thread2ExortLog(const QString &strBeginDate, const QString &strEndDate);
    void _Thread2ExortPdf(const QString &strBeginDate, const QString &strEndDate);
    void _Thread2ExortXlsx(const QString &strBeginDate, const QString &strEndDate);
    QStringList _GetMacthDateFilesFromDir(int iType, const QDir &qDir, const QString &strBeginDate, const QString &strEndDate);

    void _GuardDirSize(QString strPath, qint64 iMaxSize);
    void _Thread2GuardDirSize();

private:
    void _InitWidget();
    void _InitLayout();

private:
    bool m_bGuard;
    bool m_bLogExporting, m_bPdfExporting, m_bXlsxExporting;

    CLabelDate *m_pLogBeginDate, *m_pLogEndDate;
    CLabelDate *m_pPdfBeginDate, *m_pPdfEndDate;
    CLabelDate *m_pXlsxBeginDate, *m_pXlsxEndDate;

    QPushButton *m_pLogExportBtn, *m_pPdfExportBtn, *m_pXlsxExportBtn;
    QLabel *m_pLogLabel, *m_pPdfLabel, *m_pXlsxLabel;

    CDateTime *m_pCDateWidget;
    EnumDateType m_eDateType;

    QString m_strTipsText;
};

#endif // CDATAEXPORT_H
