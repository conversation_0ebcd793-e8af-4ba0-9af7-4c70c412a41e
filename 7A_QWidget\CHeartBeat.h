#ifndef CHEARTBEAT_H
#define CHEARTBEAT_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-16
  * Description: 心跳
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QTimer>

#include "PublicParams.h"
#include "CmdBus/CCmdBase.h"

struct SDeviceHeartbeatStruct
{
    SDeviceHeartbeatStruct()
    {
        bDeviceConnect = false;
        pCheckTimer = nullptr;
        iHeartBeatSpan = 20000;
        iDeviceStatus = eDeviceDisconnect;
        bCardboxExist = false;
        iDisconnectTimes = 0;
        bSetRTCTime = false;
    }
    bool bDeviceConnect;          //设备是否连接
    QTimer *pCheckTimer;          //检查连接定时器
    int iHeartBeatSpan;           //心跳间隔(中位机上传)
    int iDeviceStatus;            //设备状态(中位机上传)
    bool bCardboxExist;           //是否存在测试卡盒(中位机上传)
    int iDisconnectTimes;         //已断开连接的次数
    bool bSetRTCTime;             //同步下位机时间
};

class CHeartBeat : public QObject , public CCmdBase
{
    Q_OBJECT
public:
    static CHeartBeat *GetInstance();
    ~CHeartBeat();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData);

    void ResetStatus(int iMachineID);
    SDeviceHeartbeatStruct *GetDeviceHearbeatStruct(int iMachineID) const;

signals:
    void SignalUpdateItemStatus(int iMachineID, DeviceStatus eStatus, bool bCardExist);
    void SignalResetItemStatus(int iMachineID);

private slots:
    void _SlotCheckConnectTimeout();

private:
    CHeartBeat();
    void _ParseHeartBeat(int iMachineID, const QVariant &qVarData);

private:
    static CHeartBeat *m_spInstance;

    QList<SDeviceHeartbeatStruct *> m_sDeviceHeartbeatList;
};

#endif // CHEARTBEAT_H
