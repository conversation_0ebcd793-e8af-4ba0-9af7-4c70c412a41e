#ifndef CGENERALWIDGET_H
#define CGENERALWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-02
  * Description: 常规设置
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QSlider>
#include <QCheckBox>
#include <QPushButton>
#include <QRadioButton>
#include <QAtomicInt>

#include "CLineEdit.h"
#include "CLabelLineEdit.h"
#include "CHLabelTitleWidget.h"
#include "CLabelComboBox.h"
#include "CDateTimeWidget.h"
#include "SystemPage/CSysFirstTitleWidget.h"

class CGeneralWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CGeneralWidget(QWidget *parent = nullptr);
    ~CGeneralWidget();

protected:
    void showEvent(QShowEvent *pEvent) override;

signals:
    void SignalReturn();

private slots:
    void _SlotSelectTimeBtn();
    void _SlotConfirmDateTime(const QString &strDateTime);
    void _SlotBrightnessChanged(int iValue);
    void _SlotSoundChanged(int iValue);
    void _SlotSoundTimerout();
    void _SlotSaveBtn();

private:
    void _ReadCfg();
    void _ChangeSystemLogo(int iLanguage);

private:
    void _InitWidget();
    void _InitLayout();

private:
    CSysFirstTitleWidget *m_pCSysTtileLabelWidget;
    QLabel *m_pBackgroundLabel;

    CHLabelTitleWidget *m_pLanguageTitleWidget;
    QComboBox *m_pLanguageComboBox;
    QRadioButton *m_pChineseBtn, *m_pEnglishBtn, *m_pSpanishBtn, *m_pGermanBtn, *m_pItalianBtn;

    CHLabelTitleWidget *m_pBrightnessTitleWidget;
    QSlider *m_pBrightnessSlider;

    CHLabelTitleWidget *m_pSoundTitleWidget;
    QSlider *m_pSoundSlider;

    CHLabelTitleWidget *m_pLockTitleWidget;
    CHLabelLineEdit *m_pLockTimeWidget;
    QCheckBox *m_pNeverLockCheckBox;

    CHLabelTitleWidget *m_pTimeSetTitleWidget;
    CLineEdit *m_pTimeLineEdit;
    QPushButton *m_pTimeBtn;
    CDateTimeWidget *m_pCDateTimeWidget;

    CHLabelTitleWidget *m_pDevTitleWidget;
    QComboBox *m_pDevComboBox;

    QPushButton *m_pReturnBtn, *m_pSaveBtn;

    QTimer *m_pSoundTimer;
};

#endif // CGENERALWIDGET_H
