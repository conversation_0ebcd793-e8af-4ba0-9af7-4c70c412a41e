<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Russiakey</class>
 <widget class="QWidget" name="Russiakey">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>784</width>
    <height>160</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QStackedWidget" name="stackedWidget_Normal">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="page_Qwerty_Board">
      <layout class="QVBoxLayout" name="verticalLayout">
       <property name="spacing">
        <number>3</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <layout class="QHBoxLayout" name="layout_Normal_Row02">
         <property name="spacing">
          <number>2</number>
         </property>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_10">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">й</string>
           </property>
           <property name="autoRepeat">
            <bool>false</bool>
           </property>
           <property name="autoRepeatInterval">
            <number>100</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_23">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> ц</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_20">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> у</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_11">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">к</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_6">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> е</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_14">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> н</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_4">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> г</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_25">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> ш</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_26">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">щ</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_8">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="text">
            <string notr="true">з</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_22">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">х</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="layout_Normal_Row03">
         <property name="spacing">
          <number>2</number>
         </property>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_21">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">ф</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_27">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">ы</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_3">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">в</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_1">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">а</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_16">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">п</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_17">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">р</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_15">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">о</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_12">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> л</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_5">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="text">
            <string notr="true">д</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_7">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="text">
            <string notr="true">ж</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_29">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">э</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="layout_Normal_Row04" stretch="0,10,10,10,10,10,10,10,10,10,10,10,0">
         <property name="spacing">
          <number>2</number>
         </property>
         <item>
          <spacer name="horizontalSpacer_7">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>6</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_capslock">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>100</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>20</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"/>
           </property>
           <property name="icon">
            <iconset>
             <normaloff>:/newImg/newImg/KeyBorder/icon_small_normal.png</normaloff>
             <normalon>:/images/KeyBorder/icon_big.png</normalon>
             <activeoff>:/images/KeyBorder/icon_small_normal.png</activeoff>
             <activeon>:/newImg/newImg/KeyBorder/icon_big.png</activeon>:/newImg/newImg/KeyBorder/icon_small_normal.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>40</width>
             <height>40</height>
            </size>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_31">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> я</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_24">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">ч</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_18">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">с</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_13">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">м</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_9">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> и</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_19">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"> т</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_28">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true">ь</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_2">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="text">
            <string notr="true">б</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_30">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="text">
            <string notr="true">ю</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="KeyBordToolButton" name="toolButton_capslock_right">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>100</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>文泉驿微米黑</family>
             <pointsize>20</pointsize>
            </font>
           </property>
           <property name="focusPolicy">
            <enum>Qt::NoFocus</enum>
           </property>
           <property name="text">
            <string notr="true"/>
           </property>
           <property name="icon">
            <iconset>
             <normaloff>:/newImg/newImg/KeyBorder/icon_small_normal.png</normaloff>
             <normalon>:/images/KeyBorder/icon_big.png</normalon>
             <activeoff>:/images/KeyBorder/icon_small_normal.png</activeoff>
             <activeon>:/newImg/newImg/KeyBorder/icon_big.png</activeon>:/newImg/newImg/KeyBorder/icon_small_normal.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>40</width>
             <height>40</height>
            </size>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_8">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>6</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>KeyBordToolButton</class>
   <extends>QToolButton</extends>
   <header>./views/keyboardInput/keybordtoolbutton.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
