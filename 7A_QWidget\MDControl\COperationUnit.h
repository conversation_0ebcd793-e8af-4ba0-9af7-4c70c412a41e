#ifndef COPERATIONUNIT_H
#define COPERATIONUNIT_H

#include <QObject>

class COperationUnit
{    
public:
    static COperationUnit *GetInstance();

    void SendJsonText(int iMachineID, int iMethodID, const QString &strText);
    void MedianUpdate(int iMachineID, const QByteArray &qPayload);
    void PCRUpdate(int iMachineID, const QByteArray &qPayload);
    void FLUpdate(int iMachineID, const QByteArray &qPayload);

private:
    COperationUnit();
    ~COperationUnit();

    quint8 _GetDestinationID(int iMethodID);

private:
    static COperationUnit *m_spInstance;

private:
    Q_DISABLE_COPY(COperationUnit)
};

#endif // COPERATIONUNIT_H
