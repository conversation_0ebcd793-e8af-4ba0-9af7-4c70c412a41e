#include "keyboard.h"

#include <iostream>
#include <cassert>
#include <fstream>
#include <map>
#include <vector>
#include <string>
#include <memory>

#include <QtGui>
#include <QMap>
#include <QTextEdit>
#include <QPalette>
#include <QDesktopWidget>
#include <QSignalMapper>
#include <QDebug>
#include <QStyle>
#include <QFile>

#include "common/language/language.h"
#include "common/keyboardtoolbutton/keyboardtoolbutton.h"
#include "views/languagekeys/languagebasekey.h"
#include "views/longpressselector/longpressselector.h"
#include "views/pingyinselector/pingyinselector.h"
#include "views/characterkeys/characterbasekey.h"
#include "views/characterkeys/numkey/numkey.h"
#include "views/characterkeys/symbolkey/symbolkey.h"
#include "views/languagekeys/russiakey/russiakey.h"
#include "views/languagekeys/englishkey/englishkey.h"
#include "views/languagekeys/greekkey/greekkey.h"
#include "views/languagekeys/germankey/germankey.h"
#include "views/languagekeys/arabickey/arabickey.h"
#include "views/languagekeys/frenchkey/frenchkey.h"
#include "views/languagekeys/italiankey/italiankey.h"
#include "views/languagekeys/spanishkey/spanishkey.h"
#include "views/languagekeys/polskikey/polskikey.h"
#include "views/languagekeys/romaniakey/romaniakey.h"
#include "views/languagekeys/croatiakey/croatiakey.h"
#include "views/languagekeys/bulgariakey/bulgariakey.h"
#include "views/languagekeys/portugalkey/portugalkey.h"
#include "views/languagekeys/turkishkey/turkishkey.h"
#include "views/languagekeys/finnishkey/finnishkey.h"
#include "views/languagekeys/ukrainekey/ukrainekey.h"
#include "views/languagekeys/kazakhkey/kazakhkey.h"

//增加QTextEdit的样式设置
QString g_styleSheet = QString("QWidget#KeyBoard{background-color: &bkgdColor;}\
                            QLineEdit{background-color: &btnColor;font-size:&fontSizepx;border-radius:3px;}\
                            QTextEdit{background-color: &btnColor;font-size:&fontSizepx;border-radius:3px;}\
                            QLabel{background-color: &btnColor;font-size:&fontSizepx;border-radius:3px;}\
                            QListView{background-color: &btnColor;font-size:&fontSizepx;border-radius:3px;}\
                            QToolButton{background-color: &btnColor; color:rgb(38,38,38);font-size:&fontSizepx;border-radius:3px;}\
                            QToolButton:pressed{background-color: &btnColor; color:rgb(38,38,38);font-size:&fontSizepx;color:rgb(50,157,253);border-radius:3px;}\
                            QToolButton:hover{\
                                    color:rgb(50,157,253);border-radius:3px;}\
                            QToolButton#capsLockBtn{\
                                    image: url(:/images/icon_small_normal.png);border-radius:3px;}\
                            QToolButton#capsLockBtn:checked{\
                                    /*background-color: &btnColor;*/image: url(:/images/icon_big.png);border-radius:3px;}\
                            QToolButton#toolButton_Return{\
                                    image: url(:/images/back1.png);border-radius:3px;}\
                            QToolButton#toolButton_Return:pressed{\
                                    image: url(:/images/back2.png);border-radius:3px;}\
                            QToolButton#toolButton_Hide{\
                                    image: url(:/images/return1.png);border-radius:3px;}\
                            QToolButton#toolButton_Hide:pressed{\
                                    image: url(:/images/return2.png);border-radius:3px;}\
                            QToolButton#toolButton_BackSpace{\
                                    image: url(:/images/delete1.png);border-radius:3px;}\
                            QToolButton#toolButton_BackSpace:pressed{\
                                    image: url(:/images/delete2.png);border-radius:3px;}\
                            QToolButton#toolButton_hide{\
                                    background-color: &btnColor;font-size:&fontSizepx;font-weight:bold;border-radius:3px;}\
                            QToolButton#toolButton_hide:pressed{\
                                    background-color: &btnColor;font-size:&fontSizepx;font-weight:bold;border-radius:3px;}\
                            QToolButton#toolButton_hide:hover{\
                                    background-color: &btnColor;font-size:&fontSizepx;font-weight:bold;border-radius:3px;}\
                            QToolButton#toolButton_PageUp{\
                                    image: url(:/images/ch_up1.png);border-radius:3px;}\
                            QToolButton#toolButton_PageUp:pressed{\
                                    image: url(:/image/keypad/ch_up2.png);border-radius:3px;}\
                            QToolButton#toolButton_PageDown{\
                                    image: url(:/images/ch_down1.png);border-radius:3px;}\
                            QToolButton#toolButton_PageDown:pressed{\
                                    image: url(:/images/ch_down2.png);border-radius:3px;}\
                            QToolButton#toolButton_prevPage{\
                                    image: url(:/images/br_left.png);border-radius:3px;}\
                            QToolButton#toolButton_prevPage:disabled{\
                                    image:none;background-color: &bkgdColor;border-radius:3px;}\
                            QToolButton#toolButton_nextPage{\
                                    image: url(:/images/br_right.png);border-radius:3px;}\
                            QToolButton#toolButton_nextPage:disabled{\
                                    image:none;;background-color: &bkgdColor;border-radius:3px;}\
                            ");


static int g_keyboardType = KEYBOARD_TYPE_MAIN;

class KeyBoardButtons
{
    std::vector<QToolButton *> mBtns;

public:
    enum EnumButtonCount
    {
        NumCount = 10,
        SpaceCount = 1,
        DecCount= 1,
        DotCount= 1,
    };

    KeyBoardButtons(KeyBoard *keyBoard)
    {
        QToolButton *btns[] =
        {
            keyBoard->toolButton_0, keyBoard->toolButton_1, keyBoard->toolButton_2, keyBoard->toolButton_3,
            keyBoard->toolButton_4, keyBoard->toolButton_5, keyBoard->toolButton_6, keyBoard->toolButton_7,
            keyBoard->toolButton_8, keyBoard->toolButton_9,
            keyBoard->toolButton_space,
            keyBoard->toolButton__,
            keyBoard->toolButton_dot
        };

        assert(TotalCount() == sizeof(btns)/sizeof(btns[0]));
        mBtns.assign(btns, btns + TotalCount());
    }

    size_t TotalCount() const
    {
        return NumCount + SpaceCount+DecCount + DotCount;
    }

    QList<int> List_keyBord()
    {
        QList<int> key_bordNUm;

        for(int i = 0x30; i < 0x3a; i++)
        {
            key_bordNUm << i;
        }
        for(int i = 0x41; i < 0x5b; i++)
        {
            key_bordNUm << i;
        }

        key_bordNUm << 0x20;

        return key_bordNUm;
    }

    QToolButton **Begin() const
    {
        return const_cast<QToolButton **>(&mBtns[0]);
    }

    QToolButton **End() const
    {
        return Begin() + TotalCount();
    }

    QToolButton **NumCharBegin() const
    {
        return Begin();
    }

    QToolButton **NumCharEnd() const
    {
        return NumCharBegin() + NumCount;
    }

    QToolButton *Space() const
    {
        return Begin()[NumCount];
    }

    QToolButton *Dot() const
    {
        return Begin()[NumCount + SpaceCount + DecCount];
    }

    QToolButton *Dec() const
    {
        return Begin()[NumCount + SpaceCount];
    }
};

class KeyBoard::PrivateData
{
public:
    PrivateData(KeyBoard* p)
        : parent(p)
        , lastFocusedWgt(NULL)
        , pressBtn(NULL)
        , btnColor(QString("#FFFFFF"))
        , bkgdColor(QString("#E6E6E6"))
        , textColor(QString("#000"))
        , pressedColor(QString("#868482"))
        , fontFamily(QString("Source Han Sans CN"))
        , fontSize(30)
    {
        pageSize            =
        capsLock            = false;
        quoteDblLeftInput   = false;
        apostropheLeftInput = false;
        currentLanguage     = WfKeyboard::LanguageFunction::GetInstance()->GetCurrentLanguageType();
        currentKey          = currentLanguage;
    }

    void Init();

    void InitSpecialKeys();

    void InitCurrentKey();

    void InitMapper();

    void InitLatinKeyMap();

    void InitConnect();

    void SetMapper();

    void ConnectMapper();

    void SetLongPressMapper();

    void ConnectLongPressMapper();

    void SetModeButtonTag();

    void GotoMainKey();

    void GotoKey(QWidget *key);

    void UpdateStyleSheet();

public:
    KeyBoard *parent;

    QSignalMapper *sm;
    QSignalMapper *pressedSm;
    QSignalMapper *releaseSm;
    QSignalMapper *longPressSm;

    Numkey                      *numKey;
    Symbolkey                   *symbolKey;
    PingYinSelector             *pingYinSelector;
    LongPressSelector           *longPressSelector;
    QMap<int,LanguageBaseKey*>  *languageKeyMap;

    QWidget                     *lastFocusedWgt;
    KeyBoardToolButton           *pressBtn;

    bool    capsLock;
    bool    quoteDblLeftInput;
    bool    apostropheLeftInput;
    QTimer  longPressTmr;

    WfKeyboard::LANGUAGE_TYPE currentKey;
    WfKeyboard::LANGUAGE_TYPE currentLanguage;

    std::list<QWidget *> historyLst;

    QString btnColor;
    QString bkgdColor;
    QString textColor;
    QString pressedColor;
    QString fontFamily;
    int fontSize;
    int pageSize;
};

void KeyBoard::PrivateData::Init()
{
    InitSpecialKeys();
    InitLatinKeyMap();
    InitCurrentKey();
    InitMapper();
    InitConnect();

    parent->toolButton_backspace->setAutoRepeat(true);
    parent->toolButton_backspace->setAutoRepeatDelay(400);
    parent->toolButton_backspace->setAutoRepeatInterval(60);
}

void KeyBoard::PrivateData::InitSpecialKeys()
{
    longPressSelector = new LongPressSelector();
    pingYinSelector   = new PingYinSelector(parent);
    numKey            = new Numkey(parent, parent->stackedWidget);
    symbolKey         = new Symbolkey(parent, parent->stackedWidget);

    parent->stackedWidget_Normal_Header->addWidget(longPressSelector);
    parent->stackedWidget->setCurrentIndex(0);
    parent->toolButton_num->setHidden(false);
}

void KeyBoard::PrivateData::InitCurrentKey()
{
    if(languageKeyMap->keys().contains(currentKey))
    {
        QString chs, eng, math, space;

        LanguageBaseKey* baseKey = languageKeyMap->value(currentKey);
        baseKey->GetTranslate(chs,eng, math, space);

        parent->stackedWidget_Normal->insertWidget(0,baseKey->GetWidget());
        parent->stackedWidget_Normal->setCurrentWidget(baseKey->GetWidget());
        numKey->Translate(space);
        symbolKey->Translate(chs, eng, math, space);
    }

    SetModeButtonTag();
    pingYinSelector->SetActivate(currentKey == WfKeyboard::chinese);
}

void KeyBoard::PrivateData::InitLatinKeyMap()
{
    languageKeyMap              = new QMap<int,LanguageBaseKey*>();
    EnglishKey* chineseKey      = new EnglishKey("中");
    EnglishKey* englishKey      = new EnglishKey("EN");
    GreekKey* greekKey          = new GreekKey("GR");
    GermanKey* germanKey        = new GermanKey("DE");
    ItalianKey* italianKey      = new ItalianKey("IT");
    SpanishKey* spanishKey      = new SpanishKey("ESP");
    PolskiKey* polskiKey        = new PolskiKey("PL");
    RomaniaKey* romaniaKey      = new RomaniaKey("RO");
    CroatiaKey* croatiaKey      = new CroatiaKey("SH");
    PortugalKey* portugalKey    = new PortugalKey("PO");

    ArabicKey* arabicKey        = new ArabicKey("AZ");
    FrenchKey* frenchKey        = new FrenchKey("FR");
    BulgariaKey* bulgariaKey    = new BulgariaKey("BU");
    TurkishKey* turkishKey      = new TurkishKey("TU");
    FinnishKey* finnishkey      = new FinnishKey("FIN");
    Ukrainekey* ukrainekey      = new Ukrainekey("UKR");
    Russiakey* russiaKey        = new Russiakey("RU");
    KazakhKey* kazakhKey        = new KazakhKey("KK");

    languageKeyMap->insert(WfKeyboard::chinese,      chineseKey);
    languageKeyMap->insert(WfKeyboard::english,      englishKey);
    languageKeyMap->insert(WfKeyboard::greek,        greekKey);
    languageKeyMap->insert(WfKeyboard::german,       germanKey);
    languageKeyMap->insert(WfKeyboard::italian,      italianKey);
    languageKeyMap->insert(WfKeyboard::spanish,      spanishKey);
    languageKeyMap->insert(WfKeyboard::polski,       polskiKey);
    languageKeyMap->insert(WfKeyboard::romania,      romaniaKey);
    languageKeyMap->insert(WfKeyboard::croatia,      croatiaKey);
    languageKeyMap->insert(WfKeyboard::portugal,     portugalKey);
    languageKeyMap->insert(WfKeyboard::russia,       russiaKey);
    languageKeyMap->insert(WfKeyboard::arabic,       arabicKey);
    languageKeyMap->insert(WfKeyboard::french,       frenchKey);
    languageKeyMap->insert(WfKeyboard::bulgaria,     bulgariaKey);
    languageKeyMap->insert(WfKeyboard::turkey,       turkishKey);
    languageKeyMap->insert(WfKeyboard::finnish,      finnishkey);
    languageKeyMap->insert(WfKeyboard::ukrainian,    ukrainekey);
    languageKeyMap->insert(WfKeyboard::kazakh,       kazakhKey);
}

void KeyBoard::PrivateData::InitConnect()
{
    parent->connect(parent->toolButton_backspace,   SIGNAL(clicked()),  parent, SLOT(SlotBackSpaceBtnClicked()));
    parent->connect(parent->toolButton_hide,        SIGNAL(clicked()),  parent, SLOT(SlotEscBtnClicked()));
    parent->connect(parent->toolButton_znen,        SIGNAL(clicked()),  parent, SLOT(SlotLanguageBtnClicked()));
    parent->connect(parent->toolButton_num,         SIGNAL(clicked()),  parent, SLOT(SlotGotoNumBtnClicked()));
    parent->connect(parent->toolButton_symbol,      SIGNAL(clicked()),  parent, SLOT(SlotGotoSymbolBtnClicked()));

    parent->connect(parent, SIGNAL(SignalUpdatePosition(QWidget*)), parent, SLOT(SlotUpdatePosition(QWidget*)));

    parent->connect(&longPressTmr, SIGNAL(timeout()), parent, SLOT(SlotLongPressTimerOut()));
}

void KeyBoard::PrivateData::InitMapper()
{
    sm              = new QSignalMapper(parent);
    pressedSm       = new QSignalMapper(parent);
    releaseSm       = new QSignalMapper(parent);
    longPressSm     = new QSignalMapper(parent);

    SetMapper();
    ConnectMapper();
    SetLongPressMapper();
    ConnectLongPressMapper();

    if(languageKeyMap->keys().contains(currentLanguage))
    {
        languageKeyMap->value(currentLanguage)->SetSignalMapper(sm,pressedSm,releaseSm);
    }

    parent->connect(sm, SIGNAL(mapped(const QString&)), parent,SLOT(SlotSetDispText(const QString&)));
    parent->connect(pressedSm, SIGNAL(mapped(QWidget*)), parent, SLOT(SlotPressedSmMapped(QWidget *)));
    parent->connect(releaseSm, SIGNAL(mapped(QWidget*)), parent, SLOT(SlotReleaseSmMapped(QWidget*)));
}

void KeyBoard::PrivateData::ConnectMapper()
{
    KeyBoardButtons buttons(parent);
    for (QToolButton **btn = buttons.NumCharBegin(); btn < buttons.NumCharEnd(); btn++)
    {
        KeyBoardToolButton* button = dynamic_cast<KeyBoardToolButton* >(*btn);
        parent->connect(button, SIGNAL(pressEventSignal()), pressedSm, SLOT(map()));
        parent->connect(button, SIGNAL(clicked()), sm, SLOT(map()));
        parent->connect(button, SIGNAL(releaseEventSignal()), releaseSm, SLOT(map()));

    }
    parent->connect(buttons.Space(), SIGNAL(clicked()), sm, SLOT(map()));
    parent->connect(buttons.Dec(), SIGNAL(clicked()), sm, SLOT(map()));
    parent->connect(buttons.Dot(), SIGNAL(clicked()), sm, SLOT(map()));
}

void KeyBoard::PrivateData::SetModeButtonTag()
{
    if(currentKey != WfKeyboard::english)
    {
        if(languageKeyMap->keys().contains(currentLanguage))
        {
            parent->toolButton_znen->setText(languageKeyMap->value(currentLanguage)->GetName());
        }
    }
    else
    {
        parent->toolButton_znen->setText("EN");
    }
}

void KeyBoard::PrivateData::SetLongPressMapper()
{
    for(int i = 0;i < longPressSelector->GetAllButtons().count();i++)
    {
        pressedSm->setMapping(longPressSelector->GetAllButtons().at(i),longPressSelector->GetAllButtons().at(i));
        longPressSm->setMapping(longPressSelector->GetAllButtons().at(i), longPressSelector->GetAllButtons().at(i)->text());
        releaseSm->setMapping(longPressSelector->GetAllButtons().at(i),longPressSelector->GetAllButtons().at(i));
    }
}

void KeyBoard::PrivateData::ConnectLongPressMapper()
{
    QList<QAbstractButton*> keys = longPressSelector->GetAllButtons();
    for(int i = 0; i < keys.count(); i++)
    {
        KeyBoardToolButton* button = dynamic_cast<KeyBoardToolButton* >(keys.at(i));
        connect(button, SIGNAL(pressEventSignal()), pressedSm, SLOT(map()));
        connect(button, SIGNAL(clicked()), longPressSm, SLOT(map()));
        connect(button, SIGNAL(releaseEventSignal()), releaseSm, SLOT(map()));
    }
    parent->connect(longPressSm,SIGNAL(mapped(const QString&)),parent,SLOT(SlotSetDispText(const QString&)));

}

void KeyBoard::PrivateData::SetMapper()
{
    KeyBoardButtons buttons(parent);

    for (QToolButton **btn = buttons.NumCharBegin(); btn < buttons.NumCharEnd(); btn++)
    {
        pressedSm->setMapping(*btn, *btn);
        sm->setMapping(*btn, (*btn)->text());
        releaseSm->setMapping(*btn, *btn);
    }
    sm->setMapping(buttons.Space(), " ");
    sm->setMapping(buttons.Dec(),parent->toolButton__->text());
    sm->setMapping(buttons.Dot(),parent->toolButton_dot->text());
}

void KeyBoard::PrivateData::GotoKey(QWidget *key)
{
    if (parent->stackedWidget->currentWidget() != key)
    {
        historyLst.push_back(parent->stackedWidget->currentWidget());
        parent->stackedWidget->setCurrentWidget(key);
    }
}

void KeyBoard::PrivateData::GotoMainKey()
{
    historyLst.clear();
    parent->bottomWidget->setVisible(true);
    parent->stackedWidget->setCurrentIndex(0);
}

void KeyBoard::PrivateData::UpdateStyleSheet()
{
    QString style = g_styleSheet;
    style = style.replace("&btnColor", btnColor);
    style = style.replace("&bkgdColor", bkgdColor);
    style = style.replace("&textColor", textColor);
    style = style.replace("&pressedColor", pressedColor);
    style = style.replace("&fontFamily", fontFamily);
    style = style.replace("&fontSize", QString::number(fontSize));
    style = style.replace("&listViewfontSize", QString::number(fontSize - 5));


    parent->setStyleSheet(style);
    numKey->setStyleSheet(style);
    symbolKey->setStyleSheet(style);
    pingYinSelector->setStyleSheet(style);
    longPressSelector->setStyleSheet(style);

#if 1
    parent->style()->polish(parent);
    numKey->style()->polish(numKey);
    symbolKey->style()->polish(symbolKey);
    pingYinSelector->style()->polish(pingYinSelector);
    longPressSelector->style()->polish(longPressSelector);

    //需要额外执行1次,还没找到原因
    parent->toolButton_num->style()->polish(parent->toolButton_num);
    parent->toolButton_0->style()->polish(parent->toolButton_0);
    parent->toolButton_1->style()->polish(parent->toolButton_1);
    parent->toolButton_2->style()->polish(parent->toolButton_2);
    parent->toolButton_3->style()->polish(parent->toolButton_3);
    parent->toolButton_4->style()->polish(parent->toolButton_4);
    parent->toolButton_5->style()->polish(parent->toolButton_5);
    parent->toolButton_6->style()->polish(parent->toolButton_6);
    parent->toolButton_7->style()->polish(parent->toolButton_7);
    parent->toolButton_8->style()->polish(parent->toolButton_8);
    parent->toolButton_9->style()->polish(parent->toolButton_9);
#endif


    for (QMap<int,LanguageBaseKey*>::const_iterator it = languageKeyMap->constBegin(); it != languageKeyMap->constEnd(); ++it)
    {
        QWidget* wgt = it.value()->GetWidget();

        if (wgt != NULL)
        {
            wgt->setStyleSheet(style);
            wgt->style()->polish(wgt);
        }
    }

    qDebug()<<Q_FUNC_INFO<<style;
}

KeyBoard::KeyBoard(QWidget * parent, Qt::WindowFlags fl)
    : QWidget(parent, fl)
    , md(new PrivateData(this))
{
    setupUi(this);
    connect(qApp, SIGNAL(focusChanged(QWidget*,QWidget*)),this, SLOT(SlotFocusChanged(QWidget*,QWidget*)));

    Qt::WindowFlags flags = Qt::Window | Qt::WindowStaysOnTopHint | Qt::FramelessWindowHint |Qt::Popup;
#ifndef WIN32
    flags |= Qt::X11BypassWindowManagerHint;
#endif
    this->setWindowFlags( flags );
    this->setAutoFillBackground(true);

    QPalette mainBackPattle;
    mainBackPattle.setColor(QPalette::Background,QColor(225,229,234));
    this->setPalette(mainBackPattle);

    ReadStyleSheet();

    md->Init();

    oriEdt      = Edit_ori;
    titleLbl    = lbl_title;
    oriTedt     = tEdit_ori;

    if(md->currentLanguage == WfKeyboard::chinese)
    {
        emit toolButton_znen->clicked();
    }
}

KeyBoard::~KeyBoard()
{
    delete md;
}

void KeyBoard::ReadStyleSheet()
{
    QString strQssPath = ":/qss/default.qss";
    //qDebug()<<Q_FUNC_INFO<<strQssPath<<QFile::exists(strQssPath);
    QFile file(strQssPath);
    if(file.open(QIODevice::ReadOnly))
    {
        QString strStyle = file.readAll();
        strStyle.remove("\r").remove("\n").remove("\t");
        file.close();
        g_styleSheet = strStyle;
        //qDebug()<<Q_FUNC_INFO<<g_styleSheet;
    }
}

void KeyBoard::Show()
{  
    switch(g_keyboardType)
    {
    case KEYBOARD_TYPE_NUM:
        SlotGotoNumBtnClicked();
        break;
    case KEYBOARD_TYPE_SYMBOL:
        SlotGotoSymbolBtnClicked();
        break;
    case KEYBOARD_TYPE_MAIN:
        md->GotoMainKey();
        break;
    default:
        break;
    }

    g_keyboardType = KEYBOARD_TYPE_MAIN;

    QWidget::show();
}

void KeyBoard::SetKeyType(int type)
{
    g_keyboardType = type;
}

void KeyBoard::SlotPressedSmMapped(QWidget *widget)
{
    md->longPressTmr.start(1000);
    md->pressBtn = dynamic_cast<KeyBoardToolButton *>(widget);
    if(md->longPressSelector->isVisible())
    {
        stackedWidget_Normal_Header->setCurrentWidget(pageNormalHeader_Num);
        md->longPressSelector->setVisible(false);
    }
}

void KeyBoard::SlotReleaseSmMapped(QWidget *)
{
    md->longPressTmr.stop();
    md->pressBtn = NULL;
}

void KeyBoard::SlotKeyPressed(int key,int modifier)
{
    if(md->currentLanguage != WfKeyboard::chinese)
       return;

    if (stackedWidget->currentWidget() == md->numKey)
    {
#ifdef QT_DEBUG
        qDebug() << Q_FUNC_INFO << __LINE__ << "当前的输入法界面为数字输入界面" << modifier << key;
#endif
        HandleKeyboardCharacterInput(key, modifier);
    }
    else if (stackedWidget->currentWidget() == md->symbolKey)
    {
#ifdef QT_DEBUG
        qDebug() << Q_FUNC_INFO << __LINE__ << "当前的输入法界面为符号输入界面" << modifier << key;
#endif

        if (!HandleKeyboardCharacterInput(key, modifier))
        {
            if (Qt::NoModifier == modifier)
                HandleInput(QChar(key));
        }
    }
    else if (stackedWidget->currentWidget() == pageNormal)
    {
#ifdef QT_DEBUG
        qDebug() << Q_FUNC_INFO << __LINE__ << "当前的输入法界面为通用输入界面" << modifier << key;
#endif
        if (Qt::ControlModifier == modifier && Qt::Key_Space == key)
        {
            SlotLanguageBtnClicked();
            return;
        }
        if (WfKeyboard::english == md->currentKey)
            HandleKeyboardCharacterInput(key, modifier);
        else if(WfKeyboard::chinese == md->currentKey)
            HandleKeyboardPinyinInput(key, modifier);
    }
}

bool KeyBoard::HandleKeyboardCharacterInput(int key, int modifier)
{
#ifdef QT_DEBUG
    qDebug() << Q_FUNC_INFO << __LINE__ << "当前输入状态为英文输入状态！！！" << modifier << key;
#endif
    if (Qt::ControlModifier <= modifier && Qt::MetaModifier >= modifier)
    {
        if (Qt::ControlModifier == modifier)
        {
            if (Qt::Key_A == key)
                Edit_ori->selectAll();
            else if (Qt::Key_C == key)
            {
                if (!Edit_ori->selectedText().isEmpty())
                    Edit_ori->copy();
            }
            else if (Qt::Key_V == key)
                Edit_ori->paste();
            else if (Qt::Key_X == key)
            {
                if (!Edit_ori->selectedText().isEmpty())
                {
                    Edit_ori->copy();
                    Edit_ori->clear();
                }
            }
            else
                return false;
            return true;
        }
        return false;
    }
    else if (Qt::Key_Shift <= key && Qt::Key_Alt >= key)
        return false;
    else if (Qt::Key_Menu == key || Qt::Key_NumLock == key)
        return false;
    else if (Qt::Key_CapsLock == key)
    {
        SlotCapslockClicked();
        return true;
    }
    else if (Qt::Key_Backspace == key)
    {
        SlotBackSpaceBtnClicked();
        return true;
    }
    else if (Qt::Key_A <= key && Qt::Key_Z >= key)
    {
        bool bCap = ((Qt::ShiftModifier == modifier) ^ md->capsLock);

        char input = 'a';

        if (bCap)
            input = 'A';

        input = input + key - Qt::Key_A;

        HandleInput(QChar(input));
        return true;
    }
    else if (Qt::Key_0 <= key && Qt::Key_9 >= key)
    {
        HandleInput(QChar(key));
        return true;
    }
    else if (Qt::KeypadModifier == modifier)
    {
        if (Qt::Key_Insert <= key && Qt::Key_PageDown >= key)
            return false;

        if (Qt::Key_Enter == key)
            SlotEscBtnClicked();
        else
            HandleInput(QChar(key));
        return true;
    }
    else if (Qt::Key_Escape == key || Qt::Key_Return == key)
    {
        SlotEscBtnClicked();
        return true;
    }
    else if (Qt::Key_Insert <= key && Qt::Key_PageDown >= key)
        return false;
    else if ((Qt::Key_Space <= key && Qt::Key_Slash >= key)
             || (Qt::Key_Colon <= key && Qt::Key_At >= key)
             || (Qt::Key_BracketLeft <= key && Qt::Key_AsciiTilde >= key))
    {
        HandleInput(QChar(key));
        return true;
    }
    return false;
}

bool KeyBoard::HandleKeyboardPinyinInput(int key, int modifier)
{
#ifdef QT_DEBUG
    qDebug() << Q_FUNC_INFO << __LINE__ << "当前输入状态为中文输入状态！！！" << modifier << key;
#endif
    if (Qt::ControlModifier <= modifier && Qt::MetaModifier >= modifier)
    {
        if (Qt::ControlModifier == modifier)
        {
            if (Qt::Key_A == key)
                Edit_ori->selectAll();
            else if (Qt::Key_C == key)
            {
                if (!Edit_ori->selectedText().isEmpty())
                    Edit_ori->copy();
            }
            else if (Qt::Key_V == key)
                Edit_ori->paste();
            else if (Qt::Key_X == key)
            {
                if (!Edit_ori->selectedText().isEmpty())
                {
                    Edit_ori->copy();
                    Edit_ori->clear();
                }
            }
            else
                return false;
            return true;
        }
        return false;
    }
    else if (Qt::Key_A <= key && Qt::Key_Z >= key)
    {
        char cBase = (Qt::ShiftModifier == modifier) ? 'A' : 'a';
        QString text = QString(QChar(cBase + key - Qt::Key_A));
        if(!("." == text || "-" == text))
        {
            md->pingYinSelector->SetKeyboardInputFlag(true);
            md->pingYinSelector->InPutChar(text.data());
            return true;
        }
    }
    else if (Qt::Key_Backspace == key)
    {
        SlotBackSpaceBtnClicked();
        return true;
    }
    else if (Qt::Key_Return == key || Qt::Key_Enter == key || Qt::Key_Escape == key)
    {
        SlotEscBtnClicked();
        return true;
    }
    else if (Qt::Key_Space == key)
    {
        QString strSpell = md->pingYinSelector->PinYin();

        QString text = " ";
        if (!strSpell.isEmpty())
        {
            QString candidate = md->pingYinSelector->GetCandidateWord(0);
            text = candidate.isEmpty() ? strSpell : candidate;
        }

        HandleInput(text);

        md->pingYinSelector->Reset();
        return true;
    }
    else if (Qt::Key_0 <= key && Qt::Key_9 >= key)
    {
        if (md->pingYinSelector->PinYin().isEmpty())
            HandleInput(QChar(key));
        else
        {
            int iIndex = QString(char(key)).toInt();

            QString strWord = md->pingYinSelector->GetCandidateWord(iIndex - 1);
            if (!strWord.isEmpty())
                HandleInput(strWord);

            md->pingYinSelector->Reset();
        }
        return true;
    }
    else if ((Qt::Key_Insert <= key && Qt::Key_F35 >= key) || Qt::Key_Tab == key)
    {
        if (Qt::Key_PageUp == key || Qt::Key_Up == key)
        {
            md->pingYinSelector->CandidateWordPageUp();
            return true;
        }
        else if (Qt::Key_PageDown == key || Qt::Key_Down == key)
        {
            md->pingYinSelector->CandidateWordPageDown();
            return true;
        }
        return false;
    }
    else
    {
        if ((Qt::Key_Exclam <= key && Qt::Key_Slash>= key)
                || (Qt::Key_Colon <= key && Qt::Key_At >= key)
                || (Qt::Key_BracketLeft <= key && Qt::Key_AsciiTilde >= key))
        {
            if (md->pingYinSelector->PinYin().isEmpty())
                HandleKeyboardChineseSymbolInput(key, modifier);
        }
        else
        {
            if (Qt::NoModifier == modifier)
                HandleInput(QChar(key));
        }
        return true;
    }

    return false;
}

bool KeyBoard::HandleKeyboardChineseSymbolInput(int key, int modifier)
{
    bool bHandle = true;

    QString input;
    switch (key)
    {
    case Qt::Key_Exclam:
        input = QChar(0xFF01);
        break;
    case Qt::Key_QuoteDbl:
        input = (md->quoteDblLeftInput ? QChar(0x201D) : QChar(0x201C));
        md->quoteDblLeftInput = !md->quoteDblLeftInput;
        break;
    case Qt::Key_Dollar:
        input = QChar(0xFFE5);
        break;
    case Qt::Key_Apostrophe:
        input = (md->apostropheLeftInput ? QChar(0x2019) : QChar(0x2018));
        md->apostropheLeftInput = !md->apostropheLeftInput;
        break;
    case Qt::Key_ParenLeft:
        input = QChar(0xFF08);
        break;
    case Qt::Key_ParenRight:
        input = QChar(0xFF09);
        break;
    case Qt::Key_Comma:
        input = QChar(0xFF0C);
        break;
    case Qt::Key_Period:
        input = (Qt::KeypadModifier == modifier ? QChar(key) : QChar(0x3002));
        break;
    case Qt::Key_Colon:
        input = QChar(0xFF1A);
        break;
    case Qt::Key_Semicolon:
        input = QChar(0xFF1B);
        break;
    case Qt::Key_Less:
        input = QChar(0x300A);
        break;
    case Qt::Key_Greater:
        input = QChar(0x300B);
        break;
    case Qt::Key_Question:
        input = QChar(0xFF1F);
        break;
    case Qt::Key_BracketLeft:
        input = QChar(0x3010);
        break;
    case Qt::Key_Backslash:
        input = QChar(0x3001);
        break;
    case Qt::Key_BracketRight:
        input = QChar(0x3011);
        break;
    case Qt::Key_AsciiCircum:
        input = QString("%1%1").arg(QChar(0x2026));
        break;
    case Qt::Key_Underscore:
        input = QString("%1%1").arg(QChar(0x2014));
        break;
    case Qt::Key_QuoteLeft:
        input = QChar(Qt::Key_periodcentered);
        break;
    case Qt::Key_NumberSign:
    case Qt::Key_Percent:
    case Qt::Key_Ampersand:
    case Qt::Key_Asterisk:
    case Qt::Key_Plus:
    case Qt::Key_Minus:
    case Qt::Key_Slash:
    case Qt::Key_Equal:
    case Qt::Key_At:
    case Qt::Key_BraceLeft:
    case Qt::Key_Bar:
    case Qt::Key_BraceRight:
    case Qt::Key_AsciiTilde:
        input = QChar(key);
        break;
    default:
        bHandle = false;
        break;
    }

    if (bHandle)
        HandleInput(input);

    return bHandle;
}

void KeyBoard::HandleInput(const QString &input)
{
    const QValidator *validator = Edit_ori->validator();
    if (!validator)
    {
        Edit_ori->setText(Edit_ori->text() + input);
        return;
    }

    QString strValidate = Edit_ori->text() + input;
    QValidator::State state = QValidator::Acceptable;
    if (validator->inherits("QRegExpValidator"))
    {
        const QRegExpValidator *pRegExpValidator =
                static_cast<const QRegExpValidator *>(validator);
        int iPos = 0;
        state = pRegExpValidator->validate(strValidate, iPos);
#ifndef QT_NO_DEBUG
        qDebug() << Q_FUNC_INFO << __LINE__ << "正则表达式匹配:" << strValidate
                 << state << pRegExpValidator->regExp().pattern();
#endif
    }
    else if (validator->inherits("QIntValidator"))
    {
        const QIntValidator *pIntValidator =
                static_cast<const QIntValidator *>(validator);
        int iPos = 0;
        state = pIntValidator->validate(strValidate, iPos);
#ifndef QT_NO_DEBUG
        qDebug() << Q_FUNC_INFO << __LINE__ << "整形数值匹配:" << strValidate
                 << state << pIntValidator->bottom() << pIntValidator->top();
#endif
    }
    else if (validator->inherits("QDoubleValidator"))
    {
        const QDoubleValidator *pDoubleValidator =
                static_cast<const QDoubleValidator *>(validator);
        int iPos = 0;
        state = pDoubleValidator->validate(strValidate, iPos);
#ifndef QT_NO_DEBUG
        qDebug() << Q_FUNC_INFO << __LINE__ << "浮点数值匹配:"
                 << strValidate << state << pDoubleValidator->bottom()
                 << pDoubleValidator->top() << pDoubleValidator->decimals();
#endif
    }

    if (QValidator::Invalid == state)
        return;

    Edit_ori->setText(strValidate);
}

bool KeyBoard::event(QEvent *e)
{
    switch (e->type())
    {
    case QEvent::WindowActivate:
        if (md->lastFocusedWgt)
            md->lastFocusedWgt->activateWindow();
        break;
    default:
        break;
    }

    return QWidget::event(e);
}

void KeyBoard::changeEvent(QEvent *event)
{
    if (event->type() == QEvent::LanguageChange)
    {
        Ui::KeyBoard::retranslateUi(this);
    }

    QWidget::changeEvent(event);
}

void KeyBoard::SlotFocusChanged(QWidget * /*oldFocus*/, QWidget *newFocus)
{
    if (newFocus != NULL && !this->isAncestorOf(newFocus) && md->lastFocusedWgt != newFocus)
    {
        md->lastFocusedWgt = newFocus;
        SlotUpdatePosition(NULL);
    }
}

void KeyBoard::SlotSetDispText(const QString& text)
{
    if(md->longPressSelector->isVisible())
        return;
    if(md->currentKey != WfKeyboard::chinese)
        SetHanziText(text);
    else
    {
        md->pingYinSelector->SetKeyboardInputFlag(false);
        md->pingYinSelector->InPutChar(text.data());
    }
}

void KeyBoard::SlotCapslockClicked()
{
    if(md->currentKey == WfKeyboard::chinese )
        return ;

    md->capsLock = !md->capsLock;

    if(md->capsLock)
    {
        md->languageKeyMap->value(md->currentKey)->SetCapital();
    }else
    {
        md->languageKeyMap->value(md->currentKey)->SetLowerCase();
    }

    md->SetMapper();
}

void KeyBoard::SlotEscBtnClicked()
{
    this->hide();

    emit SignalEditFinished();
}

void KeyBoard::SlotBackSpaceBtnClicked()
{
    if (WfKeyboard::chinese != md->currentKey)
    {
        Edit_ori->backspace();

        QString strText = tEdit_ori->document()->toPlainText();
        if(!strText.isEmpty())
        {
            strText.remove(strText.length()- 1, 1);
            tEdit_ori->setPlainText(strText);

            QTextCursor cursor = tEdit_ori->textCursor();
            cursor.movePosition(QTextCursor::End);
            tEdit_ori->setTextCursor(cursor);
        }
        return;
    }

    if (md->pingYinSelector->PinYin().isEmpty())
    {
        Edit_ori->backspace();

        QString strText = tEdit_ori->document()->toPlainText();
        if(!strText.isEmpty())
        {
            strText.remove(strText.length()- 1, 1);
            tEdit_ori->setPlainText(strText);

            QTextCursor cursor = tEdit_ori->textCursor();
            cursor.movePosition(QTextCursor::End);
            tEdit_ori->setTextCursor(cursor);
        }

        md->pingYinSelector->Reset();
        return;
    }

    md->pingYinSelector->DeleteInputChar();
}

void KeyBoard::SlotLanguageBtnClicked()
{
    if(md->currentKey != WfKeyboard::english)
    {
        md->currentKey = WfKeyboard::english;
    }
    else
    {
        md->currentKey = md->currentLanguage;
    }

    if(md->languageKeyMap->keys().contains(md->currentKey))
    {
        QString chs,eng, math, space;

        LanguageBaseKey* baseKey = md->languageKeyMap->value(md->currentKey);
        baseKey->GetTranslate(chs,eng, math, space);

        if(this->stackedWidget_Normal->indexOf(baseKey->GetWidget()) < 0)
        {
            this->stackedWidget_Normal->addWidget(baseKey->GetWidget());
            baseKey->SetSignalMapper(md->sm,md->pressedSm,md->releaseSm);
        }

        this->stackedWidget_Normal->setCurrentWidget(baseKey->GetWidget());
        md->numKey->Translate(space);
        md->symbolKey->Translate(chs, eng, math, space);
    }

    md->SetModeButtonTag();
    md->pingYinSelector->SetActivate(md->currentKey == WfKeyboard::chinese);
}

void KeyBoard::SetHanziText( const QString& text )
{
    if (text.isNull() || text.isEmpty())
        return ;

    int length = text.length();
    for (int i = 0; i < length; i++)
    {
        emit SignalCharacterGenerated( text.at(i) );
    }

    emit SignalUpdatePosition(QApplication::focusWidget());
}

void KeyBoard::SlotUpdatePosition(QWidget *widget)
{
    Q_UNUSED(widget)
}

//void KeyBoard::setVisible(bool visible)
//{
//    qDebug()<<Q_FUNC_INFO<<visible;

//    QWidget::setVisible(visible);

//    if (visible)
//        SlotUpdatePosition(NULL);
//}

void KeyBoard::SlotGotoNumBtnClicked()
{
    md->GotoKey(md->numKey);
    this->bottomWidget->setVisible(false);
}

void KeyBoard::GotoSymbolKey(int symbolType)
{
    this->bottomWidget->setVisible(false);
    md->symbolKey->Active(static_cast<Symbolkey::SymbolTypeEnum>(symbolType));
    md->GotoKey(md->symbolKey);
}

void KeyBoard::SlotGotoSymbolBtnClicked()
{
    GotoSymbolKey(md->currentKey == WfKeyboard::chinese? Symbolkey::CHS: Symbolkey::ENG);
}

void KeyBoard::GotoPrevKey()
{
    if (!md->historyLst.empty())
     {
         QWidget *wgt = *md->historyLst.rbegin();
         md->historyLst.pop_back();
         stackedWidget->setCurrentWidget(wgt);
     }
    if (md->historyLst.empty()){
        this->bottomWidget->setVisible(true);
        this->stackedWidget->setCurrentIndex(0);
    }
}

void KeyBoard::SendKeyEvent(QKeyEvent *keyEvent)
{
    if (oriEdt)
    {
        QApplication::sendEvent(oriEdt, keyEvent);
        emit SignalUpdatePosition(oriEdt);
    }
}

void KeyBoard::GenerateEnterChar()
{
    QKeyEvent enterPress(QEvent::KeyPress, Qt::Key_Return, Qt::NoModifier);
    SendKeyEvent(&enterPress);
}

void KeyBoard::GenerateBackSpaceChar()
{
    SlotBackSpaceBtnClicked();
}

void KeyBoard::HideKeyBoard()
{    
    SlotEscBtnClicked();
}

void KeyBoard::SlotLongPressTimerOut()
{
    if(md->pressBtn != NULL)
    {
        if(md->pressBtn->GetLongPressFlag())
        {
            QString str = md->pressBtn->GetCurrentLongPressStr();

            md->longPressSelector->SetAlternativeText(str);
            stackedWidget_Normal_Header->setCurrentWidget(md->longPressSelector);
            md->SetLongPressMapper();
            md->longPressSelector->setVisible(true);
        }
    }
}

void KeyBoard::SetPagingEnabled(const bool& enable)
{
    if(md->pingYinSelector != NULL)
    {
        md->pingYinSelector->SetPagingEnabled(enable);
    }
}

void KeyBoard::SetColor(const QString& backgroundColor, const QString& buttonColor,
                        const QString &textColor, const QString& pressedColor)
{
    md->btnColor = buttonColor;
    md->bkgdColor = backgroundColor;
    md->textColor = textColor;
    md->pressedColor = pressedColor;
    md->UpdateStyleSheet();
}

void KeyBoard::SetFontFamily(const QString &fontFamily)
{
    md->fontFamily = fontFamily;
    md->UpdateStyleSheet();
}

void KeyBoard::SetFontPixelSize(const int& size)
{
    md->fontSize = size;
    md->UpdateStyleSheet();
}

void KeyBoard::SetPageSize(const int& size)
{
    if(md->pingYinSelector != NULL)
    {
        md->pingYinSelector->SetPageSize(size);
    }
}
