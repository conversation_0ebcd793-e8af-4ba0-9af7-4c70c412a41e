#include "CSelfTestDevItemWidget.h"
#include <QStyle>
#include <QDate>
#include <QDebug>
#include <QVariant>
#include <QBoxLayout>
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "CMessageBox.h"
#include "DBControl/CFtpDB.h"
#include "DBControl/CDevInfoDB.h"
#include "CReadWriteXlsxThread.h"
#include "CmdBus/CCmdBase.h"
#include "COperationUnit.h"

CSelfTestDevItemWidget::CSelfTestDevItemWidget(int iMachineID, const SDevParamsStruct &sDevParams, QWidget *parent)
    : QWidget(parent)
    , m_iMachineID(iMachineID)
    , m_sDevParams(sDevParams)
    , m_eStatus(eDeviceDisconnect)
    , m_strTipsText(tr("提示"))
{
    this->setFixedSize(sDevParams.iItemWidth, sDevParams.iItemHeight);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroupBox());
    pLayout->addStretch(1);
    this->setLayout(pLayout);

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalSetDevStatus, this, &CSelfTestDevItemWidget::SlotSetDevStatus);

    SlotSetDevStatus(iMachineID, m_eStatus);

    m_pRunTimer = new QTimer(this);
    connect(m_pRunTimer, &QTimer::timeout, this, &CSelfTestDevItemWidget::_SlotRunTimer);
}

int CSelfTestDevItemWidget::GetMachineID() const
{
    return m_iMachineID;
}

void CSelfTestDevItemWidget::ReceiveCaliResult(int iResult)
{
    if(!m_pRunTimer->isActive())
        return;

    _SelftestResult(iResult);
    m_pRunTimer->stop();
    m_pCaliBtn->setText(tr("开始自检"));

    if(0 != iResult)
    {
        ShowWarning(this->parentWidget(), m_strTipsText, tr("%1#检测模块自检失败，请联系客服").arg(m_iMachineID+1));
        return;
    }

    ShowSuccess(this->parentWidget(), m_strTipsText, tr("%1#检测模块自检成功").arg(m_iMachineID+1));
}

void CSelfTestDevItemWidget::UpdateDateLabel(QString strLastDate, QString strNextDate)
{
    m_pLastDateLabel->setText(strLastDate);
    m_pNextDateLabel->setText(strNextDate);
}

void CSelfTestDevItemWidget::SlotSetDevStatus(int iMachineID, DeviceStatus eStatus)
{
    if(m_iMachineID != iMachineID)
        return;

    m_eStatus = eStatus;
    bool bBtnEnable = true;
    QString strPropertyText, strTipsText;
    switch (eStatus)
    {
    case eDeviceDisconnect:
        strPropertyText = "disconnect";
        strTipsText = tr("离线中");
        bBtnEnable = false;
        break;
    case eDeviceSelfTest:
        strPropertyText = "self_test";
        strTipsText = tr("自检中");
        break;
    case eDeviceIdle:
        strPropertyText = "idle";
        strTipsText = tr("空闲中");
        break;
    case eDeviceTesting:
        strPropertyText = "testing";
        strTipsText = tr("测试中");
        break;
    case eDeviceTestDone:
        strPropertyText = "test_done";
        strTipsText = tr("测试完成");
        break;
    case eDeviceTestStopped:
        strPropertyText = "test_stopped";
        strTipsText = tr("测试停止");
        break;
    case eDeviceTestStopping:
        strPropertyText = "test_stopping";
        strTipsText = tr("停止中");
        break;
    case eDeviceTestFail:
        strPropertyText = "test_fail";
        strTipsText = tr("测试失败");
        break;
    case eDeviceFault:
        strPropertyText = "fault";
        strTipsText = tr("故障中");
        bBtnEnable = false;
        break;
    case eDeviceProcessing:
        strPropertyText = "processing";
        strTipsText = tr("处理中");
        break;
    case eDeviceReset:
        strPropertyText = "reset";
        strTipsText = tr("复位中");
        break;
    default:
        break;
    }

    m_pTitleLabel->setProperty("status", strPropertyText);
    m_pTitleLabel->style()->polish(m_pTitleLabel);

    m_pIndexLabel->setProperty("status", strPropertyText);
    m_pIndexLabel->style()->polish(m_pIndexLabel);

    m_pTextLabel->setProperty("status", strPropertyText);
    m_pTextLabel->style()->polish(m_pTextLabel);
    m_pTextLabel->setText(strTipsText);

    m_pCaliBtn->setProperty("status", strPropertyText);
    m_pCaliBtn->style()->polish(m_pCaliBtn);
    m_pCaliBtn->setEnabled(bBtnEnable);
}

void CSelfTestDevItemWidget::_SlotSelftestBtn()
{
    if(m_pRunTimer->isActive())
    {
        ShowInformation(this->parentWidget(), m_strTipsText, tr("正在自检，请耐心等待"));
        return;
    }

    if(eDeviceIdle != m_eStatus)
    {
        ShowInformation(this->parentWidget(), m_strTipsText, tr("只能在空闲状态下自检，请稍后重试"));
        return;
    }

    int iBtnType = ShowQuestion(this->parentWidget(), m_strTipsText, tr("确定要对%1#检测模块进行自检吗").arg(m_iMachineID+1));
    if(QMessageBox::Yes != iBtnType)
        return;

    m_pCaliBtn->setText(tr("正在自检"));
    m_pRunTimer->start(90000);

    QString strCmd = CCmdBase::GetJsonCmdString(Method_machine_reset);
    COperationUnit::GetInstance()->SendJsonText(m_iMachineID, Method_machine_reset, strCmd);

    QString strCurrentTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    QString strSN = CPublicConfig::GetInstance()->GetMachineSN();
    strSN = DeleteSpecialCharacters(strSN);
    m_strXlsxPath = QString("selftest%1_%2#_%3.xlsx").arg(strCurrentTime).arg(m_iMachineID+1).arg(strSN);
    m_strXlsxPath = CPublicConfig::GetInstance()->GetXlsxDir() + m_strXlsxPath;
    qDebug()<<QString("%1#开始自检").arg(m_iMachineID+1)<<m_strXlsxPath<<strCmd;

    //QTimer::singleShot(15000, this, &CSelfTestDevItemWidget::_SlotTestReceiveSelftest);
}

void CSelfTestDevItemWidget::_SlotTestReceiveSelftest()
{
    ReceiveCaliResult(0);
}

void CSelfTestDevItemWidget::_SlotRunTimer()
{
    _SelftestResult(-1);
    m_pRunTimer->stop();
    m_pCaliBtn->setText(tr("开始自检"));
    ShowWarning(this->parentWidget(), m_strTipsText, tr("%1#检测模块自检超时，请联系客服").arg(m_iMachineID+1));
}

void CSelfTestDevItemWidget::_SelftestResult(int iResult)
{
    QDate qDate = QDate::currentDate();
    QString strLastDate = qDate.toString("yyyy-MM-dd");
    m_pLastDateLabel->setText(strLastDate);

    if(0 == iResult)
    {
        QString strNextDate = qDate.addYears(1).toString("yyyy-MM-dd");
        m_pNextDateLabel->setText(strNextDate);
        CDevInfoDB::GetInstance().UpdateSelftestDate(m_iMachineID, strLastDate, strNextDate);
    }
    else
    {
        QString strNextDate = strLastDate;
        m_pNextDateLabel->setText(strNextDate);
        CDevInfoDB::GetInstance().UpdateSelftestDate(m_iMachineID, strLastDate, strNextDate);
    }

    QString strDate = QDate::currentDate().toString("yyyy-MM-dd");
    CDevInfoDB::GetInstance().AddSelftestResult(m_iMachineID, QString::number(iResult), "", strDate, m_strXlsxPath);

    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = m_strXlsxPath;
    pXlsxStruct->strTableName = "selftest";
    pXlsxStruct->strTitleList << tr("检测模块") << tr("结果") << tr("详情") << tr("日期");

    QString strResult = (0 == iResult ? tr("成功") : tr("失败"));
    QVariantList qVarList;
    qVarList << QString("%1#").arg(m_iMachineID+1) << strResult << "" << strDate;
    pXlsxStruct->varWriteDataList << qVarList;

    FunWriteXlsxEndCallBack lambdaFunction = [](QString strXlsxName, QString strTableName)
    {
        qDebug()<<strXlsxName<<strTableName<<"write end";
        CFtpDB::GetInstance()->AddFtpUploadFile(strXlsxName);
    };

    pXlsxStruct->WriteEndCallBack = lambdaFunction;

    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
}

QGroupBox *CSelfTestDevItemWidget::_CreateGroupBox()
{
    int iHeight = m_sDevParams.iTitleHeight;

    m_pIndexLabel = new QLabel(QString("%1").arg(m_iMachineID + 1));
    m_pIndexLabel->setFixedSize(m_sDevParams.iIndexWidth, iHeight);
    m_pIndexLabel->setObjectName("IndexLabel");
    m_pIndexLabel->setAlignment(Qt::AlignCenter);
    m_pIndexLabel->setProperty("status", "idle");

    m_pTextLabel = new QLabel(tr("空闲中"));
    m_pTextLabel->setFixedHeight(iHeight);
    m_pTextLabel->setObjectName("StatusLabel");
    m_pTextLabel->setProperty("status", "idle");

    m_pTitleLabel = new QLabel;
    m_pTitleLabel->setFixedSize(m_sDevParams.iItemWidth, iHeight);
    m_pTitleLabel->setObjectName("TitleLabel");
    m_pTitleLabel->setWindowOpacity(0.14);
    m_pTitleLabel->setProperty("status", "idle");

    m_pLastNameLabel = new QLabel(tr("上次自检："));
    m_pLastNameLabel->setObjectName("CalibrationLabel");

    m_pLastDateLabel = new QLabel("2025-07-01");
    m_pLastDateLabel->setObjectName("CalibrationLabel");

    m_pNextNameLabel = new QLabel(tr("推荐自检："));
    m_pNextNameLabel->setObjectName("CalibrationLabel");

    m_pNextDateLabel = new QLabel("2026-07-01");
    m_pNextDateLabel->setObjectName("CalibrationLabel");

    m_pCaliBtn = new QPushButton(tr("开始自检"));
    m_pCaliBtn->setObjectName("ActBtn");
    m_pCaliBtn->setProperty("status", "idle");
    m_pCaliBtn->setFixedSize(m_sDevParams.iItemWidth, m_sDevParams.iTitleHeight);
    connect(m_pCaliBtn, &QPushButton::clicked, this, &CSelfTestDevItemWidget::_SlotSelftestBtn);

    QHBoxLayout *pTitleLayout = new QHBoxLayout;
    pTitleLayout->setMargin(0);
    pTitleLayout->setSpacing(0);
    pTitleLayout->addWidget(m_pIndexLabel);
    pTitleLayout->addSpacing(15);
    pTitleLayout->addWidget(m_pTextLabel);
    pTitleLayout->addStretch(1);
    m_pTitleLabel->setLayout(pTitleLayout);

    int iDevNum = 1;
    int iItemNum = 8;
    CPublicConfig::GetInstance()->GetDevItemNum(iDevNum, iItemNum);

    bool bVLayout = false;
    if(eLanguage_English == gk_iLanguage && 1 == iDevNum && iItemNum >= 3)
        bVLayout = true;
    if(eLanguage_Spanish == gk_iLanguage && 1 == iDevNum && iItemNum >= 3)
        bVLayout = true;
    if(eLanguage_German == gk_iLanguage && 1 == iDevNum && iItemNum >= 3)
        bVLayout = true;
    if(eLanguage_Italian == gk_iLanguage && 1 == iDevNum && iItemNum >= 3)
        bVLayout = true;

    QBoxLayout *pLastLayout = nullptr;
    if(bVLayout)
    {
        pLastLayout = new QVBoxLayout;
        pLastLayout->setMargin(0);
        pLastLayout->setSpacing(0);
        pLastLayout->addStretch(1);
        pLastLayout->addWidget(m_pLastNameLabel, 0, Qt::AlignHCenter);
        pLastLayout->addWidget(m_pLastDateLabel, 0, Qt::AlignHCenter);
        pLastLayout->addStretch(1);
    }
    else
    {
        pLastLayout = new QHBoxLayout;
        pLastLayout->setMargin(0);
        pLastLayout->setSpacing(5);
        pLastLayout->addStretch(1);
        pLastLayout->addWidget(m_pLastNameLabel);
        pLastLayout->addWidget(m_pLastDateLabel);
        pLastLayout->addStretch(1);
    }

    QBoxLayout *pNextLayout = nullptr;
    if(bVLayout)
    {
        pNextLayout = new QVBoxLayout;
        pNextLayout->setMargin(0);
        pNextLayout->setSpacing(0);
        pNextLayout->addStretch(1);
        pNextLayout->addWidget(m_pNextNameLabel, 0, Qt::AlignHCenter);
        pNextLayout->addWidget(m_pNextDateLabel, 0, Qt::AlignHCenter);
        pNextLayout->addStretch(1);
    }
    else
    {
        pNextLayout = new QHBoxLayout;
        pNextLayout->setMargin(0);
        pNextLayout->setSpacing(5);
        pNextLayout->addStretch(1);
        pNextLayout->addWidget(m_pNextNameLabel);
        pNextLayout->addWidget(m_pNextDateLabel);
        pNextLayout->addStretch(1);
    }

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addWidget(m_pTitleLabel);
    pLayout->addStretch(1);
    pLayout->addLayout(pLastLayout);
    pLayout->addSpacing(15);
    pLayout->addLayout(pNextLayout);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pCaliBtn);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(m_sDevParams.iItemWidth, m_sDevParams.iItemHeight);
    pGroupBox->setObjectName("PLCItemGroupBox");
    pGroupBox->setLayout(pLayout);

    return pGroupBox;
}
