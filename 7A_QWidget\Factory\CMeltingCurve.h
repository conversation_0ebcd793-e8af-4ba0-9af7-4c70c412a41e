#ifndef CMELTINGCURVE_H
#define CMELTINGCURVE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-12-26
  * Description: 熔解曲线
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QStackedWidget>

#include "CCmdBase.h"
#include "CLabelLineEdit.h"
#include "Melting/CMeltingOneWidget.h"

class CMeltingCurve : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CMeltingCurve(QWidget *parent = nullptr);
    ~CMeltingCurve();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

public slots:
    void SlotTestStart(int iMachineID);

private slots:
    void _SlotMachineChanged(int iMachineID);
    void _SlotCardIDComboBoxChanged(const QString &strCarID);
    void _SlotCardIDLineEditTextChanged(const QString &strCardID);
    void _SlotShowCurrentTestCardID(int iMachineID);

private:
    void _InitWidget();
    void _InitLayout();
    void _ShowLast100MeltingID();

private:
    CLabelComboBox *m_pMachineComboBox;
    CLabelLineEdit *m_pCardIDLineEdit;
    QComboBox *m_pCardIDComboBox;
    QStackedWidget *m_pStackedWidget;
    QList<CMeltingOneWidget *> m_pMeltingWidgetList;
};

#endif // CMELTINGCURVE_H
