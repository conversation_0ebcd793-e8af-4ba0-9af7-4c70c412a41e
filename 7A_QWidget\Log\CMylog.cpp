#include "CMylog.h"
#include <thread>
#include <QApplication>
#include <QTextCodec>

#include "PublicFunction.h"
#include "ZipManager/zip.h"

#define SIZE_1M 1024 * 1024

CMylog &CMylog::GetInstance()
{
    static CMylog log;
    return log;
}

CMylog::CMylog()
    : m_bPrint(true)
    , m_iKeepDays(20)
    , m_iMaxSize(50)
    , m_strLogDir(QApplication::applicationDirPath() + "/data/")
    , m_qCurrentDate(QDate::currentDate())
{    
    setbuf(stdout, NULL);
    setObjectName("CMylog");

#ifdef __aarch64__
    m_bPrint = false;
#endif

    connect(this, &CMylog::SignalInit, this, &CMylog::_SlotInit, Qt::BlockingQueuedConnection);
    connect(&m_qThread, &QThread::finished, this, &CMylog::_SlotExit);

    this->moveToThread(&m_qThread);
    m_qThread.start();

    emit SignalInit();
}

void CMylog::_SlotInit()
{
    CreateDir(m_strLogDir);
    QString strCurrentDay = m_qCurrentDate.toString("yyyyMMdd");
    QStringList strDirList = {"global", "info", "warning", "error", "fatal", "slave"};
    for(int i=0; i<strDirList.size(); i++)
    {
        QString strFileDir = m_strLogDir + strDirList.at(i) + "/";
        m_strFileDirList.push_back(strFileDir);
        CreateDir(strFileDir);

        QString strFileName = QString("%1_%2.log").arg(strDirList.at(i)).arg(strCurrentDay);
        QString strFilePath = strFileDir + strFileName;
        QFile *pFile = new QFile(strFilePath, this); //先不open创建文件, fatal error这种可能没有
        m_filePtrMap.insert(static_cast<LogLevel>(i), pFile);
    }

    m_pWriteTimer = new QTimer(this);
    connect(m_pWriteTimer, &QTimer::timeout, this, &CMylog::_SlotWriteTimeout);
    m_pWriteTimer->start(5);

    m_pRunTimer = new QTimer(this);
    connect(m_pRunTimer, &QTimer::timeout, this, &CMylog::_SlotRunTimeout);
    m_pRunTimer->start(1000);

    std::thread mythread(&CMylog::_ZipLog, this);
    mythread.detach();
}

CMylog::~CMylog()
{
    m_qThread.quit();
    m_qThread.wait();
}

void CMylog::_SlotExit()
{
    if(m_pWriteTimer)
    {
        m_pWriteTimer->stop();
        m_pWriteTimer->deleteLater();
    }

    if(m_pRunTimer)
    {
        m_pRunTimer->stop();
        m_pRunTimer->deleteLater();
    }
}

void CMylog::AddLog(const CMylog::LogStruct &sLogStruct)
{
    m_qMutex.lock();
    m_sLogStructList.push_back(sLogStruct);
    m_qMutex.unlock();
}

void CMylog::_SlotWriteTimeout()
{
    if(m_sLogStructList.isEmpty())
        return;

    m_qMutex.lock();
    QList<LogStruct> qList = m_sLogStructList;
    m_sLogStructList.clear();
    m_qMutex.unlock();

    for(int i=0; i<qList.size(); i++)
    {
        const LogStruct &sLogStruct = qList.at(i);
        QFile *pFile = m_filePtrMap.value(sLogStruct.level, 0);
        if(!pFile)
            continue;

        _PrintfLog(sLogStruct.strLog);
        _CheckFileSize(pFile);

        if(!pFile->isOpen())
            pFile->open(QIODevice::WriteOnly | QIODevice::Append);
        pFile->write(sLogStruct.strLog.toLocal8Bit() + "\n");
        pFile->flush();
    }
}

void CMylog::_SlotWriteLog()
{

}

void CMylog::_SlotRunTimeout()
{
    QDate qNowDate = QDate::currentDate();
    if(qNowDate <= m_qCurrentDate)
        return;
    m_qCurrentDate = qNowDate;

    for(auto it=m_filePtrMap.begin(); it!=m_filePtrMap.end(); it++)
    {
        QFile *pFile = it.value();
        if(!pFile)
            continue;

        QFileInfo qFileInfo(pFile->fileName());
        QString strNewName = QString("%1/%2_%3.log")
                .arg(qFileInfo.absolutePath())
                .arg(qFileInfo.baseName().split("_").at(0))
                .arg(qNowDate.toString("yyyyMMdd"));
        if(pFile->isOpen())
        {
            pFile->flush();
            pFile->close();
        }
        pFile->setFileName(strNewName);
    }

    std::thread mythread(&CMylog::_ZipLog, this);
    mythread.detach();
}

void CMylog::_ZipLog()
{
    //防止在凌晨12点前几秒启动软件,构造函数里会执行1次此函数,定时器1秒后又会创建线程执行此函数
    static bool bZipping = false;
    if(bZipping)
        return;
    bZipping = true;

    QDate qNowDate = QDate::currentDate();

    for(int i=0; i<m_strFileDirList.size(); i++)
    {
        QDir qDir(m_strFileDirList.at(i));
        QFileInfoList qInfoList = qDir.entryInfoList(QDir::Files);
        for(int j=0; j<qInfoList.size(); j++)
        {
            QFileInfo qFileInfo = qInfoList.at(j);
            QDate qBirthDate = qFileInfo.birthTime().date();
            qBirthDate = QDate::fromString(qFileInfo.baseName().split("_").at(1), "yyyyMMdd");
            QString strFilePath = qFileInfo.absoluteFilePath();
            QString strFileName = qFileInfo.fileName();

            //删除超过保存日期的文件
            if(qNowDate.addDays(-m_iKeepDays) > qBirthDate)
            {
                QFile::remove(strFilePath);
                printf("delete log %s\n", strFilePath.toStdString().c_str());
                continue;
            }

            //压缩日志 (1)当天的跳过 (2)已压缩.zip的跳过
            if(qBirthDate == qNowDate || strFileName.endsWith(".zip"))
                continue;

            _ZipFile(strFilePath);
        }
    }

    bZipping = false;
}

void CMylog::_ZipFile(QString strFilePath)
{
    if(!QFile::exists(strFilePath))
        return;

    QFileInfo fileInfo(strFilePath);
    QString strFileName = fileInfo.fileName();

    QString strZipName = QString("%1.zip").arg(strFilePath);
    HZIP hz = CreateZip(strZipName.toStdString().c_str(), 0);
    ZipAdd(hz, strFileName.toStdString().c_str(), strFilePath.toStdString().c_str());
    CloseZip(hz);
    printf("zip %s %s\n", strFilePath.toStdString().c_str(), strZipName.toStdString().c_str());

    QFile::remove(strFilePath);
}

void CMylog::_PrintfLog(const QString &strLog)
{
    if(m_bPrint)
    {
#ifdef Q_OS_WIN
        QTextCodec *pTextCodec = QTextCodec::codecForName("GBK");
        QByteArray byte = pTextCodec->fromUnicode(strLog);
        printf("%s \n",byte.data());
#else
        printf("%s\n",strLog.toLocal8Bit().data());
#endif
    }
}

void CMylog::_CheckFileSize(QFile *pFile)
{
    if(pFile->size() < m_iMaxSize * SIZE_1M)
        return;

    QString strOldName = pFile->fileName();
    QString strFileName = pFile->fileName();
    int index = strFileName.split(".").last().toInt();
    if(0 == index)
    {
        // xxx.log
        strFileName += ".1";
    }
    else
    {
        // xxx.1 xxx.2
        int last = strFileName.lastIndexOf(".");
        strFileName = QString("%1.%2").arg(strFileName.mid(0, last)).arg(index + 1);
    }
    printf("new log name: %s\n", strFileName.toLocal8Bit().data());
    pFile->flush();
    pFile->close();
    pFile->setFileName(strFileName);

    //std::thread mythread(&CMylog::_ZipFile, this, strOldName);
    //mythread.detach();
}
