#ifndef CDATETIME_H
#define CDATETIME_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2023-10-30
  * Description: 选择日期
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QWidget>
#include <QPushButton>
#include <QGroupBox>
#include "CLineEdit.h"

class CTimeObject : public QWidget
{
    Q_OBJECT
public:
    CTimeObject(int iMinValue, int iMaxValue, int iCurrentValue, const QString &strName, QWidget *parent = nullptr);
    ~CTimeObject();

    int GetValue() const;
    void SetValue(int iValue);
    void SetRange(int iMinValue, int iMaxValue);

signals:
    void SignalValueChanged(int);

private slots:
    void _SlotAddBtn();
    void _SlotSubBtn();

private:
    void _InitWidget();

private:
    int m_iMinValue;
    int m_iMaxValue;
    int m_iCurrentValue;
    QString m_strName;

    QLabel *m_pLabel;
    QPushButton *m_pAddBtn;
    CLineEdit *m_pLineEdit;
    QPushButton *m_pSubBtn;
};

class CDateTime : public QWidget
{
    Q_OBJECT
public:
    explicit CDateTime(QWidget* parent = nullptr);
    ~CDateTime();

public:
    void SetDate(const QString &strDate);
    QString GetDate() const;

signals:
    void SignalConfirmDate(const QString &strDate);

protected:
    void paintEvent(QPaintEvent* pEvent) override;

private slots:
    void _SlotConfirmBtn();
    void _SlotCancelBtn();
    void _SlotYearChanged(int iYear);
    void _SlotMonthChanged(int iMonth);

private:
    void _InitWidget();
    QGroupBox *_CreateGroup();

private:
    CTimeObject *m_pYearWidget;
    CTimeObject *m_pMonthWidget;
    CTimeObject *m_pDayWidget;

    QPushButton *m_pConfirmBtn;
    QPushButton *m_pCancelBtn;

    QList<int> m_iBigMonthList, m_iSmallMonthList;
};

#endif // CDATETIME_H
