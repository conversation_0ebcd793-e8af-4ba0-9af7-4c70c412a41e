﻿#ifndef _HL7_SEGMENT_FACTORY_H_
#define _HL7_SEGMENT_FACTORY_H_

#include <map>
#include <string>

typedef void* (*NewInstancePt)();
std::map<std::string, NewInstancePt>& ObjectFactoryMap();
class HL7SegmentFactory
{
public:
	static void* CreateObject(const char* className)
	{
		std::map<std::string, NewInstancePt>::const_iterator it;
		std::map<std::string, NewInstancePt>& dynCreateMap = ObjectFactoryMap();
		it = dynCreateMap.find(className);
		if (it == dynCreateMap.end())
		{
			return NULL;
		}
		else
		{
			NewInstancePt np = it->second;
			return np();
		}
	}
	static void RegisterClass(const char* className, NewInstancePt np)
	{
		std::map<std::string, NewInstancePt>& dynCreateMap = ObjectFactoryMap();
		dynCreateMap[className] = np;
	}
};

//

class Register
{
public:
	Register(const char* className, NewInstancePt np)
	{
		HL7SegmentFactory::RegisterClass(className, np);
	}
};



#define REGISTER_CLASS(class_name)\
class class_name##Register \
{ \
public: \
	static void* NewInstance() \
{ \
	return new class_name(); \
} \
private: \
	static Register reg; \
}; \
	
#define IMPLEMENT_REGISTER_CLASS(class_name) \
	Register class_name##Register::reg(#class_name, class_name##Register::NewInstance)\

#endif

