#ifndef CHISTORYDETAILAMPCURVE_H
#define CHISTORYDETAILAMPCURVE_H

#include <QWidget>
#include <QPushButton>
#include <QRadioButton>
#include <QTableWidget>
#include "HistoryPage/CManualReviewWidget.h"
#include "CLineEdit.h"
#include "qcustomplot.h"
#include "CLabelLineEdit.h"
#include "CHLabelTitleWidget.h"
#include "PublicParams.h"

class CHistoryDetailAmpCurve : public QWidget
{
    Q_OBJECT
public:
    CHistoryDetailAmpCurve(bool bHistoryMode, QWidget *parent = nullptr);

    void ClearData();
    void ClearFL();
    void closeBtnHandle();

    void SetReviewMode(bool bReview); //审核模型生成的pdf隐藏原始曲线和审核项
    void ThresholdLlineShow(bool bShow);
    void ShowNmzaData();
    void ShowRawData();
    void SwitchHole(int iHole);


    void SetFLDataMap(const QList<QMap<double, double>> &dFLMap);
    void UpdateInfo(const SResultInfoStruct &sResult); //更新结果Label和CT表格

    // 改
    void SetHistoryIDHandle(const SCardInfoStruct&  sCardInfo, const SResultInfoStruct& sResultInfo,const SLotInfoStruct& sLotInfo);
    void UpdateTestReusltLabel(const SResultInfoStruct& sResultInfo,const SLotInfoStruct& sLotInfo);
    void UpdateTableWidget(const SResultInfoStruct& sResultInfo,const SLotInfoStruct& sLotInfo);
    // 试剂pdf特殊要求
    void PdfSpecialRequirement(bool bPdfImage);

public slots:
    void SlotShowRawCurve(bool bShowRawCurve);

signals:
    void SignalReviewConfirmBtn(SResultInfoStruct sResultInfo);
protected:
    void paintEvent(QPaintEvent* pEvent) override;
    void showEvent(QShowEvent *pEvent) override;
    void hideEvent(QHideEvent *pEvent) override;

private:

    struct sResultCtInfo
    {
        QString m_strCurveName;
        QString m_strCt;
        QString m_strThreshould;
        QString m_strPNIResult;
        QString m_strReview;
        QString m_strPCRResult;
        QString m_strTestMode;

        bool m_bNull = false;
        bool m_bControl = false;
        bool m_bPositive = false;
        bool m_bQcTest = false;
        int m_index = -1; //位置索引
    };
    void UpdateTableWidgetHandle(QTableWidget* pTableWidget,QList<sResultCtInfo>& sInfolist);
    void GetCtResultInfo(const SResultInfoStruct& sResultInfo,const SLotInfoStruct& sLotInfo,QList<CHistoryDetailAmpCurve::sResultCtInfo> &sInfolist);
    void GetQcTestCtResultInfo(const SResultInfoStruct& sResultInfo,const SLotInfoStruct& sLotInfo,QList<CHistoryDetailAmpCurve::sResultCtInfo> &sInfolist);
    void _UpdateRawPlot();
    void _UpdateNmzaPlot();
    void _SetPlotData(const QList<QList<double>> &dFLDataList);
    void _ResetYRang(const QList<QList<double>> &dList);    
    void _InitCustomplot();
    void _SetChannelCheckBox(const QString &qColor, int iChart, const QString &strChannelName);
    void _AddGraph(const QColor &qColor, int iChart, const QString &strChartName);
    void _InitTableWidget();
    QWidget* _InitChannelWidget();
    void _InitWidget();
    void _InitLayout();
    void _ChannelCheckBoxHandle();
    void _HideCheckBox();
    void _ChangeTableResult(QTableWidget* table,const stReviewParam& sResultParam);    
    void _SetManualBtn(bool bReview,const SResultInfoStruct& sResultInfo);
    void _SetThreshouldLineValue();

    // 设置阈值线复选框 是否显示
    void _SetThreCheckBoxShow(bool bShow);

    void _UpdateProjectLabel(const QString& strProjectName,const QList<sResultCtInfo>& sCtInfo);
    void _UpdateResultLabel(const QString &strTest,const QString& strProjectName,const QList<sResultCtInfo>& sCtInfo);
private slots:
    void _SlotRawRadioBtn();
    void _SlotNmzaRadioBtn();
    void _SlotChannelCheckBox();
    void _SlotReviewBtn();
    void _SlotReviewConfirmBtn();
    void _ReviewConfirm(stReviewParam stReviewParam);

private:
    bool m_bShow{false};
    int m_nTableSelectRow{-1};

    QPushButton* m_pPcrTabBtn;
    QLabel *m_pTopTabBackgroundLabel;

    QButtonGroup* m_PcrButton_group;

    QRadioButton *m_pRawRadioBtn, *m_pNmzaRadioBtn;
    QCustomPlot *m_pCustomPlot;

    QTableWidget *m_pTableWidget;

    QPushButton *m_pReviewCalcBtn;
    QPushButton *m_pReviewPassBtn;

    QLabel *m_pProjectResultLabel; //针对GBS项目的总结果

    QList<QCPItemLine *> m_pDotLineList;
    QList<QCheckBox *> m_pHoleACheckBoxList;
    QList<QCheckBox *> m_pHoleBCheckBoxList;
    QCheckBox* m_threshouldCheckBox;
    QStringList m_strColorList;
    QStringList m_StrChannelNameList;

    SResultInfoStruct m_sResultInfo;
    SCardInfoStruct m_sCardInfo;
    SLotInfoStruct m_sLotInfo;

    const bool m_bHistoryMode;
    QList<QMap<double, double>> m_dFLMap;
    QStringList m_strICNameList;
    QList<sResultCtInfo> m_sResultCtInfo;
    const QString m_strPositive, m_strNegative, m_strError, m_strNull;
    bool m_bPdfImage {false};
private:
    CManualReviewWidget *m_pManualReviewWidget;
};

#endif // CHISTORYDETAILAMPCURVE_H

