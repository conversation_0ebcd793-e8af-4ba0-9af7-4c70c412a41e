#include "CPLCInfoWidget.h"
#include <QBoxLayout>

#include "PublicConfig.h"
#include "PublicFunction.h"

CPLCInfoWidget::CPLCInfoWidget(QWidget *parent) : QWidget(parent)
{
    m_iDevNum = 1;
    m_iItemNum = 8;
    CPublicConfig::GetInstance()->GetDevItemNum(m_iDevNum, m_iItemNum);

    this->setFixedSize(1636, 800);
    _Init_1x8();

    m_pCPLCDetailWidget = new CPLCDetailWidget(this);
    m_pCPLCDetailWidget->setVisible(false);
}

void CPLCInfoWidget::_SlotDetailWidgetShow(int iMachineID)
{
    m_pCPLCDetailWidget->PageShow(iMachineID);
}

void CPLCInfoWidget::_Init_1x8()
{
    m_pReturnBtn = new QPushButton(tr("返回"));
    m_pReturnBtn->setFixedSize(150, 56);
    m_pReturnBtn->setObjectName("CancelBtn");
    connect(m_pReturnBtn, &QPushButton::clicked, this, &CPLCInfoWidget::SignalReturn);

    QString strQssPath;
    SDevParamsStruct sDevParams;

    if(1 == m_iItemNum)
    {
        sDevParams.iItemWidth = 630;
        sDevParams.iItemHeight = 550;

        sDevParams.iTitleHeight = 80;
        sDevParams.iIndexWidth = 100;

        sDevParams.iDevImageWidth = 570;
        sDevParams.iDevImageHeight = 410;

        strQssPath = ":/qss/qss/home/<USER>";
    }
    else if(2 == m_iItemNum)
    {
        sDevParams.iItemWidth = 630;
        sDevParams.iItemHeight = 550;

        sDevParams.iTitleHeight = 80;
        sDevParams.iIndexWidth = 100;

        sDevParams.iDevImageWidth = 570;
        sDevParams.iDevImageHeight = 410;

        strQssPath = ":/qss/qss/home/<USER>";
    }
    else if(3 == m_iItemNum)
    {
        sDevParams.iItemWidth = 420;
        sDevParams.iItemHeight = 470;

        sDevParams.iTitleHeight = 60;
        sDevParams.iIndexWidth = 75;

        sDevParams.iDevImageWidth = 370;
        sDevParams.iDevImageHeight = 360;

        strQssPath = ":/qss/qss/home/<USER>";
    }
    else if(4 == m_iItemNum)
    {
        sDevParams.iItemWidth = 340;
        sDevParams.iItemHeight = 380;

        sDevParams.iTitleHeight = 54;
        sDevParams.iIndexWidth = 66;

        sDevParams.iDevImageWidth = 280;
        sDevParams.iDevImageHeight = 280;

        strQssPath = ":/qss/qss/home/<USER>";
    }
    else if(5 == m_iItemNum || 6 == m_iItemNum || 7 == m_iItemNum || 8 == m_iItemNum)
    {
        sDevParams.iItemWidth = 340;
        sDevParams.iItemHeight = 320;

        sDevParams.iTitleHeight = 54;
        sDevParams.iIndexWidth = 66;

        sDevParams.iDevImageWidth = 280;
        sDevParams.iDevImageHeight = 220;

        strQssPath = ":/qss/qss/home/<USER>";
    }

    for(int i=0; i<m_iItemNum; i++)
    {
        CPLCDevItemWidget *pItemWidget = new CPLCDevItemWidget(i, sDevParams);
        connect(pItemWidget, &CPLCDevItemWidget::SignalShowDetailWidget, this, &CPLCInfoWidget::_SlotDetailWidgetShow);
        m_pDevItemList.push_back(pItemWidget);
    }

    QBoxLayout *pTopLayout = nullptr;
    if(m_iItemNum <= 4)
    {
        pTopLayout = new QHBoxLayout;
        pTopLayout->setMargin(0);
        pTopLayout->setSpacing(0);
        pTopLayout->addStretch(1);
        for(int i=0; i<m_pDevItemList.size(); i++)
        {
            pTopLayout->addWidget(m_pDevItemList.at(i));
            pTopLayout->addStretch(1);
        }
    }
    else
    {
        QHBoxLayout *pUpLayout = new QHBoxLayout;
        pUpLayout->setMargin(0);
        pUpLayout->setSpacing(0);
        pUpLayout->addSpacing(45);
        pUpLayout->addWidget(m_pDevItemList.at(0));
        pUpLayout->addStretch(1);
        pUpLayout->addWidget(m_pDevItemList.at(1));
        pUpLayout->addStretch(1);
        pUpLayout->addWidget(m_pDevItemList.at(2));
        pUpLayout->addStretch(1);
        pUpLayout->addWidget(m_pDevItemList.at(3));
        pUpLayout->addSpacing(45);

        QHBoxLayout *pDownLayout = new QHBoxLayout;
        pDownLayout->setMargin(0);
        pDownLayout->setSpacing(0);
        pDownLayout->addSpacing(45);
        if(5 == m_pDevItemList.size())
        {
            pDownLayout->addWidget(m_pDevItemList.at(4));
            pDownLayout->addStretch(1);
        }
        else if(6 == m_pDevItemList.size())
        {
            pDownLayout->addWidget(m_pDevItemList.at(4));
            pDownLayout->addSpacing(62);
            pDownLayout->addWidget(m_pDevItemList.at(5));
            pDownLayout->addStretch(1);
        }
        else if(7 == m_pDevItemList.size())
        {
            pDownLayout->addWidget(m_pDevItemList.at(4));
            pDownLayout->addSpacing(62);
            pDownLayout->addWidget(m_pDevItemList.at(5));
            pDownLayout->addSpacing(62);
            pDownLayout->addWidget(m_pDevItemList.at(6));
            pDownLayout->addStretch(1);
        }
        else
        {
            pDownLayout->addWidget(m_pDevItemList.at(4));
            pDownLayout->addStretch(1);
            pDownLayout->addWidget(m_pDevItemList.at(5));
            pDownLayout->addStretch(1);
            pDownLayout->addWidget(m_pDevItemList.at(6));
            pDownLayout->addStretch(1);
            pDownLayout->addWidget(m_pDevItemList.at(7));
            pDownLayout->addSpacing(45);
        }

        pTopLayout = new QVBoxLayout;
        pTopLayout->setMargin(0);
        pTopLayout->setSpacing(0);
        pTopLayout->addLayout(pUpLayout);
        pTopLayout->addSpacing(35);
        pTopLayout->addLayout(pDownLayout);
    }

    LoadQSS(this, strQssPath);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addLayout(pTopLayout);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pReturnBtn, 0, Qt::AlignHCenter);
    this->setLayout(pLayout);
}
