﻿#ifndef CLABELTITLEWIDGET_H
#define CLABELTITLEWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-19
  * Description: 蓝色label+文字标题 水平布局
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QWidget>

class CHLabelTitleWidget : public QWidget
{
    Q_OBJECT
public:
    CHLabelTitleWidget(const QString &strTitle, QWidget *parent = nullptr);

    void ResetTitle(const QString &strTitle);

private:
    QLabel *m_pIconLabel, *m_pTextLabel;
};

#endif // CLABELTITLEWIDGET_H
