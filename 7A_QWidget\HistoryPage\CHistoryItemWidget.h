#ifndef CHISTORYITEMWIDGET_H
#define CHISTORYITEMWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-26
  * Description: 历史item
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QCheckBox>

#include "PublicParams.h"
#include "CLabelLabel.h"

class CHistoryItemWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CHistoryItemWidget(QWidget *parent = nullptr);

    bool GetChecked() const;
    void SetChecked(bool bChecked);
    void SetHistoryResult(const SResultInfoStruct &sResultInfo);
    void SetHistoryID(int iHistoryID);
    int GetHistoryID() const;

signals:
    void SignalItemChecked(int iHistoryID, bool bChecked);

private slots:
    void _SlotCheckBox();

private:
    void _ResetLabelText(CLabelLabel *pLabel, QString strText, int iWidth);

private:
    void _InitWidget();
    void _InitLayout();

private:
    QCheckBox *m_pCheckBox;
    CLabelLabel *m_pSampleIDLabel, *m_pCardIDLabel;
    CLabelLabel *m_pTestTypeLabel, *m_pTestStatusLabel;
    CLabelLabel *m_pTestTimeLabel, *m_pNameLabel;
    CLabelLabel *m_pProjectLabel, *m_pReviewLabel;
    QLabel *m_pIconLabel;

private:
    SResultInfoStruct m_sResultInfo;
    SSampleInfoStruct m_sSampleInfo;
    SCardInfoStruct m_sCardInfo;
    SLotInfoStruct m_sLotInfo;
};

#endif // CHISTORYITEMWIDGET_H
