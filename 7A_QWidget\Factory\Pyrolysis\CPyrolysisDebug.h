#pragma once

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: chenhao
  * Date: 2024-7-8
  * Description: pyrolysis-单指令调试
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/
#include <QLabel>
#include <QComboBox>
#include <QPushButton>

#include "CCmdBase.h"
#include "CLineEdit.h"
#include "CLabelComboBox.h"



class CPyrolysisDebug : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CPyrolysisDebug(QWidget *parent = nullptr);

    void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

private slots:
    void _SlotSendBtn();
    void _SlotCmdComboBoxChanged(int);

private:
    void _initParam();
    void _InitWidget();

private:
    CLabelComboBox *m_pMachineComboBox;
    QComboBox *m_pCmdComboBox;
    QComboBox *m_pParamComboBox;
    CLineEdit *m_pParamLineEdit;
    QPushButton *m_pSendBtn;

    QStringList m_strCmdList;
    QStringList m_strHTOpenList;
    QStringList m_strHTCloseList;
    QHash<int, int> m_htIndexIDMap;
};
