#include "CNoiseTest.h"
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QHeaderView>
#include <QDebug>
#include <QFile>
#include "CMessageBox.h"
#include "PublicConfig.h"
#include "MDControl/CReadWriteXlsxThread.h"

CNoiseTest::CNoiseTest(QWidget *parent) : QWidget(parent)
{
    _InitWidget();
    Register2Map(Method_FLMDT);

    m_pCBusyProgressBar = new CBusyProgressBar(gk_strTipsText, tr("正在进行底噪测试"));
    m_pLightOneTiming = new CLightOneTiming();
    connect(m_pLightOneTiming, &CLightOneTiming::SignalTimingEnd, this, &CNoiseTest::_SlotTimingEnd);
    bNoiseTested = false;
    m_iFluorescenceType = 0;
}

CNoiseTest::~CNoiseTest()
{
    UnRegister2Map(Method_FLMDT);
    delete m_pLightOneTiming;
}

void CNoiseTest::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if (bNoiseTested)
    {
        if(iMethodID == Method_FLMDT) {
            _ReceiveMDTData(qVarData);
        }
    }
}

void CNoiseTest::SetFluorescenceType(int iTypeIndex)
{
    m_iFluorescenceType = iTypeIndex;
}

void CNoiseTest::_InitWidget()
{
    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(80, 50);

    m_pReadBtn = new QPushButton(tr("读取底噪"));
    m_pReadBtn->setFixedSize(120, 40);
    connect(m_pReadBtn, &QPushButton::clicked, this, &CNoiseTest::_SlotReadNoiseBtn);

    m_pTableWidget = new QTableWidget(this);
    m_pTableWidget->setColumnCount(5);
    m_pTableWidget->setRowCount(2);
    QStringList headerLabels;
    headerLabels <<tr("孔号") << tr("FAM") << tr("HEX") << tr("ROX") << tr("CY5");
    m_pTableWidget->setHorizontalHeaderLabels(headerLabels);
    m_pTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pTableWidget->setSelectionMode(QAbstractItemView::NoSelection);
    m_pTableWidget->setShowGrid(true);
    m_pTableWidget->setFocusPolicy(Qt::NoFocus);
    m_pTableWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTableWidget->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTableWidget->setFixedSize(700, 170);

    QHeaderView* pVerticalHeader = m_pTableWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(60);
    QHeaderView* pHorizontalHeader = m_pTableWidget->horizontalHeader();
    pHorizontalHeader->resizeSection(0, 100);
    pHorizontalHeader->resizeSection(1, 150);
    pHorizontalHeader->resizeSection(2, 150);
    pHorizontalHeader->resizeSection(3, 150);
    pHorizontalHeader->resizeSection(4, 150);
    _SetTableItem(0, 0, tr("孔1"));
    _SetTableItem(1, 0, tr("孔2"));

    QHBoxLayout* pTopLayout = new QHBoxLayout;
    pTopLayout->addWidget(m_pMachineComboBox);
    pTopLayout->addWidget(m_pReadBtn);
    pTopLayout->addStretch(1);

    QVBoxLayout* pMainLayout = new QVBoxLayout;
    pMainLayout->addLayout(pTopLayout);
    pMainLayout->addWidget(m_pTableWidget, 0, Qt::AlignCenter);
    setLayout(pMainLayout);
}

void CNoiseTest::_SlotReadNoiseBtn()
{
    bNoiseTested = true;
    
    // 启动一次Timing，采集底噪
    m_pLightOneTiming->startTimingType2(m_pMachineComboBox->GetCurrentIndex(), 1);
    m_pCBusyProgressBar->show();
}

void CNoiseTest::_SlotTimingEnd()
{
    // 采集结束后，数据已在_ReceiveMDTData中填入表格
    bNoiseTested = false;
    
    // 保存底噪数据到Excel
    _SaveNoiseDataToExcel();
    
    m_pCBusyProgressBar->close();
}

void CNoiseTest::_ReceiveMDTData(const QVariant& qVarData)
{
    // qVarData: [iHole, FAM, HEX, ROX, CY5]
    QVariantList qVarList = qVarData.toList();
    if (qVarList.size() < 5) return;
    int iHole = qVarList.at(0).toInt();
    if (iHole < 0 || iHole > 1) return;
    for (int i = 0; i < 4; ++i) {
        m_dNoiseData[iHole][i] = qVarList.at(i + 1).toDouble();
        _SetTableItem(iHole, i+1, QString::number(m_dNoiseData[iHole][i], 'f', 3));
    }
}

void CNoiseTest::_SetTableItem(int iRow, int iCol, const QString& strText)
{
    QTableWidgetItem* pItem = m_pTableWidget->item(iRow, iCol);
    if (!pItem) {
        pItem = new QTableWidgetItem;
        m_pTableWidget->setItem(iRow, iCol, pItem);
    }
    pItem->setText(strText);
    pItem->setTextAlignment(Qt::AlignCenter);
}

bool CNoiseTest::_SaveNoiseDataToExcel()
{
    QString strXlsxDir = CPublicConfig::GetInstance()->GetXlsxDir();
    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = strXlsxDir + CPublicConfig::GetInstance()->GetLightSNXlsxName();
    
    // 根据荧光类型选择模板文件（这里假设使用默认模板）
    QString strTempXlsxName = "";
    if (m_iFluorescenceType == 1) {
        strTempXlsxName = "FlToolData-dye-template.xlsx";
    }
    else {
        strTempXlsxName = "FlToolData-template.xlsx";
    }
    
    // 检查xlsx目录下是否有当前SN的xlsx文件
    if (!QFile::exists(pXlsxStruct->strXlsxName)) {
        // 如果没有，从Resource目录下复制模板文件
        QString strResourceDir = CPublicConfig::GetInstance()->GetResourceDir();
        QString strTempXlsxPath = strResourceDir + strTempXlsxName;
        if (!QFile::exists(strTempXlsxPath)) {
            ShowError(nullptr, gk_strTipsText, tr("Resource目录下没有%1模板文件").arg(strTempXlsxName));
            qDebug() << QString("Resource目录下没有%1模板文件").arg(strTempXlsxName);
            delete pXlsxStruct;
            return false;
        }
        QFile::copy(strTempXlsxPath, pXlsxStruct->strXlsxName);
        if (!QFile::exists(pXlsxStruct->strXlsxName)) {
            ShowError(nullptr, gk_strTipsText, tr("复制%1模板文件到xlsx目录失败").arg(strTempXlsxName));
            qDebug() << QString("复制%1模板文件到xlsx目录失败").arg(strTempXlsxName);
            delete pXlsxStruct;
            return false;
        }
    }

    pXlsxStruct->strTableName = tr("荧光测试");
    pXlsxStruct->bDrawChart = false;
    
    // 准备底噪数据写入Excel
    // 假设底噪数据写入到特定的工作表位置
    XlsxDataStruct xlsxDataStruct;
    
    // 写入孔1数据 (行2-5，列2-5对应FAM,HEX,ROX,CY5)
    for (int channel = 0; channel < 4; channel++) {
        xlsxDataStruct.iRow = 71;  // 孔1数据行
        xlsxDataStruct.iColumn = 4 + channel;  // 从第3列开始：FAM, HEX, ROX, CY5
        xlsxDataStruct.varValue = m_dNoiseData[0][channel];
        pXlsxStruct->dataStructList.append(xlsxDataStruct);
    }
    
    // 写入孔2数据 (行3)
    for (int channel = 0; channel < 4; channel++) {
        xlsxDataStruct.iRow = 72;  // 孔2数据行
        xlsxDataStruct.iColumn = 4 + channel;  // 从第2列开始：FAM, HEX, ROX, CY5
        xlsxDataStruct.varValue = m_dNoiseData[1][channel];
        pXlsxStruct->dataStructList.append(xlsxDataStruct);
    }
    // 提交到Excel写入线程
    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);
    
    qDebug() << "底噪数据已保存到Excel文件";
    return true;
}

