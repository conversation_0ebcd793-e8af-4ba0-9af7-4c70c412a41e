#ifndef CAGEWIDGET_H
#define CAGEWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-06-24
  * Description: 年龄 垂直布局和水平布局
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QComboBox>
#include <QBoxLayout>

#include "CLineEdit.h"

class CAgeWidget : public QWidget
{
    Q_OBJECT
public:
    CAgeWidget(int iSpacing1, int iSpacing2,
               QBoxLayout::Direction eDirection = QBoxLayout::LeftToRight, QWidget *parent = nullptr);
    virtual ~CAgeWidget();

public:
    void ResetLabelSize(int iWidth, int iHeight);
    void ResetLineEditSize(int iWidth, int iHeight);
    void ResetComboBoxSize(int iWidth, int iHeight);

    QString GetAge() const;
    void SetAge(const QString &strAge);

private:
    QLabel *m_pLabel;
    CLineEdit *m_pLineEdit;
    QComboBox *m_pComboBox;
};

class CVAgeWidget : public CAgeWidget
{
    Q_OBJECT
public:
    CVAgeWidget(int iSpacing1 = 5, int iSpacing2 = 12, QWidget *parent = nullptr);
    virtual ~CVAgeWidget();
};

class CHAgeWidget : public CAgeWidget
{
    Q_OBJECT
public:
    CHAgeWidget(int iSpacing1 = 16, int iSpacing2 = 12, QWidget *parent = nullptr);
    virtual ~CHAgeWidget();
};

#endif // CAGEWIDGET_H
