QLabel
{
    color: #353E4E;
    font-size: 24px;
    font-weight: 400;
    font-family: "Source Han Sans CN";
    border: 0px solid red;
}
QLabel#StatusLabel
{
    font-size: 22px;
    font-weight: 500;
}

QLabel#TitleIconLabel
{
   border-radius: 3px;
   background-color: #3D78E5;
}

QLabel#TitleTextLabel
{
   color: #353E4E;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
}

QLabel#LineLabel
{
   background-color: #D6DAEC;
}

QLabel#BackgroundLabel
{
   border-radius: 32px;
   background-color: #FFF;
}

/*一级标题*/
QLabel#SysTitleLabel1
{
    color: #6B788F;
    font-size: 24px;
    font-weight: 400;
    font-family: "Source Han Sans CN";
}

/*一级标题*/
QLabel#SysTitleLabel2
{
    color: #353E4E;
    font-size: 24px;
    font-weight: 500;
    font-family: "Source Han Sans CN";
}

QLineEdit
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    padding-left: 0px;
    border-radius: 28px;
    border: 0px solid #A1ABBB;
    background-color: #F3F8FF;
}

QListWidget
{    
    color: #333;
    font-family: "Source Han Sans CN";
    font-size: 20px;
    outline: none;
    border: 0px solid #F3F8FF;
    border-radius: 10px;
    background-color: #F3F8FF;
}

QListWidget::item
{
    margin: 10px 0px 0px 0px;
    color: #333;
    font-family: "Source Han Sans CN";
    font-size: 20px;
    outline: none;
    border-radius: 10px;
    background-color: #F3F8FF;
}

QListWidget::Item:selected {
    border: 1px solid #F3F8FF;
    background-color: #FFF;
}
QListWidget::Item:selected:!active {
    border: 0px solid #D6DFE9;
    background-color: #F3F8FF;
}

QGroupBox
{
   border-radius: 24px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #DEE8FB, stop: 0.15 #FFF);
}
QPushButton
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0, stop: 0 #8490FF, stop: 1 #3D78E5);
}
QPushButton#ConnectBtn
{
   font-size: 23px;
   border-radius: 25px;
}
QPushButton:focus{outline: none;}

QPushButton:pressed
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #6670c7, stop: 1 #3361b8);
}

QPushButton#CancelBtn
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 2px solid #3D78E5;
   background-color: #FFF;
}

QPushButton#CancelBtn:hover
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 3px solid #3D78E5;
   background-color: #FFF;
}

QPushButton#CancelBtn:pressed
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 4px solid #3D78E5;
   background-color: #FFF;
}

QScrollBar:vertical
{
    width: 30px;
    background: #F0F0F0;
    padding-top: 30px;
    padding-bottom: 30px;
}

QScrollBar::handle:vertical
{
    width: 30px;
    background: #B6B6B6;
    min-height: 35px;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical
{
    height: 0px;
}
