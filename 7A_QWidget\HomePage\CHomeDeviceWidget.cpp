#include "CHomeDeviceWidget.h"
#include <QBoxLayout>

#include "CConfigJson.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "HistoryPage/CHistoryDetailWidget.h"
#include "CRunTest.h"

CHomeDeviceWidget::CHomeDeviceWidget(QWidget *parent) : QWidget(parent)
{
    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalUpdateFLData,
            this, &CHomeDeviceWidget::SlotUpdateFLData);
    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalUpdateMeltingFLData,
            this, &CHomeDeviceWidget::SlotUpdateMeltingFLData);
    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalTimingTestStart,
            this, &CHomeDeviceWidget::SlotTestStart);

    m_iDevNum = 1;
    m_iItemNum = 8;
    CPublicConfig::GetInstance()->GetDevItemNum(m_iDevNum, m_iItemNum);

    m_pGroupBox = new QGroupBox(this);
    m_pGroupBox->setGeometry(0, 0, 1684, 958);
    m_pGroupBox->setObjectName("BackgroundGroup");

    m_pDetailWidget = new CHistoryDetailWidget(tr("测试详情"), false);

    connect(CRunTest::GetInstance(), &CRunTest::SignalUpdateItemStatus,
            this, &CHomeDeviceWidget::SlotUpdateItemStatus);

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalAppStartEnd,
            this, &CHomeDeviceWidget::SlotAppStartEnd);

    _Init_1x8();
}

void CHomeDeviceWidget::SlotAppStartEnd()
{
    m_pDetailWidget->setParent((QWidget*)gk_pMainWindow);
    m_pDetailWidget->setVisible(false);
}

void CHomeDeviceWidget::SetCardSampleInfo(int iMachineID, const SCardInfoStruct &sCardInfo, const SSampleInfoStruct &sSampleInfo)
{
    if(iMachineID < 0 || iMachineID >= m_pDevItemList.size())
        return;

    m_pDevItemList.at(iMachineID)->SetCardSampleInfo(sCardInfo, sSampleInfo);
}

void CHomeDeviceWidget::SlotUpdateFLData(int iMachineID, const QList<QMap<double, double> > &dFLMap)
{
    if(iMachineID < 0 || iMachineID >= m_pDevItemList.size())
        return;

    qDebug()<<QString("%1#更新荧光数据").arg(iMachineID + 1)<<dFLMap.size();
    _SetIndexFlValue(iMachineID,dFLMap);

    // 是不是当前现实的详情？
    if(iMachineID == m_iMachinedId && m_pDetailWidget->isVisible())
    {
        m_pDetailWidget->SetFLDataMap(iMachineID,dFLMap);
    }

}

void CHomeDeviceWidget::SlotUpdateMeltingFLData(int iMachineId, const QList<double>& dTempList, const QList<QMap<double, double> > &dFLMap)
{
    if(iMachineId < 0 || iMachineId >= m_pDevItemList.size())
        return;


    qDebug()<<QString("%1#更新熔解曲线荧光数据").arg(iMachineId + 1)<<dFLMap.size();
    _SetIndexMeltingFlValue(iMachineId,dTempList,dFLMap);

    if(iMachineId == m_iMachinedId && m_pDetailWidget->isVisible())
    {
        m_pDetailWidget->SetMeltingFLDataMap(iMachineId,dTempList,dFLMap);
    }

}

//开始测试后清空测试详情里的数据
void CHomeDeviceWidget::SlotTestStart(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= m_pDevItemList.size())
        return;

    qDebug()<<QString("%1#清空测试详情数据").arg(iMachineID + 1);
    m_pDevItemList.at(iMachineID)->ClearData();
}

void CHomeDeviceWidget::SlotUpdateItemStatus(int iMachineID, DeviceStatus eStatus)
{
    Q_UNUSED(eStatus);
    SRunningInfoStruct &sRunInfo = CRunTest::GetInstance()->GetRunInfoStruct(iMachineID);

    if(iMachineID == m_iMachinedId && m_pDetailWidget->isVisible())
    {
        m_pDetailWidget->UpdateInfo(sRunInfo.sResultInfo.iHistoryID);
    }
}

void CHomeDeviceWidget::SlotDetailWidgetShow(int iMachinedID)
{
    int iHistoryID = CRunTest::GetInstance()->GetRunInfoStruct(iMachinedID).sResultInfo.iHistoryID;

    m_iMachinedId = iMachinedID;
    m_pDetailWidget->SetFLDataMap(iMachinedID,_GetIndexFlValue(iMachinedID));
    m_pDetailWidget->SetMeltingFLDataMap(iMachinedID,_GetIndexMeltingTempValue(iMachinedID),_GetIndexMeltingFlValue(iMachinedID));
    m_pDetailWidget->SetHistoryID(iHistoryID);
    if(m_pDetailWidget->isVisible())
    {
        m_pDetailWidget->close();
    }
    m_pDetailWidget->raise();
    m_pDetailWidget->show();
    m_pDetailWidget->activateWindow();
}

void CHomeDeviceWidget::SoltClearFlData(int iMachinedID)
{
    _ClearIndexFlValue(iMachinedID);
    _ClearIndexMeltingValue(iMachinedID);
    if(iMachinedID == m_iMachinedId)
    {
        m_pDetailWidget->ClearData();
        m_pDetailWidget->ClearFL();
    }
}

void CHomeDeviceWidget::_Init_1x8()
{
    QString strQssPath;
    SDevParamsStruct sDevParams;

    if(1 == m_iItemNum)
    {
        sDevParams.iItemWidth = 780;
        sDevParams.iItemHeight = 600;

        sDevParams.iTitleHeight = 80;
        sDevParams.iIndexWidth = 100;

        sDevParams.iNameWidth = 160;
        sDevParams.iValueWidth = 540;
        sDevParams.iLabelHeight = 50;
        sDevParams.iAddSpacing = 30;
        sDevParams.iLabelSpacing = 20;

        sDevParams.iActIconSize = 32;
        sDevParams.iActSpacing = 10;

        strQssPath = ":/qss/qss/home/<USER>";
    }
    else if(2 == m_iItemNum)
    {
        sDevParams.iItemWidth = 700;
        sDevParams.iItemHeight = 600;

        sDevParams.iTitleHeight = 80;
        sDevParams.iIndexWidth = 100;

        sDevParams.iNameWidth = 160;
        sDevParams.iValueWidth = 460;
        sDevParams.iLabelHeight = 50;
        sDevParams.iAddSpacing = 30;
        sDevParams.iLabelSpacing = 20;

        sDevParams.iActIconSize = 32;
        sDevParams.iActSpacing = 10;

        strQssPath = ":/qss/qss/home/<USER>";
    }
    else if(3 == m_iItemNum)
    {
        sDevParams.iItemWidth = 420;
        sDevParams.iItemHeight = 500;

        sDevParams.iTitleHeight = 60;
        sDevParams.iIndexWidth = 75;

        sDevParams.iNameWidth = 130;
        sDevParams.iValueWidth = 250;
        sDevParams.iLabelHeight = 40;
        sDevParams.iAddSpacing = 30;
        sDevParams.iLabelSpacing = 10;

        sDevParams.iActIconSize = 24;
        sDevParams.iActSpacing = 5;

        strQssPath = ":/qss/qss/home/<USER>";
    }
    else if(4 == m_iItemNum)
    {
        sDevParams.iItemWidth = 390;
        sDevParams.iItemHeight = 450;

        sDevParams.iTitleHeight = 54;
        sDevParams.iIndexWidth = 66;

        sDevParams.iNameWidth = 115;
        sDevParams.iValueWidth = 220;
        sDevParams.iLabelHeight = 40;
        sDevParams.iAddSpacing = 30;
        sDevParams.iLabelSpacing = 10;

        sDevParams.iActIconSize = 24;
        sDevParams.iActSpacing = 5;

        strQssPath = ":/qss/qss/home/<USER>";
    }
    else if(5 == m_iItemNum || 6 == m_iItemNum || 7 == m_iItemNum || 8 == m_iItemNum)
    {
        sDevParams.iItemWidth = 390;
        sDevParams.iItemHeight = 384;

        sDevParams.iTitleHeight = 54;
        sDevParams.iIndexWidth = 66;

        sDevParams.iNameWidth = 115;
        sDevParams.iValueWidth = 220;
        sDevParams.iLabelHeight = 40;
        sDevParams.iAddSpacing = 20;
        sDevParams.iLabelSpacing = 10;

        sDevParams.iActIconSize = 24;
        sDevParams.iActSpacing = 5;

        strQssPath = ":/qss/qss/home/<USER>";
    }

    LoadQSS(this, strQssPath);

    for(int i = 0 ; i < 8 ; i++)
    {
        m_dFLMapList[i] = QList<QMap<double, double>>();
        m_dMeltingFLMapList[i] = QList<QMap<double, double>>();
        m_dMeltingTempMapList[i] = QList<double>();
    }

    for(int i=0; i<m_iItemNum; i++)
    {
        CHomeDevItemWidget *pItemWidget = new CHomeDevItemWidget(i, sDevParams);
        connect(pItemWidget, &CHomeDevItemWidget::SignalCreateTest, this, &CHomeDeviceWidget::SignalCreateTest);
        connect(pItemWidget, &CHomeDevItemWidget::SingalClearFlData, this, &CHomeDeviceWidget::SoltClearFlData);
        connect(pItemWidget, &CHomeDevItemWidget::SingalDetailWidgetShow, this, &CHomeDeviceWidget::SlotDetailWidgetShow);

        m_pDevItemList.push_back(pItemWidget);
    }

    if(m_iItemNum <= 4)
    {
        QHBoxLayout *pLayout = new QHBoxLayout;
        pLayout->setMargin(0);
        pLayout->setSpacing(0);
        pLayout->addStretch(1);
        for(int i=0; i<m_pDevItemList.size(); i++)
        {
            pLayout->addWidget(m_pDevItemList.at(i), 0, Qt::AlignVCenter);
            pLayout->addStretch(1);
        }
        m_pGroupBox->setLayout(pLayout);
        return;
    }

    QHBoxLayout *pUpLayout = new QHBoxLayout;
    pUpLayout->setMargin(0);
    pUpLayout->setSpacing(0);
    pUpLayout->addSpacing(20);
    pUpLayout->addWidget(m_pDevItemList.at(0));
    pUpLayout->addStretch(1);
    pUpLayout->addWidget(m_pDevItemList.at(1));
    pUpLayout->addStretch(1);
    pUpLayout->addWidget(m_pDevItemList.at(2));
    pUpLayout->addStretch(1);
    pUpLayout->addWidget(m_pDevItemList.at(3));
    pUpLayout->addSpacing(20);

    QHBoxLayout *pDownLayout = new QHBoxLayout;
    pDownLayout->setMargin(0);
    pDownLayout->setSpacing(0);
    pDownLayout->addSpacing(20);
    if(5 == m_pDevItemList.size())
    {
        pDownLayout->addWidget(m_pDevItemList.at(4));
        pDownLayout->addStretch(1);
    }
    else if(6 == m_pDevItemList.size())
    {
        pDownLayout->addWidget(m_pDevItemList.at(4));
        pDownLayout->addSpacing(28);
        pDownLayout->addWidget(m_pDevItemList.at(5));
        pDownLayout->addStretch(1);
    }
    else if(7 == m_pDevItemList.size())
    {
        pDownLayout->addWidget(m_pDevItemList.at(4));
        pDownLayout->addSpacing(28);
        pDownLayout->addWidget(m_pDevItemList.at(5));
        pDownLayout->addSpacing(28);
        pDownLayout->addWidget(m_pDevItemList.at(6));
        pDownLayout->addStretch(1);
    }
    else
    {
        pDownLayout->addWidget(m_pDevItemList.at(4));
        pDownLayout->addStretch(1);
        pDownLayout->addWidget(m_pDevItemList.at(5));
        pDownLayout->addStretch(1);
        pDownLayout->addWidget(m_pDevItemList.at(6));
        pDownLayout->addStretch(1);
        pDownLayout->addWidget(m_pDevItemList.at(7));
        pDownLayout->addSpacing(20);
    }

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addLayout(pUpLayout);
    pLayout->addStretch(1);
    pLayout->addLayout(pDownLayout);
    pLayout->addStretch(1);
    m_pGroupBox->setLayout(pLayout);
}

void CHomeDeviceWidget::_SetIndexFlValue(int index,const QList<QMap<double,double>>& FlMapList)
{
    if (index < 0 || index >= 8)
    {
        return;
    }
    for (int i = 0; i < 8;i++)
    {
        if(i == index)
        {
            m_dFLMapList[i] = FlMapList;
            break;
        }
    }
}

QList<QMap<double, double>> CHomeDeviceWidget::_GetIndexFlValue(int index)
{
    if (index < 0 || index >= 8)
    {
        return QList<QMap<double, double>>();
    }
    for (int i = 0; i < 8;i++)
    {
        if(i == index)
        {
            return m_dFLMapList[i];
            break;
        }
    }
    return QList<QMap<double, double>>();
}

QList<double> CHomeDeviceWidget::_GetIndexMeltingTempValue(int index)
{
    if (index < 0 || index >= 8)
    {
        return QList<double>();
    }
    for (int i = 0; i < 8;i++)
    {
        if(i == index)
        {
            return m_dMeltingTempMapList[i];
            break;
        }
    }
    return QList<double>();
}

QList<QMap<double, double> > CHomeDeviceWidget::_GetIndexMeltingFlValue(int index)
{
    if (index < 0 || index >= 8)
    {
        return QList<QMap<double, double>>();
    }
    for (int i = 0; i < 8;i++)
    {
        if(i == index)
        {
            return m_dMeltingFLMapList[i];
            break;
        }
    }
    return QList<QMap<double, double>>();
}

void CHomeDeviceWidget::_ClearIndexFlValue(int index)
{
    if (index < 0 || index >= 8)
    {
        return;
    }
    for (int i = 0; i < 8;i++)
    {
        if(i == index)
        {
            m_dFLMapList[i].clear();
            break;
        }
    }
}

void CHomeDeviceWidget::_ClearIndexMeltingValue(int index)
{
    if (index < 0 || index >= 8)
    {
        return;
    }
    for (int i = 0; i < 8;i++)
    {
        if(i == index)
        {
            m_dMeltingFLMapList[i].clear();
            m_dMeltingTempMapList[i].clear();
            break;
        }
    }
}

void CHomeDeviceWidget::_SetIndexMeltingFlValue(int index, const QList<double> &dTempList, const QList<QMap<double, double> > &meltingFlMapList)
{
    if (index < 0 || index >= 8)
    {
        return;
    }
    for (int i = 0; i < 8;i++)
    {
        if(i == index)
        {
            m_dMeltingFLMapList[i] = meltingFlMapList;
            m_dMeltingTempMapList[i] = dTempList;
            break;
        }
    }
}
