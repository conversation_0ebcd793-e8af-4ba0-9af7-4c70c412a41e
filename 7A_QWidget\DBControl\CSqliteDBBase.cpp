#include "CSqliteDBBase.h"
#include <QDebug>
#include <QFile>
#include <QFileInfo>
#include <QSqlQuery>
#include <QSqlError>
#include <QSqlRecord>

#include "PublicConfig.h"
#include "PublicFunction.h"

CSqliteDBBase::CSqliteDBBase(QString strDBPath, QString strConnectName)
    : m_strDBPath(strDBPath)
    , m_strConnectName(strConnectName)
    , m_strDefaultErr("database is not opened")
    , m_strLastError(m_strDefaultErr)
{
    QFileInfo info(m_strDBPath);
    m_strDBName = info.fileName();

    _OpenDataBase();
}

CSqliteDBBase::~CSqliteDBBase()
{
    _CloseDataBase();
}

QString CSqliteDBBase::GetLastErrorString() const
{
    return m_strLastError;
}

bool CSqliteDBBase::_OpenDataBase()
{
    if(m_strDBPath.isEmpty())
        return false;

    QMutexLocker locker(&m_mutex);
    m_qSqldb = QSqlDatabase::addDatabase("QSQLITE", m_strConnectName);
    m_qSqldb.setDatabaseName(m_strDBPath);
    m_qSqldb.setPassword(m_strConnectName);
    m_qSqldb.open();
    if(!m_qSqldb.isOpen())
    {
        qDebug()<<"数据库打开失败:"<<m_strDBPath<<m_qSqldb.lastError().text();
        m_qSqldb.open();
    }
    return m_qSqldb.isOpen();
}

bool CSqliteDBBase::_CloseDataBase()
{
    QMutexLocker locker(&m_mutex);
    if(m_qSqldb.isOpen())
    {
        qDebug()<<"close db:"<<m_qSqldb.databaseName();
        m_qSqldb.close();
        m_qSqldb = QSqlDatabase();
        QSqlDatabase::removeDatabase(m_strConnectName);       
    }
    return true;
}

bool CSqliteDBBase::ReOpenDataBaseFromUDisk()
{
    _CloseDataBase();

    QString strNewDBPath = GetUDiskUpdateDir() + m_strDBName;
    bool bCopy = CopyQFile(m_strDBPath, strNewDBPath);
    qDebug()<<"导入DB:"<<m_strDBPath<<strNewDBPath<<bCopy;
    if(!bCopy)
        return false;

    return _OpenDataBase();
}

bool CSqliteDBBase::_ExecuteDB(const QString &strCmd)
{
    m_strLastError.clear();
    QMutexLocker locker(&m_mutex);
    if(!m_qSqldb.isOpen())
    {
        m_strLastError = m_strDefaultErr;
        return false;
    }

    m_strLastError.clear();
    QSqlQuery sql(m_qSqldb);
    bool bExe = sql.exec(strCmd);
    if(!bExe)
        m_strLastError = sql.lastError().text();
    qDebug()<<strCmd<<bExe<<m_strLastError;
    return bExe;
}

bool CSqliteDBBase::_ExecuteDB(const QStringList &strCmdList)
{
    m_strLastError.clear();
    QMutexLocker locker(&m_mutex);
    if(!m_qSqldb.isOpen())
    {
        m_strLastError = m_strDefaultErr;
        return false;
    }

    m_strLastError.clear();
    QSqlQuery sql(m_qSqldb);
    for(int i=0; i<strCmdList.size(); i++)
    {
        QString strCmd = strCmdList.at(i);
        bool bExe = sql.exec(strCmd);
        if(!bExe)
            m_strLastError = sql.lastError().text();
        qDebug()<<strCmd<<bExe<<m_strLastError;
        if(!bExe)
            return false;
    }
    return true;
}

bool CSqliteDBBase::_QueryDB(const QString &strCmd, QList<QStringList> &strDataList)
{
    m_strLastError.clear();
    QMutexLocker locker(&m_mutex);
    if(!m_qSqldb.isOpen())
    {
        m_strLastError = m_strDefaultErr;
        return false;
    }

    QSqlQuery sql(m_qSqldb);
    bool bExe = sql.exec(strCmd);
    if(!bExe)
        m_strLastError = sql.lastError().text();
    qDebug()<<strCmd<<bExe<<m_strLastError;
    if(!bExe)
        return false;

    strDataList.clear();
    int iCnt = sql.record().count();
    while(sql.next())
    {
        QStringList one;
        for(int i=0; i<iCnt; i++)
            one.push_back(sql.value(i).toString());
        strDataList.push_back(one);
    }

    return true;
}

bool CSqliteDBBase::_QueryDB(const SQueryParamsStruct &params, QList<QStringList> &strDataList)
{
    m_strLastError.clear();
    QMutexLocker locker(&m_mutex);
    if(!m_qSqldb.isOpen())
    {
        m_strLastError = m_strDefaultErr;
        return false;
    }

    if(params.strTypeList.isEmpty())
        return false;

    QString strCmd = QString("select ");
    for(int i=0; i<params.strTypeList.size(); i++)
    {
        strCmd += QString("%1,").arg(params.strTypeList.at(i));
    }
    if(strCmd.endsWith(","))
    {
        int size = strCmd.size();
        strCmd = strCmd.mid(0,size-1);
    }
    strCmd += QString(" from %1").arg(params.strTable);

    if(!params.strCondiMap.isEmpty())
    {
        strCmd += " where";
        for(auto it=params.strCondiMap.constBegin(); it!=params.strCondiMap.end(); it++)
        {
            strCmd += QString(" %1='%2' and").arg(it.key()).arg(it.value());
        }
        if(strCmd.endsWith("and"))
        {
            int size = strCmd.size();
            strCmd = strCmd.mid(0,size-3);
        }
    }

    QSqlQuery sql(m_qSqldb);
    bool bExe = sql.exec(strCmd);
    if(!bExe)
        m_strLastError = sql.lastError().text();
    qDebug()<<strCmd<<bExe<<m_strLastError;
    if(!bExe)
        return false;

    strDataList.clear();
    int iCnt = sql.record().count();
    while(sql.next())
    {
        QStringList one;
        for(int i=0; i<iCnt; i++)
            one.push_back(sql.value(i).toString());
        strDataList.push_back(one);
    }

    return true;
}

bool CSqliteDBBase::_DeleteTable(QString strTableName)
{
    QStringList strCmdList = {QString("delete from %1").arg(strTableName),
                              QString("update sqlite_sequence set seq = 0 where name = '%1'").arg(strTableName),
                              QString("delete from sqlite_sequence where name = '%1'").arg(strTableName),
                              QString("delete from sqlite_sequence")};
    return _ExecuteDB(strCmdList);
}

QString CSqliteDBBase::_GetFirstValue(const QList<QStringList> &strList)
{
    QString strValue;
    if(!strList.isEmpty())
    {
        QStringList first = strList.at(0);
        if(!first.isEmpty())
            strValue = first.at(0);
    }
    return strValue;
}

QStringList CSqliteDBBase::_GetColumnValueList(const QList<QStringList> &strList)
{
    QStringList strValueList;
    for(int i=0; i<strList.size(); i++)
    {
        QStringList one = strList.at(i);
        if(one.isEmpty())
            continue;
        strValueList.push_back(one.at(0));
    }
    return strValueList;
}

QStringList CSqliteDBBase::_GetRowValueList(const QList<QStringList> &strList)
{
    QStringList strValueList;
    if(!strList.isEmpty())
        strValueList = strList.first();
    return strValueList;
}
