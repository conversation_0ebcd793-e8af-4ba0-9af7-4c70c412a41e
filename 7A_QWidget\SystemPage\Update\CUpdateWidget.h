﻿#ifndef CUPDATEWIDGET_H
#define CUPDATEWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-15
  * Description: 升级
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QMap>
#include <QTimer>
#include <QWidget>
#include <QCheckBox>
#include <QPushButton>
#include "CProgressBar.h"
#include "CHLabelTitleWidget.h"
#include "SystemPage/CSysFirstTitleWidget.h"

#include "CmdBus/CCmdBase.h"

class CUpdateWidget : public QWidget, public CCmdBase
{
    Q_OBJECT
public:
    explicit CUpdateWidget(QWidget *parent = nullptr);

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData) override;

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual void hideEvent(QHideEvent *pEvent) override;

signals:
    void SignalReturn();
    void SignalCopyError();
    void SignalFileMissing();
    void SignalCodeError();
    void SignalExtarctEnd();

private slots:
    void _SlotUpdateBtn();
    void _SlotRestoreBtn();
    void _SlotCopyError();
    void _SlotFileMissing();
    void _SlotCodeError();
    void _SlotExtarctEnd();
    void _SlotUpdateTimeout();

private:
    QString _CalcUpdateZipCode(QString strZipFilePath);
    void _Thread2ExtarctZip(QString strLastZipName, QString strLastZipPath);
    void _UpdateUpperMachinePart();
    void _NextUpdateLowerFirm();
    void _UpdateFirmReply(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData);
    void _UpdateFirmEnd(int iMachineID, int iMethodID, int iResult);
    void _UpdateProgressEnd();
    void _ShowResultTable();

private:
    void _InitWidget();
    void _InitLayout();

private:
    enum {eUpdate_FL=0, eUpdate_PCR=1, eUpdate_Median=2};

    bool m_bShow;
    bool m_bUpdateStart;
    int m_iUpdateType;    
    QString m_strUpdateVersion;
    QString m_strAllVersionPath;
    QString m_strCurrentVersion;
    QString m_strCurrentDir;
    QString m_strBackupDir;
    QString m_strAppDir;
    QString m_strLibDir;
    QString m_strUpdateDir;
    QString m_strUpdateZipPath;    
    QMap<int, QList<bool>> m_iNeedUpdateMap; //是否需要升级 key: iMachineID; value: {FL、PCR、中位机}
    QMap<int, QStringList> m_iUpdateResultMap; //下位机各模块升级结果  key: iMachineID; value: {FL、PCR、中位机}
    QByteArray m_byteFLFileData, m_bytePCRFileData, m_byteMedianFileData;

private:
    CSysFirstTitleWidget *m_pCSysTtileLabelWidget;
    QLabel *m_pBackgroundLabel;

    CHLabelTitleWidget *m_pCHLabelTitleWidget;

    QLabel *m_pSoftInfoLabel;
    QLabel *m_pSoftNameLabel;
    QLabel *m_pSoftModelLabel;
    QLabel *m_pReleaseVerLabel;
    QLabel *m_pTotalVerLabel;
    QLabel *m_pManufacturerLabel;
    QLabel *m_pAddressNameLabel;
    QLabel *m_pAddressValueLabel;
    QLabel *m_pGrayLabel;

    QPushButton *m_pReturnBtn, *m_pUpdateBtn, *m_pRestoreBtn;
    QCheckBox *m_pDBCheckBox;

    QTimer *m_pUpdateTimer;
    CProgressBar *m_pProgressBar;
};

#endif // CUPDATEWIDGET_H
