#include "CTextBrowser.h"
#include <QDateTime>

CTextBrowser::CTextBrowser()
{
    this->setPlaceholderText(tr("双击清空输出文字"));
}

CTextBrowser::~CTextBrowser()
{

}

void CTextBrowser::AppendLog(const QString &strLog)
{
    QString strCurrentTime = QDateTime::currentDateTime().toString("[yyyy-MM-dd hh:mm:ss]");
    append(strCurrentTime + " " + strLog);
}

void CTextBrowser::mouseDoubleClickEvent(QMouseEvent *pEvent)
{
    this->clear();
    QTextBrowser::mouseDoubleClickEvent(pEvent);
}
