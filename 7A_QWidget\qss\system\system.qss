QTextBrowser
{
    color: #000;
    font-family: "Source Han Sans CN";
    font-size: 25px;
    border: 0px solid #CCDEEE;
    border-radius: 10px;
    border-top: 1px solid #CCDEEE;
}
QLabel
{
    color: #353E4E;
    font-size: 24px;
    font-weight: 400;
    font-family: "Source Han Sans CN";
}

QLabel#BackgroundLabel
{
   border-radius: 32px;
   background-color: #FFF;
}

/*一级标题*/
QLabel#SysTitleLabel1
{
    color: #6B788F;
    font-size: 24px;
    font-weight: 400;
    font-family: "Source Han Sans CN";
}

/*一级标题*/
QLabel#SysTitleLabel2
{
    color: #353E4E;
    font-size: 24px;
    font-weight: 500;
    font-family: "Source Han Sans CN";
}

QLabel:disabled
{
    color: #FFF;
    font-size: 32px;
    font-weight: bold;
    font-family: "Source Han Sans CN";
}

QLabel#TitleIconLabel
{
   border-radius: 3px;
   background-color: #3D78E5;
}

QLabel#TitleTextLabel
{
   color: #353E4E;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
}

QLineEdit
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    padding-left: 20px;
    border-radius: 28px;
    border: 0px solid #A1ABBB;
    background-color: #F3F8FF;
}

QLineEdit#LockLineEdit
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    padding-left: 0px;
    border-radius: 28px;
    border: 0px solid #A1ABBB;
    background-color: #F3F8FF;
}

QComboBox
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    padding-left: 20px;
    border-radius: 28px;
    background-color: #F3F8FF;
}

QComboBox::drop-down
{
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 60px;
    font-weight: 500;
    border-left: 0px solid red;
}
QComboBox::down-arrow
{
    width: 32px;
    height: 32px;
    image: url(:/image/ico/login/commod.png);
    padding: 0px 20px 0px 0px;
}
QComboBox QAbstractItemView
{
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
    selection-background-color: #248CEB;
}
QComboBox QAbstractItemView::item
{
    min-height: 56px;/*下拉列表的行高，也可以看做行距*/
    color: #6B788F;
    font-size: 24px;
    font-family: "Source Han Sans CN";
}

QRadioButton
{
    color: #353E4E;
    font-family: "Source Han Sans CN";
    font-size: 24px;   
}
QRadioButton:focus{outline: none;}
QRadioButton::indicator {
    width: 28px;
    height: 28px;
}
QRadioButton::indicator::unchecked {
    image: url(:/image/ico/system/radio-off.png);
}
QRadioButton::indicator::checked {
    image: url(:/image/ico/system/radio-on.png);
}

QPushButton
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #8490FF, stop: 1 #3D78E5);
}
QPushButton:pressed
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #6670c7, stop: 1 #3361b8);
}
QPushButton:disabled
{
   color: #888;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
}

QPushButton#CancelBtn
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 2px solid #3D78E5;
   background-color: #FFF;
}
QPushButton#CancelBtn:hover
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 3px solid #3D78E5;
   background-color: #FFF;
}
QPushButton#CancelBtn:pressed
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 4px solid #3D78E5;
   background-color: #FFF;
}

/*二级标题*/
QPushButton#SysTitleBtn
{
   color: #6B788F;
   font-size: 26px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-left: 0px solid #3D78E5;
   border-top: 0px solid #3D78E5;
   border-right: 0px solid #3D78E5;
   border-bottom: 0px solid #3D78E5;
   border-radius: 0px;
   background-color: transparent;
}

/*二级标题*/
QPushButton#SysTitleBtn:disabled
{
   color: #3D78E5;
   font-size: 26px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-left: 0px solid #3D78E5;
   border-top: 0px solid #3D78E5;
   border-right: 0px solid #3D78E5;
   border-bottom: 4px solid #3D78E5;
   border-radius: 0px;
   background-color: transparent;
}

QCheckBox
{
   color: #6B788F;
   font-size: 24px;
   font-family: "Source Han Sans CN";
   border: 0px solid red;
}
QCheckBox::indicator
{
   width: 40px;
   height: 40px;
   /* subcontrol-position:right  right;*/
}
QCheckBox::indicator::unchecked
{
   image: url(:/image/ico/system/printer/uncheck.png);
}
QCheckBox::indicator::checked
{
   image: url(:/image/ico/system/printer/check.png);
}
QCheckBox:focus{outline: none;}

QGroupBox
{
   border-radius: 32px;
   background-color: #FFF;
}

QSlider::groove:horizontal
{
    height: 22px;
    background: #D6E0EB;
    border: 1px solid #D6E0EB;
    border-radius: 10px;
    padding-left:-1px;
    padding-right:-1px;
}
/*已经划过的地方*/
QSlider::sub-page:horizontal
{
    height: 10px;
    /*background: #3DA2FC;*/
    background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
        stop: 0 #8490FF, stop: 1 #3D78E5);
    border: 1px solid #DDE4EB;
    border-radius: 10px;
}
/*// 还没划过的地方*/
QSlider::add-page:horizontal
{
    height: 10px;
    background: #F3F8FF;
    border: 0px solid #777;
    border-radius: 10px;
}
/*// 设置滑动键*/
QSlider::handle:horizontal
{
    border-radius: 2px;
    border: 0px ;
    border-image: url(:/image/ico/system/slider.png);
    width: 24px;
    height: 50px;
    margin: -2px -2px -2px -2px;
}
/*// 滑动键鼠标悬停*/
QSlider::handle:horizontal:hover
{
    width: 11px;
    background: qradialgradient
    (
        spread:pad, cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
        stop:0.6 #2A8BDA, stop:0.778409 rgba(255, 255, 255, 255)
    );
    margin-top: -3px;
    margin-bottom: -3px;
    border-radius: 5px;
}
