#include "CMotorGXIO.h"
#include <QBoxLayout>
#include <QGridLayout>
#include <QPainter>
#include <QPalette>

CMotorGXIO::CMotorGXIO(QWidget *parent) : QWidget(parent)
{
    Register2Map(Method_GXIO);

    m_pReadTimer = new QTimer(this);
    connect(m_pReadTimer, &QTimer::timeout, this, &CMotorGXIO::_SlotReadTimeout);

    _InitWidget();
}

CMotorGXIO::~CMotorGXIO()
{
    UnRegister2Map(Method_GXIO);
}

void CMotorGXIO::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if(Method_GXIO == iMethodID && 0 == iResult && m_pMachineComboBox->GetCurrentIndex() == iMachineID)
    {
        QVariantList qVarList = qVarData.toList();
        if(qVarList.isEmpty())
            return;

        uint iValue = qVarList.at(0).toUInt();
        m_pValueLabel->SetValueLabelText(QString::number(iValue));

        QMap<int,int> map;
        for(int pos=0; pos<32; pos++)
        {
            if(iValue & (1 << pos))
                map.insert(pos, 1);
            else
                map.insert(pos, 0);
        }
        for(int i=0; i<m_pLabelList.size(); i++)
        {
            m_pLabelList.at(i)->SetRedColor(map.value(i));
        }
    }
}

void CMotorGXIO::_SlotReadTimeout()
{
    QString strCmd = GetJsonCmdString(Method_GXIO);
    SendJsonCmd(m_pMachineComboBox->GetCurrentIndex(), Method_GXIO, strCmd);
}

void CMotorGXIO::_SlotStartChecked(bool bChecked)
{
    if(bChecked)
    {
        int time = m_pTimeLineEdit->GetLineEditText().toInt();
        if(time <= 0)
        {
            time = 1000;
            m_pTimeLineEdit->SetLineEditText("1000");
        }
        m_pReadTimer->start(time);
    }
    else
    {
        m_pReadTimer->stop();
    }
}

void CMotorGXIO::_InitWidget()
{
    QStringList strLeftList = {tr("光学计数光耦"), tr("光学复位光耦"), tr("PCR复位光耦"), tr("PCR压紧光耦"),
                               tr("超声左右复位光耦"), tr("超声洗脱腔光耦"), tr("顶针阀1复位光耦"), tr("7号光耦"),tr("顶针阀2复位光耦")};
    QStringList strMidList = { tr("9号光耦"),tr("顶针阀3复位光耦"), tr("卡盒插入检测"), tr("顶针阀4复位光耦"),
                               tr("13号光耦"),tr("超声压紧复位光耦"),tr("提取升降复位光耦"),tr("提取升降磁珠光耦"), tr("提取升降加热光耦")};
    QStringList strRightList = {tr("气嘴复位光耦"),tr("气嘴压紧光耦"),tr("刺破复位光耦"), tr("刺破1位置光耦"),
                                tr("刺破2位置光耦"),tr("刺破3位置光耦"),tr("刺破4位置光耦"),tr("柱塞泵复位光耦"),tr("26号光耦")};

    QGridLayout *pGridLayout = new QGridLayout;
    pGridLayout->setContentsMargins(40, 0, 40, 0);
    pGridLayout->setHorizontalSpacing(40);
    pGridLayout->setVerticalSpacing(10);

    QList<CColorLabel *> pLeftList, pMidList, pRightList;
    for(int i=0; i<strLeftList.size(); i++)
    {
        CColorLabel *pLeftLabel = new CColorLabel(strLeftList.at(i));
        pLeftList.push_back(pLeftLabel);

        CColorLabel *pMidLabel = new CColorLabel(strMidList.at(i));
        pMidList.push_back(pMidLabel);

        CColorLabel *pRightLabel = new CColorLabel(strRightList.at(i));
        pRightList.push_back(pRightLabel);

        pGridLayout->addWidget(pLeftLabel, i, 0);
        pGridLayout->addWidget(pMidLabel, i, 1);
        pGridLayout->addWidget(pRightLabel, i, 2);
    }

    m_pLabelList.append(pLeftList);
    m_pLabelList.append(pMidList);
    m_pLabelList.append(pRightList);

    CColorLabel *pBlockLabel = new CColorLabel(tr("挡住:"), false);
    CColorLabel *pUnBlockLabel = new CColorLabel(tr("未挡住:"));

    m_pTimeLineEdit = new CLabelLineEdit(tr("时间间隔(ms):"), "1000");
    m_pTimeLineEdit->SetLineEditFixedSize(80, 50);

    m_pCheckBox = new QCheckBox(tr("开始"));
    m_pCheckBox->setFixedHeight(50);
    connect(m_pCheckBox, &QCheckBox::clicked, this, &CMotorGXIO::_SlotStartChecked);

    m_pValueLabel = new CLabelLabel(tr("Value:"), "4294967295");

    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(80, 50);

    QHBoxLayout *pBottomLayout = new QHBoxLayout;
    pBottomLayout->setMargin(0);
    pBottomLayout->setSpacing(20);
    pBottomLayout->addStretch(1);
    pBottomLayout->addWidget(m_pMachineComboBox);
    pBottomLayout->addWidget(pBlockLabel);
    pBottomLayout->addWidget(pUnBlockLabel);
    pBottomLayout->addWidget(m_pTimeLineEdit);
    pBottomLayout->addWidget(m_pCheckBox);
    pBottomLayout->addWidget(m_pValueLabel);
    pBottomLayout->addStretch(1);

    QVBoxLayout *pMainLayout = new QVBoxLayout;
    pMainLayout->setMargin(0);
    pMainLayout->addLayout(pGridLayout);
    pMainLayout->addSpacing(30);
    pMainLayout->addLayout(pBottomLayout);
    pMainLayout->addStretch(1);
    this->setLayout(pMainLayout);
}

CMotorGXIO::CColorLabel::CColorLabel(const QString &strName, bool bRed)
{
    this->setFixedHeight(50);

    m_pNameLabel = new QLabel(strName);
    m_pNameLabel->setFixedHeight(50);

    m_pColorLabel = new QLabel;
    m_pColorLabel->setFixedSize(40, 40);
    m_pColorLabel->setStyleSheet("QLabel{border-radius: 20px; background-color: red;}"
                                 "QLabel::disabled{background-color: green}");
    m_pColorLabel->setEnabled(bRed);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->addWidget(m_pNameLabel);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pColorLabel);

    this->setLayout(pLayout);
}

void CMotorGXIO::CColorLabel::SetRedColor(bool bRed) const
{
    m_pColorLabel->setEnabled(bRed);
}
