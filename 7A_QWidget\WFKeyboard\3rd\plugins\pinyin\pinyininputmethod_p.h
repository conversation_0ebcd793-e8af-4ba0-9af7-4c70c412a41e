/****************************************************************************
**
** Copyright (C) 2016 The Qt Company Ltd.
** Contact: https://www.qt.io/licensing/
**
** This file is part of the Qt Virtual Keyboard module of the Qt Toolkit.
**
** $QT_BEGIN_LICENSE:GPL$
** Commercial License Usage
** Licensees holding valid commercial Qt licenses may use this file in
** accordance with the commercial license agreement provided with the
** Software or, alternatively, in accordance with the terms contained in
** a written agreement between you and The Qt Company. For licensing terms
** and conditions see https://www.qt.io/terms-conditions. For further
** information use the contact form at https://www.qt.io/contact-us.
**
** GNU General Public License Usage
** Alternatively, this file may be used under the terms of the GNU
** General Public License version 3 or (at your option) any later version
** approved by the KDE Free Qt Foundation. The licenses are as published by
** the Free Software Foundation and appearing in the file LICENSE.GPL3
** included in the packaging of this file. Please review the following
** information to ensure the GNU General Public License requirements will
** be met: https://www.gnu.org/licenses/gpl-3.0.html.
**
** $QT_END_LICENSE$
**
****************************************************************************/

#ifndef PINYININPUTMETHOD_P_H
#define PINYININPUTMETHOD_P_H

//
//  W A R N I N G
//  -------------
//
// This file is not part of the Qt API.  It exists purely as an
// implementation detail.  This header file may change from version to
// version without notice, or even be removed.
//
// We mean it.
//
//#include "qinputcontext.h"
#include <QObject>

class PinyinInputMethodPrivate;

class PinyinInputMethod : public QObject
{
    Q_OBJECT
    Q_DECLARE_PRIVATE(PinyinInputMethod)

public:
    explicit PinyinInputMethod(QObject *parent = NULL);
    ~PinyinInputMethod();

//    QList<QVirtualKeyboardInputEngine::InputMode> inputModes(const QString &locale);
//    bool setInputMode(const QString &locale, QVirtualKeyboardInputEngine::InputMode inputMode);
//    bool setTextCase(QVirtualKeyboardInputEngine::TextCase textCase);

    void SetInputState();

    bool KeyEventFun(Qt::Key key, const QString &text, Qt::KeyboardModifiers modifiers);

    void EmitCandidatesList(QList<QString>);

    void SelectListData(int);

    void ChosieCandiate(int id);

    void SetFirstCandiateCount(const int&,const bool& flag = false);

    void TurnPage(const int&,const int&);

    int GetTotalChoiseNum();

//    QList<QVirtualKeyboardSelectionListModel::Type> selectionLists();
//    int selectionListItemCount(QVirtualKeyboardSelectionListModel::Type type);
//    QVariant selectionListData(QVirtualKeyboardSelectionListModel::Type type, int index, QVirtualKeyboardSelectionListModel::Role role);
//    void selectionListItemSelected(QVirtualKeyboardSelectionListModel::Type type, int index);
protected:
//    bool eventFilter(QObject *obj, QEvent *event);

    QString identifierName();
    QString language();
    void reset();
    void update();
    bool isComposing() const;
    QString inputText;
private:
    QScopedPointer<PinyinInputMethodPrivate> d_ptr;

signals:

    void SignalCandidatesList(QList<QString>);
};

QT_END_NAMESPACE

#endif // PINYININPUTMETHOD_P_H
