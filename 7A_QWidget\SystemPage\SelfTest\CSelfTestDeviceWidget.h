#ifndef CSELFTESTDEVICEWIDGET_H
#define CSELFTESTDEVICEWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2025-05-23
  * Description: 自检-仪器
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include "CSelfTestDevItemWidget.h"
#include "CmdBus/CCmdBase.h"

class CSelfTestDeviceWidget : public QWidget , public CCmdBase
{
    Q_OBJECT
public:
    explicit CSelfTestDeviceWidget(QWidget *parent = nullptr);
    ~CSelfTestDeviceWidget();

    virtual void receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData);

protected:
    virtual void showEvent(QShowEvent *pEvent);

signals:
    void SignalReturn();

private:
    void _Init_1x8();

private:
    int m_iDevNum;
    int m_iItemNum;
    QPushButton *m_pReturnBtn;
    QList<CSelfTestDevItemWidget *> m_pDevItemList;
};

#endif // CSELFTESTDEVICEWIDGET_H
