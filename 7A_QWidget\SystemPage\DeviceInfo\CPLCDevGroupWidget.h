#ifndef CPLCDEVGROUPWIDGET_H
#define CPLC<PERSON>VGROUPWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-08-01
  * Description: 下位机group
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QGroupBox>
#include "CHLabelTitleWidget.h"
#include "CPLCDevItemWidget.h"

class CPLCDevGroupWidget : public QWidget
{
    Q_OBJECT
public:
    CPLCDevGroupWidget(const QString &strDevName, const SDevParamsStruct &sDevParams,
                       const QList<int> &iMachineIDList, QWidget *parent = nullptr);

private:
    QGroupBox *_CreateGroupBox();

private:
    CHLabelTitleWidget *m_pTitleWidget;
    QList<CPLCDevItemWidget *> m_pDevItemWidgetList;

    QString m_strDevName;
    SDevParamsStruct m_sDevParams;
    QList<int> m_iMachineIDList;
};

#endif // CPLCDEVGROUPWIDGET_H
