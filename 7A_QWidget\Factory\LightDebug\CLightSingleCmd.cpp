#include "CLightSingleCmd.h"
#include <QTimer>
#include <QDebug>
#include <QListView>
#include <QBoxLayout>

CLightSingleCmd::CLightSingleCmd(QWidget *parent) : QWidget(parent)
{
    Register2Map(Method_FLLED);
    Register2Map(Method_FLADC);
    Register2Map(Method_FLCST);
    Register2Map(Method_FLCDT);
    Register2Map(Method_FLCSP);
    Register2Map(Method_FLFREQ);

    for (int i=0; i<gk_iMachineCount; i++)
    {
        SLightUiStuct *pStruct = new SLightUiStuct;
        pStruct->pLedTimer = new QTimer(this);
        pStruct->pLedTimer->setProperty("iMachineID", i);
        connect(pStruct->pLedTimer, &QTimer::timeout, this, &CLightSingleCmd::_SlotLedTimeout);

        pStruct->pPDTimer = new QTimer(this);
        pStruct->pPDTimer->setProperty("iMachineID", i);
        connect(pStruct->pPDTimer, &QTimer::timeout, this, &CLightSingleCmd::_SlotPDTimeout);

        m_sLightUiList.push_back(pStruct);
    }

    _InitWidget();
}

CLightSingleCmd::~CLightSingleCmd()
{
    UnRegister2Map(Method_FLLED);
    UnRegister2Map(Method_FLADC);
    UnRegister2Map(Method_FLCST);
    UnRegister2Map(Method_FLCDT);
    UnRegister2Map(Method_FLCSP);
    UnRegister2Map(Method_FLFREQ);

    for(int i=0; i<m_sLightUiList.size(); i++)
    {
        SLightUiStuct *pStruct = m_sLightUiList.at(i);
        delete pStruct;
        pStruct = nullptr;
    }
    m_sLightUiList.clear();
}

void CLightSingleCmd::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    QString strMethodName = CPublicConfig::GetInstance()->GetMethodNameByID(iMethodID);
    QString strLog = QString("%1# %2 ").arg(iMachineID + 1).arg(strMethodName);
    strLog += (0 == iResult) ? "true " : "false ";
    //if(Method_FLADC == iMethodID) all print
    {
        QVariantList list = qVarData.toList();
        for(int i=0; i<list.size(); i++)
            strLog += QString("%1 ").arg(list.at(i).toDouble());
    }

    m_sLightUiList[iMachineID]->pTextBrowser->append(strLog);
    m_sLightUiList[iMachineID]->pTextBrowser->moveCursor(QTextCursor::End);
}

void CLightSingleCmd::_SlotLedBtn()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    int inteval = m_pIntevalLineEdit->GetLineEditText().toInt();
    if(inteval <= 0)
    {
        inteval = 1000;
        m_pIntevalLineEdit->SetLineEditText("1000");
    }
    int times = m_pTimesLineEdit->GetLineEditText().toInt();
    if(times <= 0)
    {
        times = 1;
        m_pTimesLineEdit->SetLineEditText("1");
    }

    SendJsonCmd(iMachineID, Method_FLLED, _GetLedCmd(iMachineID));
    m_sLightUiList[iMachineID]->pLedTimer->start(inteval);
}

void CLightSingleCmd::_SlotLightBtn()
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    int inteval = m_pIntevalLineEdit->GetLineEditText().toInt();
    if(inteval <= 0)
    {
        inteval = 1000;
        m_pIntevalLineEdit->SetLineEditText("1000");
    }
    int times = m_pTimesLineEdit->GetLineEditText().toInt();
    if(times <= 0)
    {
        times = 1;
        m_pTimesLineEdit->SetLineEditText("1");
    }

    QVariantList qVarList = {m_pPDComboBox->currentIndex()};
    QString strCmd = GetJsonCmdString(Method_FLADC, qVarList);
    SendJsonCmd(iMachineID, Method_FLADC, strCmd);

    m_sLightUiList[iMachineID]->pPDTimer->start(inteval);
}

void CLightSingleCmd::_SlotIntervalChanged(const QString &strInterval)
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_sLightUiList[iMachineID]->strInterval = strInterval;
}

void CLightSingleCmd::_SlotTimesChanged(const QString &strTimes)
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_sLightUiList[iMachineID]->strTimes = strTimes;
}

void CLightSingleCmd::_SlotLed1Checked(bool bChecked)
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_sLightUiList[iMachineID]->bLight1Checked = bChecked;
}

void CLightSingleCmd::_SlotLed2Checked(bool bChecked)
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_sLightUiList[iMachineID]->bLight2Checked = bChecked;
}

void CLightSingleCmd::_SlotPDChanged(int index)
{
    int iMachineID = m_pMachineComboBox->GetCurrentIndex();
    m_sLightUiList[iMachineID]->iPDIndex = index;
}

void CLightSingleCmd::_SlotMachineChanged(int iMachineID)
{
    SLightUiStuct *pStruct = m_sLightUiList[iMachineID];
    m_pIntevalLineEdit->SetLineEditText(pStruct->strInterval);
    m_pTimesLineEdit->SetLineEditText(pStruct->strTimes);
    m_pLed1CheckBox->setChecked(pStruct->bLight1Checked);
    m_pLed2CheckBox->setChecked(pStruct->bLight2Checked);
    m_pPDComboBox->setCurrentIndex(pStruct->iPDIndex);
    m_pStackedWidget->setCurrentIndex(iMachineID);
}

void CLightSingleCmd::_SlotLedTimeout()
{
    QTimer *pTimer = dynamic_cast<QTimer*>(sender());
    if(nullptr == pTimer)
        return;

    int iMachineID = pTimer->property("iMachineID").toInt();
    int iLeftTimes = m_sLightUiList[iMachineID]->strTimes.toInt();
    iLeftTimes--;
    qDebug()<<Q_FUNC_INFO<<iMachineID + 1<<"剩余次数:"<<iLeftTimes;
    if(m_pMachineComboBox->GetCurrentIndex() == iMachineID)
        m_pTimesLineEdit->SetLineEditText(QString::number(iLeftTimes));
    if(iLeftTimes <= 0)
    {
        pTimer->stop();
        return;
    }

    SendJsonCmd(iMachineID, Method_FLLED, _GetLedCmd(iMachineID));
}

void CLightSingleCmd::_SlotPDTimeout()
{
    QTimer *pTimer = dynamic_cast<QTimer*>(sender());
    if(nullptr == pTimer)
        return;

    int iMachineID = pTimer->property("iMachineID").toInt();
    int iLeftTimes = m_sLightUiList[iMachineID]->strTimes.toInt();
    iLeftTimes--;
    qDebug()<<Q_FUNC_INFO<<iMachineID<<"剩余次数:"<<iLeftTimes;
    if(m_pMachineComboBox->GetCurrentIndex() == iMachineID)
        m_pTimesLineEdit->SetLineEditText(QString::number(iLeftTimes));
    if(iLeftTimes <= 0)
    {
        pTimer->stop();
        return;
    }

    QVariantList qVarList = {m_pPDComboBox->currentIndex()};
    QString strCmd = GetJsonCmdString(Method_FLADC, qVarList);
    SendJsonCmd(iMachineID, Method_FLADC, strCmd);
}

void CLightSingleCmd::_InitWidget()
{
    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(80, 50);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMachineChanged(int)));

    m_pIntevalLineEdit = new CLabelLineEdit(tr("间隔(ms):"));
    m_pIntevalLineEdit->SetLineEditInputMethod(Qt::ImhDigitsOnly);
    m_pIntevalLineEdit->SetLineEditFixedSize(80, 50);
    connect(m_pIntevalLineEdit, &CLabelLineEdit::SignalTextChanged,
            this, &CLightSingleCmd::_SlotIntervalChanged);

    m_pTimesLineEdit = new CLabelLineEdit(tr("次数:"));
    m_pTimesLineEdit->SetLineEditInputMethod(Qt::ImhDigitsOnly);
    m_pTimesLineEdit->SetLineEditFixedSize(80, 50);
    connect(m_pTimesLineEdit, &CLabelLineEdit::SignalTextChanged,
            this, &CLightSingleCmd::_SlotTimesChanged);

    m_pLed1CheckBox = new QCheckBox(tr("1号灯"));
    m_pLed1CheckBox->setFixedHeight(40);
    m_pLed1CheckBox->setLayoutDirection(Qt::RightToLeft);
    connect(m_pLed1CheckBox, &QCheckBox::clicked, this, &CLightSingleCmd::_SlotLed1Checked);

    m_pLed2CheckBox = new QCheckBox(tr("2号灯"));
    m_pLed2CheckBox->setFixedHeight(40);
    m_pLed2CheckBox->setLayoutDirection(Qt::RightToLeft);
    connect(m_pLed2CheckBox, &QCheckBox::clicked, this, &CLightSingleCmd::_SlotLed2Checked);

    m_pLedBtn = new QPushButton(tr("执行"));
    m_pLedBtn->setFixedSize(100, 50);
    connect(m_pLedBtn, &QPushButton::clicked, this, &CLightSingleCmd::_SlotLedBtn);

    m_pPDComboBox = new QComboBox;
    m_pPDComboBox->setView(new QListView);
    m_pPDComboBox->addItems({"PD1", "PD2"});
    m_pPDComboBox->setFixedSize(80, 50);
    connect(m_pPDComboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(_SlotPDChanged(int)));

    m_pPDBtn = new QPushButton(tr("采光"));
    m_pPDBtn->setFixedSize(140, 50);
    connect(m_pPDBtn, &QPushButton::clicked, this, &CLightSingleCmd::_SlotLightBtn);

    m_pStackedWidget = new QStackedWidget;
    for(int i=0; i<gk_iMachineCount; i++)
    {
        QTextBrowser *pTextBrower = new QTextBrowser;
        m_sLightUiList[i]->pTextBrowser = pTextBrower;
        m_pStackedWidget->addWidget(pTextBrower);
    }

    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(10);
    pTopLayout->addWidget(m_pMachineComboBox);
    pTopLayout->addStretch(1);
    pTopLayout->addWidget(m_pIntevalLineEdit);
    pTopLayout->addWidget(m_pTimesLineEdit);
    pTopLayout->addSpacing(10);
    pTopLayout->addWidget(m_pLed1CheckBox);
    pTopLayout->addWidget(m_pLed2CheckBox);
    pTopLayout->addWidget(m_pLedBtn);
    pTopLayout->addSpacing(10);
    pTopLayout->addWidget(m_pPDComboBox);
    pTopLayout->addWidget(m_pPDBtn);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(10);
    pLayout->addLayout(pTopLayout);
    pLayout->addWidget(m_pStackedWidget);

    this->setLayout(pLayout);
}

QString CLightSingleCmd::_GetLedCmd(int iMachineID)
{
    SLightUiStuct * pStruct = m_sLightUiList[iMachineID];

    int iLed = 0;
    if(!pStruct->bLight1Checked && !pStruct->bLight2Checked)
        iLed = 0;
    else if(pStruct->bLight1Checked && !pStruct->bLight2Checked)
        iLed = 1;
    else if(!pStruct->bLight1Checked && pStruct->bLight2Checked)
        iLed = 2;
    else if(pStruct->bLight1Checked && pStruct->bLight2Checked)
        iLed = 3;

    QVariantList qVarList = {iLed};
    return GetJsonCmdString(Method_FLLED, qVarList);
}
