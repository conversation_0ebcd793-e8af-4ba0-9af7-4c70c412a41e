#ifndef CCREATEIMAGE_H
#define CCREATEIMAGE_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: chenhao
  * Date: 2025-02-28
  * Description: pdf截图工具类
  * -------------------------------------------------------------------------
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/


#include<QObject>
#include<QThread>
#include "PublicParams.h"


class CHistoryDetailWidget;

class CCreateImage : public QObject
{
    Q_OBJECT
public:
    static CCreateImage* GetInstance();

public slots:
    void SaveHistoryDetailImage(const SRunningInfoStruct &sRunInfo, bool bReview);
private:
    CCreateImage();
    ~CCreateImage();

private:
    CHistoryDetailWidget* m_HistoryDetail{nullptr};
};

#endif // CCREATEIMAGE_H
