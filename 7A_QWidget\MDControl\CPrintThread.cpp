#include "CPrintThread.h"
#include <QDebug>
#include <QTextCodec>
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "DBControl/CLotInfoDB.h"
#include "DBControl/CProjectDB.h"

CPrintThread *CPrintThread::m_spInstance = nullptr;

CPrintThread::CPrintThread()
{
    qRegisterMetaType<SPrintInfoStruct>("SPrintInfoStruct");
    connect(this, &CPrintThread::SignalInitThread, this, &CPrintThread::_SlotInitThread);

    m_pThread = new QThread;
    this->moveToThread(m_pThread);
    m_pThread->start();

    emit SignalInitThread();
}

CPrintThread::~CPrintThread()
{
    printf("%s\n", Q_FUNC_INFO);
}

bool CPrintThread::GetPrinterStatus()
{
#ifdef Q_OS_WIN
    m_bOpenSerialPort = true;
#endif
    return m_bOpenSerialPort;
}

CPrintThread *CPrintThread::GetInstance()
{
    if(nullptr == m_spInstance)
        m_spInstance = new CPrintThread;
    return m_spInstance;
}

void CPrintThread::AddPrintHistoryID(int iHistoryID)
{
    emit SignalAddPrintHistoryID(iHistoryID);
}

void CPrintThread::_SlotAddPrintHistoryID(int iHistoryID)
{
    qDebug()<<"add print history id:"<<iHistoryID;
    m_iPrintIDVector.push_back(iHistoryID);
}

void CPrintThread::UpdatePrintInfo(SPrintInfoStruct sSPrintInfoStruct)
{
    emit SignalUpdatePrintInfo(sSPrintInfoStruct);
}

void CPrintThread::_SlotUpdatePrintInfo(SPrintInfoStruct sSPrintInfoStruct)
{
    qDebug()<<Q_FUNC_INFO<<"更新打印选项"<<QThread::currentThreadId();
    m_sSPrintInfoStruct = sSPrintInfoStruct;
}

void CPrintThread::PrintStringData(QString strData)
{
    emit SignalPrintStringData(strData);
}

void CPrintThread::CancelPrint()
{
    emit SignalCleanPrint();
}

void CPrintThread::_SlotCleanPrint()
{
    qDebug()<<Q_FUNC_INFO<<"取消打印";
    m_bPrinting = false;
    m_iPrintIDVector.clear();
}

void CPrintThread::_SlotPrintStringData(QString strData)
{
    qDebug()<<"测试打印:"<<strData<<",HEX:"<<strData.toLocal8Bit().toHex();

    QByteArray qPrintDateByteArray;
    QByteArray qPrintTempByteArray;

    qPrintDateByteArray[0]= 0x1b;//初始化
    qPrintDateByteArray[1]= 0x40;

    if(eLanguage_Chinese == gk_iLanguage)
    {
        qPrintDateByteArray.append(0x1c);//中文
        qPrintDateByteArray.append(0x26);
    }

    qPrintDateByteArray.append(0x0d);// 回车
    qPrintDateByteArray.append(0x0A);// 换行
    qPrintDateByteArray.append(0x0A);// 换行
    qPrintDateByteArray.append(0x0A);// 换行
    qPrintDateByteArray.append(0x0A);// 换行

    qPrintDateByteArray.append(0x1c);//放大倍数
    qPrintDateByteArray.append(0x57);
    qPrintDateByteArray.append(0x02);

    _Add2PrintArray(0x02,strData,qPrintDateByteArray);

    qPrintDateByteArray.append(0x1c);//放大倍数
    qPrintDateByteArray.append(0x57);
    qPrintDateByteArray.append(0x01);

    qPrintDateByteArray.append(0x0d);// 回车
    qPrintDateByteArray.append(0x0A);// 换行

    qPrintDateByteArray.append(0x0d);// 回车
    qPrintDateByteArray.append(0x0A);// 换行
    qPrintDateByteArray.append(0x0A);// 换行
    qPrintDateByteArray.append(0x0A);// 换行
    qPrintDateByteArray.append(0x0A);// 换行

    m_pSerialPort->write(qPrintDateByteArray);
}

void CPrintThread::_SlotInitThread()
{
    qDebug()<<"打印串口线程ID:"<<QThread::currentThreadId();

    m_pSerialPort = nullptr;
    m_bOpenSerialPort = false;
    m_bPrinting = false;

    QString strName = "/dev/ttyS4";
#ifndef __aarch64__
    strName = "/dev/ttyUSB0";
#endif

    _InitPort(strName);

    connect(this, &CPrintThread::SignalPrintStringData, this, &CPrintThread::_SlotPrintStringData);
    connect(this, &CPrintThread::SignalAddPrintHistoryID, this, &CPrintThread::_SlotAddPrintHistoryID);
    connect(this, &CPrintThread::SignalUpdatePrintInfo, this, &CPrintThread::_SlotUpdatePrintInfo);
    connect(this, &CPrintThread::SignalCleanPrint, this, &CPrintThread::_SlotCleanPrint);

    m_pRunTimer = new QTimer(this);
    connect(m_pRunTimer, &QTimer::timeout, this, &CPrintThread::_SlotRunTimeout);
    m_pRunTimer->start(500);
}

void CPrintThread::_SlotReadSerial()
{
    qDebug()<<"read print serial:"<<m_pSerialPort->readAll();
}

void CPrintThread::_SlotRunTimeout()
{
    m_pRunTimer->stop();
    if(!m_bOpenSerialPort)
    {
        m_pRunTimer->start(500);
        return;
    }

    if(m_iPrintIDVector.isEmpty())
    {
        if(m_bPrinting)
        {
            m_bPrinting = false;
            emit SignalPrintLeftNum(-1);
        }
        m_pRunTimer->start(500);
        return;
    }

    m_bPrinting = true;
    m_pRunTimer->start(12000);

    int iID = m_iPrintIDVector.takeFirst();
    emit SignalPrintLeftNum(m_iPrintIDVector.size());
    _PrintID(iID);
}

static QString AdjustPrint(QString strPrintData)
{
    QString strTemp = strPrintData;

    if(eLanguage_Chinese == gk_iLanguage)
    {
        QByteArray byte = QTextCodec::codecForName("GB2312")->fromUnicode(strPrintData);
        int index = byte.indexOf(',');
        if(index < 31)
        {
            QString strComp(31-index,' ');
            qDebug()<<"AdjustPrint chinese:"<<index<<strComp.size()<<strComp;
            int index2 = strTemp.indexOf(',');
            strTemp.insert(index2+1,strComp);
        }
    }
    else if(eLanguage_Thai == gk_iLanguage)
    {
        int index = strPrintData.indexOf(',');
        strTemp.insert(index+1,"   ");

        // 此方法ORF前会多一个空格,猜测和打印机打印泰语有关
        //        QByteArray byte = QTextCodec::codecForName("tis620")->fromUnicode(strPrintData);
        //        int index = byte.indexOf(',');
        //        qDebug()<<"thai:"<<index<<byte.size()<<byte.toHex();
        //        if(index < 31)
        //        {
        //            QString strComp(31-index,' ');
        //            qDebug()<<"AdjustPrint thai:"<<index<<strComp.size()<<strComp;
        //            int index2 = strTemp.indexOf(',');
        //            strTemp.insert(index2+1,strComp);
        //        }
    }
    else
    {
        int index = strPrintData.toLocal8Bit().indexOf(',');
        if(index < 31)
        {
            QString strComp(31-index,' ');
            qDebug()<<"AdjustPrint:"<<index<<strComp.size()<<strComp;
            strTemp.insert(index+1,strComp);
        }
    }

    return strTemp;
}

void CPrintThread::_PrintID(int iHistoryID)
{
    SResultInfoStruct sResultInfo;
    SSampleInfoStruct sSampleInfo;
    SCardInfoStruct sCardInfo;
    SLotInfoStruct sLotInfo;

    CProjectDB::GetInstance()->GetHistoryData(iHistoryID, sResultInfo);
    CProjectDB::GetInstance()->GetSampleData(sResultInfo.strProjectName, sResultInfo.strSampleID, sResultInfo.strTestTime, sSampleInfo);
    CProjectDB::GetInstance()->GetCardData(sResultInfo.strCardID, sResultInfo.strTestTime, sCardInfo);
    CLotInfoDB::GetInstance()->GetLotInfoByShowName(sResultInfo.strProjectName, sLotInfo);

    qDebug()<<"历史ID:"<<iHistoryID<<"样本ID:"<<sResultInfo.strSampleID<<"卡盒ID:"<<sResultInfo.strCardID<<"测试时间:"<<sResultInfo.strTestTime;

    QByteArray qPrintDateByteArray;

    qPrintDateByteArray[0]= 0x1b;//初始化
    qPrintDateByteArray[1]= 0x40;

    if(eLanguage_Chinese == gk_iLanguage)
    {
        qPrintDateByteArray.append(0x1c);//中文
        qPrintDateByteArray.append(0x26);
    }

    if(m_sSPrintInfoStruct.bStatement)
    {
        qDebug()<<"打印实验声明:"<<m_sSPrintInfoStruct.strStatement;
        QString strStatement = tr("实验声明:") + m_sSPrintInfoStruct.strStatement;
        _Add2PrintArray(0x01, strStatement, qPrintDateByteArray);
    }

    //----------------------------------
    for(qint8 i=0; i<16; i++)
    {
        qPrintDateByteArray.append('-');
        qPrintDateByteArray.append(0x20);
    }

    QStringList strCurveNameList = sLotInfo.strCurveName.split(";");
    QStringList strResultList = sResultInfo.strResult.split(";");
    QStringList strCTList = sResultInfo.strCTValue.split(";");
    QString strStatus = CPublicConfig::GetInstance()->GetStatusShowString(sResultInfo.iStatus);
    qDebug()<<Q_FUNC_INFO<<"打印项目信息:"<<strCurveNameList<<strResultList<<strCTList<<strStatus;

    QStringList strInfoList = GetTargetOutputInfoList(sResultInfo, sLotInfo);
    for(int i=strInfoList.size()-1; i>=0; i--)
    {
        QString strOne = strInfoList.at(i);
        qDebug()<<Q_FUNC_INFO<<strOne;
        if(IsCtrlTarget(strOne))
            continue;
        _Add2PrintArray(0x01, strOne, qPrintDateByteArray);
    }

    //----------------------------------
    for(qint8 i=0; i<16; i++)
    {
        qPrintDateByteArray.append('-');
        qPrintDateByteArray.append(0x20);
    }

    if(m_sSPrintInfoStruct.bOperator)
    {
        qDebug()<<"打印操作者:"<<sResultInfo.strOperator;
        QString strUser = tr("操作者:") + sResultInfo.strOperator;
        _Add2PrintArray(0x01, strUser, qPrintDateByteArray);
    }

    if(m_sSPrintInfoStruct.bTelephone)
    {
        qDebug()<<"打印电话:"<<sSampleInfo.strTelephone;
        QString strTelephone = tr("电话:") + sSampleInfo.strTelephone;
        _Add2PrintArray(0x01, strTelephone, qPrintDateByteArray);
    }

    if(m_sSPrintInfoStruct.bBirthday)
    {
        qDebug()<<"打印生日:"<<sSampleInfo.strBirthday;
        QString strAge = tr("生日:") + sSampleInfo.strBirthday;
        _Add2PrintArray(0x01, strAge, qPrintDateByteArray);
    }

    if(m_sSPrintInfoStruct.bAge)
    {
        qDebug()<<"打印年龄:"<<sSampleInfo.strAge;
        QString strAge = tr("年龄:") + sSampleInfo.strAge;
        _Add2PrintArray(0x01, strAge, qPrintDateByteArray);
    }

    if(m_sSPrintInfoStruct.bGender)
    {
        qDebug()<<"打印性别:"<<sSampleInfo.strGender;
        QString strGender = tr("性别:") + sSampleInfo.strGender;
        _Add2PrintArray(0x01, strGender, qPrintDateByteArray);
    }

    if(m_sSPrintInfoStruct.bName)
    {
        qDebug()<<"打印姓名:"<<sSampleInfo.strName;
        QString strName = tr("姓名:") + sSampleInfo.strName;
        _Add2PrintArray(0x01, strName, qPrintDateByteArray);
    }

    if(m_sSPrintInfoStruct.bCardEXP)
    {
        qDebug()<<"打印试剂卡有效期:"<<sCardInfo.strCardEXP;
        QString strCardEXP = tr("试剂卡有效日期:") + sCardInfo.strCardEXP;
        _Add2PrintArray(0x01, strCardEXP, qPrintDateByteArray);
    }

    if(m_sSPrintInfoStruct.bCardMFG)
    {
        qDebug()<<"打印试剂卡生产日期:"<<sCardInfo.strCardMFG;
        QString strCardMFG = tr("试剂卡生产日期:") + sCardInfo.strCardMFG;
        _Add2PrintArray(0x01, strCardMFG, qPrintDateByteArray);
    }

    if(m_sSPrintInfoStruct.bCardLot)
    {
        qDebug()<<"打印试剂卡批次:"<<sCardInfo.strCardLot;
        QString strCardSN = tr("试剂卡批次:") + sCardInfo.strCardLot;
        _Add2PrintArray(0x01, strCardSN, qPrintDateByteArray);
    }

    if(m_sSPrintInfoStruct.bCardID)
    {
        qDebug()<<"打印试剂卡编号:"<<sCardInfo.strCardID;
        QString strCardID = tr("试剂卡编号:") + sCardInfo.strCardID;
        _Add2PrintArray(0x01, strCardID, qPrintDateByteArray);
    }

    if(m_sSPrintInfoStruct.bTestTime)
    {
        qDebug()<<"打印测试时间:"<<sResultInfo.strTestTime;
        QString strPrintTime = tr("测试时间:") + sResultInfo.strTestTime;
        _Add2PrintArray(0x01, strPrintTime, qPrintDateByteArray);
    }

    if(m_sSPrintInfoStruct.bSamplingDate)
    {
        qDebug()<<"打印采样日期:"<<sSampleInfo.strSamplingDate;
        QString strSamplingDate = tr("采样日期:") + sSampleInfo.strSamplingDate;
        _Add2PrintArray(0x01, strSamplingDate, qPrintDateByteArray);
    }

    if(m_sSPrintInfoStruct.bSampleType)
    {
        qDebug()<<"打印样本类型:"<<sSampleInfo.strSampleType;
        QString strSampleType = tr("样本类型:") + sSampleInfo.strSampleType;
        _Add2PrintArray(0x01, strSampleType, qPrintDateByteArray);
    }

    if(m_sSPrintInfoStruct.bSampleID)
    {
        qDebug()<<"打印样本编号:"<<sSampleInfo.strSampleID;
        QString strSampleID = tr("样本编号:") + sSampleInfo.strSampleID;
        _Add2PrintArray(0x01, strSampleID, qPrintDateByteArray);
    }

    //----------------------------------
    for(qint8 i=0;i<16;i++)
    {
        qPrintDateByteArray.append('-');
        qPrintDateByteArray.append(0x20);
    }

    qPrintDateByteArray.append(0x0d);// 回车
    qPrintDateByteArray.append(0x0A);// 换行
    qPrintDateByteArray.append(0x1c);//放大倍数
    qPrintDateByteArray.append(0x57);
    qPrintDateByteArray.append(0x02);

    if(m_sSPrintInfoStruct.bReportTitle)
    {
        qDebug()<<"打印报告单标题:"<<m_sSPrintInfoStruct.strReportTitle;
        _Add2PrintArray(0x02, m_sSPrintInfoStruct.strReportTitle, qPrintDateByteArray);
    }

    qPrintDateByteArray.append(0x1c);//取消放大倍数
    qPrintDateByteArray.append(0x57);
    qPrintDateByteArray.append(0x01);

    qPrintDateByteArray.append(0x0d);// 回车

    qPrintDateByteArray.append(' ');
    qPrintDateByteArray.append(0x0d);// 回车
    qPrintDateByteArray.append(0x0A);// 换行
    qPrintDateByteArray.append(' ');
    qPrintDateByteArray.append(0x0d);// 回车
    qPrintDateByteArray.append(0x0A);// 换行

    qPrintDateByteArray.append(0x0A);// 换行
    qPrintDateByteArray.append(0x0A);// 换行
    qPrintDateByteArray.append(0x0A);// 换行
    qPrintDateByteArray.append(0x0A);// 换行

    qint64 iWriterNumber = m_pSerialPort->write(qPrintDateByteArray);
    qDebug()<<"print write num:"<<iWriterNumber;
}

void CPrintThread::_InitPort(QString strSerialName)
{
    m_pSerialPort = new QSerialPort(this);
    m_pSerialPort->setPortName(strSerialName);
    if(m_pSerialPort->open(QIODevice::ReadWrite))
    {
        m_pSerialPort->setBaudRate(9600);
        m_pSerialPort->setDataBits(QSerialPort::Data8);
        m_pSerialPort->setParity(QSerialPort::NoParity);
        m_pSerialPort->setFlowControl(QSerialPort::NoFlowControl);
        m_pSerialPort->setStopBits(QSerialPort::OneStop);
        m_bOpenSerialPort = true;
        m_pSerialPort->setReadBufferSize(4096);// 64
        connect(m_pSerialPort, &QSerialPort::readyRead, this, &CPrintThread::_SlotReadSerial);

        qDebug()<<"打印机串口打开成功";

        _SetPrintCode(gk_iLanguage);
    }
    else
    {
        m_bOpenSerialPort = false;
        qDebug()<<"打印机串口打开失败";
    }
}

void CPrintThread::_Add2PrintArray(uchar ucFont, QString strPrintData, QByteArray& bytePrintData)
{
    QByteArray qPrintTempByteArray = QTextCodec::codecForName("GB2312")->fromUnicode(strPrintData);

    //tis620
    if(eLanguage_Thai == gk_iLanguage)
        qPrintTempByteArray = QTextCodec::codecForName("tis620")->fromUnicode(strPrintData);
    else if(eLanguage_Chinese == gk_iLanguage)
        qPrintTempByteArray = QTextCodec::codecForName("GB2312")->fromUnicode(strPrintData);
    else
        qPrintTempByteArray = QTextCodec::codecForName("ISO-8859-15")->fromUnicode(strPrintData);

    QList<QByteArray> newPrintArrayList = _GetNewPrintArrayList(ucFont,qPrintTempByteArray);
    _AddNewPrintList2WriteArray(newPrintArrayList,bytePrintData);
}

QList<QByteArray> CPrintThread::_GetNewPrintArrayList(uchar ucFont, QByteArray printArray)
{
    QList<QByteArray> newPrintArrayList;
    int iLen = printArray.size();

    if(0 == iLen)
        return newPrintArrayList;

    //放大
    if(0x02 == ucFont)
    {
        if(iLen <= 16)
        {
            newPrintArrayList.push_back(printArray);
        }
        else
        {
            int x0 = 0;
            while(x0 <= iLen)
            {
                QByteArray byte;
                if(iLen - x0 >= 16)
                {
                    byte = printArray.mid(x0,16);
                    int nums = _GetIsPrintNums(byte);
                    if(0 == nums%2)
                    {
                        newPrintArrayList.push_back(byte);
                        x0 += 16;
                    }
                    else
                    {
                        byte[15] = 0x20;
                        newPrintArrayList.push_back(byte);
                        x0 += 15;
                    }
                }
                else
                {
                    byte = printArray.mid(x0,iLen-x0);
                    newPrintArrayList.push_back(byte);
                    break;
                }
            }
        }
    }
    else if(0x01 == ucFont) //正常倍数
    {
        if(iLen <= 32)
        {
            newPrintArrayList.push_back(printArray);
        }
        else
        {
            int x0 = 0;
            while(x0 <= iLen)
            {
                QByteArray byte;
                if(iLen - x0 >= 32)
                {
                    byte = printArray.mid(x0,32);
                    int nums = _GetIsPrintNums(byte);
                    if(0 == nums%2)
                    {
                        newPrintArrayList.push_back(byte);
                        x0 += 32;
                    }
                    else
                    {
                        byte[31] = 0x20;
                        newPrintArrayList.push_back(byte);
                        x0 += 31;
                    }
                }
                else
                {
                    byte = printArray.mid(x0,iLen-x0);
                    newPrintArrayList.push_back(byte);
                    break;
                }
            }
        }
    }

    return newPrintArrayList;
}

void CPrintThread::_AddNewPrintList2WriteArray(const QList<QByteArray> &newPrintArrayList,
                                               QByteArray &writeArray)
{
    int iSize = newPrintArrayList.size();

    for(int i=iSize-1;i >= 0;i--)
    {
        writeArray.append(newPrintArrayList[i]);
        writeArray.append(0x0d);
    }
}

int CPrintThread::_GetIsPrintNums(QByteArray byteArray)
{
    int size = byteArray.size();
    if(0 == size)
        return 0;

    int nums = 0;
    for(int i=0;i<size;i++)
    {
        char ch = byteArray[i];
        if(isprint(ch))
            nums++;
    }
    return nums;
}


void CPrintThread::_SetPrintCode(int iLanguage)
{
    QString codeString = "1B 09 1B 16 00 1B 74 2C 1B 15";

    if(eLanguage_Thai == iLanguage)
        codeString = "1B 09 1B 16 00 1B 74 1A 1B 15";
    else
        codeString = "1B 09 1B 16 00 1B 74 2C 1B 15";

    codeString = codeString.remove(" ");
    QByteArray codeArray;
    codeArray.append(codeString);

    qDebug()<<"打印,设置字符集:"<<iLanguage<<codeString;

    QByteArray codeHex = QByteArray::fromHex(codeArray);
    m_pSerialPort->write(codeHex);
}
