#ifndef CSETTINGSWIDGET_H
#define CSETTINGSWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-19
  * Description: 工厂设置
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QMutex>
#include <QWidget>
#include <QCheckBox>
#include <QPushButton>
#include <QGroupBox>
#include <QRadioButton>

#include "CLineEdit.h"
#include "CLabelLineEdit.h"
#include "CLabelComboBox.h"

class CSettingsWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CSettingsWidget(QWidget *parent = nullptr);
    ~CSettingsWidget();

protected:
    virtual void showEvent(QShowEvent *pEvent);
    virtual void hideEvent(QHideEvent *pEvent);

public slots:
    void SlotScanData(QByteArray qScanByte);

signals:
    void SignalUploadEnd(int iRet);

private slots:
    void _SlotUploadEnd(int iRet);

private slots:
    void _SlotSetStatusBtn();
    void _SlotTestDoneBtn();
    void _SlotCheckCardBox(bool bClicked);
    void _SlotDynamicUpValue(bool bClicked);
    void _SlotCalcParam(bool bClicked);
    void _SlotOpenScanBtn();
    void _SlotCloseScanBtn();
    void _SlotFaultLogBtn();
    void _SlotWindPopBtn();
    void _SlotBuzzerCheckBox();
    void _SlotBiwavBtn();
    void _SlotRegisterCheckBox(bool bClicked);
    void _SlotImportRegisterBtn();
    void _SlotExportDB2USBBtn();
    void _SlotImportHistoryDBBtn();
    void _SlotExportDB2FTPBtn();
    void _SlotInstallNCFtpBtn();
    void _SlotFtpAutoUploadCheckBox(bool bClicked);
    void _SlotCmdBtn();
    void _SlotRunTimeBtn();
    void _SlotFtpOuterNetBtn();
    void _SlotFtpInnetNetBtn();
    void _SlotFtpSaveBtn();

private:
    void _ReadCfg();
    void _Thread2UploadFtp(bool bLog, bool bDB, bool bPdf, bool bXlsx);
    void _SetFtpWidgetEnable(bool bEnable);
    void _Thread2UploadDBFtp();

private:
    void _InitWidget();
    void _InitLayout();
    QGroupBox *_CreateFtpGroupBox();

private:
    bool m_bFtpThreadRun;
    QMutex m_qFtpMutex;
    QString m_strFtpIP;
    int m_iFtpPort;
    QString m_strFtpUser;
    QString m_strFtpPwd;
    QString m_strTipsText;

private:
    CLabelComboBox *m_pMachineComboBox;
    QComboBox *m_pStatusComboBx;
    QPushButton *m_pSetStatusBtn;
    QPushButton *m_pTestDoneBtn;

    QCheckBox *m_pCheckCardBox;
    QCheckBox *m_pPNCheck;
    QCheckBox *m_pDynamicUpValue;
    QCheckBox *m_pCalcParam;

    QList<QCheckBox*> m_pCheckBGYRList;

    QPushButton *m_pOpenScanBtn;
    QPushButton *m_pCloseScanBtn;
    CLineEdit *m_pScanLineEdit;

    CLineEdit *m_pFaultCodeLineEdit;
    QPushButton *m_pFaultLogBtn;

    CLineEdit *m_pWindPopLineEdit;
    QPushButton *m_pWindPopBtn;

    QCheckBox *m_pBuzzerCheckBox;
    QPushButton *m_pBiwavBtn;

    QCheckBox *m_pRegisterCheckBox;
    QPushButton *m_pImportRegisterBtn; //导入注册xlsx
    QLabel *m_pRegisterLabel;

    QPushButton *m_pExportDB2USBBtn;
    QPushButton *m_pImportHistoryDBBtn;

    QPushButton *m_pExportDB2FTPBtn;

    QCheckBox *m_pFtpLogCheckBox, *m_pFtpDBCheckBox, *m_pFtpPdfCheckBox, *m_pFtpXlsxCheckBox;
    QPushButton *m_pInstallNCFtpBtn;
    QCheckBox *m_pFtpAutoUploadCheckBox;

    CLineEdit *m_pCmdLineEdit;
    QPushButton *m_pCmdBtn;

    CLineEdit *m_pRunTimeLineEdit;
    QPushButton *m_pRunTimeBtn;

    CHLabelLineEdit *m_pFtpIPLineEdit, *m_pFtpPortLineEdit, *m_pFtpUserLineEdit, *m_pFtpPwdLineEdit;
    QRadioButton *m_pFtpOuterNetBtn, *m_pFtpInnerNetBtn;
    QPushButton *m_pFtpSaveBtn;
};

#endif // CSETTINGSWIDGET_H
