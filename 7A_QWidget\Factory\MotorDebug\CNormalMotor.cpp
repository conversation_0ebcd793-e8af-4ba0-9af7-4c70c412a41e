#include "CNormalMotor.h"
#include <QHBoxLayout>
#include "CMotorInfoDB.h"
#include "CMessageBox.h"
#include "CMotorDB.h"

/*
 * 0 : 复位方向
 * 1 : 电机参数
 * 2 : 电机步长
 * 3 : 时钟频率
*/

#define TYPE_DIR    0
#define TYPE_PARAM8 1
#define TYPE_STEP   2
#define TYPE_HZ     3

CNormalMotor::CNormalMotor(QWidget *parent)
    : QWidget(parent)
    , m_iMotorIndex(0)
    , m_iMachineID(0)
{
    m_strMotorNameList = CPublicConfig::GetInstance()->GetMotorNameList();
    for(int i=0; i<m_strMotorNameList.size(); i++)
    {
        QList<QList<double>> oneDirList;
        for(int j=0; j<3; j++)
        {
            QList<double> oneParamList;
            for(int k=0; k<8; k++)
            {
                oneParamList.push_back(0);
            }
            oneDirList.push_back(oneParamList);
        }
        m_dAllMotorParamList.push_back(oneDirList);
    }

    Register2Map(Method_GDIR);
    Register2Map(Method_SDIR);
    Register2Map(Method_GPAM);
    Register2Map(Method_SPAM);
    Register2Map(Method_GMSL);
    Register2Map(Method_SMSL);
    Register2Map(Method_GMCLK);
    Register2Map(Method_SMCLK);
    Register2Map(Method_RLPAM);
    Register2Map(Method_RLCFG);
    Register2Map(Method_MOVE);

    _InitWidget();
    _InitLayout();

    connect(CPublicConfig::GetInstance(), &CPublicConfig::SignalSoftTypeChanged,
            this, &CNormalMotor::SlotSoftTypeChanged);
}

CNormalMotor::~CNormalMotor()
{
    UnRegister2Map(Method_GDIR);
    UnRegister2Map(Method_SDIR);
    UnRegister2Map(Method_GPAM);
    UnRegister2Map(Method_SPAM);
    UnRegister2Map(Method_GMSL);
    UnRegister2Map(Method_SMSL);
    UnRegister2Map(Method_GMCLK);
    UnRegister2Map(Method_SMCLK);
    UnRegister2Map(Method_RLPAM);
    UnRegister2Map(Method_RLCFG);
    UnRegister2Map(Method_MOVE);
}

void CNormalMotor::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    qDebug()<<QString("%1#通用电机:%2 %3").arg(iMachineID + 1).arg(iMethodID).arg(iResult);

    if(Method_GDIR == iMethodID)
    {
        QVariantList qVarList = qVarData.toList();
        if(qVarList.size() < 2)
            return;

        int iMotor = qVarList.at(0).toInt();
        if(iMotor < 0 || iMotor >= m_strMotorNameList.size())
        {
            ShowWarning(this, m_strTipsText, tr("电机名称不一致"));
            return;
        }
        m_pMotorComboBox->SetCurrentIndex(iMotor);
        m_iMotorIndex = iMotor;
        m_pDirComboBox->SetCurrentIndex(qVarList.at(1).toInt());
        QStringList strDirList = {QString::number(iMotor), QString::number(TYPE_DIR), "0", qVarList.at(1).toString()};
        CMotorDB::GetInstance().AddOneParam(strDirList);
    }
    else if(Method_GPAM == iMethodID)
    {
        QVariantList qVarList = qVarData.toList();
        if(qVarList.size() < 10)
            return;

        int iMotor = qVarList.at(0).toInt();
        if(iMotor < 0 || iMotor >= m_strMotorNameList.size())
        {
            ShowWarning(this, m_strTipsText, tr("电机名称不一致"));
            return;
        }
        m_pMotorComboBox->SetCurrentIndex(iMotor);
        m_iMotorIndex = iMotor;

        int iParam = qVarList.at(1).toInt();
        if(iParam < 0 || iParam > 2)
        {
            //ShowWarning(this, gk_strTipsText, tr("电机参数不一致"));
            //return;
        }
        m_pParamComboBox->SetCurrentIndex(iParam);

        QStringList strParamList;
        for(int i=0; i<8; i++)
        {
            double dData = qVarList.at(i+2).toDouble();
            strParamList.push_back(QString::number(dData));
            m_dAllMotorParamList[iMotor][iParam][i] = dData;
            m_pLineEditList.at(i)->SetLineEditText(QString::number(dData));
        }

        QStringList strP8List = {QString::number(iMotor), QString::number(TYPE_PARAM8), QString::number(iParam), strParamList.join(",")};
        CMotorDB::GetInstance().AddOneParam(strP8List);
    }
    else if(Method_GMSL == iMethodID)
    {
        QVariantList qVarList = qVarData.toList();
        if(qVarList.size() < 2)
            return;

        int iMotor = qVarList.at(0).toInt();
        if(iMotor < 0 || iMotor >= m_strMotorNameList.size())
        {
            ShowWarning(this, m_strTipsText, tr("电机名称不一致"));
            return;
        }
        m_pMotorComboBox->SetCurrentIndex(iMotor);
        m_iMotorIndex = iMotor;
        m_pStepLineEdit->SetLineEditText(qVarList.at(1).toString());
        QStringList strStepList = {QString::number(iMotor), QString::number(TYPE_STEP), "0", qVarList.at(1).toString()};
        CMotorDB::GetInstance().AddOneParam(strStepList);
    }
    else if(Method_GMCLK == iMethodID)
    {
        QVariantList qVarList = qVarData.toList();
        if(qVarList.size() < 2)
            return;

        int iMotor = qVarList.at(0).toInt();
        if(iMotor < 0 || iMotor >= m_strMotorNameList.size())
        {
            ShowWarning(this, m_strTipsText, tr("电机名称不一致"));
            return;
        }
        m_pMotorComboBox->SetCurrentIndex(iMotor);
        m_iMotorIndex = iMotor;
        m_pHzLineEdit->SetLineEditText(qVarList.at(1).toString());
        QStringList strCLKList = {QString::number(iMotor), QString::number(TYPE_HZ), "0", qVarList.at(1).toString()};
        CMotorDB::GetInstance().AddOneParam(strCLKList);
    }
}

void CNormalMotor::SlotSoftTypeChanged(int iSoftType)
{
    Q_UNUSED(iSoftType);
    m_strMotorNameList = CPublicConfig::GetInstance()->GetMotorNameList();
    m_pMotorComboBox->SetComboBoxList(m_strMotorNameList);
}

void CNormalMotor::showEvent(QShowEvent *pEvent)
{
    QWidget::showEvent(pEvent);
}

void CNormalMotor::_SlotMachineComboBoxChanged(int iMachineID)
{
    m_iMachineID = iMachineID;
}

void CNormalMotor::_SlotMotorComboBoxChanged(int index)
{
    m_iMotorIndex = index;
}

void CNormalMotor::_SlotParamComboBoxChanged(int index)
{
    for(int i=0; i<m_pLineEditList.size(); i++)
    {
        QString strParam = QString::number(m_dAllMotorParamList[m_iMotorIndex][index][i]);
        m_pLineEditList.at(i)->SetLineEditText(strParam);
    }
}

void CNormalMotor::_SlotDirReadBtn()
{
    QVariantList qVarList = {m_iMotorIndex};
    SendJsonCmd(m_iMachineID, Method_GDIR, GetJsonCmdString(Method_GDIR, qVarList));
}

void CNormalMotor::_SlotDirSaveBtn()
{
    QVariantList qVarList = {m_iMotorIndex, m_pDirComboBox->GetCurrentIndex()};
    SendJsonCmd(m_iMachineID, Method_SDIR, GetJsonCmdString(Method_SDIR, qVarList));
}

void CNormalMotor::_SlotDirDBBtn()
{
    m_pDirComboBox->SetCurrentIndex(-1);
    int iDirIndex = CMotorDB::GetInstance().GetParamValueByMotorIndexAndType(m_iMotorIndex, TYPE_DIR).toInt();
    m_pDirComboBox->SetCurrentIndex(iDirIndex);
}

void CNormalMotor::_SlotParamReadBtn()
{
    QVariantList qVarList = {m_iMotorIndex, m_pParamComboBox->GetCurrentIndex()};
    SendJsonCmd(m_iMachineID, Method_GPAM, GetJsonCmdString(Method_GPAM, qVarList));
}

void CNormalMotor::_SlotParamSaveBtn()
{
    int iParamIndex = m_pParamComboBox->GetCurrentIndex();
    QVariantList qVarList = {m_iMotorIndex, iParamIndex};
    for(int i=0; i<m_pLineEditList.size(); i++)
    {
        QString str = m_pLineEditList.at(i)->GetLineEditText();
        if(str.isEmpty())
        {
            ShowInformation(this, m_strTipsText, tr("参数为空,请填写完整"));
            return;
        }
        m_dAllMotorParamList[m_iMotorIndex][iParamIndex][i] = str.toDouble();
        qVarList.push_back(str.toDouble());
    }

    SendJsonCmd(m_iMachineID, Method_SPAM, GetJsonCmdString(Method_SPAM, qVarList));
}

void CNormalMotor::_SlotParamDBBtn()
{
    for(int i=0; i<m_pLineEditList.size(); i++)
        m_pLineEditList.at(i)->SetLineEditText("");

    int iParamIndex = m_pParamComboBox->GetCurrentIndex();
    QString strValue = CMotorDB::GetInstance().GetParamValueByMotorIndexAndParam8(m_iMotorIndex, iParamIndex);
    QStringList strList = strValue.split(SPLIT_IN_CMD);
    if(8 == strList.size())
    {
        for(int i=0; i<8; i++)
        {
            m_dAllMotorParamList[m_iMotorIndex][iParamIndex][i] = strList.at(i).toDouble();
            m_pLineEditList.at(i)->SetLineEditText(strList.at(i));
        }
    }
}

void CNormalMotor::_SlotStepReadBtn()
{
    QVariantList qVarList = {m_iMotorIndex};
    SendJsonCmd(m_iMachineID, Method_GMSL, GetJsonCmdString(Method_GMSL, qVarList));
}

void CNormalMotor::_SlotStepSaveBtn()
{
    QString strStep = m_pStepLineEdit->GetLineEditText();
    if(strStep.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("电机步长不能为空"));
        return;
    }

    QVariantList qVarList = {m_iMotorIndex, strStep.toInt()};
    SendJsonCmd(m_iMachineID, Method_SMSL, GetJsonCmdString(Method_SMSL, qVarList));
}

void CNormalMotor::_SlotStepDBBtn()
{
    m_pStepLineEdit->SetLineEditText("");
    int iStep = CMotorDB::GetInstance().GetParamValueByMotorIndexAndType(m_iMotorIndex, TYPE_STEP).toInt();
    m_pStepLineEdit->SetLineEditText(QString::number(iStep));
}

void CNormalMotor::_SlotHzReadBtn()
{
    QVariantList qVarList = {m_iMotorIndex};
    SendJsonCmd(m_iMachineID, Method_GMCLK, GetJsonCmdString(Method_GMCLK, qVarList));
}

void CNormalMotor::_SlotHzSaveBtn()
{
    QString strHz = m_pHzLineEdit->GetLineEditText();
    if(strHz.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("时钟频率不能为空"));
        return;
    }

    QVariantList qVarList = {m_iMotorIndex, strHz.toInt()};
    SendJsonCmd(m_iMachineID, Method_SMCLK, GetJsonCmdString(Method_SMCLK, qVarList));
}

void CNormalMotor::_SlotHzDBBtn()
{
    m_pHzLineEdit->SetLineEditText("");
    int iHz = CMotorDB::GetInstance().GetParamValueByMotorIndexAndType(m_iMotorIndex, TYPE_HZ).toInt();
    m_pHzLineEdit->SetLineEditText(QString::number(iHz));
}

void CNormalMotor::_SlotResetParamBtn()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定要恢复所有电机的参数为出厂设置吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    SendJsonCmd(m_iMachineID, Method_RLPAM, GetJsonCmdString(Method_RLPAM));
}

void CNormalMotor::_SlotResetConfigBtn()
{
    int iBtnType = ShowQuestion(this, m_strTipsText, tr("确定要恢复所有电机的配置为出厂设置吗"));
    if(QMessageBox::Yes != iBtnType)
        return;

    SendJsonCmd(m_iMachineID, Method_RLCFG, GetJsonCmdString(Method_RLCFG));
}

void CNormalMotor::_SlotMoveBtn()
{
    QString strRun = m_pMoveLineEdit->GetLineEditText();
    if(strRun.isEmpty())
    {
        ShowInformation(this, m_strTipsText, tr("步数不能为空"));
        return;
    }

    QVariantList qVarList = {m_iMotorIndex, m_pMoveComboBox->GetCurrentIndex(), strRun.toInt()};
    SendJsonCmd(m_iMachineID, Method_MOVE, GetJsonCmdString(Method_MOVE, qVarList));
}

void CNormalMotor::_InitWidget()
{
    int iLabelWidth = 180;
    int iRestoredBtnWidth = 240;
    int iMoveBtnWidth = 100;
    int iReadBtnWidth = 100;
    if(eLanguage_Spanish == gk_iLanguage)
    {
        iLabelWidth = 245;
        iRestoredBtnWidth = 280;
        iMoveBtnWidth = 140;
    }
    else if(eLanguage_German == gk_iLanguage)
    {
        iLabelWidth = 235;
        iRestoredBtnWidth = 330;
        iReadBtnWidth = 120;
    }
    else if(eLanguage_Italian == gk_iLanguage)
    {
        iLabelWidth = 255;
        iRestoredBtnWidth = 320;
        iReadBtnWidth = 120;
    }

    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(110, 50);
    m_pMachineComboBox->SetLabelFixedSize(iLabelWidth, 50);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMachineComboBoxChanged(int)));

    m_pMotorComboBox = new CLabelComboBox(tr("电机名称:"), m_strMotorNameList);
    m_pMotorComboBox->SetComboBoxFixedSize(180, 50);
    m_pMotorComboBox->SetLabelFixedSize(iLabelWidth, 50);
    connect(m_pMotorComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMotorComboBoxChanged(int)));

    m_pDirComboBox = new CLabelComboBox(tr("复位方向:"), {tr("BWD"), tr("FWD")});
    m_pDirComboBox->SetComboBoxFixedSize(110, 50);
    m_pDirComboBox->SetLabelFixedSize(iLabelWidth, 50);

    m_pDirReadBtn = new QPushButton(tr("读取"));
    m_pDirReadBtn->setFixedSize(iReadBtnWidth, 50);
    connect(m_pDirReadBtn, &QPushButton::clicked, this, &CNormalMotor::_SlotDirReadBtn);

    m_pDirSaveBtn = new QPushButton(tr("保存"));
    m_pDirSaveBtn->setFixedSize(iReadBtnWidth, 50);
    connect(m_pDirSaveBtn, &QPushButton::clicked, this, &CNormalMotor::_SlotDirSaveBtn);

    m_pDirDBBtn = new QPushButton(tr("读DB"));
    m_pDirDBBtn->setFixedSize(iReadBtnWidth, 50);
    connect(m_pDirDBBtn, &QPushButton::clicked, this, &CNormalMotor::_SlotDirDBBtn);

    //
    QStringList strParamList = {tr("最大速度"), tr("运行"), tr("复位")};
    m_pParamComboBox = new CLabelComboBox(tr("电机参数:"), strParamList);
    m_pParamComboBox->SetComboBoxFixedSize(110, 50);
    m_pParamComboBox->SetLabelFixedSize(iLabelWidth, 50);
    connect(m_pParamComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotParamComboBoxChanged(int)));

    m_pParamReadBtn = new QPushButton(tr("读取"));
    m_pParamReadBtn->setFixedSize(iReadBtnWidth, 50);
    connect(m_pParamReadBtn, &QPushButton::clicked, this, &CNormalMotor::_SlotParamReadBtn);

    m_pParamSaveBtn = new QPushButton(tr("保存"));
    m_pParamSaveBtn->setFixedSize(iReadBtnWidth, 50);
    connect(m_pParamSaveBtn, &QPushButton::clicked, this, &CNormalMotor::_SlotParamSaveBtn);

    m_pParamDBBtn = new QPushButton(tr("读DB"));
    m_pParamDBBtn->setFixedSize(iReadBtnWidth, 50);
    connect(m_pParamDBBtn, &QPushButton::clicked, this, &CNormalMotor::_SlotParamDBBtn);

    m_pStepLineEdit = new CLabelLineEdit(tr("电机步长(nm):"));
    m_pStepLineEdit->SetLineEditFixedSize(110, 50);
    m_pStepLineEdit->SetLabelFixedSize(iLabelWidth, 50);

    m_pStepReadBtn = new QPushButton(tr("读取"));
    m_pStepReadBtn->setFixedSize(iReadBtnWidth, 50);
    connect(m_pStepReadBtn, &QPushButton::clicked, this, &CNormalMotor::_SlotStepReadBtn);

    m_pStepSaveBtn = new QPushButton(tr("保存"));
    m_pStepSaveBtn->setFixedSize(iReadBtnWidth, 50);
    connect(m_pStepSaveBtn, &QPushButton::clicked, this, &CNormalMotor::_SlotStepSaveBtn);

    m_pStepDBBtn = new QPushButton(tr("读DB"));
    m_pStepDBBtn->setFixedSize(iReadBtnWidth, 50);
    connect(m_pStepDBBtn, &QPushButton::clicked, this, &CNormalMotor::_SlotStepDBBtn);

    m_pHzLineEdit = new CLabelLineEdit(tr("时钟频率(MHz):"));
    m_pHzLineEdit->SetLineEditFixedSize(110, 50);
    m_pHzLineEdit->SetLabelFixedSize(iLabelWidth, 50);

    m_pHzReadBtn = new QPushButton(tr("读取"));
    m_pHzReadBtn->setFixedSize(iReadBtnWidth, 50);
    connect(m_pHzReadBtn, &QPushButton::clicked, this, &CNormalMotor::_SlotHzReadBtn);

    m_pHzSaveBtn = new QPushButton(tr("保存"));
    m_pHzSaveBtn->setFixedSize(iReadBtnWidth, 50);
    connect(m_pHzSaveBtn, &QPushButton::clicked, this, &CNormalMotor::_SlotHzSaveBtn);

    m_pHzDBBtn = new QPushButton(tr("读DB"));
    m_pHzDBBtn->setFixedSize(iReadBtnWidth, 50);
    connect(m_pHzDBBtn, &QPushButton::clicked, this, &CNormalMotor::_SlotHzDBBtn);

    m_pResetParamBtn = new QPushButton(tr("电机参数恢复出厂"));
    m_pResetParamBtn->setFixedSize(iRestoredBtnWidth, 50);
    connect(m_pResetParamBtn, &QPushButton::clicked, this, &CNormalMotor::_SlotResetParamBtn);

    m_pResetConfigBtn = new QPushButton(tr("电机配置恢复出厂"));
    m_pResetConfigBtn->setFixedSize(iRestoredBtnWidth, 50);
    connect(m_pResetConfigBtn, &QPushButton::clicked, this, &CNormalMotor::_SlotResetConfigBtn);

    QStringList strList = {"VSTART", "A1", "V1", "AMAX", "VMAX", "DMAX", "D1", "VSTOP"};
    for(int i=0; i<strList.size(); i++)
    {
        CLabelLineEdit *pLineEdit = new CLabelLineEdit(strList.at(i));
        pLineEdit->SetLabelFixedSize(80, 50);
        pLineEdit->SetLineEditFixedSize(100, 50);
        pLineEdit->SetLineEidtAlignment(Qt::AlignCenter);
        m_pLineEditList.push_back(pLineEdit);
    }

    m_pMoveComboBox = new CLabelComboBox(tr("方向:"),{"HOME", "END"});
    m_pMoveComboBox->SetComboBoxFixedSize(100, 50);

    m_pMoveLineEdit = new CLabelLineEdit(tr("步数(um):"));
    m_pMoveLineEdit->SetLineEditFixedSize(100, 50);

    m_pMoveBtn = new QPushButton(tr("运行"));
    m_pMoveBtn->setFixedSize(iMoveBtnWidth, 50);
    connect(m_pMoveBtn, &QPushButton::clicked, this, &CNormalMotor::_SlotMoveBtn);
}

void CNormalMotor::_InitLayout()
{
    int iVSpacing = 20;

    QHBoxLayout *pDirLayout = new QHBoxLayout;
    pDirLayout->setMargin(0);
    pDirLayout->setSpacing(20);
    pDirLayout->addWidget(m_pDirComboBox);
    pDirLayout->addWidget(m_pDirReadBtn);
    pDirLayout->addWidget(m_pDirSaveBtn);
    pDirLayout->addWidget(m_pDirDBBtn);
    pDirLayout->addStretch(1);

    QHBoxLayout *pParamLayout = new QHBoxLayout;
    pParamLayout->setMargin(0);
    pParamLayout->setSpacing(20);
    pParamLayout->addWidget(m_pParamComboBox);
    pParamLayout->addWidget(m_pParamReadBtn);
    pParamLayout->addWidget(m_pParamSaveBtn);
    pParamLayout->addWidget(m_pParamDBBtn);
    pParamLayout->addStretch(1);

    QHBoxLayout *pStepLayout = new QHBoxLayout;
    pStepLayout->setMargin(0);
    pStepLayout->setSpacing(20);
    pStepLayout->addWidget(m_pStepLineEdit);
    pStepLayout->addWidget(m_pStepReadBtn);
    pStepLayout->addWidget(m_pStepSaveBtn);
    pStepLayout->addWidget(m_pStepDBBtn);
    pStepLayout->addStretch(1);

    QHBoxLayout *pHzLayout = new QHBoxLayout;
    pHzLayout->setMargin(0);
    pHzLayout->setSpacing(20);
    pHzLayout->addWidget(m_pHzLineEdit);
    pHzLayout->addWidget(m_pHzReadBtn);
    pHzLayout->addWidget(m_pHzSaveBtn);
    pHzLayout->addWidget(m_pHzDBBtn);
    pHzLayout->addStretch(1);

    QHBoxLayout *pResetLayout = new QHBoxLayout;
    pResetLayout->setMargin(0);
    pResetLayout->addWidget(m_pResetParamBtn);
    pResetLayout->addSpacing(30);
    pResetLayout->addWidget(m_pResetConfigBtn);
    pResetLayout->addStretch(1);

    QHBoxLayout *pRunLayout = new QHBoxLayout;
    pRunLayout->setMargin(0);
    pRunLayout->setSpacing(20);
    pRunLayout->addWidget(m_pMoveComboBox);
    pRunLayout->addWidget(m_pMoveLineEdit);
    pRunLayout->addWidget(m_pMoveBtn);
    pRunLayout->addStretch(1);

    QVBoxLayout *pRightLayout = new QVBoxLayout;
    pRightLayout->setMargin(0);
    pRightLayout->setSpacing(iVSpacing);
    for(int i=0; i<m_pLineEditList.size(); i++)
        pRightLayout->addWidget(m_pLineEditList.at(i));
    pRightLayout->addStretch(1);

    QVBoxLayout *pLeftLayout = new QVBoxLayout;
    pLeftLayout->setMargin(0);
    pLeftLayout->setSpacing(iVSpacing);
    pLeftLayout->addWidget(m_pMachineComboBox, 0, Qt::AlignLeft);
    pLeftLayout->addWidget(m_pMotorComboBox, 0, Qt::AlignLeft);
    pLeftLayout->addLayout(pDirLayout);
    pLeftLayout->addLayout(pParamLayout);
    pLeftLayout->addLayout(pStepLayout);
    pLeftLayout->addLayout(pHzLayout);
    pLeftLayout->addLayout(pResetLayout);
    pLeftLayout->addLayout(pRunLayout);
    pLeftLayout->addStretch(1);

    QHBoxLayout *pMainLayout = new QHBoxLayout;
    pMainLayout->setMargin(0);
    pMainLayout->addStretch(1);
    pMainLayout->addLayout(pLeftLayout);
    pMainLayout->addSpacing(140);
    pMainLayout->addLayout(pRightLayout);
    pMainLayout->addStretch(1);
    this->setLayout(pMainLayout);
}
