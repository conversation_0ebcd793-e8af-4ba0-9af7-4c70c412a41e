#include "CCheckUserWidget.h"
#include <QPainter>
#include <QBoxLayout>
#include <QApplication>
#include <QRandomGenerator>

#include "CConfigJson.h"
#include "PublicConfig.h"
#include "PublicFunction.h"
#include "DBControl/CUserDB.h"

CCheckUserWidget::CCheckUserWidget(const QString &strTitle, QWidget *parent)
    : <PERSON><PERSON><PERSON><PERSON>(parent)
    , m_b<PERSON>ee(false)
    , m_bDynamicPassword(false)
    , m_bCheckOK(false)
    , m_strTitle(strTitle)
{
    qDebug()<<Q_FUNC_INFO<<strTitle;

    m_pAlgorithmDesCrypt = new AlgorithmDesCrypt;

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->addStretch(1);
    pLayout->addWidget(_CreateGroupBox(), 0, Qt::AlignHCenter);
    pLayout->addStretch(1);
    this->setLayout(pLayout);

    LoadQSS(this, ":/qss/qss/power.qss");

    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);
    this->setFixedSize(parent ? parent->size() : G_QRootSize);
    this->setAttribute(Qt::WA_TranslucentBackground);
    this->move(0, 0);
    this->setAttribute(Qt::WA_DeleteOnClose);
    this->showNormal();
}

CCheckUserWidget::~CCheckUserWidget()
{

}

bool CCheckUserWidget::GetCheckResult() const
{
    return m_bCheckOK;
}

void CCheckUserWidget::showEvent(QShowEvent *pEvent)
{
    m_bDynamicPassword = CPublicConfig::GetInstance()->GetDynamicPassword();

    QString strUser = CPublicConfig::GetInstance()->GetLoginUser();
    m_pUserLineEdit->setText(strUser);

    QWidget::showEvent(pEvent);
}

void CCheckUserWidget::paintEvent(QPaintEvent *pEvent)
{
    QPainter painter(this);
    painter.fillRect(this->rect(), QColor(0, 0, 0, 30));
    QWidget::paintEvent(pEvent);
}

void CCheckUserWidget::_SlotUserChanged(const QString &strUser)
{
    m_pCodeLabel->clear();
    m_pCodeLabel->setVisible(false);
    m_pInfoLabel->clear();

    if(!m_bDynamicPassword)
        return;

    if(gk_strAdminUserName == strUser)
        _CodeAdminPassword();
    else if(gk_strFactoryUserName == strUser)
        _CodeFactoryPassword();
}

void CCheckUserWidget::_SlotSeeBtn()
{
    QString strImagePath = "";
    if(!m_bSee)
    {
        m_bSee = true;
        m_pPasswordLineEdit->setEchoMode(QLineEdit::Normal);
        strImagePath = ":/image/ico/login/see.png";
    }
    else
    {
        m_bSee = false;
        m_pPasswordLineEdit->setEchoMode(QLineEdit::Password);
        strImagePath = ":/image/ico/login/no_see.png";
    }
    m_pSeeBtn->setIcon(QIcon(strImagePath));
    m_pSeeBtn->setIconSize(QSize(32, 32));
}

void CCheckUserWidget::_SlotCancleBtn()
{
    m_bCheckOK = false;
    this->close();
}

void CCheckUserWidget::_SlotConfirmBtn()
{
    QString strUser = m_pUserLineEdit->text();
    QString strPassword = m_pPasswordLineEdit->text();
    if(strUser.isEmpty() || strPassword.isEmpty())
    {
        m_pInfoLabel->setText(tr("请输入用户名和密码"));
        return;
    }

    QString strErrorInfo = tr("密码错误");
    if(gk_strFlyUserName == strUser)
    {
        if(gk_strFlyPassword == strPassword)
            _CheckOK();
        else
            m_pInfoLabel->setText(strErrorInfo);
        return;
    }

    if(gk_strMaintianUserName == strUser)
    {
        QString strDB = CUserDB::GetInstance()->FindUserPasswordByName(strUser);
        if(strDB == strPassword)
            _CheckOK();
        else
            m_pInfoLabel->setText(strErrorInfo);
        return;
    }

    if(gk_strAdminUserName == strUser)
    {
        QString strDB = CUserDB::GetInstance()->FindUserPasswordByName(strUser);
        if(strDB == strPassword)
        {
            _CheckOK();
            return;
        }

        if(m_bDynamicPassword)
        {
            if(m_strCodeAdmin == strPassword)
            {
                _CheckOK();
                return;
            }
        }

        m_pInfoLabel->setText(strErrorInfo);
        return;
    }

    if(gk_strFactoryUserName == strUser)
    {
        if(m_bDynamicPassword)
        {
            if(m_strCodeFactory == strPassword)
                _CheckOK();
            else
                m_pInfoLabel->setText(strErrorInfo);
        }
        else
        {
            if(gk_strFactoryPassWord == strPassword)
                _CheckOK();
            else
                m_pInfoLabel->setText(strErrorInfo);
        }
        return;
    }

    QString strLoginUser = CPublicConfig::GetInstance()->GetLoginUser();
    QString strLoginPassword = CPublicConfig::GetInstance()->GetLoginPassword();
    if(strLoginUser != strUser)
    {
        m_pInfoLabel->setText(tr("请输入当前登录账号或管理员账号"));
        return;
    }

    if(strLoginPassword == strPassword)
        _CheckOK();
    else
        m_pInfoLabel->setText(strErrorInfo);
}

void CCheckUserWidget::_CheckOK()
{
    m_pUserLineEdit->clear();
    m_pPasswordLineEdit->clear();
    m_pCodeLabel->clear();
    m_pInfoLabel->clear();
    m_strCodeAdmin.clear();
    m_strCodeFactory.clear();

    m_bSee = false;
    m_pPasswordLineEdit->setEchoMode(QLineEdit::Password);
    m_pSeeBtn->setIcon(QIcon(":/image/ico/login/no_see.png"));
    m_pSeeBtn->setIconSize(QSize(32, 32));

    m_bCheckOK = true;
    this->close();
}

void CCheckUserWidget::_CodeFactoryPassword()
{
    int iRand= QRandomGenerator::global()->bounded(1000, 9999);
    QString strCode = "F" + QTime::currentTime().toString("ssmm") + QString::number(iRand);
    m_pCodeLabel->setVisible(true);
    m_pCodeLabel->setText(tr("机器代码：") + strCode);
    m_pAlgorithmDesCrypt->SetKey("Wondfofc");
    m_pAlgorithmDesCrypt->SetSrc(strCode);
    m_strCodeFactory = m_pAlgorithmDesCrypt->encrypt();
    qDebug()<<Q_FUNC_INFO<<"生成factory动态密码:"<<m_strCodeFactory;
}

void CCheckUserWidget::_CodeAdminPassword()
{
    int iRand= QRandomGenerator::global()->bounded(1000, 9999);
    QString strCode = "A" + QTime::currentTime().toString("ssmm") + QString::number(iRand);
    m_pCodeLabel->setVisible(true);
    m_pCodeLabel->setText(tr("机器代码：") + strCode);
    m_pAlgorithmDesCrypt->SetKey("Wondfoad");
    m_pAlgorithmDesCrypt->SetSrc(strCode);
    m_strCodeAdmin = m_pAlgorithmDesCrypt->encrypt();
    qDebug()<<Q_FUNC_INFO<<"生成Admin动态密码:"<<m_strCodeAdmin;
}

QGroupBox *CCheckUserWidget::_CreateGroupBox()
{
    m_pCHLabelTitleWidget = new CHLabelTitleWidget(m_strTitle);

    m_pUserLabel = new QLabel(tr("用户名"));

    int iEditWidth = 460, iBtnWidth = 140;
    if(eLanguage_English == gk_iLanguage)
    {
        iEditWidth = 420;
    }
    else if(eLanguage_Spanish == gk_iLanguage)
    {
        iEditWidth = 400;
    }
    else if(eLanguage_German == gk_iLanguage)
    {
        iEditWidth = 390;
        iBtnWidth = 150;
    }
    else if(eLanguage_Italian == gk_iLanguage)
    {
        iEditWidth = 385;
    }

    m_pUserLineEdit = new CLineEdit;
    m_pUserLineEdit->setFixedSize(iEditWidth, 56);
    connect(m_pUserLineEdit, &QLineEdit::textChanged, this, &CCheckUserWidget::_SlotUserChanged);

    m_pPasswordLabel = new QLabel(tr("密码"));

    m_pPasswordLineEdit = _CreateLineEdit(iEditWidth);

    m_pCodeLabel = new QLabel;
    m_pCodeLabel->setFixedWidth(596);
    m_pCodeLabel->setObjectName("InfoLabel");
    m_pCodeLabel->setAlignment(Qt::AlignCenter);
    m_pCodeLabel->setVisible(false);

    m_pInfoLabel = new QLabel;
    m_pInfoLabel->setFixedWidth(596);
    m_pInfoLabel->setObjectName("InfoLabel");
    m_pInfoLabel->setAlignment(Qt::AlignCenter);

    m_pCancleBtn = new QPushButton(tr("取消"));
    m_pCancleBtn->setFixedSize(iBtnWidth, 56);
    m_pCancleBtn->setObjectName("CancelBtn");
    connect(m_pCancleBtn, &QPushButton::clicked, this, &CCheckUserWidget::_SlotCancleBtn);

    m_pConfirmBtn = new QPushButton(tr("确认"));
    m_pConfirmBtn->setFixedSize(iBtnWidth, 56);
    m_pConfirmBtn->setObjectName("ConfirmBtn");
    connect(m_pConfirmBtn, &QPushButton::clicked, this, &CCheckUserWidget::_SlotConfirmBtn);

    QHBoxLayout *pNameLayout = new QHBoxLayout;
    pNameLayout->setMargin(0);
    pNameLayout->setSpacing(0);
    pNameLayout->addWidget(m_pUserLabel);
    pNameLayout->addStretch(1);
    pNameLayout->addWidget(m_pUserLineEdit);

    QHBoxLayout *pPassswordLayout = new QHBoxLayout;
    pPassswordLayout->setMargin(0);
    pPassswordLayout->setSpacing(0);
    pPassswordLayout->addWidget(m_pPasswordLabel);
    pPassswordLayout->addStretch(1);
    pPassswordLayout->addWidget(m_pPasswordLineEdit);

    QHBoxLayout *pBtnLayout = new QHBoxLayout;
    pBtnLayout->setMargin(0);
    pBtnLayout->setSpacing(0);
    pBtnLayout->addStretch(1);
    pBtnLayout->addWidget(m_pCancleBtn);
    pBtnLayout->addSpacing(30);
    pBtnLayout->addWidget(m_pConfirmBtn);
    pBtnLayout->addStretch(1);

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setContentsMargins(24, 15, 24, 24);
    pLayout->addWidget(m_pCHLabelTitleWidget, 0, Qt::AlignLeft);
    pLayout->addSpacing(55);
    pLayout->addLayout(pNameLayout);
    pLayout->addSpacing(40);
    pLayout->addLayout(pPassswordLayout);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pCodeLabel, 0, Qt::AlignHCenter);
    pLayout->addSpacing(5);
    pLayout->addWidget(m_pInfoLabel, 0, Qt::AlignHCenter);
    pLayout->addStretch(1);
    pLayout->addLayout(pBtnLayout);

    QGroupBox *pGroupBox = new QGroupBox;
    pGroupBox->setFixedSize(596, 460);
    pGroupBox->setLayout(pLayout);
    return pGroupBox;
}

CLineEdit *CCheckUserWidget::_CreateLineEdit(int iEditWidth)
{
    CLineEdit *pLineEdit = new CLineEdit;
    pLineEdit->setFixedSize(iEditWidth, 56);
    pLineEdit->setEchoMode(QLineEdit::Password);

    m_pSeeBtn = new QPushButton;
    m_pSeeBtn->setFixedSize(32, 32);
    m_pSeeBtn->setObjectName("SeeBtn");
    m_pSeeBtn->setCursor(QCursor(Qt::ArrowCursor));
    m_pSeeBtn->setIcon(QIcon(":/image/ico/login/no_see.png"));
    m_pSeeBtn->setIconSize(QSize(32, 32));
    connect(m_pSeeBtn, &QPushButton::clicked, this, &CCheckUserWidget::_SlotSeeBtn);

    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(m_pSeeBtn);
    pLayout->addSpacing(24);
    pLineEdit->setLayout(pLayout);

    return pLineEdit;
}

bool ShowCheckUser(const QString &strTitle, QWidget *parent)
{
    CCheckUserWidget *pWidget = new CCheckUserWidget(strTitle, parent);
    if(-1 == pWidget->exec())
        return false;
    return pWidget->GetCheckResult();
}
