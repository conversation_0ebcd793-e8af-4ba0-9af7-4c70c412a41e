﻿#ifndef _DSC_H_
#define  _DSC_H_
#include "../common/HL7Segment.h"
#include "../common/HL7Field.h"
#include "../interface/IDSC.h"

class DSC : public HL7Segment, public IDSC
{
public:
	DSC();

	~DSC();

	DECLARE_OBJECTBASE

	BEGIN_IMPL_QUERYIF(DSC)
		IMPL_QUERYIF_BYPATH(IF_OBJECTBASE, IHL7Segment, IObjectBase)
		IMPL_QUERYIF(IF_HL7SEGMENT, IHL7Segment)
		IMPL_QUERYIF(IF_DSC, IDSC)
		END_IMPL_QUERYIF()
	/*
	 *	\brief 连续指针,index 0
	 */
	void SetContinuePointer(const char* continuePointerStr);

	void GetContinuePointer(char** continuePointerStr);

	/*
	 *	\brief 连续格式,index 1
	 */
	void SetContinueFormat(ContinueFormat continueFormat);

	void GetContinueFormat(char** continueFormat);

	virtual void Parse(const char* segmentStr, EncodingCharacters encodingCharacters);

	DSC& operator=(DSC& dsc);
private:
	HL7Field m_continuePontier;
	HL7Field m_continueFormat;
};

REGISTER_CLASS(DSC);
#endif