#ifndef CFLOneMachine_H
#define CFLOneMachine_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-02-19
  * Description: 一台机器的荧光
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QWidget>
#include <QCheckBox>
#include <QComboBox>

#include <QMap>
#include <QPointF>

#include <QPlainTextEdit>
#include "CSetChartXYRange.h"
#include "CFLSetParamWidget.h"
#include "include/ccalctlib.h"

class QCustomPlot;
class QCPItemStraightLine;
class CFLOneWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CFLOneWidget(int iMachineID, QWidget *parent = nullptr);
    ~CFLOneWidget();

    void SetCardID(const QString &strCardID);
    void ClearData();
    void InitTestInfo();

    void ParseFLCmd(const QVariant &qVarData);
    void ParsePCRSignalCmd(const QVariant &qVarData);
    void ParseStartCmd(int iResult);

signals:
    void SignalShowCurrentTestCardID(int iMachineID);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual void hideEvent(QHideEvent *pEvent) override;

private slots:
    void _SlotHoleComboBoxChanged(int iHole);
    // 错误曲线备注Combox槽函数
    void _SlotErroComboBoxChanged(int iIndex);
    void _SlotCalcBtn();
    void _SlotSetParamBtn();
    void _SlotBtnList();
    void _SlotTestCheckBox(bool bCheck);
    void _SlotStandardCheckBox(bool bCheck);
    void _SlotFitComboBoxChanged(int iFit);
    void _SlotDataFitModelComboBoxChanged(int iFit);
    void _SlotChangedChartXYRange(const QStringList &strRangeList);
    void _SlotStandardParam(QList<float> pfList);
    void _SlotFLCrossParam(QList<float> pfList);

private:

    // 更新结果信息，更新备注
    void _UpdateResult(const QStringList& strList);

    bool _GetCalcParam(QString& strConfig,int index);
    QString _GetResult(double dCT);
    void _ClearEditCtInfoValue();
    void _CalcCurrentTestCT(int iResult);              //计算当前实验CT
    void _ShowHoleCurrentFLData(int iHole); //显示当前实验荧光曲线
    void _CalcOneColor(int index);
    void _UpdateOneCurve(int index, const QList<qreal> &qYList);

    // add by chenhao 获取合适算法数据 保存到map中
    void _SelectAlgorithmValue();
    //add by chenhao 计算规范化和串扰数据
    bool _AlgorithmPreprocess(const QList<QMap<double, double>>& dFLRawMapList,QList<QMap<double, double>>& dFLStandMapList,const QList<float>& standardTarget,QList<float>& fAmplify);

    //add by chenhao 计算规范化和串扰数据
    bool _CalcFlCrossPreprocess(const QList<QMap<double, double>>& dFLRawMapList,QList<QMap<double, double>>& dFLCrossMapList,const QList<QList<float>>& FLCrossListList);

    //add by chenhao 计算单个串扰
    void _CalcFlCrossResult(const QList<QMap<double, double>> &dFLFollowList, QMap<double, double> &dFLResultMap, const QList<float>& fCrossList,int index);

    //add by chenhao 计算单个规范化和串扰数据
    bool _StandardOneCure(const QList<double>& dFLRawList,QList<double>& dFLStandList,float standardTarget,float& fAmplify);



    QVector<double> _List2Vector(const QList<qreal> &qDataList);
    void _ResetYRange(const QList<qreal> & qYDataList);

    void _SaveFL2Xlsx();

private:
    void _InitCustomPlot();
    void _AddGraph(QCustomPlot* pCustomPlot, QColor penColor, QColor pointColor, int iChart, QString strChartName);
    void _InitWidget();
    void _InitLayout();

private:
    const int m_iMachineID;
    QString m_strNewCardID; // 卡盒ID+测试时间
    bool m_bHasCalcCT;
    CCalCTLib m_cCtInfo;
    QStringList m_strCurveNameList;
    QList<qreal> m_qRealDataSrcY; // 单个孔中颜色荧的光值
    // 处理后的数据
    QList<qreal> m_qRealDataSrcX;
    QList<qreal> m_dSmoothListX, m_dSmoothListY;// 平滑
    QList<qreal> m_dLMPointXList, m_dLMPointYList;// 拟合
    QList<qreal> m_dBaseLineXListS, m_dBaseLineYListS;// 基线
    QList<qreal> m_dDeltaXListS, m_dDeltaYListS;// delta

    //按上传的循环数保存,用map不能用vector,因为循环数不固定且某个循环可能重复上传
    QList<double> m_dCTInfoList; // 3 * 4 * 2 保存当前实验计算的CT值
    QList<QMap<double, double>> m_dFLDataMapList; // 4 * 2

private:
    QComboBox *m_pHoleComboBox, *m_pColorComboBox;
    QPushButton *m_pCalcBtn,*m_pSetParamBtn;
    QList<QComboBox *> m_pErrCurvRemarkList;
    QList<int> m_ErrCurvRemarkNumList;  // 修改的时候保存更新， 点击计算的时候显示保存的内容

    QList<QLabel *> m_pLabelList;
    QList<CLineEdit *> m_pLineEditList;
    QList<QPushButton *> m_pBtnList;
    QComboBox *m_pFitComboBox;

    QCheckBox *m_pTestCheckBox;
    QCheckBox *m_pStandardCheckBox;
    CLabelLineEdit *m_pCrossBGLineEdit, *m_pCrossGYLineEdit;
    QCustomPlot *m_pCustomPlot;
    CSetChartXYRange *m_pSetXY;
    // chenhao add
    // add by chenhao
    // add by chenhao 从算法中获取数据 保存到map中
    CLineEdit* m_pEnlargeNum{nullptr};
    CLineEdit* m_pParamConfig{nullptr};
    QStringList m_oneColorCtInfo;
    QMap<QString,QList<QPointF>> m_dataListMap;


    QCPItemStraightLine *m_pVerticalLin{nullptr};
    QCPItemStraightLine *m_pHorizontalLine{nullptr};

    QComboBox *m_pDataModelComboBox{nullptr};
    QPlainTextEdit  * m_pResultLineEdit{nullptr};

    QList<float> m_dStandardList;
    QList<QList<float>> m_dFLCrossListList;
    QList<float> m_StandardAmplifyList;
    bool m_bStandard{false};
    bool m_bFLCross{false};



    // 调试参数
    CFLSetParamWidget * m_pCFLSetParamWidget;
    QList<QList<float>> m_dFLCrossSetParamListList;


    QList<qreal> m_qStandardY; // 单个孔中颜色的规范化后的荧光值
    QList<float> m_fStandardSetParamList;
    float  m_StandardAmplifySetParam;
    bool m_bStandardSetParan{false};
    // 调试参数 end

    QList<QMap<double, double>> m_dFLStandradMapList; // 4 * 2
    QList<QMap<double, double>> m_dFLCrossMapList; // 4 * 2

    // chenhao end

    bool m_bShow;
    bool m_bReplot;
};

#endif // CFLOneMachine_H
