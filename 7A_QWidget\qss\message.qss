QLabel
{
   color: #000;
   font-size: 25px;
   font-family: "Source Han Sans CN";
   border: 0px solid red;
}

QLabel#BackgroundLabel
{
   border-radius: 32px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #DEE8FB, stop: 0.15 #FFF);
}
QLabel#BackgroundLabel24
{
   border-radius: 24px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #DEE8FB, stop: 0.15 #FFF);
}
QLabel#ReadLabel
{
   color:red;
}
QLabel#InfoLabel
{
   color: #000;
   font-size: 22px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
}
QLabel#TitleIconLabel
{
   border-radius: 3px;
   background-color: #3D78E5;
}
QLabel#TitleTextLabel
{
   color: #353E4E;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
}
QLabel#DetailTitleLabel
{
    font-family:"Source Han Sans CN";
    font-size: 24px;
    color:#353E4E;
    padding-left: 10px;
}

QPushButton
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0, stop: 0 #8490FF, stop: 1 #3D78E5);
}

QPushButton:focus{outline: none;}

QPushButton#CancelBtn
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 2px solid #3D78E5;
   background-color: #FFF;
}

QPushButton#CancelBtn:hover
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 3px solid #3D78E5;
   background-color: #FFF;
}

QPushButton#CancelBtn:pressed
{
   color: #3D78E5;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   border: 4px solid #3D78E5;
   background-color: #FFF;
}
QPushButton#ConfirmBtn:pressed
{
   color: #FFF;
   font-size: 24px;
   font-weight: 500;
   font-family: "Source Han Sans CN";
   border-radius: 28px;
   background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
       stop: 0 #6670c7, stop: 1 #3361b8);
}

QTableWidget
{
    border: 1px solid #CCDEEE;
    border-radius: 0px;
    selection-background-color: #60c8ff;
    color: #333; /* text color */
    font-family: "Source Han Sans CN";
    font-size: 20px;
}
QHeaderView::section
{
    border: 1px solid #D6DFE9;
    border-radius: 0px;
    background-color: #455E7C;
    height: 50px;
    color: white;
    font-size: 21px;
    font-family:"Source Han Sans CN";
 /*   font-weight:bold;*/
}
QGroupBox#CMessageGroupBox
{
    border-radius: 24px;
    background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #DEE8FB, stop: 0.15 #FFF);
}
QGroupBox#DetailGroupBox
{
    border: 0px solid #0068b7;
    border-radius: 24px;
    font-size: 18px;
    font-family:"Source Han Sans CN";
    background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #DEE8FB, stop: 0.15 #FFF);
}
/*一键升级进度条样式*/
QProgressBar#UpdateProgressBar
{
   border:0px solid grey;
   border-radius:5px;
   background-color: #e6e6e6;
}
QProgressBar::chunk#UpdateProgressBar
{
   border-radius:5px;
   background-color:#FF9900;
}

/*远程下载进度条样式*/
QProgressBar#DownloadProgressBar
{
   border:0px solid grey;
   border-radius:5px;
   background-color: #e6e6e6;
}
QProgressBar::chunk#DownloadProgressBar
{
   border-radius:5px;
   background-color:#00ff00;
}
QTextBrowser
{
   border: 0px solid #0068b7;
   font-size: 22px;
   color: #000;
   background-color: transparent;
}
/*垂直滚动条整体*/
QScrollBar:vertical
{
    width:30px;
    background:rgb(240, 240, 240);
    margin:0px,0px,0px,0px;
    padding-top:30px;   /*上预留位置*/
    padding-bottom:30px;    /*下预留位置*/
}

/*滚动条中滑块的样式*/
QScrollBar::handle:vertical
{
    width:30px;
    background:rgb(166, 166, 166);
    border-radius:0px;
    min-height:30px;
}

/*鼠标触及滑块样式*/
QScrollBar::handle:vertical:hover
{
    width:30px;
    background:rgb(86, 86, 86);
    border-radius:0px;
}
/*水平滚动条整体*/
QScrollBar:horizontal
{
    height:30px;
    background:rgb(240, 240, 240);
    margin:0px,0px,0px,0px;
    padding-left:0px;   /*上预留位置*/
    padding-right:0px;    /*下预留位置*/
}

/*滚动条中滑块的样式*/
QScrollBar::handle:horizontal
{
    height:30px;
    background:rgb(166, 166, 166);
    border-radius:0px;
    width:30px;
}

/*鼠标触及滑块样式*/
QScrollBar::handle:horizontal:hover
{
    height:30px;
    background:rgba(166, 166, 166);
    border-radius:0px;
}
