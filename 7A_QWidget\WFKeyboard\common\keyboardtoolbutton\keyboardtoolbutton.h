#ifndef KEYBOARDTOOLBUTTON_H
#define KEYBOARDTOOLBUTTON_H

#include <QToolButton>
#include <QTimer>
#include <QTime>

class KeyBoardToolButton : public QToolButton
{
    Q_OBJECT
public:
    explicit KeyBoardToolButton(QWidget *parent = 0);

    explicit KeyBoardToolButton(QString lowerCase, QString capital,QString lowerLongPressStr,QString capitalLongPressStr, QWidget *parent = 0);

    void SetCharPropetry(QString lowerCase, QString capital,QString lowerLongPressStr,QString capitalLongPressStr);

    void SetCapitalChar();

    void SetLowerCaseChar();

    bool GetLongPressFlag();

    QString GetCurrentLongPressStr();

protected:
    void mousePressEvent(QMouseEvent *e);

    void mouseReleaseEvent(QMouseEvent *e);

//    bool eventFilter(QObject *obj, QEvent *e);

signals:
    void pressEventSignal();

    void releaseEventSignal();

private:
    class PrivateData;
    PrivateData *const md;
};

#endif // KEYBOARDTOOLBUTTON_H
