﻿#ifndef _ERR_H_
#define _ERR_H_
#include "../common/HL7Segment.h"
#include "../interface/IERR.h"

class ERR :
	public HL7Segment,public IERR
{
public:
	ERR();
	virtual ~ERR();

	DECLARE_OBJECTBASE

	BEGIN_IMPL_QUERYIF(ERR)
		IMPL_QUERYIF_BYPATH(IF_OBJECTBASE, IHL7Segment, IObjectBase)
		IMPL_QUERYIF(IF_HL7SEGMENT, IHL7Segment)
		IMPL_QUERYIF(IF_ERR, IERR)
		END_IMPL_QUERYIF()

	/*
	 *	\brief 错误代码
	 */
	void SetErrorCode(const char* errorCodeStr);

	void GetErrorCode(char** errorCodeStr);

	virtual void Parse(const char* segmentStr, EncodingCharacters encodingCharacters);

	ERR& operator=(ERR& err);
private:
	HL7Field m_errorCode;
};

REGISTER_CLASS(ERR);
#endif
