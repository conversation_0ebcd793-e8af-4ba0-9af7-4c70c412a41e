#ifndef CIPCINFOWIDGET_H
#define CIPCINFOWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-30
  * Description: 上位机信息
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QPushButton>

#include "CLineEdit.h"
#include "CPressLabel.h"
#include "CDateTimeWidget.h"

class CIPCInfoWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CIPCInfoWidget(QWidget *parent = nullptr);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual void hideEvent(QHideEvent *pEvent) override;

signals:
    void SignalReturn();

private slots:
    void _SlotEditBtn();
    void _SlotScanBtn();
    void _SlotShowDateTime();
    void _SlotConfirmDateTime(const QString &strDateTime);
    void _SlotScanData(QByteArray qScanData);

private:
    void _InitWidget();
    void _InitLayout();

private:
    QLabel *m_pImageLabel;

    QLabel *m_pNameLabel;
    QLabel *m_pSNLabel;
    CLineEdit *m_pSNLineEdit;
    QLabel *m_pFirmVerLabel;
    QLabel *m_pIPLabel;
    QLabel *m_pMACLabel;

    QLabel *m_pDateNameLabel;
    CPressLabel *m_pDateValueLabel;
    CDateTimeWidget *m_pCDateTimeWidget;

    QLabel *m_pManufacturerLabel;

    QLabel *m_pAddressNameLabel;
    QLabel *m_pAddressValueLabel;

    QPushButton *m_pReturnBtn, *m_pEditBtn, *m_pScanBtn;

private:
    bool m_bEdit;
    const QString m_strTipsText;
};

#endif // CIPCINFOWIDGET_H
