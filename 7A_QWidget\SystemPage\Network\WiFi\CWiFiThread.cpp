﻿#include "CWiFiThread.h"
#include <QDebug>
#include <QProcess>

#include "PublicFunction.h"

CWiFiThread *CWiFiThread::m_spInstance = nullptr;

CWiFiThread *CWiFiThread::GetInstance()
{
    if(nullptr == m_spInstance)
        m_spInstance = new CWiFiThread;
    return m_spInstance;
}

CWiFiThread::CWiFiThread()
{
    connect(this, &CWiFiThread::SignalInit, this, &CWiFiThread::_SlotInit);
    System("killall dhcpcd");

    m_pThread = new QThread;
    this->moveToThread(m_pThread);
    m_pThread->start();

    emit SignalInit();
}

CWiFiThread::~CWiFiThread()
{

}

void CWiFiThread::OpenWiFi()
{
    m_bOpen = true;
    qDebug()<<Q_FUNC_INFO;
    System("killall udhcpc");
    System("killall dhcpcd");
    emit SignalOpenWiFi();
}

void CWiFiThread::_SlotOpenWiFi()
{
    m_bScanning = true;
    qDebug()<<"打开WiFi";

    System("killall wpa_supplicant");
    System("killall dhclient");
    System("killall udhcpc");
    System("ifconfig eth0 down");
    System("ifconfig eth1 down");
    System("ifconfig wlan0 up");
    System("ip addr flush dev wlan0");

    QString strCmd = "/usr/sbin/iw dev wlan0 scan | grep -E \"SSID:|signal\"";
    QString strScanData = RunCmdPipe(strCmd);
    qDebug()<<"wifi scan data:"<<strScanData;
    _ParseScanData(strScanData);

    m_bScanning = false;
}

void CWiFiThread::CloseWiFi()
{
    m_bOpen = false;
    emit SignalCloseWiFi();
    System("killall udhcpc");
    System("killall dhcpcd");
    qDebug()<<Q_FUNC_INFO;
}

void CWiFiThread::_SlotCloseWiFi()
{
    qDebug()<<"关闭WiFi";

    System("killall wpa_supplicant");
    System("killall dhclient");
    System("killall udhcpc");
    System("ifconfig wlan0 down");
}

void CWiFiThread::ConnectWiFi(QString strName, QString strPwd)
{
    System("killall udhcpc");
    qDebug()<<Q_FUNC_INFO;
    emit SignalConnectWiFi(strName, strPwd);
}

void CWiFiThread::_SlotConnectWiFi(QString strName, QString strPwd)
{
    qDebug()<<"连接WiFi:"<<strName<<strPwd;

    m_bConnect = true;
    QString strConfPath = "/usr/local/test/wpa_supplicant.conf";
    System("killall wpa_supplicant");
    System("killall udhcpc");
    QThread::msleep(100);

    if(strPwd.isEmpty())
    {
        qDebug()<<"配置无密码WiFi:";
        _WriteNoPwdConf(strName);
    }
    else
    {
        qDebug()<<"配置有密码WiFi:";
        QString strCmd = QString("wpa_passphrase \"%1\" \"%2\" > %3").arg(strName).arg(strPwd).arg(strConfPath);
        System(strCmd);
    }
    System("sync");
    QString strConfData;
    ReadFile(strConfPath, strConfData);
    qDebug()<<"WiFi配置读取:"<<strConfData;

    QString strConnectCmd = QString("wpa_supplicant -i wlan0 -c %1 -B").arg(strConfPath);
    QString strConnectResult = RunQProcess(strConnectCmd);
    qDebug()<<"WiFi配置结果:"<<strConnectCmd<<strConnectResult;
    QThread::msleep(1000);

    int iRunTimes = 0;
    while (1)
    {
        if(iRunTimes >= 5)
            break;
        iRunTimes++;

        bool bConnectOK = true;
        for(int i=0; i<5; i++)
        {
            QString strLink = RunQProcess("iw dev wlan0 link");
            qDebug()<<Q_FUNC_INFO<<"iw dev wlan0 link:"<<strLink;
            if(!strLink.contains("Connected to", Qt::CaseInsensitive))
            {
                bConnectOK = false;
                break;
            }
            QThread::msleep(500);
        }
        if(bConnectOK)
        {
            qDebug()<<"WiFi自动获取IP";
            System("udhcpc -i wlan0");
            QThread::msleep(200);
            break;
        }
        qDebug()<<Q_FUNC_INFO<<"iRunTimes:"<<iRunTimes;
        QThread::msleep(1000);
    }

    if(false == m_bOpen || false == m_bConnect)
    {
        qDebug()<<Q_FUNC_INFO<<"WiFi已关闭"<<m_bOpen<<m_bConnect;
        return;
    }

    if(iRunTimes >= 5)
    {
        System("killall wpa_supplicant");
        System("killall udhcpc");
        emit SignalConnectEnd(false, strName, strPwd);
    }
    else
    {
        emit SignalConnectEnd(true, strName, strPwd);
    }
}

void CWiFiThread::_ClearIP()
{
    System("ip addr flush dev wlan0");

    QTime t1 = QTime::currentTime();
    while (1)
    {
        QString str = RunQProcess("ip addr show wlan0");
        qDebug()<<__FUNCTION__<<str;
        if(!str.contains("scope global"))
            break;
        QThread::msleep(50);
    }
    qDebug()<<"清除IP耗时:"<<t1.msecsTo(QTime::currentTime());
}

void CWiFiThread::DisconnectWiFi()
{
    m_bConnect = false;
    emit SignalDisconnectWiFi();
    System("killall udhcpc");
    System("killall dhcpcd");
    qDebug()<<Q_FUNC_INFO;
}

void CWiFiThread::_SlotDisconnectWiFi()
{
    qDebug()<<"断开WiFi";
    System("killall wpa_supplicant");
    System("killall dhclient");
    System("killall udhcpc");
}

void CWiFiThread::_SlotInit()
{
    qDebug()<<"WiFi线程:"<<QThread::currentThreadId();

    m_bOpen = false;
    m_bScanning = false;
    m_bConnect = false;

    connect(this, &CWiFiThread::SignalOpenWiFi, this, &CWiFiThread::_SlotOpenWiFi);
    connect(this, &CWiFiThread::SignalCloseWiFi, this, &CWiFiThread::_SlotCloseWiFi);
    connect(this, &CWiFiThread::SignalConnectWiFi, this, &CWiFiThread::_SlotConnectWiFi);
    connect(this, &CWiFiThread::SignalDisconnectWiFi, this, &CWiFiThread::_SlotDisconnectWiFi);
}

static QString ConvertWiFiCHName(QString strWiFiName)
{
    if(strWiFiName.isEmpty())
        return strWiFiName;

    QString strNewName = strWiFiName;

    QStringList strHexList;
    QStringList strCHList;
    for(int i=0; i<strWiFiName.size();)
    {
        QString str2Char = strWiFiName.mid(i,2);
        if("\\x" != str2Char && "\\X" != str2Char)
        {
            i++;
            continue;
        }

        QString strHex = strWiFiName.mid(i,12);
        QString strHex1 = strHex;
        strHex1.remove("\\x");
        strHex1.remove("\\X");
        QByteArray byteHex;
        byteHex.append(strHex1);
        QByteArray byteBin = QByteArray::fromHex(byteHex);
        QString strCH = QString::fromLocal8Bit(byteBin);

        strHexList.push_back(strHex);
        strCHList.push_back(strCH);

        i += 12;
    }

    for(int i=0; i<strHexList.size(); i++)
    {
        strNewName.replace(strHexList.at(i),strCHList.at(i));
    }

    return strNewName;
}

void CWiFiThread::_ParseScanData(QString strScanData)
{
    if(strScanData.isEmpty())
        return;

    QMap<QString, QString> strMap;
    strMap["wondfo"] = "-48.00";
    strMap["honor100p"] = "-60.00";
    strMap["wondfo-guest"] = "-77.00";
    strMap["WF"] = "-110.00";
    strMap.clear();

    QStringList strList = strScanData.remove("\r").remove("\t").split("\n");    
    for(int i=0; i<strList.size()-1; i++)
    {
        QString strStrength = strList.at(i);
        if(!strStrength.contains("signal:"))
            continue;

        strStrength = strStrength.remove("signal:").remove("dBm");
        strStrength = strStrength.trimmed();

        QString strName = strList.at(i+1);
        strName = strName.remove("SSID:");
        strName = strName.trimmed();

        if(strName.contains("\\x", Qt::CaseInsensitive))
            strName = ConvertWiFiCHName(strName);

        if(strName.isEmpty())
            continue;

        if(strMap.contains(strName))
        {
            QString strOldStrength = strMap.value(strName);
            if(strOldStrength > strStrength)
                strMap[strName] = strStrength;
        }
        else
        {
            strMap[strName] = strStrength;
        }
    }

    emit SignalScanMap(strMap);
}

void CWiFiThread::_WriteNoPwdConf(QString strName)
{
    QString strData = "network={\n\t"
                      "ssid=\"Fenzi\"\n\t"
                      "key_mgmt=NONE\n"
                      "}\n";
    strData.replace("Fenzi", strName);

    QString strCfgPath = "/usr/local/test/wpa_supplicant.conf";
    QFile file(strCfgPath);
    if(file.open(QIODevice::ReadWrite))
    {
        file.resize(0);
        file.write(strData.toLocal8Bit());
        file.close();
    }
}
