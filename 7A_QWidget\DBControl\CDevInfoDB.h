#ifndef CDEVINFODB_H
#define CDEVINFODB_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-11-07
  * Description: 保存仪器信息
  * -------------------------------------------------------------------------
  * History:
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QMap>
#include <QObject>
#include "CSqliteDBBase.h"

class CDevInfoDB : public QObject , public CSqliteDBBase
{
    Q_OBJECT
public:
    static CDevInfoDB &GetInstance();
    virtual ~CDevInfoDB();

    //校准
    QMap<int, QStringList> GetAllCalibrateDate();
    bool GetCalibrateDate(int iMachineID, QString &strLastDate, QString &strNextDate);
    bool UpdateCalibrateDate(int iMachineID, QString strLastDate, QString strNextDate);

    bool AddCalibrateResult(int iMachineID, QString strResult, QString strDetails, QString strDate, QString strReport);
    int GetCalibrateResultCounts();
    bool GetCalibrateOnePageData(int iPage, int iOffset, QList<QStringList> &strReadList);

    //自检
    QMap<int, QStringList> GetAllSelftestDate();
    bool GetSelftestDate(int iMachineID, QString &strLastDate, QString &strNextDate);
    bool UpdateSelftestDate(int iMachineID, QString strLastDate, QString strNextDate);

    bool AddSelftestResult(int iMachineID, QString strResult, QString strDetails, QString strDate, QString strReport);
    int GetSelftestResultCounts();
    bool GetSelftestOnePageData(int iPage, int iOffset, QList<QStringList> &strReadList);

private:
    CDevInfoDB();

    void _InitCalibrateDateTable();
    void _InitCalibrateResultTable();

    void _InitSelftestDateTable();
    void _InitSelftestResultTable();

private:
    Q_DISABLE_COPY(CDevInfoDB)
};

#endif // CDEVINFODB_H
