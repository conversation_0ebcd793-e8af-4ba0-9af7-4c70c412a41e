#ifndef CLIGHTONEMDT_H
#define CLIGHTONEMDT_H

#include <QWidget>
#include <QComboBox>
#include <QPushButton>
#include <QTextBrowser>
#include <QStackedWidget>

#include "CCmdBase.h"
#include "CSetChartXYRange.h"

class QCustomPlot;

class CLightOneMDT : public QWidget
{
    Q_OBJECT
public:
    explicit CLightOneMDT(int iMachineID, QWidget *parent = nullptr);
    ~CLightOneMDT();

    void ExportData();
    void ClearData();
    void SwitchPage();
    void ReceiveMDTData(const QVariant &qVarData);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;
    virtual void hideEvent(QHideEvent *pEvent) override;

private slots:
    void _SlotSetXYRange(const QStringList &strList);
    void _SlotHoleIndexChanged(int index);

private:
    void _InitWidget();
    void _InitCustomPlot();
    void _AddGraph(QCustomPlot* pCustomPlot, QColor penColor, QColor pointColor,
                  int iChart, QString strChartName);
    void _UpdatePlot(bool bResetRange = false);

private:
    bool m_bShow;
    bool m_bReplot;
    const int m_iMachineID;
    QCustomPlot *m_pCustomPlot;
    CSetChartXYRange *m_pCSetChartXYRange;
    QComboBox *m_pHoleComboBox;
    QTextBrowser *m_pTextBrowser;
    QStackedWidget *m_pStackedWidget;
    double m_dMaxX, m_dMinY, m_dMaxY;
    QVector<double> m_dXList;
    QList<QVector<double>> m_dDataList;
};

#endif // CLIGHTONEMDT_H
