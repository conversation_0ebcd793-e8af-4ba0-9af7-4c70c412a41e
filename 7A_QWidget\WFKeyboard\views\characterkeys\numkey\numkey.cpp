#include "numkey.h"
#include "ui_numkey.h"

#include "../../keyboard.h"
#include "../symbolkey/symbolkey.h"

class Numkey::PrivateData
{
public:
    PrivateData()
    {

    }

public:
    Ui::Numkey *ui;
};

Numkey::Numkey(KeyBoard *parent, QStackedWidget *stackedWidget)
    : CharacterBase<PERSON><PERSON>(parent, stackedWidget)
    , md(new PrivateData())
{
    md->ui = new Ui::Numkey;
    md->ui->setupUi(this);

    ConnectMapper(md->ui->toolButton_0);
    ConnectMapper(md->ui->toolButton_1);
    ConnectMapper(md->ui->toolButton_2);
    ConnectMapper(md->ui->toolButton_3);
    ConnectMapper(md->ui->toolButton_4);
    ConnectMapper(md->ui->toolButton_5);
    ConnectMapper(md->ui->toolButton_6);
    ConnectMapper(md->ui->toolButton_7);
    ConnectMapper(md->ui->toolButton_8);
    ConnectMapper(md->ui->toolButton_9);
    ConnectMapper(md->ui->toolButton_Colon);
    ConnectMapper(md->ui->toolButton_Comma);
    ConnectMapper(md->ui->toolButton_Dot);
    ConnectMapper(md->ui->toolButton_Minus);
    ConnectMapper(md->ui->toolButton_Plus);
    ConnectMapper(md->ui->toolButton_Line);
    ConnectMapper(md->ui->toolButton_Space, false, " ");

    connect(md->ui->toolButton_Hide, SIGNAL(clicked()), GetKeyBoard(), SLOT(SlotEscBtnClicked()));
    connect(md->ui->toolButton_BackSpace, SIGNAL(clicked()), GetKeyBoard(), SLOT(SlotBackSpaceBtnClicked()));
    connect(md->ui->toolButton_Return, SIGNAL(clicked()), SLOT(SlotReturnBtnClicked()));
    connect(md->ui->toolButton_symbol, SIGNAL(clicked()), SLOT(SlotGotoSymbol()));
}

Numkey::~Numkey()
{
    delete md;
}

void Numkey::changeEvent(QEvent *event)
{
    if (event->type() == QEvent::LanguageChange) {
        md->ui->retranslateUi(this);
    }

    QWidget::changeEvent(event);
}

void Numkey::SlotGotoSymbol()
{
    GetKeyBoard()->GotoSymbolKey(Symbolkey::MATH);
}

void Numkey::Translate(const QString &space)
{
    md->ui->toolButton_Space->setText(space);
}
