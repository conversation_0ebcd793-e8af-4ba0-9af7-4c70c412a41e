#ifndef CPRINTERWIDGET_H
#define CPRINTERWIDGET_H

/*****************************************************
  * Copyright: wondfo
  * Project: 7A_QWidget
  * Author: hongxirong
  * Date: 2024-07-03
  * Description: 打印设置
  * -------------------------------------------------------------------------
  * History: 2024-08-20 先不启用保存按钮状态切换功能
  *
  *
  *
  * -------------------------------------------------------------------------
  ****************************************************/

#include <QLabel>
#include <QCheckBox>
#include <QTextEdit>

#include "CLineEdit.h"
#include "SystemPage/CSysSecondTitleWidget.h"
#include "SystemPage/CSysFirstTitleWidget.h"
#include "PublicParams.h"

class CPrinterWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CPrinterWidget(QWidget *parent = nullptr);

protected:
    virtual void showEvent(QShowEvent *pEvent) override;

signals:
    void SignalReturn();

private slots:
    void _SlotCheckBoxClicked(bool bChecked);
    void _SlotTextChanged(const QString &strText);
    void _SlotSelectAllBtn();
    void _SlotSaveBtn();

private:
    void _ReadCfg();
    void _CheckAllClicked();
    void _UpdatePreviewText();

private:
    void _InitWidget();
    void _InitLayout();

private:
    bool m_bSelectAll;
    SPrintInfoStruct m_sPrintStruct;
    QList<QCheckBox *> m_pAllCheckBoxList;

private:
    CSysFirstTitleWidget *m_pCSysFirstTtileWidget;
    QLabel *m_pBackgroundLabel;

    CSysSecondTitleWidget *m_pCSysSecondTitleWidget;

    QCheckBox *m_pReportTitleCheckBox;
    CLineEdit *m_pReportTitleLineEdit;

    QCheckBox *m_pSampleIDCheckBox, *m_pSampleTypeCheckBox, *m_pSamplingDateCheckBox;
    QCheckBox *m_pTestTimeCheckBox, *m_pCardIDCheckBox, *m_pCardLotCheckBox;
    QCheckBox *m_pCardMFGCheckBox, *m_pCardEXPCheckBox, *m_pNameCheckBox;
    QCheckBox *m_pGenderCheckBox, *m_pAgeCheckBox, *m_pBirthdayCheckBox;
    QCheckBox *m_pTelephoneCheckBox, *m_pOperatorCheckBox, *m_pCTCheckBox;
    QCheckBox *m_pAutoPrintCheckBox;

    QCheckBox *m_pStatementCheckBox;
    CLineEdit *m_pStatementLineEdit;

    QLabel *m_pVLabel;
    QTextEdit *m_pPreviewTextEdit;

    QPushButton *m_pReturnBtn, *m_pSelectAllBtn, *m_pSaveBtn;
};

#endif // CPRINTERWIDGET_H
